dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="mix16-arm-3-run2-$dt-peaval"
RENDERER_NAME="harmony_v4.0.15_berry_v3_1mil_orion_lpe"

CMD=(
beam python --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}
# name=mix16-arm-1-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr2e-6-resumed-run-20250727-162422-1-peaval
root_config="mini.root.dev init_actors_rpc_timeout=600"
# experiment_id=damajercak-mix16-arm-1-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-resumed-run-20250725-020418

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
policy.initial_checkpoint="az://orngscuscresco/twapi/mini/e/zhendongwang-swe_workflow_sft_gpt5-mini_lr1e-4-run4/30915951-67fb-4155-b35d-f550831018d9/checkpoint/model1/000000000469/"
eval_settings.checkpoint_dir="az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-3-pdw2-ev3-mixed-itc-spi32-gpt5-mini-sft-1e-4-469-100steps-tef03-5-tpm1-rm-lr2e-6-run-20250801-221026/policy"


root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
policy.is_multimodal=True
...encoding_name=orion_200k
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc

#zhendongwang-mix14-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run21
#swang-pdw-20250622-180636-train
#zhendongwang-mix12-pdw2-continued-32x32-o4mini-tef00-5-tpm2-rm-lr1e-5-run8
#zhendongwang-mix13-pdw2-ev3-ivt-mixed-32x32-o4mini-tef03-5-tpm2-rm-lr1e-5-run1
#swang-pdw-20250621-074823-train
#zhendongwang-mix11-pdw2-ivt-mixed-32x32-o4mini-tef03-5-tpm2-rm-lr1e-5-run1
#zhendongwang-mix9-pdw2-ivt-mixed-32x32-o4mini-tef05-7-tpm2-rm-lr1e-5-run10
#zhendongwang-mix9-pdw2-ivt-mixed-32x32-o4mini-tef05-7-tpm2-rm-lr1e-5-run9
#zhendongwang-mix7-pdw2-ivt16k-32x32-o4mini-tef05-tpm1-rm-lr1e-5-run3
#zhendongwang-mix7-pdw2-ivt16k-32x32-o4mini-tef05-tpm2-rm-lr1e-5-run2

#zhendongwang-mix3-vsc-ivt16k-32x32-nv4-tpm3-efc05-lr1e-5-rm-run4
#zhendongwang-mix1-pdw2-ivt16k-32x32-o4mini-efc05-tef1-tpm1-tc-rm-run2
#chenliang1-mix2-pdw2-ivt16k-32x32-o4mini-efc05-tef1-tpm1-run7
#zhendongwang-mix1-pdw2-ivt16k-32x32-o4mini-efc05-tef1-tpm1-tc-rm-run2
#swang-swe-data-derisk-sbt_passgraderonly

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=1               # swang concurrent thread in one process
peashooter.num_sampling_processes=100            # swang concurrent process in one worker
#peashooter.tool_pool_config.num_tool_workers=64 # swang concurrent processes in tool pool
peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
peashooter.sampling_use_ev3=True

# swang, not support
#timeout.evaluate=80000
#timeout.rollout=3600

# Set this if you want to eval all checkpoints at once in parallel (Not needed if your training run just started.)
max_workers_per_step=10 # swang nodes per step, cannot exceed total sample nodes
# max_active_steps=2    # swang active steps

eval_settings.eval_every=40
# eval_settings.min_step=70
# eval_settings.max_step=600
eval_settings.exit_on_no_new_checkpoint=False

# Inherit model params from training experiment!
auto_inherit_training_args=False

...dataset_container=orngscuscresco

# sbv data
# :deep_swe_eval_msft.swe_bench.peaval.padawan.presets:ev_sm_bench_padawan
# eval_settings.evals.0.dataset.override_target_samples_per_instance=1
# eval_settings.evals.0.dataset.max_num_yields=100
# sbv data new
':deep_swe_eval_msft.swe_bench.peaval.padawan.presets:ev_sm_bench_juice_padawan(juice=768 override_target_samples_per_instance=1 max_num_yields=256)'

# msweb data
#:deep_swe_eval_msft.msweb.peaval.padawan.presets:ev_sm_bench_padawan
#eval_settings.evals.1.dataset.override_target_samples_per_instance=1
#eval_settings.evals.1.dataset.max_num_yields=100
# msweb data new
':deep_swe_eval_msft.msweb.peaval.padawan.presets:ev_sm_bench_juice_padawan(juice=768 override_target_samples_per_instance=1 max_num_yields=256)'

# SWE-Bench Hard data
:prbot_msft.presets.swebench_hard:swebenchhard_12878_repair_msft_hq_padawan_testval
:prbot_msft.presets.swebench_hard:gtp5mini_hpe_cotograder_bus

# swe-pr-if eval data
:deep_swe_msft.rrb_if_ml_padawan_v2.presets:eval_padawan_v2_ml_sweif_prif 

metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

#policy.n_ctx=524288
#defaults.n_ctx=524288
policy.n_ctx=262144
defaults.n_ctx=262144
defaults.sampler.max_num_yields=100
# defaults.sampler.max_num_yields=100
defaults.sampler.harmony_constrained_sampling=True
...harmony_renderer_name=${RENDERER_NAME}

# Set to True if you want to evaluate step0 (your prior)
eval_settings.eval_initial_policy=True

# msft special
load.restore_from_all_clusters=False

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=swe-pevals
kafka_enable=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log
