# Generate a SAS token for the blob
import os
from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobServiceClient, generate_container_sas, ContainerSasPermissions
from datetime import datetime, timedelta
from caas.protocol import SandboxRuntime


CAAS_CUSTOM_IMAGE = "actions-runner-terminal-server-padawan" #"actions-runner-base"
CAAS_WEBDEV_CUSTOM_IMAGE = "actions-runner-terminal-server-webdev:20250724"

def make_repo_script_list(repo, repo_directory, base_commit):
    """
    Create a list of bash commands to set up the repository for testing.
    This is the setup script for the instance image.
    """
    setup_commands = [
        [f"git clone -o origin https://github.com/{repo} {repo_directory}", "/"],
        [f"chmod -R 777 {repo_directory}", "/"], # So nonroot user can run tests
        #[f"cd {repo_directory}", "/"],
        [f"git reset --hard {base_commit}", repo_directory],
        # Remove the remote so the agent won't see newer commits.
        ["git remote remove origin", repo_directory],
    ]

    return setup_commands

def to_sandbox_runtime(value: str) -> SandboxRuntime:
    return {
        "unsafe": SandboxRuntime.UNSAFE,
        "kata": SandboxRuntime.KATA_V2,
        "gvisor": SandboxRuntime.GVISOR,
    }[value.lower().strip()]