import functools
import json
import shlex
from typing import Any, AsyncIterator, Collection, Literal

import structlog
import orjson
import chat
import chz
from caas_tool.caas_container import CaasContainer
from chat.render.common import render_content
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from qstar.common.tools.berry_tool_interface import BerryTool
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from qstar.common.tools.caas_container.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from qstar.common.tools.caas_container.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
)
from qstar.common.tools.caas_container.caas_container_tool import (
    CaasContainerResourceConfig,
)
from deep_swe_eval_msft.p0_test.peaval.padawan.caas_resource_config import P0TestCaasContainerResourceConfig
try:
    from berry_caas_container_tool.caas_container_tool import (
        ToolPluginConfig as CaasToolPluginConfig,
        create_tool_plugins as create_caas_tool_plugins,
    )
except ImportError:
    # Version prior to July FI:
    from berry_caas_container_tool.caas_container_tool import (
        CaasToolPluginConfig,
        create_caas_tool_plugins,
    )

from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from abc import abstractmethod
from typing import Annotated, AsyncIterator, ClassVar, Iterator, Sequence, Literal, Callable
from function_calling_tool import FunctionCallingTool, function_the_model_can_call

from deep_swe_msft.tools.caas_padawan_tool import DeepSWECaasPadawanTool
from deep_swe_msft.webdev_padawan_v2.datasets.caas_padawan_tool import DeepSWECaasPadawanTool as DeepSWECaasWebDevPadawanTool
from qstar.common.tools.caas_container.caas_container_tool import CaasContainerResourceConfig
from deep_swe_eval_msft.p0_test.peaval.padawan.caas_resource_config import P0TestCaasContainerResourceConfig

logger = structlog.stdlib.get_logger(component=__name__)

class EvalDeepSWECaasPadawanTool(DeepSWECaasPadawanTool):
    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        default_exec_timeout: int | None = 1200_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = 'default',
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set

    # For applying penalties to bash command and enforcing forbidden commands
    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        async for message in super(DeepSWECaasPadawanTool, self)._process_sample_inner(sample):
            yield message

@chz.chz(typecheck=True)
class EvalPadawanToolConfig(OriginalCaasContainerToolConfig):
    exec_output_processor_path: str | None = (
        "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown"] = "no_access"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_timeout: int = 1_980
    default_exec_timeout: int = 1_800_000

    def get_tool_name(self) -> str:

        a = EvalDeepSWECaasPadawanTool.get_tool_name()
        return a

    def unique_descriptor_for_variants(self) -> str:
        a = EvalDeepSWECaasPadawanTool.get_tool_name()
        return a

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = EvalDeepSWECaasPadawanTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        a = tool.instruction()
        
        return a

    def get_names_of_functions_the_model_can_call(self, datapoint: HarmonyDatapoint | None = None) -> Collection[str]:
        a = EvalDeepSWECaasPadawanTool.get_names_of_functions_the_model_can_call()
        return a

    @property
    def _caas_resource_name(self) -> str:
        if self.caas_resource_name_override is not None:
            a = self.caas_resource_name_override
        else:
            a = super()._caas_resource_name
        return a

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = EvalDeepSWECaasPadawanTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        
        return tool


@chz.chz(typecheck=True)
class P0EvalPadawanToolConfig(EvalPadawanToolConfig):
    # Override resource_configs to use our P0TestCaasContainerResourceConfig
    resource_configs: tuple[CaasContainerResourceConfig, ...] = chz.field(
        default=(
            P0TestCaasContainerResourceConfig(),
        )
    )
    # Remove the plugin_configs to avoid the duplication issue
    # The setup will be handled in the setup_fn instead
    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(),  # Empty tuple - no plugins
    )

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = EvalDeepSWECaasPadawanTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        # Don't set plugins here - the parent class's initialize_tool method already handles this
        
        return tool
    

# =========

class EvalDeepSWECaasWebDevPadawanTool(DeepSWECaasWebDevPadawanTool):
    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        default_exec_timeout: int | None = 1200_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = 'default',
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set

    # For applying penalties to bash command and enforcing forbidden commands
    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        async for message in super(DeepSWECaasWebDevPadawanTool, self)._process_sample_inner(sample):
            yield message

@chz.chz(typecheck=True)
class EvalPadawanWebDevToolConfig(OriginalCaasContainerToolConfig):
    exec_output_processor_path: str | None = (
        "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown"] = "no_access"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_timeout: int = 1_980
    default_exec_timeout: int = 1_800_000

    def get_tool_name(self) -> str:

        a = EvalDeepSWECaasWebDevPadawanTool.get_tool_name()
        return a

    def unique_descriptor_for_variants(self) -> str:
        a = EvalDeepSWECaasWebDevPadawanTool.get_tool_name()
        return a

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = EvalDeepSWECaasWebDevPadawanTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        a = tool.instruction()
        
        return a

    def get_names_of_functions_the_model_can_call(self, datapoint: HarmonyDatapoint | None = None) -> Collection[str]:
        a = EvalDeepSWECaasWebDevPadawanTool.get_names_of_functions_the_model_can_call()
        return a

    @property
    def _caas_resource_name(self) -> str:
        if self.caas_resource_name_override is not None:
            a = self.caas_resource_name_override
        else:
            a = super()._caas_resource_name
        return a

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = EvalDeepSWECaasWebDevPadawanTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        
        return tool


@chz.chz(typecheck=True)
class P0EvalPadawanWebDevToolConfig(EvalPadawanWebDevToolConfig):
    # Override resource_configs to use our P0TestCaasContainerResourceConfig
    resource_configs: tuple[CaasContainerResourceConfig, ...] = chz.field(
        default=(
            P0TestCaasContainerResourceConfig(),
        )
    )
    # Remove the plugin_configs to avoid the duplication issue
    # The setup will be handled in the setup_fn instead
    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(),  # Empty tuple - no plugins
    )

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = EvalDeepSWECaasWebDevPadawanTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        # Don't set plugins here - the parent class's initialize_tool method already handles this
        
        return tool