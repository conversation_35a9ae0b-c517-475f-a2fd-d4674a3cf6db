import functools
from functools import cached_property
import json
from typing import Annotated, Any, AsyncIterator, Collection, Literal

import chat
import chz
import structlog
from caas_tool.caas_container import CaasContainer
from chat.render.common import render_content
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from function_calling_tool import function_the_model_can_call
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from qstar.common.tools.berry_tool_interface import BerryTool
from qstar.common.tools.caas_container.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from qstar.common.tools.caas_container.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
)

try:
    from berry_caas_container_tool.caas_container_tool import (
        ToolPluginConfig as CaasToolPluginConfig,
        create_tool_plugins as create_caas_tool_plugins,
    )
except ImportError:
    # Version prior to July FI:
    from berry_caas_container_tool.caas_container_tool import (
        CaasToolPluginConfig,
        create_caas_tool_plugins,
    )

from deep_swe_msft.tools.caas_vscode_tool import VSCodeTool
from chat.render.v4.experimental.strawberry.formatter import BerryChannel

logger = structlog.stdlib.get_logger(component=__name__)


class EvalVSCodeTool(VSCodeTool):
    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        default_exec_timeout: int | None = 1200_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = "default",
        real_tool: Literal["real", "fake", "skip"] = "fake",
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set
        self.real_tool = real_tool

    # For applying penalties to bash command and enforcing forbidden commands
    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        
        async for message in super(VSCodeTool, self)._process_sample_inner(sample):
            yield message

            


@chz.chz(typecheck=True)
class EvalVSCodeToolConfig(OriginalCaasContainerToolConfig):
    exec_output_processor_path: str | None = (
        "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown"] = "no_access"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_timeout: int = 1_980
    default_exec_timeout: int = 1_800_000

    tool_channel: BerryChannel | None = chz.field(
        doc="What channel the tool should go in", default=BerryChannel.CHAIN_OF_THOUGHT
    )
    real_tool: Literal["real", "fake", "skip"] = chz.field(
        default="fake",
        doc="""Values: fake, real, skipped. If 'real', the tool will be a real VSCode tool. If 'fake', it will be a mock tool.
If 'skipped', the test output will be mocked as though the tests were skipped.""",
    )

    def get_channel_for_tool_call(self) -> BerryChannel | None:
        return self.tool_channel
    
    def get_tool_name(self) -> str:
        return EvalVSCodeTool.get_tool_name()

    def unique_descriptor_for_variants(self) -> str:
        return EvalVSCodeTool.get_tool_name()

    @cached_property
    def _tool_for_tool_info(self) -> VSCodeTool:
        """Override to use EvalVSCodeTool instead of base BerryCaasContainerTool for tools_format_version=v2 support."""
        tool = EvalVSCodeTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
            real_tool=self.real_tool,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool

    @property
    def _caas_resource_name(self) -> str:
        if self.caas_resource_name_override is not None:
            return self.caas_resource_name_override
        else:
            return super()._caas_resource_name

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = EvalVSCodeTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
            real_tool=self.real_tool,
        )
        return tool
