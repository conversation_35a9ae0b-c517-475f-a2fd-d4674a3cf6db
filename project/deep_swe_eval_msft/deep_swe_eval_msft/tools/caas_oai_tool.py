import functools
import json
import shlex
from typing import Any, AsyncIterator, Collection, Literal
from functools import cached_property
from functions import Function
import structlog
import orjson
import chat
import chz
from caas_tool.caas_container import CaasContainer
from chat.render.common import render_content
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from qstar.common.tools.berry_tool_interface import Berry<PERSON>ool
from qstar.common.tools.caas_container.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from qstar.common.tools.caas_container.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
)
try:
    from berry_caas_container_tool.caas_container_tool import (
        ToolPluginConfig as CaasToolPluginConfig,
        create_tool_plugins as create_caas_tool_plugins,
    )
except ImportError:
    # Version prior to July FI:
    from berry_caas_container_tool.caas_container_tool import (
        CaasToolPluginConfig,
        create_caas_tool_plugins,
    )

from abc import abstractmethod
from typing import Annotated, AsyncIterator, ClassVar, Iterator, Sequence, Literal, Callable
from function_calling_tool import FunctionCallingTool, function_the_model_can_call

from gpt_oss_msft.tools.caas_container_tool import DeepSWECaasContainerTool
from gpt_oss_msft.tools.caas_container_tool_v2 import DeepSWECaasContainerTool as DeepSWECaasContainerToolV2

logger = structlog.stdlib.get_logger(component=__name__)

class EvalDeepSWECaasContainerTool(DeepSWECaasContainerTool):
    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 7_200_000,  # 2 hours in milliseconds
        default_exec_timeout: int | None = 10_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = 'default',
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        # custom the default_exec_timeout, the oai by default uses 10 seconds
        self._default_exec_timeout = default_exec_timeout

        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set

    # For applying penalties to bash command and enforcing forbidden commands
    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        async for message in super(DeepSWECaasContainerTool, self)._process_sample_inner(sample):
            yield message

@chz.chz(typecheck=True)
class EvalDeepSWECaasContainerToolConfig(OriginalCaasContainerToolConfig):
    tool_timeout: int = chz.field(
        doc="Maximum time to wait by the system for each tool call",
        default=1800,  # 30 minutes
    )
    exec_output_processor_path: str | None = (
        # "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
        # "gpt_oss_msft.tools.truncate:TruncateExecPostProcessor" # truncate -> max 256 lines, 256 per line
        "deep_swe_eval_msft.swe_bench.peaval.oai.truncate:TruncateExecPostProcessor" # truncate -> max 100 lines, 100 per line
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown", "enabled"] = "enabled"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    default_exec_timeout: int = 10_000

    def get_tool_name(self) -> str:

        a = EvalDeepSWECaasContainerTool.get_tool_name()
        return a

    def unique_descriptor_for_variants(self) -> str:
        a = EvalDeepSWECaasContainerTool.get_tool_name()
        return a

    @cached_property
    def _tool_for_tool_info(self) -> EvalDeepSWECaasContainerTool:
        """Override to use EvalDeepSWECaasContainerTool instead of base BerryCaasContainerTool for tools_format_version=v2 support."""
        tool = EvalDeepSWECaasContainerTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = EvalDeepSWECaasContainerTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        a = tool.instruction()
        
        return a

    def get_names_of_functions_the_model_can_call(self, datapoint: HarmonyDatapoint | None = None) -> Collection[str]:
        a = EvalDeepSWECaasContainerTool.get_names_of_functions_the_model_can_call()
        return a

    @property
    def _caas_resource_name(self) -> str:
        if self.caas_resource_name_override is not None:
            a = self.caas_resource_name_override
        else:
            a = super()._caas_resource_name
        return a

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = EvalDeepSWECaasContainerTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        return tool
    

class EvalDeepSWECaasContainerToolV2(DeepSWECaasContainerToolV2):
    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 7_200_000,  # 2 hours in milliseconds
        default_exec_timeout: int | None = 10_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = 'default',
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        # custom the default_exec_timeout, the oai by default uses 10 seconds
        self._default_exec_timeout = default_exec_timeout

        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set

    # For applying penalties to bash command and enforcing forbidden commands
    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        async for message in super(DeepSWECaasContainerToolV2, self)._process_sample_inner(sample):
            yield message

@chz.chz(typecheck=True)
class EvalDeepSWECaasContainerToolConfigV2(OriginalCaasContainerToolConfig):
    tool_timeout: int = chz.field(
        doc="Maximum time to wait by the system for each tool call",
        default=1800,  # 30 minutes
    )
    exec_output_processor_path: str | None = (
        # "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
        # "gpt_oss_msft.tools.truncate:TruncateExecPostProcessor" # truncate -> max 256 lines, 256 per line
        "deep_swe_eval_msft.swe_bench.peaval.oai.truncate:TruncateExecPostProcessor" # truncate -> max 100 lines, 100 per line
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown", "enabled"] = "enabled"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    default_exec_timeout: int = 10_000

    def get_tool_name(self) -> str:

        a = EvalDeepSWECaasContainerToolV2.get_tool_name()
        return a

    def unique_descriptor_for_variants(self) -> str:
        a = EvalDeepSWECaasContainerToolV2.get_tool_name()
        return a

    @cached_property
    def _tool_for_tool_info(self) -> EvalDeepSWECaasContainerToolV2:
        """Override to use EvalDeepSWECaasContainerToolV2 instead of base BerryCaasContainerTool for tools_format_version=v2 support."""
        tool = EvalDeepSWECaasContainerToolV2(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = EvalDeepSWECaasContainerToolV2(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        a = tool.instruction()
        
        return a

    def get_names_of_functions_the_model_can_call(self, datapoint: HarmonyDatapoint | None = None) -> Collection[str]:
        a = EvalDeepSWECaasContainerToolV2.get_names_of_functions_the_model_can_call()
        return a

    @property
    def _caas_resource_name(self) -> str:
        if self.caas_resource_name_override is not None:
            a = self.caas_resource_name_override
        else:
            a = super()._caas_resource_name
        return a

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = EvalDeepSWECaasContainerToolV2(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        return tool
    
