from typing import Any, Sequence

import chz
from berry import preset_utils
from chz_presets.core import CompositePreset, Preset
from deep_swe.datasets.config_utils import chz_path
from rcall.runtime import get_func_path
from caas_utils.conversation_init import InstructionInsertionFormat
from berry.function_wrapper import FunctionWrapper
import deep_swe.datasets.configs as base_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_eval_msft.data_converter.data_converter import get_vardisc_args, _maybe_deliberate_tool_config
import deep_swe_eval_msft.web_bench.peaval.padawan.dataset_config as configs

PADAWAN_DATASET_CONFIGS = [
    (configs.WebBenchDatasetConfigPadawan, []),
]


def ev_web_bench_juice_padawan(
    juice: int | tuple[int, ...] = 512,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
    ) -> CompositePreset[Preset[Any]]:
    return preset_utils.compose_presets(
        *[
            preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    *get_vardisc_args(juice),
                ]
            )
            for dataset_config, args in PADAWAN_DATASET_CONFIGS
        ],
    )


ev_web_bench_padawan = ev_web_bench_juice_padawan()
