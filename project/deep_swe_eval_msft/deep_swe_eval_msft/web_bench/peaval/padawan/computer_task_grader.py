"""
Computer task grader for Web Bench evaluation in Padawan environment.
"""
import shlex
from typing import TypedDict
from uuid import uuid4
import tenacity
import logging
import os
import shutil
import time
import json

import structlog
from typing import Any

import caas
import berry
from caas import ExecError
from caas.api import caas_api
from caas.commands import Exec, RawBashScript, RawExec, UploadFile

from caas.terminal.api import TerminalSession
from caas_swe_bench_train_v2.parser import ModelPatch
from caas_utils.utils import run_with_retries
from caas.protocol import NetworkMode
from caas_tool.caas_container import CaasContainer

from deep_swe_eval_msft.web_bench.peaval.padawan.setup import web_bench_setup_fn_internal
from deep_swe_eval_msft.web_bench.peaval.padawan.caas_resource_config import WEB_BENCH_CUSTOM_IMAGE
from deep_swe_eval_msft.web_bench.preparedness.web_bench_metadata import WebBenchMetadata
from deep_swe_eval_msft.tools.utils import CAAS_ENDPOINT
from deep_swe_eval_msft.web_bench.peaval.computer_task_grader_utils import (
    _grade_fn_v2_inner,
    get_model_patch,
    PROTECTED_FILES,
    GradeReport,
)
from caas_swe_bench_train_v2.parser import ModelPatch
from deep_swe_eval_msft.web_bench.peaval.computer_task_grader_utils import get_model_patch_raw, _upload_and_apply

from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)

logger = structlog.get_logger(__name__)

@tenacity.retry(
    retry=tenacity.retry_if_exception_type(
        (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception)
    ),
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(5),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
)
async def grade_fn_v2_padawan(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> GradeFnOutput:
    metadata = WebBenchMetadata.model_validate(sample.gt_datapoint.metadata)
    report = await grade_fn_v2_internal_padawan(
        terminal_session=terminal_session,
        metadata=metadata,
        fast=fast,
    )

    logger.info("Grading finished", **report)
    with open("/var/log/supervisor/keli_web_bench_grader_padawan.log", "a") as f:
        f.write(f"Grading finished: {report}\n")
    # Fill model_patch
    sample.ephemeral_metadata["model_patch"] = report["model_patch"]
    sample.metadata["grader"] = {
        "prompt": report["model_patch"],
        "response": report["test_output"],
        "score": int(report["passed"]),
    }
    output = GradeFnOutput(correct=report["passed"], extra_metadata={"pass_at_1": float(report["pass_at_1"])})
    return output

async def grade_fn_v2_padawan_binaryscore(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> bool:
    metadata = WebBenchMetadata.model_validate(sample.gt_datapoint.metadata)
    report = await grade_fn_v2_internal_padawan(
        terminal_session=terminal_session,
        metadata=metadata,
        fast=fast,
    )

    logger.info("Grading finished", **report)
    with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
        f.write(f"Grading finished: {report}\n")
    # Fill model_patch
    sample.ephemeral_metadata["model_patch"] = report["model_patch"]
    sample.metadata["grader"] = {
        "prompt": report["model_patch"],
        "response": report["test_output"],
        "score": int(report["passed"]),
    }
    
    return report["passed"]

@tenacity.retry(
    retry=tenacity.retry_if_exception_type(
        (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception, TimeoutError, RuntimeError)
    ),
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(5),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
)
async def grade_fn_v2_internal_padawan(
    *,
    terminal_session: TerminalSession,
    metadata: WebBenchMetadata,
    fast: bool,
) -> GradeReport:
    repo_root = '/web-bench' # metadata.cwd

    model_patch = await get_model_patch(
        terminal_session=terminal_session,
        base_commit="HEAD^",  # Compare against the setup commit (after folder changes)
        repo_root=repo_root,
        protected_files=PROTECTED_FILES,
    )

    return await _grade_fn_v2_inner(terminal_session, metadata, model_patch)
