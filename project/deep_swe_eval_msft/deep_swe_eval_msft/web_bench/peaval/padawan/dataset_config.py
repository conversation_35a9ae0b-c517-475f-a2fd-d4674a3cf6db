import chz
from functools import partial
from typing import Any, Sequence

import qstar.instance_completers
import qstar.instance_optimizers

from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import <PERSON>StageGrader
from qstar.presets.chz_utils import IsO<PERSON>ride, override
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.curriculums.function_wrapper import FunctionWrapper

from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    GraderService,
    TokenCompleterGraderService,
)

from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from token_completer import TokenCompleter

import caas_autograding.grader as caas_graders
from berry import InstanceCompleter, InstanceOptimizer, SampleAllocator
from qstar.allocators.continuous_rewards_adaptive_sample_allocator import (
    ContinuousRewardsAdaptiveSampleAllocator,
)

from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig 
from deep_swe.datasets import configs as default_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY, PADAWAN_SYSTEM_PROMPT

from deep_swe_eval_msft.web_bench.peaval.padawan.setup import web_bench_setup_fn as web_bench_setup_fn_padawan
from deep_swe_eval_msft.web_bench.peaval.padawan.caas_resource_config import DeepWebBenchCaasContainerResourceConfig as DeepWebBenchCaasContainerResourceConfigPadawan
from deep_swe_eval_msft.graders.eval_unit_test import EvalTermberryGrader

from deep_swe_eval_msft.tools.caas_padawan_tool import EvalPadawanToolConfig
from chat.render.v4.experimental.strawberry.formatter import BerryChannel

CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
CAAS_IDLE_TTL = 1200


def make_web_bench_graders_padawan() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.web_bench.peaval.padawan.computer_task_grader:grade_fn_v2_padawan",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def _make_tool_configs_padawan(
    container_tool_config: EvalPadawanToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
) -> tuple[ToolConfig, ...]:
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)


@chz.chz
class WebBenchGraderPadawan(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(make_web_bench_graders_padawan)

    @property
    def accepts_invalid(self) -> bool:
        """
        Generally, computer using tasks can be graded at any point in time.
        """
        return True

@chz.chz(typecheck=True)
class WebBenchDatasetConfigPadawan(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = (
        # "data.datasets.swe.eval.web_bench.padawan_demorun"
        # "data.datasets.swe.eval.web_bench.padawan_smktest"
        "data.datasets.swe.eval.web_bench.padawan_all"
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: WebBenchGraderPadawan(graders=make_web_bench_graders_padawan())
    )
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs_padawan)
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepWebBenchCaasContainerResourceConfigPadawan(setup_fn=web_bench_setup_fn_padawan,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter("deep_swe_eval_msft.web_bench.preparedness.conversation_init:conversation_init_web_bench_fn_padawan"),
        )
    )
    
    model_identity_str: str = PADAWAN_MODEL_IDENTITY
    instructions: str = PADAWAN_SYSTEM_PROMPT

