import dataclasses
import json
import logging
import time
import caas
import chz
import structlog
import tenacity

from caas.api import caas_api
from caas.commands import HttpGet, RawBashScript
from caas.protocol import VolumeMount
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from deep_swe_eval_msft.web_bench.preparedness.web_bench_metadata import WebBenchMetadata
from qstar.common import datapoint
from qstar.common.tools.berry_tool_interface import ResourceContinuationError
from qstar.common.tools.caas_container.caas_container_tool import CaasContainerResourceConfig
from deep_swe_eval_msft.tools.utils import CAAS_ENDPOINT
from deep_swe_eval_msft.data_converter.repo_setup import to_sandbox_runtime
from deep_swe_msft.tools.vscode_copilot_tool import (
    VSC_MNT,
)
from caas.protocol import NetworkMode

logger = structlog.get_logger(component=__name__)

# Web-specific container setup image for VSC
WEB_BENCH_VSC_CUSTOM_IMAGE = "acrcommitcaassouthcentralusame.azurecr.io/caas-web-bench:20250821a"

# For installing nodejs
TMP_DRI = "/usr/local/nodejsinstall"
NODEJS_VERSION = "22.14.0"
BLOB_NAME = f"node-v{NODEJS_VERSION}-linux-x64.tar.gz"
NODEJS_MNT = [
    VolumeMount(host=f"/mnt/azure_blob/tools/{BLOB_NAME}", container=f"{TMP_DRI}/{BLOB_NAME}", deprecated_use_blobfuse=True)
]

@chz.chz(typecheck=True)
class DeepWebBenchVSCCaasContainerResourceConfig(CaasContainerResourceConfig):
    """
    Allows per-datapoint control over the image, resource limits, and network access for Web Bench VSC tasks.
    """

    caas_endpoint: str = CAAS_ENDPOINT
    caas_idle_ttl: int = 1200
    use_terminal_server: bool = True

    async def wait_until_ready(self, session: caas.CaasSession) -> None:
        """
        Wait until the VSCode Copilot tool server is ready. The same implementation of terminal server.
        """
        logger.info("Starting VSCode Copilot tool server in background...")
        return_code, return_text = await session.run(
            RawBashScript(
                """
set -x
set -e
export GITHUB_PAT=1
export SERVER_MODE=1
export WORKSPACEFOLDER=/testbed
mkdir -p $WORKSPACEFOLDER
cd /app/vscode-copilot
npm run test:extension
""",
                detach=True,
            )  # 1 hour
        )

        if return_code != 0:
            raise RuntimeError(
                f"Failed to start VSCode Copilot tool server: {return_code} {return_text.decode('utf-8')}"
            )
        else:
            logger.info(
                f"VSCode Copilot tool server started with return code: {return_code} and text: {return_text.decode('utf-8')}"
            )

        # run the server in background
        try:
            logger.info("Waiting for VSCode Copilot tool server to be ready...")
            t0 = time.time()
            response = await session.run(
                HttpGet(
                    url="http://localhost:8000/healthcheck",
                    retry_count=10,
                    retry_delay=2,
                    enable_public_logging=True,
                    timeout=20,
                )
            )
            logger.info(
                f"VSCode Copilot tool server healthcheck response: {response} elapsed: {time.time() - t0}"
            )
            assert json.loads(response) == {"status": "ok"}
        except Exception as e:
            logger.error(f"VSCode Copilot tool server failed to start: {e}")
            raise e

    async def _initialize_resource_from_state_metadata(
        self, metadata: WebBenchMetadata, caas_session_state: str
    ) -> CaasContainer:
        try:
            container = CaasContainer(
                caas_endpoint=self.caas_endpoint,
                image_name=WEB_BENCH_VSC_CUSTOM_IMAGE,
                caas_session_state=caas_session_state,
                is_owner=False,
                user=self.user,
            )
            await container.send_heartbeat()
            return container
        except (
            ValueError,
            caas.NotFoundError,
            caas.ServerError,
            caas.TransportError,
            caas.ClientError,
        ) as e:
            raise ResourceContinuationError(
                resource_name=self.name(),
                resource_state=caas_session_state.encode(),
                message=(
                    f"Failed to initialize web bench VSC caas container '{self.name()}' from session state "
                    f"'{caas_session_state}': {e}"
                ),
            ) from e

    @tenacity.retry(
        retry=tenacity.retry_if_exception_type(
            (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception)
        ),
        wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
        stop=tenacity.stop_after_attempt(20),
        before_sleep=tenacity.before_sleep_log(
            logger,  # type: ignore
            logging.WARNING,
        ),
    )
    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        metadata = WebBenchMetadata.model_validate(dp.metadata)

        if self.caas_session_state is not None:
            # We are not the owner of the container, so we don't need to run the setup function.
            resource = await self._initialize_resource_from_state_metadata(
                metadata, self.caas_session_state
            )
            assert not resource.is_owner
            return resource

        if self.use_terminal_server:
            cmd = ["/server.py"]
        else:
            cmd = []

        caas = caas_api(endpoint=self.caas_endpoint)
        caas_session = await caas.new_session(
            image=WEB_BENCH_VSC_CUSTOM_IMAGE,
            cmd=cmd,
            cpu_limit="50",
            memory_limit="50g",
            idle_ttl=self.caas_idle_ttl,
            num_gpus=self.caas_num_gpus,
            network=NetworkMode.CAAS_PUBLIC_ONLY,
            sandbox_runtime=to_sandbox_runtime("unsafe"),
            pids_limit=1024,
            timeout=3600,
            volume_mounts=VSC_MNT,
            disk_limit="32g",
        )
        terminal_session = TerminalSession(caas_session)

        try:
            if self.setup_fn is not None:
                await self.setup_fn(
                    datapoint=dataclasses.asdict(dp),
                    terminal_session=terminal_session,
                )

        except Exception as e:
            with open("/var/log/supervisor/keli_web_bench_vsc_setup_error.log", "a") as f:
                f.write(f"{e}\n")
            await caas_session.close()
            raise
            
        return CaasContainer(
            caas_endpoint=self.caas_endpoint,
            image_name=WEB_BENCH_VSC_CUSTOM_IMAGE,
            caas_session_state=caas_session.save(),
            user=self.user,
            is_owner=True,
        )
