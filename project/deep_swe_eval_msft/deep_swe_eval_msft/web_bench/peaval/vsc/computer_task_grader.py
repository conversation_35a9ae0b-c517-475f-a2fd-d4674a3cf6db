"""
Computer task grader for Web Bench evaluation in VSC environment.
"""
import tenacity
import logging

import structlog
from typing import Any
import caas
import berry

from caas.terminal.api import TerminalSession
from caas_swe_bench_train_v2.parser import ModelPatch
from deep_swe_eval_msft.web_bench.preparedness.web_bench_metadata import WebBenchMetadata
from deep_swe_eval_msft.web_bench.peaval.computer_task_grader_utils import (
    _grade_fn_v2_inner,
    get_model_patch,
    get_model_patch_dummy,
    PROTECTED_FILES,
    GradeReport,
)
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)

logger = structlog.get_logger(__name__)

@tenacity.retry(
    retry=tenacity.retry_if_exception_type(
        (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception)
    ),
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(5),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
)
async def grade_fn_v2_vsc(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> GradeFnOutput:
    logger.info("Starting Web Bench grading for VSC environment")

    metadata = WebBenchMetadata.model_validate(sample.gt_datapoint.metadata)
    report = await grade_fn_v2_internal_vsc(
        terminal_session=terminal_session,
        metadata=metadata,
        fast=fast,
    )

    logger.info("Grading finished", **report)
    with open("/var/log/supervisor/keli_web_bench_grader_vsc.log", "a") as f:
        f.write(f"Grading finished: {report}\n")

    # Fill model_patch
    sample.ephemeral_metadata["model_patch"] = report["model_patch"]
    sample.metadata["grader"] = {
        "prompt": report["model_patch"],
        "response": report["test_output"],
        "score": int(report["passed"]),
    }
    output = GradeFnOutput(correct=report["passed"], extra_metadata={"pass_at_1": float(report["pass_at_1"])})
    return output

async def grade_fn_v2_vsc_binaryscore(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> bool:
    logger.info("Starting Web Bench grading for VSC environment")

    metadata = WebBenchMetadata.model_validate(sample.gt_datapoint.metadata)
    report = await grade_fn_v2_internal_vsc(
        terminal_session=terminal_session,
        metadata=metadata,
        fast=fast,
    )

    logger.info("Grading finished", **report)
    # swang need to fill model_patch
    sample.ephemeral_metadata["model_patch"] = report["model_patch"]
    sample.metadata["grader"] = {
        "prompt": report["model_patch"],
        "response": report["test_output"],
        "score": int(report["passed"]),
    }
    logger.info("Web Bench grading completed for VSC", result=result)
    return report["passed"]

@tenacity.retry(
    retry=tenacity.retry_if_exception_type(
        (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception, TimeoutError, RuntimeError)
    ),
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(5),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
)
async def grade_fn_v2_internal_vsc(
    *,
    terminal_session: TerminalSession,
    metadata: WebBenchMetadata,
    fast: bool,
) -> GradeReport:
    repo_root = '/web-bench' # metadata.cwd

    # current hack to avoid decoding issues
    model_patch = await get_model_patch_dummy(
        terminal_session=terminal_session,
        base_commit="HEAD^",  # Compare against the setup commit (after folder changes)
        repo_root=repo_root,
        protected_files=PROTECTED_FILES,
    )
    return await _grade_fn_v2_inner(terminal_session, metadata, model_patch)
