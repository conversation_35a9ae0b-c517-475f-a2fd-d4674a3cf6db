"""
Main presets file for Web Bench evaluation across different environments.
"""

from typing import Any
from berry import preset_utils
from chz_presets.core import CompositePreset, Preset

# Import environment-specific presets
from deep_swe_eval_msft.web_bench.peaval.padawan.presets import ev_web_bench_juice_padawan
from deep_swe_eval_msft.web_bench.peaval.vsc.presets import ev_web_bench_juice_vsc
from deep_swe_eval_msft.web_bench.peaval.oai.presets import ev_web_bench_juice_oai


def ev_web_bench_juice(
    juice: int | tuple[int, ...] = 768,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
) -> CompositePreset[Preset[Any]]:
    """
    Main Web Bench evaluation preset that combines all environments.
    
    Args:
        juice: Resource allocation parameter
        override_target_samples_per_instance: Number of samples per instance
        max_num_yields: Maximum number of yields
        
    Returns:
        Composite preset for Web Bench evaluation
    """
    return preset_utils.compose_presets(
        ev_web_bench_juice_padawan(juice, override_target_samples_per_instance, max_num_yields),
        ev_web_bench_juice_vsc(juice, override_target_samples_per_instance, max_num_yields),
    )


# Default presets for easy access
ev_web_bench = ev_web_bench_juice()
ev_web_bench_padawan = ev_web_bench_juice_padawan()
ev_web_bench_vsc = ev_web_bench_juice_vsc()
ev_web_bench_oai = ev_web_bench_juice_oai()
