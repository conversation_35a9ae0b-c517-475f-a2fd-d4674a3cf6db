#!/bin/bash
WANDB_PROJECT_NAME=webbench-peaval
CKPT="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"
dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="webbench-$dt-evals-peaval"

CMD=(
# oaipkg run qstar.run_eval nostrict
beam python --start-daemon --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}
auto_inherit_training_args=False

:berry_models.scallion:d64_chicken_80g_bf16
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0}'

policy.initial_checkpoint=${CKPT}
policy.is_multimodal=True
...encoding_name=orion_200k
...harmony_renderer_name=${RENDERER_NAME}

peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.use_stale_kv_cache=False
load.restore_from_all_clusters=False
peashooter.sampling_concurrency=2
peashooter.num_sampling_processes=16
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600

policy.n_ctx=131072 # 262144
defaults.n_ctx=131072 # 262144
...max_num_yields=512
defaults.sampler.max_num_yields=512
defaults.sampler.harmony_constrained_sampling=True

eval_settings.eval_every=1
eval_settings.exit_on_no_new_checkpoint=True
eval_settings.eval_initial_policy=True
eval_settings.checkpoint_dir=${CKPT}

metrics_collector=deep_swe_eval_msft.metrics.webbench_metrics_collector:SWEMetricsCollector

# sbv data new
# ':deep_swe_eval_msft.swe_bench.peaval.padawan.presets:ev_sm_bench_juice_padawan(juice=128 override_target_samples_per_instance=1 max_num_yields=256)'

# Web bench dataset
# :deep_swe_eval_msft.web_bench.peaval.presets:ev_web_bench
':deep_swe_eval_msft.web_bench.peaval.presets:ev_web_bench_juice_vsc(juice=64 override_target_samples_per_instance=1 max_num_yields=512)'
# ':deep_swe_eval_msft.web_bench.peaval.presets:ev_web_bench_juice_padawan(juice=64 override_target_samples_per_instance=1 max_num_yields=512)'
# :deep_swe_eval_msft.web_bench.peaval.presets:ev_web_bench_padawan

eval_settings.evals.0.dataset.override_target_samples_per_instance=1

defaults.channel_config.channels="analysis,final"
peashooter.sampling_use_ev3=True
...dataset_container=orngscuscresco

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=${WANDB_PROJECT_NAME}
kafka_enable=False
enable_slackbot=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log
