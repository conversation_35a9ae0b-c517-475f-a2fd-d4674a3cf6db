import shlex
from typing import TypedDict
from uuid import uuid4
import uuid
import tenacity
import logging
import os
import shutil
import time
import json
import hashlib
import difflib

import structlog
from pprint import pformat

import caas
import berry
from caas import ExecError
from caas.api import caas_api
from caas.commands import Exec, RawBashScript, RawExec, UploadFile
from caas.terminal.api import TerminalSession
from caas_swe_bench_train_v2.parser import ModelPatch
from caas_utils.utils import run_with_retries
from caas.protocol import NetworkMode

from deep_swe_eval_msft.web_bench.preparedness.web_bench_metadata import WebBenchMetadata

logger = structlog.get_logger(__name__)

class GradeReport(TypedDict):
    passed: bool
    model_patch: str
    test_output: str
    passed_tests: list[str]

def make_instance_from_str(d: str) -> dict:
    logger.info("Creating web bench instance...")
    with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
        f.write(f"Creating web bench instance...\n")
    try:
        instance: dict = json.loads(d)
    except Exception as e:
        logger.error(f"Error creating web bench instance for {d}: {e}")
        with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
            f.write(f"Error creating web bench instance for {d}: {e}\n")
        raise

    logger.info(f"Successfully loaded {instance} valid web bench instance.")
    with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
        f.write(f"Successfully loaded {instance} valid web bench instance.\n")

    return instance

def remove_ansi_escape(log: str) -> str:
    """ANSI escape makes the log colorful but not RE-friendly. Remove."""
    import re
    ansi_escape_pattern = re.compile(r"\x1B[@-_][0-?]*[ -/]*[@-~]")
    return ansi_escape_pattern.sub("", log)

async def compare_test_files(
    grade_session: TerminalSession,
    backup_dir: str,
    current_dir: str,
) -> tuple[bool, str]:
    """
    Compare test files between backup and current directories to detect modifications.
    Returns (is_clean, report) where is_clean=True means no unauthorized modifications detected.
    """
    logger.info("Starting anti-cheating test file comparison...")
    
    try:
        # Get list of files in backup directory
        return_code, backup_files_output = await grade_session.session.run(
            RawExec(
                cmd=["find", backup_dir, "-type", "f", "-name", "*.spec.js"],
                workdir="/",
                timeout=60,
            )
        )
        
        if return_code != 0:
            error_msg = f"Failed to list backup test files: {backup_files_output.decode()}"
            logger.error(error_msg)
            return False, error_msg
        
        backup_files = backup_files_output.decode().strip().split('\n')
        backup_files = [f for f in backup_files if f.strip()]  # Remove empty lines
        
        # Get list of files in current directory
        return_code, current_files_output = await grade_session.session.run(
            RawExec(
                cmd=["find", current_dir, "-type", "f", "-name", "*.spec.js"],
                workdir="/",
                timeout=60,
            )
        )
        
        if return_code != 0:
            error_msg = f"Failed to list current test files: {current_files_output.decode()}"
            logger.error(error_msg)
            return False, error_msg
        
        current_files = current_files_output.decode().strip().split('\n')
        current_files = [f for f in current_files if f.strip()]  # Remove empty lines
        
        # Extract relative paths for comparison
        backup_rel_files = {os.path.relpath(f, backup_dir) for f in backup_files}
        current_rel_files = {os.path.relpath(f, current_dir) for f in current_files}
        
        report_lines = ["=== Anti-Cheating Test File Comparison Report ==="]
        
        # Check for missing files
        missing_files = backup_rel_files - current_rel_files
        if missing_files:
            report_lines.append(f"WARNING: Missing test files: {missing_files}")
        
        # Check for extra files
        extra_files = current_rel_files - backup_rel_files
        if extra_files:
            report_lines.append(f"WARNING: Extra test files detected: {extra_files}")
        
        # Compare content of common files
        common_files = backup_rel_files & current_rel_files
        modified_files = []
        
        for rel_file in common_files:
            backup_file = os.path.join(backup_dir, rel_file)
            current_file = os.path.join(current_dir, rel_file)
            
            # Get MD5 hash of backup file
            return_code, backup_hash_output = await grade_session.session.run(
                RawExec(
                    cmd=["md5sum", backup_file],
                    workdir="/",
                    timeout=30,
                )
            )
            
            # Get MD5 hash of current file
            return_code2, current_hash_output = await grade_session.session.run(
                RawExec(
                    cmd=["md5sum", current_file],
                    workdir="/",
                    timeout=30,
                )
            )
            
            if return_code == 0 and return_code2 == 0:
                backup_hash = backup_hash_output.decode().split()[0]
                current_hash = current_hash_output.decode().split()[0]
                
                if backup_hash != current_hash:
                    modified_files.append(rel_file)
                    report_lines.append(f"MODIFIED: {rel_file}")
                    
                    # Get detailed diff for the modified file
                    return_code, backup_content_output = await grade_session.session.run(
                        RawExec(
                            cmd=["cat", backup_file],
                            workdir="/",
                            timeout=30,
                        )
                    )
                    
                    return_code2, current_content_output = await grade_session.session.run(
                        RawExec(
                            cmd=["cat", current_file],
                            workdir="/",
                            timeout=30,
                        )
                    )
                    
                    if return_code == 0 and return_code2 == 0:
                        backup_content = backup_content_output.decode().splitlines()
                        current_content = current_content_output.decode().splitlines()
                        
                        diff = list(difflib.unified_diff(
                            backup_content,
                            current_content,
                            fromfile=f"backup/{rel_file}",
                            tofile=f"current/{rel_file}",
                            lineterm=""
                        ))
                        
                        if diff:
                            report_lines.append(f"  Diff for {rel_file}:")
                            report_lines.extend([f"    {line}" for line in diff[:20]])  # Limit diff lines
                            if len(diff) > 20:
                                report_lines.append(f"    ... ({len(diff) - 20} more lines)")
                else:
                    report_lines.append(f"OK: {rel_file}")
        
        # Determine if test files are clean
        is_clean = len(missing_files) == 0 and len(extra_files) == 0 and len(modified_files) == 0
        
        if is_clean:
            report_lines.append("✅ All test files are clean - no unauthorized modifications detected")
        else:
            report_lines.append("❌ SECURITY ALERT: Test file modifications detected!")
            report_lines.append(f"  - Missing files: {len(missing_files)}")
            report_lines.append(f"  - Extra files: {len(extra_files)}")
            report_lines.append(f"  - Modified files: {len(modified_files)}")
        
        report = "\n".join(report_lines)
        
        # Log the results
        logger.info(f"Test file comparison completed. Clean: {is_clean}")
        with open("/var/log/supervisor/keli_web_bench_anti_cheat.log", "a") as f:
            f.write(f"Test file comparison completed. Clean: {is_clean}\n")
            f.write(f"{report}\n")
        
        return is_clean, report
        
    except Exception as e:
        error_msg = f"Error during test file comparison: {e}"
        logger.error(error_msg, exc_info=True)
        with open("/var/log/supervisor/keli_web_bench_anti_cheat.log", "a") as f:
            f.write(f"{error_msg}\n")
        return False, error_msg

PROTECTED_FILES = [
    ".config.js",
    ".config.ts",
    "package.json",
    "pom.xml",
    "build.gradle",
    "cargo.toml",
    "go.mod",
    "go.sum",
    "setup.cfg",
    "pyproject.toml",
    "conftest.py",
    "pytest.ini",
    "requirements.txt",
    "requirements-dev.txt",
    "tox.ini",
    "setup.py",
    ".pre-commit-config.yaml",
    "package-lock.json",
    "yarn.lock",
    "pnpm-lock.yaml",
    "webpack.config.js",
    "webpack.config.ts",
    "vite.config.js",
    "vite.config.ts",
    "rollup.config.js",
    "rollup.config.ts",
    "next.config.js",
    "next.config.ts",
    "nuxt.config.js",
    "nuxt.config.ts",
    "vue.config.js",
    "vue.config.ts",
    "angular.json",
    "tsconfig.json",
    "tsconfig.app.json",
    "tsconfig.spec.json",
    "babel.config.js",
    "babel.config.json",
    ".babelrc",
    ".eslintrc",
    ".eslintrc.json",
    ".eslintrc.js",
    ".prettierrc",
    ".prettierrc.json",
    ".prettierrc.js",
    "jest.config.js",
    "jest.config.ts",
    "vitest.config.js",
    "vitest.config.ts",
    "playwright.config.js",
    "playwright.config.ts",
    "cypress.config.js",
    "cypress.config.ts",
    ".env",
    ".env.local",
    ".env.development",
    ".env.production",
    "Dockerfile",
    ".dockerignore",
    ".gitignore",
    ".gitattributes",
    "Makefile",
    "Gemfile",
    "Gemfile.lock",
    "rust-toolchain",
    "CMakeLists.txt",
    "settings.gradle",
    "webpack.config.js",
    "webpack.config.ts",
    "rollup.config.js",
    "rollup.config.ts",
    "vite.config.js",
    "vite.config.ts",
    ".mocharc.js",
    ".mocharc.json",
    ".mocharc.yaml",
    ".mocharc.yml",
    ".jpg", ".png", ".gif", ".ico",
    ".woff", ".woff2", ".ttf",    
    ".pdf", ".zip", ".tar.gz",  
]

async def get_model_patch(
    terminal_session: TerminalSession,
    base_commit: str,
    repo_root: str,
    protected_files: list[str],
) -> ModelPatch:
    # Download the model diff (excluding config files to avoid hacks)
    exclude_patterns = shlex.join([f":(exclude){file}" for file in protected_files])
    model_patch_raw = await run_with_retries(
        terminal_session,
        f"""
cd {repo_root}
# Do not include warnings about CRLF line endings in git diff output.
git config core.safecrlf false
git add -N .
git diff --binary {base_commit} -- . {exclude_patterns}
""",
        attempts=3,
    )

    return ModelPatch(model_patch_raw.decode())

async def get_model_patch_dummy(
    terminal_session: TerminalSession,
    base_commit: str,
    repo_root: str,
    protected_files: list[str],
) -> ModelPatch:
    # Download the model diff (excluding config files to avoid hacks)
    exclude_patterns = shlex.join([f":(exclude){file}" for file in protected_files])
    model_patch_raw = await run_with_retries(
        terminal_session,
        f"""
cd {repo_root}
ls -al
""",
        attempts=3,
    )

    return ModelPatch(model_patch_raw.decode())


async def _run_single_task_test(
    grade_session: TerminalSession,
    task_index: int,
    project_dir: str,
    output_project_dir: str,
    task_id: str,
    test_file: str,
    port: int = 3000,
) -> tuple[bool, str]:
    """Run a single web-bench task test using npx playwright test with specific test file"""
        
    start_time = time.time()
    
    try:
        logger.info(f"Running web-bench task {task_index} test with file {test_file}")
        test_file_path = os.path.join(project_dir, "test", test_file)
        return_code, cmd_output = await grade_session.session.run(
            RawExec(
                cmd=["npx", "playwright", "test", test_file_path, "--workers=1", "--max-failures=1"],
                workdir=project_dir,
                timeout=600,  # 10 minutes timeout like web-bench
            )
        )
        test_output = cmd_output.decode("utf-8").strip()
        test_runtime = time.time() - start_time
        
        # Success is determined by exit code 0 and no stderr (web-bench logic)
        passed = return_code == 0
        
        logger.info(f"Task {task_index} ({test_file}) completed in {test_runtime:.2f}s, passed: {passed}")
        
        # Cleanup after each test to prevent resource accumulation
        try:
            # Kill any remaining playwright/chromium processes
            await grade_session.session.run(
                RawExec(
                    cmd=["pkill", "-f", "chromium"],
                    workdir=project_dir,
                    timeout=30,
                )
            )
            await grade_session.session.run(
                RawExec(
                    cmd=["pkill", "-f", "playwright"],
                    workdir=project_dir,
                    timeout=30,
                )
            )
            # Clean up temporary directories
            await grade_session.session.run(
                RawExec(
                    cmd=["find", "/tmp", "-name", "playwright_*", "-type", "d", "-exec", "rm", "-rf", "{}", "+"],
                    workdir="/",
                    timeout=60,
                )
            )
        except Exception as cleanup_error:
            logger.warning(f"Cleanup after task {task_index} failed: {cleanup_error}")
        
        return passed, test_output
        
    except Exception as e:
        logger.error(f"Error running task {task_index} ({test_file}): {e}")
        with open("/var/log/supervisor/keli_web_bench_grader_errors.log", "a") as f:
            f.write(f"Task {task_index} ({test_file}) failed: {e}\n")
        return False, str(e)

async def _calculate_pass_at_k(task_results: list[tuple[bool, str]], total_tasks: int = 20) -> dict:
    """Calculate pass@k metrics following web-bench methodology"""
    
    # Find the first task that failed completely
    fail_index = -1
    for i, (passed, _) in enumerate(task_results):
        if not passed:
            fail_index = i
            break
    
    if fail_index == -1:
        # All tasks passed
        pass_count = total_tasks
    else:
        # Count consecutive passes before first failure
        pass_count = fail_index
    
    pass_at_1_percentage = (pass_count / total_tasks) * 100
    
    return {
        "pass_at_1": pass_at_1_percentage,
        "consecutive_passes": pass_count,
        "total_tasks": total_tasks,
        "first_failure_at": fail_index + 1 if fail_index != -1 else None
    }

async def _grade_fn_v2_inner(
    grade_session: TerminalSession,
    metadata: WebBenchMetadata,
    model_patch: ModelPatch,
) -> GradeReport:
    instance = make_instance_from_str(metadata.task.json())
    instance_id = instance.get("id", "unknown")
    max_tasks = instance.get("total_tasks", 20)
    run_id = uuid.uuid4().hex
    # must end with instance id
    output_dir = os.path.join('/tmp/web_bench_outputs', run_id, instance_id)
    
    testbed_dir = metadata.cwd
    project_dir = metadata.project.project_path

    # Enhanced Anti-cheating check
    logger.info("Starting enhanced anti-cheating verification...")
    with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
        f.write(f"Starting enhanced anti-cheating verification...\n")
    
    # First, compare existing test files with backup to detect any modifications
    test_dir = os.path.join(project_dir, "test")
    backup_test_dir = "/tmp/backup-testfiles"
    
    is_clean, comparison_report = await compare_test_files(
        grade_session=grade_session,
        backup_dir=backup_test_dir,
        current_dir=test_dir
    )
    
    # Log the comparison results
    logger.info(f"Test file integrity check: {'PASSED' if is_clean else 'FAILED'}")
    with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
        f.write(f"Test file integrity check: {'PASSED' if is_clean else 'FAILED'}\n")
        f.write(f"Comparison report:\n{comparison_report}\n")
    
    # If test files were modified, this is a security concern
    if not is_clean:
        logger.warning("SECURITY ALERT: Test files appear to have been tampered with!")
        with open("/var/log/supervisor/keli_web_bench_anti_cheat.log", "a") as f:
            f.write(f"SECURITY ALERT: Test files tampered with in {project_dir}\n")
            f.write(f"Comparison report:\n{comparison_report}\n")
    
    # Always restore clean test files from backup (anti-cheating measure)
    logger.info("Restoring clean test files from backup...")
    copy_cmd = ["cp", "-r", "/tmp/backup-testfiles/.", f"{project_dir}/test"]
    result = await grade_session.session.run(
        RawExec(copy_cmd, timeout=300, workdir="/")
    )
    logger.info(f"Copying backup testfiles completed")
    with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
        f.write(f"Copying backup testfiles completed\n")

    # Get and sort test files
    test_dir = os.path.join(project_dir, "test")
    logger.info(f"Getting test file list from {test_dir}")
    return_code, ls_output = await grade_session.session.run(
        RawExec(
            cmd=["ls", test_dir],
            workdir='/',
            timeout=60,
        )
    )
    
    if return_code != 0:
        logger.error(f"Failed to list test directory: {ls_output.decode()}")
        test_files = []
    else:
        test_files_raw = ls_output.decode().strip().split('\n')
        # Filter for .spec.js files and sort them
        test_files = [f for f in test_files_raw if f.endswith('.spec.js')]
        
        # Custom sort to handle init.spec.js and task-n.spec.js properly
        def sort_key(filename):
            if filename == 'init.spec.js':
                return (0, 0)  # init comes first
            elif filename.startswith('task-') and filename.endswith('.spec.js'):
                try:
                    task_num = int(filename[5:-8])  # Extract number from task-N.spec.js
                    return (1, task_num)
                except ValueError:
                    return (2, filename)  # Fallback for malformed task files
            else:
                return (2, filename)  # Other files come last
        
        test_files.sort(key=sort_key)
    
    logger.info(f"Found and sorted test files: {test_files}")
    with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
        f.write(f"Found and sorted test files: {test_files}\n")
    
    try:
        assert metadata.docker_image
        
        # Create output directory
        shutil.rmtree(output_dir, ignore_errors=True)
        os.makedirs(output_dir, exist_ok=True)

        testbed_dir = metadata.cwd
        project_dir = metadata.project.project_path
        task_id = instance.get("task_id", instance_id)
        
        # Initialize the project environment
        logger.info("Setting up web-bench project environment")
        with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
            f.write(f"Setting up web-bench project environment\n")

        # Clean up any existing processes and temporary files before starting
        try:
            logger.info("Cleaning up existing processes and temporary files")
            cleanup_commands = [
                "pkill -f chromium || true",
                "pkill -f playwright || true", 
                "find /tmp -name 'playwright_*' -type d -exec rm -rf {} + || true",
                "find /tmp -name 'chromium*' -type d -exec rm -rf {} + || true"
            ]
            
            for cmd in cleanup_commands:
                await grade_session.session.run(
                    RawExec(
                        cmd=["bash", "-c", cmd],
                        workdir="/",
                        timeout=60,
                    )
                )
        except Exception as e:
            logger.warning(f"Initial cleanup failed: {e}")

        # setup_commands = [
        #     f"rm -r {project_dir}/src/*",
        #     f"cp -r {testbed_dir}/* {project_dir}/.",
        # ]
        
        # for cmd in setup_commands:
        #     logger.info(f"Running setup command: {cmd}")
        #     with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
        #         f.write(f"Running setup command: {cmd}\n")
        #     return_code, cmd_output = await grade_session.session.run(
        #         RawExec(
        #             cmd=["bash", "-c", cmd],
        #             workdir='/root',
        #             timeout=900,
        #         )
        #     )

        # with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
        #     f.write(f"Moved {testbed_dir}/* to {project_dir}/.\n")

        # Run sequential task tests following web-bench methodology
        task_results = []
        
        logger.info(f"Running sequential web-bench tasks using {len(test_files)} test files")
        with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
            f.write(f"Running sequential web-bench tasks using {len(test_files)} test files\n")
        
        for task_index, test_file in enumerate(test_files, 1):
            passed, output = await _run_single_task_test(
                grade_session=grade_session,
                task_index=task_index,
                project_dir=project_dir,
                output_project_dir=output_dir,
                task_id=f"{task_id}_task_{task_index}",
                test_file=test_file,
            )
            
            task_results.append((passed, output))
            
            # Save individual task output
            task_output_path = os.path.join(output_dir, f'task_{task_index}_{test_file}_output.txt')
            with open(task_output_path, "w") as f:
                f.write(f"Task {task_index} ({test_file}) - Passed: {passed}\n")
                f.write(f"Output:\n{output}\n")
            
            # with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
            #     f.write(f"Task {task_index} - Passed: {passed}\n")
            #     f.write(f"Output:\n{output}\n")

            # Stop on first failure (web-bench pass@1 logic)
            if not passed:
                logger.info(f"Stopping at task {task_index} ({test_file}) due to failure")
                with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
                    f.write(f"Stopping at task {task_index} ({test_file}) due to failure\n")
                break
            
            # Add delay between successful tests to prevent resource buildup
            if task_index < len(test_files):  # Don't delay after the last test
                logger.info(f"Waiting 2 seconds before next test to allow resource cleanup")
                time.sleep(2)
        
        # Calculate pass@k metrics based on number of test files
        metrics = await _calculate_pass_at_k(task_results, len(test_files))
        
        # Overall pass determination: pass@1 > 0 means at least some tasks passed
        overall_passed = metrics["consecutive_passes"] > 0
        
        grade_report = {
            "resolved": overall_passed,
            "patch_successfully_applied": True,
            "tests_status": {"passed": overall_passed},
            "web_bench_metrics": metrics,
            "task_results": [{"task": i+1, "passed": passed} for i, (passed, _) in enumerate(task_results)],
            "anti_cheat_check": {
                "test_files_clean": is_clean,
                "comparison_report": comparison_report,
                "backup_restored": True
            }
        }

        # Compile full test output
        full_test_output = []
        for i, (passed, output) in enumerate(task_results):
            full_test_output.append(f"=== Task {i+1} (Passed: {passed}) ===")
            full_test_output.append(output)
            full_test_output.append("")
        
        combined_output = "\n".join(full_test_output)

        # Save complete test output
        test_output_path = os.path.join(output_dir, f'complete_test_output.txt')
        with open(test_output_path, "w") as f:
            f.write(combined_output)

        # with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
        #     f.write(f"{combined_output}\n")

        output = f"""
        =================================================== Web-Bench Grader Report =====================================================
        {pformat(grade_report)}

        ===================================================== Anti-Cheating Report ========================================================
        Test Files Clean: {'✅ YES' if is_clean else '❌ NO - SECURITY ALERT'}
        {comparison_report}

        ===================================================== Pass@1 Metrics ========================================================
        Pass@1: {metrics['pass_at_1']:.2f}%
        Consecutive Passes: {metrics['consecutive_passes']}/{metrics['total_tasks']}
        First Failure At: Task {metrics['first_failure_at'] or 'None'}

        ===================================================== Task Results ========================================================
        {combined_output}
        """.lstrip()

        logger.info("Web-bench grader report", content=output, correct=overall_passed)
        with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
            f.write(f"Web-bench grader report\ncontent:{output}\ncorrect:{overall_passed}\n")

        return {
            "passed": overall_passed,
            "pass_at_1": metrics['pass_at_1'],
            "model_patch": model_patch.compact_patch,
            "test_output": grade_report,
            "passed_tests": [f"task_{i+1}" for i, (passed, _) in enumerate(task_results) if passed],
            "anti_cheat_clean": is_clean,
            "security_alert": not is_clean,
        }

    except Exception as e:
        logger.error(f"Error grading web bench instance {instance_id}: {e}", exc_info=True)
        with open("/var/log/supervisor/keli_web_bench_grader.log", "a") as f:
            f.write(f"Error grading web bench instance {instance_id}: {e}\n")
        
        # Cleanup on error
        try:
            cleanup_commands = [
                "pkill -f chromium || true",
                "pkill -f playwright || true", 
                "find /tmp -name 'playwright_*' -type d -exec rm -rf {} + || true",
            ]
            for cmd in cleanup_commands:
                await grade_session.session.run(
                    RawExec(
                        cmd=["bash", "-c", cmd],
                        workdir="/",
                        timeout=30,
                    )
                )
        except Exception:
            pass  # Don't let cleanup errors mask the original error
        
        raise

    finally:
        # Final cleanup regardless of success or failure
        try:
            logger.info("Performing final cleanup")
            cleanup_commands = [
                "pkill -f chromium || true",
                "pkill -f playwright || true", 
                "find /tmp -name 'playwright_*' -type d -exec rm -rf {} + || true",
                "find /tmp -name 'chromium*' -type d -exec rm -rf {} + || true"
            ]
            for cmd in cleanup_commands:
                await grade_session.session.run(
                    RawExec(
                        cmd=["bash", "-c", cmd],
                        workdir="/",
                        timeout=30,
                    )
                )
        except Exception:
            logger.warning("Final cleanup failed, but continuing")

    # Clean up output directory at the very end
    # try:
    #     shutil.rmtree(output_dir)
    # except Exception:
    #     logger.warning(f"Failed to remove output_dir {output_dir}", exc_info=True)

async def get_model_patch_raw(
    terminal_session: TerminalSession,
    base_commit: str,
    repo_root: str,
    protected_files: list[str],
):
    # Download the model diff (excluding config files to avoid hacks)
    exclude_patterns = shlex.join([f":(exclude){file}" for file in protected_files])
    model_patch_raw = await run_with_retries(
        terminal_session,
        f"""
cd {repo_root}
# Do not include warnings about CRLF line endings in git diff output.
git config core.safecrlf false
git add -N .
git diff --binary {base_commit} -- . {exclude_patterns}
""",
        attempts=3,
    )
 
    return ModelPatch(model_patch_raw.decode()), model_patch_raw.decode()

NOOP_PATCH = """\
diff --git a/this_is_invisible.py b/this_is_invisible.py
new file mode 100644
index 0000000..e69de29
--- /dev/null
+++ b/this_is_invisible.py
@@ -0,0 +1 @@
+# This is a commented out line
"""

async def _upload_and_apply(
    session: TerminalSession,
    patch: str,
    workdir: str,
) -> None:
    tmpfile = "/tmp/" + uuid4().hex
    if not patch.strip():
        p = NOOP_PATCH
    else:
        p = patch

    await session.session.run(UploadFile(path=tmpfile, data=p.encode()))
    r = await session.session.run(Exec(["git", "apply", "-v", tmpfile], workdir=workdir, timeout=60))
    return r
