"""
Setup functions for Web Bench evaluation in OAI environment.
"""

import os
import logging
import shlex
from typing import Any

import structlog

import chz
import caas
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas.commands import RawExec
from caas_utils import get_strawberry_ace_token
from caas_utils.utils import run_with_retries
from abc import ABC, abstractmethod
from deep_swe_eval_msft.swe_bench.preparedness.swe_bench_metadata import SweBenchMetadata, SweBenchTaskData
from deep_swe_eval_msft.web_bench.preparedness.web_bench_metadata import WebBenchMetadata



from gpt_oss_msft.tools.oai_coreutils_setup import setup_coreutils

logger = structlog.get_logger(__name__)

async def web_bench_setup_fn(datapoint: dict, terminal_session: TerminalSession) -> None:
    """
    Setup function for Web Bench tasks in Padawan environment.
    
    Args:
        datapoint: The harmony datapoint containing task metadata
        terminal_session: The CAAS terminal session
    """
    logger.info("Starting Web Bench setup for Padawan environment")
    metadata = WebBenchMetadata.model_validate(datapoint["metadata"])
    terminal_session.session.start_keepalive_task(keepalive_interval=60)
    try:
        await web_bench_setup_fn_internal(terminal_session=terminal_session, metadata=metadata)

        logger.info("Web Bench setup completed successfully")
        with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
            f.write(f"Web Bench setup completed successfully\n")

    except Exception as e:
        logger.error(f"Web Bench setup failed: {e}")
        with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
            f.write(f"Web Bench setup failed: {e}\n")
        raise


async def web_bench_setup_fn_internal(terminal_session: TerminalSession, metadata) -> None:
    """
    Internal setup function for Web Bench tasks in Padawan environment. 
    Args:
        terminal_session: The CAAS terminal session
        metadata: WebBenchMetadata object
    """
    try:
        project_info = metadata.project
        project_path = project_info.project_path
        cwd = metadata.cwd
        
        if project_path:
            logger.info(f"Backing up testfiles from {project_path}/src to /tmp/backup-testfiles")
            with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
                f.write(f"Backing up testfiles from {project_path}/src to /tmp/backup-testfiles\n")

            # Create directory
            result = await terminal_session.session.run(
                RawExec(["mkdir", "-p", "/tmp/backup-testfiles"], timeout=120, workdir="/")
            )
            logger.info(f"Created directory: /tmp/backup-testfiles")
            with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
                f.write(f"Created directory: /tmp/backup-testfiles\n")

            # Copy project files
            copy_cmd = ["cp", "-r", f"{project_path}/test/.", "/tmp/backup-testfiles"]
            result = await terminal_session.session.run(
                RawExec(copy_cmd, timeout=300, workdir="/")
            )
            logger.info(f"Backing up testfiles completed")
            with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
                f.write(f"Backing up testfiles completed\n")

            # Remove groundtruth and setup initial state
            # Remove "src" folder (groundtruth) and rename "src-init" to "src"
            groundtruth_cleanup_commands = [
                ["rm", "-rf", f"{project_path}/src"],
                ["mv", f"{project_path}/src-init", f"{project_path}/src"],
            ]
            
            for cleanup_cmd in groundtruth_cleanup_commands:
                try:
                    result = await terminal_session.session.run(
                        RawExec(cleanup_cmd, timeout=60, workdir="/")
                    )
                    logger.info(f"Groundtruth cleanup command completed: {' '.join(cleanup_cmd)}")
                    with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
                        f.write(f"Groundtruth cleanup command completed: {' '.join(cleanup_cmd)}\n")

                except Exception as e:
                    logger.warning(f"Groundtruth cleanup command failed (continuing): {' '.join(cleanup_cmd)}, error: {e}")
                    with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
                        f.write(f"Groundtruth cleanup command failed (continuing): {' '.join(cleanup_cmd)}, error: {e}\n")
                    # Continue with other cleanup commands even if one fails
                    continue
        
        post_setup = [
            f"echo 'cd /web-bench' >> /root/.bashrc",
            "git config --global user.name User",
            "git config --global user.email user@localhost",
        ]
        r = await terminal_session.session.run(BashScript("\n".join(post_setup), login=True, timeout=900))
        
        # Commit the folder changes so they don't appear in model patch
        git_commit_setup = [
            f"cd /web-bench",
            "git add -A",
            "git commit -m 'Setup: Remove groundtruth src and replace with src-init' || echo 'No changes to commit'"
        ]
        r = await terminal_session.session.run(BashScript("\n".join(git_commit_setup), login=True, timeout=300))

        await setup_coreutils(terminal_session.session, {}, repo_root="/web-bench", setup_python_tools=True)

        logger.info("Web Bench internal setup completed successfully")
        with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
            f.write(f"Web Bench internal setup completed successfully\n")

    except Exception as e:
        logger.error(f"Web Bench internal setup failed: {e}")
        with open("/var/log/supervisor/keli_web_bench_setup.log", "a") as f:
            f.write(f"Web Bench internal setup failed: {e}\n")
        raise
