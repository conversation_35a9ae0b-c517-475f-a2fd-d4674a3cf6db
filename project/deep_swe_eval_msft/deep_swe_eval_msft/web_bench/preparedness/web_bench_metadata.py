"""
Pydantic models for Web-Bench metadata structure.

Web-Bench is a benchmark for evaluating LLMs on web development tasks.
Each project contains multiple sequential tasks that build upon each other.
This module defines the metadata structure for converting Web-Bench data to OAI format.
"""
from typing import List, Dict
from pydantic import BaseModel

# --- Reuse ContainerResources from the previous example ---
class ContainerResources(BaseModel):
    cpu: str    # e.g. "16"
    memory: str  # e.g. "16g"

class WebBenchTaskData(BaseModel):
    id: str                    # e.g. "task-1"
    date: str                  # e.g. "2025-05-12"
    level: str                 # e.g. "easy", "moderate", "challenging"
    description: str           # Task description
    total_tasks: int           # Total number of tasks

class WebBenchProjectData(BaseModel):
    name: str                  # e.g. "@web-bench/react"
    framework: str             # e.g. "react", "angular", "vue"
    package_json: Dict         # package.json content
    project_path: str          # path to project directory

class WebBenchMetadata(BaseModel):
    cwd: str                   # e.g. "/testbed"
    docker_image: str          # e.g. "web-bench:react-latest"
    resources: Dict[str, str]  # e.g. {"cpu": "16", "memory": "16g"}
    limits: Dict[str, str]     # e.g. {"cpu": "16", "memory": "16g"}
    allow_internet: bool       # True for web development tasks
    project: WebBenchProjectData
    task: WebBenchTaskData
