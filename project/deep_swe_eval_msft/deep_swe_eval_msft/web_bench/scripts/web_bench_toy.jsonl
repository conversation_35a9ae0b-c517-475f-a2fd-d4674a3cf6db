{"problem": "You are working on a demo web development project called @web-bench/demo.\n\nOverall Level: easy\nTasks to Complete: 3 tasks\nDate Range: 2025-5-21 to 2025-5-21\n\nWork on the project by completing all the tasks. Execute in strict sequential order—step by step from start to finish. No steps are to be skipped.\n\nComplete the following tasks in order, building upon each previous task:\n\nTask 1 (ID: task-1):\nAdd a user text input (id 'user') and a password input (id 'password') in page body. Add a login button (id 'login') and a reset button (id 'reset') in page body.\n\nTask 2 (ID: task-2):\nClick button reset to clear the content of user and password input.\n\nTask 3 (ID: task-3):\nClick button login to validate the content of user and password input. When user content is empty, show message 'Invalid user' in alert dialog. When password content is empty, show message 'Invalid password' in alert dialog. Otherwise, show message 'Login successfully' in alert dialog.\n\nPlease implement all the required functionality according to the descriptions above. Make sure to follow best practices for demo development and maintain code quality throughout the implementation. Each task builds upon the previous ones, so ensure continuity and consistency across all implementations.\n", "unique_id": "@web-bench/demo_all_tasks", "solution": "", "answer": "", "metadata": {"cwd": "/testbed", "docker_image": "acrcommitcaassouthcentralusame.azurecr.io/actions-runner-terminal-server-webbench:20250817d", "resources": {"cpu": "8", "memory": "8g"}, "limits": {"cpu": "8", "memory": "8g"}, "allow_internet": true, "project": {"name": "@web-bench/demo", "framework": "demo", "package_json": {"name": "@web-bench/demo", "version": "0.0.1", "scripts": {"build": "", "test": "npx @web-bench/test-util"}, "author": "luics", "eval": {"stable": false}, "devDependencies": {"@playwright/test": "^1.49.1", "@types/node": "^22.7.9", "serve": "^14.2.4", "@web-bench/test-util": "workspace:*"}}, "project_path": "/web-bench/projects/demo"}, "task": {"total_tasks": 3, "id": "all-tasks", "date": "2025-5-21", "level": "easy", "description": "Complete all 3 tasks: task-1, task-2, task-3"}}}