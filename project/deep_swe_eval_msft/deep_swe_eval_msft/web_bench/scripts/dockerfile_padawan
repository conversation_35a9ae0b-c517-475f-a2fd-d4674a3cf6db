# First git clone the web-bench repo, and put this dockerfile under /web-bench/.
FROM acrcommitcaaseastus2dev.azurecr.io/actions-runner-terminal-server-padawan:20250714

WORKDIR /web-bench

# Configure PNPM storage path only (remove RUSH_TEMP_FOLDER)
ENV RUSH_PNPM_STORE_PATH=/tmp/rush-pnpm-store

# Create necessary directories and ensure permissions
RUN mkdir -p ${RUSH_PNPM_STORE_PATH} && \
    chmod -R 777 /tmp

# copy the web-bench repo to /web-bench/* within container
COPY . .

# Install specified version tools
RUN npm install -g npm@11.3.0 && \
    npm i -g pnpm@9.12.0 @microsoft/rush@5.140.0

# Clean up possible old files before rush update
# RUN rm -rf common/temp/*

RUN npm i playwright@1.49.1 -g

# install chromium only
RUN npx playwright install --with-deps --only-shell chromium

# # Update dependencies
RUN rush update

RUN rush build

# Reset to default workdir (optional)
WORKDIR /root