# First git clone the web-bench repo, and put this dockerfile under /web-bench/.
FROM acrbuiltincaasglobaldev.azurecr.io/caas-swe-bench:20250115a-django__django-16819

WORKDIR /web-bench

# Remove all files under /testbed at the beginning
RUN rm -rf /testbed/*

# Configure PNPM storage path only (remove RUSH_TEMP_FOLDER)
ENV RUSH_PNPM_STORE_PATH=/tmp/rush-pnpm-store

# Create necessary directories and ensure permissions
RUN mkdir -p ${RUSH_PNPM_STORE_PATH} && \
    chmod -R 777 /tmp

COPY . .

# Install Node.js 22.x and npm first
RUN apt-get update && \
    apt-get install -y curl && \
    curl -fsSL https://deb.nodesource.com/setup_22.x | bash - && \
    apt-get install -y nodejs

# Install specified version tools
RUN npm install -g npm@11.3.0 && \
    npm i -g pnpm@9.12.0 @microsoft/rush@5.140.0

# Clean up possible old files before rush update
# RUN rm -rf common/temp/*

RUN npm i playwright@1.49.1 -g

# install chromium only
RUN npx playwright install --with-deps --only-shell chromium

# # Update dependencies
RUN rush update

RUN rush build

# Reset to default workdir (optional)
WORKDIR /root