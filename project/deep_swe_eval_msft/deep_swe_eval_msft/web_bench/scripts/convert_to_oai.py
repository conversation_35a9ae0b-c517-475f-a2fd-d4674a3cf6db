#!/usr/bin/env python3
"""
Transform web bench projects and their tasks into result.jsonl using Pydantic models,
including full web development metadata with cumulative task dependencies.
"""
import os
import json
import yaml
import glob
import argparse
from typing import Dict, List
from pydantic import BaseModel

from deep_swe_eval_msft.web_bench.preparedness.web_bench_metadata import WebBenchMetadata, WebBenchTaskData, WebBenchProjectData, ContainerResources

# --- helpers ---
def generate_problem_statement(tasks: List[dict], project_name: str, framework: str) -> str:
    """Generate a problem statement for the cumulative web bench tasks."""
    if not tasks:
        return ""
    
    # Determine the overall level (use the highest level encountered)
    level_priority = {'easy': 1, 'moderate': 2, 'challenging': 3}
    max_level = 'easy'
    for task in tasks:
        task_level = task.get('level', 'easy')
        if level_priority.get(task_level, 1) > level_priority.get(max_level, 1):
            max_level = task_level
    
    # Create cumulative task descriptions
    task_descriptions = []
    for i, task in enumerate(tasks, 1):
        task_descriptions.append(f"Task {i} (ID: {task['id']}):\n{task['description']}")
    
    cumulative_description = "\n\n".join(task_descriptions)
    
    return f"""You are working on a {framework} web development project called {project_name}.

Overall Level: {max_level}
Tasks to Complete: {len(tasks)} tasks
Date Range: {tasks[0]['date']} to {tasks[-1]['date']}

Work on the project by completing all the tasks. Execute in strict sequential order—step by step from start to finish. No steps are to be skipped.

Complete the following tasks in order, building upon each previous task:

{cumulative_description}

Please implement all the required functionality according to the descriptions above. Make sure to follow best practices for {framework} development and maintain code quality throughout the implementation. Each task builds upon the previous ones, so ensure continuity and consistency across all implementations.
"""

def extract_framework_from_project_name(project_name: str) -> str:
    """Extract framework name from project name like '@web-bench/react' -> 'react'"""
    if '/' in project_name:
        return project_name.split('/')[-1]
    return project_name

def load_package_json(project_path: str) -> Dict:
    """Load package.json from project directory."""
    package_json_path = os.path.join(project_path, "package.json")
    if os.path.exists(package_json_path):
        with open(package_json_path, 'r') as f:
            return json.load(f)
    return {}

def load_tasks(project_path: str) -> List[Dict]:
    """Load tasks from either tasks.jsonl or tasks.yml file."""
    tasks = []
    
    # Try JSONL format first
    jsonl_path = os.path.join(project_path, "tasks.jsonl")
    if os.path.exists(jsonl_path):
        with open(jsonl_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                try:
                    task_data = json.loads(line)
                    tasks.append(task_data)
                except Exception as e:
                    print(f"Error parsing JSONL line {line_num} in {jsonl_path}: {e}")
        return tasks
    
    # Try YAML format
    yml_path = os.path.join(project_path, "tasks.yml")
    if os.path.exists(yml_path):
        try:
            with open(yml_path, 'r') as f:
                yaml_tasks = yaml.safe_load(f)
                if isinstance(yaml_tasks, list):
                    tasks = yaml_tasks
                else:
                    print(f"Warning: tasks.yml in {project_path} is not a list")
        except Exception as e:
            print(f"Error parsing YAML file {yml_path}: {e}")
        return tasks
    
    # No tasks file found
    return []

class WebBenchExample(BaseModel):
    problem:   str
    unique_id: str
    solution:  str = ""
    answer:    str = ""
    metadata:  WebBenchMetadata

# --- main script ---
def main(
    projects_dir: str = "/Users/<USER>/code/webdev-exp/web-bench/projects",
    out_jsonl: str = "web_bench_cumulative.jsonl",
    max_tasks: int = None
):
    """Convert all web bench projects and tasks to OAI format with all tasks combined into single datapoints."""
    
    examples = []
    
    # Get all project directories
    project_dirs = [d for d in os.listdir(projects_dir) 
                   if os.path.isdir(os.path.join(projects_dir, d)) 
                   and not d.startswith('.') 
                   and d != 'project-template']
    
    for project_dir in sorted(project_dirs):
        project_path = os.path.join(projects_dir, project_dir)
        
        # Load tasks from either JSONL or YAML format
        tasks_data = load_tasks(project_path)
        
        if not tasks_data:
            print(f"Skipping {project_dir}: no tasks.jsonl or tasks.yml found")
            continue
            
        # Load package.json for project metadata
        package_json = load_package_json(project_path)
        project_name = package_json.get('name', f'@web-bench/{project_dir}')
        framework = extract_framework_from_project_name(project_name)
        
        print(f"Processing project: {project_name} (framework: {framework}) - {len(tasks_data)} tasks")
        
        # Sort tasks by ID to ensure proper order (task-1, task-2, etc.)
        def extract_task_number(task_data):
            task_id = task_data.get('id', '')
            if task_id.startswith('task-'):
                try:
                    return int(task_id.split('-')[1])
                except (IndexError, ValueError):
                    return 0
            return 0
        
        sorted_tasks = sorted(tasks_data, key=extract_task_number)
        
        # Create single datapoint with all tasks combined
        all_tasks = sorted_tasks
        if max_tasks is not None:
            all_tasks = sorted_tasks[:max_tasks]
            
        try:
            # Create task metadata for all tasks combined
            # Use the last task's metadata as the primary task info
            latest_task = all_tasks[-1]
            task = WebBenchTaskData(
                total_tasks=len(all_tasks),
                id=f"all-tasks",
                date=str(latest_task["date"]),
                level=latest_task["level"],
                description=f"Complete all {len(all_tasks)} tasks: {', '.join([t['id'] for t in all_tasks])}"
            )
            
            # Create project metadata
            project = WebBenchProjectData(
                name=project_name,
                framework=framework,
                package_json=package_json,
                project_path=project_path
            )
            
            # Build the top-level metadata
            docker_image = "acrcommitcaassouthcentralusame.azurecr.io/actions-runner-terminal-server-webbench:20250821a"
            metadata = WebBenchMetadata(
                cwd="/testbed",
                docker_image=docker_image,
                resources={"cpu": "8", "memory": "8g"},
                limits={"cpu": "8", "memory": "8g"},
                allow_internet=True,  # Web development often needs internet access
                project=project,
                task=task
            )
            
            # Generate unique ID for all tasks combined
            unique_id = f"{project_name}_all_tasks"
            
            example = WebBenchExample(
                problem=generate_problem_statement(all_tasks, project_name, framework),
                unique_id=unique_id,
                solution="",
                answer="",
                metadata=metadata
            )
            
            examples.append(example)
            
        except Exception as e:
            print(f"Error processing {project_dir} all tasks: {e}")
            print(f"All tasks: {[t.get('id', 'unknown') for t in all_tasks]}")
            continue    # Write all examples to output file
    with open(out_jsonl, "w") as fout:
        for example in examples:
            fout.write(example.model_dump_json())
            fout.write("\n")
    
    print(f"Wrote {len(examples)} examples to {out_jsonl!r}")
    print(f"Projects processed: {len(project_dirs)}")
    
    # Print summary of datapoints created
    project_counts = {}
    for example in examples:
        project_name = example.metadata.project.name
        if project_name not in project_counts:
            project_counts[project_name] = 0
        project_counts[project_name] += 1
    
    print("\nDatapoints created per project:")
    for project_name, count in sorted(project_counts.items()):
        print(f"  {project_name}: {count} datapoint(s)")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Convert WebBench projects to OAI format with all tasks combined')
    parser.add_argument('--projects-dir', 
                       default="/Users/<USER>/code/webdev-exp/web-bench/projects",
                       help='Directory containing WebBench projects')
    parser.add_argument('--output', '-o',
                       default="web_bench_cumulative.jsonl",
                       help='Output JSONL file path')
    parser.add_argument('--max-tasks', type=int,
                       help='Maximum number of tasks per project to include (for debugging)')
    
    args = parser.parse_args()
    main(projects_dir=args.projects_dir, out_jsonl=args.output, max_tasks=args.max_tasks)