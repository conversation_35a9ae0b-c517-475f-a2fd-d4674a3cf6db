import logging
import os
from contextlib import asynccontextmanager
from pprint import pformat
from typing import Any, Async<PERSON>enerator, Self, cast
import asyncio
from caas.commands import BashScript

import structlog.stdlib
import tenacity
from typing_extensions import override

import caas
from caas.api import caas_api
import chz
from alcatraz.clusters.swarm import SwarmCluster, SwarmConfig, _SwarmClusterState
from caas.experimental.compose.protocol import ComposeSessionSpec
from caas_tool.caas_container import <PERSON><PERSON>s<PERSON><PERSON>r
#from nanoeval.solvers.computer_tasks.code_execution_interface import ComputerInterface
from nanoeval.solvers.computer_tasks.task import ComputerTask
from nanoeval_alcatraz.alcatraz_computer_interface import AlcatrazComputerInterface
from nanoeval_alcatraz.task_to_alcatraz_config import task_to_alcatraz_config
from nanoeval_caas.caas_computer_interface import (
    FLAKY_CAAS_ERRORS,
    FLAKY_CAAS_RETRY_CONFIG,
    CaasComposeRuntime,
    CaasComputerInterface,
    create_caas_session,
    Has<PERSON>ustomComposeSessionSpec,
    _HEARTBEAT_INTERVAL,
)
from nanoeval.solvers.computer_tasks.code_execution_interface import (
    ComputerConfiguration,
    ComputerInterface,
    ComputerRuntime,
    ExecutionResult,
)
from caas.experimental.compose.protocol import (
    ComposeSessionRequest,
    ComposeSessionSpec,
    ContainerSpec,
    ContainerSpecAttachedNetworkSpec,
    NetworkSpec,
)
from caas.protocol import ResourceLimits, ResourceReservations, VolumeMount
from caas.terminal.api import TerminalSession
from nanoeval_caas.hacky_task_with_caas_endpoint import HackyTaskWithCaasEndpoint
from nanoeval_caas.setup_hooks import SetupHook, SetupHookAdv
from preparedness.cybersecurity.ctf.ctf_eval_harness.ctf_agent import CTFPythonTask

from qstar.common import datapoint, errors
from qstar.common.tools.alcatraz_container.alcatraz_container_tool import (
    RESOURCE_NAME,
    SwarmClusterResource,
)
from qstar.common.tools.berry_tool_interface import ResourceConfig, ResourceContinuationError
from qstar.common.tools.caas_container.caas_container_tool import _caas_container_resource_name
from caas.protocol import NetworkMode
from deep_swe_eval_msft.msweb.preparedness.msweb_metadata import MSWEBenchMetadata, TaskData
import asyncio

from deep_swe_eval_msft.msweb.peaval.setup import msweweb_verfied_setup_fn
from deep_swe_eval_msft.msweb.preparedness.msweb_metadata import MSWEBenchMetadata, TaskData
from deep_swe_eval_msft.msweb.peaval.computer_task_grader import grade_fn_v2_internal
from caas.api import CaasSession, get_caas
from caas.commands import BashScript, Exec, UploadFile, RawExec

logger = structlog.stdlib.get_logger(component=__name__, _print=True)

# For installing nodejs
TMP_DRI="/usr/local/nodejsinstall"
NODEJS_VERSION='22.14.0'
BLOB_NAME = f"node-v{NODEJS_VERSION}-linux-x64.tar.gz"
NODEJS_MNT = [VolumeMount(host=f"/mnt/azure_blob/tools/{BLOB_NAME}", container=f"{TMP_DRI}/{BLOB_NAME}", deprecated_use_blobfuse=True)]

computer_task_caas_endpoint: str = "https://southcentralus.caas.azure.com"
cmd = ["/server.py"]
async def main():
    if True:
        if True:
            caas = caas_api(endpoint=computer_task_caas_endpoint)
            caas_session = await caas.new_session(
                image="acrcommitcaaseastus2ame.azurecr.io/mswebench/nlohmann_m_json:pr-1769--official-20250411--terminal-base-20250115c-20250507",
                #image="terminal",
                cmd=cmd,
                cpu_limit="50",
                memory_limit="50g",
                idle_ttl=1200,
                num_gpus=0,
                network=NetworkMode.CAAS_PUBLIC_ONLY,
                timeout=2400,
                volume_mounts=NODEJS_MNT,
            )
            terminal_session = TerminalSession(caas_session)
            r = await terminal_session.session.run(BashScript("ls /", login=True, timeout=900))
            print(r)

            import json
            with open("result1.jsonl", "r") as fd:
                lines = fd.readlines()
            p = json.loads(lines[0])
            msweweb_verfied_setup_fn(datapoint=p, terminal_session=terminal_session)
            metadata = MSWEBenchMetadata.model_validate(p["metadata"])

            #command = "str_replace_editor str_replace path \"/home/<USER>/src/generators/dynamic-theme.ts\" old_str \"if (fixes.length === 0 || fixes[fixes.length - 1].url[0] !== '*')\" new_str \"if (fixes.length === 0 || fixes[0].url[0] !== '*')\""
            #r = await terminal_session.session.run(BashScript(command, login=True, timeout=900))
            #print(r)
           
            #with open("./a.diff", "rb") as binary_file:
            #    await terminal_session.session.run(UploadFile('/home/<USER>', binary_file.read()))
            print(metadata.task.fix_patch)
            await terminal_session.session.run(UploadFile('/home/<USER>', metadata.task.fix_patch.encode('utf-8')))
            output = await terminal_session.session.run(Exec(['git', 'apply', '/home/<USER>'], timeout=120, workdir=metadata.cwd, env=None))
            print(output)
            output = await terminal_session.session.run(Exec(['git', 'diff'], timeout=120, workdir=metadata.cwd, env=None))
            print(output)

            r = await grade_fn_v2_internal(terminal_session=terminal_session, metadata=metadata, fast=True)
            print(r)


    return terminal_session

if __name__ == "__main__":
    # this will run your async main() and give you back terminal_session
    terminal_session = asyncio.run(main())