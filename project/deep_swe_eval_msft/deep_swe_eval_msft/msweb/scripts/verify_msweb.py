import asyncio
import json
import time
import os
import shutil
import shlex

import blobfile as bf
import structlog
from caas.commands import BashScript, RawBashScript
from caas.terminal.api import TerminalSession
from caas.api import caas_api
from caas.protocol import NetworkMode
#from deep_swe_eval_msft.msweb.peaval.vsc.setup import msweweb_verfied_setup_fn
from deep_swe_eval_msft.msweb.peaval.padawan.setup import msweweb_verfied_setup_fn
# from deep_swe_msft.tools.vscode_copilot_tool import (
#     exec,
#     new_container,
#     prepare_vsc_tool,
# )
from smokey import Smokey
from deep_swe_eval_msft.msweb.preparedness.msweb_metadata import MSWEBenchMetadata, TaskData
from deep_swe_eval_msft.msweb.preparedness.msweb_caas_eval import (
    get_instance_dir,
    get_git_commit,
    save_git_diff,
    save_git_log,
    restore_test_patch,
    restore_tests,
    grade_instance_caas,
    strict_passed,
    REPO2LANG,
    make_instance_from_str,
    get_files_in_dir,
    get_files_in_home_dir,
    change_default_port_number_in_test_file,
    modify_build_and_test_script,
    run_build_and_test_background,
    monitor_build_in_background,
    modify_java_build_and_test_script,
)
from caas.commands import UploadFile
from multi_swe_bench.harness.constant import EVALUATION_WORKDIR, FIX_PATCH_RUN_LOG_FILE, REPORT_FILE
from multi_swe_bench.harness.dataset import Dataset
from multi_swe_bench.harness.image import Config
from multi_swe_bench.harness.instance import Instance
from multi_swe_bench.harness.report import Report, ReportTask
from deep_swe_eval_msft.msweb.preparedness.msweb_metadata import MSWEBenchMetadata, TaskData
from caas.commands import Exec, RawBashScript, RawExec, UploadFile
from caas.protocol import VolumeMount
from caas_tool.caas_container import CaasContainer

logger = structlog.stdlib.get_logger(component=__name__)

# For installing nodejs
TMP_DRI="/usr/local/nodejsinstall"
NODEJS_VERSION='22.14.0'
BLOB_NAME = f"node-v{NODEJS_VERSION}-linux-x64.tar.gz"
NODEJS_MNT = [VolumeMount(host=f"/mnt/azure_blob/tools/{BLOB_NAME}", container=f"{TMP_DRI}/{BLOB_NAME}", deprecated_use_blobfuse=True)]
CAAS_REGISTRY = "acrcommitcaaseastus2ame.azurecr.io"

async def extract_project_structure(endpoint, sample):
    metadata = MSWEBenchMetadata.model_validate(sample["metadata"])

    git_commit_hash = metadata.task.base.sha
    instance_id = f"{metadata.task.org}__{metadata.task.repo}_{metadata.task.number}"
    instance = make_instance_from_str(metadata.task.json())

    instance_dir = get_instance_dir(instance, metadata.cwd)
    if os.path.exists(instance_dir):
        shutil.rmtree(instance_dir)

    os.makedirs(instance_dir, exist_ok=True)

    try:
        #caas_container = await new_container(endpoint, CAAS_REGISTRY + "/" + sample["metadata"]["docker_image"])
        repo_field = metadata.task.repo
        org_field = metadata.task.org

        repo_language = get_lang_from_repo_org(repo_field, org_field)

        if repo_language == "go":
            environment_variables = {"GOMAXPROCS": "16"}
        elif repo_language == "java":
            environment_variables = {
                "JAVA_TOOL_OPTIONS": " -XX:ActiveProcessorCount=4 -XX:ParallelGCThreads=2 -XX:ConcGCThreads=1 -Djava.util.concurrent.ForkJoinPool.common.parallelism=4",  # noqa: E501
                "JRUBY_HOME": "/home/<USER>/vendor/jruby",
            }
        else:
            environment_variables = {}


        cmd = ["/server.py"]
        caas = caas_api(endpoint=endpoint)
        caas_session = await caas.new_session(
            image=CAAS_REGISTRY + "/" + sample["metadata"]["docker_image"],
            cmd=cmd,
            cpu_limit="50", #metadata.limits.cpu,
            memory_limit="50g", #metadata.limits.memory,
            idle_ttl=3600,
            num_gpus=0,
            network=NetworkMode.CAAS_PUBLIC_ONLY,
            timeout=1200,
            volume_mounts=NODEJS_MNT,
            env=environment_variables,
            pids_limit=1024,
        )
        terminal_session = TerminalSession(caas_session, endpoint=endpoint)
        #await prepare_vsc_tool(caas_session, sample["metadata"]["cwd"])


        await msweweb_verfied_setup_fn(datapoint=sample, terminal_session=terminal_session)



        start_time_instance = time.time()

        await restore_test_patch(metadata, terminal_session)

        # restore test patch for the grader
        await restore_tests(metadata, terminal_session)

        # Evaluation
        #report = await grade_instance_caas(instance, instance_dir, terminal_session)
        fix_patch_path = instance_dir.absolute() / "fix.patch"
        with open(fix_patch_path, "w", encoding="utf-8", newline="\n") as f:
            f.write(metadata.task.fix_patch)
        container_patch_path = instance.dependency().fix_patch_path()

        with open(fix_patch_path, "rb") as binary_file:
            await terminal_session.session.run(
                UploadFile(container_patch_path, binary_file.read())
            )

        run_command = instance.fix_patch_run()  # do we need shlex.split?


        if (repo_language == "go") and (instance.pr.repo == "cli"):
            await change_default_port_number_in_test_file(terminal_session, instance)
        if repo_language in ["c", "cpp"]:
            await modify_build_and_test_script(terminal_session)
            await run_build_and_test_background(terminal_session)
            r = await monitor_build_in_background(terminal_session)
            test_output = r
        elif repo_language == "java":
            print("gohere")
            await modify_java_build_and_test_script(terminal_session)
            print("gohere1")
            r = await run_build_and_test_background(terminal_session)
            print('gohere2')
            #print(r)
            r = await monitor_build_in_background(terminal_session)
            print('gohere3')
            #print(r)
            test_output = r
        else:
            r = await terminal_session.session.run(
                    RawExec(cmd=shlex.split(run_command), workdir=f"/home/<USER>", timeout=1200, env=None)
                )

            test_output = r[1].decode("utf-8").strip()
        print('gohere4')
        #print(test_output)

        with open(instance_dir / FIX_PATCH_RUN_LOG_FILE, "w") as f:
            f.write(test_output)

        task = ReportTask(instance.pr.org, instance.pr.repo, instance.pr.number, instance_dir)

        report = task.generate_report(instance.pr.run_result, instance.pr.test_patch_result, regen=True)
        print('gohere5')
        #print(report)

        resolved, resolution_reason = strict_passed(instance, report)
        print('gohere6')
        print(f"Result for {instance_id}: resolved: {resolved}")

        end_time_instance = time.time()
        # Calculate the difference in seconds and convert to minutes
        execution_time_seconds = end_time_instance - start_time_instance
        execution_time_minutes = execution_time_seconds / 60

        return resolved
    except Exception as e:
        import traceback

        tb_str = traceback.format_exc()
        print('gohere7')
        print(tb_str)
        print(sample["unique_id"])

        # import trainflow.utils; trainflow.utils.debugpy_breakpoint(remote=True)
        raise e
    finally:
        if os.path.exists(instance_dir):
            shutil.rmtree(instance_dir)

failed = [
"cli__cli_10154",
"cli__cli_10124",
"cli__cli_9983",
"cli__cli_9845",
"cli__cli_9465",
"cli__cli_9008",
"cli__cli_8229",
"grpc__grpc-go_3361",
"grpc__grpc-go_3351",
"grpc__grpc-go_3258",
"grpc__grpc-go_3201",
"grpc__grpc-go_3119",
"grpc__grpc-go_2996",
"grpc__grpc-go_2951",
"grpc__grpc-go_2932",
"grpc__grpc-go_2760",
"grpc__grpc-go_2744",
"grpc__grpc-go_2735",
"grpc__grpc-go_2631",
"grpc__grpc-go_2630",
"grpc__grpc-go_2629",
"grpc__grpc-go_2371",
"tokio-rs__tokio_6409",
"tokio-rs__tokio_5781",
"tokio-rs__tokio_5179",
"BurntSushi__ripgrep_954",
"BurntSushi__ripgrep_727",
"BurntSushi__ripgrep_723",
"BurntSushi__ripgrep_454",
"tokio-rs__tracing_1297",
"clap-rs__clap_2161",
"ponylang__ponyc_4458",
"ponylang__ponyc_4288",
"ponylang__ponyc_4263",
"ponylang__ponyc_4182",
"ponylang__ponyc_4132",
"ponylang__ponyc_4087",
"ponylang__ponyc_4067",
"ponylang__ponyc_4061",
"ponylang__ponyc_4057",
"ponylang__ponyc_4034",
"ponylang__ponyc_4024",
"ponylang__ponyc_4017",
"ponylang__ponyc_4006",
"ponylang__ponyc_4005",
"ponylang__ponyc_3991",
"ponylang__ponyc_3973",
"ponylang__ponyc_3971",
"ponylang__ponyc_3962",
"ponylang__ponyc_3953",
"ponylang__ponyc_3819",
"ponylang__ponyc_3781",
"ponylang__ponyc_3777",
"ponylang__ponyc_3763",
"ponylang__ponyc_3758",
"ponylang__ponyc_3746",
"ponylang__ponyc_3739",
"ponylang__ponyc_3725",
"ponylang__ponyc_3723",
"ponylang__ponyc_3675",
"ponylang__ponyc_3650",
"ponylang__ponyc_3647",
"ponylang__ponyc_3603",
"ponylang__ponyc_3586",
"ponylang__ponyc_3442",
"ponylang__ponyc_3293",
"ponylang__ponyc_3006",
"ponylang__ponyc_2950",
"ponylang__ponyc_2865",
"ponylang__ponyc_2853",
"ponylang__ponyc_2664",
"ponylang__ponyc_2660",
"ponylang__ponyc_2586",
"ponylang__ponyc_2532",
"ponylang__ponyc_2504",
"ponylang__ponyc_2342",
"ponylang__ponyc_2272",
"ponylang__ponyc_2261",
"ponylang__ponyc_2247",
"ponylang__ponyc_2221",
"ponylang__ponyc_2214",
"ponylang__ponyc_2205",
"ponylang__ponyc_2203",
"ponylang__ponyc_2201",
"ponylang__ponyc_2176",
"ponylang__ponyc_2168",
"ponylang__ponyc_2126",
"ponylang__ponyc_2007",
"ponylang__ponyc_1981",
"ponylang__ponyc_1867",
"ponylang__ponyc_1858",
"ponylang__ponyc_1852",
"ponylang__ponyc_1726",
"ponylang__ponyc_1536",
"ponylang__ponyc_1516",
"ponylang__ponyc_1486",
"ponylang__ponyc_1411",
"ponylang__ponyc_1339",
"ponylang__ponyc_1260",
"ponylang__ponyc_1124",
"ponylang__ponyc_1095",
"ponylang__ponyc_1057",
"ponylang__ponyc_1051",
"ponylang__ponyc_1035",
"ponylang__ponyc_671",
"vuejs__core_11625",
"vuejs__core_9492",
"vuejs__core_8785",
"mui__material-ui_39962",
"mui__material-ui_39775",
"mui__material-ui_39688",
"mui__material-ui_39679",
"mui__material-ui_39353",
"mui__material-ui_38801",
"mui__material-ui_38544",
"mui__material-ui_36426",
"mui__material-ui_36353",
"mui__material-ui_35946",
"mui__material-ui_34548",
"mui__material-ui_33797",
"mui__material-ui_33582",
"mui__material-ui_26061",
"nlohmann__json_3605",
"nlohmann__json_969",
"nlohmann__json_807",
"nlohmann__json_545",
"nlohmann__json_508",
"anuraghazra__github-readme-stats_88",
"iamkun__dayjs_2532",
"iamkun__dayjs_2420",
"iamkun__dayjs_2399",
"iamkun__dayjs_2369",
"iamkun__dayjs_2231",
"iamkun__dayjs_1611",
"iamkun__dayjs_1201",
"iamkun__dayjs_1008",
"iamkun__dayjs_918",
"iamkun__dayjs_867",
"iamkun__dayjs_745",
"sveltejs__svelte_14937",
"sveltejs__svelte_14353",
"sveltejs__svelte_14213",
"sveltejs__svelte_14210",
"sveltejs__svelte_13589",
"sveltejs__svelte_13057",
"sveltejs__svelte_12290",
"sveltejs__svelte_11947",
"sveltejs__svelte_11720",
"sveltejs__svelte_10996",
"sveltejs__svelte_10655",
"sveltejs__svelte_9973",
"sveltejs__svelte_9970",
"sveltejs__svelte_9838",
"sveltejs__svelte_9784",
"sveltejs__svelte_9462",
"elastic__logstash_17020",
"elastic__logstash_17019",
"elastic__logstash_16968",
"elastic__logstash_16569",
"elastic__logstash_16482",
"elastic__logstash_16195",
"elastic__logstash_15964",
"fasterxml__jackson-databind_4338",
"fasterxml__jackson-databind_4311",
"fasterxml__jackson-databind_4048",
"fasterxml__jackson-databind_3860",
"fasterxml__jackson-databind_3560",
]

semaphore = asyncio.Semaphore(40)

async def extract_with_limit(endpoint: str, sample: dict):
    async with semaphore:
        return await extract_project_structure(endpoint, sample)

def get_lang_from_repo_org(repo: str, org: str) -> str:
    """
    Get the language of the repo based on the org and repo name.
    """
    if repo in REPO2LANG:
        return REPO2LANG[repo]
    if org in REPO2LANG:
        return REPO2LANG[org]
    return "unknown"

async def main():
    #endpoint: str = "https://southcentralus.caas.azure.com"
    endpoint: str = "https://southcentralus.caas.azure.com"
    with open ("result1.jsonl", "r") as f:
        samples = [json.loads(line) for line in f]

    #samples = [sample for sample in samples if sample["unique_id"] in failed]
    '''
    samples_new = []
    java_count = 0
    c_count = 0
    cpp_count = 0

    for sample in samples:
        repo_field = sample['metadata']['task']['repo']
        org_field = sample['metadata']['task']['org']

        repo_language = get_lang_from_repo_org(repo_field, org_field)

        if repo_language == "java" and java_count <= 20:
            samples_new.append(sample)
            java_count += 1
        if repo_language == "c" and c_count <= 20:
            samples_new.append(sample)
            c_count += 1
        if repo_language == "cpp" and cpp_count <= 20:
            samples_new.append(sample)
            cpp_count += 1
    #samples = samples_new[:1]
    samples = samples_new
    '''
    print(len(samples))

    tasks = {
        sample["unique_id"]: asyncio.create_task(extract_with_limit(endpoint, sample))
        for sample in samples
    }

    results = await asyncio.gather(*tasks.values(), return_exceptions=True)
    read_project_structure_map = {uid: res for uid, res in zip(tasks.keys(), results)}

    failure_cnt = 0
    with open("result_fix_good.jsonl", "a") as f, open("result_fix_fail.jsonl", "a") as f_fail:
        for sample in samples:
            rrr = read_project_structure_map[sample["unique_id"]]
            if isinstance(rrr, bool):
                if rrr:
                    f.write(json.dumps(sample) + "\n")
                else:
                    f_fail.write(json.dumps(sample) + "\n")
            else:
                # import trainflow.utils; trainflow.utils.debugpy_breakpoint(remote=True)
                failure_cnt += 1
                print('"' + sample["unique_id"] + '",')
                #print(rrr)

    print(f"Failed to extract project structure for {failure_cnt} samples out of {len(samples)}")


if __name__ == "__main__":
    asyncio.run(Smokey(main))