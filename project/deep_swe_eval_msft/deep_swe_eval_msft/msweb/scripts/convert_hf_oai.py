#!/usr/bin/env python3
"""
Transform input JSONL + image_info.yaml into result.jsonl using Pydantic models,
including full test-result metadata.
"""
import os
import glob
import json
import re
import yaml
from typing import Literal, List, Dict
from pydantic import BaseModel, Field

from deep_swe_eval_msft.msweb.preparedness.msweb_metadata import RunSummary, TaskData, ResolvedIssue, TestResultDetail, MSWEBenchMetadata

class SWEBenchExample(BaseModel):
    problem:   str
    unique_id: str
    solution:  str = ""
    answer:    str = ""
    metadata:  MSWEBenchMetadata

def load_image_info(info_file: str) -> dict[str, str]:
    with open(info_file, "r") as f:
        image_infos = yaml.safe_load(f)
    name2info = {i["name"]: i for i in image_infos}

    for info in name2info.values():
        name = info["name"]
        build_commit = info["build_commit"]
        download_date = info["download_date"]
        term_tag = info["terminal_tag"]
        version = info["version"]
        if build_commit is None:
            assert download_date is not None
            base_info = f"official-{download_date}"
        else:
            assert download_date is None
            base_info = f"rebuilt-{build_commit}"

        info["caas_image_name"] = f"{name}--{base_info}--terminal-{term_tag}-{version}"
    return name2info

# --- helpers ---
def generate_problem_statement(inst: dict) -> str:
    issues = inst["resolved_issues"]
    id2 = {i["number"]: i for i in issues}
    parts = [f"{i['title']}\n{i['body']}" for i in id2.values()]
    return "\n\n".join(parts)


def get_image_name(inst: dict, image_info: dict) -> str:
    key = f"mswebench/{inst['org'].lower()}_m_{inst['repo'].lower()}:pr-{inst['number']}"
    rec = image_info.get(key)
    if not rec:
        raise KeyError(f"image {key} not in image_info.yaml")
    return rec["caas_image_name"]

# --- main script ---
def convert_jsonl(
    in_jsonl: str  = "psa.jsonl",
    yaml_path: str = "image_info.yaml",
    out_dir: str = "peaval_formatted",
    only_process_validated_instances: bool = True
):
    # load image info
    image_info = load_image_info(yaml_path)
    
    # Extract language from filename (letters only from basename without extension)
    basename = os.path.splitext(os.path.basename(in_jsonl))[0]
    language = re.sub(r'[^a-zA-Z]', '', basename)
    
    # First pass: count lines and process data
    processed_lines = []
    line_count = 0

    with open(in_jsonl, "r") as fin:
        for line in fin:
            line = line.strip()
            if not line:
                continue
            inst = json.loads(line)

            # Check if we should filter based on validation status
            if only_process_validated_instances:
                # Construct the image key to look up validation status
                image_key = f"mswebench/{inst['org'].lower()}_m_{inst['repo'].lower()}:pr-{inst['number']}"
                image_record = image_info.get(image_key)
                
                # Skip this instance if it's not validated
                if not image_record:
                    print(f"Warning: Image record for {image_key} not found in image_info.yaml. Skipping instance.")
                    continue 

                if not image_record.get("validated", False):
                    print(f"Skipping unvalidated instance: {image_key}")
                    continue

            # parse RunSummary for each summary block
            run    = RunSummary(**inst["run_result"])
            tp_res = RunSummary(**inst["test_patch_result"])
            fp_res = RunSummary(**inst["fix_patch_result"])

            try:
                # build TaskData
                task = TaskData(
                    org               = inst["org"],
                    repo              = inst["repo"],
                    number            = inst["number"],
                    state             = inst["state"],
                    title             = inst["title"],
                    body              = inst["body"] if inst["body"] is not None else "",
                    base              = inst["base"],
                    resolved_issues   = inst["resolved_issues"],
                    fix_patch         = inst["fix_patch"],
                    test_patch        = inst["test_patch"],
                    fixed_tests       = {k: TestResultDetail(**v) for k,v in inst.get("fixed_tests", {}).items()},
                    p2p_tests         = {k: TestResultDetail(**v) for k,v in inst.get("p2p_tests", {}).items()},
                    f2p_tests         = {k: TestResultDetail(**v) for k,v in inst.get("f2p_tests", {}).items()},
                    s2p_tests         = {k: TestResultDetail(**v) for k,v in inst.get("s2p_tests", {}).items()},
                    n2p_tests         = {k: TestResultDetail(**v) for k,v in inst.get("n2p_tests", {}).items()},
                    run_result        = run,
                    test_patch_result = tp_res,
                    fix_patch_result  = fp_res,
                    instance_id       = inst["instance_id"] if "instance_id" in inst else f"{inst['org']}__{inst['repo']}_{inst['number']}",
                )

                # wrap
                example = SWEBenchExample(
                    problem   = generate_problem_statement(inst),
                    unique_id = f"{inst['org']}__{inst['repo']}_{inst['number']}",
                    solution  = "",
                    answer    = "",
                    metadata  = MSWEBenchMetadata(
                        cwd              = f"/home/<USER>",
                        docker_image     = get_image_name(inst, image_info),
                        task             = task,
                        project_structure = inst.get("project_structure", "")
                    )
                )

                processed_lines.append(example.json())
                line_count += 1
            except Exception as e:
                print(line)
                print(f"Error processing line {line_count + 1}")
                raise
    
    # Create output directory structure
    output_folder = f"{language}{line_count}"
    full_output_dir = os.path.join(out_dir, output_folder)
    os.makedirs(full_output_dir, exist_ok=True)
    
    # Write to final output file
    out_jsonl = os.path.join(full_output_dir, "result.jsonl")
    with open(out_jsonl, "w") as fout:
        for processed_line in processed_lines:
            fout.write(processed_line)
            fout.write("\n")

    print(f"Wrote {line_count} lines to {out_jsonl!r}")

def _consolidate_jsonl_files(jsonl_files: list, output_path: str) -> int:
    """
    Helper function to consolidate JSONL files into a single file.
    
    Args:
        jsonl_files: List of JSONL file paths to consolidate
        output_path: Path where the consolidated file will be written
        
    Returns:
        Number of lines in the consolidated file
    """
    total_lines = 0
    
    with open(output_path, "w", encoding="utf-8") as fout:
        for jsonl_file in sorted(jsonl_files):
            # print(f"Processing {jsonl_file}...")
            with open(jsonl_file, "r", encoding="utf-8") as fin:
                for line in fin:
                    line = line.strip()
                    if line:  # Skip empty lines
                        fout.write(line)
                        fout.write("\n")
                        total_lines += 1
    
    return total_lines

def _consolidate_with_config(input_folder: str, output_dir: str, filename_template: str, file_filter=None):
    """
    Generic helper function to consolidate JSONL files with configurable options.
    
    Args:
        input_folder: Path to folder containing JSONL files
        output_dir: Directory where the consolidated file will be written
        filename_template: Template for output filename (should contain {} for line count)
        file_filter: Optional function to filter which files to include
    """
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all JSONL files in the input folder
    jsonl_pattern = os.path.join(input_folder, "*.jsonl")
    all_jsonl_files = glob.glob(jsonl_pattern)
    
    # Apply filter if provided
    if file_filter:
        jsonl_files = [f for f in all_jsonl_files if file_filter(f)]
    else:
        jsonl_files = all_jsonl_files
    
    if not jsonl_files:
        print(f"No JSONL files found in {input_folder}")
        return
    
    temp_output = os.path.join(output_dir, "temp_consolidated.jsonl")
    total_lines = _consolidate_jsonl_files(jsonl_files, temp_output)
    
    # Rename to final filename with line count
    final_output = os.path.join(output_dir, filename_template.format(total_lines))
    os.rename(temp_output, final_output)
    
    print(f"Consolidated {len(jsonl_files)} files into {final_output}")
    print(f"Total lines: {total_lines}")

def consolidate_by_language(root_folder: str, language: str, outputs_dir: str = "consolidated"):
    """
    Consolidate all JSONL files in root_folder/language into a single JSONL file.
    
    Args:
        root_folder: Path to folder containing the language folders
        language: the name of the subfolder containing the JSONL files to consolidate
        outputs_dir: Directory where the consolidated file will be written
    """
    
    input_folder = os.path.join(root_folder, language)
    filename_template = f"{language}{{}}.jsonl"
    
    _consolidate_with_config(input_folder, outputs_dir, filename_template)

def consolidate(input_folder: str):
    """
    Consolidate all JSONL files in the given folder into a single JSONL file,
    excluding files that start with "all".
    
    Args:
        input_folder: Path to folder containing JSONL files to consolidate
    """
    
    # Filter function to exclude files starting with "all"
    def exclude_all_files(filepath: str) -> bool:
        return not os.path.basename(filepath).startswith("all")
    
    filename_template = "all_{}.jsonl"
    
    _consolidate_with_config(input_folder, input_folder, filename_template, exclude_all_files)

def main():
    
    # first let's consolidate all jsonl files by language
    languages = ["c", "cpp", "go", "java", "js", "rust", "ts"]
    root_folder = os.path.expanduser("~/code/msweb-hf-data-from-orngcresco")
    consolidated_dir = "consolidated"
    for language in languages:
        print(f"Consolidating {language}...")
        consolidate_by_language(root_folder, language, outputs_dir=consolidated_dir)
    # then also consolidate all jsonl files into a single file
    consolidate(consolidated_dir)

    # now let's do the conversion for all the consolidated files
    yaml_path = "../data/image_info.yaml"
    jsonl_files = glob.glob(os.path.join(consolidated_dir, "*.jsonl"))
    for jsonl_file in sorted(jsonl_files):
        print(f"Converting {jsonl_file}...")
        convert_jsonl(jsonl_file, yaml_path, out_dir="peaval_formatted",
                      only_process_validated_instances=True)

if __name__ == "__main__":
    main()