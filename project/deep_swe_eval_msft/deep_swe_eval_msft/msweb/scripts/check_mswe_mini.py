#!/usr/bin/env python3
"""
Check if each language subset matches the corresponding instances from the full MSWE mini set.
Validates that subset instances are identical to their counterparts in the full set.
"""
import os
import json
import argparse
from typing import Dict, List, Set, Any
from collections import defaultdict
from pathlib import Path

def load_jsonl(file_path: str) -> List[dict]:
    """Load JSONL file and return list of instances."""
    instances = []
    with open(file_path, "r", encoding="utf-8") as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
            try:
                instances.append(json.loads(line))
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON in {file_path} at line {line_num}: {e}")
    return instances

def get_instance_key(instance: dict) -> str:
    """Generate unique key for instance identification."""
    # Use unique_id as the primary key
    if "unique_id" in instance:
        return instance["unique_id"]
    
    # Fallback to problem hash if unique_id not available
    problem = instance.get("problem", "")
    return str(hash(problem))

def deep_compare(obj1: Any, obj2: Any, path: str = "") -> List[str]:
    """Deep compare two objects and return list of differences."""
    differences = []
    
    if type(obj1) != type(obj2):
        differences.append(f"{path}: Type mismatch - {type(obj1).__name__} vs {type(obj2).__name__}")
        return differences
    
    if isinstance(obj1, dict):
        all_keys = set(obj1.keys()) | set(obj2.keys())
        for key in all_keys:
            new_path = f"{path}.{key}" if path else key
            
            if key not in obj1:
                differences.append(f"{new_path}: Missing in first object")
            elif key not in obj2:
                differences.append(f"{new_path}: Missing in second object")
            else:
                differences.extend(deep_compare(obj1[key], obj2[key], new_path))
    
    elif isinstance(obj1, list):
        if len(obj1) != len(obj2):
            differences.append(f"{path}: List length mismatch - {len(obj1)} vs {len(obj2)}")
        else:
            for i, (item1, item2) in enumerate(zip(obj1, obj2)):
                new_path = f"{path}[{i}]"
                differences.extend(deep_compare(item1, item2, new_path))
    
    else:
        if obj1 != obj2 and path not in [
            # 'metadata.project_structure', "metadata.task.repo", "metadata.task.created_at", "metadata.task.version",
            "metadata.task.instance_id", "metadata.project_structure",
        ]:
            differences.append(f"{path}: Value mismatch - {repr(obj1)} vs {repr(obj2)}")
    
    return differences

def check_mswe_mini(
    full_set_path: str,
    root_folder: str,
    language_folders: List[str],
    output_file: str = "inconsistency_report.txt"
):
    """
    Check if subset instances match the full set instances.
    
    Args:
        full_set_path: Path to the full MSWE mini JSONL file
        root_folder: Root directory containing language subset folders
        language_folders: List of language folder names containing result.jsonl
        output_file: File to save inconsistency report
    """
    
    print(f"Loading full set from {full_set_path}...")
    
    # Load full set instances
    try:
        full_instances = load_jsonl(full_set_path)
    except FileNotFoundError:
        print(f"Error: Full set file not found at {full_set_path}")
        return
    
    print(f"Loaded {len(full_instances)} instances from full set")
    
    # Create lookup dictionary for full set instances
    full_set_lookup = {}
    for instance in full_instances:
        key = get_instance_key(instance)
        full_set_lookup[key] = instance
    
    print(f"Created lookup with {len(full_set_lookup)} unique instances")
    
    # Process the provided language folders
    subset_files = []
    languages = []
    
    for language_folder in language_folders:
        folder_path = os.path.join(root_folder, language_folder)
        
        if not os.path.isdir(folder_path):
            print(f"Warning: {folder_path} is not a directory, skipping")
            continue
            
        # Look for result.jsonl in this directory
        result_file = os.path.join(folder_path, "result.jsonl")
        if os.path.exists(result_file):
            subset_files.append(result_file)
            languages.append(language_folder)
            print(f"Found subset file: {result_file}")
        else:
            print(f"Warning: result.jsonl not found in {folder_path}")
    
    if not subset_files:
        print("No valid subset files found in the provided language folders")
        return
    
    # Check each subset
    inconsistencies = []
    total_checked = 0
    total_missing = 0
    total_inconsistent = 0
    
    for subset_file, language in zip(subset_files, languages):
        print(f"\nChecking {language} subset...")
        
        try:
            subset_instances = load_jsonl(subset_file)
        except Exception as e:
            print(f"Error loading {subset_file}: {e}")
            continue
        
        print(f"Loaded {len(subset_instances)} instances from {language} subset")
        
        for i, subset_instance in enumerate(subset_instances):
            total_checked += 1
            key = get_instance_key(subset_instance)
            
            # Check if instance exists in full set
            if key not in full_set_lookup:
                total_missing += 1
                inconsistencies.append(f"MISSING: {language}[{i}] with key '{key}' not found in full set")
                continue
            
            # Deep compare instances
            full_instance = full_set_lookup[key]
            differences = deep_compare(subset_instance, full_instance)
            
            if differences:
                total_inconsistent += 1
                inconsistencies.append(f"\nINCONSISTENT: {language}[{i}] with key '{key}':")
                for diff in differences:
                    inconsistencies.append(f"  - {diff}")
    
    # Write report
    report_lines = [
        "MSWE Mini Subset Consistency Check Report",
        "=" * 50,
        f"Total instances checked: {total_checked}",
        f"Missing from full set: {total_missing}",
        f"Inconsistent instances: {total_inconsistent}",
        f"Consistent instances: {total_checked - total_missing - total_inconsistent}",
        "",
        "Details:",
        "-" * 20
    ]
    
    if inconsistencies:
        report_lines.extend(inconsistencies)
    else:
        report_lines.append("All instances are consistent with the full set!")
    
    # Write to file
    with open(output_file, "w", encoding="utf-8") as f:
        f.write("\n".join(report_lines))
    
    # Print summary
    print(f"\n" + "=" * 50)
    print("SUMMARY:")
    print(f"Total instances checked: {total_checked}")
    print(f"Missing from full set: {total_missing}")
    print(f"Inconsistent instances: {total_inconsistent}")
    print(f"Consistent instances: {total_checked - total_missing - total_inconsistent}")
    print(f"\nReport saved to: {output_file}")
    
    if total_missing > 0 or total_inconsistent > 0:
        print("⚠️  Issues found! Check the report for details.")
    else:
        print("✅ All subset instances are consistent with the full set!")

def main():
    """Main function to run the consistency check."""
    parser = argparse.ArgumentParser(
        description="Check consistency between MSWE mini subsets and full set",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "--full_set",
        type=str,
        required=True,
        help="Path to the full MSWE mini JSONL file"
    )
    
    parser.add_argument(
        "--root_folder",
        type=str,
        required=True,
        help="Root directory containing language subset folders"
    )
    
    parser.add_argument(
        "--language_folders",
        type=str,
        nargs="+",
        required=True,
        help="List of language folder names containing result.jsonl"
    )
    
    parser.add_argument(
        "--output",
        type=str,
        default="inconsistency_report.txt",
        help="Output file for inconsistency report (default: inconsistency_report.txt)"
    )
    
    args = parser.parse_args()
    Path(args.output).parent.mkdir(parents=True, exist_ok=True)
    
    check_mswe_mini(
        full_set_path=args.full_set,
        root_folder=args.root_folder,
        language_folders=args.language_folders,
        output_file=args.output
    )

if __name__ == "__main__":
    main()
