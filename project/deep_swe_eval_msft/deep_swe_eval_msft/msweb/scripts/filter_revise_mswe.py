#!/usr/bin/env python3
"""
Filter and revise MSWE mini subset instances based on check_mswe_mini results.
- Drop instances not found in full set
- Revise inconsistent values to match full set values
- Save filtered and revised instances to new files
"""
import os
import json
import argparse
from typing import Dict, List, Set, Any
from copy import deepcopy

def load_jsonl(file_path: str) -> List[dict]:
    """Load JSONL file and return list of instances."""
    instances = []
    with open(file_path, "r", encoding="utf-8") as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
            try:
                instances.append(json.loads(line))
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON in {file_path} at line {line_num}: {e}")
    return instances

def save_jsonl(instances: List[dict], file_path: str):
    """Save list of instances to JSONL file."""
    with open(file_path, "w", encoding="utf-8") as f:
        for instance in instances:
            f.write(json.dumps(instance))
            f.write("\n")

def get_instance_key(instance: dict) -> str:
    """Generate unique key for instance identification."""
    # Use unique_id as the primary key
    if "unique_id" in instance:
        return instance["unique_id"]
    
    # Fallback to problem hash if unique_id not available
    problem = instance.get("problem", "")
    return str(hash(problem))

def set_nested_value(obj: dict, path: str, value: Any):
    """Set value at nested path in dictionary."""
    keys = path.split('.')
    current = obj
    
    # Navigate to the parent of the target key
    for key in keys[:-1]:
        if '[' in key and ']' in key:
            # Handle list indices like "key[0]"
            base_key = key.split('[')[0]
            index = int(key.split('[')[1].split(']')[0])
            
            if base_key not in current:
                current[base_key] = []
            while len(current[base_key]) <= index:
                current[base_key].append({})
            current = current[base_key][index]
        else:
            if key not in current:
                current[key] = {}
            current = current[key]
    
    # Set the final value
    final_key = keys[-1]
    if '[' in final_key and ']' in final_key:
        # Handle list indices
        base_key = final_key.split('[')[0]
        index = int(final_key.split('[')[1].split(']')[0])
        
        if base_key not in current:
            current[base_key] = []
        while len(current[base_key]) <= index:
            current[base_key].append(None)
        current[base_key][index] = value
    else:
        current[final_key] = value

def get_nested_value(obj: dict, path: str) -> Any:
    """Get value at nested path in dictionary."""
    keys = path.split('.')
    current = obj
    
    for key in keys:
        if '[' in key and ']' in key:
            # Handle list indices like "key[0]"
            base_key = key.split('[')[0]
            index = int(key.split('[')[1].split(']')[0])
            current = current[base_key][index]
        else:
            current = current[key]
    
    return current

def deep_compare(obj1: Any, obj2: Any, path: str = "") -> List[tuple]:
    """Deep compare two objects and return list of (path, value1, value2) differences."""
    differences = []
    
    if type(obj1) != type(obj2):
        differences.append((path, obj1, obj2))
        return differences
    
    if isinstance(obj1, dict):
        all_keys = set(obj1.keys()) | set(obj2.keys())
        for key in all_keys:
            new_path = f"{path}.{key}" if path else key
            
            if key not in obj1:
                differences.append((new_path, None, obj2[key]))
            elif key not in obj2:
                differences.append((new_path, obj1[key], None))
            else:
                differences.extend(deep_compare(obj1[key], obj2[key], new_path))
    
    elif isinstance(obj1, list):
        if len(obj1) != len(obj2):
            differences.append((path, obj1, obj2))
        else:
            for i, (item1, item2) in enumerate(zip(obj1, obj2)):
                new_path = f"{path}[{i}]"
                differences.extend(deep_compare(item1, item2, new_path))
    
    else:
        if obj1 != obj2:
            differences.append((path, obj1, obj2))
    
    return differences

def filter_revise_mswe(
    full_set_path: str,
    root_folder: str,
    language_folders: List[str],
    root_output_dir: str
):
    """
    Filter and revise subset instances based on full set.
    
    Args:
        full_set_path: Path to the full MSWE mini JSONL file
        root_folder: Root directory containing language subset folders
        language_folders: List of language folder names containing result.jsonl
        root_output_dir: Root output directory to save filtered/revised instances
    """
    
    print(f"Loading full set from {full_set_path}...")
    
    # Load full set instances
    try:
        full_instances = load_jsonl(full_set_path)
    except FileNotFoundError:
        print(f"Error: Full set file not found at {full_set_path}")
        return
    
    print(f"Loaded {len(full_instances)} instances from full set")
    
    # Create lookup dictionary for full set instances
    full_set_lookup = {}
    for instance in full_instances:
        key = get_instance_key(instance)
        full_set_lookup[key] = instance
    
    print(f"Created lookup with {len(full_set_lookup)} unique instances")
    
    # Process each language folder
    for language_folder in language_folders:
        folder_path = os.path.join(root_folder, language_folder)
        
        if not os.path.isdir(folder_path):
            print(f"Warning: {folder_path} is not a directory, skipping")
            continue
            
        # Look for result.jsonl in this directory
        result_file = os.path.join(folder_path, "result.jsonl")
        if not os.path.exists(result_file):
            print(f"Warning: result.jsonl not found in {folder_path}")
            continue
        
        print(f"\nProcessing {language_folder}...")
        
        try:
            subset_instances = load_jsonl(result_file)
        except Exception as e:
            print(f"Error loading {result_file}: {e}")
            continue
        
        print(f"Loaded {len(subset_instances)} instances from {language_folder} subset")
        
        # Filter and revise instances
        filtered_revised_instances = []
        dropped_count = 0
        revised_count = 0
        
        for i, subset_instance in enumerate(subset_instances):
            key = get_instance_key(subset_instance)
            
            # Check if instance exists in full set
            if key not in full_set_lookup:
                print(f"  Dropping instance [{i}] with key '{key}' (not found in full set)")
                dropped_count += 1
                continue
            
            # Deep compare and revise differences
            full_instance = full_set_lookup[key]
            differences = deep_compare(subset_instance, full_instance)
            
            if differences:
                print(f"  Revising instance [{i}] with key '{key}' ({len(differences)} differences)")
                revised_instance = deepcopy(subset_instance)
                
                for path, subset_value, full_value in differences:
                    try:
                        set_nested_value(revised_instance, path, full_value)
                        print(f"    - {path}: {repr(subset_value)} -> {repr(full_value)}")
                    except Exception as e:
                        print(f"    - Error setting {path}: {e}")
                
                filtered_revised_instances.append(revised_instance)
                revised_count += 1
            else:
                # Instance is consistent, keep as is
                filtered_revised_instances.append(subset_instance)
        
        # Save filtered and revised instances
        output_folder_name = f"{language_folder}_{len(filtered_revised_instances)}"
        output_folder_path = os.path.join(root_output_dir, output_folder_name)
        os.makedirs(output_folder_path, exist_ok=True)
        
        output_file = os.path.join(output_folder_path, "result.jsonl")
        save_jsonl(filtered_revised_instances, output_file)
        
        print(f"  Results for {language_folder}:")
        print(f"    Original instances: {len(subset_instances)}")
        print(f"    Dropped instances: {dropped_count}")
        print(f"    Revised instances: {revised_count}")
        print(f"    Final instances: {len(filtered_revised_instances)}")
        print(f"    Saved to: {output_file}")

def main():
    """Main function to run the filter and revision process."""
    parser = argparse.ArgumentParser(
        description="Filter and revise MSWE mini subsets based on full set",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "--full_set",
        type=str,
        required=True,
        help="Path to the full MSWE mini JSONL file"
    )
    
    parser.add_argument(
        "--root_folder",
        type=str,
        required=True,
        help="Root directory containing language subset folders"
    )
    
    parser.add_argument(
        "--language_folders",
        type=str,
        nargs="+",
        required=True,
        help="List of language folder names containing result.jsonl"
    )
    
    parser.add_argument(
        "--root_output_dir",
        type=str,
        required=True,
        help="Root output directory to save filtered/revised instances"
    )
    
    args = parser.parse_args()
    
    filter_revise_mswe(
        full_set_path=args.full_set,
        root_folder=args.root_folder,
        language_folders=args.language_folders,
        root_output_dir=args.root_output_dir
    )

if __name__ == "__main__":
    main()
