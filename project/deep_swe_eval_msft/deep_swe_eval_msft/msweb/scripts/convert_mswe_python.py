#!/usr/bin/env python3
"""
Transform input JSONL + image_info.yaml into result.jsonl using Pydantic models,
including full test-result metadata.
"""
import os
import json
import yaml
from typing import Literal, List, Dict
from pydantic import BaseModel, Field
from datetime import datetime

from deep_swe_eval_msft.swe_bench.preparedness.swe_bench_metadata import SweBenchMetadata, SweBenchTaskData, ContainerResources

# --- helpers ---
def generate_problem_statement(inst: dict) -> str:
    return inst['problem_statement']

class SWEBenchExample(BaseModel):
    problem:   str
    unique_id: str
    solution:  str = ""
    answer:    str = ""
    metadata:  SweBenchMetadata

# --- main script ---
def main(
    in_jsonl: str = "/Users/<USER>/data/qingruzhang/swe/eval_data/mswe_mini/all_languages/python/python_50.jsonl",
):
    output_dir = "./peaval_formatted/python50"
    os.makedirs(output_dir, exist_ok=True)
    out_jsonl = os.path.join(output_dir, "result.jsonl")
    with open(in_jsonl, "r") as fin, open(out_jsonl, "w") as fout:
        for line in fin:
            line = line.strip()
            if not line:
                continue
            inst = json.loads(line)

            try:
                task = SweBenchTaskData(
                    repo=inst["repo"],
                    instance_id=inst["instance_id"],
                    base_commit=inst["base_commit"],
                    patch=inst["patch"],
                    test_patch=inst["test_patch"],
                    problem_statement=inst["problem_statement"],
                    hints_text=inst["hints_text"],
                    created_at=datetime.fromtimestamp(
                        inst["created_at"] / 1000).isoformat(),
                    version=str(inst["version"]),
                    FAIL_TO_PASS=inst["FAIL_TO_PASS"],
                    PASS_TO_PASS=inst["PASS_TO_PASS"],
                    environment_setup_commit=inst["environment_setup_commit"],
                )

                # Build the top-level metadata (SweBenchMetadata)
                docker_image = f"acrbuiltincaasglobalame.azurecr.io/caas-swe-bench:20250115a-{inst['instance_id']}"
                metadata = SweBenchMetadata(
                    cwd=f"/testbed",
                    docker_image=docker_image,
                    resources=inst.get("resources", {"cpu": "16", "memory": "16g"}),
                    limits=inst.get("limits", {"cpu": "16", "memory": "16g"}),
                    allow_internet=inst.get("allow_internet", False),
                    project_structure=inst.get("project_structure", ""),
                    task=task,
                )

                example = SWEBenchExample(
                    problem   = generate_problem_statement(inst),
                    unique_id = inst["instance_id"],
                    solution  = "",
                    answer    = "",
                    metadata  = metadata
                )
                fout.write(example.json())
            except Exception as e:
                print(line)
                print(e)
                raise
            fout.write("\n")

    print(f"Wrote {out_jsonl!r}")

if __name__ == "__main__":
    main()