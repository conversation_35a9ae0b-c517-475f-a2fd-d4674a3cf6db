#!/usr/bin/env python3
"""
Split MSWE mini full set JSONL into language-specific subsets.
Reads multi_swe_bench_mini.jsonl and splits instances by language field.
"""
import os
import json
from collections import defaultdict
from typing import Dict, List

def split_mswe_mini(
    input_jsonl: str = "/Users/<USER>/data/qingruzhang/swe/eval_data/mswe_mini/mswe_mini_hf/multi_swe_bench_mini.jsonl",
    output_base_dir: str = "/Users/<USER>/data/qingruzhang/swe/eval_data/mswe_mini/all_languages"
):
    """
    Split MSWE mini dataset by language.
    
    Args:
        input_jsonl: Path to the full MSWE mini JSONL file
        output_base_dir: Base directory for output language folders
    """
    
    # Dictionary to collect instances by language
    language_instances: Dict[str, List[dict]] = defaultdict(list)
    
    # Read and group instances by language
    print(f"Reading instances from {input_jsonl}...")
    total_instances = 0
    
    with open(input_jsonl, "r", encoding="utf-8") as fin:
        for line_num, line in enumerate(fin, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                instance = json.loads(line)
                language = instance.get("language")
                
                if not language:
                    print(f"Warning: Line {line_num} has no 'language' field. Skipping.")
                    continue
                
                language_instances[language].append(instance)
                total_instances += 1
                
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON on line {line_num}: {e}")
                continue
            except Exception as e:
                print(f"Error processing line {line_num}: {e}")
                continue
    
    print(f"Total instances read: {total_instances}")
    print(f"Languages found: {sorted(language_instances.keys())}")
    
    # Create output directories and write language-specific files
    for language, instances in language_instances.items():
        language_dir = os.path.join(output_base_dir, language)
        os.makedirs(language_dir, exist_ok=True)
        
        # Create filename with instance count
        instance_count = len(instances)
        output_filename = f"{language}_{instance_count}.jsonl"
        output_path = os.path.join(language_dir, output_filename)
        
        # Write instances to language-specific file
        with open(output_path, "w", encoding="utf-8") as fout:
            for instance in instances:
                fout.write(json.dumps(instance))
                fout.write("\n")
        
        print(f"Created {output_path} with {instance_count} instances")
    
    # Print summary
    print("\nSummary:")
    for language in sorted(language_instances.keys()):
        count = len(language_instances[language])
        print(f"  {language}: {count} instances")
    
    print(f"\nTotal instances processed: {sum(len(instances) for instances in language_instances.values())}")

def main():
    """Main function to run the split operation."""
    split_mswe_mini()

if __name__ == "__main__":
    main()
