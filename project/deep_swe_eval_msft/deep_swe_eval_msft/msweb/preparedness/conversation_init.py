import textwrap
from typing import Any, Literal, Sequence

import chat
from caas_utils.conversation_init import (
    InstructionInsertionFormat,
    datapoint_rng,
    get_style_criteria_instructions,
)

from deep_swe_eval_msft.data_converter.padawan_v2_prompt import get_user_message


def conversation_init_ms_bench_fn_oai(
    *,
    datapoint: dict[str, Any],
) -> Sequence[chat.Message]:
    problem = datapoint["problem"]
    # instance_id = datapoint["metadata"]["task"]["instance_id"]
    instance_id = datapoint["unique_id"]
    workdir = datapoint["metadata"]["cwd"]
    repo_name = datapoint["metadata"]["task"]["org"] + "/" + datapoint["metadata"]["task"]["repo"]
    # following the same style as project/swe_bench/swe_bench_eval_msft/swe_bench_eval_msft/run_oai.py
    user_prompt = f"You are an AI software engineering agent.\n"
    user_prompt += f"Use `functions.run` to execute commands to accomplish the task. Avoid using interactive exec (setting `session_name`) unless it is absolutely necessary.\n"
    user_prompt += '''Examples of using `functions.run`:
- Running a script: {"cmd":["python","test.py"],"timeout":10000}
- Running some Python code: {"cmd":["python","-c","x = 0\nfor i in range(10):\n  x += i\nprint(x)"]}
- Use `apply_patch` to apply patches to a file: {"cmd":["python3", "/python_tools/apply_patch/apply_patch.py","*** Begin Patch\n*** Update File: path/to/file.py\n@@ def example():\n-pass\n+return 123\n*** End Patch"]}. Note that the apply_patch tool does not support absolute paths, only relative paths for the file to be updated. Moreover, the apply_patch tool always returns a "Done!" response, whether the patch is successfully applied or not. Pay careful attention to any error messages preceding the "Done!" response, which will tell you if the patch was successfully applied or not. A successful application will return no error messages before the "Done!" response.\n#Task'''

    user_prompt2 = f'Please fix the following issue \'{instance_id}\' in \'{repo_name}\' repository. Please resolve the issue in the problem below by editing and testing code files in your current code execution session. The repository is cloned in the `{workdir}` folder. You must fully solve the problem for your answer to be considered correct.\n'
    user_prompt2 += f'If run python, run the code by using the pre-installed python environment with `python xxx.py`. The current repo does not contain test files for this issue. You may need to implement or modify test files by yourserlf if you want to run python test. \n'

    user_prompt2 += f'Hint: You may ignore .git folder in `{workdir}` to avoid useless files. \n'
    user_prompt2 += f'Hint: Better first identify relevant files and functions through the issue, and then fix the errors. \n\n'

    user_prompt2 += "Problem statement: \n"
    user_prompt2 += f'{problem}\n\n'

    a = [chat.Message.user(user_prompt), chat.Message.user(user_prompt2)]
    return a


# for gpt5
def conversation_init_ms_bench_fn_oai_gpt5(
    *,
    datapoint: dict[str, Any],
) -> Sequence[chat.Message]:
    problem = datapoint["problem"]
    # instance_id = datapoint["metadata"]["task"]["instance_id"]
    instance_id = datapoint["unique_id"]
    workdir = datapoint["metadata"]["cwd"]
    repo_name = datapoint["metadata"]["task"]["org"] + "/" + datapoint["metadata"]["task"]["repo"]

    developer_prompt = '''In this environment, you can run `bash -lc <apply_patch_command>` to execute a diff/patch against a file, where <apply_patch_command> is a specially formatted apply patch command representing the diff you wish to execute. A valid <apply_patch_command> looks like:

apply_patch << 'PATCH'
*** Begin Patch
[YOUR_PATCH]
*** End Patch
PATCH

Where [YOUR_PATCH] is the actual content of your patch.
Always verify your changes extremely thoroughly. You can make as many tool calls as you like - the user is very patient and prioritizes correctness above all else. Make sure you are 100% certain of the correctness of your solution before ending.
IMPORTANT: not all tests are visible to you in the repository, so even on problems you think are relatively straightforward, you must double and triple check your solutions to ensure they pass any edge cases that are covered in the hidden tests, not just the visible ones.'''

    user_prompt = f'Please fix the following issue \'{instance_id}\' in \'{repo_name}\' repository. Please resolve the issue in the problem below by editing and testing code files in your current code execution session. The repository is cloned in the `{workdir}` folder. You must fully solve the problem for your answer to be considered correct.\n'
    user_prompt += "Problem statement: \n"
    user_prompt += f'{problem}\n\n'

    a = [chat.Message.developer(developer_prompt),
         chat.Message.user(user_prompt)]
    return a


def conversation_init_ms_bench_fn_padawan(
    *,
    datapoint: dict[str, Any],
) -> Sequence[chat.Message]:
    problem = datapoint["problem"]
    #instance_id = datapoint["metadata"]["task"]["instance_id"]
    instance_id = datapoint["unique_id"]
    workdir = datapoint["metadata"]["cwd"]
    repo_name = datapoint["metadata"]["task"]["org"] + "/" + datapoint["metadata"]["task"]["repo"]

    user_prompt = get_user_message(repo_name, instance_id, problem, workdir=workdir)
    
    a = [chat.Message.user(user_prompt)]
    
    return a

def conversation_init_ms_bench_fn_vsc(
    *,
    datapoint: dict[str, Any],
) -> Sequence[chat.Message]:
    problem = datapoint["problem"]
    #instance_id = datapoint["metadata"]["task"]["instance_id"]
    instance_id = datapoint["unique_id"]
    workdir = datapoint["metadata"]["cwd"]
    repo_name = datapoint["metadata"]["task"]["org"] + "/" + datapoint["metadata"]["task"]["repo"]
    project_structure = datapoint["metadata"]["project_structure"]

    workspace_msg = f"""<workspace_info>
I am working in a workspace with the following folders:
- {workdir} 
{project_structure}
This is the state of the context at this point in the conversation. The view of the workspace structure may be truncated. You can use tools to collect more context if needed.
</workspace_info>

<reminderInstructions>
You are an agent—keep going until the user's query is completely resolved before ending your turn. ONLY stop if solved or genuinely blocked.
Take action when possible; the user expects you to do useful work without unnecessary questions.
After any parallel, read-only context gathering, give a concise progress update and what's next.
Avoid repetition across turns: don't restate unchanged plans or sections (like the todo list) verbatim; provide delta updates or only the parts that changed.
Tool batches: You MUST preface each batch with a one-sentence why/what/outcome preamble.
Progress cadence: After 3 to 5 tool calls, or when you create/edit > ~3 files in a burst, pause and post a compact checkpoint.
Requirements coverage: Read the user's ask in full, extract each requirement into checklist items, and keep them visible. Do not omit a requirement. If something cannot be done with available tools, note why briefly and propose a viable alternative.
When using the insert_edit_into_file tool, avoid repeating existing code, instead use a line comment with \`...existing code...\` to represent regions of unchanged code.
Skip filler acknowledgements like "Sounds good" or "Okay, I will…". Open with a purposeful one-liner about what you're doing next.
When sharing setup or run steps, present terminal commands in fenced code blocks with the correct language tag. Keep commands copyable and on separate lines.
Avoid definitive claims about the build or runtime setup unless verified from the provided context (or quick tool checks). If uncertain, state what's known from attachments and proceed with minimal steps you can adapt later.
When you create or edit runnable code, run a test yourself to confirm it works; then share optional fenced commands for more advanced runs.
For non-trivial code generation, produce a complete, runnable solution: necessary source files, a tiny runner or test/benchmark harness, a minimal `README.md`, and updated dependency manifests (e.g., `package.json`, `requirements.txt`, `pyproject.toml`). Offer quick "try it" commands and optional platform-specific speed-ups when relevant.
Your goal is to act like a pair programmer: be friendly and helpful. If you can do more, do more. Be proactive with your solutions, think about what the user needs and what they want, and implement it proactively.
<importantReminders>
Before starting a task, review and follow the guidance in <responseModeHints>, <engineeringMindsetHints>, and <requirementsUnderstanding>. ALWAYS start your response with a brief task receipt and a concise high-level plan for how you will proceed.
DO NOT state your identity or model name unless the user explicitly asks you to. 
You MUST use the todo list tool to plan and track your progress. NEVER skip this step, and START with this step whenever the task is multi-step. This is essential for maintaining visibility and proper execution of large tasks. Follow the todoListToolInstructions strictly.
When referring to a filename or symbol in the user's workspace, wrap it in backticks.

</importantReminders>

</reminderInstructions>
"""

    user_prompt =  f"Please fix the following issue '{instance_id}' in `{repo_name}` repository. Please resolve the issue in the problem below by editing and testing code files in your current code execution session. The code is cloned in the {workdir} folder. You must fully solve the problem for your answer to be considered correct.\n"
    #user_prompt += f'If run python, run the code by using the pre-installed python environment with `python xxx.py`. The current repo does not contain test files for this issue. You may need to implement or modify test files by yourserlf if you want to run python test. \n'
    user_prompt += f'Hint: You may ignore .git folder in `{workdir}` to avoid useless files. \n'
    user_prompt += f'Hint: Better first identify relevant files and functions through the issue, and then fix the errors. \n'
    user_prompt += "Problem statement: \n"
    user_prompt += f'{problem}\n\n'
    user_prompt += f"Please summarize all the changes you made in the end.\n"

    user_prompt = f"""<userRequest>
{user_prompt}
</userRequest>
"""
    a = [chat.Message.user(workspace_msg), chat.Message.user(user_prompt)]
    
    return a