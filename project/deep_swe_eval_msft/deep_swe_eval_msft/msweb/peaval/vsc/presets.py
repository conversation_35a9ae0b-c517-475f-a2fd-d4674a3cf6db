from typing import Any, Sequence

import chz
from berry import preset_utils
from chz_presets.core import CompositePreset, Preset
from rcall.runtime import get_func_path
from caas_utils.conversation_init import InstructionInsertionFormat
from berry.function_wrapper import FunctionWrapper
import deep_swe.datasets.configs as base_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
import deep_swe_eval_msft.msweb.peaval.vsc.dataset_config as configs
from deep_swe.datasets.config_utils import chz_path
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe_eval_msft.data_converter.data_converter import get_vardisc_args, _maybe_deliberate_tool_config

VSC_DATASET_CONFIGS = [
    (configs.CMSWebVerifiedDatasetConfigVSC, []),
    (configs.CPPMSWebVerifiedDatasetConfigVSC, []),
    (configs.GoMSWebVerifiedDatasetConfigVSC, []),
    (configs.JavaMSWebVerifiedDatasetConfigVSC, []),
    (configs.JSMSWebVerifiedDatasetConfigVSC, []),
    (configs.RustMSWebVerifiedDatasetConfigVSC, []),
    (configs.TSMSWebVerifiedDatasetConfigVSC, []),
]

# Map language names to their corresponding config classes
LANGS_TO_DATASET_CONFIG = {
    "c": configs.CMSWebVerifiedDatasetConfigVSC,
    "cpp": configs.CPPMSWebVerifiedDatasetConfigVSC,
    "go": configs.GoMSWebVerifiedDatasetConfigVSC,
    "java": configs.JavaMSWebVerifiedDatasetConfigVSC,
    "js": configs.JSMSWebVerifiedDatasetConfigVSC,
    "rust": configs.RustMSWebVerifiedDatasetConfigVSC,
    "ts": configs.TSMSWebVerifiedDatasetConfigVSC,
}


format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)


def ev_sm_bench_juice_vsc(
    juice: int | tuple[int, ...] = 768,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
    langs: str = "c-cpp-go-java-js-rust-ts",
    ) -> CompositePreset[Preset[Any]]:

    # Filter VSC_DATASET_CONFIGS based on the langs parameter
    selected_dataset_configs = [
        (dataset_config, args)
        for dataset_config, args in VSC_DATASET_CONFIGS
        if any(LANGS_TO_DATASET_CONFIG[lang.lower()] == dataset_config for lang in langs.split("-") if lang.lower() in LANGS_TO_DATASET_CONFIG)
    ]

    return preset_utils.compose_presets(
        *[
            preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    #"dataset.grader.channels_for_answer=final",  # It doesn't actually look at the answer, just for channel setup.
                    #"dataset.tool_configs.0.tool_timeout=2400",  # somehow caas can be slow
                    #*_maybe_deliberate_tool_config(start_index=1),
                    *get_vardisc_args(juice),
                ]
            )
            for dataset_config, args in selected_dataset_configs
        ],
        # swang disable this for eval, or will throw format validation error
        #format
    )

ev_sm_bench_vsc = ev_sm_bench_juice_vsc()
