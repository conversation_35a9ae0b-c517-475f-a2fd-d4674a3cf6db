import dataclasses
import logging

import structlog
import tenacity
import uuid

import caas
import chz
from caas.api import caas_api
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from deep_swe_eval_msft.msweb.preparedness.msweb_metadata import MSWEBenchMetadata
from qstar.common import datapoint
from qstar.common.tools.berry_tool_interface import ResourceContinuationError
from qstar.common.tools.caas_container.caas_container_tool import CaasContainerResourceConfig
from deep_swe_eval_msft.tools.utils import CAAS_ENDPOINT
from caas.commands import DownloadFileFromContainer, Exec, PythonProgram, UploadFile, RawExec

from caas.protocol import NetworkMode
from caas.protocol import VolumeMount
from deep_swe_eval_msft.msweb.preparedness.msweb_caas_eval import get_lang_from_repo_org
from caas.commands import Exec, UploadFile
from deep_swe_msft.tools.utils import PDW_MNT
from deep_swe_eval_msft.msweb.preparedness.msweb_caas_eval import (
    get_image_name_from_meta,
    load_image_info,
)

logger = structlog.get_logger(component=__name__)

CAAS_REGISTRY = "acrcommitcaaseastus2ame.azurecr.io"

@chz.chz(typecheck=True)
class DeepSWECaasContainerResourceConfig(CaasContainerResourceConfig):
    """
    Allows per-datapoint control over the image, resource limits, and network access.
    (As a result, sandbox=True must be enabled)
    """

    caas_endpoint: str = CAAS_ENDPOINT
    caas_idle_ttl: int = 1200
    use_terminal_server: bool = True
        
    # def name(self) -> str:
    #     # TODO this is kind of sus. Look at what this PR is saying https://github.com/openai/garden/pull/19818/files
    #     # totoally sus comment by swang
    #     return "caas_container"
    #     #return _caas_container_resource_name(
    #     #    caas_network=None, enable_network_after_setup=not self.force_disable_internet
    #     #)

    async def _initialize_resource_from_state_metadata(
        self, metadata: MSWEBenchMetadata, caas_session_state: str
    ) -> CaasContainer:
        try:
            image_info = load_image_info()
            image_name = get_image_name_from_meta(metadata, image_info)
            container = CaasContainer(
                caas_endpoint=self.caas_endpoint,
                image_name=CAAS_REGISTRY + "/" + image_name, #metadata.docker_image,
                caas_session_state=caas_session_state,
                is_owner=False,
                user=self.user,
            )
            await container.send_heartbeat()
            return container
        except (
            ValueError,
            caas.NotFoundError,
            caas.ServerError,
            caas.TransportError,
            caas.ClientError,
        ) as e:
            raise ResourceContinuationError(
                resource_name=self.name(),
                resource_state=caas_session_state.encode(),
                message=(
                    f"Failed to initialize caas container '{self.name()}' from session state "
                    f"'{caas_session_state}': {e}"
                ),
            ) from e

    @tenacity.retry(
        retry=tenacity.retry_if_exception_type(
            (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception, RuntimeError)
        ),
        wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
        stop=tenacity.stop_after_attempt(10),
        before_sleep=tenacity.before_sleep_log(
            logger,  # type: ignore
            logging.WARNING,
        ),
    )
    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        metadata = MSWEBenchMetadata.model_validate(dp.metadata)

        if self.caas_session_state is not None:
            # We are not the owner of the container, so we don't need to run the setup function.
            resource = await self._initialize_resource_from_state_metadata(
                metadata, self.caas_session_state
            )
            assert not resource.is_owner
            return resource

        if self.use_terminal_server:
            cmd = ["/server.py"]
        else:
            cmd = []

        repo_field = metadata.task.repo
        org_field = metadata.task.org

        repo_language = get_lang_from_repo_org(repo_field, org_field)

        if repo_language == "go":
            environment_variables = {"GOMAXPROCS": "16"}
        elif repo_language == "java":
            environment_variables = {
                "JAVA_TOOL_OPTIONS": " -XX:ActiveProcessorCount=4 -XX:ParallelGCThreads=2 -XX:ConcGCThreads=1 -Djava.util.concurrent.ForkJoinPool.common.parallelism=4",  # noqa: E501
                "JRUBY_HOME": "/home/<USER>/vendor/jruby",
            }
        else:
            environment_variables = {}

        caas = caas_api(endpoint=self.caas_endpoint)
        image_info = load_image_info()
        image_name = get_image_name_from_meta(metadata, image_info)
        caas_session = await caas.new_session(
            image=CAAS_REGISTRY + "/" + image_name, #metadata.docker_image,
            cmd=cmd,
            cpu_limit="50", #metadata.limits.cpu,
            memory_limit="50g", #metadata.limits.memory,
            idle_ttl=self.caas_idle_ttl,
            num_gpus=self.caas_num_gpus,
            network=NetworkMode.CAAS_DEFAULT, #CAAS_PUBLIC_ONLY,
            timeout=1200,
            volume_mounts=PDW_MNT,
            env=environment_variables,
            pids_limit=1024,
        )
        terminal_session = TerminalSession(caas_session)
        
        try:  
            output = await terminal_session.session.run(RawExec(["bash", "-c", "command -v make || echo 'NO_MAKE'"], timeout=60))
            if b'NO_MAKE' in output[1]:
                output = await terminal_session.session.run(RawExec(["bash", "-c", f"apt update && apt install -y build-essential"], timeout=600, workdir="/"))

            output = await terminal_session.session.run(RawExec(["bash", "-c", "python3 --version || echo 'NO_PYTHON'"], timeout=60))
            branchName = uuid.uuid4().hex
            checkout_cmd = f"git checkout -b {branchName} {metadata.task.base.sha}"
            
            output = await terminal_session.session.run(RawExec(["bash", "-c", f"{checkout_cmd}"], timeout=120, workdir=metadata.cwd))

            version_output = output[1].decode("utf-8").strip()
            install_python = False
            if "NO_PYTHON" in version_output:
                install_python = True
            else:
                import re
                match = re.search(r"Python (\d+)\.(\d+)", version_output)
                if match:
                    major, minor = int(match.group(1)), int(match.group(2))
                    if major < 3 or (major == 3 and minor < 8):
                        install_python = True
                else:
                    install_python = True

            if install_python:
                output = await terminal_session.session.run(RawExec(["bash", "-c", f"apt-get update && apt-get install -y   build-essential   libc6-dev   libssl-dev   zlib1g-dev   libncurses5-dev   libncursesw5-dev   libreadline-dev   libsqlite3-dev   libffi-dev   libbz2-dev   liblzma-dev   libgdbm-dev   libtirpc-dev   tk-dev   wget"], timeout=360, workdir="/"))
                output = await terminal_session.session.run(RawExec(["bash", "-c", f"cd /usr/src && wget http://www.python.org/ftp/python/3.9.18/Python-3.9.18.tgz && tar xzf Python-3.9.18.tgz && cd Python-3.9.18 && ./configure  --prefix=/usr/local  --with-ensurepip=install"], timeout=360, workdir="/"))
                output = await terminal_session.session.run(RawExec(["bash", "-c", f"cd /usr/src/Python-3.9.18 && make -j$(nproc)"], timeout=360, workdir="/"))
                output = await terminal_session.session.run(RawExec(["bash", "-c", f"cd /usr/src/Python-3.9.18 && make altinstall"], timeout=360, workdir="/"))
                output = await terminal_session.session.run(RawExec(["bash", "-c", f"update-alternatives --install /usr/bin/python3 python3 /usr/local/bin/python3.9 1"], timeout=120, workdir="/"))

            #output = await terminal_session.session.run(RawExec(["bash", "-c", f"python3 --version"], timeout=240, workdir="/"))

            if self.setup_fn is not None:
                await self.setup_fn(
                    datapoint=dataclasses.asdict(dp),
                    terminal_session=terminal_session,
                    install_node=True,
                    add_package_json=True,
                )

            # swang enable network for eval
            #if not metadata.allow_internet:
            #    await caas_session.update_network(enable_network=False)
        except RuntimeError as e:
            # Handle specific RuntimeError that may occur during setup
            with open("/var/log/supervisor/zhendongw_aswe_setup_error.log", "a") as f:
                f.write(f"RuntimeError during setup: {e}\n")
                f.write(f"Metadata: {metadata.task.instance_id}\n")
            await caas_session.close()
            raise
        except Exception as e:
            with open("/var/log/supervisor/zhendongw_aswe_setup_error.log", "a") as f:
                f.write(f"{e}\n")
            await caas_session.close()
            raise
            
        return CaasContainer(
            caas_endpoint=self.caas_endpoint,
            image_name=CAAS_REGISTRY + "/" + image_name, #metadata.docker_image,
            caas_session_state=caas_session.save(),
            user=self.user,
            is_owner=True,
        )
