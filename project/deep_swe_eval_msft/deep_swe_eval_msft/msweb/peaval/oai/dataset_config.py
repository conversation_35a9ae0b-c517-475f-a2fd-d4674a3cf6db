import chz
from functools import partial
from typing import Any, Sequence

import qstar.instance_completers
import qstar.instance_optimizers
from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import <PERSON>StageGrader
from qstar.presets.chz_utils import IsO<PERSON>ride, override
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.curriculums.function_wrapper import FunctionWrapper

from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    GraderService,
    TokenCompleterGraderService,
)

from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from token_completer import TokenCompleter

import caas_autograding.grader as caas_graders
from berry import InstanceCompleter, InstanceOptimizer, SampleAllocator
from qstar.allocators.continuous_rewards_adaptive_sample_allocator import (
    ContinuousRewardsAdaptiveSampleAllocator,
)

from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig 
from deep_swe.datasets import configs as default_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_msft.vsc_data.system_prompt import VSC_MODEL_IDENTITY, VSC_SYSTEM_PROMPT

from deep_swe_eval_msft.msweb.peaval.oai.setup import msweweb_verfied_setup_fn as msweweb_verfied_setup_fn_oai
# from deep_swe_eval_msft.msweb.peaval.vsc.setup import msweweb_verfied_setup_fn as msweweb_verfied_setup_fn_vsc
from deep_swe_eval_msft.msweb.peaval.oai.caas_resource_config import DeepSWECaasContainerResourceConfig as DeepSWECaasContainerResourceConfigOAI
# from deep_swe_eval_msft.msweb.peaval.vsc.caas_resource_config import DeepSWECaasContainerResourceConfig as DeepSWECaasContainerResourceConfigVSC
from deep_swe_eval_msft.graders.eval_unit_test import EvalTermberryGrader

# from deep_swe_eval_msft.tools.caas_vscode_tool import EvalVSCodeToolConfig
from deep_swe_eval_msft.tools.caas_oai_tool import EvalDeepSWECaasContainerToolConfigV2 as EvalOAIToolConfig
from chat.render.v4.experimental.strawberry.formatter import BerryChannel

from deep_swe_eval_msft.swe_bench.peaval.oai.dataset_config import SWEBenchVerifiedDatasetConfigOAI 

CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
CAAS_IDLE_TTL = 1200

def make_msweb_verified_graders_oai() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.msweb.peaval.oai.computer_task_grader:grade_fn_v2_oai",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def _make_tool_configs_oai(
    container_tool_config: EvalOAIToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
) -> tuple[ToolConfig, ...]:
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)

@chz.chz
class MSWebVerifiedGraderOAI(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(make_msweb_verified_graders_oai)
    
    @property
    def accepts_invalid(self) -> bool:
        """
        Generally, computer using tasks can be graded at any point in time.
        """
        return True


@chz.chz(typecheck=True)
class MSWebVerifiedDatasetConfigOAI(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = (
        # deprecated
        #"data.oaici.qstar.preparedness.datasets.msweb.ts_verified_converted"
        #"data.oaici.qstar.preparedness.datasets.msweb.cpp_verified_converted"
        #"data.oaici.qstar.preparedness.datasets.msweb.msweb_sample400"
        #"data.oaici.qstar.preparedness.datasets.msweb.ttt"
        #"data.oaici.qstar.preparedness.datasets.msweb.ttt2"
        # vsc datasets
        #"data.oaici.qstar.preparedness.datasets.msweb.all_verified_converted_vsc"
        #"data.oaici.qstar.preparedness.datasets.msweb.msweb_sample368_vsc"
        # vsc filtered, suggestion
        # increase go, need to enable new envs in grader
        #"data.datasets.swe.eval.msweb.filtered_result_sample433"
        #"data.datasets.swe.eval.msweb.filtered_result_sample425"
        #"data.datasets.swe.eval.msweb.filtered_result_sample1183" # ts208 go356 rust219 js325 cpp53 c16 java6 
        # "data.datasets.swe.eval.msweb.sample1402" # ts206 go403 rust225 js328 cpp119 c51 java70 
        #"data.datasets.swe.eval.msweb.with_project_structure.all1453"
        #"data.oaici.qstar.preparedness.datasets.msweb.ttt2_vsc"
        "data.datasets.swe.eval.msweb.2025_07_10.all1424"
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: MSWebVerifiedGraderOAI(graders=make_msweb_verified_graders_oai())
    )
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs_oai)
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigOAI(setup_fn=msweweb_verfied_setup_fn_oai,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL,
                                            #    enable_network_after_setup=True,
                                               ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter("deep_swe_eval_msft.msweb.preparedness.conversation_init:conversation_init_ms_bench_fn_oai"),
        )
    )
    
    model_identity_str: str = "You are an AI assistant accessed via an API. Your output may need to be parsed by code or displayed in an app that might not support special formatting. Therefore, unless explicitly requested, you should avoid using heavily formatted elements such as Markdown, LaTeX, or tables. Bullet lists are acceptable."
    instructions: str = ''  # keli: TBD system prompt, for now empty
    # swang align sampling with system message, longer need maybe
    #max_tokens: int | None = 262144
    #max_num_yields: int | None = 100
    
@chz.chz
class CMSWebVerifiedDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str =  (
        #"data.oaici.qstar.preparedness.datasets.msweb.all_vsc_split_by_lang.c"
        #"data.oaici.qstar.preparedness.datasets.msweb.m368_vsc_split_by_lang.c"
        #"data.datasets.swe.eval.msweb.filtered_result_sample425_by_lang.c_16"
        # "data.datasets.swe.eval.msweb.sample1402_lang.c51"
        #"data.datasets.swe.eval.msweb.with_project_structure.c47"
        "data.datasets.swe.eval.msweb.2025_07_10.c43"
    )

@chz.chz
class CPPMSWebVerifiedDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str =  (
        #"data.oaici.qstar.preparedness.datasets.msweb.all_vsc_split_by_lang.cpp"
        #"data.oaici.qstar.preparedness.datasets.msweb.m368_vsc_split_by_lang.cpp"
        #"data.datasets.swe.eval.msweb.filtered_result_sample425_by_lang.cpp_53"
        # "data.datasets.swe.eval.msweb.sample1402_lang.cpp119"
        #"data.datasets.swe.eval.msweb.with_project_structure.cpp129"
        "data.datasets.swe.eval.msweb.2025_07_10.cpp123"
    )

@chz.chz
class GoMSWebVerifiedDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str =  (
        #"data.oaici.qstar.preparedness.datasets.msweb.all_vsc_split_by_lang.go"
        #"data.oaici.qstar.preparedness.datasets.msweb.m368_vsc_split_by_lang.go"
        #"data.datasets.swe.eval.msweb.filtered_result_sample425_by_lang.go_50"
        # "data.datasets.swe.eval.msweb.sample1402_lang.go403"
        #"data.datasets.swe.eval.msweb.with_project_structure.go395"
        "data.datasets.swe.eval.msweb.2025_07_10.go379"
    )

@chz.chz
class JavaMSWebVerifiedDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str =  (
        #"data.oaici.qstar.preparedness.datasets.msweb.all_vsc_split_by_lang.java"
        #"data.oaici.qstar.preparedness.datasets.msweb.m368_vsc_split_by_lang.java"
        #"data.datasets.swe.eval.msweb.filtered_result_sample425_by_lang.java_6"
        # "data.datasets.swe.eval.msweb.sample1402_lang.java70"
        #"data.datasets.swe.eval.msweb.with_project_structure.java115"
        "data.datasets.swe.eval.msweb.2025_07_10.java99"
    )

@chz.chz
class JSMSWebVerifiedDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str =  (
        #"data.oaici.qstar.preparedness.datasets.msweb.all_vsc_split_by_lang.js"
        #"data.oaici.qstar.preparedness.datasets.msweb.m368_vsc_split_by_lang.js"
        #"data.datasets.swe.eval.msweb.filtered_result_sample425_by_lang.js_100"
        # "data.datasets.swe.eval.msweb.sample1402_lang.js328"
        #"data.datasets.swe.eval.msweb.with_project_structure.js329"
        "data.datasets.swe.eval.msweb.2025_07_10.js351"
    )

@chz.chz
class RustMSWebVerifiedDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str =  (
        #"data.oaici.qstar.preparedness.datasets.msweb.all_vsc_split_by_lang.rust"
        #"data.oaici.qstar.preparedness.datasets.msweb.m368_vsc_split_by_lang.rust"
        #"data.datasets.swe.eval.msweb.filtered_result_sample425_by_lang.rust_100"
        # "data.datasets.swe.eval.msweb.sample1402_lang.rust225"
        #"data.datasets.swe.eval.msweb.with_project_structure.rust220"
        "data.datasets.swe.eval.msweb.2025_07_10.rust222"
    )

@chz.chz
class TSMSWebVerifiedDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str =  (
        #"data.oaici.qstar.preparedness.datasets.msweb.all_vsc_split_by_lang.ts"
        #"data.oaici.qstar.preparedness.datasets.msweb.m368_vsc_split_by_lang.ts"
        #"data.datasets.swe.eval.msweb.filtered_result_sample425_by_lang.ts_100"
        # "data.datasets.swe.eval.msweb.sample1402_lang.ts206"
        #"data.datasets.swe.eval.msweb.with_project_structure.ts218"
        "data.datasets.swe.eval.msweb.2025_07_10.ts198"
    )


@chz.chz
class CMSWEMiniDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str = (
        "data.qingruzhang.swe.eval_data.mswe_mini.peaval_formatted_revise.c16_16"
    )


@chz.chz
class CPPMSWEMiniDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str = (
        "data.qingruzhang.swe.eval_data.mswe_mini.peaval_formatted_revise.cpp50_50"
    )


@chz.chz
class GoMSWEMiniDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str = (
        "data.qingruzhang.swe.eval_data.mswe_mini.peaval_formatted_revise.go48_48"
    )


@chz.chz
class JavaMSWEMiniDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str = (
        "data.qingruzhang.swe.eval_data.mswe_mini.peaval_formatted_revise.java41_41"
    )


@chz.chz
class JSMSWEMiniDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str = (
        "data.qingruzhang.swe.eval_data.mswe_mini.peaval_formatted_revise.js48_48"
    )


@chz.chz
class RustMSWEMiniDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str = (
        "data.qingruzhang.swe.eval_data.mswe_mini.peaval_formatted_revise.rust50_49"
    )


@chz.chz
class TSMSWEMiniDatasetConfigOAI(MSWebVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str = (
        "data.qingruzhang.swe.eval_data.mswe_mini.peaval_formatted_revise.ts49_49"
    )


@chz.chz
class PythonMSWEMiniDatasetConfigOAI(SWEBenchVerifiedDatasetConfigOAI, IsOverride):
    dataset_id: str = (
        "data.qingruzhang.swe.eval_data.mswe_mini.peaval_formatted_revise.python50_47"
    )