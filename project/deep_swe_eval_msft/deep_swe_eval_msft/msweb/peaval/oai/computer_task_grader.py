import shlex
from typing import TypedDict
from uuid import uuid4
import tenacity
import logging
import os
import shutil
import time
import json

import structlog

import caas
import berry
from caas import ExecError
from caas.api import caas_api
from caas.commands import Exec, RawBashScript, RawExec, UploadFile
from caas.terminal.api import TerminalSession
from caas_swe_bench_train_v2.parser import ModelPatch
from caas_utils.utils import run_with_retries
from caas.protocol import NetworkMode

from deep_swe_eval_msft.msweb.peaval.oai.setup import msweweb_verfied_setup_fn_internal
from deep_swe_eval_msft.msweb.preparedness.msweb_metadata import MSWEBenchMetadata, TaskData
from deep_swe_eval_msft.msweb.preparedness.msweb_caas_eval import (
    get_instance_dir,
    get_git_commit,
    save_git_diff,
    save_git_log,
    restore_test_patch,
    restore_tests,
    grade_instance_caas,
    strict_passed,
    REPO2LANG,
    make_instance_from_str,
    get_files_in_dir,
    get_files_in_home_dir,
)

from deep_swe_eval_msft.msweb.peaval.computer_task_grader_utils import get_model_patch, \
    GradeReport, PROTECTED_FILES, _grade_fn_v2_inner

logger = structlog.get_logger(__name__)


async def grade_fn_v2_oai(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> bool:
    metadata = MSWEBenchMetadata.model_validate(sample.gt_datapoint.metadata)
    report = await grade_fn_v2_internal_oai(
        terminal_session=terminal_session,
        metadata=metadata,
        fast=fast,
    )
    logger.info("Grading finished", **report)
    sample.ephemeral_metadata["model_patch"] = report["model_patch"]
    sample.metadata["grader"] = {
        "prompt": report["model_patch"],
        "response": report["test_output"],
        "score": int(report["passed"]),
    }
    return report["passed"]


async def grade_fn_v2_internal_oai(
    *,
    terminal_session: TerminalSession,
    metadata: MSWEBenchMetadata,
    fast: bool,
) -> GradeReport:
    repo_root = metadata.cwd
    assert isinstance(metadata.task, TaskData)

    model_patch = await get_model_patch(
        terminal_session=terminal_session,
        base_commit=metadata.task.base.sha,
        repo_root=repo_root,
        protected_files=PROTECTED_FILES,
    )

    # swang disable this for eval
    # if not model_patch.has_test_and_non_test():
    #     return {
    #         "passed": False,
    #         "test_output": "Model patch does not have both tests/non-tests",
    #         "model_patch": model_patch.compact_patch,
    #         "passed_tests": [],
    #     }

    if fast:
        # Soft reset the repository to the base commit (but don't run setup again)
        # await run_with_retries(
        #     terminal_session,
        #     f"cd {repo_root} && git add -A && git reset --hard {metadata.task.base.sha}",
        #     attempts=3,
        # )

        # hand off the model patch object instead of individual fields.
        return await _grade_fn_v2_inner(terminal_session, metadata, model_patch)

    # use the secure grading path, passing the full model patch object.
    return await _grade_fn_v2_secure_oai(metadata, model_patch)


CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"


async def _grade_fn_v2_secure_oai(
    metadata: MSWEBenchMetadata,
    model_patch: ModelPatch,
) -> GradeReport:
    """
    The "safe" route - re-initialize a fresh container and grade in there.
    """
    caas = caas_api(CAAS_ENDPOINT)
    async with caas.use_session(
        image=metadata.docker_image,
        cpu_limit=metadata.limits.cpu,
        memory_limit=metadata.limits.memory,
        network=NetworkMode.CAAS_PUBLIC_ONLY,
        keepalive_interval=30,
        timeout=1200,
    ) as session:
        grade_session = TerminalSession(session)
        await msweweb_verfied_setup_fn_internal(terminal_session=grade_session, metadata=metadata)

        return await _grade_fn_v2_inner(
            grade_session=grade_session,
            metadata=metadata,
            model_patch=model_patch,
        )

