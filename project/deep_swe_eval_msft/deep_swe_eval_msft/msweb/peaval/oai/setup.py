import os
import shlex
from typing import Any

import caas
import structlog
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas_utils.utils import run_with_retries
from deep_swe_eval_msft.msweb.preparedness.msweb_metadata import MSWEBenchMetadata, TaskData

# from deep_swe_msft.swe_bench_train_v2_vsc.setup.get_coreutils import setup_coreutils
from gpt_oss_msft.tools.oai_coreutils_setup import setup_coreutils
from deep_swe_eval_msft.msweb.preparedness.msweb_caas_eval import ensure_git_clean, clean_answer_patches


logger = structlog.get_logger(component=__name__)

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def msweweb_verfied_setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    metadata = MSWEBenchMetadata.model_validate(datapoint["metadata"])
    terminal_session.session.start_keepalive_task(keepalive_interval=60)
    try:
        await msweweb_verfied_setup_fn_internal(terminal_session=terminal_session, metadata=metadata)
        with open("/var/log/supervisor/qingruzhang_mswe_setup.log", "a") as f:
            f.write(f"Setup MSWE successfully\n")
    except Exception as err:
        with open("/var/log/supervisor/qingruzhang_mswe_setup.log", "a") as f:
            f.write(f"Failed to setup MSWE: {err}\n")
        raise RuntimeError(f"Failed to setup MSWE: {err}") from err

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def msweweb_verfied_setup_fn_internal(
    *,
    terminal_session: TerminalSession,
    metadata: MSWEBenchMetadata,
) -> None:
    task = metadata.task
    assert isinstance(task, TaskData)

    post_setup = [
        f"echo 'cd {metadata.cwd}' >> /root/.bashrc",
        "git config --global user.name User",
        "git config --global user.email user@localhost",
        #"pre-commit install || true",
    ]


    r = await terminal_session.session.run(BashScript("\n".join(post_setup), login=True, timeout=900))
    
    try:
        await clean_answer_patches(terminal_session)
        
        await ensure_git_clean(metadata, terminal_session)
    except Exception as e:
        with open("/var/log/supervisor/qingruzhang_mswe_setup_error.log", "a") as f:
            f.write(f"{e}\n")
        raise

    await setup_coreutils(terminal_session.session, {}, repo_root=metadata.cwd, setup_python_tools=True)
