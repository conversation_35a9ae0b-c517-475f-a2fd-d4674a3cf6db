WANDB_PROJECT_NAME=mswe-pevals

# vsc checkpoint
CKPT="az://orngscuscresco/twapi/mini/e/haoranxu-vsc-mix16-arm2-stage2-itc-768-juice-128-32x320822-2351/policy/step_000150/250823073816NU2G6E4I-0"

RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"

dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="amrhendy-vsc-mix16-arm2-stage2-step_000150-j768-max_yields_256-mswe_js_ts-$dt-peaval"

CMD=(
beam python --start-daemon --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken

root_config='mini.root.dev init_actors_rpc_timeout=600 driver_rpc_timeout=600 dedicated_driver_node=False'

# experiment_id=zhendongwang-mix9-pdw2-ivt-mixed-32x32-o4mini-tef05-7-tpm2-rm-lr1e-5-run9
auto_inherit_training_args=False

policy.initial_checkpoint=${CKPT}
policy.is_multimodal=True
...encoding_name=orion_200k
...harmony_renderer_name=${RENDERER_NAME}

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=2                   # swang concurrent thread in one process
peashooter.num_sampling_processes=100               # swang concurrent process in one worker
peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600

# Set this if you want to eval all checkpoints at once in parallel (Not needed if your training run just started.)
# max_workers_per_step=4 # swang nodes per step, cannot exceed total sample nodes
# max_active_steps=2    # swang active steps

eval_settings.eval_every=1
# eval_settings.min_step=150
# eval_settings.max_step=150
eval_settings.exit_on_no_new_checkpoint=True # False to keep it running and watching for new checkpoints saved from training experiment id
# Set to True if you want to evaluate step0 (your prior)
eval_settings.eval_initial_policy=True
# set it if no training experiment id
eval_settings.checkpoint_dir=${CKPT}

# data
# skip swe eval
# :deep_swe_eval_msft.swe_bench.peaval.vsc.presets:ev_sm_bench_vsc
# eval_settings.evals.0.dataset.override_target_samples_per_instance=1
# eval_settings.evals.0.dataset.max_num_yields=100

# just eval mswe
':deep_swe_eval_msft.msweb.peaval.vsc.presets:ev_sm_bench_juice_vsc(juice=768 override_target_samples_per_instance=1 max_num_yields=256 langs=js-ts)'

# SWE-Bench Hard data
# :prbot_msft.presets.swebench_hard:swebenchhard_12878_repair_msft_hq_vsc_testval
# :prbot_msft.presets.swebench_hard:o3_hpe_cotograder_bus

eval_settings.evals.0.dataset.override_target_samples_per_instance=1

metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

policy.n_ctx=262144
defaults.n_ctx=262144
defaults.sampler.max_num_yields=256
defaults.sampler.harmony_constrained_sampling=True

# msft special
load.restore_from_all_clusters=False

# defaults.channel_config.channels="analysis,final"
peashooter.sampling_use_ev3=True
seed=47

...dataset_container=orngscuscresco

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=${WANDB_PROJECT_NAME}
kafka_enable=False
enable_slackbot=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log
