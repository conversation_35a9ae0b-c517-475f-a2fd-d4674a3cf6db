import concurrent.futures
import json
import logging
import re
import shlex
import sys
import traceback
from typing import Any, Callable

import berry
import birder
import chat
import chz
import structlog
import termcolor
from berry.sampler import Sam<PERSON>
from deep_swe_msft.vsc_graders.cotograder_utils import write_to_local_file
from mini.metrics import capture_metrics, metrics
from peashooter.eval.checkpoint_finder import load_checkpoint_state
from peashooter.eval.eval_args import EvalArgs
from peashooter.eval.eval_runner import run_eval_on_current_cluster
from peashooter.eval.eval_settings import EvalSettings
from peashooter.eval.metrics_collector import MetricsCollector
from peashooter.experiment.slackbot import maybe_use_slackbot
from qstar import eval_utils
from qstar.common import dynamic_utils
from qstar.common.defaults import Defaults
from qstar.common.tools import renderer_worker
from qstar.curriculums.dataset_config import validate_dataset_against_sampler
from qstar.evals.eval import EvalSettings as QstarEvalSettings
from qstar.evals.eval import MetricAggregator
from qstar.presets import preset_utils
from qstar.qstar_policy_config import QstarDefaultWandbConfig
from qstar.samplers.sampler import BaseSampler as QstarBaseSampler
from research_security_profiles import get_security_profile
from rlsnow.db.utils import execute, snowflake_connector_creator

# Forbidden commands (strictly not allowed)
FORBIDDEN_COMMANDS = ["pytest", "python -m pytest", "run_tests.sh"]

# Discouraged commands (not recommended but less severe)
DISCOURAGED_COMMANDS = ["grep", "find", "ls", "sed", "cat", "bash", "oai", "mv", "cp", "rm"]


def detect_command_violations(content: str) -> tuple[str, str]:
    """Check if the content contains any forbidden or discouraged commands

    Returns:
        tuple[str, str]: (forbidden_command, discouraged_command) - one will be empty string
    """

    # Context-aware detection that distinguishes between JSON tool parameters and actual shell commands
    content_lower = content.lower()

    # Define command patterns for detection
    def get_command_patterns(cmd_lower: str) -> list[str]:
        return [
            rf"\b{re.escape(cmd_lower)}\s+[-\w]",  # Command with flags: "pytest -v"
            rf"\b{re.escape(cmd_lower)}\s+\w+\.\w+",  # Command with file: "pytest test.py"
            rf"\b{re.escape(cmd_lower)}$",  # Command at end of string
            rf"^{re.escape(cmd_lower)}\b",  # Command at start of string
            rf"&&\s*{re.escape(cmd_lower)}\b",  # After && operator: "cd /path && pytest"
            rf"&\s*{re.escape(cmd_lower)}\b",  # After & operator: "cmd & pytest"
            rf";\s*{re.escape(cmd_lower)}\b",  # After ; operator: "ls; pytest"
            rf"\|\s*{re.escape(cmd_lower)}\b",  # After | operator: "echo 'test' | pytest"
        ]

    # Check for forbidden commands first (higher priority)
    for forbidden_cmd in FORBIDDEN_COMMANDS:
        cmd_lower = forbidden_cmd.lower()
        command_patterns = get_command_patterns(cmd_lower)

        for pattern in command_patterns:
            if re.search(pattern, content_lower):
                return (cmd_lower, "")  # Return forbidden command, empty discouraged

    # Check for discouraged commands only if no forbidden command found
    for discouraged_cmd in DISCOURAGED_COMMANDS:
        cmd_lower = discouraged_cmd.lower()
        command_patterns = get_command_patterns(cmd_lower)

        for pattern in command_patterns:
            if re.search(pattern, content_lower):
                return ("", cmd_lower)  # Return empty forbidden, discouraged command

    return ("", "")  # No violations found


_LOG_PREFIX = "eval/"
ERROR_LOG_PATH = "/var/log/supervisor/haoran_swe_metric_error.log"


@chz.chz
class SWEMetricsCollector(MetricsCollector):
    def collect_metrics(
        self,
        step: int,
        checkpoint_path: str,
        evals: list[berry.Eval],
        samples: list[berry.VirtualSampleWithGrade],
        executor: concurrent.futures.Executor | None = None,
    ) -> birder.Metrics:
        total_messages = 0
        total_prompt_tokens = 0
        total_solution_tokens = 0
        total_tokens = 0
        total_forbidden_commands = 0
        total_discouraged_commands = 0
        # Fine-grained tracking for each discouraged command type
        discouraged_command_counts = {cmd.lower(): 0 for cmd in DISCOURAGED_COMMANDS}
        total_yields = 0
        total_passed = 0
        total_webbench_pass_at_1 = 0
        total_repeat_reads = 0
        total_run_tests = 0
        # New: per-sample indicator totals
        total_if_run_tests = 0
        total_if_repeat_reads = 0
        # manage_todo_list metrics
        total_manage_todo_list_calls = 0            # raw count of manage_todo_list tool calls (read + write)
        total_if_manage_todo_list = 0               # number of samples that used manage_todo_list at least once
        total_self_contained_manage_todo_list_samples = 0  # samples whose write calls were all self-contained & valid

        dataset_id = None
        for sample in samples:
            # Track file read ranges for this sample
            file_read_ranges = {}  # {filepath: [(start, end), ...]}
            # New: per-sample indicators
            has_repeat_read = False
            sample_if_run_tests = 0
            # manage_todo_list per-sample tracking
            manage_calls: list[dict] = []  # raw parsed JSON payloads
            has_manage_todo_list = False
            writes_all_self_contained = True  # assume true until a violation

            total_messages += sample.scalars["harmony_scalars"]["num_messages"]
            total_tokens += sample.scalars["count_tokens"]
            total_prompt_tokens += sample.scalars["count_prompt_tokens"]
            total_passed += 1 if sample.is_correct else 0
            total_webbench_pass_at_1 += sample.metadata["grader.0"].get('pass_at_1', 0.0)

            ttt = sample.text
            for message in ttt.split("<|im_end|>"):
                if message.startswith(
                    "<|im_start|>assistant<|meta_sep|>analysis to=functions.run_in_terminal <|meta_start|>"
                ):
                    # Parse run_in_terminal call and check for forbidden/discouraged commands
                    try:
                        json_start = message.find("{")
                        json_end = message.rfind("}") + 1
                        if json_start != -1 and json_end > json_start:
                            params = json.loads(message[json_start:json_end])
                            command = params.get("command", "")
                            if command:
                                forbidden_result, discouraged_result = detect_command_violations(command)
                                if forbidden_result:
                                    total_forbidden_commands += 1
                                elif discouraged_result:
                                    total_discouraged_commands += 1
                                    discouraged_command_counts[discouraged_result] += 1
                        else:
                            raise ValueError("run_in_terminal JSON payload not found")
                    except Exception as e:
                        write_to_local_file(
                            message=f"Exception in parsing run_in_terminal call: {e}\n{traceback.format_exc()}",
                            local_file_path=ERROR_LOG_PATH,
                            threshold=1.0,
                        )
                elif message.startswith(
                    "<|im_start|>assistant<|meta_sep|>analysis to=functions.manage_todo_list <|meta_start|>"
                ):
                    # Track manage_todo_list calls
                    total_manage_todo_list_calls += 1
                    has_manage_todo_list = True
                    try:
                        json_start = message.find("{")
                        json_end = message.rfind("}") + 1
                        if json_start != -1 and json_end > json_start:
                            payload_raw = message[json_start:json_end]
                            payload = json.loads(payload_raw)
                            if isinstance(payload, dict):
                                manage_calls.append(payload)
                                # For write operations, perform rule-based self-contained validation
                                if payload.get("operation") == "write":
                                    todo_list = payload.get("todoList")
                                    # Basic structural checks
                                    if not isinstance(todo_list, list) or len(todo_list) == 0:
                                        writes_all_self_contained = False
                                        continue
                                    required_fields = {"id", "title", "description", "status"}
                                    ids = []
                                    in_progress_count = 0
                                    for item in todo_list:
                                        if not isinstance(item, dict):
                                            writes_all_self_contained = False
                                            break
                                        if not required_fields.issubset(item.keys()):
                                            writes_all_self_contained = False
                                            break
                                        # Sequential IDs check later
                                        try:
                                            ids.append(int(item["id"]))
                                        except Exception:
                                            writes_all_self_contained = False
                                            break
                                        status = item.get("status")
                                        if status not in {"not-started", "in-progress", "completed"}:
                                            writes_all_self_contained = False
                                            break
                                        if status == "in-progress":
                                            in_progress_count += 1
                                        # Title word count rule-of-thumb 3-7 words (spec requirement 3-7 words)
                                        title_words = str(item.get("title", "")).strip().split()
                                        # Description non-empty enforcement (must carry context)
                                        if not str(item.get("description", "")).strip():
                                            writes_all_self_contained = False
                                            break
                                    if not writes_all_self_contained:
                                        continue
                                    # Single in-progress item constraint
                                    if in_progress_count > 1:
                                        writes_all_self_contained = False
                                        continue
                                    # IDs strictly sequential starting at 1
                                    if ids != list(range(1, len(ids) + 1)):
                                        writes_all_self_contained = False
                        else:
                            # Could not parse JSON; treat as non self-contained
                            writes_all_self_contained = False
                    except Exception as e:
                        writes_all_self_contained = False
                        write_to_local_file(
                            message=f"Exception in manage_todo_list call parse: {e}\n{traceback.format_exc()}",
                            local_file_path=ERROR_LOG_PATH,
                            threshold=1.0,
                        )
                elif message.startswith(
                    "<|im_start|>assistant<|meta_sep|>analysis to=functions.runTests <|meta_start|>"
                ):
                    total_run_tests += 1
                    # New: mark that this sample executed tests at least once
                    sample_if_run_tests = 1
                elif (
                    "<|im_start|>assistant<|meta_sep|>analysis to=functions.read_file <|meta_start|>"
                    in message
                ):
                    # Parse read_file call
                    try:
                        # FIX: message is a raw string; extract JSON payload by slicing braces
                        json_start = message.find("{")
                        json_end = message.rfind("}") + 1
                        if json_start != -1 and json_end > json_start:
                            params = json.loads(message[json_start:json_end])
                            filepath = params.get("filePath")
                            # Coerce to int and provide defaults
                            offset = int(params.get("offset", 1) or 1)
                            limit = int(params.get("limit", 2000) or 2000)

                            if filepath:
                                start_line = offset
                                end_line = offset + limit - 1

                                if filepath not in file_read_ranges:
                                    file_read_ranges[filepath] = []

                                file_read_ranges[filepath].append((start_line, end_line))
                        else:
                            raise ValueError("read_file JSON payload not found")
                    except Exception as e:
                        write_to_local_file(
                            message=f"Exception in read_file call: {e}\n{traceback.format_exc()}",
                            local_file_path=ERROR_LOG_PATH,
                            threshold=1.0,
                        )

            # Check for overlaps after collecting all ranges for this sample
            for filepath, ranges in file_read_ranges.items():
                if len(ranges) <= 1:
                    continue

                # Sort ranges by start position
                sorted_ranges = sorted(ranges, key=lambda x: x[0])

                # Check each range against all previous ranges
                for i in range(1, len(sorted_ranges)):
                    current_start, current_end = sorted_ranges[i]

                    # Check if current range overlaps with any previous range
                    overlaps = False
                    for j in range(i):
                        prev_start, prev_end = sorted_ranges[j]
                        # Check if current range overlaps with previous range
                        if current_start <= prev_end:
                            overlaps = True
                            break

                    if overlaps:
                        total_repeat_reads += 1
                        has_repeat_read = True

            ttt = sample.text
            # swang, it can also calculate by get yield from the first and last
            # <|im_start|>assistant budget=187095 yield_budget=1<|meta_sep|>analysis<|im_sep|>, then minus
            if sample.scalars["harmony_scalars"]["message_token_counts"] is not None:
                for mtc in sample.scalars["harmony_scalars"]["message_token_counts"]:
                    if (
                        mtc.role == chat.Role.ASSISTANT
                        and mtc.channel == "analysis"
                        and mtc.recipient.startswith("functions.")
                    ):
                        total_yields += 1
            else:
                # process invalid cases
                for message in ttt.split("<|im_end|>"):
                    if message.startswith(
                        "<|im_start|>assistant<|meta_sep|>analysis to=functions."
                    ):
                        total_yields += 1

            # New: accumulate per-sample indicators
            if has_repeat_read:
                total_if_repeat_reads += 1
            total_if_run_tests += sample_if_run_tests
            if has_manage_todo_list:
                total_if_manage_todo_list += 1
                if writes_all_self_contained:
                    total_self_contained_manage_todo_list_samples += 1

            dataset_id = sample.gt_datapoint.dataset_config.dataset_id
        total_solution_tokens = total_tokens - total_prompt_tokens

        avg_messages = total_messages / len(samples) if samples and len(samples) > 0 else 0
        avg_yields = total_yields / len(samples) if samples and len(samples) > 0 else 0
        avg_solution_tokens = (
            total_solution_tokens / len(samples) if samples and len(samples) > 0 else 0
        )
        avg_passed = total_passed / len(samples) if samples and len(samples) > 0 else 0
        avg_webbench_pass_at_1 = total_webbench_pass_at_1 / len(samples) if samples and len(samples) > 0 else 0
        avg_forbidden_commands = (
            total_forbidden_commands / len(samples) if samples and len(samples) > 0 else 0
        )
        avg_discouraged_commands = (
            total_discouraged_commands / len(samples) if samples and len(samples) > 0 else 0
        )
        avg_repeat_reads = total_repeat_reads / len(samples) if samples and len(samples) > 0 else 0
        avg_run_tests = total_run_tests / len(samples) if samples and len(samples) > 0 else 0
        # New: averages for per-sample indicators (between 0 and 1)
        avg_if_run_tests = total_if_run_tests / len(samples) if samples and len(samples) > 0 else 0
        avg_if_repeat_reads = (
            total_if_repeat_reads / len(samples) if samples and len(samples) > 0 else 0
        )
        # manage_todo_list aggregated metrics
        avg_manage_todo_list_calls = (
            total_manage_todo_list_calls / len(samples) if samples and len(samples) > 0 else 0
        )
        avg_if_manage_todo_list = (
            total_if_manage_todo_list / len(samples) if samples and len(samples) > 0 else 0
        )  # ratio of samples that used manage_todo_list
        avg_self_contained_manage_todo_list = (
            total_self_contained_manage_todo_list_samples / total_if_manage_todo_list
            if total_if_manage_todo_list > 0
            else 0
        )

        with capture_metrics(step=step):
            metrics.scalar("policy/step", step)
            metrics.scalar(f"user_define/{dataset_id}/avg_messages", avg_messages)
            metrics.scalar(f"user_define/{dataset_id}/avg_yields", avg_yields)
            metrics.scalar(f"user_define/{dataset_id}/avg_solution_tokens", avg_solution_tokens)
            metrics.scalar(f"user_define/{dataset_id}/avg_passed", avg_passed)
            metrics.scalar(f"user_define/{dataset_id}/avg_webbench_pass_at_1", avg_webbench_pass_at_1)
            metrics.scalar(
                f"user_define/{dataset_id}/avg_forbidden_commands", avg_forbidden_commands
            )
            metrics.scalar(
                f"user_define/{dataset_id}/avg_discouraged_commands", avg_discouraged_commands
            )
            metrics.scalar(f"user_define/{dataset_id}/avg_repeat_reads", avg_repeat_reads)
            metrics.scalar(f"user_define/{dataset_id}/avg_run_tests", avg_run_tests)
            # New: log averages for if_* metrics
            metrics.scalar(f"user_define/{dataset_id}/avg_if_run_tests", avg_if_run_tests)
            metrics.scalar(f"user_define/{dataset_id}/avg_if_repeat_reads", avg_if_repeat_reads)
            metrics.scalar(f"user_define/{dataset_id}/total_messages", total_messages)
            metrics.scalar(f"user_define/{dataset_id}/total_solution_tokens", total_solution_tokens)
            metrics.scalar(f"user_define/{dataset_id}/total_passed", total_passed)
            metrics.scalar(f"user_define/{dataset_id}/total_webbench_pass_at_1", total_webbench_pass_at_1)
            metrics.scalar(
                f"user_define/{dataset_id}/total_forbidden_commands", total_forbidden_commands
            )
            metrics.scalar(
                f"user_define/{dataset_id}/total_discouraged_commands", total_discouraged_commands
            )
            metrics.scalar(f"user_define/{dataset_id}/total_tokens", total_tokens)
            metrics.scalar(f"user_define/{dataset_id}/total_prompt_tokens", total_prompt_tokens)
            metrics.scalar(f"user_define/{dataset_id}/total_repeat_reads", total_repeat_reads)
            metrics.scalar(f"user_define/{dataset_id}/total_run_tests", total_run_tests)
            # New: log totals for if_* metrics
            metrics.scalar(f"user_define/{dataset_id}/total_if_run_tests", total_if_run_tests)
            metrics.scalar(f"user_define/{dataset_id}/total_if_repeat_reads", total_if_repeat_reads)
            # manage_todo_list metrics
            metrics.scalar(
                f"user_define/{dataset_id}/total_manage_todo_list_calls", total_manage_todo_list_calls
            )
            metrics.scalar(
                f"user_define/{dataset_id}/total_if_manage_todo_list", total_if_manage_todo_list
            )  # samples with at least one call
            metrics.scalar(
                f"user_define/{dataset_id}/total_self_contained_manage_todo_list_samples",
                total_self_contained_manage_todo_list_samples,
            )
            metrics.scalar(
                f"user_define/{dataset_id}/avg_manage_todo_list_calls", avg_manage_todo_list_calls
            )
            metrics.scalar(
                f"user_define/{dataset_id}/avg_if_manage_todo_list", avg_if_manage_todo_list
            )  # ratio of samples invoking manage_todo_list
            metrics.scalar(
                f"user_define/{dataset_id}/avg_self_contained_manage_todo_list",
                avg_self_contained_manage_todo_list,
            )  # ratio of samples whose manage_todo_list usage was fully self-contained

            # Fine-grained discouraged command metrics
            for cmd_type, count in discouraged_command_counts.items():
                avg_count = count / len(samples) if samples and len(samples) > 0 else 0
                metrics.scalar(
                    f"user_define/{dataset_id}/discouraged_commands/{cmd_type}/total", count
                )
                metrics.scalar(
                    f"user_define/{dataset_id}/discouraged_commands/{cmd_type}/avg", avg_count
                )

            aggregator = MetricAggregator(evals)
            eval_utils.collect_eval_metrics(
                samples, evals, aggregator, dict(), _LOG_PREFIX, executor
            )

            birder_metrics = birder.Metrics()
            birder_metrics.load_state(metrics.pop_metrics_state())

            return birder_metrics
