"""
P0 Tests Metrics Collector

This module implements a comprehensive metrics collector for P0 tests, designed to provide
detailed logging and analytics for P0 test evaluation processes. The collector logs
extensively to help understand the evaluation metrics and patterns.

The logs are written to /var/log/supervisor/p0_metrics_collector.log for monitoring
and analysis purposes.
"""
import concurrent.futures
import json
import os
import re
from datetime import datetime
import subprocess

import structlog
import berry
import birder
import chz
import chat

from mini.metrics import capture_metrics, metrics
from peashooter.eval.metrics_collector import MetricsCollector
from qstar import eval_utils
from qstar.evals.eval import MetricAggregator
from orange_tap2_msft.generate_test_report import generate_report_from_structured_data

_LOG_PREFIX = "eval/"

logger = structlog.stdlib.get_logger(component=__name__, _print=True)
PO_METRICS_COLLECTOR_LOG_PATH = "/var/log/supervisor/p0_metrics_collector.log"
HTML_OUTPUT_PATH_PREFIX = "/var/log/supervisor/p0_test_reports"
BLOB_STORAGE_REPORTS_PATH = "az://orngscuscresco/data/padawan_p0_test_reports"


@chz.chz
class P0MetricsCollector(MetricsCollector):
    def collect_metrics(
        self,
        step: int,
        checkpoint_path: str,
        evals: list[berry.Eval],
        samples: list[berry.VirtualSampleWithGrade],
        executor: concurrent.futures.Executor | None = None,
    ) -> birder.Metrics:
        total_messages = 0
        total_prompt_tokens = 0
        total_solution_tokens = 0
        total_tokens = 0
        total_bash_commands = 0
        total_padawan_commands = 0
        total_yields = 0
        total_passed = 0
        
        dataset_id = "default"
        for sample in samples:
            
            total_messages += sample.scalars['harmony_scalars']['num_messages']
            total_tokens += sample.scalars["count_tokens"]
            total_prompt_tokens += sample.scalars["count_prompt_tokens"]
            total_passed += 1 if sample.is_correct else 0
            ttt = sample.text
            for message in ttt.split("<|im_end|>"):
                if message.startswith("<|im_start|>assistant<|meta_sep|>analysis to=functions.str_replace_editor code<|im_sep|>"):
                    total_padawan_commands += 1
                elif message.startswith("<|im_start|>assistant<|meta_sep|>analysis to=functions.bash code<|im_sep|>"):
                    total_bash_commands += 1
            # swang, it can also calculate by get yield from the first and last
            # <|im_start|>assistant budget=187095 yield_budget=1<|meta_sep|>analysis<|im_sep|>, then minus
            if sample.scalars["harmony_scalars"]["message_token_counts"] is not None:
                for mtc in sample.scalars["harmony_scalars"]["message_token_counts"]:
                    if mtc.role == chat.Role.ASSISTANT and mtc.channel == "analysis" and mtc.recipient.startswith("functions."):
                        total_yields += 1
            else:
                # process invalid cases
                for message in ttt.split("<|im_end|>"):
                    if message.startswith("<|im_start|>assistant<|meta_sep|>analysis to=functions."):
                        total_yields += 1

            dataset_id = sample.gt_datapoint.dataset_config.dataset_id
            if hasattr(sample, 'metadata') and sample.metadata and isinstance(sample.metadata, dict) and 'grader.0' in sample.metadata:
                # Use synchronous logging since we're in a sync context
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                with open(PO_METRICS_COLLECTOR_LOG_PATH, "a") as f:
                    f.write(f"[{timestamp}] [{dataset_id}] Grader: {sample.metadata['grader.0']}\n")
                
                # Generate test report if structured grader result is available
                try:
                    grader_output_data = sample.metadata.get('grader.0')
                    if grader_output_data:
                        sanitized_checkpoint = re.sub(r'[^A-Za-z0-9._-]+', '_', checkpoint_path.rstrip('/'))
                        id_str = f"{sanitized_checkpoint}_step_{step}"
                        json_output_path = f"{HTML_OUTPUT_PATH_PREFIX}/grader_output_json/{id_str}.json"
                        output_path = f"{HTML_OUTPUT_PATH_PREFIX}/{id_str}.html"
                        # Ensure directory exists
                        os.makedirs(os.path.dirname(json_output_path), exist_ok=True)
                        if os.path.exists(json_output_path):
                            with open(json_output_path, 'r') as jf:
                                saved = json.load(jf)
                            # If saved JSON matches structured result format, merge values
                            if isinstance(saved, dict) and 'totalChecks' in saved:
                                sr = saved
                                # Compute combined totals
                                existing_total_checks = sr.get('totalChecks', 0)
                                incoming_total_checks = grader_output_data.get('totalChecks', 0)
                                sr['totalChecks'] = existing_total_checks + incoming_total_checks

                                existing_passed_checks = sr.get('passedChecks', 0)
                                incoming_passed_checks = grader_output_data.get('passedChecks', 0)
                                sr['passedChecks'] = existing_passed_checks + incoming_passed_checks

                                sr['failedChecks'] = sr['totalChecks'] - sr['passedChecks']
                                sr['overallPassRate'] = round((sr['passedChecks'] / sr['totalChecks']), 1) if sr['totalChecks'] > 0 else 0

                                # Merge test case lists
                                existing_cases = sr.get('testCases', []) or []
                                incoming_cases = grader_output_data.get('testCases', []) or []
                                sr['testCases'] = existing_cases + incoming_cases

                                # Save merged structured result
                                with open(json_output_path, 'w') as jf:
                                    json.dump(sr, jf, indent=2)
                                structured_data = sr
                            else:
                                # Overwrite with new grader data
                                structured_data = grader_output_data
                                with open(json_output_path, 'w') as jf:
                                    json.dump(structured_data, jf, indent=2)
                        else:
                            # No existing JSON, write new grader data
                            structured_data = grader_output_data
                            with open(json_output_path, 'w') as jf:
                                json.dump(structured_data, jf, indent=2)

                        # Generate report using the structured data
                        report_path = generate_report_from_structured_data(structured_data, output_path)
                        with open(PO_METRICS_COLLECTOR_LOG_PATH, "a") as f:
                            f.write(f"Test report generated: {report_path}\n")
                        
                        # Push the report to blob storage
                        # Push the report to blob storage
                        try:
                            blob_destination = f"{BLOB_STORAGE_REPORTS_PATH}/{id_str}.html"
                            upload_cmd = ["bbb", "cp", report_path, blob_destination]
                            result = subprocess.run(upload_cmd, capture_output=True, text=True)
                            if result.returncode == 0:
                                with open(PO_METRICS_COLLECTOR_LOG_PATH, "a") as f:
                                    f.write(f"Test report uploaded to blob storage: {blob_destination}\n")
                            else:
                                with open(PO_METRICS_COLLECTOR_LOG_PATH, "a") as f:
                                    f.write(f"Failed to upload test report to blob storage. Command: {' '.join(upload_cmd)}\n")
                                    f.write(f"Error: {result.stderr}\n")
                        except Exception as upload_e:
                            with open(PO_METRICS_COLLECTOR_LOG_PATH, "a") as f:
                                f.write(f"Exception during blob storage upload: {str(upload_e)}\n")
                except Exception as e:
                    with open(PO_METRICS_COLLECTOR_LOG_PATH, "a") as f:
                            f.write(f"Failed to generate test report: {str(e)}\n")
        total_solution_tokens = total_tokens - total_prompt_tokens
        
        avg_messages = total_messages / len(samples) if samples and len(samples) > 0 else 0
        avg_yields = total_yields / len(samples) if samples and len(samples) > 0 else 0
        avg_solution_tokens = total_solution_tokens / len(samples) if samples and len(samples) > 0 else 0
        avg_passed = total_passed / len(samples) if samples and len(samples) > 0 else 0
        avg_bash_commands = total_bash_commands / len(samples) if samples and len(samples) > 0 else 0
        avg_padawan_commands = total_padawan_commands / len(samples) if samples and len(samples) > 0 else 0
        

        with capture_metrics(step=step):
            metrics.scalar("policy/step", step)
            metrics.scalar(f"user_define/{dataset_id}/avg_messages", avg_messages)
            metrics.scalar(f"user_define/{dataset_id}/avg_yields", avg_yields)
            metrics.scalar(f"user_define/{dataset_id}/avg_solution_tokens", avg_solution_tokens)
            metrics.scalar(f"user_define/{dataset_id}/avg_passed", avg_passed)
            metrics.scalar(f"user_define/{dataset_id}/avg_bash_commands", avg_bash_commands)
            metrics.scalar(f"user_define/{dataset_id}/avg_padawan_commands", avg_padawan_commands)
            metrics.scalar(f"user_define/{dataset_id}/total_messages", total_messages)
            metrics.scalar(f"user_define/{dataset_id}/total_solution_tokens", total_solution_tokens)
            metrics.scalar(f"user_define/{dataset_id}/total_passed", total_passed)
            metrics.scalar(f"user_define/{dataset_id}/total_bash_commands", total_bash_commands)
            metrics.scalar(f"user_define/{dataset_id}/total_padawan_commands", total_padawan_commands)
            metrics.scalar(f"user_define/{dataset_id}/total_tokens", total_tokens)
            metrics.scalar(f"user_define/{dataset_id}/total_prompt_tokens", total_prompt_tokens)

            aggregator = MetricAggregator(evals)
            eval_utils.collect_eval_metrics(
                samples, evals, aggregator, dict(), _LOG_PREFIX, executor
            )
            birder_metrics = birder.Metrics()
            birder_metrics.load_state(metrics.pop_metrics_state())

            return birder_metrics

