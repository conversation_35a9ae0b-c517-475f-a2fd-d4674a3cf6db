import concurrent.futures
import logging
import shlex
import sys
from typing import Any, Callable

import structlog
import termcolor

import berry
import birder
import chz
import chat

from berry.sampler import Sampler
from mini.metrics import capture_metrics, metrics
from peashooter.eval.checkpoint_finder import load_checkpoint_state
from peashooter.eval.eval_args import EvalArgs
from peashooter.eval.eval_runner import run_eval_on_current_cluster
from peashooter.eval.eval_settings import EvalSettings
from peashooter.eval.metrics_collector import MetricsCollector
from peashooter.experiment.slackbot import maybe_use_slackbot
from qstar import eval_utils
from qstar.common import dynamic_utils
from qstar.common.defaults import Defaults
from qstar.common.tools import renderer_worker
from qstar.curriculums.dataset_config import validate_dataset_against_sampler
from qstar.evals.eval import EvalSettings as QstarEvalSettings
from qstar.evals.eval import MetricAggregator
from qstar.presets import preset_utils
from qstar.samplers.sampler import BaseSampler as QstarBaseSampler
from qstar.qstar_policy_config import QstarDefaultWandbConfig
from research_security_profiles import get_security_profile
from rlsnow.db.utils import execute, snowflake_connector_creator

_LOG_PREFIX = "eval/"

logger = structlog.stdlib.get_logger(component=__name__, _print=True)


@chz.chz
class SWEMetricsCollector(MetricsCollector):
    def collect_metrics(
        self,
        step: int,
        checkpoint_path: str,
        evals: list[berry.Eval],
        samples: list[berry.VirtualSampleWithGrade],
        executor: concurrent.futures.Executor | None = None,
    ) -> birder.Metrics:
        total_messages = 0
        total_prompt_tokens = 0
        total_solution_tokens = 0
        total_tokens = 0
        total_bash_commands = 0
        total_padawan_commands = 0
        total_yields = 0
        total_passed = 0
        total_pass_at_1 = 0
        
        dataset_id = None
        for sample in samples:
            
            with open("/var/log/supervisor/keli_web_bench_metrics.log", "a") as f:
                f.write(f"sample: {sample}\n")

            total_messages += sample.scalars['harmony_scalars']['num_messages']
            total_tokens += sample.scalars["count_tokens"]
            total_prompt_tokens += sample.scalars["count_prompt_tokens"]
            total_passed += 1 if sample.is_correct else 0
            total_pass_at_1 += sample.metadata["grader.0"].get('pass_at_1', 0.0)
            
            ttt = sample.text
            for message in ttt.split("<|im_end|>"):
                if message.startswith("<|im_start|>assistant<|meta_sep|>analysis to=functions.str_replace_editor code<|im_sep|>"):
                    total_padawan_commands += 1
                elif message.startswith("<|im_start|>assistant<|meta_sep|>analysis to=functions.bash code<|im_sep|>"):
                    total_bash_commands += 1
            # swang, it can also calculate by get yield from the first and last
            # <|im_start|>assistant budget=187095 yield_budget=1<|meta_sep|>analysis<|im_sep|>, then minus
            if sample.scalars["harmony_scalars"]["message_token_counts"] is not None:
                for mtc in sample.scalars["harmony_scalars"]["message_token_counts"]:
                    if mtc.role == chat.Role.ASSISTANT and mtc.channel == "analysis" and mtc.recipient.startswith("functions."):
                        total_yields += 1
            else:
                # process invalid cases
                for message in ttt.split("<|im_end|>"):
                    if message.startswith("<|im_start|>assistant<|meta_sep|>analysis to=functions."):
                        total_yields += 1

            dataset_id = sample.gt_datapoint.dataset_config.dataset_id
        total_solution_tokens = total_tokens - total_prompt_tokens
        
        avg_messages = total_messages / len(samples) if samples and len(samples) > 0 else 0
        avg_yields = total_yields / len(samples) if samples and len(samples) > 0 else 0
        avg_solution_tokens = total_solution_tokens / len(samples) if samples and len(samples) > 0 else 0
        avg_passed = total_passed / len(samples) if samples and len(samples) > 0 else 0
        avg_bash_commands = total_bash_commands / len(samples) if samples and len(samples) > 0 else 0
        avg_padawan_commands = total_padawan_commands / len(samples) if samples and len(samples) > 0 else 0
        avg_passed = total_passed / len(samples) if samples and len(samples) > 0 else 0
        avg_pass_at_1 = total_pass_at_1 / len(samples) if samples and len(samples) > 0 else 0

        with capture_metrics(step=step):
            metrics.scalar("policy/step", step)
            metrics.scalar(f"user_define/{dataset_id}/avg_messages", avg_messages)
            metrics.scalar(f"user_define/{dataset_id}/avg_yields", avg_yields)
            metrics.scalar(f"user_define/{dataset_id}/avg_solution_tokens", avg_solution_tokens)
            metrics.scalar(f"user_define/{dataset_id}/avg_passed", avg_passed)
            metrics.scalar(f"user_define/{dataset_id}/avg_bash_commands", avg_bash_commands)
            metrics.scalar(f"user_define/{dataset_id}/avg_padawan_commands", avg_padawan_commands)
            metrics.scalar(f"user_define/{dataset_id}/total_messages", total_messages)
            metrics.scalar(f"user_define/{dataset_id}/total_solution_tokens", total_solution_tokens)
            metrics.scalar(f"user_define/{dataset_id}/total_passed", total_passed)
            metrics.scalar(f"user_define/{dataset_id}/total_bash_commands", total_bash_commands)
            metrics.scalar(f"user_define/{dataset_id}/total_padawan_commands", total_padawan_commands)
            metrics.scalar(f"user_define/{dataset_id}/total_tokens", total_tokens)
            metrics.scalar(f"user_define/{dataset_id}/total_prompt_tokens", total_prompt_tokens)
            metrics.scalar(f"user_define/{dataset_id}/avg_pass_at_1", avg_pass_at_1)

            aggregator = MetricAggregator(evals)
            eval_utils.collect_eval_metrics(
                samples, evals, aggregator, dict(), _LOG_PREFIX, executor
            )

            # checkpoint_state = load_checkpoint_state(checkpoint_path)
            # if state_driver := checkpoint_state.state_driver:
            #     if train_worker_state := state_driver.get("train_worker_state"):
            #         metrics_to_log = {}
            #         for key, prefix in [
            #             ("all_time_metrics", "cumulative/"),
            #             ("curriculum_metrics", "curriculum/"),
            #         ]:
            #             if metrics_dict := train_worker_state.get(key):
            #                 for k, v in metrics_dict.items():
            #                     if isinstance(v, (int, float)):
            #                         metrics_to_log[f"{prefix}{k}"] = v
            #         if metrics_to_log:
            #             logger.info(
            #                 "Logging train metrics",
            #                 step=step,
            #                 metric_names=list(metrics_to_log.keys()),
            #             )
            #             for k, v in metrics_to_log.items():
            #                 metrics.scalar(k, v)

            birder_metrics = birder.Metrics()
            birder_metrics.load_state(metrics.pop_metrics_state())

            return birder_metrics

