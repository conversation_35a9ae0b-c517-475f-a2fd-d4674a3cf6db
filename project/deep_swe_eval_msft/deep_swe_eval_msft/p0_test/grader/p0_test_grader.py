import structlog
import berry
from typing import Dict, Any, List, Callable
from dataclasses import dataclass
from uuid import uuid4
from caas.terminal.api import TerminalSession
from deep_swe_eval_msft.p0_test.preparedness.p0_metadata import P0Metadata
from deep_swe_eval_msft.swe_bench.peaval.computer_task_grader_utils import (
    get_model_patch,
    PROTECTED_FILES
)
from .rb_grade_utils import (
    log_to_file,
    check_npm_install_usage,
    check_build_before_str_replace,
    check_build_after_str_replace,
    check_npm_test_usage,
    check_go_test_execution_wrapper,
    validate_conversation_and_patch,
    log_grading_summary,
    check_specific_files_modified_wrapper,
    check_only_specific_file_modified_wrapper,
    check_content_wrapper,
    check_build_usage_count_wrapper,
    check_create_command_not_used_wrapper,
    check_early_file_editing_wrapper,
    check_file_exists_wrapper,
    check_file_content_contains_wrapper,
    check_file_content_excludes_wrapper,
    check_project_buildable_wrapper,
    check_specific_files_modified_via_str_replace,
    check_partial_results_events_count,
    check_partial_results_uses_markdown_task_list,
    check_first_partial_result_has_incomplete_tasks,
    check_last_partial_result_no_incomplete_tasks,
    check_last_partial_result_has_completed_tasks,
    check_pr_has_title,
    check_pr_description_has_body,
    # New web dev E2E checking functions
    check_modern_scaffolding_usage,
    check_scaffolding_not_abandoned,
    check_dev_server_started,
    check_playwright_screenshot_taken,
    check_playwright_navigation,
    check_playwright_password_entry,
    check_playwright_login_click,
    check_comment_reply_with_screenshot,
    check_comment_reply_without_screenshot,
)

logger = structlog.get_logger(__name__)

@dataclass
class CheckConfig:
    """Configuration for a single check function."""
    name: str
    func: Callable
    is_async: bool = False
    kwargs: Dict[str, Any] | None = None

@dataclass
class GradeConfig:
    """Configuration for a grading function."""
    name: str
    testcase_name: str
    checks: List[CheckConfig]
    uses_patch: bool = True

async def generic_grade_function(
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    config: GradeConfig,
) -> bool:
    """Generic grading function that executes all checks and ANDs them together."""

    # Get instance_id for logging first
    instance_id = sample.gt_datapoint.unique_id or str(uuid4())
    
    await log_to_file(f"Starting {config.name}", instance_id)
    
    try:

        metadata = P0Metadata.model_validate(sample.gt_datapoint.metadata)
        # Get model patch if needed
        model_patch = None
        if config.uses_patch:
            # Extract metadata directly from the dictionary
            metadata_dict = sample.gt_datapoint.metadata
            commit = metadata_dict.get('commit', 'HEAD')  # Default to HEAD if no commit specified
            repo_root = f"/tmp/{instance_id}"  # Repo root with instance_id

            await log_to_file(f"Using repo_root: {repo_root}, commit: {commit}", instance_id)

            model_patch = await get_model_patch(
                terminal_session=terminal_session,
                base_commit=commit,
                repo_root=repo_root,
                protected_files=PROTECTED_FILES,
            )
            
            # Log patch information for validation
            await log_to_file(f"Model patch retrieved - length: {len(model_patch.raw_patch)} characters", instance_id)
            
            await log_to_file(f"Raw Patch: {model_patch.raw_patch}", instance_id)

        if not sample.conversation:
            await log_to_file("ERROR: No sample.conversation, cannot grade", instance_id)
            return False
        
        # Validate conversation and patch if we have a patch
        if model_patch:
            validation_success = await validate_conversation_and_patch(sample.conversation, model_patch, instance_id)
            if not validation_success:
                await log_to_file("WARNING: Validation failed, continuing with grading", instance_id)
        
        # Execute all checks and collect results
        check_results = {}
        for check_config in config.checks:
            try:
                # Prepare arguments for the check function
                args = [sample.conversation]
                kwargs = check_config.kwargs or {}
                
                # Add model_patch if the function expects it
                if 'model_patch' in check_config.func.__code__.co_varnames:
                    kwargs['model_patch'] = model_patch
                
                # Add instance_id if the function expects it
                if 'instance_id' in check_config.func.__code__.co_varnames:
                    kwargs['instance_id'] = instance_id
                
                # Add terminal_session if the function expects it
                if 'terminal_session' in check_config.func.__code__.co_varnames:
                    kwargs['terminal_session'] = terminal_session
                
                # Call the function
                if check_config.is_async:
                    result = await check_config.func(*args, **kwargs)
                else:
                    result = check_config.func(*args, **kwargs)
                    
                check_results[check_config.name] = result
                await log_to_file(f"{check_config.name}: {result}", instance_id)
            except Exception as e:
                await log_to_file(f"ERROR in check {check_config.name}: {str(e)}", instance_id)
                check_results[check_config.name] = False
        
        # Calculate individual scores (0 or 1) and response parts
        individual_scores = {}
        checks_array = []
        passed_checks = 0
        total_checks = len(check_results)
        
        for check_name, result in check_results.items():
            score_value = 1 if result else 0
            individual_scores[check_name] = score_value
            
            # Add to checks array in the desired format
            checks_array.append({
                "name": check_name,
                "status": "pass" if result else "fail",
                "message": None
            })
            
            if result:
                passed_checks += 1
        
        # Calculate overall metrics
        pass_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0
        overall_status = "pass" if all(check_results.values()) else "fail"
        
        # Build the structured test result
        test_case_result = {
            "name": config.testcase_name,
            "status": overall_status,
            "passRate": round(pass_rate, 1),
            "totalChecks": total_checks,
            "passedChecks": passed_checks,
            "failedChecks": total_checks - passed_checks,
            "checks": checks_array,
            "rolloutlogs": str(sample.conversation) if sample.conversation else ""
        }
        
        # Build the complete test suite result
        structured_result = {
            "name": "Padawan P0 Tests Report",
            "duration": sample.metadata.get('sampling/duration', 0) * 1000,  # Convert to ms
            "overallPassRate": round(pass_rate, 1),
            "totalChecks": total_checks,
            "passedChecks": passed_checks,
            "failedChecks": total_checks - passed_checks,
            "testCases": [test_case_result]
        }
        
        # Store both formats in metadata
        sample.metadata["grader"] = {
            "prompt": f"{config.name} grader functions: " + ", ".join(
                check_config.name for check_config in config.checks
            ),
            "response": "; ".join(f"{name}: {result}" for name, result in check_results.items()),
            "score": passed_checks / total_checks if total_checks > 0 else 0.0,
            **individual_scores  # Original individual scores
        }
        
        # Add the structured result
        sample.metadata["grader"] = structured_result
        
        # Log both formats
        await log_to_file(f"Individual scores: {individual_scores}", instance_id)
        await log_to_file(f"Structured result: {structured_result}", instance_id)
        
        result = all(check_results.values())
        await log_grading_summary(instance_id, config.name, individual_scores, result)
        
        return result
        
    except Exception as e:
        await log_to_file(f"ERROR in {config.name}: {str(e)}", instance_id)
        import traceback
        await log_to_file(f"Traceback: {traceback.format_exc()}", instance_id)
        return False

# Define configurations for each grading function
GRADE_CONFIGS = {
    "str_replace_patch_not_create": GradeConfig(
        name="grade_fn_str_replace_patch_not_create",
        testcase_name="str_replace_patch_not_create",
        uses_patch=True,
        checks=[
            CheckConfig("dependency_install_used", check_npm_install_usage),
            CheckConfig("build_multiple_times", check_build_usage_count_wrapper, kwargs={"count_threshold": 1}),
            CheckConfig("build_before_replace", check_build_before_str_replace),
            CheckConfig("build_after_replace", check_build_after_str_replace),
            CheckConfig("home_tsx_modified", check_specific_files_modified_via_str_replace, is_async=True, 
                       kwargs={"files": ['app/routes/home.tsx']}),
            CheckConfig("create_command_not_used", check_create_command_not_used_wrapper),
            CheckConfig("home_tsx_file_exists", check_file_exists_wrapper, is_async=True,
                       kwargs={"file_path": 'app/routes/home.tsx'}),
            CheckConfig("home_tsx_title_updated", check_file_content_contains_wrapper, is_async=True,
                       kwargs={"file_path": 'app/routes/home.tsx', "patterns": ['Copilot for Developers']}),
            CheckConfig("home_tsx_description_updated", check_file_content_contains_wrapper, is_async=True,
                       kwargs={"file_path": 'app/routes/home.tsx', "patterns": ['Copilot for Developers is a new way to build and ship software faster.']}),
            CheckConfig("home_tsx_old_content_removed", check_file_content_excludes_wrapper, is_async=True,
                       kwargs={"file_path": 'app/routes/home.tsx', "patterns": ['New React Router App', 'Welcome to React Router']}),
            CheckConfig("project_still_buildable", check_project_buildable_wrapper, is_async=True),
            # partial results checks
            CheckConfig("partial_results_events_count", check_partial_results_events_count, is_async=True),
            CheckConfig("partial_results_uses_markdown_task_list", check_partial_results_uses_markdown_task_list, is_async=True),
            CheckConfig("first_partial_result_has_incomplete_tasks", check_first_partial_result_has_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_no_incomplete_tasks", check_last_partial_result_no_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_has_completed_tasks", check_last_partial_result_has_completed_tasks, is_async=True),
            # result checks
            CheckConfig("pr_has_title", check_pr_has_title, is_async=True,
                       kwargs={"issue_number": 0}),
            CheckConfig("pr_description_has_body", check_pr_description_has_body, is_async=True,
                       kwargs={"issue_number": 0}),
        ],
    ),
    
    "create_test_in_existing_suite": GradeConfig(
        name="grade_fn_create_test_in_existing_suite",
        testcase_name="create_test_in_existing_suite",
        uses_patch=True,
        checks=[
            CheckConfig("prompt_playground_test_modified", check_specific_files_modified_wrapper, is_async=True,
                       kwargs={"files": ['test/PromptPlayground.test.tsx']}),
            CheckConfig("prompt_slice_test_modified", check_specific_files_modified_wrapper, is_async=True,
                       kwargs={"files": ['test/promptSlice.test.ts']}),
            CheckConfig("store_test_modified", check_specific_files_modified_wrapper, is_async=True,
                       kwargs={"files": ['test/store.test.ts']}),
            CheckConfig("npm_test_used", check_npm_test_usage),
            # partial results checks
            CheckConfig("partial_results_events_count", check_partial_results_events_count, is_async=True),
            CheckConfig("partial_results_uses_markdown_task_list", check_partial_results_uses_markdown_task_list, is_async=True),
            CheckConfig("first_partial_result_has_incomplete_tasks", check_first_partial_result_has_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_no_incomplete_tasks", check_last_partial_result_no_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_has_completed_tasks", check_last_partial_result_has_completed_tasks, is_async=True),
            # result checks
            CheckConfig("pr_has_title", check_pr_has_title, is_async=True,
                       kwargs={"issue_number": 0}),
            CheckConfig("pr_description_has_body", check_pr_description_has_body, is_async=True,
                       kwargs={"issue_number": 0}),
        ],
    ),
    
    "promptly_start_fre_repo": GradeConfig(
        name="grade_fn_promptly_start_fre_repo", 
        testcase_name="promptly_start_fre_repo",
        uses_patch=False,
        checks=[
            CheckConfig("early_file_editing", check_early_file_editing_wrapper, is_async=True, kwargs={"turn_limit": 15}),
            # partial results checks
            CheckConfig("partial_results_events_count", check_partial_results_events_count, is_async=True),
            CheckConfig("partial_results_uses_markdown_task_list", check_partial_results_uses_markdown_task_list, is_async=True),
            CheckConfig("first_partial_result_has_incomplete_tasks", check_first_partial_result_has_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_no_incomplete_tasks", check_last_partial_result_no_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_has_completed_tasks", check_last_partial_result_has_completed_tasks, is_async=True),
            # result checks
            CheckConfig("pr_has_title", check_pr_has_title, is_async=True,
                       kwargs={"issue_number": 0}),
            CheckConfig("pr_description_has_body", check_pr_description_has_body, is_async=True,
                       kwargs={"issue_number": 0}),
        ],
    ),
    
    "lukehoban_full_function": GradeConfig(
        name="grade_fn_lukehoban_full_function",
        testcase_name="lukehoban_full_function",
        uses_patch=True,
        checks=[
            CheckConfig("main_go_modified", check_specific_files_modified_wrapper, is_async=True,
                       kwargs={"files": ['main.go']}),
            CheckConfig("main_test_go_modified", check_specific_files_modified_wrapper, is_async=True,
                       kwargs={"files": ['main_test.go']}),
            CheckConfig("eval_function_updated", check_content_wrapper, is_async=True,
                       kwargs={"patterns": ['func Eval(ast AST)']}),
            CheckConfig("parse_function_updated", check_content_wrapper, is_async=True,
                       kwargs={"patterns": ['func Parse(input string)']}),
            CheckConfig("fun_keyword_added", check_content_wrapper, is_async=True,
                       kwargs={"patterns": ['"FUN"'], "added_lines_only": True}),
            CheckConfig("eval_updated_for_fun", check_content_wrapper, is_async=True,
                       kwargs={"patterns": ['return Eval'], "added_lines_only": True}),
            CheckConfig("go_test_executed", check_go_test_execution_wrapper, is_async=True),
            # partial results checks
            CheckConfig("partial_results_events_count", check_partial_results_events_count, is_async=True),
            CheckConfig("partial_results_uses_markdown_task_list", check_partial_results_uses_markdown_task_list, is_async=True),
            CheckConfig("first_partial_result_has_incomplete_tasks", check_first_partial_result_has_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_no_incomplete_tasks", check_last_partial_result_no_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_has_completed_tasks", check_last_partial_result_has_completed_tasks, is_async=True),
            # result checks
            CheckConfig("pr_has_title", check_pr_has_title, is_async=True,
                       kwargs={"issue_number": 0}),
            CheckConfig("pr_description_has_body", check_pr_description_has_body, is_async=True,
                       kwargs={"issue_number": 0}),
        ],
    ),
    
    "can_address_full_test": GradeConfig(
        name="grade_fn_can_address_full_test",
        testcase_name="can_address_full_test",
        uses_patch=True,
        checks=[
            CheckConfig("expected_file_modified", check_only_specific_file_modified_wrapper, is_async=True,
                       kwargs={"files": ['runtime/test/model/capi-chat-completion-client.test.ts']}),
            CheckConfig("content_413_found", check_content_wrapper, is_async=True,
                       kwargs={"patterns": ['413']}),
            CheckConfig("content_copilot_swe_agent_vision_found", check_content_wrapper, is_async=True,
                       kwargs={"patterns": ['copilot_swe_agent_vision']}),
            CheckConfig("content_data_image_png_base64_found", check_content_wrapper, is_async=True,
                       kwargs={"patterns": ['data:image/png;base64']}),
            CheckConfig("content_429_found", check_content_wrapper, is_async=True,
                       kwargs={"patterns": ['429']}),
            # partial results checks
            CheckConfig("partial_results_events_count", check_partial_results_events_count, is_async=True),
            CheckConfig("partial_results_uses_markdown_task_list", check_partial_results_uses_markdown_task_list, is_async=True),
            CheckConfig("first_partial_result_has_incomplete_tasks", check_first_partial_result_has_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_no_incomplete_tasks", check_last_partial_result_no_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_has_completed_tasks", check_last_partial_result_has_completed_tasks, is_async=True),
            # result checks
            CheckConfig("pr_has_title", check_pr_has_title, is_async=True,
                       kwargs={"issue_number": 0}),
            CheckConfig("pr_description_has_body", check_pr_description_has_body, is_async=True,
                       kwargs={"issue_number": 0}),
        ],
    ),
    
    # Web Dev E2E Test Cases
    "webdev_react_scaffolding": GradeConfig(
        name="grade_fn_webdev_react_scaffolding",
        testcase_name="webdev_react_scaffolding",
        uses_patch=False,
        checks=[
            CheckConfig("used_scaffolding_approach", check_modern_scaffolding_usage, is_async=True),
            CheckConfig("did_not_abandon_scaffolding", check_scaffolding_not_abandoned, is_async=True),
            CheckConfig("started_dev_server", check_dev_server_started, is_async=True),
            CheckConfig("took_screenshot", check_playwright_screenshot_taken, is_async=True),
            # partial results checks
            CheckConfig("partial_results_events_count", check_partial_results_events_count, is_async=True),
            CheckConfig("partial_results_uses_markdown_task_list", check_partial_results_uses_markdown_task_list, is_async=True),
            CheckConfig("first_partial_result_has_incomplete_tasks", check_first_partial_result_has_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_no_incomplete_tasks", check_last_partial_result_no_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_has_completed_tasks", check_last_partial_result_has_completed_tasks, is_async=True),
            # result checks
            CheckConfig("pr_has_title", check_pr_has_title, is_async=True,
                       kwargs={"issue_number": 0}),
            CheckConfig("pr_description_has_body", check_pr_description_has_body, is_async=True,
                       kwargs={"issue_number": 0}),
        ],
    ),
    
    "webdev_playwright_auth_bug": GradeConfig(
        name="grade_fn_webdev_playwright_auth_bug",
        testcase_name="webdev_playwright_auth_bug",
        uses_patch=True,
        checks=[
            CheckConfig("navigated_to_page", check_playwright_navigation, is_async=True),
            CheckConfig("entered_password", check_playwright_password_entry, is_async=True),
            CheckConfig("clicked_login_button", check_playwright_login_click, is_async=True),
            CheckConfig("took_screenshot", check_playwright_screenshot_taken, is_async=True),
            # partial results checks
            CheckConfig("partial_results_events_count", check_partial_results_events_count, is_async=True),
            CheckConfig("partial_results_uses_markdown_task_list", check_partial_results_uses_markdown_task_list, is_async=True),
            CheckConfig("first_partial_result_has_incomplete_tasks", check_first_partial_result_has_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_no_incomplete_tasks", check_last_partial_result_no_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_has_completed_tasks", check_last_partial_result_has_completed_tasks, is_async=True),
            # result checks
            CheckConfig("pr_has_title", check_pr_has_title, is_async=True,
                       kwargs={"issue_number": 0}),
            CheckConfig("pr_description_has_body", check_pr_description_has_body, is_async=True,
                       kwargs={"issue_number": 0}),
        ],
    ),
    
    "webdev_playwright_comment_reply_with_screenshot": GradeConfig(
        name="grade_fn_webdev_playwright_comment_reply_with_screenshot",
        testcase_name="webdev_playwright_comment_reply_with_screenshot",
        uses_patch=True,
        checks=[
            CheckConfig("took_screenshot", check_playwright_screenshot_taken, is_async=True),
            CheckConfig("replied_to_comment_with_screenshot", check_comment_reply_with_screenshot, is_async=True),
            # partial results checks
            CheckConfig("partial_results_events_count", check_partial_results_events_count, is_async=True),
            CheckConfig("partial_results_uses_markdown_task_list", check_partial_results_uses_markdown_task_list, is_async=True),
            CheckConfig("first_partial_result_has_incomplete_tasks", check_first_partial_result_has_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_no_incomplete_tasks", check_last_partial_result_no_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_has_completed_tasks", check_last_partial_result_has_completed_tasks, is_async=True),
            # result checks
            CheckConfig("pr_has_title", check_pr_has_title, is_async=True,
                       kwargs={"issue_number": 0}),
            CheckConfig("pr_description_has_body", check_pr_description_has_body, is_async=True,
                       kwargs={"issue_number": 0}),
        ],
    ),
    
    "webdev_playwright_comment_reply_without_screenshot": GradeConfig(
        name="grade_fn_webdev_playwright_comment_reply_without_screenshot",
        testcase_name="webdev_playwright_comment_reply_without_screenshot",
        uses_patch=True,
        checks=[
            CheckConfig("replied_to_comment_without_screenshot", check_comment_reply_without_screenshot, is_async=True),
            # partial results checks
            CheckConfig("partial_results_events_count", check_partial_results_events_count, is_async=True),
            CheckConfig("partial_results_uses_markdown_task_list", check_partial_results_uses_markdown_task_list, is_async=True),
            CheckConfig("first_partial_result_has_incomplete_tasks", check_first_partial_result_has_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_no_incomplete_tasks", check_last_partial_result_no_incomplete_tasks, is_async=True),
            CheckConfig("last_partial_result_has_completed_tasks", check_last_partial_result_has_completed_tasks, is_async=True),
            # result checks
            CheckConfig("pr_has_title", check_pr_has_title, is_async=True,
                       kwargs={"issue_number": 0}),
            CheckConfig("pr_description_has_body", check_pr_description_has_body, is_async=True,
                       kwargs={"issue_number": 0}),
        ],
    ),
}

# Create the specific grading functions using the generic function
async def grade_fn_str_replace_patch_not_create(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
) -> bool:
    """Grade function for 'Does call str_replace to patch, not create' test case.
    
    This grader covers the following assertions:
    1. Does use dependency install (npm install)
    2. Does use existing build to run a build more than once (npm run build > 1)
    3. Does use build before str_replace
    4. Does use build after str_replace
    5. Does use str_replace to patch the file (home.tsx)
    6. Does not use str_replace to create a file (no create commands)
    7. home.tsx file content checks (title, description, old content removal)
    8. project is still buildable after changes
    """
    return await generic_grade_function(sample, terminal_session, GRADE_CONFIGS["str_replace_patch_not_create"])


async def grade_fn_create_test_in_existing_suite(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
) -> bool:
    """Grade function for 'Can create a test in existing tests if test suite is present' test case.
    
    This grader covers the following assertions:
    1. test/PromptPlayground.test.tsx is changed
    2. test/promptSlice.test.ts is changed  
    3. test/store.test.ts is changed
    4. Does use test to validate (npm test/npm run test)
    """
    return await generic_grade_function(sample, terminal_session, GRADE_CONFIGS["create_test_in_existing_suite"])


async def grade_fn_promptly_start_fre_repo(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
) -> bool:
    """Grade function for 'Promptly Starts Work in a Fresh Repo' test case.
    
    This grader covers the following assertion:
    1. A file was edited in the first 15 turns
    """
    return await generic_grade_function(sample, terminal_session, GRADE_CONFIGS["promptly_start_fre_repo"])


async def grade_fn_lukehoban_full_function(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
) -> bool:
    """Grade function for 'lukehoban full calclang function implementation' test case.
    
    This grader covers the following assertions:
    1. main.go is modified
    2. main_test.go is modified
    3. updates Eval function (func Eval(ast AST))
    4. updates Parse function (func Parse(input string))
    5. "FUN" is added to parser (appears in added lines starting with '+')
    6. Eval updated to handle FUN (return Eval appears in added lines starting with '+')
    7. go tests pass (executes 'go test -v ./...' and checks for no 'FAIL' in output)
    """
    return await generic_grade_function(sample, terminal_session, GRADE_CONFIGS["lukehoban_full_function"])


async def grade_fn_can_address_full_test(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
) -> bool:
    """Grade function for 'can address full test generation requested' test case.
    
    This grader covers the following assertions:
    1. runtime/test/model/capi-chat-completion-client.test.ts is the ONLY file modified (exactly one file)
    2. diff contains test for '413' status code
    3. diff contains test for 'copilot_swe_agent_vision' functionality
    4. diff contains test for 'data:image/png;base64' image data handling
    5. diff contains test for '429' status code
    """
    return await generic_grade_function(sample, terminal_session, GRADE_CONFIGS["can_address_full_test"])


# Web Dev E2E Test Case Grading Functions

async def grade_fn_webdev_react_scaffolding(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
) -> bool:
    """Grade function for 'Uses scaffolding tools to create a new React application' test case.
    
    This grader covers the following assertions from webDevE2E.test.ts:
    1. Used a scaffolding approach (modern React scaffolding commands)
    2. Did not abandon the scaffolding approach (no stop_bash on scaffolding session)
    3. Started the dev server (npm start, npm run dev, etc.)
    4. Took a screenshot of the running application (playwright-browser_take_screenshot)
    """
    return await generic_grade_function(sample, terminal_session, GRADE_CONFIGS["webdev_react_scaffolding"])


async def grade_fn_webdev_playwright_auth_bug(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
) -> bool:
    """Grade function for 'Uses playwright to resolve a bug based on repro steps' test case.
    
    This grader covers the following assertions from webDevE2E.test.ts:
    1. Navigated to a web page (playwright-browser_navigate)
    2. Entered password in the password textbox (playwright-browser_type with 'Password')
    3. Clicked the login button (playwright-browser_click with 'Login')
    4. Took a screenshot of the running application (playwright-browser_take_screenshot)
    """
    return await generic_grade_function(sample, terminal_session, GRADE_CONFIGS["webdev_playwright_auth_bug"])


async def grade_fn_webdev_playwright_comment_reply_with_screenshot(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
) -> bool:
    """Grade function for 'Uses playwright to take screenshots in comment reply' test case.
    
    This grader covers the following assertions from webDevE2E.test.ts:
    1. Took a screenshot of the running application (playwright-browser_take_screenshot)
    2. Replied to the comment with a screenshot (reply_to_comment with .png/.jpg)
    """
    return await generic_grade_function(sample, terminal_session, GRADE_CONFIGS["webdev_playwright_comment_reply_with_screenshot"])


async def grade_fn_webdev_playwright_comment_reply_without_screenshot(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
) -> bool:
    """Grade function for 'Does not include screenshots in comment reply when there are no UI changes' test case.
    
    This grader covers the following assertion from webDevE2E.test.ts:
    1. Replied to the comment without a screenshot (reply_to_comment without .png/.jpg)
    """
    return await generic_grade_function(sample, terminal_session, GRADE_CONFIGS["webdev_playwright_comment_reply_without_screenshot"])
