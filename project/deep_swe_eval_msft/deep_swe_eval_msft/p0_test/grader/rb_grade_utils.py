import structlog
import tenacity
import logging
from datetime import datetime
import aiofiles
import shlex

from caas import ExecError
from caas.terminal.api import TerminalSession
from caas.commands import RawExec
from caas_swe_bench_train_v2.parser import ModelPatch

logger = structlog.get_logger(__name__)


# Diff-based check functions using ModelPatch
async def check_files_modified_in_patch(model_patch: ModelPatch, file_patterns: list[str], instance_id: str | None = None) -> dict[str, bool]:
    """Check if specific files are modified in the patch."""
    results = {}
    patch_text = model_patch.raw_patch
    
    if instance_id:
        await log_to_file(f"Checking files modified in patch for patterns: {file_patterns}", instance_id)
        await log_to_file(f"Patch length: {len(patch_text)} characters", instance_id)
    
    for pattern in file_patterns:
        # Check if the file appears in the diff
        file_found = f"diff --git a/{pattern}" in patch_text or f"--- a/{pattern}" in patch_text
        results[pattern] = file_found
        
        if instance_id:
            await log_to_file(f"File '{pattern}' found in patch: {file_found}", instance_id)
            if file_found:
                # Show a snippet of the file's diff section
                import re
                pattern_escaped = re.escape(pattern)
                match = re.search(f"diff --git a/{pattern_escaped}.*?(?=diff --git|$)", patch_text, re.DOTALL)
                if match:
                    snippet = match.group(0)[:500] + "..." if len(match.group(0)) > 500 else match.group(0)
                    await log_to_file(f"Diff snippet for '{pattern}':\n{snippet}", instance_id)
    
    return results


async def check_content_in_patch(model_patch: ModelPatch, content_patterns: list[str], instance_id: str | None = None, added_lines_only: bool = False) -> dict[str, bool]:
    """Check if specific content appears in the patch.
    
    Args:
        model_patch: The patch to search in
        content_patterns: List of patterns to search for
        instance_id: Instance ID for logging
        added_lines_only: If True, only search in lines that start with '+' (added lines)
    """
    results = {}
    patch_text = model_patch.raw_patch
    
    if instance_id:
        search_type = "added lines only" if added_lines_only else "entire patch"
        await log_to_file(f"Checking content patterns in {search_type}: {content_patterns}", instance_id)
    
    for pattern in content_patterns:
        if added_lines_only:
            # Check only in lines that start with '+' (added lines)
            diff_lines = patch_text.split('\n')
            added_lines_with_pattern = [
                line for line in diff_lines 
                if pattern in line and line.startswith('+')
            ]
            content_found = len(added_lines_with_pattern) > 0
            
            if instance_id:
                await log_to_file(f"Pattern '{pattern}' found in {len(added_lines_with_pattern)} added lines", instance_id)
                if added_lines_with_pattern:
                    # Log first few matches for debugging
                    for i, line in enumerate(added_lines_with_pattern[:3]):
                        await log_to_file(f"  Added line {i+1}: {line}", instance_id)
        else:
            # Original behavior - check entire patch
            content_found = pattern in patch_text
            
            if instance_id:
                await log_to_file(f"Content pattern '{pattern}' found in patch: {content_found}", instance_id)
                if content_found:
                    # Show context around the found pattern
                    import re
                    # Find all occurrences with some context
                    matches = []
                    start = 0
                    while True:
                        idx = patch_text.find(pattern, start)
                        if idx == -1:
                            break
                        # Get 100 chars before and after for context
                        context_start = max(0, idx - 100)
                        context_end = min(len(patch_text), idx + len(pattern) + 100)
                        context = patch_text[context_start:context_end]
                        matches.append(f"...{context}...")
                        start = idx + 1
                        if len(matches) >= 3:  # Limit to first 3 matches
                            break
                    
                    for i, match in enumerate(matches):
                        await log_to_file(f"Pattern '{pattern}' context {i+1}: {match}", instance_id)
        
        results[pattern] = content_found
    
    return results


def check_file_content_updated_in_patch(model_patch: ModelPatch, filename: str, old_content: str, new_content: str) -> bool:
    """Check if a file has specific content changes in the patch."""
    patch_text = model_patch.raw_patch
    
    # Check if the file is modified
    file_modified = f"diff --git a/{filename}" in patch_text or f"--- a/{filename}" in patch_text
    if not file_modified:
        return False
    
    # Check for removal of old content and addition of new content
    old_removed = f"-{old_content}" in patch_text
    new_added = f"+{new_content}" in patch_text
    
    return old_removed and new_added


# Missing functions referenced in GRADE_CONFIGS
def check_str_replace_usage(conversation) -> bool:
    """Return True if str_replace command is used in str_replace_editor tool calls."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == 'functions.str_replace_editor':
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if command == "str_replace":
                    return True
            except Exception:
                continue
    return False


def check_create_usage(conversation) -> bool:
    """Return True if create command is used in str_replace_editor tool calls."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == 'functions.str_replace_editor':
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if command == "create":
                    return True
            except Exception:
                continue
    return False


def check_npm_build_usage(conversation) -> bool:
    """Return True if any bash tool call runs npm build commands."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if 'npm run build' in command or 'npm build' in command:
                    return True
            except Exception:
                continue
    return False


def check_home_tsx_mentioned(conversation) -> bool:
    """Return True if home.tsx is mentioned in conversation."""
    return any('app/routes/home.tsx' in str(msg.content) or 'home.tsx' in str(msg.content) 
               for msg in conversation.messages)


def check_test_file_creation(conversation) -> bool:
    """Return True if test file creation/modification is detected in conversation."""
    test_patterns = ['.test.', '.spec.', '/test/', '/tests/']
    return any(
        any(pattern in str(msg.content) for pattern in test_patterns)
        for msg in conversation.messages
    )


def check_report_progress(conversation) -> bool:
    """Return True if progress reporting is detected in conversation."""
    progress_patterns = ['progress', 'completed', 'finished', 'done']
    return any(
        any(pattern in str(msg.content).lower() for pattern in progress_patterns)
        for msg in conversation.messages
    )


logger = structlog.get_logger(__name__)


@tenacity.retry(
    retry=tenacity.retry_if_exception_type(
        (ExecError, Exception, TimeoutError, RuntimeError)
    ),
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(3),
    before_sleep=tenacity.before_sleep_log(logger, logging.WARNING),
)
async def log_to_file(
    message: str,
    instance_id: str,
    log_file: str = "/var/log/supervisor/p0_tests.log",
):
    """Logs a message to the specified log file with a timestamp."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        async with aiofiles.open(log_file, "a") as f:
            await f.write(f"[{timestamp}] [{instance_id}] {message}\n")
    except ImportError:
        # Fallback to synchronous logging if aiofiles not available
        with open(log_file, "a") as f:
            f.write(f"[{timestamp}] [{instance_id}] {message}\n")


# Conversation pattern check functions
def check_npm_install_usage(conversation) -> bool:
    """Return True if any bash tool call runs npm install commands."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if 'npm install' in command or 'npm ci' in command:
                    return True
            except Exception:
                continue
    return False


def check_build_usage_count(conversation) -> int:
    """Return count of npm build usage in bash tool calls."""
    import json
    
    count = 0
    for msg in conversation.messages:
        if msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if 'npm run build' in command:
                    count += 1
            except Exception:
                continue
    return count


def check_str_replace_on_home_tsx(conversation) -> bool:
    """Return True if str_replace is used on home.tsx file."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == 'functions.str_replace_editor':
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                path = args.get("path", "")
                if (command == "str_replace" and 
                    ('app/routes/home.tsx' in path or 'home.tsx' in path)):
                    return True
            except Exception:
                continue
    return False


def check_build_before_str_replace(conversation) -> bool:
    """Return True if npm build is used before str_replace on app/routes/home.tsx."""
    import json
    
    str_replace_found = False
    build_before_replace = False
    
    for msg in conversation.messages:
        # Check for npm build before we find str_replace on home.tsx
        if not str_replace_found and msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if 'npm run build' in command:
                    build_before_replace = True
            except Exception:
                continue
        
        # Check if we found str_replace on home.tsx (either full path or filename)
        if msg.recipient == 'functions.str_replace_editor':
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                path = args.get("path", "")
                if (command == "str_replace" and 
                    ('app/routes/home.tsx' in path or 'home.tsx' in path)):
                    str_replace_found = True
            except Exception:
                continue
    
    return build_before_replace and str_replace_found


def check_build_after_str_replace(conversation) -> bool:
    """Return True if npm build is used after str_replace on app/routes/home.tsx."""
    import json
    
    str_replace_found = False
    build_after_replace = False
    
    for msg in conversation.messages:
        # Check if we found str_replace on home.tsx (either full path or filename)
        if msg.recipient == 'functions.str_replace_editor':
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                path = args.get("path", "")
                if (command == "str_replace" and 
                    ('app/routes/home.tsx' in path or 'home.tsx' in path)):
                    str_replace_found = True
            except Exception:
                continue
        
        # Check for npm build after we found str_replace on home.tsx
        if str_replace_found and msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if 'npm run build' in command:
                    build_after_replace = True
            except Exception:
                continue
    
    return str_replace_found and build_after_replace


def check_title_content_updated(conversation) -> bool:
    """Return True if conversation shows title being updated to 'Copilot for Developers'."""
    return any(
        'Copilot for Developers' in str(msg.content)
        for msg in conversation.messages
    )


def check_description_content_updated(conversation) -> bool:
    """Return True if conversation shows description being updated to the target text."""
    target_desc = 'Copilot for Developers is a new way to build and ship software faster.'
    return any(
        target_desc in str(msg.content)
        for msg in conversation.messages
    )


def check_old_content_removal(conversation) -> bool:
    """Return True if conversation shows removal of old title/description content."""
    old_patterns = ['New React Router App', 'Welcome to React Router']
    return any(
        any(pattern in str(msg.content) for pattern in old_patterns)
        for msg in conversation.messages
    )


def check_specific_test_file_modified(conversation, test_file_name: str) -> bool:
    """Return True if a specific test file is mentioned or modified in conversation."""
    return any(
        test_file_name in str(msg.content)
        for msg in conversation.messages
    )


def check_npm_test_usage(conversation) -> bool:
    """Return True if any bash tool call runs npm test commands."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if 'npm test' in command or 'npm run test' in command:
                    return True
            except Exception:
                continue
    return False


def check_single_test_file_modified(conversation, expected_file: str) -> bool:
    """Return True if only the expected test file is mentioned/modified in conversation."""
    # Check if the expected file is mentioned
    expected_file_mentioned = check_specific_test_file_modified(conversation, expected_file)
    
    # For this simple check, we assume if the expected file is mentioned and 
    # it's the primary focus, then it's the only file modified
    return expected_file_mentioned


def check_expected_test_content(conversation, expected_words: list) -> dict:
    """Return a dict showing which expected words/content are found in conversation."""
    results = {}
    conversation_text = " ".join(str(msg.content) for msg in conversation.messages)
    
    for word in expected_words:
        results[word] = word in conversation_text
    
    return results


def check_early_file_editing(conversation, turn_limit: int = 15) -> bool:
    """Return True if file editing occurs within the specified turn limit.
    This matches the JavaScript logic that finds the lowest turn number where 
    str_replace_editor commands (str_replace, create, insert) are used.
    """
    import json
    
    file_editing_commands = ['str_replace', 'create', 'insert']
    lowest_turn_file_editing = float('inf')  # Equivalent to Number.MAX_SAFE_INTEGER
    
    for i, msg in enumerate(conversation.messages):
        # Consider message index as turn number (0-based, so add 1)
        turn_number = i + 1
        
        # Check if this message uses str_replace_editor with editing commands
        if msg.recipient == 'functions.str_replace_editor':
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if command in file_editing_commands:
                    if turn_number < lowest_turn_file_editing:
                        lowest_turn_file_editing = turn_number
            except Exception:
                continue
    
    # Check if lowest turn is within the limit (matches JS: lowestTurnToolMessageEventForStrReplaceEditor <= limit)
    return lowest_turn_file_editing <= turn_limit


def check_go_files_modified(conversation) -> dict:
    """Return a dict showing which Go files are mentioned/modified in conversation."""
    go_files = ['main.go', 'main_test.go']
    results = {}
    
    for file_name in go_files:
        results[file_name] = check_specific_test_file_modified(conversation, file_name)
    
    return results


def check_go_function_updates(conversation) -> dict:
    """Return a dict showing which Go functions are updated in conversation."""
    function_patterns = [
        'func Eval(ast AST)',
        'func Parse(input string)'
    ]
    results = {}
    conversation_text = " ".join(str(msg.content) for msg in conversation.messages)
    
    for pattern in function_patterns:
        results[pattern] = pattern in conversation_text
    
    return results


def check_go_fun_keyword_added(conversation) -> bool:
    """Return True if FUN keyword is added to parser in conversation."""
    conversation_text = " ".join(str(msg.content) for msg in conversation.messages)
    return '"FUN"' in conversation_text


def check_go_eval_updated_for_fun(conversation) -> bool:
    """Return True if Eval function is updated to handle FUN in conversation."""
    conversation_text = " ".join(str(msg.content) for msg in conversation.messages)
    return 'return Eval' in conversation_text or 'Eval(' in conversation_text


async def check_go_test_execution_wrapper(conversation, model_patch, instance_id, terminal_session):
    """Check if go tests pass by actually running 'go test -v ./...' command.
    This matches the JavaScript logic that:
    1. Checks out the original main_test.go
    2. Applies the test patch to add TestEval function
    3. Executes the test and checks for 'FAIL' in output.
    """
    
    # Test patch that adds TestEval function
    test_patch = """diff --git a/main_test.go b/main_test.go
index 9f68f3f..30801ab 100644
--- a/main_test.go
+++ b/main_test.go
@@ -22,3 +22,26 @@ func TestParse(t *testing.T) {
 		})
 	}
 }
+
+func TestEval(t *testing.T) {
+	tests := []struct {
+		input    string
+		expected int
+	}{
+		{"ADD 1 2", 3},
+		{"SUB 5 3", 2},
+		{"FUN x ADD x 1 2", 3}, // x=2, ADD 2 1 = 3
+		{"FUN y SUB y 3 5", 2}, // y=5, SUB 5 3 = 2
+	}
+
+	for _, tc := range tests {
+		t.Run(tc.input, func(t *testing.T) {
+			ast := Parse(tc.input)
+			if ast == nil {
+				t.Fatalf("Parse returned nil for input: %s", tc.input)
+			}
+			result := Eval(ast)
+			assert.Equal(t, tc.expected, result)
+		})
+	}
+}
+"""
    
    try:
        repo_root = f"/tmp/{instance_id}"
        
        # Step 1: Checkout the original main_test.go file
        await log_to_file(f"Checking out original main_test.go", instance_id)
        checkout_cmd = f"cd {repo_root} && git checkout 480526ac16d881cbbb6b8466d9f852b2e5aac1f2 -- main_test.go"
        output = await terminal_session.session.run(RawExec(["bash", "-c", checkout_cmd], timeout=120, workdir=repo_root))
        checkout_output = output[1].decode("utf-8").strip()
        
        await log_to_file(f"Git checkout completed", instance_id)
        
        # Step 2: Apply the test patch
        await log_to_file(f"Applying test patch to add TestEval function", instance_id)
        
        # Write the patch to a temporary file and apply it
        patch_cmd = f"cd {repo_root} && cat > test_patch.patch << 'EOF'\n{test_patch}\nEOF\ngit apply test_patch.patch"
        output = await terminal_session.session.run(RawExec(["bash", "-c", patch_cmd], timeout=120, workdir=repo_root))
        patch_apply_output = output[1].decode("utf-8").strip()
        
        await log_to_file(f"Test patch applied successfully", instance_id)
        
        # Step 3: Run the go tests
        await log_to_file(f"Running go test -v ./... in {repo_root}", instance_id)
        
        test_cmd = f"cd {repo_root} && go test -v ./..."
        output = await terminal_session.session.run(RawExec(["bash", "-c", test_cmd], timeout=120, workdir=repo_root))
        test_output = output[1].decode("utf-8").strip()
        
        output_str = test_output if isinstance(test_output, str) else str(test_output)
        
        # Check if test output contains 'FAIL' - if it does, tests failed
        tests_pass = 'FAIL' not in output_str
        
        if tests_pass:
            await log_to_file(f"Go tests passed. Output length: {len(output_str)} chars", instance_id)
        else:
            await log_to_file(f"Go tests failed. 'FAIL' found in output", instance_id)
            
        # Log a portion of the test output for debugging
        output_snippet = output_str[:1000] + "..." if len(output_str) > 1000 else output_str
        await log_to_file(f"Go test output snippet: {output_snippet}", instance_id)
        
        return tests_pass
        
    except Exception as e:
        await log_to_file(f"Error during go test execution process: {e}", instance_id)
        import traceback
        await log_to_file(f"Traceback: {traceback.format_exc()}", instance_id)
        return False


def check_go_test_execution(conversation) -> bool:
    """Return True if go test commands are executed in bash tool calls."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if 'go test' in command:
                    return True
            except Exception:
                continue
    return False

# Custom check functions that combine logic as needed
def check_create_not_used(conversation) -> bool:
    """Check that create tool was NOT used."""
    return not check_create_usage(conversation)

def check_str_replace_or_create_used(conversation) -> bool:
    """Check that either str_replace OR create tool was used."""
    return check_str_replace_usage(conversation) or check_create_usage(conversation)

def check_report_progress_or_home_tsx(conversation) -> bool:
    """Check that either report_progress was called OR home.tsx was mentioned."""
    return check_report_progress(conversation) or check_home_tsx_mentioned(conversation)


async def validate_conversation_and_patch(conversation, model_patch: ModelPatch, instance_id: str):
    """Validate the conversation and patch for debugging purposes."""
    await log_to_file("=== VALIDATION: Conversation and Patch Analysis ===", instance_id)
    
    # Conversation validation
    if not conversation:
        await log_to_file("ERROR: No conversation found", instance_id)
        return False
    
    message_count = len(conversation.messages)
    await log_to_file(f"Conversation has {message_count} messages", instance_id)
    
    # Log message types and recipients
    recipients = {}
    for i, msg in enumerate(conversation.messages):
        recipient = getattr(msg, 'recipient', 'unknown')
        recipients[recipient] = recipients.get(recipient, 0) + 1
        
        # Log first few messages for debugging
        if i < 5:
            content_preview = str(msg.content)[:200] + "..." if len(str(msg.content)) > 200 else str(msg.content)
            await log_to_file(f"Message {i+1} -> {recipient}: {content_preview}", instance_id)
    
    await log_to_file(f"Message recipients breakdown: {recipients}", instance_id)
    
    # Patch validation
    patch_length = len(model_patch.raw_patch)
    await log_to_file(f"Patch length: {patch_length} characters", instance_id)
    
    if patch_length == 0:
        await log_to_file("WARNING: Empty patch - no file changes detected", instance_id)
        return False
    
    # Count files in patch
    import re
    file_diffs = re.findall(r'diff --git a/([^\s]+)', model_patch.raw_patch)
    await log_to_file(f"Files modified in patch: {len(file_diffs)}", instance_id)
    if file_diffs:
        await log_to_file(f"Modified files: {file_diffs}", instance_id)
    
    # Count additions and deletions
    additions = model_patch.raw_patch.count('\n+')
    deletions = model_patch.raw_patch.count('\n-')
    await log_to_file(f"Patch stats - Additions: {additions}, Deletions: {deletions}", instance_id)
    
    return True


async def log_grading_summary(instance_id: str, function_name: str, checks: dict, result: bool):
    """Log a comprehensive grading summary for validation."""
    await log_to_file("=" * 60, instance_id)
    await log_to_file(f"GRADING SUMMARY FOR {function_name.upper()}", instance_id)
    await log_to_file("=" * 60, instance_id)
    
    await log_to_file(f"Overall Result: {'PASS' if result else 'FAIL'}", instance_id)
    await log_to_file(f"Total Checks: {len(checks)}", instance_id)
    passed_count = sum(1 for check in checks.values() if check)
    await log_to_file(f"Passed: {passed_count}, Failed: {len(checks) - passed_count}", instance_id)
    
    await log_to_file("\nDetailed Check Results:", instance_id)
    for check_name, check_result in checks.items():
        status = "✓ PASS" if check_result else "✗ FAIL"
        await log_to_file(f"  {status} - {check_name}: {check_result}", instance_id)
    
    await log_to_file("=" * 60, instance_id)


# Wrapper functions for complex checks that need additional parameters
async def check_specific_files_modified_wrapper(conversation, model_patch, instance_id, files):
    """Wrapper for check_files_modified_in_patch that returns result for the specific file(s)."""
    files_modified = await check_files_modified_in_patch(model_patch, files, instance_id)
    if len(files) == 1:
        # Return boolean result for single file
        return files_modified.get(files[0], False)
    else:
        # Return True if all files are modified
        return all(files_modified.values())


async def check_specific_files_modified_via_str_replace(conversation, model_patch, instance_id, files):
    """Wrapper that checks if specific files are modified via str_replace_editor tool calls."""
    import json
    
    if instance_id:
        await log_to_file(f"Checking if files {files} were modified via str_replace", instance_id)
    
    files_modified = {file: False for file in files}
    
    for msg in conversation.messages:
        if msg.recipient == 'functions.str_replace_editor':
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                path = args.get("path", "")
                
                if command == "str_replace":
                    for file in files:
                        if path and path.endswith(file):
                            files_modified[file] = True
                            if instance_id:
                                await log_to_file(f"Found str_replace for file {file} at path {path}", instance_id)
            except Exception as e:
                if instance_id:
                    await log_to_file(f"Error parsing str_replace_editor message: {e}", instance_id)
                continue
    
    if len(files) == 1:
        # Return boolean result for single file
        return files_modified.get(files[0], False)
    else:
        # Return True if all files are modified
        return all(files_modified.values())


async def check_content_wrapper(conversation, model_patch, instance_id, patterns, added_lines_only=False):
    """Wrapper for check_content_in_patch with specific patterns."""
    content_checks = await check_content_in_patch(model_patch, patterns, instance_id, added_lines_only=added_lines_only)
    return all(content_checks.values())

def check_build_usage_count_wrapper(conversation, count_threshold=1):
    """Wrapper for build usage count check."""
    build_count = check_build_usage_count(conversation)
    return build_count > count_threshold

def check_create_command_not_used_wrapper(conversation):
    """Wrapper that checks if create command is NOT used in str_replace_editor (matches JS implementation)."""
    import json
    
    used_create_count = 0
    
    for msg in conversation.messages:
        if msg.recipient == 'functions.str_replace_editor':
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if command == "create":
                    used_create_count += 1
            except Exception:
                continue
    
    # Return True if create command was NOT used (count is 0)
    return used_create_count == 0

async def check_early_file_editing_wrapper(conversation, instance_id=None, turn_limit=15):
    """Wrapper for early file editing check with enhanced logging."""
    import json
    
    if instance_id:
        await log_to_file(f"Checking for early file editing within {turn_limit} turns", instance_id)
    
    file_editing_commands = ['str_replace', 'create', 'insert']
    lowest_turn_file_editing = float('inf')
    file_editing_events = []
    turn_number = 0
    for msg in conversation.messages:
        if msg.author.role != "assistant":
            continue

        if msg.recipient.startswith("functions."):
            turn_number += 1

        # Check if this message uses str_replace_editor with editing commands
        if msg.recipient == 'functions.str_replace_editor':
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if command in file_editing_commands:
                    file_editing_events.append({
                        'turn': turn_number,
                        'command': command,
                        'path': args.get('path', 'unknown')
                    })
                    if turn_number < lowest_turn_file_editing:
                        lowest_turn_file_editing = turn_number
            except Exception:
                continue
    
    result = lowest_turn_file_editing <= turn_limit
    
    if instance_id:
        await log_to_file(f"File editing events found: {file_editing_events}", instance_id)
        await log_to_file(f"Lowest turn for file editing: {lowest_turn_file_editing}", instance_id)
        await log_to_file(f"Turn limit: {turn_limit}", instance_id)
        await log_to_file(f"Early file editing check result: {result}", instance_id)
        
        if not result and lowest_turn_file_editing != float('inf'):
            await log_to_file(f"File editing occurred too late. First editing at turn {lowest_turn_file_editing}, limit was {turn_limit}", instance_id)
        elif not result:
            await log_to_file(f"No file editing found in conversation", instance_id)
    
    return result

async def check_file_exists_and_content(instance_id: str, file_path: str, terminal_session) -> tuple[bool, str]:
    """Check if a file exists and return its content."""
    try:
        repo_root = f"/tmp/{instance_id}"
        full_path = f"{repo_root}/{file_path}"
        
        # Use terminal to check if file exists and read content
        await log_to_file(f"Checking if file {file_path} exists at {full_path}", instance_id)
        
        # Use terminal session to read the file content
        cat_cmd = f"cat {shlex.quote(full_path)}"
        output = await terminal_session.session.run(RawExec(["bash", "-c", cat_cmd], timeout=120, workdir=repo_root))
        content_output = output[1].decode("utf-8").strip()
        
        content = content_output if isinstance(content_output, str) else str(content_output)
        await log_to_file(f"File {file_path} exists with {len(content)} characters", instance_id)
        return True, content
        
    except Exception as e:
        await log_to_file(f"File {file_path} does not exist or cannot be read: {e}", instance_id)
        return False, ""

async def check_file_exists_wrapper(conversation, model_patch, instance_id, file_path, terminal_session):
    """Check if a specific file exists."""
    exists, _ = await check_file_exists_and_content(instance_id, file_path, terminal_session)
    return exists

async def check_file_content_contains_wrapper(conversation, model_patch, instance_id, file_path, patterns, terminal_session):
    """Check if file exists and contains all specified patterns."""
    exists, content = await check_file_exists_and_content(instance_id, file_path, terminal_session)
    if not exists:
        return False
    
    # Check if all patterns are found in the content
    for pattern in patterns:
        if pattern not in content:
            await log_to_file(f"Pattern '{pattern}' not found in {file_path}", instance_id)
            return False
    
    await log_to_file(f"All patterns {patterns} found in {file_path}", instance_id)
    return True

async def check_file_content_excludes_wrapper(conversation, model_patch, instance_id, file_path, patterns, terminal_session):
    """Check if file exists and does NOT contain any of the specified patterns."""
    exists, content = await check_file_exists_and_content(instance_id, file_path, terminal_session)
    if not exists:
        return False
    
    # Check that none of the patterns are found in the content
    for pattern in patterns:
        if pattern in content:
            await log_to_file(f"Unwanted pattern '{pattern}' found in {file_path}", instance_id)
            return False
    
    await log_to_file(f"None of the unwanted patterns {patterns} found in {file_path}", instance_id)
    return True

async def check_project_buildable_wrapper(conversation, model_patch, instance_id, terminal_session):
    """Check if the project is still buildable after changes by running npm run build."""
    
    try:
        repo_root = f"/tmp/{instance_id}"
        
        await log_to_file(f"Running npm run build in {repo_root}", instance_id)
        
        build_cmd = f"cd {repo_root} && npm run build"
        output = await terminal_session.session.run(RawExec(["bash", "-c", build_cmd], timeout=120, workdir=repo_root))
        build_output = output[1].decode("utf-8").strip()
        
        output_str = build_output if isinstance(build_output, str) else str(build_output)
        
        # Check if build output contains error indicators
        has_error = 'Error' in output_str or 'Fail' in output_str
        build_success = not has_error
        
        if build_success:
            await log_to_file(f"Build succeeded. Output length: {len(output_str)} chars", instance_id)
        else:
            await log_to_file(f"Build failed. Error indicators found in output", instance_id)
            
        # Log a portion of the build output for debugging
        output_snippet = output_str[:1000] + "..." if len(output_str) > 1000 else output_str
        await log_to_file(f"Build output snippet: {output_snippet}", instance_id)
        
        return build_success
        
    except Exception as e:
        await log_to_file(f"Error running build command: {e}", instance_id)
        return False


async def check_only_specific_file_modified_wrapper(conversation, model_patch, instance_id, files):
    """
    Wrapper that checks if exactly one specific file is modified and it's the expected one.
    This completely reuses the existing check_files_modified_in_patch function.
    This matches the logic:
    const modifiedFiles = getModifiedFilesInDiff(diff);
    check(checks, 'runtime/test/model/capi-chat-completion-client.test.ts is the only file modified', 
          modifiedFiles.length === 1 && modifiedFiles[0] === 'runtime/test/model/capi-chat-completion-client.test.ts', 
          `Modified files: ${modifiedFiles.join(', ')}`);
    """
    if len(files) != 1:
        await log_to_file(f"ERROR: check_only_specific_file_modified_wrapper expects exactly 1 file, got {len(files)}", instance_id)
        return False
    
    expected_file = files[0]
    
    # Extract all potential file names from the patch to check
    import re
    patch_text = model_patch.raw_patch
    file_diffs = re.findall(r'diff --git a/([^\s]+)', patch_text)
    
    await log_to_file(f"Modified files found in patch: {file_diffs}", instance_id)
    await log_to_file(f"Expected file: {expected_file}", instance_id)
    
    # Use the existing function to verify each file is actually modified
    if file_diffs:
        files_modified_check = await check_files_modified_in_patch(model_patch, file_diffs, instance_id)
        actually_modified_files = [f for f, modified in files_modified_check.items() if modified]
    else:
        actually_modified_files = []
    
    await log_to_file(f"Actually modified files (verified): {actually_modified_files}", instance_id)
    
    # Check if exactly one file is modified AND it's the expected file
    result = (len(actually_modified_files) == 1 and 
              actually_modified_files[0] == expected_file)
    
    if result:
        await log_to_file(f"SUCCESS: {expected_file} is the only file modified", instance_id)
    else:
        if len(actually_modified_files) != 1:
            await log_to_file(f"FAIL: Expected exactly 1 file modified, got {len(actually_modified_files)} files: {', '.join(actually_modified_files)}", instance_id)
        else:
            await log_to_file(f"FAIL: Expected '{expected_file}' to be modified, but got '{actually_modified_files[0]}'", instance_id)
    
    return result


def get_tasks(partial_result_message: str) -> dict:
    """Extract task counts from a partial result message.
    
    Args:
        partial_result_message: Message containing markdown task list
        
    Returns:
        Dict with 'incompleteTaskCount' and 'completedTaskCount'
    """
    import re
    
    # Look for incomplete tasks: - [ ]
    incomplete_tasks = re.findall(r'- \[ \].*', partial_result_message)
    # Look for completed tasks: - [x]
    completed_tasks = re.findall(r'- \[x\].*', partial_result_message)
    
    return {
        'incompleteTaskCount': len(incomplete_tasks),
        'completedTaskCount': len(completed_tasks)
    }


async def check_partial_results(conversation, instance_id: str | None = None) -> dict:
    """Check partial results events from conversation.
    
    Looks for messages that call functions.report_progress.
    """
    import json
    
    # Extract partial result events from conversation by looking for functions.report_progress calls
    partial_result_events = []
    for i, msg in enumerate(conversation.messages):
        # Check if this is an assistant message calling functions.report_progress
        if msg.recipient == "functions.report_progress":
            try:
                content = json.loads(str(msg.content))
                pr_desc = content.get("prDescription", None)
                if pr_desc is not None:
                    partial_result_events.append(pr_desc)
                    if instance_id:
                        await log_to_file(f"Extracted prDescription from message {i+1}: {len(pr_desc)} characters", instance_id)
            except Exception:
                continue
    
    await log_to_file(f"Found {len(partial_result_events)} partial result events", instance_id)
    
    results = {}
    # Since we're always dealing with createPRAgent, expect at least 2 partial result events
    expected_count = 2
    
    # Check if we have enough partial result events
    results['partial_results_events_count'] = len(partial_result_events) >= expected_count
    await log_to_file(f"Partial results count check: {len(partial_result_events)} >= {expected_count} = {results['partial_results_events_count']}", instance_id)
    
    # Check if markdown task list is used in all partial results
    markdown_task_list = True
    for i, partial_result in enumerate(partial_result_events):
        has_markdown = '- [' in str(partial_result)
        if not has_markdown:
            markdown_task_list = False
            if instance_id:
                await log_to_file(f"Partial result {i+1} missing markdown task list", instance_id)
        else:
            if instance_id:
                await log_to_file(f"Partial result {i+1} has markdown task list", instance_id)
    results['partial_results_uses_markdown_task_list'] = markdown_task_list
    
    # For createPRAgent action, check task progression
    if len(partial_result_events) > 0:
        # Check first partial result has incomplete tasks
        first_tasks = get_tasks(str(partial_result_events[0]))
        results['first_partial_result_has_incomplete_tasks'] = first_tasks['incompleteTaskCount'] > 0
        await log_to_file(f"First partial result tasks: {first_tasks['incompleteTaskCount']} incomplete, {first_tasks['completedTaskCount']} completed", instance_id)
        
        # Check last partial result
        last_tasks = get_tasks(str(partial_result_events[-1]))
        results['last_partial_result_no_incomplete_tasks'] = last_tasks['incompleteTaskCount'] == 0
        results['last_partial_result_has_completed_tasks'] = last_tasks['completedTaskCount'] > 0
        await log_to_file(f"Last partial result tasks: {last_tasks['incompleteTaskCount']} incomplete, {last_tasks['completedTaskCount']} completed", instance_id)
    else:
        await log_to_file("No partial result events found, skipping task progression checks", instance_id)
        results['first_partial_result_has_incomplete_tasks'] = False
        results['last_partial_result_no_incomplete_tasks'] = False
        results['last_partial_result_has_completed_tasks'] = False
    
    await log_to_file(f"Partial results check summary: {results}", instance_id)
    return results


async def check_result(conversation, issue_number: int = 0, instance_id: str | None = None) -> dict:
    """Check result from conversation. Looks for messages with channel "final".
    """
    import re
    
    await log_to_file(f"Starting check_result for instance_id: {instance_id}, issue_number: {issue_number}", instance_id)
    
    # Try to extract result from conversation - look for final result or PR details
    result_found = False
    pr_title = ""
    pr_description = ""
    
    final_messages = 0
    
    # Look through messages for PR creation results in "final" messages
    for i, msg in enumerate(conversation.messages):
        content_str = str(msg.content)
        
        # Look for assistant messages with channel "final"
        if msg.channel == "final":
            
            final_messages += 1
            result_found = True
            
            if instance_id:
                await log_to_file(f"Found final message {final_messages} in message {i+1}", instance_id)
                await log_to_file(f"Final message content length: {len(content_str)} characters", instance_id)
            
            # Extract PR title using regex - look for <pr_title>...</pr_title>
            title_match = re.search(r'<pr_title>\s*(.*?)\s*</pr_title>', content_str, re.DOTALL)
            if title_match:
                pr_title = title_match.group(1).strip()
                if instance_id:
                    await log_to_file(f"Extracted PR title: '{pr_title}' (length: {len(pr_title)})", instance_id)
            else:
                if instance_id:
                    await log_to_file(f"No <pr_title> tags found in final message {final_messages}", instance_id)
            
            # Extract PR description using regex - look for <pr_description>...</pr_description>
            desc_match = re.search(r'<pr_description>\s*(.*?)\s*</pr_description>', content_str, re.DOTALL)
            if desc_match:
                pr_description = desc_match.group(1).strip()
                if instance_id:
                    await log_to_file(f"Extracted PR description: {len(pr_description)} characters", instance_id)
                    await log_to_file(f"PR description preview: {pr_description[:200]}...", instance_id)
            else:
                if instance_id:
                    await log_to_file(f"No <pr_description> tags found in final message {final_messages}", instance_id)
            
            # Break after finding the first final message (should be only one)
            break
    
    
    results = {}
    
    results['pr_has_title'] = len(pr_title) > 0
    results['pr_description_has_body'] = len(pr_description) > 0
    
    if instance_id:
        await log_to_file(f"PR title check: '{pr_title}' -> {results['pr_has_title']}", instance_id)
        await log_to_file(f"PR description check: {len(pr_description)} chars -> {results['pr_description_has_body']}", instance_id)
    
    if issue_number != 0:
        expected_text = f"Fixes #{issue_number}."
        has_fixes_issue = expected_text in pr_description
        results['pr_description_has_fixes_issue'] = has_fixes_issue
        if instance_id:
            await log_to_file(f"Fixes issue check: looking for '{expected_text}' -> {has_fixes_issue}", instance_id)
    
    results['result_found'] = result_found
    
    await log_to_file(f"Result check summary: {results}", instance_id)
    return results

async def check_partial_results_events_count(conversation, model_patch=None, instance_id: str = ""):
    """Check if partial results events >= 2."""
    partial_results = await check_partial_results(conversation, instance_id)
    return partial_results.get('partial_results_events_count', False)

async def check_partial_results_uses_markdown_task_list(conversation, model_patch=None, instance_id: str = ""):
    """Check if partial results events uses markdown task list."""
    partial_results = await check_partial_results(conversation, instance_id)
    return partial_results.get('partial_results_uses_markdown_task_list', False)

async def check_first_partial_result_has_incomplete_tasks(conversation, model_patch=None, instance_id: str = ""):
    """Check if first partial result should have incomplete tasks."""
    partial_results = await check_partial_results(conversation, instance_id)
    return partial_results.get('first_partial_result_has_incomplete_tasks', False)

async def check_last_partial_result_no_incomplete_tasks(conversation, model_patch=None, instance_id: str = ""):
    """Check if last partial result should not have incomplete tasks."""
    partial_results = await check_partial_results(conversation, instance_id)
    return partial_results.get('last_partial_result_no_incomplete_tasks', False)

async def check_last_partial_result_has_completed_tasks(conversation, model_patch=None, instance_id: str = ""):
    """Check if last partial result should have completed tasks."""
    partial_results = await check_partial_results(conversation, instance_id)
    return partial_results.get('last_partial_result_has_completed_tasks', False)

async def check_pr_has_title(conversation, model_patch=None, instance_id: str = "", issue_number=0):
    """Check if PR has title."""
    result = await check_result(conversation, issue_number, instance_id)
    return result.get('pr_has_title', False)

async def check_pr_description_has_body(conversation, model_patch=None, instance_id: str = "", issue_number=0):
    """Check if PR description has body."""
    result = await check_result(conversation, issue_number, instance_id)
    return result.get('pr_description_has_body', False)


# Web Dev E2E Test Case Checking Functions

async def check_modern_scaffolding_usage(conversation) -> bool:
    """Check if modern React scaffolding tools were used OR manual React scaffolding was used."""
    import json
    
    modern_scaffolding_commands = [
        "npx create-next-app",      # Next.js (recommended by React team)
        "npm create vite@latest",   # Vite (recommended by React team)
        "npx create-vite",          # Alternative Vite command
        "npm create react@latest",  # Modern React create command
        "npx create-react-router",  # React Router framework
        "npx create-remix",         # Remix framework
        "npm create expo@latest",   # Expo for React Native/web
        "npx create-react-app",     # Legacy but still functional
    ]
    
    # Check for modern scaffolding tools first
    for msg in conversation.messages:
        if msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if any(cmd in command for cmd in modern_scaffolding_commands):
                    return True
            except Exception:
                continue
    
    # If no modern scaffolding tools found, check for manual React scaffolding
    return await check_manual_react_scaffolding(conversation)


async def check_scaffolding_not_abandoned(conversation) -> bool:
    """Check if scaffolding approach was not abandoned (no stop_bash on scaffolding session)."""
    import json
    
    scaffolding_session_id = None
    modern_scaffolding_commands = [
        "npx create-next-app", "npm create vite@latest", "npx create-vite",
        "npm create react@latest", "npx create-react-router", "npx create-remix",
        "npm create expo@latest", "npx create-react-app"
    ]
    
    # First, find the scaffolding session ID
    for msg in conversation.messages:
        if msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if any(cmd in command for cmd in modern_scaffolding_commands):
                    scaffolding_session_id = args.get('sessionId')
                    break
            except Exception:
                continue
    
    if not scaffolding_session_id:
        return True  # No scaffolding found, so it wasn't abandoned
    
    # Check if stop_bash was called on the scaffolding session
    for msg in conversation.messages:
        if msg.recipient == "functions.stop_bash":
            try:
                args = json.loads(str(msg.content))
                session_id = args.get('sessionId', '')
                if scaffolding_session_id and scaffolding_session_id == session_id:
                    return False  # Scaffolding was abandoned
            except Exception:
                continue
    
    return True  # Scaffolding was not abandoned


async def check_dev_server_started(conversation) -> bool:
    """Check if a development server was started."""
    import json
    
    dev_server_commands = [
        "npm start", "npm run start", "npm run dev", "npm run serve",
        "yarn start", "yarn dev", "pnpm start", "pnpm dev",
        "npx vite", "npx next dev", "npx serve",
        "python -m http.server", "python3 -m http.server"
    ]
    
    for msg in conversation.messages:
        if msg.recipient == "functions.bash":
            try:
                args = json.loads(str(msg.content))
                command = args.get("command", "")
                if any(cmd in command for cmd in dev_server_commands):
                    return True
            except Exception:
                continue
    return False


async def check_playwright_screenshot_taken(conversation) -> bool:
    """Check if a screenshot was taken using Playwright."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == "functions.playwright_browser_take_screenshot":
            try:
                args = json.loads(str(msg.content))
                # Check if the args contain image file extensions
                args_str = str(args)
                if '.png' in args_str or '.jpg' in args_str or '.jpeg' in args_str:
                    return True
            except Exception:
                continue
    return False


async def check_playwright_navigation(conversation) -> bool:
    """Check if Playwright was used to navigate to a web page."""
    for msg in conversation.messages:
        if msg.recipient == "functions.playwright_browser_navigate":
            return True
    return False


async def check_playwright_password_entry(conversation) -> bool:
    """Check if password was entered using Playwright."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == "functions.playwright_browser_type":
            try:
                args = json.loads(str(msg.content))
                # Check if the args contain password-related text
                args_str = str(args).lower()
                if 'password' in args_str:
                    return True
            except Exception:
                continue
    return False


async def check_playwright_login_click(conversation) -> bool:
    """Check if login button was clicked using Playwright."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == "functions.playwright_browser_click":
            try:
                args = json.loads(str(msg.content))
                # Check if the args contain login-related text
                args_str = str(args).lower()
                if 'login' in args_str:
                    return True
            except Exception:
                continue
    return False


async def check_comment_reply_with_screenshot(conversation) -> bool:
    """Check if comment reply included a screenshot."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == "functions.reply_to_comment":
            try:
                args = json.loads(str(msg.content))
                # Check if the args contain image file extensions
                args_str = str(args)
                if '.png' in args_str or '.jpg' in args_str or '.jpeg' in args_str:
                    return True
            except Exception:
                continue
    return False


async def check_comment_reply_without_screenshot(conversation) -> bool:
    """Check if comment reply was made without a screenshot."""
    import json
    
    for msg in conversation.messages:
        if msg.recipient == "functions.reply_to_comment":
            try:
                args = json.loads(str(msg.content))
                # Check if the args do NOT contain image file extensions
                args_str = str(args)
                if '.png' not in args_str and '.jpg' not in args_str and '.jpeg' not in args_str:
                    return True
            except Exception:
                continue
    return False


async def check_manual_react_scaffolding(conversation) -> bool:
    """Check if manual React scaffolding was used (creating React project files manually).
    
    This function detects when React project files are created manually by looking for
    file creation/editing operations that include React-specific indicators.
    
    Based on the TypeScript implementation:
    - Looks for 'str_replace_editor' or 'create' function calls
    - Checks for React-specific files and dependencies
    """
    import json
    
    # Manual scaffolding indicators from the TypeScript code
    manual_scaffolding_indicators = [
        "vite.config.js",           # Vite configuration
        "package.json",             # Package manifest
        "App.jsx",                  # React App component
        "@vitejs/plugin-react",     # Vite React plugin
        "react",                    # React dependency
        "react-dom",                # React DOM dependency
    ]
    
    for msg in conversation.messages:
        # Check both str_replace_editor and create function calls
        if msg.recipient in ["functions.str_replace_editor", "functions.create"]:
            try:
                args = json.loads(str(msg.content))
                args_str = str(args)
                
                # Check if any of the manual scaffolding indicators are present
                if any(indicator in args_str for indicator in manual_scaffolding_indicators):
                    return True
            except Exception:
                continue
    
    return False
