import textwrap
from typing import Any, Literal, Sequence

import chat
from caas_utils.conversation_init import (
    InstructionInsertionFormat,
    datapoint_rng,
    get_style_criteria_instructions,
)

from deep_swe_eval_msft.data_converter.padawan_v2_prompt import get_user_message, P0_webdev_eval_get_user_message

def conversation_init_p0_test_fn_padawan(
    *,
    datapoint: dict[str, Any],
) -> Sequence[chat.Message]:
    """
    Conversation init function for P0 test data.
    P0 data has structure: {"problem": "...", "unique_id": "...", "metadata": {...}}
    """
    problem = datapoint["problem"]
    instance_id = datapoint["unique_id"]
    
    # P0 test data may not have the same metadata structure as msweb
    # Extract what we can from metadata, provide defaults if missing
    metadata = datapoint.get("metadata", {})
    workdir = metadata.get("cwd", "/tmp")
    
    # P0 might not have org/repo structure, so we'll create a synthetic one
    task_meta = metadata.get("task", {})
    if "org" in task_meta and "repo" in task_meta:
        repo_name = task_meta["org"] + "/" + task_meta["repo"]
    else:
        # Use instance_id as synthetic repo name for P0 tests
        repo_name = f"p0-test/{instance_id}"

    user_prompt = get_user_message(repo_name, instance_id, problem, workdir=workdir)
    
    return [chat.Message.user(user_prompt)]


def conversation_init_p0_webdev_fn_padawan(
    *,
    datapoint: dict[str, Any],
) -> Sequence[chat.Message]:
    """
    Conversation init function for P0 test data.
    P0 data has structure: {"problem": "...", "unique_id": "...", "metadata": {...}}
    """
    problem = datapoint["problem"]
    instance_id = datapoint["unique_id"]
    
    # P0 test data may not have the same metadata structure as msweb
    # Extract what we can from metadata, provide defaults if missing
    metadata = datapoint.get("metadata", {})
    workdir = metadata.get("cwd", "/tmp")
    
    # P0 might not have org/repo structure, so we'll create a synthetic one
    task_meta = metadata.get("task", {})
    commit_id = metadata.get("commit", "1234567890abcdef")
    if "org" in task_meta and "repo" in task_meta:
        repo_name = task_meta["org"] + "/" + task_meta["repo"]
    else:
        # Use instance_id as synthetic repo name for P0 tests
        repo_name = f"p0-test/{instance_id}"

    user_prompt = P0_webdev_eval_get_user_message(repo_name, instance_id, problem, commit_id, workdir=workdir)
    
    return [chat.Message.user(user_prompt)]
