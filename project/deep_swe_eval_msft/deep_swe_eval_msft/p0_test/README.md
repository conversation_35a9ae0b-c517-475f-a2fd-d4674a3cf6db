# GitHub Padawan P0 Basic Evaluation Tests

This directory contains the Padawan P0 basic evaluation tests, based on the tests from the [sweagentd GitHub repository](https://github.com/github/sweagentd/blob/main/runtime/test/evals/basic.test.ts).

## Setup Instructions

### 1. Branch Configuration

Ensure you're using the correct branches:
- **Glass repo**: `main` branch
- **Torchflow-mirror repo**: `orange/main` branch

### 2. Cluster Setup

#### Sync Code to Existing Cluster
If you already have a peashooter cluster for peaval evaluation, sync the latest code to both controller and worker nodes using:

```bash
b sync
```

#### Create New Peashooter Cluster
If you need to create a new peashooter cluster, use the following configuration:

```bash
PS=(
    python -m qstar.peashooter.run_peashooter
    ...rapid_id=p0-peaval-eval
    rapid.cluster.name=prod-southcentralus-hpe-5
    rapid.cluster.priority=team-critical
    rapid.pull_git_on_restart=False
    num_controller_nodes=2
    cpu_controller=True
    n_train_gpus=0
    n_gpus_per_sampler=8
    sampling_jobs=2
    eval_only=True
    security_profile=msft-orng
    strict=False
)
BRIX_QUOTA=team-moonfire-genaicore "${PS[@]}"
```

## Running the Evaluation

### 1. Access the Controller Node
SSH into the controller node of your peashooter cluster:
```bash
b ssh
```

### 2. Navigate to Evaluation Directory
```bash
cd ~/code/glass/project/deep_swe_eval_msft/deep_swe_eval_msft/p0_test/peaval
```

### 3. Submit Evaluation Job

Launch a tmux session and run the evaluation script:
```bash
tmux
bash pdwp0_peaval_eval.sh
```

### 4. View the HTML Report

Once the evaluation job completes, you can view the HTML report for your peaval run in Azure storage:

`az://orngscuscresco/data/padawan_p0_test_reports/<checkpoint_name>_step_<step>.html`

## Report Comparison

You can compare HTML reports from different checkpoints to analyze evaluation results.

- [Watch a quick demo video](https://microsoft-my.sharepoint.com/:v:/p/gargamit/EYxqT_OxQSNMiRNNkeeUiA0Bss4TG3nLHYUrE24AKk_RvQ?e=AKewhF&nav=eyJyZWZlcnJhbEluZm8iOnsicmVmZXJyYWxBcHAiOiJTdHJlYW1XZWJBcHAiLCJyZWZlcnJhbFZpZXciOiJTaGFyZURpYWxvZy1MaW5rIiwicmVmZXJyYWxBcHBQbGF0Zm9ybSI6IldlYiIsInJlZmVycmFsTW9kZSI6InZpZXcifX0%3D)

- Use the [report comparison web app](https://report-comparator.azurewebsites.net/) to compare two or more reports.

**Steps:**
1. Download the desired HTML report files from  
    `az://orngscuscresco/data/padawan_p0_test_reports/<checkpoint_name>_step_<step>.html`
2. Open the [report comparison web app](https://report-comparator.azurewebsites.net/).
3. Upload the downloaded HTML files to compare their results side by side.
