datetime=$(date +"%Y%m%d-%H%M")

INIT_CKPT=az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-4-pdw2-ev3-mixed-itc-spi32-gpt5-mini-sft-1e-4-469-100steps-tef03-5-tpm1-rm-lr1e-5-run-20250813-072230/policy/step_000100/250815071207CK2G3E3O-0/

CMD=(
beam python --use-cwd -m qstar.run_eval # from David's p0 submission script
# oaipkg run qstar.run_eval # from o3-reproduce
nostrict  
name=pdwp0-eval-$datetime-tp-peaval

# checkpoint from https://lemon.int.prod-uksouth-7.dev.openai.org/experiments/legacy?experiment_id=damajercak-mix16-arm-4-pdw2-ev3-mixed-itc-spi32-gpt5-mini-sft-1e-4-469-100steps-tef03-5-tpm1-rm-lr1e-5-run-20250813-072230&dataset_name=data.damajercak.swe.upload07312025.sbhv2.train&grader_name=MultiStageGraderWithAuxReinforcements&filters=damajercak
:berry_models.scallion:d64_chicken_80g_bf16
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0}'
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc
policy.n_ctx=262144
defaults.n_ctx=262144

auto_inherit_training_args=False 
eval_settings.checkpoint_dir="$INIT_CKPT"
policy.initial_checkpoint="$INIT_CKPT" 
load.restore_from_all_clusters=False

policy.is_multimodal=True  
...encoding_name=orion_200k  

peashooter.timeout_seconds.stalled_datapoint=3600
peashooter.timeout_seconds.lost_datapoint=1800
peashooter.use_stale_kv_cache=False
load.restore_from_all_clusters=False

metrics_collector=deep_swe_eval_msft.metrics.p0_metrics_collector:P0MetricsCollector

...max_num_yields=256
defaults.sampler.max_num_yields=256
policy.encoding_name=orion_200k
defaults.sampler.harmony_constrained_sampling=True
eval_settings.eval_initial_policy=True
eval_settings.eval_every=10000

# :deep_swe_eval_msft.p0_test.peaval.padawan.presets:padawan_p0_tests
:deep_swe_eval_msft.p0_test.peaval.padawan.presets:padawan_p0_webdev

security_profile=msft-orng
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a peaval-$datetime.log