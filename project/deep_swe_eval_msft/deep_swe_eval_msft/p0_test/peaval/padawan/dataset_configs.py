import chz
from typing import Sequence
import chat

from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.graders.grader import Grader
from qstar.presets.chz_utils import IsOverride, override
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.curriculums.function_wrapper import FunctionWrapper

import caas_autograding.grader as caas_graders

from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY, PADAWAN_SYSTEM_PROMPT
from deep_swe_eval_msft.p0_test.peaval.padawan.caas_resource_config import P0TestCaasContainerResourceConfig as DeepSWECaasContainerResourceConfigPadawan
from deep_swe_eval_msft.p0_test.peaval.padawan.setup import p0_test_setup_fn as p0_test_setup_fn_padawan
from deep_swe_eval_msft.p0_test.peaval.padawan.setup import p0_webdev_setup_fn as p0_webdev_setup_fn_padawan
from deep_swe_eval_msft.graders.eval_unit_test import EvalTermberryGrader
from deep_swe_eval_msft.tools.caas_padawan_tool import P0EvalPadawanToolConfig, P0EvalPadawanWebDevToolConfig
from chat.render.v4.experimental.strawberry.formatter import BerryChannel

CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
CAAS_IDLE_TTL = 1200


# Individual grader functions for each dataset config

def make_can_address_full_test_grader_padawan() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    """Grader for can_address_full_test dataset - tests full test generation capabilities."""
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.p0_test.grader.p0_test_grader:grade_fn_can_address_full_test",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def make_can_create_a_test_grader_padawan() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    """Grader for can_create_a_test dataset - tests test creation in existing suites."""
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.p0_test.grader.p0_test_grader:grade_fn_create_test_in_existing_suite",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def make_does_call_str_replace_grader_padawan() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    """Grader for does_call_str_replace dataset - tests str_replace vs create usage."""
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.p0_test.grader.p0_test_grader:grade_fn_str_replace_patch_not_create",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def make_lukehoban_full_function_grader_padawan() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    """Grader for lukehoban_full_function dataset - tests comprehensive Go file editing capabilities."""
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.p0_test.grader.p0_test_grader:grade_fn_lukehoban_full_function",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def make_promptly_start_fre_repo_grader_padawan() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    """Grader for promptly_start_fre_repo dataset - tests early file editing and npm dependency management."""
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.p0_test.grader.p0_test_grader:grade_fn_promptly_start_fre_repo",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def make_p0_test_graders_padawan() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    """Legacy function - kept for backwards compatibility. Use specific grader functions instead."""
    # Default to can_address_full_test grader for backwards compatibility
    return make_can_address_full_test_grader_padawan()


def _make_tool_configs_padawan(
    enable_retrieval: bool = False,
) -> tuple[ToolConfig, ...]:
    return (P0EvalPadawanToolConfig(),)


def _make_tool_configs_padawan_webdev(
    enable_retrieval: bool = False,
) -> tuple[ToolConfig, ...]:
    return (P0EvalPadawanWebDevToolConfig(),)


@chz.chz
class P0VerifiedGraderPadawan(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(make_p0_test_graders_padawan)

    @property
    def accepts_invalid(self) -> bool:
        """
        P0 computer tasks can be graded at any point in time.
        """
        return True


@chz.chz(typecheck=True)
class P0TestConfig(DeepSWEDatasetConfig, IsOverride):
    """Base class for P0 dataset configurations"""
    dataset_container: str = chz.field(default="orngscuscresco")
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs_padawan)
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigPadawan(setup_fn=p0_test_setup_fn_padawan,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter("deep_swe_eval_msft.p0_test.preparedness.conversation_init:conversation_init_p0_test_fn_padawan"),
        )
    )
    model_identity_str: str = PADAWAN_MODEL_IDENTITY
    instructions: str = PADAWAN_SYSTEM_PROMPT


@chz.chz(typecheck=True)
class P0CanAddressFullTestDatasetConfigPadawan(P0TestConfig, IsOverride):
    dataset_id: str = chz.field(default="data.datasets.swe.eval.p0_jsonl.can_address_full_test")
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: P0VerifiedGraderPadawan(graders=make_can_address_full_test_grader_padawan())
    )


@chz.chz(typecheck=True)
class P0CanCreateATestDatasetConfigPadawan(P0TestConfig, IsOverride):
    dataset_id: str = chz.field(default="data.datasets.swe.eval.p0_jsonl.can_create_a_test")
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: P0VerifiedGraderPadawan(graders=make_can_create_a_test_grader_padawan())
    )


@chz.chz(typecheck=True)
class P0DoesCallStrReplaceDatasetConfigPadawan(P0TestConfig, IsOverride):
    dataset_id: str = chz.field(default="data.datasets.swe.eval.p0_jsonl.does_call_str_replace")
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: P0VerifiedGraderPadawan(graders=make_does_call_str_replace_grader_padawan())
    )


@chz.chz(typecheck=True)
class P0LukehobanFullFunctionDatasetConfigPadawan(P0TestConfig, IsOverride):
    dataset_id: str = chz.field(default="data.datasets.swe.eval.p0_jsonl.lukehoban_full_function")
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: P0VerifiedGraderPadawan(graders=make_lukehoban_full_function_grader_padawan())
    )


@chz.chz(typecheck=True)
class P0PromptlyStartFreRepoDatasetConfigPadawan(P0TestConfig, IsOverride):
    dataset_id: str = chz.field(default="data.datasets.swe.eval.p0_jsonl.promptly_start_fre_repo")
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: P0VerifiedGraderPadawan(graders=make_promptly_start_fre_repo_grader_padawan())
    )

@chz.chz(typecheck=True)
class P0WebDevTestConfig(DeepSWEDatasetConfig, IsOverride):
    """Webdev for P0 dataset configurations"""
    dataset_container: str = chz.field(default="orngscuscresco")
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs_padawan_webdev)
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigPadawan(setup_fn=p0_webdev_setup_fn_padawan,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL,
                                               use_webdev_image=True),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter("deep_swe_eval_msft.p0_test.preparedness.conversation_init:conversation_init_p0_webdev_fn_padawan"),
        )
    )
    model_identity_str: str = PADAWAN_MODEL_IDENTITY
    instructions: str = PADAWAN_SYSTEM_PROMPT

def make_webdev_react_scaffolding() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.p0_test.grader.p0_test_grader:grade_fn_webdev_react_scaffolding",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

@chz.chz(typecheck=True)
class P0WebDevReactScaffoldingDatasetConfigPadawan(P0WebDevTestConfig, IsOverride):
    dataset_id: str = chz.field(default="data.datasets.swe.eval.p0_jsonl.webdev_react_scaffolding")
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: P0VerifiedGraderPadawan(graders=make_webdev_react_scaffolding())
    )

def make_webdev_playwright_auth_bug() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.p0_test.grader.p0_test_grader:grade_fn_webdev_playwright_auth_bug",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

@chz.chz(typecheck=True)
class P0WebDevPlaywrightAuthBugDatasetConfigPadawan(P0WebDevTestConfig, IsOverride):
    dataset_id: str = chz.field(default="data.datasets.swe.eval.p0_jsonl.webdev_playwright_auth_bug")
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: P0VerifiedGraderPadawan(graders=make_webdev_playwright_auth_bug())
    )

def make_webdev_playwright_comment_reply_with_screenshot() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.p0_test.grader.p0_test_grader:grade_fn_webdev_playwright_comment_reply_with_screenshot",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

@chz.chz(typecheck=True)
class P0WebDevPlaywrightCommentReplyWithScreenshotDatasetConfigPadawan(P0WebDevTestConfig, IsOverride):
    dataset_id: str = chz.field(default="data.datasets.swe.eval.p0_jsonl.webdev_playwright_comment_reply_with_screenshot")
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: P0VerifiedGraderPadawan(graders=make_webdev_playwright_comment_reply_with_screenshot())
    )

def make_webdev_playwright_comment_reply_without_screenshot() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.p0_test.grader.p0_test_grader:grade_fn_webdev_playwright_comment_reply_without_screenshot",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

@chz.chz(typecheck=True)
class P0WebDevPlaywrightCommentReplyWithoutScreenshotDatasetConfigPadawan(P0WebDevTestConfig, IsOverride):
    dataset_id: str = chz.field(default="data.datasets.swe.eval.p0_jsonl.webdev_playwright_comment_reply_without_screenshot")
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: P0VerifiedGraderPadawan(graders=make_webdev_playwright_comment_reply_without_screenshot())
    )