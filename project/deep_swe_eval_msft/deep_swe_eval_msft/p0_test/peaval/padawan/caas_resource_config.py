import dataclasses
import logging
import os
import uuid
from datetime import datetime

import structlog
import tenacity
import shlex
from textwrap import dedent

import caas
import chz
from caas.api import caas_api
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from deep_swe_eval_msft.p0_test.preparedness.p0_metadata import P0Metadata
from qstar.common import datapoint
from qstar.common.tools.berry_tool_interface import ResourceContinuationError
from qstar.common.tools.caas_container.caas_container_tool import CaasContainerResourceConfig
from deep_swe_eval_msft.tools.utils import CAAS_ENDPOINT
from deep_swe_eval_msft.data_converter.repo_setup import CAAS_CUSTOM_IMAGE, CAAS_WEBDEV_CUSTOM_IMAGE, make_repo_script_list, to_sandbox_runtime
from caas.commands import DownloadFileFromContainer, Exec, PythonProgram, UploadFile, RawExec

from caas.protocol import NetworkMode
from caas.protocol import VolumeMount
from deep_swe_msft.tools.utils import PDW_MNT

logger = structlog.get_logger(component=__name__)


def log_with_timestamp(message: str, instance_id: str = None):
    """Log messages with timestamp to a local file for debugging"""
    log_file = "/var/log/supervisor/p0_test_setup.log"
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    with open(log_file, "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        instance_id_str = f"[{instance_id}]" if instance_id else ""
        f.write(f"[{timestamp}]{instance_id_str}: {message}\n")


@chz.chz(typecheck=True)
class P0TestCaasContainerResourceConfig(CaasContainerResourceConfig):
    """
    Allows per-datapoint control over the image, resource limits, and network access.
    (As a result, sandbox=True must be enabled)
    """

    caas_endpoint: str = CAAS_ENDPOINT
    caas_idle_ttl: int = 1200
    use_terminal_server: bool = True
    use_webdev_image: bool = False

    async def _initialize_resource_from_state_metadata(
        self, metadata: P0Metadata, caas_session_state: str
    ) -> CaasContainer:
        try:
            caas_image = CAAS_CUSTOM_IMAGE if not self.use_webdev_image else CAAS_WEBDEV_CUSTOM_IMAGE
            container = CaasContainer(
                caas_endpoint=self.caas_endpoint,
                image_name=caas_image,
                caas_session_state=caas_session_state,
                is_owner=False,
                user=self.user,
            )
            await container.send_heartbeat()
            return container
        except (
            ValueError,
            caas.NotFoundError,
            caas.ServerError,
            caas.TransportError,
            caas.ClientError,
        ) as e:
            raise ResourceContinuationError(
                resource_name=self.name(),
                resource_state=caas_session_state.encode(),
                message=(
                    f"Failed to initialize caas container '{self.name()}' from session state "
                    f"'{caas_session_state}': {e}"
                ),
            ) from e

    @tenacity.retry(
        retry=tenacity.retry_if_exception_type(
            (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception, RuntimeError)
        ),
        wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
        stop=tenacity.stop_after_attempt(10),
        before_sleep=tenacity.before_sleep_log(
            logger,  # type: ignore
            logging.WARNING,
        ),
    )
    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        # Construct P0Metadata from raw metadata like in setup.py and grader
        metadata = P0Metadata.model_validate(dp.metadata)
        
        if self.caas_session_state is not None:
            # We are not the owner of the container, so we don't need to run the setup function.
            resource = await self._initialize_resource_from_state_metadata(
                metadata, self.caas_session_state
            )
            assert not resource.is_owner
            return resource

        if self.use_terminal_server:
            cmd = ["/server.py"]
        else:
            cmd = []

        caas = caas_api(endpoint=self.caas_endpoint)
        caas_image = CAAS_CUSTOM_IMAGE if not self.use_webdev_image else CAAS_WEBDEV_CUSTOM_IMAGE
        
        caas_session = await caas.new_session(
            image=caas_image,
            cmd=cmd,
            cpu_limit="50",
            memory_limit="60g",
            idle_ttl=self.caas_idle_ttl,
            num_gpus=self.caas_num_gpus,
            network=NetworkMode.CAAS_DEFAULT, #CAAS_DEFAULT,
            sandbox_runtime=to_sandbox_runtime("unsafe"),
            pids_limit=1024, # for dongdong's ml data evaluation, we need to set a higher pids limit
            timeout=1200,
            volume_mounts=PDW_MNT,
        )
        
        terminal_session = TerminalSession(caas_session)

        try:
            # download repo here

            if self.setup_fn is not None:
                await self.setup_fn(
                    datapoint=dataclasses.asdict(dp),
                    terminal_session=terminal_session,
                )
            
            # Create new branch and initial commit
            TIMEOUT = 300  # 5 minutes timeout
            checkout_new_branch = True  # You can make this configurable if needed
            
            datapoint_dict = dataclasses.asdict(dp)
            instance_id = datapoint_dict.get("unique_id", "unknown")
            repo_root = f"/tmp/{instance_id}"
            
            branchName = f'model-branch-{uuid.uuid4().hex[:8]}'  # Unique branch name
            log_with_timestamp(f"Init CAAS with image: {caas_image}", instance_id)
            log_with_timestamp(f"Creating new branch: {branchName}", instance_id)
            
            # Force create/switch to the branch (handles existing branch case)
            # Remove any existing .git directory and reinitialize git
            # Output git status for debugging before reinitializing git
            mirror_setup_cmd = dedent(r"""
                git config --global url.$CAAS_GIT_MIRROR_URL/github.com/.insteadOf https://github.com/
                """).strip()
            r = await terminal_session.session.run(RawExec(["bash", "-lc", mirror_setup_cmd]))
                
            _, git_status_output = await terminal_session.session.run(
                RawExec(['bash', '-c', 'git status || echo "No git repo found"'], timeout=TIMEOUT, workdir=repo_root, env=None)
            )
            log_with_timestamp(f"Git status before reinit:\n{git_status_output.decode('utf-8')}", instance_id)
            await terminal_session.session.run(
                RawExec(['bash', '-c', 'rm -rf .git && git init'], timeout=TIMEOUT, workdir=repo_root, env=None)
            )
            await terminal_session.session.run(
                RawExec(['bash', '-c', f'git checkout -B {branchName}'], timeout=TIMEOUT, workdir=repo_root, env=None)
            )
            await terminal_session.session.run(RawExec(['bash', '-c', f'git add .'], timeout=TIMEOUT, workdir=repo_root, env=None))
            await terminal_session.session.run(RawExec(['bash', '-c', f'git commit --allow-empty -m "New Initial Commit"'], timeout=TIMEOUT, workdir=repo_root, env=None))
            
            # Get the new commit ID
            _, commit_output = await terminal_session.session.run(RawExec(['bash', '-c', 'git rev-parse HEAD'], timeout=TIMEOUT, workdir=repo_root, env=None))
            new_commit_id = commit_output.decode('utf-8').strip()
            _, git_status_output = await terminal_session.session.run(
                RawExec(['bash', '-c', 'git status || echo "No git repo found"'], timeout=TIMEOUT, workdir=repo_root, env=None)
            )
            log_with_timestamp(f"Git status after reinit:\n{git_status_output.decode('utf-8')}", instance_id)
            
            # Update the commit ID in the datapoint
            if "metadata" in datapoint_dict and datapoint_dict["metadata"]:
                datapoint_dict["metadata"]["commit"] = new_commit_id
                log_with_timestamp(f"Updated datapoint metadata commit to: {new_commit_id}", instance_id)
            
            # Update the metadata object as well
            if hasattr(metadata, 'commit'):
                metadata.commit = new_commit_id
                dp.metadata["commit"] = new_commit_id
                log_with_timestamp(f"Updated metadata commit to: {new_commit_id}", instance_id)
            
            log_with_timestamp(f"Successfully created branch {branchName} and made initial commit {new_commit_id}", instance_id)
            
        except RuntimeError as e:
            with open("/var/log/supervisor/p0_test_setup.log", "a") as f:
                f.write(f"{e}\n")
                f.write(f"P0 Task: {metadata.task.name}, Repo: {metadata.task.repo}\n")
                
            await caas_session.close()
            raise
        except Exception as e:
            with open("/var/log/supervisor/p0_test_setup.log", "a") as f:
                f.write(f"{e}\n")
                f.write(f"P0 Task: {metadata.task.name}, Repo: {metadata.task.repo}\n")
            await caas_session.close()
            raise
            
        return CaasContainer(
            caas_endpoint=self.caas_endpoint,
            image_name=caas_image,
            caas_session_state=caas_session.save(),
            user=self.user,
            is_owner=True,
        )
