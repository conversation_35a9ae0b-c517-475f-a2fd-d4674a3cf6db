import os
import shlex
import uuid
from typing import Any
from functools import cache
from datetime import datetime

import structlog
import blobfile as bf

import chz
import caas
from caas.commands import BashScript, UploadFile, RawExec
from caas.terminal.api import TerminalSession
from caas_utils import get_strawberry_ace_token
from caas_utils.utils import run_with_retries
from abc import ABC, abstractmethod
from deep_swe_eval_msft.p0_test.preparedness.p0_metadata import P0Metadata #, P0TaskData
from sciclone_utils.operations import sync_from_canonical_source

from deep_swe_msft.tools.get_coreutils import setup_coreutils as setup_fn_padawan_terminal_session

logger = structlog.get_logger(component=__name__)

def log_with_timestamp(message: str, instance_id: str = None):
    """Log messages with timestamp to a local file for debugging"""
    log_file = "/var/log/supervisor/p0_test_setup.log"
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    with open(log_file, "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        instance_id_str = f"[{instance_id}]" if instance_id else ""
        f.write(f"[{timestamp}]{instance_id_str}: {message}\n")

# Mapping of dataset names (from dataset_id) to their corresponding tarball files
DATASET_TARBALL_MAPPING = {
    "can_address_full_test": "can_address_full_test.tar",
    "can_create_a_test": "can_create_a_test.tar", 
    "does_call_str_replace": "does_call_str_replace.tar",
    "lukehoban_full_function": "lukehoban_full_function_impl.tar",
    "promptly_start_fre_repo": "promptly_start_fre_repo.tar",
    "webdev_playwright_auth_bug": "webdev_auth_bug.tar",
    "webdev_playwright_comment_reply_with_screenshot": "webdev_comments_screenshots.tar",
    "webdev_playwright_comment_reply_without_screenshot": "webdev_comments_wo_screenshots.tar",
    "webdev_react_scaffolding": "webdev_react_scaffold.tar",
}

# Fallback mapping of metadata names to dataset names
METADATA_NAME_MAPPING = {
    "can address full test generation requested": "can_address_full_test",
    "can create a test in existing tests if test suite is present": "can_create_a_test",
    "does call str_replace to patch, not create": "does_call_str_replace", 
    "lukehoban full calclang function implementation": "lukehoban_full_function",
    "promptly starts work in a fresh repo": "promptly_start_fre_repo",
    "Playwright Auth Bug Test": "webdev_playwright_auth_bug",
    "Playwright Comment Reply With Screenshot Test": "webdev_playwright_comment_reply_with_screenshot",
    "Playwright Comment Reply Without Screenshot Test": "webdev_playwright_comment_reply_without_screenshot",
    "React Scaffolding Test": "webdev_react_scaffolding",
}

def extract_dataset_name_from_id(dataset_id: str) -> str | None:
    """
    Extract dataset name from dataset_id.
    e.g., "data.datasets.swe.eval.p0_jsonl.can_address_full_test" -> "can_address_full_test"
    """
    if not dataset_id:
        return None
    parts = dataset_id.split('.')
    return parts[-1] if parts else None

def detect_dataset_name_from_datapoint(datapoint: dict[str, Any]) -> str | None:
    """
    Detect dataset name from various sources in the datapoint.
    """
    # Method 1: Look in metadata for the name and try exact match first
    if "metadata" in datapoint and datapoint["metadata"]:
        metadata_name = datapoint["metadata"].get("name", "").lower()
        
        # Try exact match first
        if metadata_name in METADATA_NAME_MAPPING:
            return METADATA_NAME_MAPPING[metadata_name]
        
        # Try fuzzy matching
        for key, dataset_name in METADATA_NAME_MAPPING.items():
            if key.lower() in metadata_name or metadata_name in key.lower():
                return dataset_name
    
    # Method 2: Try to infer from unique_id
    unique_id = datapoint.get("unique_id", "")
    if unique_id:
        unique_id_clean = unique_id.replace("-", "").replace("_", "").lower()
        for dataset_key in DATASET_TARBALL_MAPPING.keys():
            dataset_key_clean = dataset_key.replace("_", "").lower()
            if dataset_key_clean in unique_id_clean:
                return dataset_key
    
    # Method 3: Look for special indicators in problem text
    problem_text = datapoint.get("problem", "").lower()
    if "test" in problem_text and "coverage" in problem_text:
        return "can_address_full_test"
    elif "str_replace" in problem_text:
        return "does_call_str_replace"
    elif "lukehoban" in problem_text or "golang" in problem_text:
        return "lukehoban_full_function"
    elif "create" in problem_text and "test" in problem_text:
        return "can_create_a_test"
    
    return None

@cache
def get_p0_tarball_bytes(dataset_name: str) -> bytes | None:
    """
    Retrieve tarball bytes for a given dataset name.
    Returns None if the dataset doesn't have a corresponding tarball.
    """
    if dataset_name not in DATASET_TARBALL_MAPPING:
        return None
    
    tarball_filename = DATASET_TARBALL_MAPPING[dataset_name]
    uri = f"az://orngscuscresco/data/datasets/swe/eval/p0_test/tar_ball/{tarball_filename}"
    
    try:
        log_with_timestamp(f"Retrieving tarball for {dataset_name} from {uri}")
        return bf.read_bytes(sync_from_canonical_source(uri))
    except Exception as e:
        logger.warning(f"Failed to retrieve tarball for {dataset_name}: {e}")
        return None

async def setup_p0_tarball(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
    workdir: str = None
) -> bool:
    """
    Download and extract the appropriate tarball for the P0 test dataset.
    Returns True if successful, False if no tarball is needed or if it fails.
    """
    instance_id = datapoint.get("unique_id", "unknown")
    
    # Set workdir to /tmp/{unique_id} if not provided
    if workdir is None:
        workdir = f"/tmp/{instance_id}"
    
    # Use the improved detection function
    dataset_name = detect_dataset_name_from_datapoint(datapoint)
    
    if not dataset_name:
        log_with_timestamp(f"No dataset name could be determined from datapoint, skipping tarball setup", instance_id)
        return False
    
    log_with_timestamp(f"Detected dataset name: {dataset_name} for tarball setup", instance_id)
    
    # Get tarball bytes
    tarball_bytes = get_p0_tarball_bytes(dataset_name)
    if tarball_bytes is None:
        log_with_timestamp(f"No tarball available for dataset: {dataset_name}", instance_id)
        return False
    
    try:
        tarball_filename = DATASET_TARBALL_MAPPING[dataset_name]
        tarball_path = f"/tmp/{tarball_filename}"
        
        log_with_timestamp(f"Uploading tarball: {tarball_filename} ({len(tarball_bytes)} bytes)", instance_id)
        
        # Upload tarball to container
        await terminal_session.session.run(
            UploadFile(tarball_path, tarball_bytes)
        )
        
        log_with_timestamp(f"Successfully uploaded tarball: {tarball_filename}", instance_id)
        
        # Extract tarball to workdir
        _, output = await terminal_session.session.run(
            RawExec(
                cmd=[
                    "bash", "-c", 
                    f"set -x && mkdir -p {workdir} && tar -xf {tarball_path} -C {workdir}"
                ],
                timeout=300,  # 5 minutes
                workdir="/",
            ),
        )
        
        log_with_timestamp(f"Successfully extracted tarball to {workdir}. Output: {output.decode('utf-8')[:500]}", instance_id)
        
        # List contents of workdir for debugging
        _, ls_output = await terminal_session.session.run(
            RawExec(
                cmd=["ls", "-la", workdir],
                timeout=60,
                workdir="/",
            ),
        )
        
        log_with_timestamp(f"Contents of {workdir}:\n{ls_output.decode('utf-8')}", instance_id)
        
        return True
        
    except Exception as e:
        import traceback
        log_with_timestamp(f"Failed to setup tarball for {dataset_name}: {e}", instance_id)
        log_with_timestamp(f"Traceback:\n{traceback.format_exc()}", instance_id)
        return False

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def p0_test_setup_fn(
    *,
    datapoint: dict[str, Any],
    #metadata: SweBenchMetadata,
    terminal_session: TerminalSession,
) -> None:
    instance_id = datapoint.get("unique_id", "unknown")
    log_with_timestamp(f"Starting P0 test setup", instance_id)
    
    metadata = P0Metadata.model_validate(datapoint["metadata"])
    try:
        terminal_session.session.start_keepalive_task(keepalive_interval=60)
        
        # Setup tarball first
        tarball_success = await setup_p0_tarball(
            datapoint=datapoint,
            terminal_session=terminal_session,
        )
        
        log_with_timestamp(f"Tarball setup result: {tarball_success}", instance_id)
        
        await p0_test_setup_fn_internal(
            datapoint=datapoint, terminal_session=terminal_session, metadata=metadata
        )
        
        log_with_timestamp(f"P0 test setup completed successfully", instance_id)
        
    except Exception as e:
        import traceback
        log_with_timestamp(f"P0 test setup failed: {e}", instance_id)
        log_with_timestamp(f"Traceback:\n{traceback.format_exc()}", instance_id)
        raise
    finally:
        await terminal_session.session.stop_keepalive_task()

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def p0_test_setup_fn_internal(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
    metadata: P0Metadata,
) -> None:
    instance_id = datapoint.get("unique_id", "unknown")
    repo_root = f"/tmp/{instance_id}"
    
    # For P0 tests, we need more flexible setup since we don't have SweBenchTaskData necessarily
    post_setup = [
        f"mkdir -p {repo_root}",
        f"echo 'cd {repo_root}' >> /root/.bashrc",
        "git config --global user.name User", 
        "git config --global user.email user@localhost",
        "git config --global --add safe.directory '*'",
    ]
    log_with_timestamp(f"Running post-setup commands", instance_id)
    r = await terminal_session.session.run(BashScript("\n".join(post_setup), login=True, timeout=900))
    
    # Use the new standardized setup approach
    log_with_timestamp(f"Running padawan terminal session setup", instance_id)
    await setup_fn_padawan_terminal_session(
            terminal_session.session,
            {},
            repo_root=repo_root,
            login=False,
            install_node=False,
            add_package_json=True,
            mix_vsc_tool=False,
            skip_tool_packages=True,
            checkout_new_branch=False,
        )
    
    log_with_timestamp(f"Internal setup completed", instance_id)



# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def p0_webdev_setup_fn(
    *,
    datapoint: dict[str, Any],
    #metadata: SweBenchMetadata,
    terminal_session: TerminalSession,
) -> None:
    instance_id = datapoint.get("unique_id", "unknown")
    log_with_timestamp(f"Starting P0 test setup", instance_id)
    
    metadata = P0Metadata.model_validate(datapoint["metadata"])
    from deep_swe_msft.webdev_padawan_v2.datasets.setup import webdev_setup_fn_with_mcp_tools
    from deep_swe_msft.webdev_padawan_v2.datasets.mcp import start_mcp_server, send_mcp_request_to_server, test_mcp_server, create_html

    _ = await webdev_setup_fn_with_mcp_tools(datapoint=datapoint, terminal_session=terminal_session)

    try:
        terminal_session.session.start_keepalive_task(keepalive_interval=60)
        
        # Start the long-running MCP server
        server_result = await start_mcp_server(
            terminal_session=terminal_session,
            output_dir='/root/screenshots',
        )
        if "error" in server_result:
            raise RuntimeError(f"Failed to start MCP server: {server_result['error']}")
        
        log_with_timestamp(f"MCP server started successfully: {server_result}")

        # Setup tarball first
        tarball_success = await setup_p0_tarball(
            datapoint=datapoint,
            terminal_session=terminal_session,
        )
        
        log_with_timestamp(f"Tarball setup result: {tarball_success}", instance_id)
        
        await p0_test_setup_fn_internal(
            datapoint=datapoint, terminal_session=terminal_session, metadata=metadata
        )
        
        log_with_timestamp(f"P0 test setup completed successfully", instance_id)
        
    except Exception as e:
        import traceback
        log_with_timestamp(f"P0 test setup failed: {e}", instance_id)
        log_with_timestamp(f"Traceback:\n{traceback.format_exc()}", instance_id)
        raise
    finally:
        await terminal_session.session.stop_keepalive_task()    

