from typing import Any

import chz
from berry import preset_utils
from chz_presets.core import CompositePreset, Preset
from deep_swe.datasets.config_utils import chz_path
from deep_swe_eval_msft.data_converter.data_converter import get_vardisc_args
import deep_swe_eval_msft.p0_test.peaval.padawan.dataset_configs as configs

PADAWAN_DATASET_CONFIGS = [
    (configs.P0CanAddressFullTestDatasetConfigPadawan, []),
    (configs.P0CanCreateATestDatasetConfigPadawan, []),
    (configs.P0DoesCallStrReplaceDatasetConfigPadawan, []),
    (configs.P0LukehobanFullFunctionDatasetConfigPadawan, []),
    (configs.P0PromptlyStartFreRepoDatasetConfigPadawan, []),
]

PADAWAN_WEBDEV_DATASET_CONFIGS = [
    (configs.P0WebDevReactScaffoldingDatasetConfigPadawan, []),
    (configs.P0WebDevPlaywrightAuthBugDatasetConfigPadawan, []),
    (configs.P0WebDevPlaywrightCommentReplyWithScreenshotDatasetConfigPadawan, []),
    (configs.P0WebDevPlaywrightCommentReplyWithoutScreenshotDatasetConfigPadawan, []),
]


def padawan_p0_tests(
    juice: int | tuple[int, ...] = 128,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
) -> CompositePreset[Preset[Any]]:
    return preset_utils.compose_presets(
        *[
            preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    *get_vardisc_args(juice),
                    "dataset.override_target_samples_per_instance=4",
                ]
            )
            for dataset_config, args in PADAWAN_DATASET_CONFIGS
        ],
    )

def padawan_p0_webdev(
    juice: int | tuple[int, ...] = 128,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
) -> CompositePreset[Preset[Any]]:
    return preset_utils.compose_presets(
        *[
            preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    *get_vardisc_args(juice),
                    "dataset.override_target_samples_per_instance=4",
                ]
            )
            for dataset_config, args in PADAWAN_WEBDEV_DATASET_CONFIGS
        ],
    )


padawan_p0_tests = padawan_p0_tests()
padawan_p0_webdev = padawan_p0_webdev()
