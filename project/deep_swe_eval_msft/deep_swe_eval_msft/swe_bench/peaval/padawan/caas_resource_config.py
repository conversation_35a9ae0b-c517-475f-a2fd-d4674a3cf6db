import dataclasses
import logging

import structlog
import tenacity
import shlex
from textwrap import dedent

import caas
import chz
from caas.api import caas_api
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from deep_swe_eval_msft.swe_bench.preparedness.swe_bench_metadata import SweBenchMetadata
from qstar.common import datapoint
from qstar.common.tools.berry_tool_interface import ResourceContinuationError
from qstar.common.tools.caas_container.caas_container_tool import CaasContainerResourceConfig
from deep_swe_eval_msft.tools.utils import CAAS_ENDPOINT
from deep_swe_eval_msft.data_converter.repo_setup import CAAS_CUSTOM_IMAGE, make_repo_script_list, to_sandbox_runtime
from caas.commands import DownloadFileFromContainer, Exec, PythonProgram, UploadFile, RawExec, DownloadFromBlobstore
from caas.protocol import Tmpfs
from deep_swe_msft.tools.utils import get_strawberry_ace_token

from caas.protocol import NetworkMode
from caas.protocol import VolumeMount
from deep_swe_msft.tools.utils import PDW_MNT

logger = structlog.get_logger(component=__name__)


@chz.chz(typecheck=True)
class DeepSWECaasContainerResourceConfig(CaasContainerResourceConfig):
    """
    Allows per-datapoint control over the image, resource limits, and network access.
    (As a result, sandbox=True must be enabled)
    """

    caas_endpoint: str = CAAS_ENDPOINT
    caas_idle_ttl: int = 1200
    use_terminal_server: bool = True
    use_custom_image: bool = False
    use_tarball: bool = True
    skip_tool_packages: bool = False

    # def name(self) -> str:
    #     # TODO this is kind of sus. Look at what this PR is saying https://github.com/openai/garden/pull/19818/files
    #     # totoally sus comment by swang
    #     return "caas_container"
    #     #return _caas_container_resource_name(
    #     #    caas_network=None, enable_network_after_setup=not self.force_disable_internet
    #     #)

    async def _initialize_resource_from_state_metadata(
        self, metadata: SweBenchMetadata, caas_session_state: str
    ) -> CaasContainer:
        try:
            caas_image = metadata.docker_image if not self.use_custom_image else CAAS_CUSTOM_IMAGE
            container = CaasContainer(
                caas_endpoint=self.caas_endpoint,
                image_name=caas_image,
                caas_session_state=caas_session_state,
                is_owner=False,
                user=self.user,
            )
            await container.send_heartbeat()
            return container
        except (
            ValueError,
            caas.NotFoundError,
            caas.ServerError,
            caas.TransportError,
            caas.ClientError,
        ) as e:
            raise ResourceContinuationError(
                resource_name=self.name(),
                resource_state=caas_session_state.encode(),
                message=(
                    f"Failed to initialize caas container '{self.name()}' from session state "
                    f"'{caas_session_state}': {e}"
                ),
            ) from e

    @tenacity.retry(
        retry=tenacity.retry_if_exception_type(
            (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception, RuntimeError)
        ),
        wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
        stop=tenacity.stop_after_attempt(10),
        before_sleep=tenacity.before_sleep_log(
            logger,  # type: ignore
            logging.WARNING,
        ),
    )
    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        metadata = SweBenchMetadata.model_validate(dp.metadata)

        if self.caas_session_state is not None:
            # We are not the owner of the container, so we don't need to run the setup function.
            resource = await self._initialize_resource_from_state_metadata(
                metadata, self.caas_session_state
            )
            assert not resource.is_owner
            return resource

        if self.use_terminal_server:
            cmd = ["/server.py"]
        else:
            cmd = []

        caas = caas_api(endpoint=self.caas_endpoint)
        nid = metadata.task.instance_id.replace('__', '_').replace('-','_')

        caas_image = metadata.docker_image if not self.use_custom_image else CAAS_CUSTOM_IMAGE
        if self.use_custom_image:
            if self.use_tarball:
                
                tmpfs = [Tmpfs(container=metadata.cwd, size="40g")]
                caas_session = await caas.new_session(
                    image=CAAS_CUSTOM_IMAGE,
                    cmd=cmd,
                    cpu_limit="50",
                    memory_limit="60g",
                    idle_ttl=self.caas_idle_ttl,
                    num_gpus=self.caas_num_gpus,
                    network=NetworkMode.CAAS_DEFAULT, #CAAS_DEFAULT,
                    sandbox_runtime=to_sandbox_runtime("unsafe"),
                    pids_limit=1024, # for dongdong's ml data evaluation, we need to set a higher pids limit
                    timeout=1200,
                    volume_mounts=PDW_MNT,
                    #tmpfs=tmpfs
                )
            else:
                caas_session = await caas.new_session(
                    image=CAAS_CUSTOM_IMAGE,
                    cmd=cmd,
                    cpu_limit="50",
                    memory_limit="60g",
                    idle_ttl=self.caas_idle_ttl,
                    num_gpus=self.caas_num_gpus,
                    network=NetworkMode.CAAS_DEFAULT, #CAAS_DEFAULT,
                    sandbox_runtime=to_sandbox_runtime("unsafe"),
                    pids_limit=1024, # for dongdong's ml data evaluation, we need to set a higher pids limit
                    timeout=1200,
                    volume_mounts=PDW_MNT,
                )
        else:
            caas_session = await caas.new_session(
                image=metadata.docker_image,
                cmd=cmd,
                cpu_limit="50", #metadata.limits.cpu,
                memory_limit="50g", #metadata.limits.memory,
                idle_ttl=self.caas_idle_ttl,
                num_gpus=self.caas_num_gpus,
                network=NetworkMode.CAAS_DEFAULT, #CAAS_DEFAULT,
                #network=NetworkMode.CAAS_PUBLIC_ONLY,
                timeout=1200,
                volume_mounts=PDW_MNT,
            )
        terminal_session = TerminalSession(caas_session)

        try:
            if self.use_custom_image:
                if not self.use_tarball:
                    mirror_setup_cmd = dedent(r"""
                    git config --global url.$CAAS_GIT_MIRROR_URL/github.com/.insteadOf https://github.com/
                    """).strip()
                    exit_code, output = await caas_session.run(RawExec(["bash", "-lc", mirror_setup_cmd]))
                    if exit_code != 0:
                        raise RuntimeError(f"Command failed: {mirror_setup_cmd} Output: {output.decode()}")
                
                    exit_code, output = await caas_session.run(RawExec(shlex.split(f"mkdir -p {metadata.cwd}"), timeout=120, workdir="/"))
                    if exit_code != 0:
                        raise RuntimeError(f"Command mkdir {metadata.cwd} Output: {output.decode()}")
                    
                    # if use ci image no tarball, commit is original_base_commit when build image
                    # build tarball will refresh this commit and saved into base_commit
                    if "original_base_commit" in dp.metadata["task"]:
                        dp.metadata["task"]["base_commit"] = dp.metadata["task"]["original_base_commit"]
                        metadata.task.base_commit = dp.metadata["task"]["original_base_commit"]
                        logger.info(f"In ci no tarball, using {dp.metadata['task']['original_base_commit']} to replace base commit {dp.metadata['task']['base_commit']} for {dp.metadata['task']['repo']} in {dp.metadata['task']['instance_id']}")

                
                    commands = make_repo_script_list(metadata.task.repo, metadata.cwd, metadata.task.base_commit)
                    for command, workdir in commands:
                        exit_code, output = await caas_session.run(RawExec(shlex.split(command), timeout=1200, workdir=workdir))
                        logger.info(f"Running command in caas container {exit_code} {output.decode()}")
                        if exit_code != 0:
                            raise RuntimeError(f"Command failed: {exit_code} {command} Output: {output.decode()}")

                else:
                    mirror_setup_cmd = dedent(r"""
                    git config --global url.$CAAS_GIT_MIRROR_URL/github.com/.insteadOf https://github.com/
                    """).strip()
                    exit_code, output = await caas_session.run(RawExec(["bash", "-lc", mirror_setup_cmd]))
                    if exit_code != 0:
                        raise RuntimeError(f"Command failed: {mirror_setup_cmd} Output: {output.decode()}")
                    
                    exit_code, output = await caas_session.run(RawExec(shlex.split(f"mkdir -p {metadata.cwd}"), timeout=120, workdir="/"))
                    if exit_code != 0:
                        raise RuntimeError(f"Command mkdir {metadata.cwd} Output: {output.decode()}")
                    
                    url = f"https://orngcaas.blob.core.windows.net/data/datasets/swe/eval/swe_bench/tarball/{nid}.tar"
                    local_file = f"{metadata.cwd}/{nid}.tar"

                    STRAWBERRYACE_TOKEN = get_strawberry_ace_token()
                    cmd = f'curl -k -sL "{url}?{STRAWBERRYACE_TOKEN}" -o "{local_file}"'

                    exit_code, output = await caas_session.run(
                        RawExec(["bash", "-c", cmd], timeout=600)  # adjust timeout as needed
                    )
                    if exit_code != 0:
                        raise RuntimeError(f"Command failed: {exit_code} {cmd} Output: {output.decode()}")
                    
                    # logger.info(f"Downloading from {url} to {local_file}")
                    # await caas_session.run(
                    #     DownloadFromBlobstore(url, local_file)
                    # )
                    
                    exit_code, output = await caas_session.run(RawExec(["bash", "-c", f'''
set -e
set -x
mkdir -p {metadata.cwd} # create wdir if not using tmpfs
cd {metadata.cwd}
tar -xf {local_file} -C .
rm {local_file}
ls -la
du -sh .
git config --global --add safe.directory {metadata.cwd}
git log
'''], timeout=60))
                    
                    logger.info(f"Exit Code: {exit_code} Output: {output.decode()}")
                    if exit_code != 0:
                        raise RuntimeError(f"Command failed: {exit_code} tar -xf {local_file} Output: {output.decode()}")
                    
                    command = f"chmod -R 777 {metadata.cwd}"
                    exit_code, output = await caas_session.run(RawExec(shlex.split(command), timeout=1200, workdir="/"))
                    logger.info(f"Running command in caas container {output.decode()}")
                    
                    if exit_code != 0:
                        raise RuntimeError(f"Command failed: {exit_code} {command} Output: {output.decode()}")
            else:
                # if use swe image, commit is original_base_commit when build image
                # build tarball will refresh this commit and saved into base_commit
                if "original_base_commit" in dp.metadata["task"]:
                    dp.metadata["task"]["base_commit"] = dp.metadata["task"]["original_base_commit"]
                    metadata.task.base_commit = dp.metadata["task"]["original_base_commit"]
                    logger.info(f"In swe, using {dp.metadata['task']['original_base_commit']} to replace base commit {dp.metadata['task']['base_commit']} for {dp.metadata['task']['repo']} in {dp.metadata['task']['instance_id']}")

            if self.setup_fn is not None:
                await self.setup_fn(
                    datapoint=dataclasses.asdict(dp),
                    terminal_session=terminal_session,
                    skip_tool_packages=self.skip_tool_packages,
                )

            # swang enable network for eval
            #if not metadata.allow_internet:
            #    await caas_session.update_network(enable_network=False)
        except RuntimeError as e:
            with open("/var/log/supervisor/zhendongw_aswe_setup_error.log", "a") as f:
                f.write(f"{e}\n")
                f.write(f"Metadata: {metadata.task.instance_id}\n")
            await caas_session.close()
            raise
        except Exception as e:
            with open("/var/log/supervisor/zhendongw_aswe_setup_error.log", "a") as f:
                f.write(f"{e}\n")
            await caas_session.close()
            raise
            
        return CaasContainer(
            caas_endpoint=self.caas_endpoint,
            image_name=caas_image,
            caas_session_state=caas_session.save(),
            user=self.user,
            is_owner=True,
        )
