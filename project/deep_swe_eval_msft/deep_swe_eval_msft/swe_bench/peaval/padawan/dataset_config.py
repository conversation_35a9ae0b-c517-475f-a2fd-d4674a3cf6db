import chz
from functools import partial
from typing import Any, Sequence

import qstar.instance_completers
import qstar.instance_optimizers

from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import <PERSON><PERSON>tageGrader
from qstar.presets.chz_utils import IsO<PERSON>ride, override
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.curriculums.function_wrapper import FunctionWrapper

from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    GraderService,
    TokenCompleterGraderService,
)

from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from token_completer import TokenCompleter

import caas_autograding.grader as caas_graders
from berry import InstanceCompleter, InstanceOptimizer, SampleAllocator
from qstar.allocators.continuous_rewards_adaptive_sample_allocator import (
    ContinuousRewardsAdaptiveSampleAllocator,
)

from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig 
from deep_swe.datasets import configs as default_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY, PADAWAN_SYSTEM_PROMPT

from deep_swe_eval_msft.swe_bench.peaval.padawan.setup import swe_bench_verfied_setup_fn as swe_bench_verfied_setup_fn_padawan
from deep_swe_eval_msft.swe_bench.peaval.padawan.caas_resource_config import DeepSWECaasContainerResourceConfig as DeepSWECaasContainerResourceConfigPadawan
from deep_swe_eval_msft.graders.eval_unit_test import EvalTermberryGrader

from deep_swe_eval_msft.tools.caas_padawan_tool import EvalPadawanToolConfig
from chat.render.v4.experimental.strawberry.formatter import BerryChannel

CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
CAAS_IDLE_TTL = 1200


def make_swe_bench_verified_graders_padawan() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    # swang seems channels_for_answer is deprecated
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.swe_bench.peaval.padawan.computer_task_grader:grade_fn_v2_padawan",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def make_swe_bench_verified_switch_image_graders_padawan() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    # swang seems channels_for_answer is deprecated
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.swe_bench.peaval.padawan.computer_task_grader:grade_fn_v2_switch_image_padawan",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def _make_tool_configs_padawan(
    container_tool_config: EvalPadawanToolConfig,
    #container_tool_config: PadawanToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
) -> tuple[ToolConfig, ...]:
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)


@chz.chz
class SWEBenchVerifiedGraderPadawan(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(make_swe_bench_verified_graders_padawan)

    @property
    def accepts_invalid(self) -> bool:
        """
        Generally, computer using tasks can be graded at any point in time.
        """
        return True

@chz.chz(typecheck=True)
class SWEBenchVerifiedDatasetConfigPadawan(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = (
        # deprecated
        #"data.oaici.qstar.preparedness.datasets.swe_bench.all_verified_converted_vsc"
        #"data.oaici.qstar.preparedness.datasets.swe_bench.astropy_vsc"
        # vsc filtered, suggestion
        #"data.oaici.qstar.preparedness.datasets.swe_bench.astropy_vsc"
        #"data.datasets.swe.eval.swe_bench.sympy"
        # "data.datasets.swe.eval.swe_bench.all_verified_converted490_vsc"
        "data.datasets.swe.eval.swe_bench.swe_image_490_tb"
        #"data.oaici.qstar.preparedness.datasets.swe_bench.ttt2_vsc"
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: SWEBenchVerifiedGraderPadawan(graders=make_swe_bench_verified_graders_padawan())
    )
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs_padawan)
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigPadawan(setup_fn=swe_bench_verfied_setup_fn_padawan,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL,
                                               use_custom_image=False,
                                               skip_tool_packages=False),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter("deep_swe_eval_msft.swe_bench.preparedness.conversation_init:conversation_init_ms_bench_fn_padawan"),
        )
    )
    
    model_identity_str: str = PADAWAN_MODEL_IDENTITY
    instructions: str = PADAWAN_SYSTEM_PROMPT
    # swang align sampling with system message
    #max_tokens: int | None = 262144
    #max_num_yields: int | None = 100

@chz.chz
class CActionSWEGraderDatasetConfigPadawan(SWEBenchVerifiedDatasetConfigPadawan, IsOverride):
    dataset_id: str = (
        "data.datasets.swe.eval.swe_bench.prod_image_swe_grader_490_tb"
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigPadawan(setup_fn=swe_bench_verfied_setup_fn_padawan,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL,
                                               use_custom_image=True,
                                               skip_tool_packages=True),
        )
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: SWEBenchVerifiedGraderPadawan(graders=make_swe_bench_verified_switch_image_graders_padawan())
    )

@chz.chz
class CActionCGraderDatasetConfigPadawan(SWEBenchVerifiedDatasetConfigPadawan, IsOverride):
    dataset_id: str = (
        "data.datasets.swe.eval.swe_bench.prod_image_490_tb"
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigPadawan(setup_fn=swe_bench_verfied_setup_fn_padawan,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL,
                                               use_custom_image=True,
                                               skip_tool_packages=True),
        )
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: SWEBenchVerifiedGraderPadawan(graders=make_swe_bench_verified_graders_padawan())
    )