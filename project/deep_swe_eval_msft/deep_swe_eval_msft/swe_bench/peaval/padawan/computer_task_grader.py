import shlex
from typing import TypedDict
from uuid import uuid4
import tenacity
import logging
import os
import shutil
import time
import json

import structlog

import caas
import berry
from caas import ExecError
from caas.api import caas_api
from caas.commands import Exec, RawBashScript, RawExec, UploadFile
from caas.terminal.api import TerminalSession
from caas_swe_bench_train_v2.parser import ModelPatch
from caas_utils.utils import run_with_retries
from caas.protocol import NetworkMode
from caas_tool.caas_container import CaasContainer

from deep_swe_eval_msft.swe_bench.peaval.padawan.setup import swe_bench_verfied_setup_fn_internal
from deep_swe_eval_msft.swe_bench.preparedness.swe_bench_metadata import SweBenchMetadata, SweBenchTaskData
from deep_swe_eval_msft.tools.utils import CAAS_ENDPOINT
from deep_swe_eval_msft.data_converter.repo_setup import CAAS_CUSTOM_IMAGE, make_repo_script_list, to_sandbox_runtime

from deep_swe_eval_msft.swe_bench.peaval.computer_task_grader_utils import _grade_fn_v2_inner, GradeReport, get_model_patch, PROTECTED_FILES
from deep_swe_eval_msft.swe_bench.peaval.computer_task_grader_utils import get_model_patch_raw, _upload_and_apply

logger = structlog.get_logger(__name__)


@tenacity.retry(
    retry=tenacity.retry_if_exception_type(
        (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception)
    ),
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(5),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
)
async def grade_fn_v2_padawan(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> bool:
    metadata = SweBenchMetadata.model_validate(sample.gt_datapoint.metadata)
    report = await grade_fn_v2_internal_padawan(
        terminal_session=terminal_session,
        metadata=metadata,
        fast=fast,
    )

    logger.info("Grading finished", **report)
    # swang need to fill model_patch
    sample.ephemeral_metadata["model_patch"] = report["model_patch"]
    sample.metadata["grader"] = {
        "prompt": report["model_patch"],
        "response": report["test_output"],
        "score": int(report["passed"]),
    }
    
    return report["passed"]


async def grade_fn_v2_switch_image_padawan(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> bool:
    metadata = SweBenchMetadata.model_validate(sample.gt_datapoint.metadata)
    model_patch, model_patch_raw = await get_model_patch_raw(terminal_session, metadata.task.base_commit, metadata.cwd, PROTECTED_FILES)
    if "original_base_commit" in sample.gt_datapoint.metadata["task"]:
        sample.gt_datapoint.metadata["task"]["base_commit"] = sample.gt_datapoint.metadata["task"]["original_base_commit"]
        metadata.task.base_commit = sample.gt_datapoint.metadata["task"]["original_base_commit"]
    result = await grade_fn_v2_switch_image_internal_padawan(sample=sample, metadata=metadata, model_patch_raw=model_patch_raw, model_patch=model_patch)
    return result

@tenacity.retry(
    retry=tenacity.retry_if_exception_type(
        (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception, TimeoutError, RuntimeError)
    ),
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(5),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
)
async def grade_fn_v2_switch_image_internal_padawan(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    metadata: SweBenchMetadata,
    model_patch_raw: str,
    model_patch: ModelPatch,
) -> GradeReport:
    container1 = await CaasContainer.new(caas_endpoint=CAAS_ENDPOINT,
                                image_name=metadata.docker_image,
                                idle_ttl=1200,
                                memory_limit="16g",
                                cpu_limit="16",
                                network=NetworkMode.CAAS_PUBLIC_ONLY)
    grade_session = container1.terminal_session
    
    #await _upload_and_apply(container1.terminal_session, model_patch.test_patch, "/testbed")
    r = await _upload_and_apply(container1.terminal_session, model_patch_raw, metadata.cwd)

    report = await _grade_fn_v2_inner(
        grade_session=grade_session,
        metadata=metadata,
        model_patch=model_patch,
    )

    logger.info("Grading finished", **report)
    # swang need to fill model_patch
    sample.ephemeral_metadata["model_patch"] = report["model_patch"]
    sample.metadata["grader"] = {
        "prompt": report["model_patch"],
        "response": report["test_output"],
        "score": int(report["passed"]),
    }
    
    return report["passed"]

@tenacity.retry(
    retry=tenacity.retry_if_exception_type(
        (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception, TimeoutError, RuntimeError)
    ),
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(5),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
)
async def grade_fn_v2_internal_padawan(
    *,
    terminal_session: TerminalSession,
    metadata: SweBenchMetadata,
    fast: bool,
) -> GradeReport:
    repo_root = metadata.cwd
    assert isinstance(metadata.task, SweBenchTaskData)

    model_patch = await get_model_patch(
        terminal_session=terminal_session,
        base_commit=metadata.task.base_commit,
        repo_root=repo_root,
        protected_files=PROTECTED_FILES,
    )

    # swang disable this for eval
    # if not model_patch.has_test_and_non_test():
    #     return {
    #         "passed": False,
    #         "test_output": "Model patch does not have both tests/non-tests",
    #         "model_patch": model_patch.compact_patch,
    #         "passed_tests": [],
    #     }

    if fast:
        # Soft reset the repository to the base commit (but don't run setup again)
        # await run_with_retries(
        #     terminal_session,
        #     f"cd {repo_root} && git add -A && git reset --hard {metadata.task.base_commit}",
        #     attempts=3,
        # )

        # hand off the model patch object instead of individual fields.
        return await _grade_fn_v2_inner(terminal_session, metadata, model_patch)

    # use the secure grading path, passing the full model patch object.
    return await _grade_fn_v2_secure_padawan(metadata, model_patch)



async def _grade_fn_v2_secure_padawan(
    metadata: SweBenchMetadata,
    model_patch: ModelPatch,
) -> GradeReport:
    """
    The "safe" route - re-initialize a fresh container and grade in there.
    """
    caas = caas_api(CAAS_ENDPOINT)
    async with caas.use_session(
        image=metadata.docker_image,
        cpu_limit=metadata.limits.cpu,
        memory_limit=metadata.limits.memory,
        network=NetworkMode.CAAS_PUBLIC_ONLY,
        keepalive_interval=30,
        timeout=1200,
    ) as session:
        grade_session = TerminalSession(session)
        await swe_bench_verfied_setup_fn_internal(terminal_session=grade_session, metadata=metadata)

        return await _grade_fn_v2_inner(
            grade_session=grade_session,
            metadata=metadata,
            model_patch=model_patch,
        )
