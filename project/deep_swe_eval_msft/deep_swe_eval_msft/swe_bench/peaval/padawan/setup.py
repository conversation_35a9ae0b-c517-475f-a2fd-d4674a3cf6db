import os
import shlex
from typing import Any

import structlog

import chz
import caas
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas_utils import get_strawberry_ace_token
from caas_utils.utils import run_with_retries
from abc import ABC, abstractmethod
from deep_swe_eval_msft.swe_bench.preparedness.swe_bench_metadata import SweBenchMetadata, SweBenchTaskData

from deep_swe_msft.tools.get_coreutils import setup_coreutils

logger = structlog.get_logger(component=__name__)

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_verfied_setup_fn(
    *,
    datapoint: dict[str, Any],
    #metadata: SweBenchMetadata,
    terminal_session: TerminalSession,
    skip_tool_packages: bool = False,
) -> None:
    metadata = SweBenchMetadata.model_validate(datapoint["metadata"])
    try:
        terminal_session.session.start_keepalive_task(keepalive_interval=60)
        await swe_bench_verfied_setup_fn_internal(terminal_session=terminal_session, metadata=metadata, skip_tool_packages=skip_tool_packages)
    finally:
        await terminal_session.session.stop_keepalive_task()

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_verfied_setup_fn_internal(
    *,
    terminal_session: TerminalSession,
    metadata: SweBenchMetadata,
    skip_tool_packages: bool = False,
) -> None:
    task = metadata.task
    assert isinstance(task, SweBenchTaskData)

    post_setup = [
        f"echo 'cd {metadata.cwd}' >> /root/.bashrc",
        "git config --global user.name User",
        "git config --global user.email user@localhost",
        #"pre-commit install || true",
    ]
    
    r = await terminal_session.session.run(BashScript("\n".join(post_setup), login=True, timeout=900))
    
    await setup_coreutils(
        terminal_session.session, 
        {}, 
        repo_root=metadata.cwd,
        login=False,
        install_node=True if not skip_tool_packages else False,
        add_package_json=True,
        mix_vsc_tool=False,
        skip_tool_packages=skip_tool_packages,
        checkout_new_branch=False,
        )
