from functools import partial
from typing import Any, Sequence

import caas_autograding.grader as caas_graders
import chz
import qstar.instance_completers
import qstar.instance_optimizers
from berry import InstanceCompleter, InstanceOptimizer, SampleAllocator
from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.datasets import configs as default_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig
from deep_swe_eval_msft.graders.eval_unit_test import EvalTermberryGrader
from deep_swe_eval_msft.swe_bench.peaval.vsc.caas_resource_config import (
    DeepSWECaasContainerResourceConfig as DeepSWECaasContainerResourceConfigVSC,
)
from deep_swe_eval_msft.swe_bench.peaval.vsc.setup import (
    swe_bench_verfied_setup_fn as swe_bench_verfied_setup_fn_vsc,
)
from deep_swe_eval_msft.tools.caas_vscode_tool import EvalVSCodeToolConfig
from deep_swe_msft.vsc_data.system_prompt import VSC_MODEL_IDENTITY, VSC_SYSTEM_PROMPT
from qstar.allocators.continuous_rewards_adaptive_sample_allocator import (
    ContinuousRewardsAdaptiveSampleAllocator,
)
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    GraderService,
    TokenCompleterGraderService,
)
from qstar.presets.chz_utils import IsOverride, override
from token_completer import TokenCompleter

CAAS_ENDPOINT = "https://eastus2.caas.azure.com"  # "https://eastus2.caas.azure.com" #"https://southcentralus.caas.azure.com"
CAAS_IDLE_TTL = 1200


def make_swe_bench_verified_graders_vsc() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.swe_bench.peaval.vsc.computer_task_grader:grade_fn_v2_vsc",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,),
        ),
    )


def _make_tool_configs_vsc(
    container_tool_config: EvalVSCodeToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
) -> tuple[ToolConfig, ...]:
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)


@chz.chz
class SWEBenchVerifiedGraderVSC(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(
        make_swe_bench_verified_graders_vsc
    )

    @property
    def accepts_invalid(self) -> bool:
        """
        Generally, computer using tasks can be graded at any point in time.
        """
        return True


@chz.chz(typecheck=True)
class SWEBenchVerifiedDatasetConfigVSC(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = (
        # deprecated
        # "data.oaici.qstar.preparedness.datasets.swe_bench.all_verified_converted_vsc"
        # "data.oaici.qstar.preparedness.datasets.swe_bench.astropy_vsc"
        # vsc filtered, suggestion
        #"data.oaici.qstar.preparedness.datasets.swe_bench.astropy_vsc"
        # "data.datasets.swe.eval.swe_bench.all_verified_converted490_vsc"
        "data.datasets.swe.eval.swe_bench.swe_image_490_tb"
        #"data.oaici.qstar.preparedness.datasets.swe_bench.ttt2_vsc"
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: SWEBenchVerifiedGraderVSC(graders=make_swe_bench_verified_graders_vsc())
    )
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs_vsc)
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigVSC(
                setup_fn=swe_bench_verfied_setup_fn_vsc,
                caas_endpoint=CAAS_ENDPOINT,
                caas_idle_ttl=CAAS_IDLE_TTL,
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                "deep_swe_eval_msft.swe_bench.preparedness.conversation_init:conversation_init_ms_bench_fn_vsc"
            ),
        )
    )

    model_identity_str: str = VSC_MODEL_IDENTITY
    instructions: str = VSC_SYSTEM_PROMPT
    # swang align sampling with system message
    # max_tokens: int | None = 262144
    # max_num_yields: int | None = 100


@chz.chz(typecheck=True)
class SWEBenchVerifiedDatasetConfigVSCPromptFile(SWEBenchVerifiedDatasetConfigVSC, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = "data.datasets.swe.eval.swe_bench.swe_image_490_tb"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            # use prompt_file mode to generate attachment style prompt file instructions
            conversation_converter(
                "deep_swe_eval_msft.swe_bench.preparedness.conversation_init:conversation_init_ms_bench_fn_vsc",
                mode="prompt_file",
            ),
        )
    )

@chz.chz(typecheck=True)
class SWEBenchVerifiedDatasetConfigVSCRewrite(SWEBenchVerifiedDatasetConfigVSC, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = "data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbv_490"
    # override to use rewrite mode (raw problem only in userRequest)
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                "deep_swe_eval_msft.swe_bench.preparedness.conversation_init:conversation_init_ms_bench_fn_vsc",
                mode="rewrite",
            ),
        )
    )

@chz.chz(typecheck=True)
class SWEBenchVerifiedDatasetConfigVSCRewrite2(SWEBenchVerifiedDatasetConfigVSC, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = "data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbv_490_2"
    # second rewrite dataset variant also uses rewrite mode
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                "deep_swe_eval_msft.swe_bench.preparedness.conversation_init:conversation_init_ms_bench_fn_vsc",
                mode="rewrite",
            ),
        )
    )