import shlex
from typing import TypedDict
from uuid import uuid4
import tenacity
import logging
import os
import shutil
import time
import json

import structlog

import caas
import berry
from caas import ExecError
from caas.api import caas_api
from caas.commands import Exec, RawBashScript, RawExec, UploadFile
from caas.terminal.api import TerminalSession
from caas_swe_bench_train_v2.parser import ModelPatch
from caas_utils.utils import run_with_retries
from caas.protocol import NetworkMode

from deep_swe_eval_msft.swe_bench.peaval.vsc.setup import swe_bench_verfied_setup_fn_internal
from deep_swe_eval_msft.swe_bench.preparedness.swe_bench_metadata import SweBenchMetadata, SweBenchTaskData

from deep_swe_eval_msft.swe_bench.peaval.computer_task_grader_utils import _grade_fn_v2_inner, GradeReport, get_model_patch, PROTECTED_FILES
from deep_swe_eval_msft.data_converter.repo_setup import CAAS_CUSTOM_IMAGE, make_repo_script_list, to_sandbox_runtime

logger = structlog.get_logger(__name__)


async def grade_fn_v2_vsc(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> bool:
    metadata = SweBenchMetadata.model_validate(sample.gt_datapoint.metadata)
    report = await grade_fn_v2_internal_vsc(
        terminal_session=terminal_session,
        metadata=metadata,
        fast=fast,
    )

    logger.info("Grading finished", **report)
    # swang need to fill model_patch
    sample.ephemeral_metadata["model_patch"] = report["model_patch"]
    sample.metadata["grader"] = {
        "prompt": report["model_patch"],
        "response": report["test_output"],
        "score": int(report["passed"]),
    }
    return report["passed"]


async def grade_fn_v2_internal_vsc(
    *,
    terminal_session: TerminalSession,
    metadata: SweBenchMetadata,
    fast: bool,
) -> GradeReport:
    repo_root = metadata.cwd
    assert isinstance(metadata.task, SweBenchTaskData)

    model_patch = await get_model_patch(
        terminal_session=terminal_session,
        base_commit=metadata.task.base_commit,
        repo_root=repo_root,
        protected_files=PROTECTED_FILES,
    )

    # swang disable this for eval
    # if not model_patch.has_test_and_non_test():
    #     return {
    #         "passed": False,
    #         "test_output": "Model patch does not have both tests/non-tests",
    #         "model_patch": model_patch.compact_patch,
    #         "passed_tests": [],
    #     }

    if fast:
        # Soft reset the repository to the base commit (but don't run setup again)
        # await run_with_retries(
        #     terminal_session,
        #     f"cd {repo_root} && git add -A && git reset --hard {metadata.task.base_commit}",
        #     attempts=3,
        # )

        # hand off the model patch object instead of individual fields.
        return await _grade_fn_v2_inner(terminal_session, metadata, model_patch)

    # use the secure grading path, passing the full model patch object.
    return await _grade_fn_v2_secure_vsc(metadata, model_patch)


CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"


async def _grade_fn_v2_secure_vsc(
    metadata: SweBenchMetadata,
    model_patch: ModelPatch,
) -> GradeReport:
    """
    The "safe" route - re-initialize a fresh container and grade in there.
    """
    caas = caas_api(CAAS_ENDPOINT)
    async with caas.use_session(
        image=metadata.docker_image,
        cpu_limit=metadata.limits.cpu,
        memory_limit=metadata.limits.memory,
        network=NetworkMode.CAAS_PUBLIC_ONLY,
        keepalive_interval=30,
        sandbox_runtime=to_sandbox_runtime("unsafe"),
        timeout=1200,
        disk_limit="48g",
    ) as session:
        grade_session = TerminalSession(session)
        await swe_bench_verfied_setup_fn_internal(terminal_session=grade_session, metadata=metadata)

        return await _grade_fn_v2_inner(
            grade_session=grade_session,
            metadata=metadata,
            model_patch=model_patch,
        )

