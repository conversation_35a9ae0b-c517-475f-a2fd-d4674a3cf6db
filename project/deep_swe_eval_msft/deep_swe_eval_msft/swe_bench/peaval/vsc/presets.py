from typing import Any, Sequence

import chz
from berry import preset_utils
from chz_presets.core import CompositePreset, Preset
from rcall.runtime import get_func_path
from caas_utils.conversation_init import InstructionInsertionFormat
from berry.function_wrapper import FunctionWrapper
from deep_swe.datasets.config_utils import chz_path
from deep_swe_eval_msft.data_converter.data_converter import get_vardisc_args, _maybe_deliberate_tool_config
from deep_swe_msft.swe_bench_train_v2_vsc.dataset_config import SWEBenchTrainTelemetry0901DatasetConfig
import deep_swe_eval_msft.swe_bench.peaval.vsc.dataset_config as configs


VSC_DATASET_CONFIGS = [
    (configs.SWEBenchVerifiedDatasetConfigVSC, []),
    # (configs.SWEBenchVerifiedDatasetConfigVSCPromptFile, []),
]

VSC_DATASET_TELEMETRY_CONFIGS = [
    (configs.SWEBenchVerifiedDatasetConfigVSCRewrite, []),
    (configs.SWEBenchVerifiedDatasetConfigVSCRewrite2, []),
]

VSC_DATASET_DERISK_CONFIGS = [
    (SWEBenchTrainTelemetry0901DatasetConfig, []),
]

def ev_sm_bench_juice_vsc(
    juice: int | tuple[int, ...] = 128,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
    ) -> CompositePreset[Preset[Any]]:
    return preset_utils.compose_presets(
        *[
            preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    #"dataset.grader.channels_for_answer=final",  # It doesn't actually look at the answer, just for channel setup.
                    #"dataset.tool_configs.0.tool_timeout=2400",  # somehow caas can be slow
                    #*_maybe_deliberate_tool_config(start_index=1),
                    *get_vardisc_args(juice),
                ]
            )
            for dataset_config, args in VSC_DATASET_CONFIGS
        ],
        # swang disable this for eval, or will throw format validation error
        #format
    )

def ev_sm_bench_telemetry_juice_vsc(
    juice: int | tuple[int, ...] = 128,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
    ) -> CompositePreset[Preset[Any]]:
    return preset_utils.compose_presets(
        *[
            preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    #"dataset.grader.channels_for_answer=final",  # It doesn't actually look at the answer, just for channel setup.
                    #"dataset.tool_configs.0.tool_timeout=2400",  # somehow caas can be slow
                    #*_maybe_deliberate_tool_config(start_index=1),
                    *get_vardisc_args(juice),
                ]
            )
            for dataset_config, args in VSC_DATASET_TELEMETRY_CONFIGS
        ],
        # swang disable this for eval, or will throw format validation error
        #format
    )

def ev_sm_bench_juice_vsc_derisk(
    juice: int | tuple[int, ...] = 128,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
    ) -> CompositePreset[Preset[Any]]:
    return preset_utils.compose_presets(
        *[
            preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    #"dataset.grader.channels_for_answer=final",  # It doesn't actually look at the answer, just for channel setup.
                    #"dataset.tool_configs.0.tool_timeout=2400",  # somehow caas can be slow
                    #*_maybe_deliberate_tool_config(start_index=1),
                    *get_vardisc_args(juice),
                ]
            )
            for dataset_config, args in VSC_DATASET_DERISK_CONFIGS
        ],
        # swang disable this for eval, or will throw format validation error
        #format
    )


ev_sm_bench_vsc = ev_sm_bench_juice_vsc()
ev_sm_bench_telemetry_vsc = ev_sm_bench_telemetry_juice_vsc()
ev_sm_bench_vsc_derisk = ev_sm_bench_juice_vsc_derisk()
