import dataclasses
import json
import logging
import time

import caas
import chz
import structlog
import tenacity
from caas.api import caas_api
from caas.protocol import NetworkMode
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from deep_swe_eval_msft.swe_bench.preparedness.swe_bench_metadata import SweBenchMetadata
from qstar.common import datapoint
from qstar.common.tools.berry_tool_interface import ResourceContinuationError
from qstar.common.tools.caas_container.caas_container_tool import CaasContainerResourceConfig

CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"

logger = structlog.get_logger(component=__name__)


@chz.chz(typecheck=True)
class DeepSWECaasContainerResourceConfig(CaasContainerResourceConfig):
    """
    Allows per-datapoint control over the image, resource limits, and network access.
    (As a result, sandbox=True must be enabled)
    """

    caas_endpoint: str = CAAS_ENDPOINT
    caas_idle_ttl: int = 1200
    use_terminal_server: bool = True

    async def _initialize_resource_from_state_metadata(
        self, metadata: SweBenchMetadata, caas_session_state: str
    ) -> CaasContainer:
        try:
            container = CaasContainer(
                caas_endpoint=self.caas_endpoint,
                image_name=metadata.docker_image,
                caas_session_state=caas_session_state,
                is_owner=False,
                user=self.user,
            )
            await container.send_heartbeat()
            return container
        except (
            ValueError,
            caas.NotFoundError,
            caas.ServerError,
            caas.TransportError,
            caas.ClientError,
        ) as e:
            raise ResourceContinuationError(
                resource_name=self.name(),
                resource_state=caas_session_state.encode(),
                message=(
                    f"Failed to initialize caas container '{self.name()}' from session state "
                    f"'{caas_session_state}': {e}"
                ),
            ) from e

    @tenacity.retry(
        retry=tenacity.retry_if_exception_type(
            (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception)
        ),
        wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
        stop=tenacity.stop_after_attempt(20),
        before_sleep=tenacity.before_sleep_log(
            logger,  # type: ignore
            logging.WARNING,
        ),
    )
    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        metadata = SweBenchMetadata.model_validate(dp.metadata)

        if self.caas_session_state is not None:
            # We are not the owner of the container, so we don't need to run the setup function.
            resource = await self._initialize_resource_from_state_metadata(
                metadata, self.caas_session_state
            )
            assert not resource.is_owner
            return resource

        if self.use_terminal_server:
            cmd = ["/server.py"]
        else:
            cmd = []

        caas = caas_api(endpoint=self.caas_endpoint)
        caas_session = await caas.new_session(
            image=metadata.docker_image,
            cmd=cmd,
            cpu_limit="50", #metadata.limits.cpu,
            memory_limit="50g", #metadata.limits.memory,
            idle_ttl=self.caas_idle_ttl,
            num_gpus=self.caas_num_gpus,
            network=NetworkMode.CAAS_PUBLIC_ONLY,
            timeout=1200,
        )
        terminal_session = TerminalSession(caas_session)

        try:
            if self.setup_fn is not None:
                await self.setup_fn(
                    datapoint=dataclasses.asdict(dp),
                    terminal_session=terminal_session,
                )

            # swang enable network for eval
            #if not metadata.allow_internet:
            #    await caas_session.update_network(enable_network=False)
        except Exception as e:
            with open("/var/log/supervisor/aswe_setup_error.log", "a") as f:
                f.write(f"{e}\n")
            await caas_session.close()
            raise

        return CaasContainer(
            caas_endpoint=self.caas_endpoint,
            image_name=metadata.docker_image,
            caas_session_state=caas_session.save(),
            user=self.user,
            is_owner=True,
        )
