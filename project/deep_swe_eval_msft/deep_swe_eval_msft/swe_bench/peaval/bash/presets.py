from typing import Any, Sequence

import chz
from berry import preset_utils
from chz_presets.core import CompositePreset, Preset
from rcall.runtime import get_func_path
from caas_utils.conversation_init import InstructionInsertionFormat
from berry.function_wrapper import FunctionWrapper
import deep_swe.datasets.configs as base_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_eval_msft.data_converter.data_converter import get_vardisc_args, _maybe_deliberate_tool_config

def ev_swe_bench_juice_bash(
    juice: int | tuple[int, ...] = 768,
    #override_target_samples_per_instance: int = 1,
) -> Preset[Any]:
    # NOTE: slightly modified version of :qstar.presets.preparedness.model_autonomy:ev_swebench_477_container_tool
    return preset_utils.eval_dataset_preset(
        [
            "dataset=deep_swe_eval_msft.swe_bench.peaval.bash.dataset_config:SWEBenchVerifiedDatasetConfigBASH",
            #f"dataset.{override_target_samples_per_instance=}",
            #"dataset.grader.channels_for_answer=final",  # It doesn't actually look at the answer, just for channel setup.
            #"dataset.tool_configs.0.tool_timeout=2400",  # somehow caas can be slow
            #*_maybe_deliberate_tool_config(start_index=1),
            *get_vardisc_args(juice),
        ]
    )

def ev_sm_bench_juice_bash(juice: int | tuple[int, ...] = 768) -> CompositePreset[Preset[Any]]:
    return preset_utils.compose_presets(
        ev_swe_bench_juice_bash(juice),
    )

ev_sm_bench_bash = ev_sm_bench_juice_bash()
