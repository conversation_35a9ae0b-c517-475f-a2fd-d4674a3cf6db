import os
import shlex
from typing import Any

import caas
import structlog
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas_utils.utils import run_with_retries
from deep_swe_eval_msft.swe_bench.preparedness.swe_bench_metadata import SweBenchMetadata, SweBenchTaskData


logger = structlog.get_logger(component=__name__)

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_verfied_setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    metadata = SweBenchMetadata.model_validate(datapoint["metadata"])
    try:
        terminal_session.session.start_keepalive_task(keepalive_interval=60)
        await swe_bench_verfied_setup_fn_internal(terminal_session=terminal_session, metadata=metadata)
    finally:
        await terminal_session.session.stop_keepalive_task()

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_verfied_setup_fn_internal(
    *,
    terminal_session: TerminalSession,
    metadata: SweBenchMetadata,
) -> None:
    task = metadata.task
    assert isinstance(task, SweBenchTaskData)

    post_setup = [
        f"echo 'cd {shlex.quote(metadata.cwd)}' >> /root/.bashrc",
        "git config --global user.name User",
        "git config --global user.email user@localhost",
        #"pre-commit install || true",
    ]

    r = await terminal_session.session.run(BashScript("\n".join(post_setup), login=True, timeout=900))

    # swang do we need to do this
    #await terminal_session.session.run((BashScript"cd /testbed && git add -A && git commit -m setup", login=True, timeout=900))