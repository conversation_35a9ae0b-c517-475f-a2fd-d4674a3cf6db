import chz
import caas_autograding.grader as caas_graders
from chat.render.v4.experimental.strawberry.formatter import Berry<PERSON>hannel
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig 
from deep_swe.datasets import configs as default_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_eval_msft.swe_bench.peaval.bash.setup import swe_bench_verfied_setup_fn as swe_bench_verfied_setup_fn_bash
from deep_swe_eval_msft.swe_bench.peaval.bash.caas_resource_config import DeepSWECaasContainerResourceConfig as DeepSWECaasContainerResourceConfigBASH
from deep_swe_eval_msft.graders.eval_unit_test import EvalTermberryGrader
from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.common.tools.caas_container.caas_container_tool import C<PERSON>sContainerToolConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from qstar.graders.grader import Grader
from qstar.presets.chz_utils import IsOverride, override

CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
CAAS_IDLE_TTL = 1200

INSTRUCTIONS = """
Use `container.exec` to execute commands to accomplish the task. Avoid using interactive exec (setting `session_name`) unless it is absolutely necessary.
Always test your changes: either write a unit test or use `python -c` to manually invoke the new codepaths.
Internet access is not permitted in this container.

Examples of using `container.exec`:

- Running a script: {"cmd":["python","test.py"],"timeout":10000}
- Running some Python code: {"cmd":["python","-c","x = 0\\nfor i in range(10):\\n  x += i\\nprint(x)"]}
- Listing files: {"cmd":["ls"]}
- Grepping a file: {"cmd":["grep","class Action","aimacode/planning.py"],"timeout":1000}
- Showing lines in a file: {"cmd":["sed","-n","1,100p","path/to/file.py"]}
- Use `apply_patch` to apply patches to a file: {"cmd":["apply_patch","*** Begin Patch\\n*** Update File: path/to/file.py\\n@@ def example():\\n-pass\\n+return 123\\n*** End Patch"]}
""".strip()


def make_swe_bench_verified_graders_bash() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.swe_bench.peaval.bash.computer_task_grader:grade_fn_v2_bash",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def _make_tool_configs_bash(
    container_tool_config: CaasContainerToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
) -> tuple[ToolConfig, ...]:
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)


@chz.chz
class SWEBenchVerifiedGraderBASH(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(make_swe_bench_verified_graders_bash)

    @property
    def accepts_invalid(self) -> bool:
        """
        Generally, computer using tasks can be graded at any point in time.
        """
        return True

@chz.chz(typecheck=True)
class SWEBenchVerifiedDatasetConfigBASH(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = (
        # deprecated
        #"data.oaici.qstar.preparedness.datasets.swe_bench.all_verified_converted_vsc"
        #"data.oaici.qstar.preparedness.datasets.swe_bench.astropy_vsc"
        # vsc filtered, suggestion
        #"data.oaici.qstar.preparedness.datasets.swe_bench.astropy_vsc"
        "data.datasets.swe.eval.swe_bench.all_verified_converted490_vsc"
        #"data.oaici.qstar.preparedness.datasets.swe_bench.ttt2_vsc"
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: SWEBenchVerifiedGraderBASH(graders=make_swe_bench_verified_graders_bash())
    )
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs_bash)
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigBASH(setup_fn=swe_bench_verfied_setup_fn_bash,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter("deep_swe_eval_msft.swe_bench.preparedness.conversation_init:conversation_init_ms_bench_fn_vsc"),
        )
    )
    instructions: str = INSTRUCTIONS
    # swang align sampling with system message
    #max_tokens: int | None = 262144
    #max_num_yields: int | None = 100