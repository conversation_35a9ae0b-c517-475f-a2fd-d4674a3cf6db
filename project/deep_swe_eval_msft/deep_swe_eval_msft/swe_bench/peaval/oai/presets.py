from typing import Any, Sequence

import chz
from berry import preset_utils
from chz_presets.core import CompositePreset, Preset
from deep_swe.datasets.config_utils import chz_path
from rcall.runtime import get_func_path
from caas_utils.conversation_init import InstructionInsertionFormat
from berry.function_wrapper import FunctionWrapper
import deep_swe.datasets.configs as base_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_eval_msft.data_converter.data_converter import get_vardisc_args, _maybe_deliberate_tool_config
import deep_swe_eval_msft.swe_bench.peaval.oai.dataset_config as configs

OAI_DATASET_CONFIGS = [
    (configs.SWEBenchVerifiedDatasetConfigOAI, []),
]

OAI_DATASET_CONFIGS_GPT5 = [
    (configs.SWEBenchVerifiedDatasetConfigOAI_GPT5, []),
]

def ev_sm_bench_juice_oai(
    juice: int | tuple[int, ...] = 768,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
    ) -> CompositePreset[Preset[Any]]:
    return preset_utils.compose_presets(
        *[
            preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    #"dataset.grader.channels_for_answer=final",  # It doesn't actually look at the answer, just for channel setup.
                    #"dataset.tool_configs.0.tool_timeout=2400",  # somehow caas can be slow
                    #*_maybe_deliberate_tool_config(start_index=1),
                    *get_vardisc_args(juice),
                ]
            )
            for dataset_config, args in OAI_DATASET_CONFIGS
        ],
        # swang disable this for eval, or will throw format validation error
        #format
    )

def ev_sm_bench_juice_oai_gpt5(
    juice: int | tuple[int, ...] = 768,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 100,
    ) -> CompositePreset[Preset[Any]]:
    return preset_utils.compose_presets(
        *[
            preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    #"dataset.grader.channels_for_answer=final",  # It doesn't actually look at the answer, just for channel setup.
                    #"dataset.tool_configs.0.tool_timeout=2400",  # somehow caas can be slow
                    #*_maybe_deliberate_tool_config(start_index=1),
                    *get_vardisc_args(juice),
                ]
            )
            for dataset_config, args in OAI_DATASET_CONFIGS_GPT5
        ],
        # swang disable this for eval, or will throw format validation error
        #format
    )


ev_sm_bench_oai = ev_sm_bench_juice_oai()
ev_sm_bench_oai_gpt5 = ev_sm_bench_juice_oai_gpt5()
