import chz
from functools import partial
from typing import Any, Sequence

import qstar.instance_completers
import qstar.instance_optimizers

from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import <PERSON><PERSON>tageGrader
from qstar.presets.chz_utils import IsO<PERSON>ride, override
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.curriculums.function_wrapper import FunctionWrapper

from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    GraderService,
    TokenCompleterGraderService,
)

from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from token_completer import TokenCompleter

import caas_autograding.grader as caas_graders
from berry import InstanceCompleter, InstanceOptimizer, SampleAllocator
from qstar.allocators.continuous_rewards_adaptive_sample_allocator import (
    ContinuousRewardsAdaptiveSampleAllocator,
)

from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig 
from deep_swe.datasets import configs as default_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY, PADAWAN_SYSTEM_PROMPT
from gpt_oss_msft.data.prompts import MODEL_IDENTITY

from deep_swe_eval_msft.swe_bench.peaval.oai.setup import swe_bench_verfied_setup_fn as swe_bench_verfied_setup_fn_oai
from deep_swe_eval_msft.swe_bench.peaval.oai.caas_resource_config import DeepSWECaasContainerResourceConfig as DeepSWECaasContainerResourceConfigOAI
from deep_swe_eval_msft.graders.eval_unit_test import EvalTermberryGrader

# from deep_swe_eval_msft.tools.caas_oai_tool import EvalDeepSWECaasContainerToolConfig as EvalOAIToolConfig
from deep_swe_eval_msft.tools.caas_oai_tool import EvalDeepSWECaasContainerToolConfigV2 as EvalOAIToolConfig
from chat.render.v4.experimental.strawberry.formatter import BerryChannel

CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
CAAS_IDLE_TTL = 1200


def make_swe_bench_verified_graders_oai() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    # swang seems channels_for_answer is deprecated
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.swe_bench.peaval.oai.computer_task_grader:grade_fn_v2_oai",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def make_swe_bench_verified_switch_image_graders_oai() -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    # swang seems channels_for_answer is deprecated
    return (
        EvalTermberryGrader(
            grade_fn="deep_swe_eval_msft.swe_bench.peaval.oai.computer_task_grader:grade_fn_v2_switch_image_oai",
            channels_for_answer=(BerryChannel.FINAL_ANSWER,)),
    )

def _make_tool_configs_oai(
    container_tool_config: EvalOAIToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
) -> tuple[ToolConfig, ...]:
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)


@chz.chz
class SWEBenchVerifiedGraderOAI(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(make_swe_bench_verified_graders_oai)

    @property
    def accepts_invalid(self) -> bool:
        """
        Generally, computer using tasks can be graded at any point in time.
        """
        return True

@chz.chz(typecheck=True)
class SWEBenchVerifiedDatasetConfigOAI(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = (
        "data.xiaofewa.swe.eval.swe_bench.all_verified_converted477_vsc"
        # "data.datasets.swe.eval.swe_bench.all_verified_converted490_vsc"
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: SWEBenchVerifiedGraderOAI(graders=make_swe_bench_verified_graders_oai())
    )
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs_oai)
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigOAI(setup_fn=swe_bench_verfied_setup_fn_oai,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter("deep_swe_eval_msft.swe_bench.preparedness.conversation_init:conversation_init_ms_bench_fn_oai"),
        )
    )
    
    model_identity_str: str = "You are an AI assistant accessed via an API. Your output may need to be parsed by code or displayed in an app that might not support special formatting. Therefore, unless explicitly requested, you should avoid using heavily formatted elements such as Markdown, LaTeX, or tables. Bullet lists are acceptable."
    instructions: str = '' #keli: TBD system prompt, for now empty
    # swang align sampling with system message
    #max_tokens: int | None = 262144
    #max_num_yields: int | None = 100

@chz.chz(typecheck=True)
class SWEBenchVerifiedDatasetConfigOAI_GPT5(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = (
        "data.xiaofewa.swe.eval.swe_bench.all_verified_converted477_vsc"
        # "data.datasets.swe.eval.swe_bench.all_verified_converted490_vsc"
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: SWEBenchVerifiedGraderOAI(graders=make_swe_bench_verified_graders_oai())
    )
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs_oai)
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfigOAI(setup_fn=swe_bench_verfied_setup_fn_oai,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter("deep_swe_eval_msft.swe_bench.preparedness.conversation_init:conversation_init_ms_bench_fn_oai_gpt5"),
        )
    )
    
    model_identity_str: str = "You are an AI assistant accessed via an API. Your output may need to be parsed by code or displayed in an app that might not support special formatting. Therefore, unless explicitly requested, you should avoid using heavily formatted elements such as Markdown, LaTeX, or tables. Bullet lists are acceptable."
    instructions: str = '''Desired oververbosity for the final answer (not analysis): 3
    An oververbosity of 1 means the model should respond using only the minimal content necessary to satisfy the request, using concise phrasing and avoiding extra detail or explanation.
    An oververbosity of 10 means the model should provide maximally detailed, thorough responses with context, explanations, and possibly multiple examples.
    The desired oververbosity should be treated only as a *default*. Defer to any user or developer requirements regarding response length, if present.'''
    # swang align sampling with system message
    #max_tokens: int | None = 262144
    #max_num_yields: int | None = 100
