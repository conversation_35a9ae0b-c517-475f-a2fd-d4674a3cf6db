import os
import shlex
from typing import Any

import structlog

import chz
import caas
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas_utils import get_strawberry_ace_token
from caas_utils.utils import run_with_retries
from abc import ABC, abstractmethod
from deep_swe_eval_msft.swe_bench.preparedness.swe_bench_metadata import SweBenchMetadata, SweBenchTaskData

# from deep_swe_msft.tools.get_coreutils import setup_coreutils
from gpt_oss_msft.tools.oai_coreutils_setup import setup_coreutils

logger = structlog.get_logger(component=__name__)

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_verfied_setup_fn(
    *,
    datapoint: dict[str, Any],
    #metadata: SweBenchMetadata,
    terminal_session: TerminalSession,
) -> None:
    metadata = SweBenchMetadata.model_validate(datapoint["metadata"])
    terminal_session.session.start_keepalive_task(keepalive_interval=60)
    try:
        await swe_bench_verfied_setup_fn_internal(terminal_session=terminal_session, metadata=metadata)
        with open("/var/log/supervisor/keli_swe_bench_verfied_setup.log", "a") as f:
            f.write(f"Setup SBV successfully\n")
    except Exception as err:
        with open("/var/log/supervisor/keli_swe_bench_verfied_setup.log", "a") as f:
            f.write(f"Failed to setup SBV: {err}\n")
        raise RuntimeError(f"Failed to setup SBV: {err}") from err
    # finally:
    #     await terminal_session.session.stop_keepalive_task()

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_verfied_setup_fn_internal(
    *,
    terminal_session: TerminalSession,
    metadata: SweBenchMetadata,
) -> None:
    task = metadata.task
    assert isinstance(task, SweBenchTaskData)

    post_setup = [
        f"echo 'cd {metadata.cwd}' >> /root/.bashrc",
        "git config --global user.name User",
        "git config --global user.email user@localhost",
        #"pre-commit install || true",
    ]
    
    r = await terminal_session.session.run(BashScript("\n".join(post_setup), login=True, timeout=1200))
    
    await setup_coreutils(terminal_session.session, {}, repo_root=metadata.cwd, setup_python_tools=True)
