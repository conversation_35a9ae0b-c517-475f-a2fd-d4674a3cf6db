from typing import ClassVar, Iterator

from chat import MessageContent, Text
from llm_exec_postprocessor import ExecPostProcessor, ToolExecOutput

# 256 * 256 = 65KB ~= 20K tokens of output
MAX_OUTPUT_LINES = 100
MAX_LINE_LENGTH = 100
DECODE_ERROR_BEHAVIOR = "backslashreplace"


def _truncate_line(line: str, max_length: int) -> str:
    if len(line) > max_length:
        return line[:max_length] + "...\n"
    return line


def truncate_lines(
    output: bytes, *, max_lines: int = MAX_OUTPUT_LINES, max_line_length: int = MAX_LINE_LENGTH
) -> tuple[str, int, int]:
    """
    Limit the number of lines and the number of characters per line in the output.

    Returns:
        tuple[str, int, int]: The truncated output, the number of lines omitted, and the total number of lines prior to truncation.
    """
    output_lines = output.splitlines(keepends=True)
    if len(output_lines) > max_lines:
        truncated_lines = len(output_lines) - max_lines
        text_output = "".join(
            [
                *[
                    _truncate_line(
                        line.decode(errors=DECODE_ERROR_BEHAVIOR), max_length=max_line_length
                    )
                    for line in output_lines[: max_lines // 2]
                ],
                f"[...{truncated_lines} additional lines omitted...]\n",
                *[
                    _truncate_line(
                        line.decode(errors=DECODE_ERROR_BEHAVIOR), max_length=max_line_length
                    )
                    for line in output_lines[-(max_lines // 2) :]
                ],
            ]
        )
    else:
        truncated_lines = 0
        text_output = "".join(
            [
                _truncate_line(
                    line.decode(errors=DECODE_ERROR_BEHAVIOR),
                    max_length=max_line_length * (max_lines // len(output_lines)),
                )
                for line in output_lines
            ]
        )
    return text_output, truncated_lines, len(output_lines)


class TruncateExecPostProcessor(ExecPostProcessor):
    LLM_EXEC_POST_PROCESSOR_NAME: ClassVar[str] = "TruncateExecPostProcessor"
    LLM_EXEC_POST_PROCESSOR_VERSION: ClassVar[str] = "1.0.0"

    def __call__(self, output: ToolExecOutput) -> Iterator[MessageContent]:
        # Truncate the output to a reasonable length
        text_output, truncated_lines, total_lines = truncate_lines(output.output)
        yield Text.from_string(text_output)

        # Second message contains additional metadata (status code, error, truncation)
        status_messages = [f"Exit code: {output.status_code}"]
        status_messages.append(f"Wall time: {output.duration_seconds:.4g} seconds")
        if output.system_error:
            status_messages.append(f"Error: {output.system_error}")
        if truncated_lines:
            status_messages.append(f"Total output lines: {total_lines}")
        yield Text.from_string("\n".join(status_messages))
