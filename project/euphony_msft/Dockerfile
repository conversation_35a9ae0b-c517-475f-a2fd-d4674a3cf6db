# syntax=docker/dockerfile:1.7-labs
# This Dockerfile creates an optimized image for running the Euphony Web Components application

# This version matches the one used by rcall in rcall/rcall/global_config.py.
# Some newer images have the wrong Python version, so we must be careful to
# match rcall.
ARG PYTHON_BASE_IMAGE="iridiumsdc.azurecr.io/rcall:crow-1229215"

FROM ${PYTHON_BASE_IMAGE}

# Set working directory
WORKDIR /root

# Install tini for proper signal handling
ENV TINI_VERSION v0.19.0
RUN ARCH=$(dpkg --print-architecture) \
    && if [ "$ARCH" = "amd64" ]; then TINI_ARCH="amd64"; elif [ "$ARCH" = "arm64" ]; then TINI_ARCH="arm64"; else TINI_ARCH="$ARCH"; fi \
    && wget -O /tini "https://github.com/krallin/tini/releases/download/${TINI_VERSION}/tini-${TINI_ARCH}" \
    && chmod +x /tini

# Install supervisor for managing multiple processes and nginx for frontend
RUN apt-get update && apt-get install -y supervisor curl nginx && rm -rf /var/lib/apt/lists/*

RUN echo "python version: $(python --version)" \
    && echo "pip version: $(pip --version)" \
    && echo "python path: $(which python)"

ENV PATH="/root/.pyenv/versions/3.12.9/bin:$PATH"

# Copy both repositories from the build context
# glass repository contains the project files
COPY glass/ /root/code/glass/
# torchflow-mirror repository contains install.py and oaipkg dependencies
COPY torchflow-mirror/ /root/code/openai/

# Set environment variables for oaipkg
ENV UV_BUILD_CONSTRAINT="/root/code/openai/oaipackaging/oaipkg/global_constraints.txt"
ENV UV_INDEX_URL="https://pypi.org/simple/"

# Pare down what oaipkg installs during bootstrap to the minimum necessary.
ENV OAIPKG_BOOTSTRAP_MINIMAL=1

# Disable oaipkg magic - nothing should get installed outside of pinned
# dependencies. Such installation would probably fail because haven't copied the
# entire repo.
ENV OAIPKG_DISABLE_META_MISSING=1

# Docker Desktop "amd64" CPU type is generic, but we need to download wheels
# which are only built for avx2. This is safe because wheels don't actually run
# in the build process, only when the image is deployed to a cluster.
ENV OAIPKG_CPU_ISALEVEL=avx2
ENV OAIPKG_INSTALL_CPU_OPTIMIZER_KERNELS=unsafe_skip
ENV OAIPKG_INSTALL_PROTON_WHEEL=unsafe_skip

ENV OAIPKG_VERBOSE=1

# Install euphony and its dependencies using oaipkg
# Set PYTHONPATH to include glass repository for package discovery
ENV OAIPKG_POLYREPO_ROOTS="/root/code/glass"

# Copy wheels to the correct location (similar to busman setup)
ENV OAIPKG_ARTIFACTS_BASE=az://orngoaiartifacts

ENV BUILDKITE_AGENT_CACHE_HOST_DIR="/oaiwheelcache"
RUN --mount=type=bind,source=./orngoaiartifacts/wheels,target=/wheelcache/oaipkg_wheels_cache/ \
    if [ -f /root/code/glass/project/busman_msft/copy_wheels.py ]; then \
        python /root/code/glass/project/busman_msft/copy_wheels.py; \
    fi

# Install euphony package (this will be the Python backend dependencies)
RUN --mount=type=secret,id=tokencache,dst=/root/.azure/msal_token_cache.json --mount=type=secret,id=profile,dst=/root/.azure/azureProfile.json \
    PIP_NO_CACHE_DIR=off UV_NO_CACHE=true BUILDKITE=1 OAIPKG_INSTALLER_BACKEND=uv \
    /root/code/openai/install.py euphony_msft \
    && rm -rf /tmp/oaipkg

# Install Node.js for frontend build
# Check if pnpm exists first, only install if not present
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && which pnpm || npm install -g pnpm --force

# Build frontend
WORKDIR /root/code/openai/project/euphony
RUN if [ -f package.json ]; then \
        pnpm install --frozen-lockfile \
        && pnpm build \
        && cp -r dist/* /var/www/html/ \
        && chown -R www-data:www-data /var/www/html; \
    fi

# Configure nginx for frontend
COPY <<EOF /etc/nginx/sites-available/default
server {
    listen 80;
    server_name _;
    root /var/www/html;
    index index.html;
    
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8020;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Set environment variables for applications
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV OTEL_DISABLE=true
ENV PORT=80

# Server uses this
ENV OPENAI_API_KEY="fake-api-key"

# Create supervisor configuration
RUN mkdir -p /var/log/supervisor
COPY <<EOF /etc/supervisor/conf.d/euphony.conf
[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/nginx.err.log
stdout_logfile=/var/log/supervisor/nginx.out.log
user=root

[program:euphony_backend]
command=python -m uvicorn euphony_msft.server.app_wrapper:app --host 0.0.0.0 --port 8020 --reload
directory=/root/code/openai/project/euphony
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/euphony_backend.err.log
stdout_logfile=/var/log/supervisor/euphony_backend.out.log
user=root
environment=PYTHONPATH="/root/code/glass"
EOF

# Expose ports for both services
EXPOSE 80 8020

# Health check for both services
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:80/ && curl -f http://localhost:8020/ping/ || exit 1

# Use tini as entrypoint for proper signal handling
ENTRYPOINT ["/tini", "--"]

# Start supervisor to manage both services
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/euphony.conf"]
