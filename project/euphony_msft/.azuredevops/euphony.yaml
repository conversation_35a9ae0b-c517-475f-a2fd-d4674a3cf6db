pool: "iridium-builder"

trigger:
  branches:
    include:
      - main
  paths:
    include:
      - project/euphony_msft/**

pr:
  branches:
    include:
      - main
  paths:
    include:
      - project/euphony_msft/Dockerfile

resources:
  repositories:
    - repository: bootstrapping
      type: git
      name: Mimco/bootstrapping
      refName: refs/heads/main

stages:
  - stage: FetchOAIArtifactWheels
    displayName: "Fetch oaiartifact wheels"
    # Disabled to save time by using a pre-built artifact. Set to true to regenerate.
    condition: false
    jobs:
      - job: FetchOAIArtifactWheels
        displayName: "Fetch oaiartifact wheels"
        pool: "orange-builder"
        steps:
          - checkout: none

          - template: .azuredevops/templates/orange-az-setup.yaml@bootstrapping

          - script: |
              az account set --subscription "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e"
            displayName: "Set subscription"

          - script: |
              set -eufx -o pipefail
              mkdir -p $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels
              az storage blob download-batch \
                --pattern "*-cp312-cp312-linux_x86_64.whl" \
                --destination $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels \
                --account-name orngoaiartifacts \
                --source wheels \
                --auth-mode login
              az storage blob download-batch \
                --pattern "*-py3-none-any.whl" \
                --destination $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels \
                --account-name orngoaiartifacts \
                --source wheels \
                --auth-mode login
            displayName: "Download orngoaiartifacts from Azure Storage"

          - task: PublishBuildArtifacts@1
            displayName: "Publish orngoaiartifacts as build artifact"
            inputs:
              pathToPublish: "$(Build.ArtifactStagingDirectory)"
              artifactName: "orngoaiartifacts"

  - stage: "PushToIridiumsdc"
    displayName: "Build and Push Euphony Docker image to iridiumsdc"
    # We do not need to wait for the previous FetchOAIArtifactWheels stage to finish ss we skipped
    # the previous stage for now and use the artifacts from an existing build to save up time.
    # Here we explicitly declare no dependency to make this stage run even if the previous step is skipped.
    dependsOn: []
    jobs:
      - job: BuildAndPushEuphonyImage
        displayName: "Build and push Euphony Docker image"
        pool: "iridium-builder"
        steps:
          - template: .azuredevops/templates/az-setup.yaml@bootstrapping

          # Checkout the main repository
          - checkout: self
            fetchDepth: 1
            fetchTags: false

          # Checkout torchflow-mirror repository which contains install.py
          - checkout: git://Mimco/_git/torchflow-mirror@kaiyi/feat/add-euphony-0825
            fetchDepth: 1
            fetchTags: false

          - task: DownloadBuildArtifacts@0
            displayName: "Download Artifact"
            inputs:
              buildType: 'specific'
              project: '$(System.TeamProjectId)'
              pipeline: '$(System.DefinitionId)' # Reference this pipeline itself
              buildVersionToDownload: 'specific'
              # Pinned to a specific build to save time instead of re-generating the artifact.
              # https://dev.azure.com/project-argos/Mimco/_build/results?buildId=1328935&view=results
              buildId: '1328935'
              artifactName: "orngoaiartifacts"
              downloadPath: "$(System.DefaultWorkingDirectory)"

          - script: |
              set -eufx -o pipefail
              export GLASS_REPO_DIR=$(realpath ./glass)
              export BUILD_CONTEXT_DIR=$(realpath .)

              # Login to Azure Container Registry
              az acr login --name iridiumsdc

              # Set Docker image names
              export DOCKER_IMAGE=iridiumsdc.azurecr.io/euphony/euphony_msft:$(Build.BuildId)
              export DOCKER_IMAGE_LATEST=iridiumsdc.azurecr.io/euphony/euphony_msft:latest
              export DOCKER_BUILDKIT=1

              # Build Docker image using parent directory as context (contains both glass and torchflow-mirror)
              docker buildx build \
                "$BUILD_CONTEXT_DIR" \
                -f "$GLASS_REPO_DIR/project/euphony_msft/Dockerfile" \
                -t "$DOCKER_IMAGE" \
                --platform=linux/amd64 \
                --progress=plain

              # Push the versioned image
              docker push "$DOCKER_IMAGE"

              # Tag and push the latest image
              docker tag "$DOCKER_IMAGE" "$DOCKER_IMAGE_LATEST"
              docker push "$DOCKER_IMAGE_LATEST"

              echo "Successfully built and pushed Euphony Docker images:"
              echo "  - $DOCKER_IMAGE"
              echo "  - $DOCKER_IMAGE_LATEST"
            displayName: "Build and push Euphony Docker image"