import hashlib
import os
import shutil
from pathlib import Path


def gethash(path):
    """
    Generate SHA1 hash for wheel file path to match oaipkg caching structure.

    Args:
        path (Path): Path to the wheel file

    Returns:
        str: SHA1 hash (first 16 characters)
    """
    whl_path = os.path.join(*path.parts[-2:])
    baseblob = os.environ.get("OAIPKG_ARTIFACTS_BASE", "az://orngoaiartifacts")
    final_path = os.path.join(baseblob, "wheels", whl_path)
    sha = hashlib.sha1(final_path.encode()).hexdigest()[:16]
    print(f"SHA1 hash for {final_path}: {sha}")
    return sha


def copy_files(from_dir, to_dir, converter_func=None):
    """
    Copy wheel files from source to destination with hash-based naming.

    Args:
        from_dir (str): Source directory containing wheel files
        to_dir (str): Destination directory for cached wheels
        converter_func (callable): Function to generate hash for file path
    """
    for file in Path(from_dir).rglob("*"):
        if file.is_file():
            print(f"Processing {file}...")
            sha = converter_func(file) if converter_func else file.stem
            # Create destination path: to_dir/sha/filename
            dest_dir = os.path.join(to_dir, "oaipkg_wheels_cache", sha)
            os.makedirs(dest_dir, exist_ok=True)
            dest_file = os.path.join(dest_dir, file.name)
            shutil.copy(str(file), dest_file)
            print(f"Copied {file} to {dest_file}")


if __name__ == "__main__":
    # Get directories from environment or use defaults
    from_dir = "/wheelcache/oaipkg_wheels_cache/"
    to_dir = os.environ.get("BUILDKITE_AGENT_CACHE_HOST_DIR", "/oaiwheelcache")

    print(f"Copying wheels from {from_dir} to {to_dir}")

    # Check if source directory exists
    if not os.path.exists(from_dir):
        print(f"Source directory {from_dir} does not exist, skipping wheel copy")
        exit(0)

    # Call the function to copy wheels
    copy_files(from_dir, to_dir, converter_func=gethash)
