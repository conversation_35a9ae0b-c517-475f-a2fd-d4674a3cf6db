[project]
name = "euphony_msft"
version = "0.1.0"
dependencies = []

[tool.oaipkg]
monorepo-dependencies = [
    "euphony",
]
ruff_full_check = true

# If you are writing a library that is intended to be widely used, delete these lines.
# You can also explicitly allow specific dependent projects using allowlisted-dependents.
disallow-dependents = true

[tool.buildkite]
name = ":musical_note: euphony_msft"
pipeline = "buildkite/pipeline.yml"
owner = "@zhangkaiyi"

[tool.ruff]
extend = "../../pyproject.toml"

[tool.ruff.lint]
extend-select = ["ANN2"]

[tool.setuptools.packages.find]
include = ["euphony_msft*"]

[build-system]
requires = ["setuptools>=64.0"]
build-backend = "setuptools.build_meta"

[tool.mypy]
strict = true
local_partial_types = true

# These two make life easier if you use a lot of untyped libraries:
warn_return_any = false
allow_untyped_calls = true

# Do not use `ignore_missing_imports`, instead use:
# disable_error_code = ["import-untyped"]

# If you don't want to force use of type annotations:
# allow_incomplete_defs = true
# allow_untyped_defs = true
# allow_any_generics = true