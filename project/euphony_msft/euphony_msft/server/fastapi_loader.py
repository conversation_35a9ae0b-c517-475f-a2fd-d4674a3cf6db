import importlib.util
import os
import uuid


def load_fastapi_app_from_file(path: str, attr_name: str = "app"):
    """Load and return a FastAPI app (or other attribute) from a Python file path.

    This lets us import files whose filenames are not valid Python module names
    (for example, names containing hyphens) without renaming them.

    Raises:
        FileNotFoundError: if the file does not exist.
        ImportError: if the module cannot be loaded or the attribute is missing.
    """
    if not os.path.exists(path):
        raise FileNotFoundError(path)

    module_name = f"fastapi_app_{uuid.uuid4().hex}"
    spec = importlib.util.spec_from_file_location(module_name, path)
    if spec is None or spec.loader is None:
        raise ImportError(f"Could not load spec for {path}")

    module = importlib.util.module_from_spec(spec)
    # type: ignore[attr-defined]
    spec.loader.exec_module(module)

    try:
        return getattr(module, attr_name)
    except AttributeError as e:
        raise ImportError(f"No '{attr_name}' object found in {path}") from e
