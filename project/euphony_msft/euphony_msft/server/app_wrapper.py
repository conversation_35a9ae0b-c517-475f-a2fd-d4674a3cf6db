from euphony_msft.server.fastapi_loader import load_fastapi_app_from_file
from fastapi import FastAPI

# The euphony server fastapi file is located here.
# We load the original FastAPI app from the specified file directly
# instead of importing it as a python module, because of:
# - The original euphony app contains a hyphen in its filename,
#   causing it cannot be imported.
# - It locates in a "euphony/server" folder instead of "euphony/euphony"
#   make it not installed during `oaipkg install`.
file_path = "/root/code/openai/project/euphony/server/fastapi-main.py"
original_app = load_fastapi_app_from_file(file_path)

# We wrap the original euphony with a "/api" prefix.
app = FastAPI()
app.mount("/api", original_app)
