#!/bin/bash

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/config.sh"

log_info "==================================================================="
log_info "Running Tool Choice Tests"
log_info "==================================================================="

# Helper function to analyze tool choice response
analyze_tool_choice_response() {
    local json="$1"
    local expected_behavior="$2"
    
    local finish_reason
    finish_reason=$(echo "$json" | jq -r '.choices[0].finish_reason' 2>/dev/null)
    
    local has_tool_calls
    has_tool_calls=$(echo "$json" | jq -r '.choices[0].message.tool_calls != null' 2>/dev/null)
    
    local has_content
    has_content=$(echo "$json" | jq -r '.choices[0].message.content != null and .choices[0].message.content != ""' 2>/dev/null)
    
    log_info "Expected: $expected_behavior"
    log_info "Finish reason: $finish_reason"
    log_info "Has tool calls: $has_tool_calls"
    log_info "Has content: $has_content"
    
    if [[ "$has_tool_calls" == "true" ]]; then
        log_info "Tool calls:"
        echo "$json" | jq '.choices[0].message.tool_calls' 2>/dev/null
    fi
    
    if [[ "$has_content" == "true" ]]; then
        log_info "Content:"
        echo "$json" | jq -r '.choices[0].message.content' 2>/dev/null
    fi
}

# Test 1: tool_choice="none" - should never call tools
test_tool_choice_none() {
    log_info "Test 1: tool_choice=\"none\" - Force no tool use"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "'"$USER_MESSAGE"'"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tool_choice": "none",
        "tools": ['"$BASH_TOOL"']
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    if validate_response "$response"; then
        local json=$(get_json_response "$response")
        log_success "tool_choice=\"none\" test passed (${time}s)"
        analyze_tool_choice_response "$json" "Direct text response, no tools"
    else
        log_error "tool_choice=\"none\" test failed (${time}s)"
        return 1
    fi
    echo ""
}

# Test 2: tool_choice="auto" - let model decide
test_tool_choice_auto() {
    log_info "Test 2: tool_choice=\"auto\" - Let model decide"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "'"$USER_MESSAGE"'"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tool_choice": "auto",
        "tools": ['"$BASH_TOOL"']
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    if [[ "$status" == "200" ]]; then
        log_success "tool_choice=\"auto\" test passed (${time}s)"
        analyze_tool_choice_response "$json" "Model decides - likely tool call for this prompt"
    else
        log_error "tool_choice=\"auto\" test failed (HTTP $status)"
        echo "$json"
    fi
    echo ""
}

# Test 3: tool_choice="required" - must call some tool
test_tool_choice_required() {
    log_info "Test 3: tool_choice=\"required\" - Force tool use, model picks which"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "Help me with a task"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tool_choice": "required",
        "tools": [
            '"$BASH_TOOL"',
            {
                "type": "function",
                "function": {
                    "name": "help_user",
                    "description": "Provides help to the user",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "task": {
                                "type": "string", 
                                "description": "The task to help with"
                            }
                        },
                        "required": ["task"]
                    }
                }
            }
        ]
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    if [[ "$status" == "200" ]]; then
        log_success "tool_choice=\"required\" test passed (${time}s)"
        analyze_tool_choice_response "$json" "Must call some tool - model chooses which"
    else
        log_error "tool_choice=\"required\" test failed (HTTP $status)"
        echo "$json"
    fi
    echo ""
}

# Test 4: Specific function choice - force specific tool
test_tool_choice_specific() {
    log_info "Test 4: Specific function choice - Force specific tool"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "I need to run a command, the command is ls"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tool_choice": {
            "type": "function",
            "function": {"name": "bash"}
        },
        "tools": [
            '"$BASH_TOOL"',
            {
                "type": "function", 
                "function": {
                    "name": "other_tool",
                    "description": "Some other tool",
                    "parameters": {"type": "object", "properties": {}}
                }
            }
        ]
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    if [[ "$status" == "200" ]]; then
        log_success "Specific function choice test passed (${time}s)"
        analyze_tool_choice_response "$json" "Must call bash function specifically"
        
        # Verify it called the right function
        local called_function
        called_function=$(echo "$json" | jq -r '.choices[0].message.tool_calls[0].function.name' 2>/dev/null)
        if [[ "$called_function" == "bash" ]]; then
            log_success "Correctly called the specified 'bash' function"
        else
            log_error "Expected 'bash', but called '$called_function'"
        fi
    else
        log_error "Specific function choice test failed (HTTP $status)"
        echo "$json"
    fi
    echo ""
}

# Test 5: Invalid tool_choice handling
test_tool_choice_invalid() {
    log_info "Test 5: Invalid tool_choice handling"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "Hello"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tool_choice": "invalid_choice",
        "tools": ['"$BASH_TOOL"']
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    if [[ "$status" == "200" ]]; then
        log_success "Invalid tool_choice handling test passed (${time}s)"
        analyze_tool_choice_response "$json" "Should fallback to auto mode"
    else
        log_warning "Invalid tool_choice returned HTTP $status - this might be expected"
        echo "$json"
    fi
    echo ""
}

# Test 6: tool_choice with non-existent function
test_tool_choice_nonexistent() {
    log_info "Test 6: tool_choice with non-existent function"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "Test"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tool_choice": {
            "type": "function",
            "function": {"name": "nonexistent_function"}
        },
        "tools": ['"$BASH_TOOL"']
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    log_info "Non-existent function test result (${time}s) - HTTP $status"
    if [[ "$status" != "200" ]]; then
        log_info "Expected failure - function doesn't exist in tools"
    else
        analyze_tool_choice_response "$json" "Should handle gracefully"
    fi
    echo "$json"
    echo ""
}

# Test 7: tool_choice="auto" vs no tool_choice (default behavior)
test_tool_choice_default_vs_auto() {
    log_info "Test 7: Comparing default behavior vs explicit tool_choice=\"auto\""
    
    # Test with default (no tool_choice specified)
    log_info "7a: Default behavior (no tool_choice)"
    local data_default='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "'"$USER_MESSAGE"'"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tools": ['"$BASH_TOOL"']
    }'
    
    local response_default
    response_default=$(make_request "$DEFAULT_ENDPOINT" "$data_default")
    local status_default=$(get_http_status "$response_default")
    local json_default=$(get_json_response "$response_default")
    
    if [[ "$status_default" == "200" ]]; then
        log_success "Default behavior test passed"
        local finish_default
        finish_default=$(echo "$json_default" | jq -r '.choices[0].finish_reason' 2>/dev/null)
        log_info "Default finish reason: $finish_default"
    fi
    
    # Test with explicit auto
    log_info "7b: Explicit tool_choice=\"auto\""
    local data_auto='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "'"$USER_MESSAGE"'"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tool_choice": "auto",
        "tools": ['"$BASH_TOOL"']
    }'
    
    local response_auto
    response_auto=$(make_request "$DEFAULT_ENDPOINT" "$data_auto")
    local status_auto=$(get_http_status "$response_auto")
    local json_auto=$(get_json_response "$response_auto")
    
    if [[ "$status_auto" == "200" ]]; then
        log_success "Explicit auto test passed"
        local finish_auto
        finish_auto=$(echo "$json_auto" | jq -r '.choices[0].finish_reason' 2>/dev/null)
        log_info "Auto finish reason: $finish_auto"
    fi
    
    log_info "Both should behave identically"
    echo ""
}

# Test 8: Multi-turn conversation with tool results
test_tool_conversation_flow() {
    log_info "Test 8: Multi-turn conversation with tool results"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "You are helpful"},
            {"role": "user", "content": "list files"},
            {"role": "assistant", "tool_calls": [{"id": "call123", "type": "function", "function": {"name": "bash", "arguments": "{\"command\":\"ls\"}"}}]},
            {"role": "tool", "content": "file1.txt file2.txt", "tool_call_id": "call123"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tools": ['"$BASH_TOOL"']
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    if validate_response "$response"; then
        log_success "Multi-turn tool conversation test passed (${time}s)"
        analyze_tool_choice_response "$json" "Should provide summary of tool results"
        
        # Check if response mentions the files
        local content
        content=$(echo "$json" | jq -r '.choices[0].message.content' 2>/dev/null)
        if [[ "$content" =~ "file1.txt" ]] || [[ "$content" =~ "file2.txt" ]]; then
            log_success "Response correctly references tool output"
        else
            log_warning "Response may not be referencing tool output properly"
        fi
    else
        log_error "Multi-turn tool conversation test failed (${time}s)"
        echo "$json"
    fi
    echo ""
}

# Run all tool choice tests
main() {
    test_tool_choice_none
    test_tool_choice_auto
    test_tool_choice_required
    test_tool_choice_specific
    test_tool_choice_invalid
    test_tool_choice_nonexistent
    test_tool_choice_default_vs_auto
    test_tool_conversation_flow
    log_success "All tool choice tests completed!"
}

# Run tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main
fi