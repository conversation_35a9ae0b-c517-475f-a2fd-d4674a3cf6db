# OpenAI Proxy Test Suite

Comprehensive test scripts for validating OpenAI Proxy service functionality.

## Quick Start

```bash
cd tests
./run_all_tests.sh          # Run all tests
./test_normal.sh            # Basic API functionality  
./test_constrained.sh       # Constrained mode with juice parameters
./test_tool_choice.sh       # Tool choice parameter testing
```

## Test Scripts

### `test_normal.sh`
Tests standard OpenAI-compatible API functionality:
- Basic text completion (no tools)
- Tool-enabled completion (auto mode)

### `test_constrained.sh` 
Tests constrained sampling mode (`constrained=true` + `juice` parameters):
- Constrained completion without tools
- Constrained completion with single tool
- Constrained completion with multiple tools

### `test_tool_choice.sh`
Comprehensive testing of `tool_choice` parameter options:
- `"none"` - Force no tool usage
- `"auto"` - Let model decide (default)
- `"required"` - Force tool usage, model picks which
- `{"type": "function", "function": {"name": "func_name"}}` - Force specific function
- Invalid/nonexistent tool choice handling

### `run_all_tests.sh`
Main test runner with:
- Endpoint connectivity testing  
- Progress tracking and result summary

## Configuration

Edit `config.sh` to modify:
- **Endpoints**: `LOCAL_ENDPOINT`, `PROD_ENDPOINT`
- **Authentication**: `COOKIE` value
- **Models**: `DEFAULT_MODEL`, `CONSTRAINED_MODEL`

## Validation

Tests check both HTTP status codes and response content for `ParseError` strings. Any test returning `ParseError` content is marked as failed.