#!/bin/bash

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/config.sh"

log_info "==================================================================="
log_info "Running Normal API Tests"
log_info "==================================================================="

# Test 1: Basic completion without tools
test_basic_completion() {
    log_info "Test 1: Basic completion without tools"
    
    local data='{
        "model": "'"$DEFAULT_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "top_p": '"$DEFAULT_TOP_P"',
        "frequency_penalty": '"$DEFAULT_FREQUENCY_PENALTY"',
        "presence_penalty": '"$DEFAULT_PRESENCE_PENALTY"'
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local time=$(get_response_time "$response")
    
    if validate_response "$response"; then
        local json=$(get_json_response "$response")
        log_success "Basic completion test passed (${time}s)"
        echo "$json" | jq '.choices[0].message.content' 2>/dev/null || echo "$json"
    else
        log_error "Basic completion test failed (${time}s)"
        return 1
    fi
    echo ""
}

# Test 2: Completion with tools (auto mode)
test_completion_with_tools() {
    log_info "Test 2: Completion with tools (default auto mode)"
    
    local data='{
        "model": "'"$DEFAULT_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "'"$USER_MESSAGE"'"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tool_choice": "auto",
        "top_p": '"$DEFAULT_TOP_P"',
        "frequency_penalty": '"$DEFAULT_FREQUENCY_PENALTY"',
        "presence_penalty": '"$DEFAULT_PRESENCE_PENALTY"',
        "parallel_tool_calls": false,
        "tools": ['"$BASH_TOOL"']
    }'

    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local time=$(get_response_time "$response")
    
    if validate_response "$response"; then
        local json=$(get_json_response "$response")
        log_success "Completion with tools test passed (${time}s)"
        
        local finish_reason
        finish_reason=$(echo "$json" | jq -r '.choices[0].finish_reason' 2>/dev/null)
        log_info "Finish reason: $finish_reason"
        
        if [[ "$finish_reason" == "tool_calls" ]]; then
            echo "$json" | jq '.choices[0].message.tool_calls' 2>/dev/null || echo "$json"
        else
            echo "$json" | jq '.choices[0].message.content' 2>/dev/null || echo "$json"
        fi
    else
        log_error "Completion with tools test failed (${time}s)"
        return 1
    fi
    echo ""
}


# Run all normal API tests
main() {
    local failed_tests=0
    
    if ! test_basic_completion; then
        ((failed_tests++))
    fi
    
    if ! test_completion_with_tools; then
        ((failed_tests++))
    fi
    
    if [[ $failed_tests -eq 0 ]]; then
        log_success "All normal API tests completed successfully!"
        return 0
    else
        log_error "$failed_tests test(s) failed"
        return 1
    fi
}

# Run tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main
fi