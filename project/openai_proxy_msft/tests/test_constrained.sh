#!/bin/bash

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/config.sh"

log_info "==================================================================="
log_info "Running Constrained Mode Tests (juice + constrained)"
log_info "==================================================================="

# Test 1: Constrained mode without tools
test_constrained_no_tools() {
    log_info "Test 1: Constrained mode without tools"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "Hello, what can you help me with?"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "top_p": '"$DEFAULT_TOP_P"'
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    if validate_response "$response"; then
        local json=$(get_json_response "$response")
        log_success "Constrained mode (no tools) test passed (${time}s)"
        echo "$json" | jq '.choices[0].message.content' 2>/dev/null || echo "$json"
    else
        log_error "Constrained mode (no tools) test failed (${time}s)"
        return 1
    fi
    echo ""
}

# Test 2: Constrained mode with tools
test_constrained_with_tools() {
    log_info "Test 2: Constrained mode with tools"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "'"$USER_MESSAGE"'"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "top_p": '"$DEFAULT_TOP_P"',
        "frequency_penalty": '"$DEFAULT_FREQUENCY_PENALTY"',
        "presence_penalty": '"$DEFAULT_PRESENCE_PENALTY"',
        "parallel_tool_calls": false,
        "tools": ['"$BASH_TOOL"']
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    if validate_response "$response"; then
        local json=$(get_json_response "$response")
        log_success "Constrained mode with tools test passed (${time}s)"
        
        local finish_reason
        finish_reason=$(echo "$json" | jq -r '.choices[0].finish_reason' 2>/dev/null)
        log_info "Finish reason: $finish_reason"
        
        if [[ "$finish_reason" == "tool_calls" ]]; then
            log_info "Tool calls generated:"
            echo "$json" | jq '.choices[0].message.tool_calls' 2>/dev/null || echo "$json"
        else
            log_info "Direct response generated:"
            echo "$json" | jq '.choices[0].message.content' 2>/dev/null || echo "$json"
        fi
    else
        log_error "Constrained mode with tools test failed (${time}s)"
        return 1
    fi
    echo ""
}

# Test 3: Constrained mode with multiple tools
test_constrained_multiple_tools() {
    log_info "Test 3: Constrained mode with multiple tools"
    
    local data='{
        "model": "'"$CONSTRAINED_MODEL"'",
        "messages": [
            {"role": "system", "content": "'"$SYSTEM_MESSAGE"'"},
            {"role": "user", "content": "Check the current time and list files in current directory"}
        ],
        "temperature": '"$DEFAULT_TEMPERATURE"',
        "tools": [
            '"$BASH_TOOL"',
            {
                "type": "function",
                "function": {
                    "name": "get_time",
                    "description": "Gets the current system time",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        ]
    }'
    
    local response
    response=$(make_request "$DEFAULT_ENDPOINT" "$data")
    local status=$(get_http_status "$response")
    local time=$(get_response_time "$response")
    local json=$(get_json_response "$response")
    
    if validate_response "$response"; then
        local json=$(get_json_response "$response")
        log_success "Constrained mode multiple tools test passed (${time}s)"
        
        local finish_reason
        finish_reason=$(echo "$json" | jq -r '.choices[0].finish_reason' 2>/dev/null)
        log_info "Finish reason: $finish_reason"
        
        if [[ "$finish_reason" == "tool_calls" ]]; then
            echo "$json" | jq '.choices[0].message.tool_calls' 2>/dev/null || echo "$json"
        else
            echo "$json" | jq '.choices[0].message.content' 2>/dev/null || echo "$json"
        fi
    else
        log_error "Constrained mode multiple tools test failed (${time}s)"
        return 1
    fi
    echo ""
}

# Run all constrained mode tests
main() {
    local failed_tests=0
    
    if ! test_constrained_no_tools; then
        ((failed_tests++))
    fi
    
    if ! test_constrained_with_tools; then
        ((failed_tests++))
    fi
    
    if ! test_constrained_multiple_tools; then
        ((failed_tests++))
    fi
    
    if [[ $failed_tests -eq 0 ]]; then
        log_success "All constrained mode tests completed successfully!"
        return 0
    else
        log_error "$failed_tests test(s) failed"
        return 1
    fi
}

# Run tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main
fi