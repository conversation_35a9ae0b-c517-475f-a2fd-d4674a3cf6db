#!/bin/bash

# Load configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/config.sh"

# Test results tracking
declare -a TEST_RESULTS
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run a test script and track results
run_test_script() {
    local script_name="$1"
    local script_path="$SCRIPT_DIR/$script_name"
    
    if [[ ! -f "$script_path" ]]; then
        log_error "Test script not found: $script_path"
        return 1
    fi
    
    log_info "===================================================================" 
    log_info "Running $script_name"
    log_info "==================================================================="
    
    # Make script executable
    chmod +x "$script_path"
    
    # Run the test script and capture output
    local start_time=$(date +%s)
    if bash "$script_path"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_success "$script_name completed successfully in ${duration}s"
        TEST_RESULTS+=("✅ $script_name - PASSED (${duration}s)")
        ((PASSED_TESTS++))
    else
        local end_time=$(date +%s)  
        local duration=$((end_time - start_time))
        log_error "$script_name failed after ${duration}s"
        TEST_RESULTS+=("❌ $script_name - FAILED (${duration}s)")
        ((FAILED_TESTS++))
    fi
    
    ((TOTAL_TESTS++))
    echo ""
}

# Function to show test configuration
show_config() {
    log_info "==================================================================="
    log_info "Test Configuration"
    log_info "==================================================================="
    log_info "Endpoint: $DEFAULT_ENDPOINT"
    log_info "Default Model: $DEFAULT_MODEL"
    log_info "Constrained Model: $CONSTRAINED_MODEL"
    log_info "Temperature: $DEFAULT_TEMPERATURE"
    echo ""
}

# Function to display final results
show_results() {
    log_info "==================================================================="
    log_info "Test Results Summary"  
    log_info "==================================================================="
    
    for result in "${TEST_RESULTS[@]}"; do
        echo -e "$result"
    done
    
    echo ""
    log_info "Total Tests: $TOTAL_TESTS"
    if [[ $PASSED_TESTS -gt 0 ]]; then
        log_success "Passed: $PASSED_TESTS"
    fi
    if [[ $FAILED_TESTS -gt 0 ]]; then
        log_error "Failed: $FAILED_TESTS"
    fi
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_success "🎉 All tests passed!"
        return 0
    else
        log_error "💥 Some tests failed"
        return 1
    fi
}

# Function to test endpoint connectivity  
test_connectivity() {
    log_info "Testing endpoint connectivity..."
    
    # Simple health check
    local response
    response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -H "$COOKIE_HEADER" "$DEFAULT_ENDPOINT/" 2>/dev/null)
    local status=$(echo "$response" | grep "^HTTP_STATUS:" | cut -d: -f2)
    
    if [[ "$status" == "200" ]]; then
        log_success "Endpoint is reachable"
        return 0
    else
        log_error "Endpoint connectivity test failed (HTTP $status)"
        log_error "Please check your endpoint configuration and network connectivity"
        return 1
    fi
}

# Main execution function
main() {
    local start_time=$(date)
    
    echo ""
    log_info "🚀 Starting OpenAI Proxy Test Suite"
    log_info "Started at: $start_time"
    echo ""
    
    if ! test_connectivity; then
        log_error "Connectivity test failed - aborting test suite"
        exit 1
    fi
    
    # Show configuration
    show_config
    
    # Run test scripts in order
    run_test_script "test_normal.sh"
    run_test_script "test_constrained.sh"
    run_test_script "test_tool_choice.sh"
    
    # Show final results
    local end_time=$(date)
    echo ""
    log_info "Test suite completed at: $end_time"
    echo ""
    show_results
    
    # Exit with appropriate code
    exit $?
}

# Run the test suite
main