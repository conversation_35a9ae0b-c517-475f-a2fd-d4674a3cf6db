#!/bin/bash

# =================================================================
# OpenAI Proxy Test Configuration
# =================================================================

# Endpoints
LOCAL_ENDPOINT="http://10.133.73.42:8500"
PROD_ENDPOINT="https://openai-proxy.int.prod-southcentralus-hpe-3.dev.openai.org"

# Default endpoint for testing (change this to switch between local/prod)
DEFAULT_ENDPOINT="$LOCAL_ENDPOINT"

# Authentication
COOKIE="<YOUR-COOKIE>"

# Model configurations
# DEFAULT_MODEL="bus:snap:orngcresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted:user:ssloto\$harmony_v4.0.15_berry_v3_16k_orion_text"
# DEFAULT_MODEL="bus:snap:orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted:user:msft\$harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"
# DEFAULT_MODEL="bus:snap:orngcresco/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted:user:usvsns\$harmony_v4.0.15_berry_v3_1mil_orion_lpe"
DEFAULT_MODEL="bus:snap:orngcresco/twapi/mini/e/damajercak-mix16-arm-4-pdw2-ev3-mixed-itc-spi32-gpt5-mini-sft-1e-4-469-100steps-tef03-5-tpm1-rm-lr1e-5-run-20250813-072230/policy/step_000100:user:evaluation\$harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"
# DEFAULT_MODEL="bus:snap:orngcresco/models/snapshots/chat-gpt-4o-tt-v3-d64_80g_bf16_fridge-step3075-250316-decrypted:user:shuowa\$harmony_v4.0.15_16k_orion_text_only_no_asr_2k_action"
# DEFAULT_MODEL="bus:snap:orngcresco/twapi/mini/e/yunshengli-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-rrbif-v1/policy/step_000300:user:xuga\$harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc"
CONSTRAINED_MODEL="$DEFAULT_MODEL?juice=128&constrained=true"

# Common headers
CONTENT_TYPE="Content-Type: application/json"
COOKIE_HEADER="Cookie: $COOKIE"

# Test parameters
DEFAULT_TEMPERATURE=0
DEFAULT_TOP_P=0.95
DEFAULT_FREQUENCY_PENALTY=0
DEFAULT_PRESENCE_PENALTY=0

# Common tools definition
BASH_TOOL='{
    "type": "function",
    "function": {
        "name": "bash",
        "description": "Runs a bash command in an interactive bash",
        "parameters": {
            "type": "object",
            "properties": {
                "command": {
                    "type": "string",
                    "description": "The bash command and arguments to run"
                }
            },
            "required": ["command"]
        }
    }
}'

# Test messages
SYSTEM_MESSAGE="You are an AI assistant."
USER_MESSAGE="list dir in /home/<USER>"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to make curl request with common settings
make_request() {
    local endpoint="$1"
    local data="$2"
    local extra_headers="$3"
    
    curl -s -w "\nHTTP_STATUS:%{http_code}\nTIME_TOTAL:%{time_total}\n" \
         --connect-timeout 10 \
         --max-time 120 \
         -H "$CONTENT_TYPE" \
         -H "$COOKIE_HEADER" \
         $extra_headers \
         -d "$data" \
         "$endpoint/v1/chat/completions"
}

# Function to extract HTTP status from response
get_http_status() {
    echo "$1" | grep "HTTP_STATUS:" | cut -d: -f2
}

# Function to extract response time
get_response_time() {
    echo "$1" | grep "TIME_TOTAL:" | cut -d: -f2
}

# Function to extract JSON response (everything before HTTP_STATUS line)
get_json_response() {
    echo "$1" | sed '/HTTP_STATUS:/,$d'
}

# Function to check if response contains ParseError
has_parse_error() {
    local json="$1"
    if echo "$json" | grep -q "ParseError"; then
        return 0  # Has ParseError
    else
        return 1  # No ParseError
    fi
}

# Function to validate response (checks both HTTP status and ParseError)
validate_response() {
    local response="$1"
    local status=$(get_http_status "$response")
    local json=$(get_json_response "$response")
    
    if [[ "$status" != "200" ]]; then
        log_error "HTTP request failed (status: $status)"
        echo "$json"
        return 1
    fi
    
    if has_parse_error "$json"; then
        log_error "Response contains ParseError"
        echo "$json"
        return 1
    fi
    
    return 0  # Valid response
}