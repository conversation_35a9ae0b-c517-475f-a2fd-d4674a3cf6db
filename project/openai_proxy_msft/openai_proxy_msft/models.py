"""Type definitions for OpenAI Proxy service."""

from enum import Enum
from typing import Annotated, Any, Dict, List, Optional, Union

from pydantic import BaseModel, ConfigDict, Field


class OpenAICompatibleModel(BaseModel):
    """Base model with OpenAI-compatible serialization that excludes None values."""

    model_config = ConfigDict(
        # Exclude None values from serialization to maintain OpenAI compatibility
        use_enum_values=True,
    )

    def model_dump(self, **kwargs):
        """Custom serialization that excludes None values."""
        # Set exclude_none=True by default for OpenAI compatibility
        kwargs.setdefault("exclude_none", True)
        return super().model_dump(**kwargs)


class ToolCall(OpenAICompatibleModel):
    """Represents a tool call made by the model."""

    id: str = Field(..., description="The ID of the tool call")
    type: str = Field(default="function", description="The type of the tool call")
    function: Dict[str, Any] = Field(..., description="The function that the model called")


class ChatMessage(OpenAICompatibleModel):
    """Represents a message in a chat conversation."""

    role: str = Field(..., description="The role of the message author")
    content: Optional[str] = Field(default=None, description="The content of the message")
    tool_calls: Optional[List[ToolCall]] = Field(
        default=None, description="The tool calls generated by the model"
    )
    tool_call_id: Optional[str] = Field(
        default=None, description="Tool call that this message is responding to"
    )
    id: Optional[str] = Field(default=None, description="Message ID")
    cot_id: Optional[str] = Field(default=None, description="ID for the COT content")
    cot_summary: Optional[str] = Field(default=None, description="Summary of the COT reasoning")
    annotations: List[Any] = Field(default_factory=list, description="OpenAI message annotations")
    refusal: Optional[str] = Field(default=None, description="OpenAI refusal field")


class ToolChoiceEnum(str, Enum):
    """
    OpenAI-compatible tool choice enumeration.

    These values control how the model interacts with function calling:
    - NONE: Disable all tool use, force direct user response
    - AUTO: Allow model to decide whether and which tools to use (default)
    - REQUIRED: Force the model to use some tool, but let it choose which
    """

    NONE = "none"
    AUTO = "auto"
    REQUIRED = "required"


class SpecificToolChoice(OpenAICompatibleModel):
    """
    Represents a specific function that must be called.

    Used with tool_choice={"type": "function", "function": {"name": "func_name"}}
    to force the model to call exactly one specific function.
    """

    type: str = "function"
    function: Dict[str, str]  # {"name": "function_name"}


ToolChoice = Annotated[
    SpecificToolChoice | ToolChoiceEnum,
    Field(discriminator=None),
]


class ChatCompletionRequest(OpenAICompatibleModel):
    """Request model for chat completions."""

    model: str = Field(..., description="ID of the model to use")
    messages: List[ChatMessage] = Field(
        ..., description="A list of messages comprising the conversation"
    )
    tools: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="A list of tools the model may call"
    )
    tool_choice: ToolChoice = Field(
        default=ToolChoiceEnum.AUTO,
        description="Controls which (if any) tool is called by the model. "
        "Can be 'none', 'auto', 'required', or {'type': 'function', 'function': {'name': 'func_name'}}",
    )
    stream: bool = Field(default=False, description="Whether to stream back partial progress")
    temperature: float = Field(default=1.0, description="Sampling temperature")
    max_tokens: Optional[int] = Field(
        default=None, description="Maximum number of tokens to generate"
    )
    top_p: float = Field(default=1.0, description="Nucleus sampling parameter")
    n: int = Field(default=1, description="Number of chat completion choices to generate")
    stop: Optional[List[str]] = Field(
        default=None, description="Up to 4 sequences where the API will stop generating"
    )


class ChatCompletionChoice(OpenAICompatibleModel):
    """Represents a choice in a chat completion response."""

    index: int
    message: Optional[ChatMessage] = None
    delta: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None
    content_filter_results: Dict[str, Any] = Field(
        default_factory=dict, description="OpenAI content filter results"
    )
    logprobs: Optional[Any] = Field(default=None, description="OpenAI log probabilities")


class ChatCompletionUsage(OpenAICompatibleModel):
    """Usage statistics for a chat completion."""

    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponse(OpenAICompatibleModel):
    """Response model for chat completions."""

    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
    usage: Optional[ChatCompletionUsage] = None
    system_fingerprint: Optional[str] = Field(default=None, description="OpenAI system fingerprint")


class ChatCompletionStreamResponse(OpenAICompatibleModel):
    """Response model for streaming chat completions."""

    id: str
    object: str = "chat.completion.chunk"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
