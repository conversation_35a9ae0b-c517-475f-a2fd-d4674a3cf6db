#!/usr/bin/env python
"""
FastAPI proxy service for all OpenAI compatible clients to support local model API calls.

This service provides an OpenAI-compatible API interface that internally uses
BusTokenCompleter to communicate with local model engines running in pods.

Usage:
    python proxy.py

The service will start on http://localhost:8500/v1 and provide:
    - POST /v1/chat/completions (OpenAI compatible)
"""

import json
import logging
import time
import urllib.parse
import uuid
from typing import Any, AsyncGenerator, Dict, List, Optional, Tuple, Union

from bus_token_completer import BusTokenCompleter, QoSType
from chat import chat

# Import required components from the existing codebase
from chat.render.renderer_registry import get_renderer
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from message_completer.message_completer import ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter
from openai_proxy_msft.models import (
    ChatCompletionChoice,
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatCompletionStreamResponse,
    ChatCompletionUsage,
    ChatMessage,
    SpecificToolChoice,
    ToolCall,
    ToolChoiceEnum,
)
from openai_proxy_msft.utils import convert_openai_tools_to_tools_section

# Add these imports for constrained extension support
from qstar.common.tools import constraint_machine_spec
from qstar.common.tools.renderer_worker import (
    get_disc_score_token,
    get_end_of_tool_message_token,
    get_end_of_turn_token,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# constants
DEFAULT_JUICE = 128


# FastAPI app
app = FastAPI(
    title="OpenAI Proxy",
    description="OpenAI-compatible API for local model engines",
    version="1.0.0",
)


class OpenAIProxy:
    """Main proxy service class that handles OpenAI API compatibility."""

    def __init__(self):
        # Cache for initialized components per configuration
        self._component_cache = {}

    def _get_render_constrained_extension(self, renderer_name: str, tools: tuple):
        """Get the constrained extension for the renderer."""
        # Ensure tools is a tuple for hashability
        if not isinstance(tools, tuple):
            tools = tuple(tools)

        extension = constraint_machine_spec.constraint_machine_extension(
            harmony_renderer_name=renderer_name,
            encoding_name="orion_200k",
            final_token_sequences=frozenset(
                [
                    (get_end_of_tool_message_token(renderer_name),),
                    (get_end_of_turn_token(renderer_name), get_disc_score_token(renderer_name)),
                ]
            ),
            json_eot_tokens=frozenset(
                [
                    get_end_of_tool_message_token(renderer_name),
                    get_end_of_turn_token(renderer_name),
                ]
            ),
            tools=tools,
            channels=("analysis", "final"),
            response_format_names=(),
            enable_json_constraining=False,
            none_channel_allowed=False,
        )
        return extension

    def _extract_function_names_from_tools(self, tools_data: List[Dict[str, Any]]) -> tuple:
        """Extract function names from tools data for constrained extension."""
        if not tools_data:
            return tuple()

        function_names = []

        # Process each tool to extract function names
        for tool in tools_data:
            if isinstance(tool, dict) and "function" in tool:
                function_data = tool["function"]
                function_name = function_data.get("name", "unknown")
                function_names.append(f"functions.{function_name}")

        return tuple(function_names)

    def _get_components(
        self,
        model: str,
        temperature: float,
        tools: Optional[List[Dict[str, Any]]] = None,
        use_constrained: bool = False,
    ):
        """Get or create components for the given configuration."""
        topic, renderer, _ = parse_model(model)

        # Include constrained flag in cache key if tools are present
        cache_key_parts = [topic, renderer, str(temperature)]
        if tools and use_constrained:
            # Add a hash of the tools to the cache key for constrained configurations
            tools_hash = hash(str(sorted([t.get("function", {}).get("name", "") for t in tools])))
            cache_key_parts.append(f"constrained_{tools_hash}")
        cache_key = ":".join(cache_key_parts)

        if cache_key not in self._component_cache:
            try:
                # Get the renderer
                renderer_obj = get_renderer(renderer)
                logger.info(f"Initialized renderer: {renderer}")

                # Configure BusTokenCompleter
                bus_tc_config = BusTokenCompleter.Config(
                    topic_or_snapshot=topic,
                    qos_type=QoSType.ROUND_ROBIN_BY_POD,
                )

                # Configure TokenMessageCompleter
                completion_params = {"temperature": temperature}

                # Add constrained extension if requested and tools are provided
                if tools and use_constrained:
                    valid_function_tool_names = self._extract_function_names_from_tools(tools)
                    if valid_function_tool_names:
                        extension = self._get_render_constrained_extension(
                            renderer, valid_function_tool_names
                        )
                        completion_params["extensions"] = [extension]
                        logger.info(
                            f"Added constrained extension for tools: {valid_function_tool_names}"
                        )
                    else:
                        logger.info(
                            f"No valid function names found, using default. (has tools but no valid function names)"
                        )
                else:
                    logger.info(f"No valid function names found, using default. (no tools)")

                message_completer_config = TokenMessageCompleter.Config(
                    token_completer_config=bus_tc_config,
                    completion_params=completion_params,
                    renderer=renderer_obj,
                )

                # Build the message completer
                message_completer = message_completer_config.build()

                self._component_cache[cache_key] = {
                    "renderer": renderer_obj,
                    "message_completer": message_completer,
                }

                logger.info(f"Successfully initialized components for {cache_key}")

            except Exception as e:
                logger.error(f"Failed to initialize components for {cache_key}: {e}")
                raise

        return self._component_cache[cache_key]

    def _extract_content_and_recipient(self, chat_message):
        """Extract content from a chat message, handling different content types."""
        try:
            content = getattr(chat_message, "content", None)
            if content is not None and content.strip():
                return content, "all"
            tool_calls = getattr(chat_message, "tool_calls", None)
            if tool_calls and len(tool_calls) > 0:
                first_tool_call = tool_calls[0]
                function = getattr(first_tool_call, "function", None)
                if function and isinstance(function, dict):
                    arguments = function.get("arguments", "")
                    if arguments:
                        return arguments, function.get("name", "all")
            return "", "all"
        except (AttributeError, TypeError, IndexError):
            return "", "all"

    def _find_author_name(self, tool_call_id, messages):
        for msg in messages:
            if not msg.tool_calls:
                continue
            for tool_call in msg.tool_calls:
                if tool_call.id == tool_call_id:
                    return tool_call.function.get("name")
        return None

    def _convert_openai_messages_to_conversation(
        self,
        messages: List[ChatMessage],
        tools: Optional[List[Dict[str, Any]]] = None,
        juice: int = DEFAULT_JUICE,
    ) -> chat.Conversation:
        """Convert OpenAI format messages to internal conversation format."""
        chat_messages = []

        # Handle system message separately - use the first system message or create default
        system_message = None
        user_messages = []

        for msg in messages:
            if msg.role == "system" and system_message is None:
                system_message = msg.content
            elif msg.role in ["user", "assistant", "tool"]:
                user_messages.append(msg)

        # Create system message with proper format
        if system_message is None:
            system_message = "You are ChatGPT, a large language model trained by OpenAI."

        # Convert tools to tools_section format
        tools_section = convert_openai_tools_to_tools_section(tools)

        # Only set channel_config if we have tools
        if tools_section:
            chat_messages.append(
                chat.Message.system(
                    model_identity_desc=system_message,
                    tools_section=tools_section,
                    channel_config=chat.SystemChannelConfig(
                        valid_channels=("analysis", "final"), channel_required=True
                    ),
                    metadata=chat.SystemContentMetadata(reward_multiplier=juice),
                )
            )
        else:
            chat_messages.append(
                chat.Message.system(
                    model_identity_desc=system_message,
                    metadata=chat.SystemContentMetadata(reward_multiplier=juice),
                )
            )

        # Add user and assistant messages
        for msg in user_messages:
            if msg.role == "user":
                chat_messages.append(chat.Message.user(msg.content))
            elif msg.role == "assistant":
                content, recipient = self._extract_content_and_recipient(msg)
                chat_messages.append(chat.Message.assistant(content=content, recipient=recipient))
            elif msg.role == "tool":
                author_name = self._find_author_name(msg.tool_call_id, user_messages)
                chat_messages.append(chat.Message.tool(msg.content, author_name))

        # Create conversation
        convo = chat.Conversation(messages=chat_messages)
        logger.info(f"convo: {convo}")

        # Set budget overrides
        convo.metadata.header_yields_budget_total_override = 256
        convo.metadata.header_yields_budget_for_action = 256

        return convo

    def _process_tool_choice(
        self,
        tool_choice: Optional[Union[str, Dict[str, Any]]],
        tools: Optional[List[Dict[str, Any]]],
        use_constrained: bool,
    ) -> Tuple[bool, Optional[str], bool]:
        """
        Convert OpenAI tool_choice parameter to harmony renderer completion parameters.

        The tool_choice parameter controls how the model interacts with function calling,
        affecting three key aspects of completion generation:
        - should_end_header: Whether to skip the harmony decision-making phase
        - recipient: Force-set the function recipient (for specific tool calls)
        - require_recipient: Whether the model must call some tool

        Returns:
            (should_end_header, recipient, require_recipient)
        """
        # Default to direct user response (safest fallback)
        should_end_header = True
        recipient = None
        require_recipient = False

        # Without tools, there's nothing to choose - force direct response
        if not tools:
            return True, None, False

        # Normalize tool_choice to handle various input formats
        if tool_choice is None:
            # OpenAI default: let model decide whether to use tools
            tool_choice = ToolChoiceEnum.AUTO
        elif isinstance(tool_choice, str):
            try:
                tool_choice = ToolChoiceEnum(tool_choice)
            except ValueError:
                # Invalid string values fallback to auto (most permissive)
                tool_choice = ToolChoiceEnum.AUTO
        elif isinstance(tool_choice, SpecificToolChoice):
            # Handle specific function selection using SpecificToolChoice object
            if tool_choice.type == "function" and tool_choice.function:
                func_name = tool_choice.function.get("name")
                if func_name:
                    # Force specific function call by setting recipient and ending header
                    # This bypasses the model's decision-making process entirely
                    recipient = f"functions.{func_name}"
                    should_end_header = True  # Skip harmony header since decision is predetermined
                    require_recipient = False  # Not needed when recipient is explicitly set
                    return should_end_header, recipient, require_recipient
            # Invalid dict format falls back to auto
            tool_choice = ToolChoiceEnum.AUTO

        # Apply the core tool_choice business logic
        if tool_choice == ToolChoiceEnum.NONE:
            # "none" forces a user-facing message by bypassing all tool considerations
            # This is the simplest case - treat as if no tools exist
            should_end_header = True
            recipient = None
            require_recipient = False

        elif tool_choice == ToolChoiceEnum.AUTO:
            # "auto" allows the model to decide whether to use tools
            # The model needs the header generation phase to make this decision
            # regardless of constrained mode
            should_end_header = False
            recipient = None
            require_recipient = False

        elif tool_choice == ToolChoiceEnum.REQUIRED:
            # "required" forces tool use but lets model choose which tool
            # Always need the header phase for the model to make this decision
            should_end_header = False
            recipient = None
            require_recipient = True  # Enforce that some tool must be called

        return should_end_header, recipient, require_recipient

    def _get_effective_tools(
        self,
        tools: Optional[List[Dict[str, Any]]],
        tool_choice: Optional[Union[str, Dict[str, Any]]],
    ) -> Optional[List[Dict[str, Any]]]:
        """
        Filter tools based on tool_choice to optimize constrained sampling performance.

        This filtering happens before component initialization to avoid unnecessary
        constraint generation for unused tools. The constrained sampling system
        builds constraints for all provided tools, so removing unused tools early
        reduces computational overhead and potential parsing conflicts.
        """
        if not tools:
            return None

        # tool_choice="none" disables function calling entirely
        # Return None to signal that no tools should be available to the model
        if tool_choice == ToolChoiceEnum.NONE or tool_choice == "none":
            return None

        # Specific tool choice requires filtering to only the requested function
        # This is crucial for constrained sampling performance - providing only
        # the needed tool prevents the model from generating invalid tool calls
        if isinstance(tool_choice, SpecificToolChoice):
            # SpecificToolChoice has type and function attributes
            if tool_choice.type == "function" and tool_choice.function:
                func_name = tool_choice.function.get("name")
                if func_name:
                    # Filter to only the matching tool definition
                    matching_tools = [
                        tool
                        for tool in tools
                        if (
                            tool.get("type") == "function"
                            and tool.get("function", {}).get("name") == func_name
                        )
                    ]
                    # Return None if requested tool doesn't exist (fails gracefully)
                    return matching_tools if matching_tools else None

        # For "auto", "required", or unrecognized choices, provide all tools
        # This gives maximum flexibility to the model's decision-making process
        return tools

    # Extract text content from a message, will use it later
    def _extract_content_text(self, message) -> str:
        """Extract text content from a message, handling different content types."""
        if hasattr(message, "content") and message.content:
            content = message.content

            # Check for specific content attributes
            if hasattr(content, "text"):
                # Direct text attribute (e.g., Code objects)
                return content.text
            elif hasattr(content, "parts"):
                # Text objects with parts
                return "".join(str(part) for part in content.parts)
            elif hasattr(content, "__str__"):
                # Has string representation
                return str(content)
            else:
                # Last resort: repr
                return repr(content)
        return ""

    async def _generate_completion(
        self, request: ChatCompletionRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate completion using BusTokenCompleter."""
        try:
            # Parse model to get extra parameters
            topic, renderer, extra_params = parse_model(request.model)

            # Check if constrained extension should be used
            use_constrained = extra_params.get("constrained", False)
            if isinstance(use_constrained, str):
                use_constrained = use_constrained.lower() == "true"

            # Pre-filter tools to optimize constrained sampling and ensure coherent behavior
            # This must happen before component initialization since the constraint system
            # builds parsing rules based on the provided tool definitions
            effective_tools = self._get_effective_tools(request.tools, request.tool_choice)

            # Initialize components with the filtered tool set
            # Component caching is keyed by tool hash, so different tool_choice values
            # that result in different effective tools will use separate cached instances
            components = self._get_components(
                request.model,
                request.temperature,
                tools=effective_tools,
                use_constrained=use_constrained,
            )
            message_completer = components["message_completer"]

            # Build conversation with effective tools, not original tools
            # This ensures the system message contains only the tools the model should consider
            juice = extra_params.get("juice", DEFAULT_JUICE)
            convo = self._convert_openai_messages_to_conversation(
                request.messages, effective_tools, juice=juice
            )

            # Determine completion behavior based on tool_choice requirements
            # These parameters directly control the harmony renderer's decision-making process
            completion_params = {
                "conversations": [convo],
                "n": request.n if request.n is not None else 1,
                "seed": 0,
            }

            # Convert OpenAI tool_choice semantics to harmony completion parameters
            should_end_header, recipient, require_recipient = self._process_tool_choice(
                request.tool_choice, effective_tools, use_constrained
            )

            logger.info(
                f"tool_choice: {request.tool_choice}, should_end_header: {should_end_header}, recipient: {recipient}, require_recipient: {require_recipient}, use_constrained: {use_constrained}"
            )

            # Apply the harmony-specific parameters that enforce tool_choice behavior
            if should_end_header:
                # Skip harmony header generation when tool choice is predetermined
                completion_params["end_header"] = True
            else:
                completion_params["end_header"] = False

            if recipient:
                # Force specific function call by setting recipient directly
                completion_params["recipient"] = recipient
            if require_recipient:
                # Enforce that model must call some tool (used with tool_choice="required")
                completion_params["require_recipient"] = True

            completion = await message_completer.async_completion(**completion_params)

            choice = completion.choices[0]
            messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)

            logger.info(
                f"Raw message count: {len(messages)}, last message end_turn: {messages[-1].end_turn if messages else None}"
            )
            for i, msg in enumerate(messages):
                logger.info(
                    f"Message {i}: channel={getattr(msg, 'channel', 'N/A')}, recipient={getattr(msg, 'recipient', 'N/A')}, end_turn={getattr(msg, 'end_turn', 'N/A')}"
                )
            logger.debug(f"Raw messages: {messages}")

            openai_response = convert_messages_to_openai_format(
                messages, f"chatcmpl-{uuid.uuid4().hex[:29]}", "temp-model"
            )
            choice_data = openai_response["choices"][0]
            message_data = choice_data["message"]

            # Extract data for our format
            content = message_data.get("content")
            tool_calls = []
            if "tool_calls" in message_data and message_data["tool_calls"]:
                for tc in message_data["tool_calls"]:
                    tool_call = ToolCall(id=tc["id"], type=tc["type"], function=tc["function"])
                    tool_calls.append(tool_call)

            cot_summary = message_data.get("cot_summary", "")
            cot_id = message_data.get("cot_id")
            usage = openai_response["usage"]
            finish_reason = choice_data["finish_reason"]

            # Yield the complete response data with COT information and usage
            yield {
                "content": content if content and not tool_calls else None,
                "tool_calls": tool_calls if tool_calls else None,
                "finish_reason": finish_reason,
                "cot_summary": cot_summary,
                "cot_id": cot_id,
                "usage": usage,
            }

        except Exception as e:
            logger.error(f"Error generating completion: {e}")
            raise HTTPException(status_code=500, detail=f"Completion generation failed: {str(e)}")


def parse_model(model: str) -> tuple[str, str, Dict[str, Any]]:
    """Parse model string in format 'topic$renderer' or 'topic$renderer?param1=value1&param2=value2'.

    Note: The topic parameter should be provided without the 'az://' prefix.

    Examples:
    - Input: 'bus:snap:orngcresco/twapi/mini/e/yunshengli-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-rrbif-v1/policy/step_000300:user:xuga$harmony_v4.0.16'
    - Output: ('bus:snap:orngcresco/twapi/mini/e/yunshengli-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-rrbif-v1/policy/step_000300:user:xuga', 'harmony_v4.0.16', {})

    - Input: 'bus:snap:model$harmony_v4.0.16?juice=2.0&constrained=true'
    - Output: ('bus:snap:model', 'harmony_v4.0.16', {'juice': 2.0, 'constrained': True})
    """
    try:
        # Split from the right to get renderer first
        if "$" not in model:
            raise ValueError(
                f"Invalid model format. Expected 'topic$renderer[?params]', got '{model}'"
            )

        # Find the last $ to separate renderer
        last_dollar_idx = model.rfind("$")
        if last_dollar_idx == -1:
            raise ValueError(
                f"Invalid model format. Expected 'topic$renderer[?params]', got '{model}'"
            )

        topic = model[:last_dollar_idx]
        renderer_and_params = model[last_dollar_idx + 1 :]

        # Check if there are query parameters
        extra_params = {}
        if "?" in renderer_and_params:
            renderer, params_str = renderer_and_params.split("?", 1)

            # Parse query parameters
            if params_str:
                parsed_params = urllib.parse.parse_qs(params_str, keep_blank_values=True)

                # Convert single-value lists to single values and attempt type conversion
                for key, values in parsed_params.items():
                    if len(values) == 1:
                        value = values[0]
                        # Try to convert to appropriate type
                        try:
                            # Try float first (covers int as well)
                            if "." in value:
                                extra_params[key] = float(value)
                            else:
                                extra_params[key] = int(value)
                        except ValueError:
                            # Try boolean
                            if value.lower() in ("true", "false"):
                                extra_params[key] = value.lower() == "true"
                            else:
                                # Keep as string
                                extra_params[key] = value
                    else:
                        # Keep as list if multiple values
                        extra_params[key] = values
        else:
            renderer = renderer_and_params

        # Validate that none of the parts are empty
        if not topic or not renderer:
            raise ValueError(
                f"All model parameters must be non-empty. Got topic='{topic}', renderer='{renderer}'"
            )

        # Clean up whitespace
        topic = topic.strip()
        renderer = renderer.strip()

        logger.info(
            f"Parsed model parameters - topic: {topic}, renderer: {renderer}, extra_params: {extra_params}"
        )

        return topic, renderer, extra_params

    except Exception as e:
        logger.error(f"Failed to parse model parameters '{model}': {e}")
        raise HTTPException(status_code=400, detail=f"Invalid model parameters: {model}")


# Initialize the proxy service
proxy = OpenAIProxy()


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "service": "openai-proxy", "version": "1.0.0"}


@app.get("/v1/models")
async def list_models():
    """List available models (OpenAI compatible)."""
    return {
        "object": "list",
        "data": [
            {
                "id": "bus",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "openai-proxy",
            }
        ],
    }


@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    """Create a chat completion (OpenAI compatible)."""
    completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
    created = int(time.time())

    logger.info(f"user: {request.messages[-1]}")

    if request.stream:
        return StreamingResponse(
            _stream_chat_completion(completion_id, created, request),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            },
        )
    else:
        return await _non_stream_chat_completion(completion_id, created, request)


async def _stream_chat_completion(
    completion_id: str,
    created: int,
    request: ChatCompletionRequest,
):
    """Generate streaming chat completion response."""
    try:
        # Send initial chunk with role
        initial_chunk = ChatCompletionStreamResponse(
            id=completion_id,
            created=created,
            model=request.model,
            choices=[
                ChatCompletionChoice(index=0, delta={"role": "assistant"}, finish_reason=None)
            ],
        )
        yield f"data: {json.dumps(initial_chunk.model_dump())}\n\n"

        # Get completion data
        response_data = None
        async for data in proxy._generate_completion(request):
            response_data = data
            break  # We get all data in one go now

        if response_data:
            # Send tool calls if available
            if response_data.get("tool_calls"):
                tool_calls_chunk = ChatCompletionStreamResponse(
                    id=completion_id,
                    created=created,
                    model=request.model,
                    choices=[
                        ChatCompletionChoice(
                            index=0,
                            delta={
                                "tool_calls": [
                                    tc.model_dump() for tc in response_data["tool_calls"]
                                ]
                            },
                            finish_reason=None,
                        )
                    ],
                )
                yield f"data: {json.dumps(tool_calls_chunk.model_dump())}\n\n"

            # Send content if available
            elif response_data.get("content"):
                content_chunk = ChatCompletionStreamResponse(
                    id=completion_id,
                    created=created,
                    model=request.model,
                    choices=[
                        ChatCompletionChoice(
                            index=0, delta={"content": response_data["content"]}, finish_reason=None
                        )
                    ],
                )
                yield f"data: {json.dumps(content_chunk.model_dump())}\n\n"

            # Send final chunk with finish reason
            final_chunk = ChatCompletionStreamResponse(
                id=completion_id,
                created=created,
                model=request.model,
                choices=[
                    ChatCompletionChoice(
                        index=0, delta={}, finish_reason=response_data.get("finish_reason", "stop")
                    )
                ],
            )
        else:
            # Fallback final chunk
            final_chunk = ChatCompletionStreamResponse(
                id=completion_id,
                created=created,
                model=request.model,
                choices=[ChatCompletionChoice(index=0, delta={}, finish_reason="stop")],
            )

        yield f"data: {json.dumps(final_chunk.model_dump())}\n\n"
        yield "data: [DONE]\n\n"

    except Exception as e:
        logger.error(f"Error in streaming completion {completion_id}: {e}", exc_info=True)
        error_chunk = {
            "error": {"message": str(e), "type": "server_error", "code": "internal_error"}
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"


async def _non_stream_chat_completion(
    completion_id: str,
    created: int,
    request: ChatCompletionRequest,
):
    """Generate non-streaming chat completion response."""
    try:
        # Get completion data
        response_data = None
        async for data in proxy._generate_completion(request):
            response_data = data
            break  # We get all data in one go now

        if not response_data:
            response_data = {
                "content": "",
                "tool_calls": None,
                "finish_reason": "stop",
                "cot_summary": "",
                "cot_id": None,
                "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0},
            }

        # Create the response message
        message = ChatMessage(
            role="assistant",
            content=response_data.get("content"),
            tool_calls=response_data.get("tool_calls"),
            id=str(uuid.uuid4()),
            cot_id=response_data.get("cot_id"),
            cot_summary=response_data.get("cot_summary"),
        )

        # Use the usage data from _generate_completion (now using reference implementation)
        usage_data = response_data.get(
            "usage", {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
        )

        response = ChatCompletionResponse(
            id=completion_id,
            created=created,
            model=request.model,
            choices=[
                ChatCompletionChoice(
                    index=0,
                    message=message,
                    finish_reason=response_data.get("finish_reason", "stop"),
                )
            ],
            usage=ChatCompletionUsage(
                prompt_tokens=usage_data["prompt_tokens"],
                completion_tokens=usage_data["completion_tokens"],
                total_tokens=usage_data["total_tokens"],
            ),
        )

        return response.model_dump()

    except Exception as e:
        logger.error(f"Error in non-streaming completion: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def convert_messages_to_openai_format(messages, completion_id, model_name):
    """Convert to OpenAI API format"""
    if not messages:
        choices = [
            {
                "content_filter_results": {},
                "finish_reason": "stop",
                "index": 0,
                "logprobs": None,
                "message": {
                    "id": completion_id,
                    "annotations": [],
                    "content": "Error: No valid response generated",
                    "refusal": None,
                    "role": "assistant",
                },
            }
        ]
        usage = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
    else:
        # Find the last tool call message (code content type)
        tool_call_message = None
        analysis_text_parts = []
        if messages[-1].end_turn:
            is_last_step = True
        else:
            is_last_step = False

        logger.info(
            f"Conversation state: is_last_step={is_last_step}, last_message_end_turn={messages[-1].end_turn}"
        )
        logger.info(
            f"All messages end_turn status: {[(i, hasattr(msg, 'end_turn'), getattr(msg, 'end_turn', 'N/A')) for i, msg in enumerate(messages)]}"
        )

        # Extract token usage information from messages metadata
        prompt_tokens = 0
        completion_tokens = 0

        # Collect analysis channel text content and find tool calls
        for message in messages:
            if hasattr(message, "content"):
                content_obj = message.content

                # Extract token usage from message metadata
                if hasattr(message, "metadata") and message.metadata:
                    if "message_content_token_count" in message.metadata:
                        completion_tokens += message.metadata["message_content_token_count"]
                    if "first_content_token_index" in message.metadata:
                        # The first_content_token_index indicates where content starts in the total token sequence
                        # This can help us estimate prompt tokens for the first message
                        if prompt_tokens == 0:  # Only set once for the first message
                            prompt_tokens = message.metadata["first_content_token_index"]

                # Collect analysis channel text for cot_summary
                if (
                    hasattr(message, "channel")
                    and message.channel == "analysis"
                    and hasattr(content_obj, "content_type")
                    and content_obj.content_type == "text"
                ):
                    if hasattr(content_obj, "parts"):
                        # Handle Text content with parts
                        text_parts = [str(part) for part in content_obj.parts]
                        analysis_text_parts.extend(text_parts)
                    elif hasattr(content_obj, "text"):
                        analysis_text_parts.append(content_obj.text)

                # Find tool call (code content)
                # if (hasattr(content_obj, 'content_type') and content_obj.content_type == 'code'):
                if (
                    hasattr(message, "recipient")
                    and message.recipient
                    and message.recipient != "all"
                ):
                    tool_call_message = message

        # Calculate total tokens
        total_tokens = prompt_tokens + completion_tokens
        usage = {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": total_tokens,
        }

        # Build cot_summary from analysis text
        cot_summary = " ".join(analysis_text_parts).strip() if analysis_text_parts else ""

        # Determine the primary response
        if tool_call_message:
            # Handle tool call message
            content_obj = tool_call_message.content

            # Get the actual text content
            if hasattr(content_obj, "text"):
                text_content = content_obj.text
            elif hasattr(content_obj, "content"):
                text_content = content_obj.content
            elif hasattr(content_obj, "__str__"):
                text_content = str(content_obj)
            else:
                text_content = repr(content_obj)

            try:
                tool_call_content = json.loads(text_content)
                function_name = (
                    tool_call_message.recipient.split(".")[-1]
                    if hasattr(tool_call_message, "recipient")
                    and "." in tool_call_message.recipient
                    else (
                        tool_call_message.recipient
                        if hasattr(tool_call_message, "recipient")
                        else "unknown"
                    )
                )
                finish_reason = "stop" if is_last_step else "tool_calls"
                logger.info(
                    f"Tool call response: is_last_step={is_last_step}, finish_reason={finish_reason}"
                )

                choice = {
                    "content_filter_results": {},
                    "finish_reason": finish_reason,
                    "index": 0,
                    "logprobs": None,
                    "message": {
                        "id": str(uuid.uuid4()),
                        "annotations": [],
                        "content": None,
                        "refusal": None,
                        "role": "assistant",
                        "tool_calls": [
                            {
                                "id": f"call_{str(uuid.uuid4()).replace('-', '')}",
                                "type": "function",
                                "function": {
                                    "name": function_name,
                                    "arguments": json.dumps(tool_call_content),
                                },
                            }
                        ],
                    },
                }

                # Updated: CoT is not needed for now, commented out.
                # CAUTION: The value set to `cot_summary` is actually CoT, not CoT summary.
                #          We need to re-think before exposing them to public API.
                #
                # Add COT fields only if they have content
                # if cot_summary:
                #     choice["message"]["cot_id"] = f"cot_{str(uuid.uuid4())}"
                #     choice["message"]["cot_summary"] = cot_summary
            except (json.JSONDecodeError, TypeError):
                # Fallback to text content if JSON parsing fails
                choice = {
                    "content_filter_results": {},
                    "finish_reason": "stop",
                    "index": 0,
                    "logprobs": None,
                    "message": {
                        "id": str(uuid.uuid4()),
                        "annotations": [],
                        "content": text_content,
                        "refusal": None,
                        "role": "assistant",
                    },
                }
        else:
            # No tool call found, use the last message or consolidated text
            if True:
                # Fall back to last message content
                message = messages[-1]
                content_obj = message.content

                if hasattr(content_obj, "text"):
                    content_text = content_obj.text
                elif hasattr(content_obj, "parts"):
                    content_text = "".join(str(part) for part in content_obj.parts)
                elif hasattr(content_obj, "__str__"):
                    content_text = str(content_obj)
                else:
                    content_text = repr(content_obj)

                # Check for system errors
                if (
                    hasattr(content_obj, "content_type")
                    and content_obj.content_type == "system_error"
                ):
                    content_text = f"Error: {content_text}"

            # the finish_reason of text responses always "stop"
            finish_reason = "stop"
            logger.info(
                f"Text response: is_last_step={is_last_step}, finish_reason={finish_reason}"
            )

            choice = {
                "content_filter_results": {},
                "finish_reason": finish_reason,
                "index": 0,
                "logprobs": None,
                "message": {
                    "id": str(uuid.uuid4()),
                    "annotations": [],
                    "content": content_text,
                    "refusal": None,
                    "role": "assistant",
                },
            }

            # Updated: CoT is not needed for now, commented out.
            # CAUTION: The value set to `cot_summary` is actually CoT, not CoT summary.
            #          We need to re-think before exposing them to public API.
            #
            # Add COT fields if available for non-tool-call responses
            # if cot_summary:
            #     choice["message"]["cot_id"] = f"cot_{str(uuid.uuid4())}"
            #     choice["message"]["cot_summary"] = cot_summary

        choices = [choice]

    return {
        "id": f"chatcmpl-{str(uuid.uuid4())}",
        "object": "chat.completion",
        "created": int(time.time()),
        "model": model_name,
        "choices": choices,
        "usage": usage,
        "system_fingerprint": None,
    }
