from typing import Any, Dict, List, Optional

from function_calling_tool import <PERSON><PERSON><PERSON><PERSON>ing<PERSON><PERSON>
from functions import TOP_LEVEL_FUNCTIONS_NAMESPACE, parse_functions


class CustomFunctionCalls(FunctionCallingTool):
    def __init__(self, function_definitions):
        self._function_namespace = parse_functions(function_definitions)

    @classmethod
    def get_tool_name(cls) -> str:
        # parse_functions use this namespace as default.
        # We did not do any customizations here, so return the default value.
        # The value is "functions" defined in functions/core.py.
        return TOP_LEVEL_FUNCTIONS_NAMESPACE

    def instruction(self) -> str:
        return self._function_namespace.to_typescript()

    def valid_recipients(self):
        # The recipient for a function call is like <namespace>.<function>
        # Below are some examples:
        #   functions.bash
        #   functions.str_replace_editor
        for func in self._function_namespace.functions:
            yield f"{self._function_namespace.name}.{func.name}"


def convert_openai_tools_to_tools_section(tools: Optional[List[Dict[str, Any]]]) -> Dict[str, str]:
    """Convert OpenAI format tools to internal tools_section format."""
    if not tools:
        return {}

    """
    the original tools is like: 
	[
        {
            "type": "function",
            "function": {
                "name": "bash",
                "description": "Runs a bash command in an interactive bash",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "description": "The bash command and arguments to run"
                        }
                    },
                    "required": [
                        "command"
                    ]
                }
            }
        }
    ]
    so we need to extract the function definitions from the tools list and pass them to the CustomFunctionCalls class.
    """
    function_calls_tool = CustomFunctionCalls(
        [t["function"] for t in tools if t["type"] == "function"]
    )

    return {"functions": function_calls_tool.instruction()}
