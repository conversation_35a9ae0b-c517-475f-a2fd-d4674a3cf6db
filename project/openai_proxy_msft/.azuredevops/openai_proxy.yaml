pool: "iridium-builder"

trigger:
  branches:
    include:
      - main
  paths:
    include:
      - project/openai_proxy_msft/**

pr:
  branches:
    include:
      - main
  paths:
    include:
      - project/openai_proxy_msft/Dockerfile

resources:
  repositories:
    - repository: bootstrapping
      type: git
      name: Mimco/bootstrapping
      refName: refs/heads/main

stages:
  - stage: FetchOAIArtifactWheels
    displayName: "Fetch oaiartifact wheels"
    # Disabled to save time by using a pre-built artifact. Set to true to regenerate.
    condition: false
    jobs:
      - job: FetchOAIArtifactWheels
        displayName: "Fetch oaiartifact wheels"
        pool: "orange-builder"
        steps:
          - checkout: self
            fetchDepth: 1
            fetchTags: false

          - template: .azuredevops/templates/orange-az-setup.yaml@bootstrapping

          - script: |
              az account set --subscription "92b5cfd5-0b8e-4007-a4dd-2d07c2b9998e"
            displayName: "Set subscription"

          - script: |
              set -eufx -o pipefail
              mkdir -p $BUILD_ARTIFACTSTAGINGDIRECTORY/encoders
              az storage blob download-batch \
                --pattern "encodings/*" \
                --destination $BUILD_ARTIFACTSTAGINGDIRECTORY/encoders \
                --account-name orngoaiartifacts \
                --source data-gym \
                --auth-mode login
            displayName: "Download encoders from Azure Storage"

          - script: |
              set -eufx -o pipefail
              mkdir -p $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels
              for PATTERN in "*cp312*linux_x86_64.whl" "*none-any.whl" "*py3*x86_64.whl"; do
                echo "Downloading blobs matching: $PATTERN"
                az storage blob download-batch \
                  --pattern "$PATTERN" \
                  --destination "$BUILD_ARTIFACTSTAGINGDIRECTORY/wheels" \
                  --account-name orngoaiartifacts \
                  --source wheels \
                  --auth-mode login
              done
              mkdir -p $BUILD_ARTIFACTSTAGINGDIRECTORY/public-wheels
              az storage blob download \
                --container-name wheels \
                --name public-wheels/connexion2/connexion2-2020.0.dev1-py2.py3-none-any.whl \
                --file $BUILD_ARTIFACTSTAGINGDIRECTORY/public-wheels/connexion2-2020.0.dev1-py2.py3-none-any.whl \
                --account-name orngoaiartifacts \
                --auth-mode login
            displayName: "Download orngoaiartifacts from Azure Storage"

          - script: |
              set -eufx -o pipefail
              sudo apt-get update
              sudo apt-get install -y python3 python3-venv python3-pip
              sudo ln -sf /usr/bin/python3 /usr/bin/python
            displayName: 'Install Python 3 (self-hosted agent)'

          - script: |
              set -eufx -o pipefail
              export OAIPKG_ARTIFACTS_BASE=az://orngoaiartifacts
              export BUILDKITE_AGENT_CACHE_HOST_DIR="$(Build.ArtifactStagingDirectory)/oaiwheelcache"
              export WHEEL_SRC_DIR="$(Build.ArtifactStagingDirectory)/wheels"
              python ./project/openai_proxy_msft/.azuredevops/copy_wheels.py
            displayName: Copy wheels to target folder

          - script: |
              set -eufx -o pipefail
              rm -frv $BUILD_ARTIFACTSTAGINGDIRECTORY/wheels
            displayName: "Remove original wheels"

          - task: PublishPipelineArtifact@1
            displayName: "Publish orngoaiartifacts as pipeline artifact"
            inputs:
              targetPath: "$(Build.ArtifactStagingDirectory)"
              artifact: "orngoaiartifacts"
              publishLocation: "pipeline"

  - stage: "PushToIridiumsdc"
    displayName: "Build and Push OpenAI Proxy Docker image to iridiumsdc"
    # We do not need to wait for the previous FetchOAIArtifactWheels stage to finish ss we skipped
    # the previous stage for now and use the artifacts from an existing build to save up time.
    # Here we explicitly declare no dependency to make this stage run even if the previous step is skipped.
    dependsOn: []
    jobs:
      - job: BuildAndPushOpenAIProxyImage
        displayName: "Build and push OpenAI Proxy Docker image"
        pool: "iridium-builder"
        steps:
          - template: .azuredevops/templates/az-setup.yaml@bootstrapping

          # Checkout the main repository
          - checkout: self
            fetchDepth: 1
            fetchTags: false

          # Checkout torchflow-mirror repository which contains install.py
          - checkout: git://Mimco/_git/torchflow-mirror@zhizhu/20250604-forward-integration
            fetchDepth: 1
            fetchTags: false

          - task: DownloadPipelineArtifact@2
            displayName: "Download Artifact (pipeline artifact)"
            inputs:
              buildType: 'specific'
              project: '$(System.TeamProjectId)'
              pipeline: '$(System.DefinitionId)' # Reference this pipeline itself
              buildVersionToDownload: 'specific'
              # Pinned to a specific build to save time instead of re-generating the artifact.
              # https://dev.azure.com/project-argos/Mimco/_build/results?buildId=1376302&view=results
              buildId: '1376302'
              artifact: "orngoaiartifacts"
              targetPath: "$(System.DefaultWorkingDirectory)/orngoaiartifacts"

          - script: |
              set -eufx -o pipefail
              export GLASS_REPO_DIR=$(realpath ./glass)
              export BUILD_CONTEXT_DIR=$(realpath .)

              # Login to Azure Container Registry
              az acr login --name iridiumsdc

              # Set Docker image names
              export DOCKER_IMAGE=iridiumsdc.azurecr.io/openai_proxy/openai_proxy_msft:$(Build.BuildId)
              export DOCKER_IMAGE_LATEST=iridiumsdc.azurecr.io/openai_proxy/openai_proxy_msft:latest
              export DOCKER_IMAGE_CACHE=iridiumsdc.azurecr.io/openai_proxy/openai_proxy_msft:buildcache
              export DOCKER_BUILDKIT=1

              # Build Docker image using parent directory as context (contains both glass and torchflow-mirror)
              docker buildx build \
                "$BUILD_CONTEXT_DIR" \
                -f "$GLASS_REPO_DIR/project/openai_proxy_msft/Dockerfile" \
                -t "$DOCKER_IMAGE" \
                -t "$DOCKER_IMAGE_LATEST" \
                --platform=linux/amd64 \
                --progress=plain \
                --push

              echo "Successfully built and pushed OpenAI Proxy Docker images:"
              echo "  - $DOCKER_IMAGE"
              echo "  - $DOCKER_IMAGE_LATEST"
            displayName: "Build and push OpenAI Proxy Docker image"
