import base64
import io
import re
import xml.etree.ElementTree as ET
from functools import cached_property
from typing import Sequence

import blobfile as bf
import chz
from bus_token_completer import BusTokenCompleter, QoSType
from chat import Conversation, Message, chat, render
from chat.render import get_renderer
from chat.render.common import render_content
from chat.render.renderer_base import BaseRenderer
from chat.render.v4.experimental.strawberry.formatter import Berry<PERSON>hannel
from dice_msft.prompts.grader import GRADER_XML_WITH_ICL, GRADER_XML_WITH_ICL_MM
from message_completer import TokenMessageCompleter
from PIL import Image
from qstar.common import datapoint, types, utils
from qstar.graders import answer_extraction
from qstar.graders import grader as grader_module
from qstar.sample_completers import sample_completer

# TODO: Remove
VIZ_RESULTS_PATH = "az://orngwus2cresco/data/shtri/dice/viz/results"


def image_to_base64(path: str, add_data_prefix: bool = False) -> str:
    """
    Open an image using blobfile and convert it to a base64 string.

    Args:
        path (str): Path to the image (local, gs://, s3://, etc.).
        add_data_prefix (bool): If True, adds 'data:image/xxx;base64,' prefix.

    Returns:
        str: Base64 encoded string of the image.
    """
    with bf.BlobFile(path, "rb") as f:
        img = Image.open(io.BytesIO(f.read()))
        img.load()  # Ensure image is fully loaded into memory

    buffered = io.BytesIO()
    format = img.format if img.format else "PNG"  # Default to PNG if format unknown
    img.save(buffered, format=format)
    img_bytes = buffered.getvalue()
    img_base64 = base64.b64encode(img_bytes).decode("utf-8")

    if add_data_prefix:
        mime_type = f"image/{format.lower()}"
        return f"data:{mime_type};base64,{img_base64}"

    return img_base64


def get_gt_content_asset_pointer(gt_answer: str):
    return [
        f"{VIZ_RESULTS_PATH}/{f}"
        for f in map(lambda x: x.strip().split("plots/")[-1], gt_answer.split(","))
        if not f.endswith("json")
    ]


def grade_xml_v1(
    convo: chat.Conversation,
    general_renderer: BaseRenderer,
    bus_grader: BusTokenCompleter,
) -> dict:
    cur_convo = convo.model_copy()

    tokens = general_renderer.render_for_completion_multimodal_toklist(
        cur_convo, role=chat.Role.ASSISTANT
    )
    [loaded_tokens] = [tokens]
    completion = "None"
    try:
        completions = bus_grader.completion(
            [loaded_tokens],
            max_tokens=(2**17 - 2**16),
            stop=general_renderer.stop_sequences(),  # type: ignore
            temperature=1.0,
        )

        output_messages = general_renderer.parse(
            completions.choices[0].toklist.spans[0].tokens, role=chat.Role.ASSISTANT
        )
        current_completion = render_content(output_messages, 0)
        if current_completion.startswith("```xml"):
            current_completion = current_completion[len("```xml") :]

        if current_completion.endswith("```"):
            current_completion = current_completion[:-3]

        def extract_score_reason(xml_string):
            # Normalize tags to standard <score> and <reason>
            xml_string = re.sub(
                r"<score_start>(.*?)<score_end>", r"<score>\1</score>", xml_string, flags=re.DOTALL
            )
            xml_string = re.sub(
                r"<reason_start>(.*?)<reason_end>",
                r"<reason>\1</reason>",
                xml_string,
                flags=re.DOTALL,
            )
            xml_string = re.sub(
                r"<score_start>(.*?)</score_start>",
                r"<score>\1</score>",
                xml_string,
                flags=re.DOTALL,
            )
            xml_string = re.sub(
                r"<reason_start>(.*?)</reason_start>",
                r"<reason>\1</reason>",
                xml_string,
                flags=re.DOTALL,
            )

            # Wrap with a root element if necessary
            if (
                "<final_answer>" not in xml_string.lower()
                and "<finalanswer>" not in xml_string.lower()
            ):
                xml_string = f"<root>{xml_string}</root>"
            root = ET.fromstring(xml_string)

            # Initialize default values
            score, reason = 0.0, None

            # Search for the tags in the XML structure
            if root.find(".//score") is not None:
                score = root.find(".//score").text.strip()
            # if root.find('.//reason') is not None:
            #     reason = root.find('.//reason').text.strip()

            return float(score)

        score = extract_score_reason(current_completion)
        completion = current_completion
    except Exception as e:
        print(f"{e=}")
        score = 0.0
        completion = "Exception"

    return {
        "score": score,
        "completion": completion,
    }


@chz.chz(typecheck=True)
class ICL4oGrader(grader_module.Grader[datapoint.MathgenDatapoint]):
    """
    Grader with "In-Context Learning" examples to reason and provide
    score comparing teacher vs student solutions.
    Response Format: XML with 2 fields; score, reason
    """

    extractor: answer_extraction.AnswerExtractor[str] = chz.field(
        doc="Answer extractor to use for extracting answers from samples.",
        default_factory=answer_extraction.ChannelExtractor,
    )

    @cached_property
    def _get_my_renderer(self) -> BaseRenderer:
        return get_renderer("harmony_v4.0.15_16k_orion_text_only_no_asr_2k_action")
        # return get_renderer("harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget")

    @cached_property
    def _get_grader_system_msg(self) -> chat.Message:
        return chat.Message.system(
            model_identity_desc="You are an intelligent AI grader used for comparing solutions for a given question. You can provide a floating point score between 0.0 to 1.0.",
            channel_config=chat.SystemChannelConfig(
                valid_channels=(BerryChannel.CHAIN_OF_THOUGHT, BerryChannel.FINAL_ANSWER),
                channel_required=True,
            ),
            metadata=chat.SystemContentMetadata(
                reward_multiplier=None,
            ),
        )

    @cached_property
    def _bus_grader(self) -> BusTokenCompleter:
        return BusTokenCompleter(
            topic_mode_or_user="chawlapranit",
            # topic_mode_or_user="appberry",
            topic_or_snapshot="az://orngcresco/models/snapshots/chat-gpt-4o-tt-v3-d64_80g_bf16_fridge-step3075-250316-decrypted",
            # topic_or_snapshot="az://orngcresco/models/snapshots/models.tc/nv4-mident-run2-20250310-decrypted",
            # topic_mode_or_user="msft",
            # topic_or_snapshot="az://orngcresco/models/snapshots/chat-gpt-4o-tt-v3-d64_80g_bf16_fridge-step3075-250316-decrypted",
            # topic_or_snapshot="az://orngcresco/models/snapshots/o3-0402-410-8shard-decrypted",
            bus_line="bus",
            qos_type=QoSType.FIFO,
        )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:
        given_answers = self._extract_answers(samples)
        gt_answers = [utils.not_none(sample.gt_datapoint.answer) for sample in samples]

        result: list[types.SampleWithGrade] = []
        try:
            for sample, given_answer, gt_answer in zip(
                samples, given_answers, gt_answers, strict=True
            ):
                if given_answer and sample.gt_datapoint.problem and self._bus_grader.is_ready():
                    if "viz" in sample.gt_datapoint.metadata.get("category", ""):
                        given_answer = []
                        temp_convo = sample.conversation
                        for i in range(len(temp_convo.messages) - 1, -1, -1):
                            if temp_convo.messages[i].content.content_type == "multimodal_text":
                                # for part in temp_convo.messages[i].content.parts:
                                #     if isinstance(part, chat.Image):
                                #         given_answer.append(part.get_base64())
                                asset_pointer = (
                                    temp_convo.messages[i].content.parts[-1].asset_pointer
                                )
                                base64_str = image_to_base64(asset_pointer)
                                given_answer.append(base64_str)
                                # break
                        gt_answer = [
                            image_to_base64(ap) for ap in get_gt_content_asset_pointer(gt_answer)
                        ]

                        user_prompt = (
                            GRADER_XML_WITH_ICL_MM.replace(
                                "{problem}", sample.gt_datapoint.metadata.get("question", "")
                            )
                            + """Teacher solution:
                        <teacher_solution_start>
                        """
                        )

                        message_parts = (
                            [user_prompt]
                            + [
                                chat.Image(
                                    encoding="base64", payload=gt_ans, format="png", fovea=1080
                                )
                                for gt_ans in gt_answer
                            ]
                            + [
                                """<teacher_solution_end>

                        Student solution:
                        <student_solution_start>
                        """
                            ]
                            + [
                                chat.Image(encoding="base64", payload=ans, format="png", fovea=1080)
                                for ans in given_answer
                            ]
                            + [
                                """<student_solution_end>

                        Final Answer:"""
                            ]
                        )

                        grader_convo = Conversation(
                            messages=[
                                self._get_grader_system_msg,
                                chat.Message(
                                    role=chat.Role.USER,
                                    content=chat.MultimodalText(parts=message_parts),
                                ),
                            ]
                        )
                    else:
                        user_query = (
                            GRADER_XML_WITH_ICL.replace(
                                "{problem}", sample.gt_datapoint.metadata.get("question", "")
                            )
                            .replace("{canonical_solution}", gt_answer)
                            .replace("{student_answer}", given_answer)
                        )
                        # given_answer = chat.Message.user(prompt)
                        user_prompt = chat.Message.user(user_query)
                        grader_convo = Conversation(
                            messages=[self._get_grader_system_msg] + [user_prompt]
                        )

                    grader_response = grade_xml_v1(
                        convo=grader_convo,
                        general_renderer=self._get_my_renderer,
                        bus_grader=self._bus_grader,
                    )

                    grader_metadata = {
                        "problem": sample.gt_datapoint.metadata.get("question", ""),
                        "reference": gt_answer,
                        "output": given_answer,
                        "response": grader_response,
                    }

                    # completion_message = chat.Message.assistant(given_answer)
                    result.append(
                        sample.with_correctness(
                            reward_name="dice_grader_v2",
                            is_correct=grader_response["score"] >= 0.99,
                            given_answer=given_answer,
                            telemetry_metadata=grader_metadata,
                            additional_metadata={
                                "sample_metadata": sample.gt_datapoint.metadata,
                                **grader_metadata,
                            },
                        )
                    )
        except Exception as e:
            result = [
                sample.with_correctness(
                    reward_name="dice_grader_exception",
                    is_correct=0.0,
                    given_answer=given_answer,
                    telemetry_metadata={
                        "ground_truth_answer": gt_answer,
                        "reference_answer": given_answer,
                        "exception": {
                            "type": type(e).__name__,
                            "message": str(e),
                            "args": e.args,
                        },
                        "sample_metadata": sample.gt_datapoint.metadata,
                    },
                )
                for sample, given_answer, gt_answer in zip(
                    samples, given_answers, gt_answers, strict=True
                )
            ]

        return result

    def _extract_answers(self, samples: Sequence[types.SampleWithCompletion]) -> list[str | None]:
        answers: list[str | None] = []
        for sample in samples:
            assert len(self.channels_for_answer) == 1, "Only one channel for answer is supported"
            extraction_result = self.extractor.extract_answer(
                sample, channel_for_answer=self.channels_for_answer[0]
            )
            if isinstance(extraction_result, answer_extraction.FailedExtraction):
                answer = None
                sample.metadata.update({"extraction_errors": extraction_result.errors})
            else:
                answer = extraction_result.answer
                sample.metadata.update(extraction_result.metadata)
            answers.append(answer)
        return answers
