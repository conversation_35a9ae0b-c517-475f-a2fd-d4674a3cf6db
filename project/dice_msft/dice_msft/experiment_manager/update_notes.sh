#!/bin/bash

# Usage: ./update_notes.sh <EXP_ID> "<new_notes>"

set -e

# --- Argument Parsing ---
if [ $# -lt 2 ]; then
  echo "Usage: $0 <EXP_ID> \"<new_notes>\""
  echo "Example: $0 E001 \"Updated notes for this experiment\""
  exit 1
fi

EXP_ID=$1
NEW_NOTES=$2

# --- Check if experiment_log.csv exists ---
if [ ! -f "experiment_log.csv" ]; then
  echo "Error: experiment_log.csv not found!"
  exit 1
fi

# --- Check if EXP_ID exists in the CSV ---
if ! grep -q "^$EXP_ID," experiment_log.csv; then
  echo "Error: Experiment ID '$EXP_ID' not found in experiment_log.csv"
  exit 1
fi

# --- Create backup ---
cp experiment_log.csv experiment_log.csv.backup

# --- Update the notes field ---
# The notes field is the 8th column (last column)
awk -F',' -v exp_id="$EXP_ID" -v new_notes="\"$NEW_NOTES\"" '
BEGIN { OFS="," }
$1 == exp_id {
    # Replace the 8th field (notes) with new notes
    $8 = new_notes
}
{ print }
' experiment_log.csv > experiment_log_temp.csv

# --- Replace original file ---
mv experiment_log_temp.csv experiment_log.csv

echo "Successfully updated notes for experiment $EXP_ID"
echo "Backup saved as: experiment_log.csv.backup"
echo "New notes: $NEW_NOTES"

# --- Show the updated row ---
echo ""
echo "Updated row:"
grep "^$EXP_ID," experiment_log.csv