ID,BASE_MODEL,INSTANCES_PER_BATCH,TARGET_SAMPLES,N_GPUS,DATE,NOTES,COMMENTS
E001,o3,232,16,64,2025-08-18,"","Run stuck after 2 batch steps"
E002,o3,232,16,64,2025-08-18,"Added queue_granularity=16","Stuck after 2 batch steps"
E003,o3,232,16,16,2025-08-18,"Lower SPI"
E004,o3,232,16,16,2025-08-18,"Lower SPI and lower inverse token cost to 256", "too many max_token errors, bug in n_ctx setting"
E005,o3,232,16,64,2025-08-18,"Do not skip config validation + set both train/rollout n_ctx to 130k,"Rollout after batch2 took way longer, redis timeout. But maxtoken issue seem to be fixed"
E006,o3,232,16,64,2025-08-19,"<PERSON><PERSON><PERSON>'s suggestion to improve rollout throughput","{kamo-dice-o3-16x64-gpu232-08192025-025853} {Stuck after 2 steps, tmux session lost}"
E007,o3,232,16,64,2025-08-19,"rollout throughput + new args from https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9658/RFT-with-QStar\?anchor\=qstar-script--%5B%5D\(%23gpt5-reasoning-script\)","{kamo-dice-o3-16x64-gpu232-08192025-055827} {slicing error}"
E008,o3,232,16,64,2025-08-19,"rollout throughput + new args from https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9658/RFT-with-QStar\?anchor\=qstar-script--%5B%5D\(%23gpt5-reasoning-script\) + remove policy_config causing slicing error","{kamo-dice-o3-16x64-gpu232-08192025-080747} multimodal should be false"
E009,o3,232,16,64,2025-08-19,"rollout throughput + new args from https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9658/RFT-with-QStar\?anchor\=qstar-script--%5B%5D\(%23gpt5-reasoning-script\) + remove policy_config causing slicing error + fix multimodal arg"
