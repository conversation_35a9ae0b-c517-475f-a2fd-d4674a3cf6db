# Model configuration
model_preset: "d64_80g_bf16_fridge"
base_model: "o3"
# Core training parameters
instances_per_batch: 32
target_samples_per_instance: 128
channels: "analysis,final,confidence"
inverse_token_cost: 1024
num_initial_samples: 8
min_initial_samples_per_variant: 8
# Model and checkpoint configuration
initial_checkpoint: "az://orngscuscresco/models/snapshots/o3-d64-step860-20250324-retransferred-20250401-decrypted/"
renderer_name: "harmony_v4.0.16_berry_v3_1mil_orion_no_budget"
max_tokens: 131072
encoding_name: "orion_200k"
n_gpus: 152
# Dataset configuration
max_n_datapoints_per_dataset: 1000
dataset_container: "orngscuscresco"
# Training curriculum
max_epochs: 10
# System configuration
root_config: "mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False"
policy_ml_config: "tensorcache_v2_load_allow_missing_tensor=True"
# Optimizer configuration
lr_per_instance_d16: "1e-5"
# Peashooter configuration (optimized for OOM prevention)
n_batches_in_flight: 2
sampling_concurrency: 8
num_sampling_processes: 32
num_instance_workers: 16
queue_granularity: 8
stalled_datapoint_timeout: 7200
lost_datapoint_timeout: 360
