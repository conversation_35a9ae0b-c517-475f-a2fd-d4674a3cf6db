#!/bin/bash
CONFIG_FILE=$1

# Function to read YAML values using grep and sed
get_yaml_value() {
    local key=$1
    local default=$2
    local file=$3
    
    # Look for the key in the YAML file
    local value=$(grep "^${key}:" "$file" 2>/dev/null | sed 's/^[^:]*: *//' | sed 's/^"//' | sed 's/"$//')
    
    # Return default if key not found or file doesn't exist
    if [[ -z "$value" || ! -f "$file" ]]; then
        echo "$default"
    else
        echo "$value"
    fi
}

# Configurable parameters with defaults
model_preset=$(get_yaml_value "model_preset" "d64_80g_bf16_fridge" "$CONFIG_FILE")
base_model=$(get_yaml_value "base_model" "o3" "$CONFIG_FILE")
instances_per_batch=$(get_yaml_value "instances_per_batch" "16" "$CONFIG_FILE")
target_samples_per_instance=$(get_yaml_value "target_samples_per_instance" "64" "$CONFIG_FILE")
channels=$(get_yaml_value "channels" "analysis,final,confidence" "$CONFIG_FILE")
inverse_token_cost=$(get_yaml_value "inverse_token_cost" "16384" "$CONFIG_FILE")
num_initial_samples=$(get_yaml_value "num_initial_samples" "8" "$CONFIG_FILE")
min_initial_samples_per_variant=$(get_yaml_value "min_initial_samples_per_variant" "8" "$CONFIG_FILE")
initial_checkpoint=$(get_yaml_value "initial_checkpoint" "az://orngwus2cresco/models/snapshots/o3-d64-step860-20250324-retransferred-20250401-decrypted/" "$CONFIG_FILE")
renderer_name=$(get_yaml_value "renderer_name" "harmony_v4.0.16_berry_v3_1mil_orion_no_budget" "$CONFIG_FILE")
max_tokens=$(get_yaml_value "max_tokens" "131072" "$CONFIG_FILE")
encoding_name=$(get_yaml_value "encoding_name" "orion_200k" "$CONFIG_FILE")
n_gpus=$(get_yaml_value "n_gpus" "232" "$CONFIG_FILE")
max_n_datapoints_per_dataset=$(get_yaml_value "max_n_datapoints_per_dataset" "1000" "$CONFIG_FILE")
dataset_container=$(get_yaml_value "dataset_container" "orngwus2cresco" "$CONFIG_FILE")
max_epochs=$(get_yaml_value "max_epochs" "10" "$CONFIG_FILE")

# System configuration parameters
root_config=$(get_yaml_value "root_config" "mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False" "$CONFIG_FILE")
policy_ml_config=$(get_yaml_value "policy_ml_config" "tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0}" "$CONFIG_FILE")

# Optimizer configuration parameters
lr_per_instance_d16=$(get_yaml_value "lr_per_instance_d16" "1e-5" "$CONFIG_FILE")

# Peashooter configuration parameters
n_batches_in_flight=$(get_yaml_value "n_batches_in_flight" "2" "$CONFIG_FILE")
sampling_concurrency=$(get_yaml_value "sampling_concurrency" "6" "$CONFIG_FILE")
num_sampling_processes=$(get_yaml_value "num_sampling_processes" "8" "$CONFIG_FILE")
num_instance_workers=$(get_yaml_value "num_instance_workers" "16" "$CONFIG_FILE")
queue_granularity=$(get_yaml_value "queue_granularity" "16" "$CONFIG_FILE")
stalled_datapoint_timeout=$(get_yaml_value "stalled_datapoint_timeout" "7200" "$CONFIG_FILE")
lost_datapoint_timeout=$(get_yaml_value "lost_datapoint_timeout" "360" "$CONFIG_FILE")

dt=$(date '+%m%d%Y-%H%M%S')

beam python --start-daemon --use-cwd -m qstar.run_experiment \
nostrict \
name=dice-${base_model}-${instances_per_batch}x${target_samples_per_instance}-gpu${n_gpus}-$dt \
:berry_models.scallion:${model_preset} \
:qstar.presets.common:longer_timeout \
timeout.default=None \
root_config="$root_config" \
policy.ml_config="$policy_ml_config" \
optimizer.hparam_scaler.lr_per_instance_d16=$lr_per_instance_d16 \
defaults.instances_per_batch=$instances_per_batch \
defaults.target_samples_per_instance=$target_samples_per_instance \
defaults.channel_config.channels=$channels \
defaults.inverse_token_cost=$inverse_token_cost \
...num_initial_samples=$num_initial_samples \
...min_initial_samples_per_variant=$min_initial_samples_per_variant \
...harmony_renderer_name="$renderer_name" \
policy.initial_checkpoint="$initial_checkpoint" \
policy.encoding_name=$encoding_name \
policy.n_gpus=$n_gpus \
policy.n_ctx="$max_tokens" \
...n_ctx="$max_tokens" \
:dice_msft.presets.files:train_v2_preset\(max_n_datapoints_per_dataset=$max_n_datapoints_per_dataset\) \
...dataset_container=$dataset_container \
berry_curriculum=berry_curriculums.MultiEpochCurriculum \
berry_curriculum.max_epochs=$max_epochs \
security_profile=msft-orng \
github_upload=False \
wandb_enable=True \
wandb.wandb_project=dice_msft_train \
kafka_enable=False \
enable_slackbot=False \
seed=9999 \
skip_validate_config=False \
batch_completer.n_batches_in_flight=$n_batches_in_flight \
peashooter.sampling_concurrency=$sampling_concurrency \
peashooter.num_sampling_processes=$num_sampling_processes \
peashooter.num_instance_workers=$num_instance_workers \
peashooter.queue_granularity=$queue_granularity \
peashooter.timeout_seconds.stalled_datapoint=$stalled_datapoint_timeout \
peashooter.timeout_seconds.lost_datapoint=$lost_datapoint_timeout
