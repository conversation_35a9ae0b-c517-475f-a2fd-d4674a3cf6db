#!/bin/bash

# Usage: ./new_experiment.sh key1=value1 key2=value2 ... notes="your notes here"
# Example: ./new_experiment.sh base_model=o3 instances_per_batch=16 n_gpus=232 notes="Testing new configuration"

set -e

# --- Parse key-value arguments ---
declare -A overrides
notes=""

for arg in "$@"; do
    if [[ $arg == *"="* ]]; then
        key="${arg%%=*}"
        value="${arg#*=}"
        
        # Handle notes specially
        if [[ "$key" == "notes" ]]; then
            notes="$value"
        else
            overrides["$key"]="$value"
        fi
    else
        echo "Error: All arguments must be in key=value format"
        echo "Usage: $0 key1=value1 key2=value2 ... notes=\"your notes here\""
        echo "Example: $0 base_model=o3 instances_per_batch=16 n_gpus=232 notes=\"Testing new configuration\""
        exit 1
    fi
done

# --- Check if templates exist ---
if [[ ! -f "templates/config_template.yaml" ]] || [[ ! -f "templates/run_template.sh" ]]; then
    echo "Error: Template files not found. Please ensure templates/config_template.yaml and templates/run_template.sh exist."
    exit 1
fi

# --- Next Experiment ID ---
if [[ ! -d "experiments" ]]; then
    mkdir experiments
fi

last_exp=$(ls experiments 2>/dev/null | grep -E '^E[0-9]{3}$' | sort | tail -1 | sed 's/E//' || echo "0")
if [ -z "$last_exp" ] || [ "$last_exp" = "0" ]; then
    next_exp=1
else
    next_exp=$((10#$last_exp + 1))
fi
EXP_ID=$(printf "E%03d" $next_exp)

# --- Create Directories & Copy Templates ---
mkdir -p experiments/$EXP_ID/logs experiments/$EXP_ID/outputs
cp templates/config_template.yaml experiments/$EXP_ID/config.yaml
cp templates/run_template.sh experiments/$EXP_ID/run.sh
chmod +x experiments/$EXP_ID/run.sh

# --- Apply overrides to config.yaml ---
config_file="experiments/$EXP_ID/config.yaml"
yq_command=""

for key in "${!overrides[@]}"; do
    value="${overrides[$key]}"
    
    # Check if the key exists in the original config using yq
    if yq -e ".${key}" templates/config_template.yaml >/dev/null 2>&1; then
        # Determine if value should be quoted (non-numeric values)
        if [[ "$value" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
            # Numeric value, no quotes
            yq_command+=" | .$key = $value"
        else
            # String value, add quotes
            yq_command+=" | .$key = \"$value\""
        fi
    else
        echo "Warning: Key '$key' not found in config template. Skipping."
    fi
done

# Apply all changes at once if there are any
if [[ -n "$yq_command" ]]; then
    yq_command="$yq_command"
    yq -i "${yq_command#* | }" "$config_file"
fi

# --- Read essential values for logging using yq ---
base_model=$(yq '.base_model' "$config_file")
n_gpus=$(yq '.n_gpus' "$config_file")
instances_per_batch=$(yq '.instances_per_batch' "$config_file")
target_samples_per_instance=$(yq '.target_samples_per_instance' "$config_file")

# --- Append to experiment_log.csv ---
DATE_STR=$(date '+%Y-%m-%d')
if [[ ! -f "experiment_log.csv" ]] || ! grep -q "^ID," experiment_log.csv; then
    echo "ID,BASE_MODEL,N_GPUS,INSTANCES_PER_BATCH,TARGET_SAMPLES_PER_INSTANCE,DATE,NOTES" > experiment_log.csv
fi
echo "$EXP_ID,$base_model,$n_gpus,$instances_per_batch,$target_samples_per_instance,$DATE_STR,\"$notes\"" >> experiment_log.csv

# --- Output Summary ---
echo "Experiment $EXP_ID created at experiments/$EXP_ID/"
echo "Generated name will be: dice-$base_model-${instances_per_batch}x${target_samples_per_instance}-gpu${n_gpus}-<timestamp>"
echo ""
echo "Configuration:"
echo "  Base Model: $base_model"
echo "  GPUs: $n_gpus"
echo "  Instances per Batch: $instances_per_batch"
echo "  Target Samples per Instance: $target_samples_per_instance"
if [[ -n "$notes" ]]; then
    echo "  Notes: $notes"
fi
echo ""
echo "Applied overrides:"
for key in "${!overrides[@]}"; do
    echo "  $key = ${overrides[$key]}"
done
echo ""
echo "You can edit config.yaml or run.sh further if needed."
echo "To run: cd experiments/$EXP_ID && ./run.sh config.yaml"
