# DICE MSFT Experiment Manager

A systematic experiment management system for QSTAR distributed training runs with automatic tracking and configuration management.

## Quick Setup

1. **Initialize directories:**
   ```bash
   mkdir -p experiments templates
   touch experiment_log.csv
   ```

2. **Dependencies:**
   ```bash
   # Install yq for YAML processing (choose one)
   pip install yq
   # OR
   brew install yq  # macOS
   ```

## File Structure

```
experiment_manager/
├── templates/
│   ├── config_template.yaml    # YAML config with all parameters
│   └── run_template.sh         # Bash script template (no yq dependency)
├── experiments/               # Auto-generated experiment folders
├── experiment_log.csv        # Tracking log
├── new_experiment.sh         # Create new experiments
└── update_notes.sh          # Update experiment notes
```
### Template Files

**`templates/config_template.yaml`** - Default parameter configuration:
```yaml
# Core training parameters
instances_per_batch: 16
target_samples_per_instance: 64
n_gpus: 232
max_tokens: 131072

# Peashooter configuration (performance tuning)
n_batches_in_flight: 2
sampling_concurrency: 6
num_sampling_processes: 8
# ... (25+ configurable parameters)
```

**`templates/run_template.sh`** - Training execution script:
```bash
#!/bin/bash
# Uses bash-native YAML parsing (no yq dependency)
get_yaml_value() {
    # Extracts values from config.yaml
}

# Launches beam python with qstar.run_experiment
beam python --start-daemon --use-cwd -m qstar.run_experiment \
name=dice-${base_model}-${instances_per_batch}x${target_samples_per_instance}-gpu${n_gpus}-$dt \
# ... (30+ command line parameters)
```

**Generated experiment structure:**
```
experiments/E001/
├── config.yaml          # Customized from template
├── run.sh              # Executable copy of template  
├── logs/               # Training output logs
└── outputs/            # Model artifacts
```

## Usage Workflow

### 1. Create New Experiment
```bash
./new_experiment.sh key1=value1 key2=value2 notes="description"
```

**Example:**
```bash
./new_experiment.sh base_model=o3 n_gpus=216 instances_per_batch=16 target_samples_per_instance=64 notes="OOM-fixed 16x64 config"
```

**Common Parameters:**
- `base_model`: `o3`, `o1` 
- `n_gpus`: `216`, `232`, `464`
- `instances_per_batch`: `8`, `16`, `32`
- `target_samples_per_instance`: `16`, `32`, `64`
- `inverse_token_cost`: `256`, `16384` (length penalty)
- `sampling_concurrency`: `6`, `64`, `128` (peashooter performance)
- `n_batches_in_flight`: `2`, `16`, `20` (memory usage)

### 2. Run Experiment
```bash
# sync changes with remote pod
rcall-brix sync <pod>

# cd to the E001 directory ( inside the pod after SSH )
cd experiments/E001

# Run the script
bash ./run.sh config.yaml
```

### 3. Monitor Progress
```bash
# View all experiments
cat experiment_log.csv

# Update experiment notes
./update_notes.sh E001 "Converged at step 500, 92.3% accuracy"
```

**Sample experiment_log.csv:**
```csv
ID,BASE_MODEL,N_GPUS,INSTANCES_PER_BATCH,TARGET_SAMPLES_PER_INSTANCE,DATE,NOTES,COMMENTS
E001,o3,232,16,64,2025-08-18,"Baseline run","Converged at step 500, 92.3% accuracy"
E002,o3,232,16,64,2025-08-18,"Added queue_granularity=16"
```

### 4. Review Results ( manual copy )
- Check `experiments/E001/logs/` for training logs
- Check `experiments/E001/outputs/` for model outputs

## Template Features

- **No yq dependency**: Uses bash-native YAML parsing
- **Automatic validation**: `skip_validate_config=False` by default
- **Context length safety**: Sets both training and sampling context
- **Peashooter optimization**: Includes all concurrency parameters
- **Experiment tracking**: CSV log with timestamps and notes

## Example Experiment Flow

```bash
# Create high-performance experiment
./new_experiment.sh base_model=o3 n_gpus=216 instances_per_batch=16 target_samples_per_instance=32 sampling_concurrency=64 notes="Balanced 16x32 config"

# Run it
cd experiments/E001 && ./run.sh config.yaml

# Monitor and update notes as needed ( this can be used to add comments after submitting the run )
./update_notes.sh E001 "Running well, 85% GPU utilization"
```

## Adding New Parameters

To add new configurable parameters to your experiments:

### 1. Update Template Files

**Step 1: Add to `templates/config_template.yaml`**
```yaml
# Add your new parameter with default value
new_parameter_name: "default_value"
```

**Step 2: Update `templates/run_template.sh`**
```bash
# Add parameter extraction (around line 35 with other parameters)
new_parameter_name=$(get_yaml_value "new_parameter_name" "default_value" "$CONFIG_FILE")

# Add to beam command (around line 50 with other parameters)
beam python --start-daemon --use-cwd -m qstar.run_experiment \
# ... existing parameters ...
new_parameter_name=$new_parameter_name \
# ... rest of command
```

### 2. Use New Parameters

**Create experiment with new parameters:**
```bash
./new_experiment.sh base_model=o3 n_gpus=216 new_parameter_name="custom_value" notes="Testing new parameters"
```

### Example Workflow

```bash
# 1. Add 'dropout_rate' parameter to templates
# templates/config_template.yaml: dropout_rate: 0.1
# templates/run_template.sh: dropout_rate=$(get_yaml_value "dropout_rate" "0.1" "$CONFIG_FILE")
# templates/run_template.sh: model.dropout_rate=$dropout_rate \

# 2. Create experiment with custom dropout
./new_experiment.sh base_model=o3 dropout_rate=0.2 notes="Higher dropout experiment"

# 3. Run experiment
cd experiments/E007 && ./run.sh config.yaml
```

### Parameter Validation

The system will:
- Use template defaults if parameter not specified in `new_experiment.sh`
- Override defaults with values provided in `new_experiment.sh`
- Warn if you specify parameters not found in the template
- Generate experiments with all parameters tracked in the CSV log

This allows easy experimentation with new hyperparameters while maintaining reproducibility.