import json
import random

import blobfile as bf


def get_samples(data_path, is_train=True):
    if not data_path:
        return {
            "question": "what is 2+2?",
            "reference": "4",
            "answer": "5",
        }
    dir = f"az://orngwus2cresco/data/shtri/dice/051525/raw/{data_path}/{'train' if is_train else 'test'}"

    samples = []
    for file in bf.listdir(dir):
        with bf.BlobFile(f"{dir}/{file}", "rb") as fd:
            samples = list(fd)
    print(f"{dir} = {len(samples)}")
    return samples


total = []

for type in [
    "train",
    # "test"
]:
    print("*-" * 20 + f"{type}" + "*-" * 20)
    total = []
    for dataset_id in [
        "csv_batch1",
        "json_batch1",
        "mf_batch1",
        "mf_batch2",
        "mf_batch3",
        "mf_batch4",
        "noans_batch1",
        "ppt_pdf_batch1",
        "sqlite_batch1",
        "tsv_batch1",
        # "xlsx_batch1",
        "xml_batch1",
        "access_batch1",
        "ambiguous_batch1",
        "ci_coding",
        "mf_batch5",
        "mf_batch6",
        "mf_batch7",
        "mf_batch8",
        "mf_batch9",
        "noans_batch2",
        "noans_batch3",
        "noans_batch4",
        "pbi_batch1",
        "pdf_batch2",
        "ppt_batch2",
        "viz_batch1",
        "viz_batch2",
        "xml_batch2",
        "yaml_batch1",
    ]:
        total += get_samples(
            data_path=dataset_id,
            is_train=type == "train",
        )

    outdir = f"az://orngwus2cresco/data/shtri/dice/051525/combined/no_xlsx/{type}/0.jsonl"
    with bf.BlobFile(outdir, "wb") as fout:
        fout.writelines(total)

    random.shuffle(total)

    with bf.BlobFile(outdir, "rb") as fout:
        print(len(list(fout)))

# for _ in range(random.randint(5, 15)):
# print(f"{total[random.randint(0, len(total))]}")
