"""
Usage:
     python simple_eval.py \
        --data_path 'az://orngwus2cresco/data/shtri/dice/uploads/raw/cwc_ci_v2/test/diceberry_file_code_interpreter_v2.jsonl' \
        --path_to_files az://orngwus2cresco/data/shtri/022425_raw_files/v2/ \
        --out_file_name /root/cwc_xlsx_capi.jsonl \
        --N 250 \
        --k 1 \
        --max_episode_steps 50 \
        --renderer_name harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc \
        --no-inference_bus \
        --inference_ip localhost \
        --category cwc_xlsx \
        --retries 10 \
        --semaphores 16 \
        --caas_workspace /home/<USER>/data \
        --skip_lines 0 \
        --bus_grader

data_path:
    az://orngwus2cresco/data/shtri/dice/uploads/raw/cwc_pptpdf/test/diceberry_file_code_interpreter_singleturn_pptpdf 1.jsonl
    az://orngwus2cresco/data/shtri/dice/uploads/raw/cwc_mf/test/diceberry_file_code_interpreter_singleturn_multifile.jsonl
    az://orngwus2cresco/data/shtri/dice/uploads/raw/cwc_othertypes/test/diceberry_file_code_interpreter_singleturn_othertypes.jsonl
    az://orngwus2cresco/data/shtri/dice/051525/raw/noans_batch4/test/test1.jsonl
    az://orngwus2cresco/data/shtri/dice/051525/raw/noans_batch4/test/test2.jsonl

    DSAT set:
    az://orngcresco/data/shtri/dice/051525/raw/dsat_batch1/test/dsat_1.jsonl
    
    Viz set:
    az://orngwus2cresco/data/shtri/dice/051525/raw/viz_batch1/test/all_data.jsonl
"""

import argparse
import ast
import asyncio
import json
import logging
import os
import random
import re
import subprocess
import sys
import uuid
import xml.etree.ElementTree as ET
from dataclasses import dataclass
from functools import cached_property
from typing import Any, Dict, Tuple

import blobfile as bf
import chat
from bus_token_completer import BusTokenCompleter, QoSType
from caas.api import caas_api
from caas.commands import UploadFile
from caas.memory import CaasMemory
from chat import Conversation, Message, chat
from chat.render.common import render_content
from chat.render.renderer_base import BaseRenderer
from chat.render.renderer_registry import get_renderer
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from chat.tools import DefaultMultiToolKit, take_one_step_with_tools
from dice_msft.common.datapoint_converters import format_task_interpretation
from dice_msft.graders.icl_grader import get_gt_content_asset_pointer, image_to_base64
from dice_msft.prompts.grader import GRADER_XML_WITH_ICL, GRADER_XML_WITH_ICL_MM
from dice_msft.prompts.instructions import (
    ANALYST_QV2_DEV,
    ANALYST_QV2_SYS,
    CAPI_SYS_0,
    DEFAULT_SYS_IDENTITY,
    DEV_INSTRUCTION_V0,
    DEV_INSTRUCTION_V1,
    SYS_INSTRUCTION_V1,
)
from functions.core import Function
from functions.schemas import ObjectSchema, StringSchema
from legacy_rest_token_completer import LegacyRestTokenCompleter
from qstar.common.tools.python.python_tool import PythonTool
from research_ace.v2.client.caas_impl import JupyterKernelRuntimeCaasImpl
from research_ace.v2.tools.python_code_execution_tool import get_instructions
from token_completer import CompleterBackend, TokenCompleter
from token_completer.backend import CompleterBackend
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def format_few_shot_examples(
    user_message: str, search_folder_path: str | list[str] | None = None
) -> str:
    if search_folder_path:
        if isinstance(search_folder_path, list):
            # If it's a list of file paths, join them with a comma separator.
            paths_str = ", ".join(search_folder_path)
            return (
                f"When leveraging `python`, use the files located at `[{paths_str}]`.\n\n"
                "# Task\n\n"
                f"{user_message}"
            )
        else:
            return (
                f"When leveraging `python`, use the file located at `{search_folder_path}`.\n\n"
                "# Task\n\n"
                f"{user_message}"
            )
    else:
        return "# Task\n\n" f"{user_message}"


def make_conversation_object(
    dp: Dict[str, Any],
    caas_workspace,
    base_name="",
    is_analyst_qv2=False,
    is_ppo=False,
) -> chat.Conversation:
    """
    Build a chat.Conversation for a row in the JSONL.
    We always treat file references as a list (even for single-file cases)
    so that both single- and multifile scenarios are processed uniformly.
    """
    # Convert Answer1: if Answer1 is "No Answer found", record it as "[NO_ANSWER_FOUND]".
    answer = ""
    if base_name.startswith("viz_"):
        if base_name.endswith("batch1"):
            raw = dp.get("Answer2", "")
            answer = ",".join(x.strip() for x in raw.split("\t") if x.strip())
        elif base_name.endswith("batch2"):
            raw = dp.get("Answer2", "{}")
            answer = (
                ",".join(
                    x.split("/")[-1]
                    for x in json.loads(raw)
                    if x.strip() and not x.endswith("json")
                )
                if raw
                else ""
            )
    else:
        answer = dp.get("Answer1", "")
        if not isinstance(answer, str):
            answer = str(answer)
        if answer == "No Answer found":
            answer = "[NO_ANSWER_FOUND]"
    # answer = dp.get("Answer1", "").strip()
    # if answer == "No Answer found":
    #     answer = "[NO_ANSWER_FOUND]"

    system_msg = chat.Message.system(
        model_identity_desc=None,  # DEFAULT_SYS_INSTRUCTION
        instructions=ANALYST_QV2_SYS if is_analyst_qv2 else SYS_INSTRUCTION_V1,
        tools_section={
            "python": get_instructions(
                name="python",
                timeout=15 * 60.0,
                enable_matplotlib_instructions=True,  # chart instructions
            ).replace("/mnt/data", f"{caas_workspace}")
        },
        channel_config=chat.SystemChannelConfig(
            valid_channels=(
                BerryChannel.CHAIN_OF_THOUGHT,
                BerryChannel.FINAL_ANSWER,
                # BerryChannel.COMMENTARY,
                # BerryChannel.ANSWER_JUSTIFICATION,
                # BerryChannel.CONFIDENCE,
                # BerryChannel.DELEGATE_SUMMARY,
            ),
        ),
    )

    developer_msg = chat.Message.developer(
        instructions=ANALYST_QV2_DEV if is_analyst_qv2 else DEV_INSTRUCTION_V0
    )

    filenames = []
    search_paths = []
    file_list = []

    # Try to read from file_urls (if provided)
    if dp.get("file_urls", ""):
        file_urls = dp.get("file_urls", "")
        try:
            file_list = json.loads(file_urls)
            if not isinstance(file_list, list):
                file_list = [file_list]
        except json.JSONDecodeError as e:
            # Fallback: Try parse as literal
            file_list = ast.literal_eval(file_urls)
        except Exception as e:
            # Fallback: assume comma-separated list.
            file_list = [x.strip() for x in file_urls.split(",") if x.strip()]
    # Otherwise, fallback to file_url (wrap it into a list)
    elif (file_url := dp.get("file_url", "")) and isinstance(file_url, str) and file_url.strip():
        file_list = [file_url.strip()]

    # Build filenames and search paths from the file_list (even if single file)
    for f in file_list:
        # import ipdb; ipdb.set_trace()
        base_file = os.path.basename(f.strip())
        if not base_file.isascii():
            raise ValueError(f"Skipping conversation due to invalid file name: {base_file}")
        filenames.append(base_file)
        search_paths.append(f"{caas_workspace}/{base_file}")

    # Use the list of search paths if available
    formatted_user_content = format_few_shot_examples(
        dp.get("query", ""), search_paths if search_paths else None
    )
    user_msg = chat.Message.user(content=formatted_user_content)

    # Build the initial messages list.
    if is_analyst_qv2:
        system_msg = chat.Message.system(
            # model_identity_desc=DEFAULT_SYS_INSTRUCTION,
            model_identity_desc="",  # None goes to default You're ChatGPT
            # instructions=CAPI_SYS_0,
            instructions=ANALYST_QV2_SYS,
            # tools_section={
            #     "python": tool_instructions,
            # },
            # tools_section=None,  # As per match with CAPI request
            tools_section={
                "functions": "namespace functions {\n\n// Utility for executing python code.\ntype python = (_: {\n// Python code to be executed.\ncode: string,\n}) => any;\n\n} // namespace functions",
            },
            # To keep same as defaults.channel_config.channels
            channel_config=chat.SystemChannelConfig(
                valid_channels=(
                    BerryChannel.CHAIN_OF_THOUGHT,
                    BerryChannel.COMMENTARY,
                    # BerryChannel.ANSWER_JUSTIFICATION,
                    BerryChannel.FINAL_ANSWER,
                    BerryChannel.CONFIDENCE,
                    # BerryChannel.DELEGATE_SUMMARY,
                ),
                channel_required=True,
                deprecated_knowledge_cutoff="2024-06",
                tool_channel_constraints={
                    BerryChannel.COMMENTARY: [
                        "functions",
                    ]
                },
            ),
            metadata=chat.SystemContentMetadata(
                reward_multiplier=512,
            ),
        )

        # Use the list of search paths if available
        formatted_user_content = format_task_interpretation(
            dp.get("query", ""), search_paths if search_paths else None
        )

        developer_msg = chat.Message.developer(
            function_namespaces=[
                chat.FunctionNamespace(
                    name="functions",
                    functions=[
                        Function(
                            name="python",
                            description="Utility for executing python code.",
                            object_schema=ObjectSchema(
                                properties={
                                    "code": StringSchema(
                                        description="Python code to be executed.",
                                    ),
                                },
                                required=["code"],
                            ),
                            raw_parameters={
                                "type": "object",
                                "properties": {
                                    "code": {
                                        "type": "string",
                                        "description": "Python code to be executed.",
                                    }
                                },
                                "required": ["code"],
                            },
                        )
                    ],
                ),
            ],
            instructions=ANALYST_QV2_SYS,
        )

        developer_msg_2 = chat.Message.developer(ANALYST_QV2_DEV)
        user_prequel_msg = chat.Message.user(
            content=f'{filenames[0]} {dp.get("query", "")}',
            # content="42179.xlsx How many hours in total are Dan and Isabel scheduled to work from Monday to Friday?",
            # content="Get me 100th digit of pi",
        )

        # filenames=["42179.xlsx"]
        assistant_file_upload_msgs = [
            chat.Message.assistant(
                content=chat.Code(
                    language=chat.Language.JSON,
                    text=json.dumps(
                        {
                            "doc_name": filenames[0],
                        }
                    ),
                ),
                recipient="functions.fetch_file",
                channel=BerryChannel.COMMENTARY,
            )
        ]
        tool_file_upload_msgs = [
            chat.Message.tool(
                content=json.dumps(
                    {
                        "index": "0",
                        "FileContent": "Access the file through Python execution to view its content.",
                        "FileName": filenames[0],
                        "ResponseType": "Success",
                    }
                ),
                author_name="fetch_file",
                channel=BerryChannel.COMMENTARY,
            ),
        ]

        assistant_tool_msgs = [
            chat.Message.assistant(
                content=chat.Code(
                    language=chat.Language.JSON,
                    text=json.dumps(
                        {
                            "code": "import pandas as pd\n# Let's see what sheets and contents the Excel file has\nfile_path = '42179.xlsx'\nxl = pd.ExcelFile(file_path)\nsheet_names = xl.sheet_names\nsheet_names\n",
                        }
                    ),
                ),
                recipient="functions.python",
                channel=BerryChannel.COMMENTARY,
            )
        ]

        tool_python_msgs = [
            chat.Message.tool(
                content='{"executedCode":"import pandas as pd\n# Let\'s see what sheets and contents the Excel file has\nfile_path = \'42179.xlsx\'\nxl = pd.ExcelFile(file_path)\nsheet_names = xl.sheet_names\nsheet_names\n","result":"[\'Rota\', \'Employees\', \'Roles\', \'Hours\']","status":"Success","stdout":'
                ',"stderr":'
                ',"outputFiles":[]}',
                author_name="python",
                channel=BerryChannel.COMMENTARY,
            ),
        ]

        user_msg = chat.Message.user(
            content=formatted_user_content,
            # content="[TASK INTERPRETATION] Use `python` to execute Python code and accomplish the following task:\n Calculate the total number of hours Dan and Isabel are scheduled to work from Monday to Friday using the data in the Excel file '42179.xlsx'.\nIf the user's request refers to uploaded data when leveraging `python`, use file(s) located at /mnt/data/42179.xlsx.",
        )

        messages = [
            system_msg,
            # developer_msg,
            developer_msg_2,
            user_prequel_msg,
            *assistant_file_upload_msgs,
            *tool_file_upload_msgs,
            # *assistant_tool_msgs,
            # *tool_python_msgs,
            user_msg,
        ]
    else:
        messages = [system_msg, developer_msg, user_msg]

    return chat.Conversation(
        messages=messages,
        metadata={
            "reference": answer,
            "question": formatted_user_content,
            "Answer_dtype": dp.get("Answer_dtype", None),
            "category": base_name,
            "filenames": filenames,
        },
    )


def get_one_sample(data_path, index=-1):
    with bf.BlobFile(data_path, "rb") as fd:
        samples = list(fd)
        if index < 0:
            index = random.randint(0, len(samples))
        if index >= len(samples):
            print(
                f"=============================== Exiting at sample {index} from {data_path} set ===================================="
            )
            return None
        sample = samples[index]
    print(
        f"=============================== picking up sample {index} from {data_path} set ===================================="
    )
    return json.loads(sample)


async def get_caas_sess(CAAS_ENDPOINT="https://westus2.caas.azure.com"):
    # CAAS_ENDPOINT='http://westus2-01.caas.net'
    test_api = caas_api(
        endpoint=CAAS_ENDPOINT,
    )
    return await test_api.new_session(
        image="acrbuiltincaasglobalame.azurecr.io/dice:base-20250115a-jupyter-from-ml-base",
        cmd="/home/<USER>/.openai_internal/jupyter_server/run-server.sh",
        env={
            "API_PORT": "8080",
            "PROCESS_MEMORY_LIMIT": CaasMemory.resolve(CaasMemory.from_gb(4)),
        },
        cpu_limit="2.0",
        memory_limit=CaasMemory.from_gb(4),
        # memory_reservation=CaasMemory.from_gb(4),
        enable_network=True,
        idle_ttl=15 * 60,
        # image='python',
        # image='jupyter',
    )


async def get_dice_convo_and_toolkit(
    sp,
    path_to_files,
    caas_workspace,
    category,
) -> Tuple[Conversation, Dict, DefaultMultiToolKit]:
    convo = make_conversation_object(
        dp=sp,
        caas_workspace=caas_workspace,
        base_name=category,
        is_analyst_qv2=True,  # Manual
    )
    md = convo.metadata
    file_name = convo.metadata.get("filenames", [])
    retries = 10

    sess = await get_caas_sess()
    if file_name:
        for files in file_name:
            # import ipdb; ipdb.set_trace()
            with bf.BlobFile(os.path.join(path_to_files, files), "rb") as fd:
                for attempt in range(retries):
                    try:
                        await sess.run(
                            UploadFile(
                                f"{caas_workspace}/{files}",
                                fd.read(),
                            )
                        )
                        print(f"File upload for {files} attempt {attempt + 1} ran successfully..")
                        break
                    except Exception as e:
                        print(
                            f"File upload for {files} attempt {attempt + 1} failed with error {e}, retry ..."
                        )
                        if attempt < retries - 1:
                            asyncio.sleep(min(1 * (2**attempt), 16))
                        else:
                            raise

    toolkit = DefaultMultiToolKit(
        tools=[
            PythonTool(
                runtime=JupyterKernelRuntimeCaasImpl(
                    session=sess,
                    user="shtri",
                )
            )
        ]
    )

    return convo, md, toolkit


def grade_xml_v1(
    convo: chat.Conversation,
    general_renderer: BaseRenderer,
    bus_grader: BusTokenCompleter,
) -> dict:
    # print("Begin get likert score for completion.")
    cur_convo = convo.model_copy()
    # print(f'{cur_convo=}')
    tokens = general_renderer.render_for_completion_multimodal_toklist(
        cur_convo, role=chat.Role.ASSISTANT
    )
    [loaded_tokens] = [tokens]

    # print(f'{loaded_tokens=}')
    completion = "None"
    try:
        completions = bus_grader.completion(
            [loaded_tokens],
            max_tokens=(2**17 - 2**16),
            # stop=general_renderer.stop_sequences(),  # type: ignore
            stop=[[200002]],
            temperature=1.0,
        )

        output_messages = general_renderer.parse(
            completions.choices[0].toklist.spans[0].tokens, role=chat.Role.ASSISTANT
        )
        current_completion = render_content(output_messages, 0)

        if current_completion.startswith("```xml"):
            current_completion = current_completion[len("```xml") :]

        if current_completion.endswith("```"):
            current_completion = current_completion[:-3]

        def extract_score_reason(xml_string):
            # Normalize tags to standard <score> and <reason>
            xml_string = re.sub(
                r"<score_start>(.*?)<score_end>", r"<score>\1</score>", xml_string, flags=re.DOTALL
            )
            xml_string = re.sub(
                r"<reason_start>(.*?)<reason_end>",
                r"<reason>\1</reason>",
                xml_string,
                flags=re.DOTALL,
            )
            xml_string = re.sub(
                r"<score_start>(.*?)</score_start>",
                r"<score>\1</score>",
                xml_string,
                flags=re.DOTALL,
            )
            xml_string = re.sub(
                r"<reason_start>(.*?)</reason_start>",
                r"<reason>\1</reason>",
                xml_string,
                flags=re.DOTALL,
            )

            # Wrap with a root element if necessary
            if (
                "<final_answer>" not in xml_string.lower()
                and "<finalanswer>" not in xml_string.lower()
            ):
                xml_string = f"<root>{xml_string}</root>"
            root = ET.fromstring(xml_string)

            # Initialize default values
            score, reason = 0.0, None

            # Search for the tags in the XML structure
            if root.find(".//score") is not None:
                score = root.find(".//score").text.strip()

            return float(score)

        score = extract_score_reason(current_completion)
        completion = current_completion
    except Exception as e:
        print(f"{e=}")
        score = 0.0
        completion = str(e)

    return {
        "score": score,
        "completion": completion,
    }


class Completers:
    def __init__(self, engine_url: str, renderer_name: str):
        self.engine_url = engine_url
        self.renderer_name = renderer_name

    @cached_property
    def _get_grader_bus_completer(self):
        return BusTokenCompleter(
            topic_mode_or_user="chawlapranit",
            topic_or_snapshot="az://orngcresco/models/snapshots/chat-gpt-4o-tt-v3-d64_80g_bf16_fridge-step3075-250316-decrypted",
            bus_line="bus",
            qos_type=QoSType.FIFO,
        )

    @cached_property
    def _get_bus_completer(self):
        return BusTokenCompleter(
            topic_mode_or_user="diceberry",
            topic_or_snapshot="az://orngcresco/twapi/mini/e/shtri-032825-dtr3-4ott_gr-05062025-222524/policy/step_000180/",
            # bus_line="bus",
            qos_type=QoSType.FIFO,
            max_retries=3,
        )

    @cached_property
    def _get_mt_message_completer(self):
        token_completer_config = LegacyRestTokenCompleter.Config(
            api_base=self.engine_url,
            backend=CompleterBackend.FALCON_MM_BACKEND,
        )
        berry_turn_completer_config = BerryMultiMessageTurnCompleter.Config(
            token_completer_config=token_completer_config,
            completion_params={"model": "model", "temperature": 1.0},
            renderer=self.renderer_name,
        )
        return berry_turn_completer_config.build()

    async def shell_completion(
        self,
        messages: list[chat.Message],
        renderer: BaseRenderer,
        wait_for_ready: bool = True,
        max_tokens: int = 2**17,
    ) -> list[chat.Message]:
        """
        Calls the enginev3_dev.curl CLI with your messages and returns
        only the '<|meta_sep|>…' completion line.

        python -m enginev3_dev.curl completion --wait_for_ready=True --max_tokens=1024 --messages=""
        """
        output_messages = []

        def serialize_message(msg: chat.Message):
            return {
                "role": str(msg.role),
                "content": str(msg.content),
            }

        def serialize_assistant_tool_message(msg: chat.Message, call_id: uuid.UUID):
            return {
                "role": "assistant",
                "tool_calls": [
                    {
                        "function": {
                            "arguments": json.dumps(
                                {
                                    "code": str(msg.content),
                                }
                            ),
                            "name": msg.recipient,
                        },
                        "type": "function",
                        "id": str(call_id),
                    }
                ],
            }

        def serialize_tool_message(msg: chat.Message, call_id: uuid.UUID):
            return {
                "role": "tool",
                "content": str(msg.content),
                "name": str(msg.author),
                "tool_call_id": str(call_id),
            }

        call_id = uuid.uuid4()

        def get_payload(messages):
            payload = []
            for m in messages:
                if (
                    # m.channel == BerryChannel.CHAIN_OF_THOUGHT
                    m.role == chat.Role.ASSISTANT
                    and (m.recipient.startswith("functions") or m.recipient.startswith("python"))
                ):
                    call_id = f"call_{str(uuid.uuid4())}"
                    payload.append(serialize_assistant_tool_message(m, call_id))
                elif (
                    # m.channel == BerryChannel.CHAIN_OF_THOUGHT
                    m.role == chat.Role.TOOL
                    and m.recipient == "all"
                ):
                    payload.append(serialize_tool_message(m, call_id))
                else:
                    payload.append(serialize_message(m))
            return json.dumps(payload)

        attempt = 0
        while attempt < 4:
            attempt += 1
            try:
                # import ipdb; ipdb.set_trace()

                # 2. Build the CLI command
                cmd = [
                    sys.executable,
                    "-m",
                    "enginev3_dev.curl",
                    "completion",
                    f"--wait_for_ready={wait_for_ready}",
                    f"--max_tokens={max_tokens}",
                    f"--prompt=dbt1",
                    f"--temperature=0",
                    "--messages",
                    get_payload(messages + output_messages),
                ]

                proc = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.STDOUT,
                )
                stdout, _ = await proc.communicate()
                # lines = stdout.decode().splitlines()
                # 3. Run it and capture both stdout and stderr
                # proc = subprocess.run(
                #     cmd,
                #     stdout=subprocess.PIPE,
                #     stderr=subprocess.STDOUT,
                #     text=True,
                # )

                lines = []
                pick = False
                # HACK: Process curl output based on its response schema
                # Use start_chicken_engine interface instead
                for idx, line in enumerate(stdout.decode().splitlines()):
                    # print(f"{idx=}|{line=}")

                    line = line.strip("'").strip("\\")
                    if line.startswith("req_"):
                        pick = False

                    if pick:
                        print(f"{idx=}|{line=}")
                        lines.append(line)

                    if line.startswith("UnaryCompletionResponse"):
                        pick = True

                output_messages.append(
                    renderer.completion_to_message(
                        text="\n".join(lines),
                        role=chat.Role.ASSISTANT,
                    )
                )
                # import ipdb; ipdb.set_trace()
                message = output_messages[-1]
                if (
                    message.channel == BerryChannel.FINAL_ANSWER
                    or message.end_turn
                    or (
                        message.recipient.startswith("functions")
                        or message.recipient == PythonTool.name
                    )
                ):
                    break
            except Exception as e:
                print(f"error getting completion attempt = {attempt}, {e=}, retrying...")
                logger.warning(f"error getting competion attempt = {attempt}, {e=}, retrying...")
                await asyncio.sleep(1)

        return output_messages

    def get_completer_and_convo_for_grade(
        self,
        convo: chat.Conversation,
        problem: str,
        reference: str,
        answer: str,
        category: str = "default",  # For viz
    ) -> Tuple[BusTokenCompleter, Conversation]:
        system_message = chat.Message.system(
            model_identity_desc="You are an intelligent AI grader used for comparing solutions for a given question. You can provide a floating point score between 0.0 to 1.0.",
            channel_config=chat.SystemChannelConfig(
                valid_channels=(BerryChannel.CHAIN_OF_THOUGHT, BerryChannel.FINAL_ANSWER),
                channel_required=True,
            ),
            metadata=chat.SystemContentMetadata(
                reward_multiplier=64,
            ),
        )

        if "viz" in category:
            given_answer = []
            temp_convo = convo.copy()
            for i in range(len(temp_convo.messages) - 1, -1, -1):
                if temp_convo.messages[i].content.content_type == "multimodal_text":
                    for part in temp_convo.messages[i].content.parts:
                        if isinstance(part, chat.Image):
                            given_answer.append(part.get_base64())
                    # asset_pointer = (
                    #     temp_convo.messages[i].content.parts[-1].asset_pointer
                    # )
                    # base64_str = image_to_base64(asset_pointer)
                    # given_answer.append(base64_str)
                    # break
            gt_answer = [image_to_base64(ap) for ap in get_gt_content_asset_pointer(reference)]

            user_prompt = (
                GRADER_XML_WITH_ICL_MM.replace("{problem}", problem)
                + """Teacher solution:
            <teacher_solution_start>
            """
            )

            message_parts = (
                [user_prompt]
                + [
                    chat.Image(encoding="base64", payload=gt_ans, format="png", fovea=1080)
                    for gt_ans in gt_answer
                ]
                + [
                    """<teacher_solution_end>

            Student solution:
            <student_solution_start>
            """
                ]
                + [
                    chat.Image(encoding="base64", payload=ans, format="png", fovea=1080)
                    for ans in given_answer
                ]
                + [
                    """<student_solution_end>

            Final Answer:"""
                ]
            )

            return self._get_grader_bus_completer, Conversation(
                messages=[
                    system_message,
                    chat.Message(
                        role=chat.Role.USER,
                        content=chat.MultimodalText(parts=message_parts),
                    ),
                ]
            )
        else:
            user_query = (
                GRADER_XML_WITH_ICL.replace("{problem}", problem)
                .replace("{canonical_solution}", reference)
                .replace("{student_answer}", answer)
            )

            user_message = chat.Message.user(user_query)
            return self._get_grader_bus_completer, Conversation(
                messages=[system_message, user_message]
            )

    async def get_completions_message_from_bus(
        self,
        convo: Conversation,
        renderer: BaseRenderer,
    ):
        cur_convo = convo.model_copy()
        output_messages = []
        attempt = 0
        while attempt < 5:
            attempt += 1
            try:
                tokens = renderer.render_for_completion_multimodal_toklist(
                    cur_convo, role=chat.Role.ASSISTANT
                )
                completions = await self._get_bus_completer.async_completion(
                    prompt=[tokens],
                    max_tokens=(2**17 - 2**16),
                    stop=renderer.stop_sequences(),  # type: ignore
                    temperature=1.0,
                )

                message: Message = renderer.parse(
                    completions.choices[0].toklist.spans[0].tokens, role=chat.Role.ASSISTANT
                )

                # import ipdb; ipdb.set_trace()
                output_messages.append(message)
                cur_convo = cur_convo.with_suffix(message)
                if (
                    message.channel == BerryChannel.FINAL_ANSWER
                    or message.end_turn
                    or message.recipient == PythonTool.name
                ):
                    break
            except Exception as e:
                print(f"error getting competion attempt = {attempt}, {e=}, retrying...")
                logger.warning(f"error getting competion attempt = {attempt}, {e=}, retrying...")
                await asyncio.sleep(1)

        return output_messages

    async def get_completions_message_from_berry_msg_turn_completer(
        self,
        convo: Conversation,
    ):
        for i in range(5):
            try:
                completion = await self._get_mt_message_completer.async_completion(
                    conversation=convo,
                    # end_header=is_last_step,
                    temperature=1.0,
                    # reward_multiplier=64,
                    # valid_channels=("analysis", "confidence", "final"),
                    valid_channels=(
                        BerryChannel.CHAIN_OF_THOUGHT,
                        # BerryChannel.CONFIDENCE,
                        BerryChannel.FINAL_ANSWER,
                    ),
                )
                return completion.output_messages
            except Exception as e:
                logger.warning(f"error getting competion attempt = {i}, {e=}, retrying...")
                await asyncio.sleep(1)
        return None


async def main(
    data_path="",
    path_to_files="",
    caas_workspace="",
    data_index=0,
    pass_attempt=0,
    file_handle=None,
    max_episode_steps=50,
    category="default",
    inference_ip="localhost",
    renderer_name="",
    inference_bus=False,
    bus_grader=True,
    retries=10,
    skip_lines=0,
    inference_enginev3=True,
):
    if sp := get_one_sample(data_path=data_path, index=data_index + skip_lines):
        for i in range(retries):
            try:
                convo, md, toolkit = await get_dice_convo_and_toolkit(
                    sp=sp,
                    path_to_files=path_to_files,
                    caas_workspace=caas_workspace,
                    category=category,
                )
                break
            except Exception as e:
                if i == (retries - 1):
                    logger.warning(f"Error creating session skipping {data_index=}...")
                    return
                logger.warning(f"error creating session attempt = {i}, {e=}, retrying...")
                await asyncio.sleep(1)
        reference_answer = md["reference"]
        user_prompt = md["question"]

        print("Set up message_completer_config..Starting rollouts")
        for elapsed_step in range(max_episode_steps):
            is_last_step = elapsed_step == max_episode_steps - 1
            if inference_enginev3:
                output_messages = await completers.shell_completion(
                    convo.messages,
                    get_renderer(renderer_name),
                )
            elif inference_bus:
                output_messages = await completers.get_completions_message_from_bus(
                    convo,
                    get_renderer(renderer_name),
                )
            else:
                output_messages = (
                    await completers.get_completions_message_from_berry_msg_turn_completer(
                        convo,
                    )
                )
            if output_messages == None or len(output_messages) < 1:
                logger.warning(f"UNABLE TO SAMPLE FOR {data_index=}, TERMINATING..")
                return

            # import ipdb; ipdb.set_trace()

            # HACK: Convert tool names even if different
            # TODO: Add only for Analyst changes
            new_output_messages = []
            for message in output_messages:
                if (
                    message.channel == BerryChannel.CHAIN_OF_THOUGHT
                    and message.role == chat.Role.ASSISTANT
                    and message.recipient.startswith("functions.python")
                ):
                    logger.info(
                        f"{data_index}:{elapsed_step} function call made to {message.recipient}"
                    )
                    content = str(message.content)
                    try:
                        content = json.loads(str(message.content)).get("code", "")
                    except Exception as e:
                        pass

                    new_output_messages.append(
                        chat.Message.assistant(
                            content=chat.Code(
                                language=chat.Language.PYTHON3,
                                text=content,
                            ),
                            recipient="python",
                            channel=BerryChannel.CHAIN_OF_THOUGHT,
                            end_turn=False,
                        )
                    )
                else:
                    new_output_messages.append(message)

            convo = convo.with_suffix(*new_output_messages)
            for message in new_output_messages:
                if message.channel == BerryChannel.FINAL_ANSWER or message.end_turn:
                    is_last_step = True

            if is_last_step:
                break

            async for tool_message in take_one_step_with_tools(
                prefix_convo=convo.prefix(-1),
                message=convo.messages[-1],
                toolkit=toolkit,
            ):
                if convo.messages[-1].id == tool_message.id:
                    convo.messages[-1] = tool_message
                else:
                    convo = convo.with_suffix(tool_message)
            logger.info(f"{data_index} step {elapsed_step}: tool used")
        print(f"{data_index}: sampling for convo completed, total episode: {elapsed_step}")

        current_completion = render_content(convo.messages[-1], 0)
        # TODO: confidence channel
        for msg in convo.messages:
            if msg.channel == "final":
                current_completion = render_content(msg, 0)

        grader_response = None
        # TODO: Reuse grading logic, accept grader renderers and make generic
        if bus_grader:
            token_completer, grader_convo = completers.get_completer_and_convo_for_grade(
                convo=convo,
                problem=user_prompt,
                reference=reference_answer,
                answer=current_completion,
                category=category,
            )
            grader_response = grade_xml_v1(
                convo=grader_convo,
                general_renderer=get_renderer(
                    "harmony_v4.0.15_16k_orion_text_only_no_asr_2k_action"
                ),
                bus_grader=token_completer,
            )

        file_handle.write(
            json.dumps(
                {
                    "data_index": data_index,
                    "conversation": convo.model_dump_json(),
                    "answer": reference_answer,
                    "reward": grader_response["score"],
                    "category": category,
                    "grader": grader_response,
                    "pass": pass_attempt,
                }
            )
            + "\n"
        )

        logger.info(f"Grade for {data_index=} is {grader_response}")
    return


async def run_tasks(
    data_path: str = "",
    path_to_files: str = "",
    caas_workspace: str = "",
    out_file_name: str = "out.jsonl",
    N: int = 783,
    k: int = 1,
    max_episode_steps: int = 50,
    category: str = "default",
    inference_ip: str = "localhost",
    renderer_name: str = "",
    inference_bus: bool = False,
    bus_grader: bool = True,
    retries: int = 10,
    semaphores: int = 64,
    skip_lines: int = 0,
):
    passes = []
    semaphore = asyncio.Semaphore(semaphores)
    out_file = open(out_file_name, "w", encoding="utf-8-sig")

    async def process_results(index, pass_attempt):
        async with semaphore:  # Acquire semaphore
            rew = await main(
                data_path=data_path,
                path_to_files=path_to_files,
                caas_workspace=caas_workspace,
                data_index=index,
                pass_attempt=pass_attempt,
                file_handle=out_file,
                max_episode_steps=max_episode_steps,
                category=category,
                inference_ip=inference_ip,
                renderer_name=renderer_name,
                inference_bus=inference_bus,
                bus_grader=bus_grader,
                retries=retries,
                skip_lines=skip_lines,
            )
            passes.append(rew)

    coros = [process_results(i, attempt) for i in range(N) for attempt in range(k)]
    await asyncio.gather(*coros)
    return passes


def print_results(out_file_name: str) -> None:
    # Initialize a dictionary to store the sum of rewards and count of each category
    category_rewards = {}

    with open(out_file_name, "r", encoding="utf-8-sig") as file:
        for line in file:
            record = json.loads(line)
            category = record["category"]
            reward = record["reward"]
            if category in category_rewards:
                category_rewards[category]["sum"] += reward
                category_rewards[category]["count"] += 1
            else:
                category_rewards[category] = {"sum": reward, "count": 1}

    # Calculate and print the mean reward per category
    total_rewards, total_count = 0, 0
    print(
        "Final results below :\n -------- \n |Category| Count | Mean Reward|\n |-------|------|-------|"
    )
    for category, values in category_rewards.items():
        total_rewards += values["sum"]
        total_count += values["count"]
        mean_reward = values["sum"] / values["count"]
        print(f"|{category} | {values['count']} | {mean_reward} |")

    overall_mean_reward = total_rewards / total_count
    print(f"| Overall | {total_count} | {overall_mean_reward} |")


@dataclass
class Config:
    data_path: str
    path_to_files: str
    caas_workspace: str
    out_file_name: str
    N: int  # Total samples
    k: int  # Pass @ k
    max_episode_steps: int
    category: str
    inference_ip: str
    renderer_name: str
    inference_bus: bool
    bus_grader: bool
    retries: int
    semaphores: int
    skip_lines: int


def get_arg_parser():
    parser = argparse.ArgumentParser(description="Run DICE eval loop with configurable args")
    parser.add_argument(
        "--data_path",
        type=str,
        default="az://orngcresco/data/zhennizhang/test_run_input.jsonl",
        help="Input data path",
    )
    parser.add_argument(
        "--path_to_files",
        type=str,
        default="az://orngcresco/data/zhennizhang/test_run/",
        help="Path to files for CAAS upload",
    )

    parser.add_argument(
        "--out_file_name",
        type=str,
        default="<EMAIL>",
        help="Output results file",
    )

    parser.add_argument("--N", type=int, default=50, help="Number of samples")
    parser.add_argument("--k", type=int, default=1, help="Pass@k")
    parser.add_argument(
        "--max_episode_steps", type=int, default=50, help="Number of steps in episode"
    )
    parser.add_argument(
        "--renderer_name",
        type=str,
        default="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget",
        help="Renderer name",
    )
    parser.add_argument(
        "--category",
        type=str,
        default="default",
        help="Category name to identify the dataset type. Use `viz` prefix for visualization sets.",
    )
    parser.add_argument(
        "--retries", type=int, default=10, help="Number of retries for single sample"
    )
    parser.add_argument("--semaphores", type=int, default=32, help="Number of async workers")
    parser.add_argument(
        "--skip_lines", type=int, default=0, help="Number of lines to skip when picking up samples"
    )
    parser.add_argument(
        "--caas_workspace",
        type=str,
        default="/home/<USER>/data",
        help="Workspace path in CAAS session",
    )

    parser.add_argument("--inference_ip", type=str, default="localhost", help="Inference engine IP")
    parser.add_argument(
        "--inference_bus", action="store_true", help="Use bus inference (default: False)"
    )
    parser.add_argument("--no-inference_bus", dest="inference_bus", action="store_false")
    parser.set_defaults(inference_bus=True)

    parser.add_argument("--bus_grader", action="store_true", help="Use bus grader (default: True)")
    parser.add_argument("--no-bus_grader", dest="bus_grader", action="store_false")
    parser.set_defaults(bus_grader=True)

    return parser


if __name__ == "__main__":
    parser = get_arg_parser()
    args = parser.parse_args()

    # Setup completers and global args
    completers = Completers(
        engine_url=f"http://{'localhost' if args.inference_bus else args.inference_ip}:5122/v1/inference",
        renderer_name=args.renderer_name,
    )

    # Allow switching by editing here or using CLI. Use the config class for clarity.
    config = Config(
        data_path=args.data_path,
        path_to_files=args.path_to_files,
        caas_workspace=args.caas_workspace,
        out_file_name=args.out_file_name,
        N=args.N,
        k=args.k,
        category=args.category,
        max_episode_steps=args.max_episode_steps,
        inference_ip=args.inference_ip,
        renderer_name=args.renderer_name,
        inference_bus=args.inference_bus,
        bus_grader=args.bus_grader,
        retries=args.retries,
        semaphores=args.semaphores,
        skip_lines=args.skip_lines,
    )
    print(
        f"Running evaluation with: {config.out_file_name}, pass@{config.k}, engine={config.inference_ip} \n"
        + "*" * 20
    )
    results = asyncio.run(
        run_tasks(
            data_path=config.data_path,
            path_to_files=config.path_to_files,
            caas_workspace=config.caas_workspace,
            out_file_name=config.out_file_name,
            N=config.N,
            k=config.k,
            max_episode_steps=config.max_episode_steps,
            category=config.category,
            inference_ip=config.inference_ip,
            renderer_name=config.renderer_name,
            inference_bus=config.inference_bus,
            bus_grader=config.bus_grader,
            retries=config.retries,
            semaphores=config.semaphores,
            skip_lines=config.skip_lines,
        )
    )
    print_results(config.out_file_name)
