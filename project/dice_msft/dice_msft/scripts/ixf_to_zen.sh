# Example usage:
# bash personal/dh/export/export_nv4_to_zen.sh az://oaiiad1/oaimodelrunner/ixf_models/nv4/os-4-2025-04-01-17-53-26/2025-04-01-17-53-26/snapshot.e8b64621e6fb05fe7b8481fd1a8e03a7612f55ddbb26444bf9c0cd9c39d52d01.json


set -ex

ixf_snapshot_path=${1}
d=$(date +%Y%m%d%H%M%S)
# override if you want to use a different remote root
remote_root=${remote_root:-az://orngwus2cresco/models/shtri/ixf2zen}
# override if you want to use a different number of op shards
n_op_shards=${n_op_shards-4}

export CLUSTER_CACHE_DIR="/tmp/import_staging_dir/"
export OPENAI_FEATURE_WEIGHT_LOADER_ADAPTOR="ClusterCacheAdaptor"
export OPENAI_FEATURE_MAX_TOKENS_PER_BATCH=${OPENAI_FEATURE_MAX_TOKENS_PER_BATCH:-256}
export OPENAI_FEATURE_IXF_USE_DUST_BULK_KV_LAYER=${OPENAI_FEATURE_IXF_USE_DUST_BULK_KV_LAYER:-true}
export OPENAI_FEATURE_DETERMINISTIC_MODE=${OPENAI_FEATURE_DETERMINISTIC_MODE:-balanced}


slug="${basename:-nv4}-os$n_op_shards"
export_path=$remote_root/$slug/$d
local_export_path=/tmp/zen_exports/$slug/$d

echo "Exporting $slug to ${local_export_path}"
q=${OPENAI_FEATURE_PARAM_QUANTIZATION-".*mlp/[AB]/weight=b64_e5m3_a_ll"}

# 1. IXF -> Zen
rm -rf /tmp/zen_exports

OPENAI_FEATURE_PARAM_QUANTIZATION="$q" \
OPENAI_FEATURE_PIPE_LAYOUT="n_shards=$n_op_shards" \
OPENAI_FEATURE_MLP_UNEVEN_RESHARDING=True \
OPENAI_FEATURE_MLP_UNEVEN_RESHARDING_SIMULATE_PADDING_FOR_N_SHARDS=4 \
mpirun -n "$n_op_shards" -l python -m enginev3_dev.record_model snapshot_path="$ixf_snapshot_path" \
kernel_log_path=/tmp/zen_exports export_snapshot_path="$local_export_path/$slug"

# get first and only file with .json ending + assert that there is only one file
f=$(bbb ls "$local_export_path/$slug.*.json" | grep -v "params")
# assert that there is only one file
if [[ $(echo "$f" | wc -l) -ne 1 ]]; then
echo "Expected one file, got $(echo "$f" | wc -l)": "$f"
exit 1
fi
bname=$(basename "$f")
bbb cptree $local_export_path $export_path
echo "Unencrytped filename: $export_path/$bname"