# Set environment variables
export OPENAI_API_KEY="dummy_key"
export TWDEV_LAUNCH_CACHE_SVC_ENGINE=1
export RUST_BACKTRACE=1

# Install bus (optional)
oaipkg install bus > ~/bus_install.log 2>&1

# Run setup script
brix run --env TWDEV_LAUNCH_CACHE_SVC_ENGINE=1 -- python /root/code/openai/torchflow/run-torchflow-setup.py > cache_svc.log 2>&1

# model_config="falcon.multimodal.runs.scallion-d36-s64-lpe
# raise_on_load_for_missing_tensor=True
# twppo.scallion.lpe.common
# enable_tensorcache_v2=False
# ixf_max_cache_list_length=4096
# ixf_kv_block_unit_size=2048
# n_op_shards=4 pipe_depth=2 n_replicas=1
# "

model_config="falcon.multimodal.runs.scallion-d36-s64-lpe
raise_on_load_for_missing_tensor=False
twppo.scallion.text.common
enable_tensorcache_v2=False
ixf_max_cache_list_length=4096
ixf_kv_block_unit_size=2048
n_op_shards=4 pipe_depth=2 n_replicas=1
"
# model_config="falcon.multimodal.runs.scallion-d36-s64-lpe twppo.scallion.lpe.common n_op_shards=4 pipe_depth=2 n_expert_shards=1 mask_masked_tokens_dust_decoder=False attn_global_scale_param=exp moe_global_scale_param=exp enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_replicas=1"

# # Start the engine
# nohup python -m harmony_scripts.engines.start_engine \
#     --name=dbv4-engine \
#     --mode=optimal \
#     --snapshot_path="az://orngwus2cresco/models/shtri/zen/shtri-032825-dtr3-4ott_gr-05062025-222524/v2/model/2025-06-19-21-19-11/snapshot.cd22b1f0944ee047c9b74cc19e32569194337a3b17c7623c052cefc987ecaaaf.json" \
#     --gpu_kind=A100_80G \
#     --cluster=local \
#     --renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget \
#     --extra_config_string="$model_config" \
#     --restartable \
#     --is_multimodal=False \
#     --enable_healthcheck=True \
#     --use_ev3=True \
#     --n_replicas=1 \
#     > ~/clean_zen.log 2>&1 &

# az://orngwus2cresco/models/shtri/zen/shtri-032825-dtr3-4ott_gr-05062025-222524/v2/model/2025-06-19-21-19-11/snapshot.cd22b1f0944ee047c9b74cc19e32569194337a3b17c7623c052cefc987ecaaaf.json


python -u -m twapi.bin.start_engine\
    --root_config="twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 create_model_timeout=1080000
    step_timeout=1080000 initial_step_timeout=1080000 download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000
    ban_nodes= enable_wandb_hook=False enable_snowflake_hook=False"\
    --model_config="$model_config" \
    --skip_tw_ixf_comparison=True \
    --n_replicas=1\
    --n_op_shards=4\
    --pipe_depth=2\
    --load_path="az://orngtransfer/devaultoutbound/models/congcongchen/shtri-032825-dtr3-4ott-v3/snapshot.0ea9bc8950319d43ddbbe32901a8647e98b8a2928555421043ef173ae555a28e.json" \
    --local=True\
    --sequence_parallelism_enabled=False\
    --falcon_sampling=False\
    --is_multimodal=False \
    > ~/ixf.log 2>&1 &

# Launch Zen 

# beam python -u -m chicken_inference_scripts.start_chicken_engine \
#     --snapshot_family="scallion_lpe" \
#     --snapshot_name="d36_80g_mbg16_bf16_chicken" \
#     --attn_implementation_override=None \
#     --use_fp4_moe=True \
#     --use_fp4_kv_cache=True \
#     --config_overrides="ixf_batch_runner_memory_buffer=8_000_000_000"


########### OLDER ATTEMPTS ##############

# model_config="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=4 pipe_depth=2 n_replicas=1 ixf_implementation=zen enable_zen_instead_of_falcon=True enable_zen_instead_of_ixf=True"

# python -u -m twapi.bin.start_engine\
#     --root_config="twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 create_model_timeout=1080000
#     step_timeout=1080000 initial_step_timeout=1080000 download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000
#     ban_nodes= enable_wandb_hook=False enable_snowflake_hook=False"\
#     --model_config="$model_config" \
#     --n_replicas=1\
#     --n_op_shards=4\
#     --pipe_depth=2\
#     --load_path="az://orngwus2cresco/models/shtri/zen/shtri-032825-dtr3-4ott_gr-05062025-222524/v1/model.2ce5857b3535817ad49b9dcdd5cfbab9657aa10a46b018035df26420fc17f009.json" \
#     --local=True\
#     > ~/zen_twapi_start_engine.log 2>&1 &
    
# # python prepare_snapshot.py --model_name="falcon.multimodal.runs.scallion-d36-s64-lpe" \
# # --falcon_path="az://orngcresco/twapi/mini/e/shtri-032825-dtr3-4ott_gr-05062025-222524/policy/step_000180/25050622260733BATEDI-0/" \
# # --export_dir="congcongchen/shtri-032825-dtr3-4ott-v3" \
# # --override model="falcon.multimodal.runs.scallion-d36-s64-lpe twppo.scallion.text.common microbatch_dict={1024:16,2048:8,4096:4,8192:2,16384:1,32768:1,65536:1,131072:1,262144:1,524288:1,1048576:1,2097152:1,4194304:1} enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=4 pipe_depth=2 n_replicas=1 n_ctx=1048576 eval_n_ctx=1048576 dataset_encoding=orion_200k restore_parent_state=params,unembedding weight_decay_to_init=False attn_temp_gain_nelem=1048576" \
# # root="twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 create_model_timeout=1080000 step_timeout=1080000 initial_step_timeout=1080000 download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000 ban_nodes= enable_wandb_hook=False enable_snowflake_hook=False" \
# # skip_falcon_comparison="True" falcon_comparison_tolerance="0.075" force_fp8="False" skip_dataset_specific_info="True" skip_snapshot_comparison="True" override_ev3_mm_defaults="True"

    # --extra_config_string="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=2 pipe_depth=4 n_replicas=1" \
# # curl http://localhost:5122/v1/inference -H "Content-Type: application/json" -H "Authorization: Bearer Fake" -H "OpenAI-Organization: openai" -d '{ "prompt": "Fun fact is fun because?", "max_tokens": 64, "temperature": 0 }'