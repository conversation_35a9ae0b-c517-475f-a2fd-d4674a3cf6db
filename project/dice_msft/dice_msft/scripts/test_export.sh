export MODEL_FALCON_2_ZEN="falcon.multimodal.runs.scallion-d36-s64-lpe do_multimodal_projections_in_ixf_batch_runner=False include_audio_embeddings=False decoupled_attn=False decoupled_attention_token_mapping=None twppo.lpe.sample allow_embedding_prefix_loading=True ixf_implementation=zen n_op_shards=8 pipe_depth=4 n_expert_shards=1 twppo.scallion.common"
export MODEL_FALCON_2_IXF="falcon.multimodal.runs.scallion-d36-s64-lpe twppo.scallion.lpe.common n_op_shards=4 pipe_depth=2 n_expert_shards=1 mask_masked_tokens_dust_decoder=False attn_global_scale_param=exp moe_global_scale_param=exp"

export EXPORT_DIR="az://orngwus2cresco/models/shtri/zen/shtri-032825-dtr3-4ott_gr-05062025-222524/v2/model"
export CHECKPOINT_DIR="az://orngwus2cresco/twapi/mini/e/shtri-032825-dtr3-4ott_gr-05062025-222524/policy/step_000180/25050622260733BATEDI-0/"

python -m model_export_scripts.export_inference --model="$MODEL_FALCON_2_IXF" --falcon_path="$CHECKPOINT_DIR" --export_dir="$EXPORT_DIR" --falcon_comparison_tolerance=0.06 --force_fp8=False --activation_dtype_override="resblock_internal_dtype=fp16"  --export_reshard=False --soft_fail_falcon_comparison=True --skip_dataset_specific_info=True --override_checkpoint_attention_implementation=False --snapshot_name=shtri-dbv4-tc2ixf-test --description="Export to IXF for DBV4 model." --override_ev3_mm_defaults=True 2>&1 --cluster=local | tee output_logs.log &

# force_fp8=False,
#  python -m applied_models.tools.launch_encrypt_job launch --snapshot_path az://orngwus2cresco/models/shtri/zen/shtri-032825-dtr3-4ott_gr-05062025-222524/v2/model/2025-06-19-21-19-11/snapshot.cd22b1f0944ee047c9b74cc19e32569194337a3b17c7623c052cefc987ecaaaf.json --dest_path az://orngwus2cresco/models/shtri/zen/shtri-032825-dtr3-4ott_gr-05062025-222524/v2/model/2025-06-19-21-19-11.encrypted --cluster=owl --use_git=False --team=rl --use_gpu_machines --n_shards 4 --torchflow_path=az://orngwus2cresco/twapi/mini/e/shtri-032825-dtr3-4ott_gr-05062025-222524/policy/step_000180/25050622260733BATEDI-0/ --base_model_config=falcon.multimodal.runs.scallion-d36-s64-lpe --num_machines=<num_machines>


#### IXF MODEL DIR ####
# az://orngwus2cresco/models/shtri/zen/shtri-032825-dtr3-4ott_gr-05062025-222524/v2/model/2025-06-19-21-19-11/snapshot.cd22b1f0944ee047c9b74cc19e32569194337a3b17c7623c052cefc987ecaaaf.json