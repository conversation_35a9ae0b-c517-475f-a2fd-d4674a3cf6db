#!/usr/bin/env python3
"""
Script to upload DataGen's CSV format on blobs to be used as dataset IDs in presets.
It downloads locally the file_urls and answer_urls and uploads them to provided blob paths.

NOTE:
    1. Only update "jsonl-blob-prefix" argument to a new path (this will be dataset_id)
    2. CSV file must contain 
        1. "file_urls" (List[str]) which can be loaded via json loads
        2. "file_url" (str) which is a "single" url
    3. Ensure directory structure for files to add 
        directory_for_all_data/
            <directory>/
                train/*.csv
                test/*.csv
    4. For visualization category, ensure prefix for "directory" folder as "viz_"

Usage:
    python upload_from_csv.py \
        --data-dir /path/to/data/dice_register \
        --file-blob-prefix az://orngcresco/data/shtri/022425_raw_files/v2 \
        --answer-blob-prefix az://orngcresco/data/shtri/dice/viz/results \
        --jsonl-blob-prefix az://orngcresco/data/shtri/dice/051525/raw \
        --concurrency 32 \
        --retries 5
"""

import argparse
import ast
import asyncio
import json
import os
import sys

import pandas as pd

# import blobfile as bf


async def download_and_upload(
    url: str,
    blob_prefix: str,
    retries: int = 5,
    retry_backoff: float = 1.0,
):
    """
    Download a file from `url` using curl and upload it to `blob_prefix`/filename using `bbb cp`.
    Retries up to `retries` times with exponential backoff.
    """
    filename = url.split("/")[-1]
    for attempt in range(1, retries + 1):
        try:
            # Download with curl
            curl_proc = await asyncio.create_subprocess_exec(
                "curl",
                "-f",
                "-O",
                url,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            stdout, stderr = await curl_proc.communicate()
            if curl_proc.returncode != 0:
                raise RuntimeError(
                    f"curl failed [{curl_proc.returncode}]: {stderr.decode().strip()}"
                )

            # Upload with bbb cp
            upload_target = f"{blob_prefix.rstrip('/')}/{filename}"
            bbb_proc = await asyncio.create_subprocess_exec(
                "bbb",
                "cp",
                filename,
                upload_target,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            stdout, stderr = await bbb_proc.communicate()
            if bbb_proc.returncode != 0:
                raise RuntimeError(
                    f"bbb cp failed [{bbb_proc.returncode}]: {stderr.decode().strip()}"
                )

            # Clean up local file
            try:
                os.remove(filename)
            except OSError:
                pass

            print(f"✅ Successfully uploaded {filename} to {upload_target}")
            return

        except Exception as e:
            if attempt < retries:
                wait_time = retry_backoff * attempt
                print(
                    f"⚠️  Attempt {attempt}/{retries} failed for URL '{url}': {e}. Retrying in {wait_time}s..."
                )
                await asyncio.sleep(wait_time)
            else:
                print(f"❌  All {retries} attempts failed for URL '{url}': {e}")
                return


async def process_row(
    idx: int,
    row: pd.Series,
    directory: str,
    file_blob_prefix: str,
    answer_blob_prefix: str,
    semaphore: asyncio.Semaphore,
    retries: int,
):
    """
    Process a single row: download all file_urls (or file_url) and upload them, then
    if applicable, download answer URLs and upload them under answer_blob_prefix.
    """
    async with semaphore:
        # Handle file URLs
        try:
            if "file_urls" in row and not pd.isna(row.file_urls):
                try:
                    for url in json.loads(row.file_urls):
                        await download_and_upload(url, file_blob_prefix, retries=retries)
                except json.JSONDecodeError as e:
                    for url in ast.literal_eval(row.file_urls):
                        await download_and_upload(url, file_blob_prefix, retries=retries)
                except Exception as e:
                    print(f"❌ Skipping file URLs for index {idx}: {e}")
            elif "file_url" in row and not pd.isna(row.file_url):
                await download_and_upload(row.file_url.strip(), file_blob_prefix, retries=retries)
            print(f"{'*'*5} Uploaded files for index {idx} {'*'*5}")
        except Exception as e:
            print(f"❌ Skipping file URLs for index {idx}: {e}")

        # Handle answer URLs (only for viz_* directories)
        try:
            answer_urls = []
            if directory.startswith("viz_"):
                if directory.endswith("batch1"):
                    raw = row.get("Answer2", "")
                    answer_urls = [x.strip() for x in raw.split("\t") if x.strip()]
                elif directory.endswith("batch2"):
                    raw = row.get("Answer2", "{}")
                    answer_urls = json.loads(raw) if raw else []

            if answer_urls:
                for url in answer_urls:
                    await download_and_upload(url, answer_blob_prefix, retries=retries)
                print(
                    f"{'*'*5} Uploaded answer URLs for directory={directory}, index={idx} {'*'*5}"
                )
        except Exception as e:
            print(f"❌ Skipping answer URLs for index {idx}: {e}")


async def run_tasks_for_csv(
    csv_path: str,
    data_dir: str,
    directory: str,
    split: str,
    jsonl_blob_prefix: str,
    file_blob_prefix: str,
    answer_blob_prefix: str,
    concurrency: int,
    retries: int,
):
    """
    For a given CSV file:
      1. Convert it to JSONL and upload to jsonl_blob_prefix/{directory}/{split}/{basename}.jsonl
      2. For each row in the CSV, download and upload associated file and answer URLs concurrently.
    """
    # Load CSV into DataFrame
    try:
        df = pd.read_csv(csv_path)
    except Exception as e:
        print(f"❌ Failed to read CSV '{csv_path}': {e}")
        return

    # Create JSONL locally and upload
    base_name = os.path.splitext(os.path.basename(csv_path))[0]
    jsonl_local_path = os.path.join(os.path.dirname(csv_path), f"{base_name}.jsonl")
    jsonl_blob_path = f"{jsonl_blob_prefix.rstrip('/')}/{directory}/{split}/{base_name}.jsonl"

    try:
        with open(jsonl_local_path, "w", encoding="utf-8") as f:
            df.to_json(f, force_ascii=False, orient="records", lines=True)
        # Upload JSONL
        proc = await asyncio.create_subprocess_exec(
            "bbb",
            "cp",
            jsonl_local_path,
            jsonl_blob_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await proc.communicate()
        if proc.returncode != 0:
            raise RuntimeError(f"bbb cp failed [{proc.returncode}]: {stderr.decode().strip()}")
        print(f"✅ Uploaded JSONL for '{csv_path}' to '{jsonl_blob_path}'")
        # Clean up local JSONL
        try:
            os.remove(jsonl_local_path)
        except OSError:
            pass
    except Exception as e:
        print(f"❌ Failed to generate or upload JSONL for '{csv_path}': {e}")
        return

    # Process each row concurrently, limited by semaphore
    semaphore = asyncio.Semaphore(concurrency)
    tasks = []
    for idx, row in df.iterrows():
        tasks.append(
            process_row(
                idx=idx,
                row=row,
                directory=directory,
                file_blob_prefix=file_blob_prefix,
                answer_blob_prefix=answer_blob_prefix,
                semaphore=semaphore,
                retries=retries,
            )
        )
    # Gather all tasks
    await asyncio.gather(*tasks, return_exceptions=True)


def find_csv_files(data_dir: str):
    """
    Walk through `data_dir` and yield tuples (directory_name, split, csv_path)
    for every CSV file under subdirectories that match the expected structure:
      data_dir/
        <directory>/
          train/*.csv
          test/*.csv
    """
    for directory in os.listdir(data_dir):
        if directory.startswith("."):
            continue
        full_dir_path = os.path.join(data_dir, directory)
        if not os.path.isdir(full_dir_path):
            continue
        for split in ("train", "test"):
            split_path = os.path.join(full_dir_path, split)
            if not os.path.isdir(split_path):
                continue
            for fname in os.listdir(split_path):
                if fname.lower().endswith(".csv"):
                    yield directory, split, os.path.join(split_path, fname)


def parse_args():
    parser = argparse.ArgumentParser(
        description="Upload DataGen CSV files and associated blobs to Azure/Azure-like storage."
    )
    parser.add_argument(
        "--data-dir",
        required=True,
        help="Root directory containing sub-folders (e.g., access_batch1/, ambiguous_batch1/) with train/ and test/ CSVs.",
    )
    parser.add_argument(
        "--file-blob-prefix",
        required=True,
        help="Blob prefix (URI) where downloaded files (from file_urls/file_url) will be uploaded.",
    )
    parser.add_argument(
        "--answer-blob-prefix",
        required=True,
        help="Blob prefix (URI) where downloaded answer files (from Answer2) will be uploaded.",
    )
    parser.add_argument(
        "--jsonl-blob-prefix",
        required=True,
        help="Blob prefix (URI) where generated JSONL files will be uploaded.",
    )
    parser.add_argument(
        "--concurrency",
        type=int,
        default=32,
        help="Maximum number of concurrent download-and-upload tasks.",
    )
    parser.add_argument(
        "--retries",
        type=int,
        default=5,
        help="Number of retry attempts for each download/upload operation.",
    )
    parser.add_argument(
        "--retry-backoff",
        type=float,
        default=1.0,
        help="Base backoff time (in seconds) multiplied by attempt count on retry.",
    )
    return parser.parse_args()


def main():
    args = parse_args()

    # Ensure data directory exists
    if not os.path.isdir(args.data_dir):
        print(
            f"❌ Data directory '{args.data_dir}' does not exist or is not a directory.",
            file=sys.stderr,
        )
        sys.exit(1)

    # Iterate over all CSV files in the expected structure
    for directory, split, csv_path in find_csv_files(args.data_dir):
        print(f"\n🔄 Processing CSV: directory='{directory}', split='{split}', path='{csv_path}'")
        try:
            asyncio.run(
                run_tasks_for_csv(
                    csv_path=csv_path,
                    data_dir=args.data_dir,
                    directory=directory,
                    split=split,
                    jsonl_blob_prefix=args.jsonl_blob_prefix,
                    file_blob_prefix=args.file_blob_prefix,
                    answer_blob_prefix=args.answer_blob_prefix,
                    concurrency=args.concurrency,
                    retries=args.retries,
                )
            )
        except KeyboardInterrupt:
            print("❌ Interrupted by user. Exiting.")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Error while processing '{csv_path}': {e}")

    print("\n🏁 All tasks completed.")


if __name__ == "__main__":
    main()
