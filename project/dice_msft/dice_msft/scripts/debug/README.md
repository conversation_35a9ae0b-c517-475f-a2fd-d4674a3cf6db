## Debugging IXF and Zen Models

IXF weights
1. Prod_IXF: with twppo.scallion.text.common
    - **az://orngtransfer/devaultoutbound/models/congcongchen/shtri-032825-dtr3-4ott-v3/snapshot.0ea9bc8950319d43ddbbe32901a8647e98b8a2928555421043ef173ae555a28e.json**
        ```
        model_config="falcon.multimodal.runs.scallion-d36-s64-lpe
        raise_on_load_for_missing_tensor=True
        twppo.scallion.text.common
        enable_tensorcache_v2=False
        ixf_max_cache_list_length=4096
        ixf_kv_block_unit_size=2048
        n_op_shards=4 pipe_depth=2 n_replicas=1
        "
        ```

2. WIP_IXF: with twppo.scallion.lpe.common

Zen weights:
1. Prod_Zen: Not yet hosted
2. WIP_Zen: with twppo.scallion.lpe.common (ev3 engine up - scraping ongoing)