import asyncio
import json
import random
import re
import xml.etree.ElementTree as ET

import blobfile as bf
from bus_token_completer import BusTokenCompleter, QoSType
from chat import Conversation, chat
from chat.render import get_renderer
from chat.render.common import render_content
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from dice_msft.prompts.grader import GRADER_XML_WITH_ICL


def get_one_sample(data_path, index=-1):
    if not data_path:
        return {
            "question": "what is 2+2?",
            "reference": "4",
            "answer": "5",
        }

    with bf.BlobFile(data_path, "rb") as fd:
        samples = list(fd)
        if index < 0:
            index = random.randint(0, len(samples))
        if index >= len(samples):
            print(
                f"=============================== Exiting at sample {index} from {data_path} set ===================================="
            )
            return None
        sample = samples[index]
    print(
        f"=============================== picking up sample {index} from {data_path} set ===================================="
    )
    return json.loads(sample)


async def main(
    data_path,
    data_index,
    fout,
):
    if sp := get_one_sample(data_path=data_path, index=data_index):
        user_query = (
            GRADER_XML_WITH_ICL.replace("{problem}", sp.get("question"))
            .replace("{canonical_solution}", str(sp.get("reference")))
            .replace("{student_answer}", str(sp.get("answer")))
        )
        # given_answer = chat.Message.user(prompt)
        user_prompt = chat.Message.user(user_query)
        grader_convo = Conversation(
            messages=[
                chat.Message.system(
                    model_identity_desc="You are an intelligent AI grader used for comparing solutions for a given question. You can provide a floating point score between 0.0 to 1.0.",
                    channel_config=chat.SystemChannelConfig(
                        valid_channels=(BerryChannel.CHAIN_OF_THOUGHT, BerryChannel.FINAL_ANSWER),
                        channel_required=True,
                    ),
                    metadata=chat.SystemContentMetadata(
                        reward_multiplier=None,
                    ),
                )
            ]
            + [user_prompt]
        )

        tokens = renderer.render_for_completion_multimodal_toklist(
            grader_convo, role=chat.Role.ASSISTANT
        )
        for _ in range(4):
            try:
                if token_completer.is_ready():
                    print(f"{token_completer.is_ready()=}\n{tokens=}")
                    completions = await token_completer.async_completion(
                        [tokens],
                        max_tokens=(2**17 - 2**16),
                        stop=renderer.stop_sequences(),  # type: ignore
                        temperature=1.0,
                    )

                    output_messages = renderer.parse(
                        completions.choices[0].toklist.spans[0].tokens, role=chat.Role.ASSISTANT
                    )

                    current_completion = render_content(output_messages, 0)
                    if current_completion.startswith("```xml"):
                        current_completion = current_completion[len("```xml") :]

                    if current_completion.endswith("```"):
                        current_completion = current_completion[:-3]

                    def extract_score_reason(xml_string):
                        # Normalize tags to standard <score> and <reason>
                        xml_string = re.sub(
                            r"<score_start>(.*?)<score_end>",
                            r"<score>\1</score>",
                            xml_string,
                            flags=re.DOTALL,
                        )
                        xml_string = re.sub(
                            r"<reason_start>(.*?)<reason_end>",
                            r"<reason>\1</reason>",
                            xml_string,
                            flags=re.DOTALL,
                        )
                        xml_string = re.sub(
                            r"<score_start>(.*?)</score_start>",
                            r"<score>\1</score>",
                            xml_string,
                            flags=re.DOTALL,
                        )
                        xml_string = re.sub(
                            r"<reason_start>(.*?)</reason_start>",
                            r"<reason>\1</reason>",
                            xml_string,
                            flags=re.DOTALL,
                        )

                        # Wrap with a root element if necessary
                        if (
                            "<final_answer>" not in xml_string.lower()
                            and "<finalanswer>" not in xml_string.lower()
                        ):
                            xml_string = f"<root>{xml_string}</root>"
                        root = ET.fromstring(xml_string)

                        # Initialize default values
                        score, reason = 0.0, None

                        # Search for the tags in the XML structure
                        if root.find(".//score") is not None:
                            score = root.find(".//score").text.strip()
                        # if root.find('.//reason') is not None:
                        #     reason = root.find('.//reason').text.strip()

                        return float(score)

                    score = extract_score_reason(current_completion)
                    completion = current_completion
                    fout.write(
                        json.dumps(
                            {
                                "score": score,
                                "completion": completion,
                            }
                        )
                        + "\n"
                    )
                    break
            except Exception as e:
                print(f"Error hitting grader bus {e}, retry {_+1}..")
                await asyncio.sleep(1)


async def run_tasks(
    filename: str,
    N: int,
    k: int,
):
    filepre = filename.split(".")[0]
    passes = []
    semaphore = asyncio.Semaphore(8)
    out_file = open(f"{filepre}_results.jsonl", "w", encoding="utf-8")

    async def process_results(index, pass_attempt):
        async with semaphore:  # Acquire semaphore
            rew = await main(
                data_path=filename,
                data_index=index,
                fout=out_file,
            )
            passes.append(rew)

    coros = [process_results(i, attempt) for i in range(N) for attempt in range(k)]
    await asyncio.gather(*coros)
    return passes


if __name__ == "__main__":
    # renderer = get_renderer("harmony_v4.0.15_16k_orion_text_only_no_asr_2k_action")
    renderer = get_renderer("harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget")
    token_completer = BusTokenCompleter(
        topic_mode_or_user="chawlapranit",
        # topic_mode_or_user="appberry",
        # topic_mode_or_user="diceberry",
        topic_or_snapshot="az://orngcresco/models/snapshots/chat-gpt-4o-tt-v3-d64_80g_bf16_fridge-step3075-250316-decrypted",
        # topic_or_snapshot="az://orngcresco/models/snapshots/models.tc/nv4-mident-run2-20250310-decrypted",
        # topic_or_snapshot="az://orngcresco/twapi/mini/e/shtri-032825-dtr3-4ott_gr-05062025-222524/policy/step_000180/",
        bus_line="bus",
        qos_type=QoSType.FIFO,
    )

    system_message = chat.Message.system(
        # model_identity_desc='You are an intelligent AI grader used for comparing solutions for a given question. You can provide a floating point score between 0.0 to 1.0.',
        model_identity_desc=None,
        channel_config=chat.SystemChannelConfig(
            valid_channels=(BerryChannel.CHAIN_OF_THOUGHT, BerryChannel.FINAL_ANSWER),
            channel_required=True,
        ),
        metadata=chat.SystemContentMetadata(
            reward_multiplier=128,
        ),
    )

    for file in [
        "/root/v3.jsonl",
        "/root/v4.jsonl",
    ]:
        results = asyncio.run(
            run_tasks(
                filename=file,
                N=500,
                k=1,
            )
        )

# for file in [
#     '/root/v3.jsonl',
#     '/root/v4.jsonl',
# ]:
#     query = "question: 2+3, answer: 4"

#     user_prompt = chat.Message.user(query)
#     conversation = Conversation(messages=[system_message] + [user_prompt])
#     tokens = renderer.render_for_completion_multimodal_toklist(conversation, role=chat.Role.ASSISTANT)
#     try:
#         if token_completer.is_ready():
#             print(f"{token_completer.is_ready()=}\n{tokens=}")
#             completions = token_completer.completion(
#                 [tokens],
#                 max_tokens=(2**17 - 2**16),
#                 stop=renderer.stop_sequences(),  # type: ignore
#                 temperature=1.0,
#             )

#             output_messages = renderer.parse(
#                 completions.choices[0].toklist.spans[0].tokens, role=chat.Role.ASSISTANT
#             )
#             current_completion = render_content(output_messages, 0)
#             print(f"{current_completion=}")
#     except:
#         pass
