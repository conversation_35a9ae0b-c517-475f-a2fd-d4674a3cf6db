#!/usr/bin/env python3
"""
Script to extract all samples from specified berry experiments and save to a JSONL file.

Usage:
    python download_snowflake.py \
        --experiment-ids shtri-prs-noans-dbv4-06052025-165059-peaval exp2 exp3 \
        --output-path /path/to/output.jsonl \
        --security-profile msft \
        [--shard 0]

Arguments:
    --experiment-ids   One or more experiment IDs from Lemon.
    --output-path      Path to write the resulting JSONL file.
    --security-profile Security profile string or identifier for RLSnowReader. "msft"/"m365"
    --shard            (Optional) Shard number to use when querying (default: 0).
"""

import argparse

import chz
from mini.finetune.datasets.jsonl import write_jsonl
from rlsnow import reader
from rlsnow.reader_impl import const


def extract_samples(
    experiment_ids: tuple[str, ...],
    output_path: str,
    security_profile: str,
    shard: int = 0,
) -> None:
    """
    Connects to RLSnow using the given security_profile, runs a query to fetch samples
    for each experiment_id, and writes the combined results to output_path in JSONL format.
    """
    # Initialize the reader with the provided security profile
    r = reader.RLSnowReader(security_profile)

    # Build a CTE that unions all SELECT statements for each experiment ID
    experiment_queries: list[str] = []
    for expt_id in experiment_ids:
        # If shard logic is needed later, swap out the hardcoded zero:
        # shard = const.compute_shard(expt_id, total_shards)
        experiment_queries.append(
            f"SELECT * FROM public.sample_events WHERE base_experiment_id = '{expt_id}'"
        )
    samples_cte = " UNION ALL ".join(experiment_queries)

    # Final query to join upload and completion events, extracting relevant JSON fields
    query = f"""
    WITH expt_samples AS ({samples_cte})
    SELECT
        b.*,
        a.data:prompt::string AS prompt,
        a.data:conversation::string AS conversation,
        a.data:metadata_slim AS metadata_slim
    FROM expt_samples a
    JOIN (
        SELECT DISTINCT
            sample_id,
            data:gt_datapoint_serializable:dataset_id::string AS dataset_id,
            data:gt_datapoint_serializable:unique_id::string AS unique_id
        FROM expt_samples
        WHERE event_type = 'driver_upload_sample_batch'
    ) b
    ON a.sample_id = b.sample_id
       AND a.event_type = 'worker_sample_complete'
    """

    # Execute the query, parsing JSON fields "prompt" and "conversation"
    samples = r.execute(
        query,
        parse_json_fields=["prompt", "conversation"],
    )

    # Write out to JSONL
    write_jsonl(output_path, samples)
    print(f"Success: saved {len(samples)} samples to {output_path}")


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Extract samples for berry experiments and save as JSONL."
    )
    parser.add_argument(
        "--experiment-ids",
        nargs="+",
        required=True,
        help="One or more experiment IDs to extract samples from (separated by spaces).",
    )
    parser.add_argument(
        "--output-path",
        required=True,
        help="Output JSONL file path (e.g., /path/to/output.jsonl).",
    )
    parser.add_argument(
        "--security-profile",
        required=True,
        help="Security profile identifier/string for RLSnowReader authentication.",
    )
    parser.add_argument(
        "--shard",
        type=int,
        default=0,
        help="Shard number to include in the sample_events query (default: 0).",
    )
    return parser.parse_args()


def main():
    args = parse_args()
    experiment_ids_tuple = tuple(args.experiment_ids)
    extract_samples(
        experiment_ids=experiment_ids_tuple,
        output_path=args.output_path,
        security_profile=args.security_profile,
        shard=args.shard,
    )


if __name__ == "__main__":
    main()
