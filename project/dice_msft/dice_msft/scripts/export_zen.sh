export OPENAI_API_KEY="dummy_fake"
export CHECKPOINT_DIR="az://orngwus2cresco/twapi/mini/e/shtri-032825-dtr3-4ott_gr-05062025-222524/policy/step_000180/25050622260733BATEDI-0/"

# export to orange storage directly
export EXPORT_DIR="az://orngwus2cresco/models/shtri/zen/shtri-032825-dtr3-4ott_gr-05062025-222524/v1/model"
export OPENAI_FEATURE_INTERLEAVE_GEGLU_DIM=1
export OPENAI_FEATURE_EXPORT_TEST_VECTORS_ALL_RAN="1"
export MODEL="falcon.multimodal.runs.scallion-d36-s64-lpe twppo.scallion.text.common microbatch_dict={1024:16,2048:8,4096:4,8192:2,16384:1,32768:1,65536:1,131072:1,262144:1,524288:1,1048576:1,2097152:1,4194304:1} enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=4 pipe_depth=2 n_replicas=1 n_ctx=1048576 eval_n_ctx=1048576 dataset_encoding=orion_200k restore_parent_state=params,unembedding weight_decay_to_init=False attn_temp_gain_nelem=1048576 ixf_implementation=zen"

nohup python -m model_export_scripts.export_inference \
  --model="$MODEL"\
  --root="twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 create_model_timeout=1080000 step_timeout=1080000 initial_step_timeout=1080000 download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000 ban_nodes= enable_wandb_hook=False enable_snowflake_hook=False"\
  --export_dir="$EXPORT_DIR"\
  --skip_falcon_comparison=True \
  --falcon_comparison_tolerance=0.06\
  --force_fp8=False\
  --falcon_path="$CHECKPOINT_DIR"\
  --skip_dataset_specific_info=True\
  --skip_snapshot_comparison=True\
  --override_ev3_mm_defaults=True\
  --add_unique_prefix=False\
  --override_checkpoint_attention_implementation=False\
  --skip_snapshot_format_verification=True\
  > ~/zen.log 2>&1 &