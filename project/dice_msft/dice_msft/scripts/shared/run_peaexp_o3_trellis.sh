#!/bin/bash

# [Aug 12,2025] Bad actor error - suspect OOM issue 
    # num_initial_samples = 16 --> 8
# [Aug 13,2025] Reduce GPUs from 232 --> 216 


INITIAL_CHECKPOINT=az://orngscuscresco/models/snapshots/o3-d64-step860-20250324-retransferred-20250401-decrypted/
RENDERER_NAME=harmony_v4.0.16_berry_v3_1mil_orion_no_budget
MAX_TOKENS=131072
dt=$(date '+%m%d%Y-%H%M%S')

beam python --start-daemon --use-cwd -m qstar.run_experiment \
nostrict \
name=dice-o3-test-16x64-scus-trellis-highutil-$dt \
:berry_models.scallion:d64_80g_bf16_fridge \
:qstar.presets.common:longer_timeout \
defaults.instances_per_batch=16 \
defaults.target_samples_per_instance=64 \
defaults.channel_config.channels=analysis,final,confidence \
defaults.inverse_token_cost=16384 \
...num_initial_samples=8 \
...min_initial_samples_per_variant=8 \
...harmony_renderer_name="$RENDERER_NAME" \
policy.initial_checkpoint="$INITIAL_CHECKPOINT" \
policy.encoding_name=orion_200k \
policy.n_gpus=216 \
policy.n_ctx="$MAX_TOKENS" \
":dice_msft.presets.trellis_client:tr_dice_train_v2_easy_trellis_dataset()" \
...dataset_container=orngscuscresco \
berry_curriculum=berry_curriculums.MultiEpochCurriculum \
berry_curriculum.max_epochs=10 \
security_profile=msft-orng \
github_upload=False \
wandb_enable=True \
wandb.wandb_project=dice_msft_train \
kafka_enable=False \
enable_slackbot=False \
seed=9999 \
skip_validate_config=True