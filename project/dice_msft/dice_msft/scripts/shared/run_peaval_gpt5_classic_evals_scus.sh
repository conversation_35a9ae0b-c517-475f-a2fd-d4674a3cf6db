# INITIAL_CHECKPOINT=az://orngcresco/models/snapshots/models.tc/nv4-mident-run2-20250310-decrypted

# DBV4 baseline
# INITIAL_CHECKPOINT=az://orngcresco/twapi/mini/e/shtri-032825-dtr3-4ott_gr-05062025-222524/policy/step_000180/25050622260733BATEDI-0/

#o3 - 8x8 test run
# INITIAL_CHECKPOINT=az://orngcresco/twapi/mini/e/kamo-dice-o3-test-8x8-07252025-233842/policy/
# o3 - 16x64 - CR run 
# INITIAL_CHECKPOINT=az://orngscuscresco/twapi/mini/e/kamo-dice-o3-highutil-16x64-scus-08092025-080608/policy/

#o3 - simple instance completer = 16x64
# INITIAL_CHECKPOINT=az://orngscuscresco/twapi/mini/e/kamo-dice-o3-test-16x64-wus-08072025-070821/policy/

#o3 - simple instance completer = 16x64 - Aug 21, 2025
# INITIAL_CHECKPOINT=az://orngscuscresco/twapi/mini/e/kamo-dice-o3-16x64-gpu232-08192025-190134/policy/step_000180/250821095322DZPCLQ2T-0/
# INITIAL_CHECKPOINT=az://orngscuscresco/twapi/mini/e/kamo-dice-o3-16x64-gpu152-08222025-180137/policy/step_000250/250825092136IA3V5WRL-0/
# INITIAL_CHECKPOINT=az://orngscuscresco/twapi/mini/e/kamo-dice-o3-16x64-gpu152-08222025-180137/policy/step_000350/250827095144EVOYDXVX-0/

# gpt 5 
INITIAL_CHECKPOINT=az://orngscuscresco/twapi/mini/e/kamo-dice-gpt5reasoning-16x64-0829-042218-ctx_131k/policy/step_000320/250901085824JKWLBGWC-0/


#o3 - 16x16 test run with all DBv4 parameters
# INITIAL_CHECKPOINT=az://orngcresco/twapi/mini/e/kamo-dice-o3-test-16x16-07272025-072149/policy/
# step_000010/250725233930GXFDH373-0/
# INITIAL_CHECKPOINT=az://orngcresco/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted
# INITIAL_CHECKPOINT=az://orngcresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted/



# IXF dbv4-rc0
# INITIAL_CHECKPOINT=az://orngcresco/data/saik/bizchat-4/2025-05-14-10-47-16/snapshot.6c2d20a8a8b6ed212038eb893f1cc084faf14cbea6da196e7d19f1c90d074bf7.json

# LPE_EXPORT=az://orngscuscresco/models/snapshots/models.tc/nv4-lpe-encoder-transferred-20250122-decrypted/clip_export/snapshot.9d0a4a4735167dfa1c54bcade9e6b3677acbc66507438b52313e004fc12c769c.json
# RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget"
RENDERER_NAME=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_tools_v2
# TOPIC_MODE_OR_USER=msft
# MAX_TOKENS=131072
MAX_TOKENS=131072
dt=`date '+%m%d%Y-%H%M%S'`

CMD=(
beam python --use-cwd --start-daemon -m qstar.run_eval
nostrict
name=dice-gpt5-16x64-scus-classic-eval-step320-$dt-peaval
#dice-o3-16x16-$dt-peaval
# name=qv2_hypo-1-exp-1-conf-$dt-peaval
max_queue_length=100000
eval_settings.eval_every=10

# Model layout preset; ensure checkpoint matches description
:berry_models.scallion:d64_chicken_80g_bf16 \
eval_settings.checkpoint_dir="$INITIAL_CHECKPOINT"
policy.initial_checkpoint="$INITIAL_CHECKPOINT"
policy.is_multimodal=True
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0}'

defaults.n_ctx="$MAX_TOKENS"
...max_tokens="$MAX_TOKENS"
...harmony_renderer_name="$RENDERER_NAME"
...sampler.harmony_constrained_sampling=True
...use_stale_kv_cache=False
...return_token_alternatives=None

...max_num_yields=100

seed=1729

# Clip settings
# ...clip_config.snapshot=$LPE_EXPORT
# ...clip_config.rapid_pool=clip
# ...clip_config.encoder_type=lpe
peashooter.sampling_use_ev3=True
peashooter.timeout_seconds.stalled_datapoint=15000
peashooter.kv_spilling=False
peashooter.queue_granularity=1

peashooter.num_sampling_processes=32
peashooter.sampling_concurrency=16

eval_settings.exit_on_no_new_checkpoint=True
...enable_special_stop_token=True
...eval_initial_policy=False
# ...channels_for_answer=final # Do not need this

defaults.variant_producer=dice_msft.datasets.configs:DiceVardiscProducer # Verify if overrides custom variant_producer
defaults.variant_producer.override_reward_multiplier=64 # Verify if overrides custom variant_producer
defaults.channel_config.channels=analysis,commentary,final,confidence
# defaults.channel_config.channels=analysis,final,confidence
# Dataset settings
# ...dataset=HarmonyCompletionDatasetConfig
:dice_msft.presets.files:complete_eval_preset\(max_n_datapoints_per_dataset=10\)
# :dice_msft.presets.files:hard_eval_preset\(max_n_datapoints_per_dataset=10\)
# :dice_msft.presets.files:hard_dablike_preset\(max_n_datapoints_per_dataset=10\)
# :dice_msft.presets.files:analyst_qv2_seval_preset\(max_n_datapoints_per_dataset=4\)
# :dice_msft.presets.files:noans_eval_preset\(max_n_datapoints_per_dataset=10\)
# :dice_msft.presets.files:cwc_seval_v2\(max_n_datapoints_per_dataset=4\)
...dataset_container=orngscuscresco

# Logging
load.restore_from_all_clusters=False
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=dice_msft_peaval
kafka_enable=False
enable_slackbot=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a dice_msft_peaval-$dt-runs.log