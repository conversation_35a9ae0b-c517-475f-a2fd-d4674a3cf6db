EXPERIMENT_NAME=dice-gpt5reasoning-16x64-$(date +%m%d-%H%M%S)-ctx_131k
LOGFILE=${EXPERIMENT_NAME}.log

CMD=(
beam python --start-daemon --use-cwd -m qstar.run_experiment nostrict
name=$EXPERIMENT_NAME
# name=pdw2-test-sbh-run1
seed=20250807

skip_validate_config=True

# ## Policy settings
:berry_models.scallion:d64_chicken_80g_bf16

root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0}'


# gpt-5
policy.initial_checkpoint="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"

policy.n_gpus=152
policy.is_multimodal=True 
...encoding_name=orion_200k
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_tools_v2

# Systems settings
:qstar.presets.common:longer_timeout
peashooter.sampling_use_ev3=True
peashooter.timeout_seconds.stalled_datapoint=7200
peashooter.timeout_seconds.lost_datapoint=360 
timeout.default=None

# Model settings
# max_steps=20 
policy.n_ctx=131072
defaults.n_ctx=131072
# defaults.sampler.stop_tokens="<|im_end|>,<|fim_suffix|>" 
...harmony_constrained_sampling=True
optimizer.hparam_scaler.lr_per_instance_d16=1e-5
...save_every=10

# Dataset configs!
:dice_msft.presets.files:train_v2_preset\(max_n_datapoints_per_dataset=1000\)
...dataset_container=orngscuscresco

berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=10

# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
defaults.inverse_token_cost=1024
defaults.instances_per_batch=16
defaults.target_samples_per_instance=64

# Enable the COMMENTARY channel
defaults.channel_config.channels="analysis,final,confidence" #  The channels that the policy can use, i.e. the channels that appear in the system prompt.

# Sampling and timeout 
batch_completer.n_batches_in_flight=2 # number of batches in flight
peashooter.num_sampling_processes=32 # number of sampling processes per instance worker
peashooter.sampling_concurrency=8 # concurrency sampling threads per process
peashooter.num_instance_workers=16 # number of instance workers

# Dataset configs
defaults.instance_completer=qstar.instance_completers:VariantsInstanceCompleter
defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=dice_msft_train
kafka_enable=False
enable_slackbot=False
)

# Print the assembled command
echo "🛠️ Command: ${CMD[@]}"

# Execute and log
"${CMD[@]}" 2>&1 | tee "$LOGFILE"