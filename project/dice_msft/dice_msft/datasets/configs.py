from functools import partial
from typing import Any, Callable

import chz
import structlog
from berry import InstanceCompleter, InstanceOptimizer, SampleAllocator
from berry.function_wrapper import FunctionWrapper
from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from chat.render.v4.experimental.strawberry.formatter import <PERSON><PERSON>hannel
from qstar.allocators.continuous_rewards_adaptive_sample_allocator import (
    ContinuousRewardsAdaptiveSampleAllocator,
)
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.common.tools.caas_container.caas_container_tool import (
    CaasContainerResourceConfig,
    resolve_setup_fn,
)
from qstar.common.tools.python.python_tool import JupyterKernelResourceConfig, PythonToolConfig
from qstar.curriculums.alt_config_variant_producer import AltConfigVariant, AltConfigVariantProducer
from qstar.curriculums.dataset_config import ChannelConfig, HarmonyCompletionDatasetConfig
from qstar.curriculums.variant_producer import (
    CompositeVariantProducer,
    ToolVariant,
    ToolVariantProducer,
    VarDiscountingVariantProducer,
    VariantProducer,
)
from qstar.presets.chz_utils import IsOverride, override
from rcall.runtime import get_func_path

DICE_IMAGE_NAME = {
    "0115": "acrbuiltincaasglobalame.azurecr.io/dice:base-20250115a-jupyter-from-ml-base"
}


@chz.chz
class DiceVardiscProducer(VarDiscountingVariantProducer, IsOverride):
    reward_distribution_power: float = 1.0
    min_reward_multiplier: int = 64
    max_reward_multiplier: int = 512
    num_reward_multipliers: int = 1
    override_reward_multiplier: int = 128


@chz.chz
class DicePythonDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngcresco")
    tool_configs: tuple[ToolConfig, ...] = chz.field(
        default=(
            PythonToolConfig(
                is_user_visible=False,
            ),
        )
    )

    # Defaults to a jupyter kernel resource
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            JupyterKernelResourceConfig(
                setup_fn="dice_msft.common.setup:dice_setup_fn",
                image=DICE_IMAGE_NAME["0115"],
                caas_endpoint="https://westus2.caas.azure.com",
            ),
        )
    )

    max_num_yields: int = 500
    variant_producer: VariantProducer | None = override(DiceVardiscProducer)


@chz.chz
class DicePythonUserVisibleDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngcresco")
    tool_configs: tuple[ToolConfig, ...] = chz.field(
        default=(
            PythonToolConfig(
                is_user_visible=True,
            ),
        )
    )

    # Defaults to a jupyter kernel resource
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            JupyterKernelResourceConfig(
                setup_fn="dice_msft.common.setup:dice_setup_fn",
                image=DICE_IMAGE_NAME["0115"],
                caas_endpoint="https://westus2.caas.azure.com",
            ),
        )
    )

    max_num_yields: int = 500
    variant_producer: VariantProducer | None = override(DiceVardiscProducer)


@chz.chz
class DABStepPythonDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngcresco")
    tool_configs: tuple[ToolConfig, ...] = chz.field(
        default=(
            PythonToolConfig(
                is_user_visible=False,
            ),
        )
    )

    # Defaults to a jupyter kernel resource
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            JupyterKernelResourceConfig(
                setup_fn="dice_msft.common.setup:dabstep_setup_fn",
                image=DICE_IMAGE_NAME["0115"],
                caas_endpoint="https://westus2.caas.azure.com",
            ),
        )
    )

    max_num_yields: int = 500
    variant_producer: VariantProducer | None = override(DiceVardiscProducer)


@chz.chz
class DataGenJSONLDatasetConfig(DicePythonDatasetConfig, IsOverride):
    dataset_id: str = ("data.shtri.dice.viz.jsonl_test",)
    # X_max_n_datapoints: int = chz.field(default=5)

    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            FunctionWrapper(
                name="dice_msft.common.datapoint_converters:datagen_jsonl_to_sample",
                kwargs={
                    "base_name": "default",
                    "is_ppo": True,
                    "workspace": "sandbox",
                },
            ),
        )
    )


@chz.chz
class CAPIJSONLDatasetConfig(DicePythonDatasetConfig, IsOverride):
    channel_config: ChannelConfig | None = chz.field(
        default=(
            ChannelConfig(
                channels=(
                    BerryChannel.CHAIN_OF_THOUGHT,
                    BerryChannel.COMMENTARY,
                    # BerryChannel.ANSWER_JUSTIFICATION,
                    BerryChannel.FINAL_ANSWER,
                    BerryChannel.CONFIDENCE,
                ),
            ),
        ),
    )
    dataset_id: str = ("data.shtri.dice.viz.jsonl_test",)
    # X_max_n_datapoints: int = chz.field(default=5)

    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            FunctionWrapper(
                name="dice_msft.common.datapoint_converters:analyst_qv2_datagen_jsonl_to_sample",
                kwargs={
                    "base_name": "default",
                    "is_ppo": True,
                    "workspace": "sandbox",
                },
            ),
        )
    )
