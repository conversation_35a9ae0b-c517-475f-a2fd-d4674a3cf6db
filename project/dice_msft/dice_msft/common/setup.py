from __future__ import annotations

import asyncio
import logging
import os
import re
import uuid
from typing import Any

import blobfile as bf
import chz
import import_contexts
from caas import commands
from caas.api import CaasSession
from caas.experimental.compose.api import ComposeSession
from chat.tools import Tool
from dice_msft.common.datapoint_converters import WORKSPACES
from qstar.common import datapoint
from research_ace.v2.client.caas_impl import (  # CaasJupyterKernelRuntimeFactory,
    JupyterKernelRuntimeCaasImpl,
)
from tool_resources import Resource

# with import_contexts.load_lazily():
#     import qstar.common.tools.vision_cot.vision_cot_tool as vision_cot_tool

logger = logging.getLogger(__name__)

# orngscuscresco, orngwus2cresco


async def dice_setup_fn(
    *,
    datapoint: dict[str, Any],
    runtime: JupyterKernelRuntimeCaasImpl,
    workspace: str = "sandbox",
) -> None:
    # temp for Zhenning
    FILEPATH = "az://orngscuscresco/data/shtri/022425_raw_files/v2/"
    # original
    # FILEPATH = "az://orngwus2cresco/data/shtri/022425_raw_files/v2/"

    file_setup = datapoint.get("metadata", {}).get("filenames", [])
    retries = 4

    if file_setup:
        for files in file_setup:
            with bf.BlobFile(os.path.join(FILEPATH, files), "rb") as fd:
                for attempt in range(retries):
                    try:
                        session: CaasSession | ComposeSession = runtime._session._session
                        await session.run(
                            commands.UploadFile(f"{WORKSPACES[workspace]}/{files}", fd.read())
                        )
                        logger.info(
                            f"File upload for {files} attempt {attempt + 1} ran successfully"
                        )
                        break
                    except Exception as e:
                        logger.info(
                            f"File upload for {files} attempt {attempt + 1} failed with error {e}, retry ..."
                        )
                        if attempt < retries - 1:
                            await asyncio.sleep(min(1 * (2**attempt), 16))
                        else:
                            break


async def dabstep_setup_fn(
    *,
    datapoint: dict[str, Any],
    runtime: JupyterKernelRuntimeCaasImpl,
    workspace: str = "sandbox",
) -> None:
    retries = 4
    DABSTEP_FILES = "az://orngcresco/data/shtri/dice/dabstep/"
    for f in [
        "acquirer_countries.csv",
        "fees.json",
        "manual.md",
        "merchant_category_codes.csv",
        "merchant_data.json",
        "payments-readme.md",
        "payments.csv",
    ]:
        with bf.BlobFile(os.path.join(DABSTEP_FILES, f), "rb") as fd:
            for attempt in range(retries):
                try:
                    session: CaasSession | ComposeSession = runtime._session._session
                    await session.run(
                        commands.UploadFile(
                            f"{WORKSPACES[workspace]}/{f}",
                            fd.read(),
                        )
                    )
                    print(f"File upload for {f} attempt {attempt + 1} ran successfully..")
                    break
                except Exception as e:
                    print(
                        f"File upload for {f} attempt {attempt + 1} failed with error {e}, retry ..."
                    )
                    if attempt < retries - 1:
                        asyncio.sleep(min(1 * (2**attempt), 16))
                    else:
                        raise
