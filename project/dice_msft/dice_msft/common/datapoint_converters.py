import ast
import json
import os
import random
import re
from typing import Any, Dict, Iterator

import pandas as pd
from chat import chat
from chat.chat import MessageContentType
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from dice_msft.prompts.instructions import (
    ANALYST_QV2_DEV,
    ANALYST_QV2_SYS,
    CAPI_SYS_0,
    DEFAULT_SYS_IDENTITY,
    DEV_INSTRUCTION_V0,
    DEV_INSTRUCTION_V1,
    SYS_INSTRUCTION_V1,
)
from functions.core import Function
from functions.schemas import ObjectSchema, StringSchema
from research_ace.v2.tools.python_code_execution_tool import get_instructions

CODE_TIMEOUT = 60000
WORKSPACES = {
    "dice": "/workspace-dice",
    "sandbox": "/home/<USER>/data",
    "mnt": "/mnt/data",
}


def format_task_interpretation(
    user_message: str, search_folder_path: str | list[str] | None = None
) -> str:
    if search_folder_path:
        if isinstance(search_folder_path, list):
            # If it's a list of file paths, join them with a comma separator.
            paths_str = ", ".join(search_folder_path)
            return (
                f"[TASK INTERPRE<PERSON>TION] Use `python` to execute Python code and accomplish the following task:\n"
                f"{user_message}\n"
                f"If the user's request refers to uploaded data when leveraging `python`, use file(s) located at {paths_str}"
            )
        else:
            return (
                f"[TASK INTERPRETATION] Use `python` to execute Python code and accomplish the following task:\n"
                f"{user_message}\n"
                f"If the user's request refers to uploaded data when leveraging `python`, use file(s) located at {search_folder_path}"
            )
    else:
        return "# Task\n\n" f"{user_message}"


def format_few_shot_examples(
    user_message: str, search_folder_path: str | list[str] | None = None
) -> str:
    if search_folder_path:
        if isinstance(search_folder_path, list):
            # If it's a list of file paths, join them with a comma separator.
            paths_str = ", ".join(search_folder_path)
            return (
                f"When leveraging `python`, use the files located at `[{paths_str}]`.\n\n"
                "# Task\n\n"
                f"{user_message}"
            )
        else:
            return (
                f"When leveraging `python`, use the file located at `{search_folder_path}`.\n\n"
                "# Task\n\n"
                f"{user_message}"
            )
    else:
        return "# Task\n\n" f"{user_message}"


def replace_filename(text: str, filename: str, search_path: str, label: str) -> str:
    """
    Replace occurrences of the filename in the given text with the search_path.
    The replacement is done in two stages:
      1. Replacing occurrences preceded by a directory prefix.
      2. Replacing bare filename occurrences using negative lookbehind.
    If replacements are made, debug info is printed.
    """
    original_text = text
    pattern_with_prefix = r"(?:working_dir|working\-dir|work)[\\/]+" + re.escape(filename)
    text, count1 = re.subn(pattern_with_prefix, search_path, text)

    total_replacements = count1
    if total_replacements > 0 and random.random() < 0.002:
        print(
            f"DEBUG: Found and replaced {total_replacements} occurrence(s) in {label} using patterns with prefix and bare filename with '{search_path}'."
        )
        # print(f"DEBUG: Original {label}: {original_text}")
        # print(f"DEBUG: New {label}: {text}")
    return text


def replace_filename_multifile(
    text: str, filenames: list[str], search_paths: list[str], label: str
) -> str:
    """
    For multifile scenarios: iterates over each filename and corresponding search path,
    replacing occurrences in the given text.
    """
    for fname, sp in zip(filenames, search_paths):
        text = replace_filename(text, fname, sp, label)
    return text


def datagen_jsonl_to_sample(
    dp: Dict[str, Any],
    base_name: str = "default",
    is_ppo: bool = True,
    workspace: str = "sandbox",
) -> list[dict[str, Any]]:
    """
    Build a chat.Conversation for a row in the JSONL.
    We always treat file references as a list (even for single-file cases)
    so that both single- and multifile scenarios are processed uniformly.
    """

    # Convert Answer1: if Answer1 is "No Answer found", record it as "[NO_ANSWER_FOUND]".
    answer = ""
    if base_name.startswith("viz_"):
        if base_name.endswith("batch1"):
            raw = dp.get("Answer2", "")
            answer = ",".join(x.strip() for x in raw.split("\t") if x.strip())
        elif base_name.endswith("batch2"):
            raw = dp.get("Answer2", "{}")
            answer = (
                ",".join(
                    x.split("/")[-1]
                    for x in json.loads(raw)
                    if x.strip() and not x.endswith("json")
                )
                if raw
                else ""
            )

    else:
        answer = dp.get("Answer1", "")
        if not isinstance(answer, str):
            answer = str(answer)
        if answer == "No Answer found":
            answer = "[NO_ANSWER_FOUND]"

    tool_instructions = get_instructions(
        name="python",
        timeout=15 * 60.0,
        enable_matplotlib_instructions=False,  # chart instructions
    ).replace("/mnt/data", f"{WORKSPACES[workspace]}")

    system_msg = chat.Message.system(
        # model_identity_desc=DEFAULT_SYS_INSTRUCTION,
        model_identity_desc=None,
        instructions=SYS_INSTRUCTION_V1,
        tools_section={
            "python": tool_instructions,
        },
        # To keep same as defaults.channel_config.channels
        channel_config=chat.SystemChannelConfig(
            valid_channels=(
                BerryChannel.CHAIN_OF_THOUGHT,
                BerryChannel.FINAL_ANSWER,
                # BerryChannel.COMMENTARY,
                # BerryChannel.ANSWER_JUSTIFICATION,
                BerryChannel.CONFIDENCE,
                # BerryChannel.DELEGATE_SUMMARY,
            ),
            channel_required=True,
        ),
        metadata=chat.SystemContentMetadata(
            reward_multiplier=None,
        ),
    )

    developer_msg = chat.Message.developer(instructions=DEV_INSTRUCTION_V0)

    filenames = []
    search_paths = []
    file_list = []

    # Try to read from file_urls (if provided)
    if dp.get("file_urls", ""):
        file_urls = dp.get("file_urls", "")
        try:
            file_list = json.loads(file_urls)
            if not isinstance(file_list, list):
                file_list = [file_list]
        except json.JSONDecodeError as e:
            # Fallback: Try parse as literal
            file_list = ast.literal_eval(file_urls)
        except Exception as e:
            # Fallback: assume comma-separated list.
            file_list = [x.strip() for x in file_urls.split(",") if x.strip()]
    # Otherwise, fallback to file_url (wrap it into a list)
    elif (file_url := dp.get("file_url", "")) and isinstance(file_url, str) and file_url.strip():
        file_list = [file_url.strip()]

    # Build filenames and search paths from the file_list (even if single file)
    for f in file_list:
        base_file = os.path.basename(f.strip())
        # if not base_file.isascii():
        #     raise ValueError(f"Skipping conversation due to invalid file name: {base_file}")
        filenames.append(base_file)
        search_paths.append(f"{WORKSPACES[workspace]}/{base_file}")

    # Use the list of search paths if available
    formatted_user_content = format_few_shot_examples(
        dp.get("query", ""), search_paths if search_paths else None
    )
    user_msg = chat.Message.user(content=formatted_user_content)

    # Build the initial messages list.
    messages = [system_msg, developer_msg, user_msg]
    conversation_metadata = {
        "question": dp.get("query", ""),
        "user_query": formatted_user_content,
        "reference": answer,
        "Answer_dtype": dp.get("Answer_dtype", None),
        "category": base_name,
        "filenames": filenames,
    }

    if is_ppo:
        return [
            {
                "problem": chat.Conversation(messages=messages, metadata=conversation_metadata),
                "answer": answer,
                "subject": base_name,
                "metadata": conversation_metadata,
            }
        ]

    # Process chain-of-thoughts (cots) if available.
    cots = json.loads(dp.get("global_iterations_x"))
    for cot in cots[:-1]:
        # Combine analysis and reasoning from the cot.
        analysis_text = cot["analysis"] + cot["reasoning"]
        if filenames:
            analysis_text = replace_filename_multifile(
                analysis_text, filenames, search_paths, "analysis_text"
            )
        messages.append(
            chat.Message.assistant(
                content=analysis_text, channel=BerryChannel.CHAIN_OF_THOUGHT, end_turn=False
            )
        )

        # Process the extracted code after stripping markdown formatting.
        extracted_code = re.sub(r"```python\s*|```", "", cot["code"]).strip()
        if filenames:
            extracted_code = replace_filename_multifile(
                extracted_code, filenames, search_paths, "extracted_code"
            )
        messages.append(
            chat.Message.assistant(
                content=chat.make_content(
                    "code", json.dumps({"code": extracted_code, "timeout": CODE_TIMEOUT})
                ),
                recipient=chat.DEFAULT_RECIPIENT,
                channel=BerryChannel.CHAIN_OF_THOUGHT,
                end_turn=False,
            )
        )
        messages.append(
            chat.Message.tool(
                author_name="tool",
                content=cot["Execution output"],
                channel=BerryChannel.CHAIN_OF_THOUGHT,
            )
        )

    # Final cot processing.
    messages.append(
        chat.Message.assistant(
            content=cots[-1]["analysis"] + cots[-1]["reasoning"] + cots[-1]["completion"],
            channel=BerryChannel.CHAIN_OF_THOUGHT,
            end_turn=False,
        )
    )

    final_completion = f"The final answer is \\boxed{{{answer}}}"

    # Append a final assistant message with the updated (boxed) answer.
    messages.append(
        chat.Message.assistant(
            content=final_completion, channel=BerryChannel.FINAL_ANSWER, end_turn=True
        )
    )

    return [
        {
            "problem": chat.Conversation(messages=messages),
            "answer": dp.get("answer"),
            "metadata": dp.get("metadata"),
        }
    ]


def analyst_qv2_datagen_jsonl_to_sample(
    dp: Dict[str, Any],
    base_name: str = "default",
    is_ppo: bool = True,
    workspace: str = "sandbox",
) -> list[dict[str, Any]]:
    """
    Build a Conversation for a row like analyst quality v2 update
    """

    # Convert Answer1: if Answer1 is "No Answer found", record it as "[NO_ANSWER_FOUND]".
    answer = ""
    if base_name.startswith("viz_"):
        if base_name.endswith("batch1"):
            raw = dp.get("Answer2", "")
            answer = ",".join(x.strip() for x in raw.split("\t") if x.strip())
        elif base_name.endswith("batch2"):
            raw = dp.get("Answer2", "{}")
            answer = (
                ",".join(
                    x.split("/")[-1]
                    for x in json.loads(raw)
                    if x.strip() and not x.endswith("json")
                )
                if raw
                else ""
            )
    else:
        answer = dp.get("Answer1", "")
        if not isinstance(answer, str):
            answer = str(answer)
        if answer == "No Answer found":
            answer = "[NO_ANSWER_FOUND]"

    tool_instructions = get_instructions(
        name="python",
        timeout=15 * 60.0,
        enable_matplotlib_instructions=False,  # chart instructions
    ).replace("/mnt/data", f"{WORKSPACES[workspace]}")

    system_msg = chat.Message.system(
        # model_identity_desc=DEFAULT_SYS_INSTRUCTION,
        model_identity_desc="",  # None goes to default You're ChatGPT
        instructions=CAPI_SYS_0,
        # tools_section={
        #     "python": tool_instructions,
        # },
        tools_section=None,  # As per match with CAPI request
        # To keep same as defaults.channel_config.channels
        channel_config=chat.SystemChannelConfig(
            valid_channels=(
                BerryChannel.CHAIN_OF_THOUGHT,
                BerryChannel.COMMENTARY,
                # BerryChannel.ANSWER_JUSTIFICATION,
                BerryChannel.FINAL_ANSWER,
                BerryChannel.CONFIDENCE,
                # BerryChannel.DELEGATE_SUMMARY,
            ),
            channel_required=True,
            deprecated_knowledge_cutoff="2024-06-01",
            tool_channel_constraints={
                BerryChannel.COMMENTARY: [
                    "functions",
                ]
            },
        ),
        metadata=chat.SystemContentMetadata(
            reward_multiplier=None,
        ),
    )

    filenames = []
    search_paths = []
    file_list = []

    # Try to read from file_urls (if provided)
    if dp.get("file_urls", ""):
        file_urls = dp.get("file_urls", "")
        try:
            file_list = json.loads(file_urls)
            if not isinstance(file_list, list):
                file_list = [file_list]
        except json.JSONDecodeError as e:
            # Fallback: Try parse as literal
            file_list = ast.literal_eval(file_urls)
        except Exception as e:
            # Fallback: assume comma-separated list.
            file_list = [x.strip() for x in file_urls.split(",") if x.strip()]
    # Otherwise, fallback to file_url (wrap it into a list)
    elif (file_url := dp.get("file_url", "")) and isinstance(file_url, str) and file_url.strip():
        file_list = [file_url.strip()]

    # Build filenames and search paths from the file_list (even if single file)
    for f in file_list:
        base_file = os.path.basename(f.strip())
        if not base_file.isascii():
            raise ValueError(f"Skipping conversation due to invalid file name: {base_file}")
        filenames.append(base_file)
        search_paths.append(f"{WORKSPACES[workspace]}/{base_file}")

    # Use the list of search paths if available
    formatted_user_content = format_task_interpretation(
        dp.get("query", ""), search_paths if search_paths else None
    )

    developer_msg = chat.Message.developer(
        function_namespaces=[
            chat.FunctionNamespace(
                name="functions",
                functions=[
                    Function(
                        name="python",
                        description="Utility for executing python code.",
                        object_schema=ObjectSchema(
                            properties={
                                "code": StringSchema(
                                    description="Python code to be executed.",
                                ),
                            },
                            required=["code"],
                        ),
                        raw_parameters={
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "description": "Python code to be executed.",
                                }
                            },
                            "required": ["code"],
                        },
                    )
                ],
            ),
        ],
        instructions=ANALYST_QV2_SYS,
    )

    developer_msg_2 = chat.Message.developer(ANALYST_QV2_DEV)
    user_prequel_msg = chat.Message.user(
        content=dp.get("query", ""),
    )

    assistant_file_upload_msgs = [
        chat.Message.assistant(
            content=chat.Code(
                language=chat.Language.JSON,
                text=json.dumps(
                    {
                        "doc_name": filenames[0],
                    }
                ),
            ),
            recipient="functions.fetch_file",
        )
    ]
    tool_file_upload_msgs = [
        chat.Message.tool(
            content=json.dumps(
                {
                    "index": "0",
                    "FileContent": "Access the file through Python execution to view its content.",
                    "FileName": filenames[0],
                    "ResponseType": "Success",
                }
            ),
            author_name="fetch_file",
        ),
    ]
    user_msg = chat.Message.user(content=formatted_user_content)

    messages = [
        system_msg,
        developer_msg,
        developer_msg_2,
        user_prequel_msg,
        *assistant_file_upload_msgs,
        *tool_file_upload_msgs,
        user_msg,
    ]

    conversation_metadata = {
        "question": dp.get("query", ""),
        "user_query": formatted_user_content,
        "reference": answer,
        "Answer_dtype": dp.get("Answer_dtype", None),
        "category": base_name,
        "filenames": filenames,
    }

    return [
        {
            "problem": chat.Conversation(messages=messages, metadata=conversation_metadata),
            "answer": answer,
            "subject": base_name,
            "metadata": conversation_metadata,
        }
    ]
