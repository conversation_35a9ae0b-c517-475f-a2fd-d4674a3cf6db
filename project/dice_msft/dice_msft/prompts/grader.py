GRADER_XML_WITH_ICL = """I am given a question about a data analysis task, along with two solutions: one from a teacher (the canonical solution) and one from a student. I need to compare these two solutions strictly in terms of content and completeness. My goal is to assign a score between -0.5 and 1.0, along with a brief explanation for that score. I must always output this as valid XML with the fields "score" and "reason".

Here is my scoring rubric:

• -0.5: This is used ONLY when the student solution explicitly says "No answer found" but the teacher’s solution provides a valid answer.
• 0: The student’s solution either fails to address the question at all or has zero content overlap with the teacher’s solution.
• 1: The student’s solution exactly matches the teacher’s solution (in terms of actual content needed to answer the question). Minor differences in formatting or variable names do not affect the content match.
• Any other score from -0.5 to 1.0 can be used to indicate partial correctness. For example, if the student solution provides some correct elements but is missing key parts, I might assign 0.5, 0.23, 0.75, etc., and briefly explain which parts match and which parts are missing.

Below are examples demonstrating how I apply the scoring:

Example 1:
Question:
<question_start>
Which organization has the highest average rate across 'Consumer Directed', 'Consumer Directed Enhanced', and 'Consumer Directed Live In' services, and what are its details?
<question_end>

Teacher solution:
<teacher_solution_start>
{'Organization': 'CONCEPTS OF INDEPENDENT CHOICES, INC.', 'Organization code': 240779859, 'Location': 'WESTCHESTER', 'Consumer Directed rate': 22.31, 'Consumer Directed Enhanced rate': 22.85, 'Consumer Directed Live In rate': 290.03, 'Average rate': 111.73}
<teacher_solution_end>

Student solution:
<student_solution_start>
{'Organization': '0240779859', 'Name': 'CONCEPTS OF INDEPENDENT CHOICES, INC.', 'Date': '2022-01-01','County': 'WESTCHESTER', 'Consumer Directed': 22.31, 'Consumer Directed Enhanced': 22.85, 'Consumer Directed Live In': 290.03, 'Average Rate': 111.73}
<student_solution_end>

Final Answer:
<reason_start>
Name, Code (ignoring the zero prefix) and location of the organization are same for both solutions. Extra 'Date' key doesn't affect correctness.
<reason_end>
<score_start>
1.0
<score_end>

Explanation:
<explanation_start>
The student’s answer is effectively the same as the teacher’s. Minor name and formatting differences do not affect correctness.
<explanation_end>

----------------------------------------------------

Example 2:
Question:
<question_start>
What is the total expenditure on pupil personnel services and business services for the school district 'McKeesport Area SD' in the years 2015-16 and 2016-17?
<question_end>

Teacher solution:
<teacher_solution_start>
[2178519, 2317047]
<teacher_solution_end>

Student solution:
<student_solution_start>
(3560800.0, 4634094.0)
<student_solution_end>

Final Answer:
<reason_start>
The values provided by the student are not in the approximate range of the teacher’s values.
<reason_end>
<score_start>
0.0
<score_end>

Explanation:
<explanation_start>
The student’s data doesn’t match or approximate the teacher’s data.
<explanation_end>

----------------------------------------------------

Example 3:
Question:
<question_start>
Identify the fiscal quarter with the highest revenue growth rate and provide the corresponding gross profit margin. Similarly, determine the fiscal quarter with the lowest revenue growth rate and provide the corresponding gross profit margin.
<question_end>

Teacher solution:
<teacher_solution_start>
{'highest': {'quarter': '2Q12', 'GPM': 24.11}, 'lowest': {'quarter': '1Q19', 'GPM': 18.25}}
<teacher_solution_end>

Student solution:
<student_solution_start>
("2Q12", 0.241133, "1Q19", 0.182494)
<student_solution_end>

Final Answer:
<reason_start>
The quarters and their corresponding gross profit margins match perfectly (24.11% vs. 0.241133, etc.).
<reason_end>
<score_start>
1.0
<score_end>

Explanation:
<explanation_start>
Despite the student’s numeric format, the content is the same.
<explanation_end>

----------------------------------------------------

Example 4 (partial match with extra data):
Question:
<question_start>
Which modules have 'Please select' status, and what appropriate modules from the 'Lists' sheet can replace these placeholders?
<question_end>

Teacher solution:
<teacher_solution_start>
{'Fall': ['CH-100', 'CH-120', 'CH-140'], 'Spring': ['CH-101', 'CH-121', 'CH-141']}
<teacher_solution_end>

Student solution:
<student_solution_start>
(["CH-XXX", "CTLA-GER-XX/CTHU-HUM-XXX"], ["CH-100 General Biochemistry", "CH-120 General & Inorganic Chemistry", "CH-140 Classical Physics", "CH-221 Mathematical & Physical Foundations of Robotics I", "CH-230 Programming in C/C++", "CH-233 Mathematical Foundations of CS", "CH-241 General Logistics", "CH-300 Introduction to International Business", "CH-320 Introduction to the Social Sciences I", "CH-330 Introduction to International Relations Theory", "CH-700 Introduction to Data Science", "SDT-104 Scientific Programming in Python", "CH-101 General Cell Biology", "CH-121 Introduction to Biotechnology", "CH-141 Modern Physics", "CH-152 Mathematical Modeling", "CH-212 Foundations of Communications and Electronics", "CH-222 Mathematical & Physical Foundations of Robotics II", "CH-231 Algorithms & Data Structures", "CH-234 Digital Systems & Computer Architecture", "CH-240 General Industrial Engineering", "CH-301 Introduction to Finance & Accounting", "CH-321 Introduction to the Social Sciences II", "CH-331 Introduction to Modern European History", "CH-701 Data Structures & Processing", "SDT-103 Development in JVM Languages"])
<student_solution_end>

Final Answer:
<reason_start>
Student solution references many extra modules. Some correct matches exist (CH-100, CH-120, CH-140, CH-101, CH-121, CH-141). Score reflects partial correctness.
<reason_end>
<score_start>
0.23
<score_end>

Explanation:
<explanation_start>
The student’s list is a superset with extraneous items.
<explanation_end>

----------------------------------------------------

Example 5 (missing certain correlations but partial match):
Question:
<question_start>
What are the Pearson correlation coefficients between S, TS, B, and TB gene numbers for each GO term?
<question_end>

Teacher solution:
<teacher_solution_start>
{'S vs. B': 0.985185, 'TS and TB': 'NaN'}
<teacher_solution_end>

Student solution:
<student_solution_start>
{('S gene number', 'TS gene number'): None, ('S gene number', 'B gene number'): 0.985185, ('S gene number', 'TB gene number'): None, ('TS gene number', 'B gene number'): None, ('TS gene number', 'TB gene number'): None, ('B gene number', 'TB gene number'): None}
<student_solution_end>

Final Answer:
<reason_start>
Both solutions show a correlation only between S and B with the same coefficient (0.985185); everything else is NaN or None.
<reason_end>
<score_start>
1.0
<score_end>

Explanation:
<explanation_start>
The student’s numeric correlation data aligns with the teacher’s.
<explanation_end>

----------------------------------------------------

Example 6 (special case of -0.5):
Question:
<question_start>
Could you determine the sensor's operating temperature range during the data recording and compare it with changes in gy and gyf to assess temperature-induced drift?
<question_end>

Teacher solution:
<teacher_solution_start>
(100K, 125K, "Comparison not possible")
<teacher_solution_end>

Student solution:
<student_solution_start>
[NO_ANSWER_FOUND]
<student_solution_end>

Final Answer:
<reason_start>
Student explicitly states no answer, yet teacher provides a valid one.
<reason_end>
<score_start>
-0.5
<score_end>

Explanation:
<explanation_start>
This is the special case. The student’s solution is "No Answer found" while the teacher has an actual solution.
<explanation_end>

----------------------------------------------------

Example 7 (demonstrating a partial match of 0.5):
Question:
<question_start>
Calculate the total counts of A, B, and C among the entire population, and identify which category is the highest.
<question_end>

Teacher solution:
<teacher_solution_start>
{'A_count': 150, 'B_count': 120, 'C_count': 30, 'Highest': 'A'}
<teacher_solution_end>

Student solution:
<student_solution_start>
{'A_count': 150, 'B_count': 120, 'C_count': 30}
<student_solution_end>

Final Answer:
<reason_start>
The student correctly provides the counts for A, B, and C but does not identify the highest category.
<reason_end>
<score_start>
0.5
<score_end>

Explanation:
<explanation_start>
The student’s solution is correct for the raw counts but omits which category is highest.
<explanation_end>

----------------------------------------------------

Example 8 (special case when teacher solution is No answer):
Question:
<question_start>
What is the total revenue reported by Visionary Holdings Inc. in their most recent annual filing?
<question_end>

Teacher solution:
<teacher_solution_start>
[NO_ANSWER_FOUND]
<teacher_solution_end>

Student solution:
<student_solution_start>
The JSON payload does not provide a clear figure for total revenue, so the document must be checked separately for that information.  
<student_solution_end>

Final Answer:
<reason_start>
Student has the correctly identified no answer is possible for the question, same as teacher.
<reason_end>
<score_start>
1.0
<score_end>

Explanation:
<explanation_start>
Student has correctly identified answering for the question is not possible.
<explanation_end>

----------------------------------------------------

Example 9 (another partial example with a 0.75 score):
Question:
<question_start>
Please compute the mean and median for the dataset [5, 8, 8, 10, 12, 12, 12] and identify if the distribution is skewed.
<question_end>

Teacher solution:
<teacher_solution_start>
{'mean': 9.0, 'median': 10, 'skew': 'Slightly left-skewed'}
<teacher_solution_end>

Student solution:
<student_solution_start>
{'mean': 9.0, 'median': 10, 'skew': 'Symmetrical'}
<student_solution_end>

Final Answer:
<reason_start>
Student has the correct mean and median but reports a different skew than the teacher.
<reason_end>
<score_start>
0.75
<score_end>

Explanation:
<explanation_start>
The student is mostly correct (mean and median match) but misses on the skew characterization.
<explanation_end>

----------------------------------------------------

When I produce my final output, I must do the following:
1) Read the question.
2) Look at the teacher’s canonical solution.
3) Compare it with the student’s solution.
4) Output a XML with two fields: "score" and "reason". The "score" must be in the range -0.5 to 1.0 and the "reason" must be a short statement.
5) If the student explicitly writes "No answer found" while the teacher solution exists, I must give a score of -0.5. If the student solution is an exact content match, I assign 1.0. If there is no meaningful overlap, I assign 0. If the student solution is partially correct, I give an appropriate partial score in (-0.5, 1), explaining which parts match.
6) The explanation must be concise, focusing only on the rationale for the assigned score.

I will now use these guidelines to evaluate the student’s response against the teacher’s canonical solution, ensuring I generate a final answer strictly in XML format.

Question:
<question_start>
{problem}
<question_end>

Teacher solution:
<teacher_solution_start>
{canonical_solution}
<teacher_solution_end>

Student solution:
<student_solution_start>
{student_answer}
<student_solution_end>

Final Answer:"""


GRADER_XML_WITH_ICL_MM = """I am given a question about a data analysis task, along with two solutions: one from a teacher (the canonical solution) and one from a student. I need to compare these two solutions strictly in terms of content and completeness. My goal is to assign a score between -0.5 and 1.0, along with a brief explanation for that score. I must always output this as valid XML with the fields "score" and "reason".

Here is my scoring rubric:

• -0.5: This is used ONLY when the student solution explicitly says "No answer found" but the teacher’s solution provides a valid answer.
• 0: The student’s solution either fails to address the question at all or has zero content overlap with the teacher’s solution.
• 1: The student’s solution exactly matches the teacher’s solution (in terms of actual content needed to answer the question). Minor differences in formatting or variable names do not affect the content match.
• Any other score from -0.5 to 1.0 can be used to indicate partial correctness. For example, if the student solution provides some correct elements but is missing key parts, I might assign 0.5, 0.23, 0.75, etc., and briefly explain which parts match and which parts are missing.

Below are examples demonstrating how I apply the scoring:

Example 1:
Question:
<question_start>
Which organization has the highest average rate across 'Consumer Directed', 'Consumer Directed Enhanced', and 'Consumer Directed Live In' services, and what are its details?
<question_end>

Teacher solution:
<teacher_solution_start>
{'Organization': 'CONCEPTS OF INDEPENDENT CHOICES, INC.', 'Organization code': 240779859, 'Location': 'WESTCHESTER', 'Consumer Directed rate': 22.31, 'Consumer Directed Enhanced rate': 22.85, 'Consumer Directed Live In rate': 290.03, 'Average rate': 111.73}
<teacher_solution_end>

Student solution:
<student_solution_start>
{'Organization': '0240779859', 'Name': 'CONCEPTS OF INDEPENDENT CHOICES, INC.', 'Date': '2022-01-01','County': 'WESTCHESTER', 'Consumer Directed': 22.31, 'Consumer Directed Enhanced': 22.85, 'Consumer Directed Live In': 290.03, 'Average Rate': 111.73}
<student_solution_end>

Final Answer:
<reason_start>
Name, Code (ignoring the zero prefix) and location of the organization are same for both solutions. Extra 'Date' key doesn't affect correctness.
<reason_end>
<score_start>
1.0
<score_end>

Explanation:
<explanation_start>
The student’s answer is effectively the same as the teacher’s. Minor name and formatting differences do not affect correctness.
<explanation_end>

----------------------------------------------------

Example 2:
Question:
<question_start>
What is the total expenditure on pupil personnel services and business services for the school district 'McKeesport Area SD' in the years 2015-16 and 2016-17?
<question_end>

Teacher solution:
<teacher_solution_start>
[2178519, 2317047]
<teacher_solution_end>

Student solution:
<student_solution_start>
(3560800.0, 4634094.0)
<student_solution_end>

Final Answer:
<reason_start>
The values provided by the student are not in the approximate range of the teacher’s values.
<reason_end>
<score_start>
0.0
<score_end>

Explanation:
<explanation_start>
The student’s data doesn’t match or approximate the teacher’s data.
<explanation_end>

----------------------------------------------------

Example 3:
Question:
<question_start>
Identify the fiscal quarter with the highest revenue growth rate and provide the corresponding gross profit margin. Similarly, determine the fiscal quarter with the lowest revenue growth rate and provide the corresponding gross profit margin.
<question_end>

Teacher solution:
<teacher_solution_start>
{'highest': {'quarter': '2Q12', 'GPM': 24.11}, 'lowest': {'quarter': '1Q19', 'GPM': 18.25}}
<teacher_solution_end>

Student solution:
<student_solution_start>
("2Q12", 0.241133, "1Q19", 0.182494)
<student_solution_end>

Final Answer:
<reason_start>
The quarters and their corresponding gross profit margins match perfectly (24.11% vs. 0.241133, etc.).
<reason_end>
<score_start>
1.0
<score_end>

Explanation:
<explanation_start>
Despite the student’s numeric format, the content is the same.
<explanation_end>

----------------------------------------------------

Example 4 (partial match with extra data):
Question:
<question_start>
Which modules have 'Please select' status, and what appropriate modules from the 'Lists' sheet can replace these placeholders?
<question_end>

Teacher solution:
<teacher_solution_start>
{'Fall': ['CH-100', 'CH-120', 'CH-140'], 'Spring': ['CH-101', 'CH-121', 'CH-141']}
<teacher_solution_end>

Student solution:
<student_solution_start>
(["CH-XXX", "CTLA-GER-XX/CTHU-HUM-XXX"], ["CH-100 General Biochemistry", "CH-120 General & Inorganic Chemistry", "CH-140 Classical Physics", "CH-221 Mathematical & Physical Foundations of Robotics I", "CH-230 Programming in C/C++", "CH-233 Mathematical Foundations of CS", "CH-241 General Logistics", "CH-300 Introduction to International Business", "CH-320 Introduction to the Social Sciences I", "CH-330 Introduction to International Relations Theory", "CH-700 Introduction to Data Science", "SDT-104 Scientific Programming in Python", "CH-101 General Cell Biology", "CH-121 Introduction to Biotechnology", "CH-141 Modern Physics", "CH-152 Mathematical Modeling", "CH-212 Foundations of Communications and Electronics", "CH-222 Mathematical & Physical Foundations of Robotics II", "CH-231 Algorithms & Data Structures", "CH-234 Digital Systems & Computer Architecture", "CH-240 General Industrial Engineering", "CH-301 Introduction to Finance & Accounting", "CH-321 Introduction to the Social Sciences II", "CH-331 Introduction to Modern European History", "CH-701 Data Structures & Processing", "SDT-103 Development in JVM Languages"])
<student_solution_end>

Final Answer:
<reason_start>
Student solution references many extra modules. Some correct matches exist (CH-100, CH-120, CH-140, CH-101, CH-121, CH-141). Score reflects partial correctness.
<reason_end>
<score_start>
0.23
<score_end>

Explanation:
<explanation_start>
The student’s list is a superset with extraneous items.
<explanation_end>

----------------------------------------------------

Example 5 (missing certain correlations but partial match):
Question:
<question_start>
What are the Pearson correlation coefficients between S, TS, B, and TB gene numbers for each GO term?
<question_end>

Teacher solution:
<teacher_solution_start>
{'S vs. B': 0.985185, 'TS and TB': 'NaN'}
<teacher_solution_end>

Student solution:
<student_solution_start>
{('S gene number', 'TS gene number'): None, ('S gene number', 'B gene number'): 0.985185, ('S gene number', 'TB gene number'): None, ('TS gene number', 'B gene number'): None, ('TS gene number', 'TB gene number'): None, ('B gene number', 'TB gene number'): None}
<student_solution_end>

Final Answer:
<reason_start>
Both solutions show a correlation only between S and B with the same coefficient (0.985185); everything else is NaN or None.
<reason_end>
<score_start>
1.0
<score_end>

Explanation:
<explanation_start>
The student’s numeric correlation data aligns with the teacher’s.
<explanation_end>

----------------------------------------------------

Example 6 (special case of -0.5):
Question:
<question_start>
Could you determine the sensor's operating temperature range during the data recording and compare it with changes in gy and gyf to assess temperature-induced drift?
<question_end>

Teacher solution:
<teacher_solution_start>
(100K, 125K, "Comparison not possible")
<teacher_solution_end>

Student solution:
<student_solution_start>
[NO_ANSWER_FOUND]
<student_solution_end>

Final Answer:
<reason_start>
Student explicitly states no answer, yet teacher provides a valid one.
<reason_end>
<score_start>
-0.5
<score_end>

Explanation:
<explanation_start>
This is the special case. The student’s solution is "No Answer found" while the teacher has an actual solution.
<explanation_end>

----------------------------------------------------

Example 7 (demonstrating a partial match of 0.5):
Question:
<question_start>
Calculate the total counts of A, B, and C among the entire population, and identify which category is the highest.
<question_end>

Teacher solution:
<teacher_solution_start>
{'A_count': 150, 'B_count': 120, 'C_count': 30, 'Highest': 'A'}
<teacher_solution_end>

Student solution:
<student_solution_start>
{'A_count': 150, 'B_count': 120, 'C_count': 30}
<student_solution_end>

Final Answer:
<reason_start>
The student correctly provides the counts for A, B, and C but does not identify the highest category.
<reason_end>
<score_start>
0.5
<score_end>

Explanation:
<explanation_start>
The student’s solution is correct for the raw counts but omits which category is highest.
<explanation_end>

----------------------------------------------------

Example 8 (special case when teacher solution is No answer):
Question:
<question_start>
What is the total revenue reported by Visionary Holdings Inc. in their most recent annual filing?
<question_end>

Teacher solution:
<teacher_solution_start>
[NO_ANSWER_FOUND]
<teacher_solution_end>

Student solution:
<student_solution_start>
The JSON payload does not provide a clear figure for total revenue, so the document must be checked separately for that information.  
<student_solution_end>

Final Answer:
<reason_start>
Student has the correctly identified no answer is possible for the question, same as teacher.
<reason_end>
<score_start>
1.0
<score_end>

Explanation:
<explanation_start>
Student has correctly identified answering for the question is not possible.
<explanation_end>

----------------------------------------------------

Example 9 (another partial example with a 0.75 score):
Question:
<question_start>
Please compute the mean and median for the dataset [5, 8, 8, 10, 12, 12, 12] and identify if the distribution is skewed.
<question_end>

Teacher solution:
<teacher_solution_start>
{'mean': 9.0, 'median': 10, 'skew': 'Slightly left-skewed'}
<teacher_solution_end>

Student solution:
<student_solution_start>
{'mean': 9.0, 'median': 10, 'skew': 'Symmetrical'}
<student_solution_end>

Final Answer:
<reason_start>
Student has the correct mean and median but reports a different skew than the teacher.
<reason_end>
<score_start>
0.75
<score_end>

Explanation:
<explanation_start>
The student is mostly correct (mean and median match) but misses on the skew characterization.
<explanation_end>

----------------------------------------------------

When I produce my final output, I must do the following:
1) Read the question.
2) Look at the teacher’s canonical solution.
3) Compare it with the student’s solution.
4) Output a XML with two fields: "score" and "reason". The "score" must be in the range -0.5 to 1.0 and the "reason" must be a short statement.
5) If the student explicitly writes "No answer found" while the teacher solution exists, I must give a score of -0.5. If the student solution is an exact content match, I assign 1.0. If there is no meaningful overlap, I assign 0. If the student solution is partially correct, I give an appropriate partial score in (-0.5, 1), explaining which parts match.
6) The explanation must be concise, focusing only on the rationale for the assigned score.

I will now use these guidelines to evaluate the student’s response against the teacher’s canonical solution, ensuring I generate a final answer strictly in XML format.

Question:
<question_start>
{problem}
<question_end>

"""
