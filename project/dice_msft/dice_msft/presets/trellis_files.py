from typing import Literal, cast

import berry.preset_utils
from chz_presets.core import CompositePreset, compose_presets
from dice_msft.presets.utils import (
    get_analyst_qv2_jsonl_args,
    get_datagen_jsonl_args,
    get_datagen_jsonl_train_args,
    get_default_grader_args,
    get_maybe_max_n_datapoints_per_dataset,
    get_target_spi,
)

noans_batch4_sandbox_eval = berry.preset_utils.eval_dataset_preset(
    [
        "dataset.dataset_id=data.shtri.dice.051525.raw.noans_batch4.train",
        "dataset.datapoint_converters.0.kwargs.base_name=noans_batch4",
        "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
    ]
    + get_datagen_jsonl_args()
    + get_target_spi(5)
    + get_default_grader_args()
)

mf_batch8_sandbox_eval = berry.preset_utils.eval_dataset_preset(
    [
        "dataset.dataset_id=data.shtri.dice.051525.raw.mf_batch8.train",
        "dataset.datapoint_converters.0.kwargs.base_name=mf_batch8",
        "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
    ]
    + get_datagen_jsonl_args()
    + get_target_spi(5)
    + get_default_grader_args()
)

pdf_batch2_sandbox_eval = berry.preset_utils.eval_dataset_preset(
    [
        "dataset.dataset_id=data.shtri.dice.051525.raw.pdf_batch2.train",
        "dataset.datapoint_converters.0.kwargs.base_name=pdf_batch2",
        "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
    ]
    + get_datagen_jsonl_args()
    + get_target_spi(5)
    + get_default_grader_args()
)


test_viz_pr = berry.preset_utils.eval_dataset_preset(
    [
        "dataset=dice_msft.datasets.configs:DataGenJSONLDatasetConfig",
        "dataset.is_multimodal=True",
        "dataset.datapoint_converters.0.name=dice_msft.common.datapoint_converters:datagen_jsonl_to_sample",
        "dataset.datapoint_converters.0.kwargs.base_name=test_viz",
        "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
        "dataset.dataset_id=data.shtri.dice.viz.jsonl_test",
        "dataset.grader=dice_msft.graders.icl_grader:ICL4oGrader",
        "dataset.override_target_samples_per_instance=100",
    ]
)


def analyst_qv2_seval_preset(
    max_n_datapoints_per_dataset: int | None = None,
) -> CompositePreset:
    prs = [
        berry.preset_utils.eval_dataset_preset(
            get_analyst_qv2_jsonl_args(
                d_id=dataset_id,
                workspace="sandbox",
                is_train=False,
            )
            + get_target_spi(1)
            + get_default_grader_args()
            # + get_maybe_max_n_datapoints_per_dataset(
            #     max_n_datapoints_per_dataset
            # )
        )
        for dataset_id in [
            "cwc_ci_v2",
            "cwc_pptpdf",
            "cwc_mf",
            "cwc_othertypes",
        ]
    ]
    return compose_presets(*prs)


def cwc_seval_v2(
    max_n_datapoints_per_dataset: int | None = None,
) -> CompositePreset:
    prs = [
        berry.preset_utils.eval_dataset_preset(
            [
                "dataset=dice_msft.datasets.configs:DataGenJSONLDatasetConfig",
                "dataset.is_multimodal=True",
                "dataset.datapoint_converters.0.name=dice_msft.common.datapoint_converters:datagen_jsonl_to_sample",
                "dataset.datapoint_converters.0.kwargs.base_name=cwc_xlsx",
                "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
                "dataset.dataset_id=data.shtri.dice.uploads.raw.cwc_ci_v2.test",
                "dataset.grader=dice_msft.graders.icl_grader:ICL4oGrader",
                "dataset.override_target_samples_per_instance=1",
            ]
        ),
        berry.preset_utils.eval_dataset_preset(
            [
                "dataset=dice_msft.datasets.configs:DataGenJSONLDatasetConfig",
                "dataset.is_multimodal=True",
                "dataset.datapoint_converters.0.name=dice_msft.common.datapoint_converters:datagen_jsonl_to_sample",
                "dataset.datapoint_converters.0.kwargs.base_name=cwc_pptpdf",
                "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
                "dataset.dataset_id=data.shtri.dice.uploads.raw.cwc_pptpdf.test",
                "dataset.grader=dice_msft.graders.icl_grader:ICL4oGrader",
                "dataset.override_target_samples_per_instance=1",
            ]
        ),
        berry.preset_utils.eval_dataset_preset(
            [
                "dataset=dice_msft.datasets.configs:DataGenJSONLDatasetConfig",
                "dataset.is_multimodal=True",
                "dataset.datapoint_converters.0.name=dice_msft.common.datapoint_converters:datagen_jsonl_to_sample",
                "dataset.datapoint_converters.0.kwargs.base_name=cwc_mf",
                "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
                "dataset.dataset_id=data.shtri.dice.uploads.raw.cwc_mf.test",
                "dataset.grader=dice_msft.graders.icl_grader:ICL4oGrader",
                "dataset.override_target_samples_per_instance=1",
            ]
        ),
        berry.preset_utils.eval_dataset_preset(
            [
                "dataset=dice_msft.datasets.configs:DataGenJSONLDatasetConfig",
                "dataset.is_multimodal=True",
                "dataset.datapoint_converters.0.name=dice_msft.common.datapoint_converters:datagen_jsonl_to_sample",
                "dataset.datapoint_converters.0.kwargs.base_name=cwc_othertypes",
                "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
                "dataset.dataset_id=data.shtri.dice.uploads.raw.cwc_othertypes.test",
                "dataset.grader=dice_msft.graders.icl_grader:ICL4oGrader",
                "dataset.override_target_samples_per_instance=1",
            ]
        ),
    ]
    return compose_presets(*prs)


def hard_dablike_preset(
    max_n_datapoints_per_dataset: int | None = None,
) -> CompositePreset:
    prs = [
        berry.preset_utils.eval_dataset_preset(
            [
                "dataset=dice_msft.datasets.configs:DataGenJSONLDatasetConfig",
                "dataset.is_multimodal=True",
                "dataset.datapoint_converters.0.name=dice_msft.common.datapoint_converters:datagen_jsonl_to_sample",
                "dataset.datapoint_converters.0.kwargs.base_name=hard_dablike",
                "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
                "dataset.dataset_id=data.shtri.dice.uploads.raw.test_dabstep.test",
                "dataset.grader=dice_msft.graders.icl_grader:ICL4oGrader",
                "dataset.override_target_samples_per_instance=4",
            ]
        )
    ]
    return compose_presets(*prs)


def noans_eval_preset(
    max_n_datapoints_per_dataset: int | None = None,
) -> CompositePreset:
    prs = [
        berry.preset_utils.eval_dataset_preset(
            get_datagen_jsonl_args(
                d_id=dataset_id,
                workspace="sandbox",
                is_train=False,
            )
            + get_target_spi(1)
            + get_default_grader_args()
            # + get_maybe_max_n_datapoints_per_dataset(
            #     max_n_datapoints_per_dataset
            # )
        )
        for dataset_id in [
            "noans_batch1",
            "noans_batch2",
            "noans_batch3",
            "noans_batch4",
        ]
    ]
    return compose_presets(*prs)


def complete_eval_preset(
    max_n_datapoints_per_dataset: int | None = None,
) -> CompositePreset:
    prs = [
        berry.preset_utils.eval_dataset_preset(
            get_datagen_jsonl_args(
                d_id=dataset_id,
                workspace="sandbox",
            )
            + get_target_spi(1)
            + get_default_grader_args()
            # + get_maybe_max_n_datapoints_per_dataset(
            #     max_n_datapoints_per_dataset
            # )
        )
        for dataset_id in [
            "csv_batch1",
            "json_batch1",
            "mf_batch1",
            "mf_batch2",
            "mf_batch3",
            "mf_batch4",
            "noans_batch1",
            "ppt_pdf_batch1",
            "sqlite_batch1",
            "tsv_batch1",
            "xlsx_batch1",
            "xml_batch1",
            "access_batch1",
            "ambiguous_batch1",
            "ci_coding",
            "mf_batch5",
            "mf_batch6",
            "mf_batch7",
            "mf_batch8",
            # "mf_batch9",
            "noans_batch2",
            "noans_batch3",
            "noans_batch4",
            "pbi_batch1",
            "pdf_batch2",
            "ppt_batch2",
            "viz_batch1",
            "viz_batch2",
            "xml_batch2",
            "yaml_batch1",
        ]
    ]
    return compose_presets(*prs)


def train_v2_preset(
    max_n_datapoints_per_dataset: int | None = None,
) -> CompositePreset:
    prs = [
        berry.preset_utils.training_dataset_preset(
            get_datagen_jsonl_train_args(
                d_id=dataset_id,
                workspace="sandbox",
                is_train=True,
            )
            # + get_target_spi(1)
            + get_default_grader_args(dataset_arg=True)
            # + get_maybe_max_n_datapoints_per_dataset(
            #     max_n_datapoints_per_dataset
            # )
        )
        for dataset_id in [
            "easy",
        ]
    ]
    return compose_presets(*prs)


# trellis
# Run this command to submit the trellis dataset: trellis dataset create --name "diceberry_train_dataset" --description "This is diceberry dataset for MAI/M365 team" --role train --owners "kamo,erictang" :dice_msft.presets.trellis_files:dice_train_v2_easy_trellis_dataset
dice_train_v2_easy_trellis_dataset = train_v2_preset(max_n_datapoints_per_dataset=1000)
