import os
from typing import List

import berry.preset_utils
from chz_presets.core import CompositePreset, compose_presets


def get_default_grader_args(dataset_arg: bool = False) -> List[str]:
    return [
        f"{'dataset.' if not dataset_arg else ''}grader=dice_msft.graders.icl_grader:ICL4oGrader",
    ]


def get_target_spi(spi: int = 1) -> List[str]:
    return [f"dataset.override_target_samples_per_instance={spi}"]


def get_datagen_jsonl_train_args(
    d_id: str | None = None,
    workspace: str | None = None,
    is_train: bool = False,
) -> List[str]:
    dataset_args = [
        "=dice_msft.datasets.configs:DataGenJSONLDatasetConfig",
        "is_multimodal=True",
        "datapoint_converters.0.name=dice_msft.common.datapoint_converters:datagen_jsonl_to_sample",
    ]
    if d_id:
        return [
            f"dataset_id=data.shtri.dice.051525.combined.{d_id}.{'train' if is_train else 'test'}",
            f"datapoint_converters.0.kwargs.base_name={d_id}",
            f"datapoint_converters.0.kwargs.workspace={workspace}",
        ] + dataset_args
    return dataset_args


def get_datagen_jsonl_args(
    d_id: str | None = None,
    workspace: str | None = None,
    is_train: bool = False,
) -> List[str]:
    dataset_args = [
        "dataset=dice_msft.datasets.configs:DataGenJSONLDatasetConfig",
        "dataset.is_multimodal=True",
        "dataset.datapoint_converters.0.name=dice_msft.common.datapoint_converters:datagen_jsonl_to_sample",
    ]
    if d_id:
        return [
            f"dataset.dataset_id=data.shtri.dice.051525.raw.{d_id}.{'train' if is_train else 'test'}",
            f"dataset.datapoint_converters.0.kwargs.base_name={d_id}",
            f"dataset.datapoint_converters.0.kwargs.workspace={workspace}",
        ] + dataset_args
    return dataset_args


def get_analyst_qv2_jsonl_args(
    d_id: str | None = None,
    workspace: str | None = None,
    is_train: bool = False,
) -> List[str]:
    dataset_args = [
        "dataset=dice_msft.datasets.configs:CAPIJSONLDatasetConfig",
        "dataset.is_multimodal=True",
        "dataset.datapoint_converters.0.name=dice_msft.common.datapoint_converters:analyst_qv2_datagen_jsonl_to_sample",
    ]
    if d_id:
        return [
            f"dataset.dataset_id=data.shtri.dice.uploads.raw.{d_id}.{'train' if is_train else 'test'}",
            f"dataset.datapoint_converters.0.kwargs.base_name={d_id}",
            f"dataset.datapoint_converters.0.kwargs.workspace={workspace}",
        ] + dataset_args
    return dataset_args


def get_maybe_max_n_datapoints_per_dataset(
    max_n_datapoints_per_dataset: int | None,
) -> list[str]:
    if max_n_datapoints_per_dataset is None:
        return []
    return [
        f"max_n_datapoints={max_n_datapoints_per_dataset}",
        # "safely_set_max_datapoints=False",
    ]
