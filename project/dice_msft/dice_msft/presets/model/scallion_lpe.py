from berry_models.scallion_lpe import (
    d24_80g,
    d36_40g_bf16_chicken,
    d36_80g_256k_bf16_chicken,
    d36_80g_bf16_chicken,
    d36_80g_giant_embeddings_bf16_chicken,
    d36_80g_giant_embeddings_bf16_chicken_oom_debug,
    d36_80g_mbg16_bf16,
    d36_80g_mbg16_bf16_chicken,
    d36_80g_mbg16_bf16_chicken_shoptim,
    d36_141g,
    d36_141g_mbg16,
    d36_141g_mbg16_flexpoint_bwd_attention_bf16_logits,
)

d36_80g_mbg16_bf16 = d36_80g_mbg16_bf16
d36_80g_bf16_chicken = d36_40g_bf16_chicken
d36_40g_bf16_chicken = d36_40g_bf16_chicken
d36_80g_mbg16_bf16_chicken = d36_80g_mbg16_bf16_chicken
d36_80g_mbg16_bf16_chicken_shoptim = d36_80g_mbg16_bf16_chicken_shoptim
d36_80g_bf16_chicken = d36_80g_bf16_chicken
d36_80g_256k_bf16_chicken = d36_80g_256k_bf16_chicken
d36_80g_giant_embeddings_bf16_chicken = d36_80g_giant_embeddings_bf16_chicken
d36_80g_giant_embeddings_bf16_chicken_oom_debug = d36_80g_giant_embeddings_bf16_chicken_oom_debug
d36_141g = d36_141g
d36_141g_mbg16 = d36_141g_mbg16
d36_141g_mbg16_flexpoint_bwd_attention_bf16_logits = (
    d36_141g_mbg16_flexpoint_bwd_attention_bf16_logits
)
d24_80g = d24_80g
