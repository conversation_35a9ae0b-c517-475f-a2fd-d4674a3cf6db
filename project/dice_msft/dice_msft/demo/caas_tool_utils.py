import base64
import os
import time

import structlog
from caas.api import <PERSON><PERSON><PERSON>, CaasSession
from caas.commands import Exec, UploadFile
from caas.memory import CaasMemory
from chat.tools import DefaultMultiToolKit
from dice_msft.common.datapoint_converters import WORKSPACES
from dice_msft.demo.logs import initialize_logging
from qstar.common.tools.python.python_tool import PythonTool
from research_ace.v2.client.caas_impl import JupyterKernelRuntimeCaasImpl

logger = structlog.get_logger(__name__)

CAAS_CONTAINER_RETRY_LIMIT = 10
GZIP_FILE_NAME = "app.tar.gz"


async def get_caas_sess(caas: Caas, caas_image: str, idle_ttl: int) -> CaasSession:
    # CAAS_ENDPOINT='http://westus2-01.caas.net'
    return await caas.new_session(
        image=caas_image,
        cmd="/home/<USER>/.openai_internal/jupyter_server/run-server.sh",
        env={
            "API_PORT": "8080",
            "PROCESS_MEMORY_LIMIT": CaasMemory.resolve(CaasMemory.from_gb(4)),
        },
        cpu_limit="2.0",
        memory_limit=CaasMemory.from_gb(4),
        # memory_reservation=CaasMemory.from_gb(4),
        enable_network=True,
        idle_ttl=idle_ttl,
        # image='python',
        # image='jupyter',
    )


def get_container_id(sess: CaasSession) -> str:
    return sess.container.container_id


async def start_caas_session(caas: Caas, caas_image: str, idle_ttl: int) -> CaasSession:
    for attempt in range(CAAS_CONTAINER_RETRY_LIMIT):
        try:
            sess = await get_caas_sess(
                caas=caas,
                caas_image=caas_image,
                idle_ttl=idle_ttl,
            )
            break
        except Exception as e:
            logger.warning(
                f"Attempt {attempt + 1}: CAASSession failed to start with error {e}, retrying..."
            )
            if attempt < CAAS_CONTAINER_RETRY_LIMIT - 1:
                time.sleep(min(1 * (2**attempt), 16))
            else:
                raise Exception(f"Failed to start a caas container after {attempt} tries.")
    return sess


async def run_remote_command(sess: CaasSession, cmd: Exec) -> str:
    logger.info(f"Running remote command on container: {cmd.cmd} workdir: {cmd.workdir}")
    output = await sess.run(cmd)
    output = output.decode()
    logger.info(f"Remote command output: {output}")
    return output


async def upload_file_to_container(
    sess: CaasSession,
    uploaded_folder_zip_base64: str,
    uploaded_folder_name: str,
):
    gzip = base64.b64decode(uploaded_folder_zip_base64)
    # unzip_target_folder = os.path.join(CAAS_WORKSPACE_DIR, uploaded_folder_name)
    unzip_target_folder = WORKSPACES["sandbox"]
    gzip_path = os.path.join(unzip_target_folder, GZIP_FILE_NAME)
    logger.info(f"Uploading {len(gzip)} bytes to caas. Remote path: {unzip_target_folder}")
    await sess.run(UploadFile(gzip_path, gzip))  # override silently
    await sess.run(
        Exec(
            [
                "bash",
                "-c",
                f"tar -xf {gzip_path} --strip-components=2 -C {unzip_target_folder}",
            ]
        )
    )
    # list_of_files = []
    list_of_files = await sess.run(Exec(["ls", "-la", unzip_target_folder]))
    logger.info(await sess.run(Exec(["pwd"])))
    # await run_remote_command(container, Exec(["bash", "-c", f"""
    #     git config --global --add safe.directory {unzip_target_folder}
    #     git config --global user.email "<EMAIL>"
    #     git config --global user.name "AI"
    #     git init
    #     git add .
    #     git commit -m "init"
    # """], workdir=unzip_target_folder))
    logger.info(
        f"Zip uploaded and unzipped at {unzip_target_folder}. Root level content:\n{list_of_files}."
    )
    return unzip_target_folder, list_of_files


# async def get_head_commit_id(container: CaasContainer, workdir: str) -> str:
#     logger.info(f"get_head_commit_id: workdir={workdir}")
#     current_commit = await run_remote_command(container, Exec(["git", "rev-parse", "HEAD"], workdir=workdir))
#     current_commit = current_commit.strip()
#     logger.info(f"get_head_commit_id={current_commit}")
#     return current_commit


def get_python_tool(sess: CaasSession) -> PythonTool:
    return PythonTool(
        runtime=JupyterKernelRuntimeCaasImpl(
            session=sess,
            # user="dice_msft_agent",
        )
    )
