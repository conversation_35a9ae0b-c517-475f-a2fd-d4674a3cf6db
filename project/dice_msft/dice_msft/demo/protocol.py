from enum import StrE<PERSON>
from typing import List, Optional

import chat
from dice_msft.datasets.configs import DICE_IMAGE_NAME
from pydantic import BaseModel, validator


class ModelMeta(BaseModel):
    renderer_name: str = "harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget"
    inference_ip: str = ""
    seed: int = 1400
    temperature: float = 1

    @validator("temperature")
    def check_temperature(cls, v):
        if not (0 <= v <= 1):
            raise ValueError("temperature must be between 0 and 1")
        return v

    @property
    def name(self):
        return self.__class__.__qualname__


class DiceV4(ModelMeta):
    # renderer_name: str = "harmony_v4.0.16_berry_v3_1mil_orion_no_budget"
    renderer_name: str = "harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget"
    # renderer_name: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"
    inference_ip: str = "localhost"


class DiceV5(ModelMeta):
    renderer_name: str = "harmony_v4.0.16_berry_v3_1mil_orion_no_budget"
    # renderer_name: str = "harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget"
    # renderer_name: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"
    inference_ip: str = "localhost"


MODEL_MAPPING = {
    "DiceV4": DiceV4(),
    "DiceV5": DiceV5(),
}
DEFAULT_MODEL = "DiceV5"


class SharedBaseModel(BaseModel):
    session_id: str
    container_id: str | None = None  # not required for requests


class SimpliedChatMessage(BaseModel):
    role: str
    content: str
    audience: str | None = None
    end_turn: bool | None = None
    id: str | None = None
    channel: str | None = None
    asset_pointer_content: List[str] | None = []


class RollerStepResult(BaseModel):
    convo: chat.Conversation
    new_messages: list[chat.Message]


class NewSessionRequest(BaseModel):
    caas_image_name: str = DICE_IMAGE_NAME.get("0115", "")
    idle_ttl: int = 15 * 60


class NewSessionResponse(SharedBaseModel):
    pass


class UploadRequest(SharedBaseModel):
    zip_base64: str
    folder_name: str


class UploadResponse(SharedBaseModel):
    unzipped_folder_path: str


class KeepAliveRequest(SharedBaseModel):
    pass


class KeepAliveResponse(SharedBaseModel):
    success: bool


class RollRequest(SharedBaseModel):
    model: str | None = None
    messages: list[SimpliedChatMessage]
    system_instruction: str = ""
    developer_instruction: str = ""
    search_folder_path: str
    max_episode_steps: int = 50
    roll_id: str | None = None  # distinguish between different roll requests in the same session
    enable_diagnosis: bool = (
        True  # TODO change default to False when we have user consent flow from UX
    )


class RollForEvalRequest(BaseModel):
    model: str | None = None
    messages: list[SimpliedChatMessage]
    system_instruction: str = ""
    developer_instruction: str = ""
    search_folder_path: str
    max_episode_steps: int = 50
    roll_id: str | None = None  # distinguish between different roll requests in the same session
    enable_diagnosis: bool = (
        True  # TODO change default to False when we have user consent flow from UX
    )
    file_data: Optional[str] = None  # Base64-encoded file data
    file_name: Optional[str] = None  # Name of the file
