#!/bin/bash
# filepath: /home/<USER>/code/glass/project/dice_msft/dice_msft/demo/bus/launch_bus_dbv4.sh

# Configuration variables
USER_NAME="diceberry"
SNAPSHOT_PATH="az://orngcresco/twapi/mini/e/shtri-032825-dtr3-4ott_gr-05062025-222524/policy/step_000180/"

# Set environment variables
export OPENAI_API_KEY="dummy_key"
export TWDEV_LAUNCH_CACHE_SVC_ENGINE=1
export RUST_BACKTRACE=1

# Install required packageb sync
oaipkg install bus > ~/bus_install.log 2>&1

# Run setup script
brix run --env TWDEV_LAUNCH_CACHE_SVC_ENGINE=1 -- python /root/code/openai/torchflow/run-torchflow-setup.py > cache_svc.log 2>&1

# Start the engine
python -m harmony_scripts.engines.start_engine \
--name=dbv4-engine \
--mode=optimal \
--snapshot_path="$SNAPSHOT_PATH" \
--gpu_kind=A100_80G \
--cluster=local \
--renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget \
--extra_config_string="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=2 pipe_depth=4 n_replicas=1" \
--restartable \
--is_multimodal=True \
--encoder_decoder_snapshots='{"clip":"az://orngcresco/models/snapshots/models.tc/nv4-lpe-encoder-transferred-20250122-decrypted/clip_export/snapshot.9d0a4a4735167dfa1c54bcade9e6b3677acbc66507438b52313e004fc12c769c.json"}' \
--bus_enable_qos=True \
--bus_topic_mode_or_user="$USER_NAME" \
--enable_healthcheck=True \
--n_replicas=1 \
--bus_rate_limiter=KV_UTIL \
--use_bus_v2=True

# Run bup hello in a loop
while true; do
    echo "Running bup hello at $(date)"
    bup hello \
    --user="$USER_NAME" \
    --snapshot="$SNAPSHOT_PATH"
    
    sleep 60  # Wait 60 seconds (1 minute)
done
