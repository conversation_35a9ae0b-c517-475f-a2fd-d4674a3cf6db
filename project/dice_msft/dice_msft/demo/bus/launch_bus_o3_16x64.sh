#!/bin/bash
# filepath: /home/<USER>/code/glass/project/dice_msft/dice_msft/demo/bus/launch_bus_o3_16x64.sh

# Configuration variables
USER_NAME="diceberry"
SNAPSHOT_PATH="az://orngscuscresco/twapi/mini/e/kamo-dice-o3-16x64-gpu152-08222025-180137/policy/step_000250/"

RUST_BACKTRACE=1 nohup python -m harmony_scripts.engines.start_engine \
--name o3-engine \
--model_config=falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal \
--mode=optimal \
--snapshot_path="$SNAPSHOT_PATH" \
--is_multimodal=true \
--gpu_kind=A100_80G \
--renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget" \
--extra_config_string="load_inference_snapshots_with_tcv2=False raise_on_load_for_missing_tensor=False ixf_sampling_extension_gpu_to_cpu_async=False twapi_cpu_comm_backend=gloo ixf_batcher_response_loop_timeout=600 allow_embedding_prefix_loading=True tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0} n_op_shards=8" \
--encoder_decoder_snapshots '{"clip":"az://orngscuscresco/models/snapshots/mm/oaiomni/rcall/crow-export/scallion2b/omni/model_final_ts.pt"}' \
--bus_enable_qos=True \
--bus_rate_limiter=KV_UTIL \
--bus_topic_mode_or_user="$USER_NAME" \
--cluster=local \
--n_replicas=1

while true; do
    echo "Running bup hello at $(date)"
    bup hello \
    --user="$USER_NAME" \
    --snapshot="$SNAPSHOT_PATH"
    
    sleep 60  # Wait 60 seconds (1 minute)
done