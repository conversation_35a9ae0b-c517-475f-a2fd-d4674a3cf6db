#!/usr/bin/env -S streamlit run

import base64
import datetime
import json
import logging
import os
import sys
import tempfile
from dataclasses import dataclass
from enum import Enum

import requests
import streamlit as st
from azure.identity import DefaultAzureCredential
from pylatexenc import latex2text
from pylatexenc.latex2text import LatexNodes2Text


class ReverseProxyEnv(Enum):
    PROD_UKSOUTH_7 = "https://*************"
    PROD_UKSOUTH_8 = "https://***********"
    PROD_UKSOUTH_15 = "https://*************"
    STAGE_SOUTHCENTRALUS_HPE_1 = "https://*************"
    PROD_SOUTHCENTRALUS_HPE_2 = "https://************"
    PROD_SOUTHCENTRALUS_HPE_3 = "https://***************"
    PROD_SOUTHCENTRALUS_HPE_4 = "https://*************"
    PROD_SOUTHCENTRALUS_HPE_5 = "https://**********"
    PROD_WESTUS2_19 = "https://***************"
    localhost = "http://localhost:1999"


@dataclass
class DiceBerryModel:
    name: str
    service_url: str
    reverse_proxy_url: ReverseProxyEnv


################################## config #####################################
ENDPOINTS = (
    DiceBerryModel(
        name="DiceV5",
        service_url="http://o3-demo-0-svc-1999.kamo.svc.cluster.local:1999",
        reverse_proxy_url=ReverseProxyEnv.localhost,
    ),
    DiceBerryModel(
        name="DiceV4",
        service_url="http://dice-demo-0-svc-1999.shtri.svc.cluster.local:1999",
        reverse_proxy_url=ReverseProxyEnv.localhost,
    ),
)

# Orange Endpoints
SCOPE = "2a750fd4-529b-4678-b332-6331e201131c/.default"
# TODO add ameroot
VERIFY_SSL = False
# General
IDLE_TTL_S = 15 * 60
REQUEST_TTL_S = 30 * 60
MAX_EPISODE_STEPS = 1000
################################## code #######################################


@st.cache_resource
def get_l2t() -> LatexNodes2Text:
    """Lazily create and cache the LatexNodes2Text converter as a module singleton."""
    latex_context = latex2text.get_default_latex_context_db().filter_context(
        exclude_categories=["nonascii-specials"]
    )
    l2t_obj = LatexNodes2Text(latex_context=latex_context, keep_comments=True)
    return l2t_obj


def clean_markdown(content):
    content = get_l2t().latex_to_text(content)
    content = content.replace("\\n", "\n")
    return content


def run(cmd):
    logging.info(f"Running command: {cmd}")
    import subprocess

    p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    out, err = p.communicate()
    logging.info(f"exit code: {p.returncode}")
    logging.info(f"Output: {out}")
    if err:
        logging.error(f"Error: {err}")
    if p.returncode != 0:
        exit(p.returncode)


def gzip_folder_and_b64encode(folder_path) -> str:
    tmp = tempfile.mkdtemp(prefix="uploads_")
    if not os.path.exists(tmp):
        os.makedirs(tmp)
    for file in folder_path:
        file_path = os.path.join(tmp, file.name)
        logging.info(f"adding file {file.name} to {tmp=} folder")
        with open(file_path, "wb") as tp:
            tp.write(file.getbuffer())
    tf = tempfile.NamedTemporaryFile(delete=False, suffix=".tar.gz").name

    run(f"tar --exclude-vcs -czf {tf} {tmp}")
    with open(f"{tf}", "rb") as f:
        tar_b64encoded = base64.b64encode(f.read()).decode("utf-8")
    logging.info(
        f"gzip_folder_and_b64encode folder: {tf} tar_b64encoded len: {len(tar_b64encoded)}"
    )
    # logging.info(f"gzip_folder_and_b64encode tmp: {tmp}")
    run(f"rm -rf {tmp}")
    run(f"rm -f {tf}")
    # run(f"cd {parent_folder} && tar --exclude-vcs -czf {tmp} {folder_name}")
    # with open(, "rb") as f:
    # tar_b64encoded = base64.b64encode(folder_path.read()).decode("utf-8")
    # logging.info(f"zip_folder_and_b64encode read tar_b64encoded len: {len(tar_b64encoded)}")
    return tar_b64encoded, None


def setup_logging():
    run_id = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    tmpdir = "/tmp/dice-agent-client"
    os.makedirs(tmpdir, exist_ok=True)
    logging.basicConfig(
        force=True,
        level=logging.INFO,
        format="[%(asctime)s][%(levelname)s][%(name)s][Ln %(lineno)d] %(message)s",
        handlers=[logging.FileHandler(f"{tmpdir}/{run_id}.log"), logging.StreamHandler(sys.stdout)],
    )
    # exclude noisy loggers.
    excluded_loggers = [
        "azure.core.pipeline.policies.http_logging_policy",
    ]
    for logger_name in excluded_loggers:
        logging.getLogger(logger_name).setLevel(logging.CRITICAL)
    logging.info(f"setup logging for run {run_id}")


class ReverseProxyClient:
    def __init__(self, model: DiceBerryModel, session_id: str = None):
        self.model = model
        self.session_id = None
        self.credential = DefaultAzureCredential()

    def get_headers(self) -> dict:
        token = self.credential.get_token(SCOPE)
        token = token.token
        headers = {"Authorization": f"Bearer {token}"}
        if self.model.service_url:
            headers["SERVICE_URL"] = self.model.service_url
        return headers

    ## high level APIs
    def new_session(self):
        response = requests.post(
            f"{self.model.reverse_proxy_url.value}/new_session",
            # headers=self.get_headers(),
            json={"idle_ttl": IDLE_TTL_S},
            timeout=REQUEST_TTL_S,
            verify=VERIFY_SSL,
        )
        logging.info(f"/new_session status: {response.status_code}")
        if response.status_code != 200:
            try:
                error_content = response.json()
            except Exception:
                error_content = response.text
            raise Exception(
                f"/new_session failed with status {response.status_code}: {error_content}"
            )
        session_data = response.json()["session_id"]
        logging.info(f"{session_data=}")
        assert "endpoints" in json.loads(session_data)
        self.session_id = session_data

    def init_repo(self, gzip_b64encoded: str, filename: str) -> dict:
        response = requests.post(
            f"{self.model.reverse_proxy_url.value}/upload",
            # headers=self.get_headers(),
            json={
                "session_id": self.session_id,
                "zip_base64": gzip_b64encoded,
                "folder_name": "test-demo",
            },
            timeout=REQUEST_TTL_S,
            verify=VERIFY_SSL,
        )
        logging.info(f"/upload status: {response.status_code}")

    def roll(
        self,
        prompt: str,
        file_name: str,
        system_instruction: str,
        developer_instruction: str,
    ):
        logging.info(
            f"Prompt: {prompt}. File names: {file_name}, system_instruction : {system_instruction} , developer_instruction : {developer_instruction}"
        )
        request_body = {
            "session_id": self.session_id,
            "search_folder_path": "",
            "messages": [
                {
                    "role": "user",
                    "content": prompt,
                }
            ],
            "max_episode_steps": MAX_EPISODE_STEPS,
            "system_instruction": system_instruction,
            "developer_instruction": developer_instruction,
            "model": self.model.name,
        }
        if file_name is not None:
            request_body["search_folder_path"] = file_name
            logging.info(f"search_folder_path: {request_body['search_folder_path']}")
        with requests.post(
            f"{self.model.reverse_proxy_url.value}/roll",
            # headers=self.get_headers(),
            json=request_body,
            stream=True,  # Set stream to True to handle streaming response
            verify=VERIFY_SSL,
        ) as response:
            assert response.status_code == 200, f"roll failed with {response.json()}"
            try:
                cot_count = 1
                for line in response.iter_lines(decode_unicode=True):
                    if line:  # filter out keep-alive new lines
                        try:
                            line = json.loads(line)
                        except json.JSONDecodeError:
                            logging.warning(f"Failed to decode line: {line}")
                            continue
                        if line["channel"] == "git":
                            tmp = tempfile.NamedTemporaryFile(delete=False, suffix=".patch").name
                            logging.info(f"tmp patch filename: {tmp}")
                            with open(tmp, "w") as f:
                                f.write(line["content"])
                            run(f"cd {st.session_state.repo_path} && git apply --binary {tmp}")
                            line[
                                "content"
                            ] = f"###### Applied Git Diff Locally\n```\n{line['content']}\n```"
                            yield line
                        else:
                            line["cot_count"] = cot_count
                            yield line
                            cot_count += 1
            except KeyboardInterrupt:
                # Handle any cleanup here if needed when stopping the stream
                logging.warning("Stream was interrupted by the user.")

    def get_tool_recipients(self) -> list[str]:
        return ["tool", "container_exec", "container.exec", "container"]


def update_reverse_proxy_client():
    st.session_state.proxy = ReverseProxyClient(st.session_state.selected_model)
    st.session_state.proxy.new_session()


def render_base64_image(b64data, caption=None):
    """Render a base64-encoded image using Streamlit."""
    try:
        img_bytes = base64.b64decode(b64data)
        st.image(img_bytes, caption=caption, use_container_width=True)
    except Exception as e:
        st.warning(f"Failed to render image: {e}")


def write_cot_item(chunk):
    expander_format = f"**{chunk['cot_count']}.** 📝 **Channel:** `{chunk['channel']}`  |  🛠️ **Role:** `{chunk['role']}`  |  👥 **Audience:** `{chunk['audience']}`"

    if (
        chunk["channel"] == "analysis"
        and chunk["role"] == "assistant"
        and chunk["audience"] == "python"
    ):
        code_snippet = chunk["content"]
        language = "python"
        with st.expander(expander_format, expanded=True):
            st.code(code_snippet, language=language, line_numbers=True)
    elif chunk["channel"] == "analysis" and chunk["role"] == "tool" and chunk["audience"] == "all":
        content = chunk["content"]
        assets = chunk.get("asset_pointer_content", [])
        for asset in assets:
            render_base64_image(asset)
        st.expander(expander_format, expanded=True).code(content, language=None, wrap_lines=True)
    else:
        st.expander(expander_format, expanded=True).markdown(chunk["content"])


################################## streamlit flow #############################
st.title("DICE Agent", help=None)

if "logging_state" not in st.session_state:
    setup_logging()
    st.session_state.logging_state = "ready"

if "messages" not in st.session_state:
    st.session_state.messages = []

if not st.session_state.messages:
    selected_model = st.selectbox(
        "Select model",
        ENDPOINTS,
        format_func=lambda x: x.name,
        #   on_change=update_reverse_proxy_client,
        key="selected_model",
    )

if "proxy" not in st.session_state:
    update_reverse_proxy_client()

# Then handle file upload or new prompts
if "repo_path" not in st.session_state and not st.session_state.messages:
    repo_path = st.file_uploader(
        "Choose a file to upload",
        type=["xlsx", "csv", "txt", "json", "jsonl", "py", "pptx", "pdf", "tsv", "xml", "yaml"],
        accept_multiple_files=True,
    )
    if repo_path and len(repo_path) > 0:
        st.session_state.repo_path = repo_path
        if len(repo_path) == 1 and repo_path[0].name.startswith("chat_history"):
            st.session_state.messages = json.load(repo_path)
        else:
            st.markdown("Uploading your files to the Dice Agent remote session...")
            st.session_state.proxy.init_repo(*gzip_folder_and_b64encode(repo_path))
            st.markdown("Upload completed. Starting chat!")
        st.rerun()

# system_instruction
system_instruction = st.text_area(
    "Enter custom system instruction (optional)",
    value="",
    placeholder="You are Microsoft 365 Copilot, a large language model trained by Microsoft.",
    key="system_instruction",
)

# developer_instruction
developer_instruction = st.text_area(
    "Enter custom developer instruction (optional)",
    value="",
    placeholder="# Instructions\n\nUse `python` to execute python code in jupyter kernel to accomplish the task if needed. If the task is unclear, vague, or lacks sufficient detail, ask the user clarifying questions to ensure you fully understand the task.\n\n# Response Formats\n\nAlways answer in the format: The final answer is \\boxed{final_answer}.",
    key="developer_instruction",
)

prompt = st.chat_input("How can I help?")
if prompt:
    patches = []
    # Create a user message
    user_message = {"role": "user", "channel": "user", "raw_content": prompt}
    # Reset messages and add the user message
    st.session_state.messages = [user_message]
    file_name = None
    if "repo_path" in st.session_state:
        file_name = "|".join([x.name for x in st.session_state.repo_path])

    with st.chat_message("user"):
        st.markdown(prompt)
    # Create a new message structure for the assistant response
    assistant_message = {
        "role": "assistant",
        "channel": "analysis",
        "stream_output": [],
        "raw_content": "",  # Initialize with empty strings
        "markdown_content": "",
    }

    # Add it to the messages list
    st.session_state.messages.append(assistant_message)

    with st.chat_message("assistant"):
        with st.spinner("Running CoT", show_time=True):
            stream_container = st.container()

            # Process the stream
            for chunk in st.session_state.proxy.roll(
                prompt, file_name, system_instruction, developer_instruction
            ):
                # Add to the message's stream output
                assistant_message["stream_output"].append(chunk)
                # Update the stream display in real-time
                with stream_container:
                    write_cot_item(chunk)

                if chunk["channel"] == "final":
                    cleaned_content = clean_markdown(chunk["content"])
                    st.session_state.messages.append(
                        {
                            "role": chunk["role"],
                            "markdown_content": cleaned_content,
                            "raw_content": chunk["content"],
                            "channel": chunk["channel"],
                        }
                    )

        # After streaming is complete, force a rerun to ensure proper rendering
        st.rerun()

# show all existing messages

# Display all existing messages first
for i, message in enumerate(st.session_state.messages):
    with st.chat_message(message["role"]):
        if message["channel"] == "analysis":
            # Display the stream output if it exists
            if "stream_output" in message:
                for chunk in message["stream_output"]:
                    write_cot_item(chunk)
        elif message["channel"] == "final":
            # Handle toggle for LaTeX rendering
            toggle_key = f"latex_render_{i}"
            if toggle_key not in st.session_state:
                st.session_state[toggle_key] = False

            # Use a direct toggle that updates the session state
            render_mode = st.toggle(
                "Use LaTeX rendering", value=st.session_state[toggle_key], key=f"toggle_{i}"
            )
            # Update session state only if it changed
            if render_mode != st.session_state[toggle_key]:
                st.session_state[toggle_key] = render_mode
                st.rerun()  # Force rerun only when toggle changes

            if st.session_state[toggle_key] and "raw_content" in message:
                st.latex(message["raw_content"])
            elif "markdown_content" in message:
                st.markdown(message["markdown_content"], unsafe_allow_html=True)
        else:
            st.markdown(message["raw_content"])

# Add download button if there are messages
if st.session_state.messages:
    # Create a downloadable version of the messages
    json_str = json.dumps(st.session_state.messages, indent=2)

    # Create a direct download button
    st.download_button(
        label="Download Chat History",
        data=json_str,
        file_name="chat_history.json",
        mime="application/json",
    )
