import datetime
import logging
import os

import structlog
from colorama import Fore


def get_format(server: str, level: int) -> str:
    label = logging.getLevelName(level).lower()

    def fmt(color):
        return f"%(asctime)s {color}[{server}:{label}]{Fore.RESET} %(message)s"

    if level == logging.CRITICAL:
        return fmt(Fore.MAGENTA)
    elif level == logging.ERROR:
        return fmt(Fore.RED)
    elif level == logging.WARNING:
        return fmt(Fore.YELLOW)
    elif level == logging.INFO:
        return fmt(Fore.GREEN)
    else:
        return fmt(Fore.GREEN)


class MyFormatter(logging.Formatter):
    def __init__(self, server: str):
        self.server = server

    def format(self, record) -> str:
        format = get_format(self.server, record.levelno)
        return logging.Formatter(format, datefmt="%Y-%m-%d %H:%M:%S").format(record)


def default_logging_handler(server: str, level=logging.DEBUG):
    handler = logging.StreamHandler()
    handler.setLevel(level)
    handler.setFormatter(MyFormatter(server))
    return handler


def initialize_logging(server: str):
    logging.getLogger().handlers.clear()
    run_id = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    tmpdir = "/tmp/fastapi"
    os.makedirs(tmpdir, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        handlers=[default_logging_handler(server), logging.FileHandler(f"{tmpdir}/{run_id}.log")],
    )
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.UnicodeDecoder(),
            structlog.stdlib.render_to_log_kwargs,
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
