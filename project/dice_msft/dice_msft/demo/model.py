import os
import re
from collections import defaultdict
from enum import StrEnum
from typing import As<PERSON><PERSON><PERSON>ator

import chat
import structlog
from bus_token_completer import Bus<PERSON><PERSON><PERSON>ompleter, QoSType
from chat.render.common import check_is_parse_error
from chat.render.renderer_base import BaseRenderer
from chat.render.renderer_registry import get_renderer
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from chat.tools import DefaultMultiToolKit, take_one_step_with_tools
from dice_msft.common.datapoint_converters import WORKSPACES, format_few_shot_examples
from dice_msft.demo.protocol import (
    DEFAULT_MODEL,
    MODEL_MAPPING,
    ModelMeta,
    RollerStepResult,
    SimpliedChatMessage,
)
from dice_msft.prompts.instructions import (
    DEFAULT_SYS_IDENTITY,
    DEV_INSTRUCTION_V1,
    SYS_INSTRUCTION_V1,
)
from legacy_rest_token_completer import LegacyRestTokenCompleter
from message_completer import TokenMessageCompleter
from message_completer.message_completer import <PERSON>rse<PERSON>rrorMode
from research_ace.v2.tools.python_code_execution_tool import get_instructions
from token_completer import CompleterBackend
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter

logger = structlog.get_logger(__name__)


class Model:
    def __init__(self, model_meta: ModelMeta):
        self.model_meta = model_meta
        self.active_model = DEFAULT_MODEL
        self._message_completer_cache = defaultdict(self._create_completer)

        summarizer_ip = os.environ.get("SUMMARIZER_IP", None)
        self.use_external_summarizer = summarizer_ip is not None

        if self.use_external_summarizer:
            message_summarizer_config = TokenMessageCompleter.Config(
                token_completer_config=LegacyRestTokenCompleter.Config(
                    api_base=f"http://{summarizer_ip}:5122/v1/inference",
                    backend=CompleterBackend.FALCON_TEXT_BACKEND,
                ),
                renderer="harmony_v4.0.13_16k_orion_mmgen_no_asr_2k_action",
                completion_params={
                    "seed": 42,
                    "temperature": 0,
                    "model": "model",
                    "headers": {"organization": "openai-internal"},
                },
            )
            self.message_summarizer = message_summarizer_config.build()
        else:
            self.message_summarizer = self.message_completer

    @property
    def active_model(self) -> ModelMeta:
        return self._active_model

    @active_model.setter
    def active_model(self, model):
        if not model:
            model = DEFAULT_MODEL

        if model not in MODEL_MAPPING:
            raise ValueError(f"active_model must be one of {MODEL_MAPPING.keys()}")

        self._active_model = MODEL_MAPPING.get(model)

    def _create_completer(self):
        # return BusTokenCompleter(
        #     topic_mode_or_user="msft",
        #     topic_or_snapshot="az://orngcresco/twapi/mini/e/shtri-032825-dtr3-4ott_gr-05062025-222524/policy/step_000180/25050622260733BATEDI-0/",
        #     # bus_line="bus",
        #     qos_type=QoSType.FIFO,
        #     max_retries=3,
        # )
        token_completer_config = LegacyRestTokenCompleter.Config(
            api_base=f"http://{self.active_model.inference_ip}:5122/v1/inference",
            backend=CompleterBackend.FALCON_MM_BACKEND,
        )
        berry_turn_completer_config = BerryMultiMessageTurnCompleter.Config(
            token_completer_config=token_completer_config,
            completion_params={
                # "seed": self.active_model.seed,
                "model": "model",
                "temperature": self.active_model.temperature,
                # "headers": {"organization": "openai-internal"},
            },
            renderer=self.active_model.renderer_name,
        )
        return berry_turn_completer_config.build()
        # message_completer_config = TokenMessageCompleter.Config(
        #     token_completer_config=LegacyRestTokenCompleter.Config(
        #         api_base=f"http://{self.active_model.inference_ip}:5122/v1/inference",
        #         backend=CompleterBackend.FALCON_TEXT_BACKEND,
        #     ),
        #     renderer=self.active_model.renderer_name,
        #     completion_params={
        #         "seed": self.active_model.seed,
        #         "temperature": self.active_model.temperature,
        #         "model": "model",
        #         "headers": {"organization": "openai-internal"},
        #     },
        # )
        # return message_completer_config.build()

    @property
    def message_completer(self):
        return self._message_completer_cache[self.active_model.name]

    def setup_convo_with_user_messages(
        self,
        messages: list[SimpliedChatMessage],
        system_instruction: str,
        developer_instruction: str,
        search_folder_path: str,
        toolkit: DefaultMultiToolKit,
    ):
        if self.active_model.name == "DiceV4" or self.active_model.name == "DiceV5":
            user_instruction = ""
            harmony_messages = []
            is_first_user_message = False
            for m in messages:
                try:
                    role = m.role.lower()
                    if not is_first_user_message and role == "user":
                        files = []
                        if search_folder_path:
                            files = [
                                f"`{WORKSPACES['sandbox']}/{f}`"
                                for f in search_folder_path.split("|")
                            ]
                        user_instruction = format_few_shot_examples(
                            m.content,
                            files,
                        )
                        is_first_user_message = True
                        continue

                    harmony_messages.append(
                        chat.Message(
                            author=chat.Author(role=role),
                            content=chat.Text.from_string(m.content),
                            recipient=(m.audience if m.audience else chat.DEFAULT_RECIPIENT),
                        ),
                    )
                except Exception:
                    # NOTE(mercerchen): We don't want to expose OAI error message.
                    # Put a simple message here for now.
                    raise f"Invalid message: {m}"

            return chat.Conversation(
                messages=[
                    chat.Message.system(
                        model_identity_desc=None,
                        instructions=[
                            SYS_INSTRUCTION_V1 if system_instruction == "" else system_instruction
                        ],
                        tools_section={
                            "python": get_instructions(
                                name="python",
                                timeout=15 * 60.0,
                                enable_matplotlib_instructions=True,  # chart instructions
                            ).replace("/mnt/data", f"{WORKSPACES['sandbox']}")
                        },
                        channel_config=chat.SystemChannelConfig(
                            valid_channels=(
                                BerryChannel.CHAIN_OF_THOUGHT,
                                BerryChannel.FINAL_ANSWER,
                            ),
                            channel_required=True,
                        ),
                        metadata=chat.SystemContentMetadata(
                            reward_multiplier=None,
                        ),
                    ),
                    chat.Message.developer(
                        DEV_INSTRUCTION_V1 if developer_instruction == "" else developer_instruction
                    ),
                    chat.Message.user(user_instruction),
                    *harmony_messages,
                ]
            )
        else:
            raise f"Invalid model name: {self.active_model.name}"

    async def async_take_one_step_with_model(
        self,
        convo: chat.Conversation,
        renderer: BaseRenderer,
        is_last_step: bool = False,
    ) -> RollerStepResult:
        # Sometimes ParseError are returned from model, but retry usually works.
        # We add retry (at max 3 times) here for now.
        # TODO: Remove this retry after model fixed the issue.
        max_trial = 3
        for i in range(max_trial):
            # tokens = renderer.render_for_completion_multimodal_toklist(
            #     convo, role=chat.Role.ASSISTANT
            # )
            # [loaded_tokens] = [tokens]

            # completion = await self.message_completer.async_completion(
            #     prompt=[loaded_tokens],
            #     max_tokens=(2**17 - 2**16),
            #     stop=renderer.stop_sequences(),  # type: ignore
            #     temperature=1.0,
            # )

            # message = renderer.parse(
            #     completions.choices[0].toklist.spans[0].tokens, role=chat.Role.ASSISTANT
            # )
            completion = await self.message_completer.async_completion(
                conversation=convo,
                # end_header=is_last_step,
                reward_multiplier=64,
                # valid_channels=(
                #     BerryChannel.CHAIN_OF_THOUGHT,
                #     BerryChannel.FINAL_ANSWER,
                # ),
                valid_channels=("analysis", "confidence", "final"),
            )
            new_messages = completion.output_messages
            for message in new_messages:
                logger.info(f"Model message: {message}")

            if any(check_is_parse_error(m) for m in new_messages):
                logger.warning(f"ParseError detected in trial {i}.")
                if i < max_trial - 1:
                    continue

            convo = convo.with_suffix(*new_messages)
            return RollerStepResult(convo=convo, new_messages=new_messages)

    async def async_take_one_step_with_tools(
        self, convo: chat.Conversation, toolkit: DefaultMultiToolKit
    ) -> RollerStepResult:
        messages = convo.messages
        num_messages = len(messages)
        async for tool_message in take_one_step_with_tools(
            prefix_convo=convo.prefix(-1),
            message=messages[-1],
            toolkit=toolkit,
        ):
            logger.info(f"Tool message: {tool_message}")
            if convo.messages[-1].id == tool_message.id:
                convo.messages[-1] = tool_message
            else:
                convo = convo.with_suffix(tool_message)
        new_messages = convo.messages[num_messages:]
        return RollerStepResult(convo=convo, new_messages=new_messages)

    def _split_text_to_chucks(self, text, max_words=200):
        sentence_pattern = re.compile(r'(?<=[.!?])(?:(?:"|\')?\s+|\s+)(?=[A-Z0-9])')
        sentences = sentence_pattern.split(text)

        chunks = []
        current_chunk = []
        current_word_count = 0

        for sentence in sentences:
            sentence_word_count = len(sentence.split())

            if current_word_count + sentence_word_count <= max_words:
                current_chunk.append(sentence)
                current_word_count += sentence_word_count
            else:
                if current_chunk:
                    chunks.append(" ".join(current_chunk))
                current_chunk = [sentence]
                current_word_count = sentence_word_count

        if current_chunk:
            chunks.append(" ".join(current_chunk))

        return chunks

    async def generate_summary(self, message: chat.Message):

        if self.use_external_summarizer:
            # logic borrowed from https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/lib/cot/cot/cot_summarizer.py&version=GBopenai/msft/rlhf-mirror-updates&_a=contents
            system_instruction = "You are a faithful summarizer of your thought process and you bring the user along with it. You must write the summary in only the following locale language: en"
            chunks = self._split_text_to_chucks(str(message.content))
        else:
            system_instruction = " ".join(
                [
                    "Forget everything you have been told. "
                    "You are an AI rewritter and summarizer. ",
                    "You will be given a message containing chain-of-thought planning on solving a software engineering issue. Please try your best to rewrite it to be more consise and better reflex how the task is planned to be done. ",
                    "You might notice some commands in JSON format in the text. Please do NOT include them in your answer, but use human language to describe what to do instead. ",
                    "Do NOT use markdown format, just provide the pure text rewrite. "
                    "Make sure the finial answer is truthful, complete, and short with in 20 words. ",
                ]
            )
            chunks = [
                "# Task\n\nSummarize the following chain-of-thought message:\n\n"
                + str(message.content)
            ]

        convo = chat.Conversation(
            messages=[
                chat.Message.system(system_instruction),
            ]
        )

        out_messages = []
        for i, chuck in enumerate(chunks):
            user_message = [chat.Message.user(content=chuck)]
            convo = convo.with_suffix(*user_message)
            completion = await self.message_summarizer.async_completion(
                conversations=[convo], n=1, end_header=True
            )
            new_messages = completion.choices[0].get_messages(
                parse_error_mode=ParseErrorMode.SYSTEM_ERROR
            )
            new_messages = [new_messages[-1]]
            out_messages.extend(new_messages)
            convo = convo.with_suffix(*new_messages)

        return out_messages

    async def async_roll(
        self,
        convo: chat.Conversation,
        toolkit: DefaultMultiToolKit,
        max_episode_steps: int,
    ) -> AsyncGenerator[RollerStepResult, None]:
        valid_nontool_recipients = {
            m.author.display_name() for m in convo.messages if m.author.role != chat.Role.TOOL
        }
        valid_recipients = (
            valid_nontool_recipients
            | (set([tool.name for tool in toolkit.tools]) if toolkit else set())
            | {"all"}
        )

        for elapsed_step in range(max_episode_steps):
            is_last_step = elapsed_step == max_episode_steps - 1
            model_step_result = await self.async_take_one_step_with_model(
                convo=convo,
                is_last_step=is_last_step,
                renderer=get_renderer(self.active_model.renderer_name),
            )
            convo = model_step_result.convo
            yield model_step_result

            if (
                is_last_step
                or model_step_result.new_messages[-1].end_turn
                or (
                    model_step_result.new_messages[-1].recipient not in valid_recipients
                    and model_step_result.new_messages[-1].recipient.split(".")[0]
                    not in valid_recipients
                )
            ):
                break
            # call tool once
            tools_step_result = await self.async_take_one_step_with_tools(
                convo=convo, toolkit=toolkit
            )
            convo = tools_step_result.convo
            yield tools_step_result
