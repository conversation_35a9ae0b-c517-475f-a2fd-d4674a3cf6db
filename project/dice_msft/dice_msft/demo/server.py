import base64
import json
import os
import traceback
import uuid
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import chat
import dice_msft.demo.protocol as protocol
import structlog
import uvicorn
from caas.api import caas_api
from chat.tools import DefaultMultiToolKit
from dice_msft.demo.caas_tool_utils import (
    get_container_id,
    get_python_tool,
    start_caas_session,
    upload_file_to_container,
)
from dice_msft.demo.model import Model
from dice_msft.graders.icl_grader import image_to_base64
from fastapi import FastAPI, File, Request, UploadFile, status
from fastapi.responses import JSONResponse, StreamingResponse

logger = structlog.get_logger(__name__)


def is_prod():
    return os.environ.get("ENV", "dev") == "prod"


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Waiting for server to start...")
    # This should not change throughout the server's life span
    app.state.model = Model(model_meta=protocol.ModelMeta())
    app.state.caas = caas_api(endpoint="https://westus2.caas.azure.com")
    logger.info("Loaded CAAS API...")
    yield


app = FastAPI(lifespan=lifespan)


@app.get("/health")
async def health():
    return {"status": "ok"}


async def async_roller_with_diagnosis(
    model: Model, toolkit: DefaultMultiToolKit, roll_request: protocol.RollRequest
) -> AsyncGenerator[protocol.SimpliedChatMessage, None]:
    i = 0
    async for message in async_roller(model, toolkit, roll_request):
        logger.info(f"[diagnosis] roll_id={roll_request.roll_id} yield message[{i}]={message}")
        yield message
        i += 1


async def async_roller(
    model: Model, toolkit: DefaultMultiToolKit, roll_request: protocol.RollRequest
) -> AsyncGenerator[protocol.SimpliedChatMessage, None]:
    # previous_commit_id = await get_head_commit_id(container, roll_request.search_folder_path)
    model.active_model = roll_request.model
    convo = model.setup_convo_with_user_messages(
        messages=roll_request.messages,
        system_instruction=roll_request.system_instruction,
        developer_instruction=roll_request.developer_instruction,
        search_folder_path=roll_request.search_folder_path,
        toolkit=toolkit,
    )
    logger.info(
        f"Rolling with {roll_request.max_episode_steps} max_episode_steps using model {model.active_model.name}."
    )
    async for roller_step_result in model.async_roll(
        convo=convo, toolkit=toolkit, max_episode_steps=roll_request.max_episode_steps
    ):
        for message in roller_step_result.new_messages:
            if (
                message.channel == "final"
                and message.role == chat.Role.ASSISTANT
                and message.recipient == "all"
                and len(str(message.content).strip()) == 0
            ):
                continue
            # elif (
            #     message.channel == "analysis"
            #     and message.role == chat.Role.ASSISTANT
            #     and message.recipient == "all"
            #     and len(str(message.content).strip()) > 0
            # ):
            #     summary_messages = await model.generate_summary(message)
            #     for summary_message in summary_messages:
            #         yield (
            #             protocol.SimpliedChatMessage(
            #                 role=str(message.role),
            #                 content=str(summary_message.content),
            #                 audience=str(message.recipient),
            #                 end_turn=message.end_turn,
            #                 id=str(summary_message.id),
            #                 channel="summary",
            #             ).model_dump_json() + "\n"
            #     )
            else:
                content = str(message.content)
                asset_content = []
                if message.content.content_type == "multimodal_text":
                    for part in message.content.parts:
                        if isinstance(part, chat.Image):
                            asset_content.append(part.get_base64())
                yield (
                    protocol.SimpliedChatMessage(
                        role=str(message.role),
                        content=content,
                        audience=str(message.recipient),
                        end_turn=message.end_turn,
                        id=str(message.id),
                        channel=message.channel,
                        asset_pointer_content=asset_content,
                    ).model_dump_json()
                    + "\n"
                )

                # # only try to generate diff from caas after tool call
                # if str(message.role) == "tool":
                #     diff = await generate_diff(container, roll_request.search_folder_path, previous_commit_id)
                #     # most of the time tool doesn't modify code
                #     if diff:
                #         (diff_payload, previous_commit_id) = diff
                #         yield (
                #             protocol.SimpliedChatMessage(
                #                 role="assistant",
                #                 content=diff_payload,
                #                 audience="all",
                #                 end_turn=message.end_turn,
                #                 id=str(uuid.uuid4()),
                #                 channel="git_apply_patch",
                #             ).model_dump_json() + "\n"
                #         )


@app.post("/new_session")
async def new_session(http_request: Request, new_session_request: protocol.NewSessionRequest):
    logger.info(f"Received a /new_session request: {new_session_request}")
    try:
        session = await start_caas_session(
            caas=http_request.app.state.caas,
            caas_image=new_session_request.caas_image_name,
            idle_ttl=new_session_request.idle_ttl,
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=protocol.NewSessionResponse(
                session_id=session.save(),
                container_id=get_container_id(session),
            ).model_dump(),
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": f"Failed with error:\n{e}"},
        )


@app.post("/upload")
async def upload(
    http_request: Request,
    upload_request: protocol.UploadRequest,
):
    try:
        logger.info("Received a /upload request")
        sess = http_request.app.state.caas.continue_session(
            upload_request.session_id,
        )
        logger.info(f"Successfully restored container.")
        unzip_target_folder, list_of_files = await upload_file_to_container(
            sess, upload_request.zip_base64, upload_request.folder_name
        )
        logger.info(
            f"Folder at {unzip_target_folder}.\nList of files at the folder:\n{list_of_files}"
        )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=protocol.UploadResponse(
                session_id=upload_request.session_id,
                unzipped_folder_path=unzip_target_folder,
                container_id=get_container_id(sess),
            ).model_dump(),
        )
    except Exception as e:
        logger.error(f"upload failed: {e}. {traceback.format_stack()}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": f"Failed with error:\n{e}"},
        )


# @app.post("/keep_alive")
# async def keep_alive(
#     keep_alive_request: protocol.KeepAliveRequest,
# ):
#     try:
#         logger.info("Received a /keep_alive request")
#         container = await CaasContainer.load(
#             base64.b64decode(keep_alive_request.session_id)
#         )
#         logger.info(f"Successfully restored container.")
#         success = await container.jupyter_session.session.keepalive()
#         return JSONResponse(
#             status_code=status.HTTP_200_OK,
#             content=protocol.KeepAliveResponse(
#                 session_id=keep_alive_request.session_id,
#                 success=success,
#                 container_id=get_container_id(container),
#             ).dict(),
#         )
#     except Exception as e:
#         return JSONResponse(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             content={"message": f"Failed with error:\n{e}"},
#         )


@app.post("/roll")
async def roll(
    http_request: Request,
    roll_request: protocol.RollRequest,
):
    logger.info(f"Received a roll request, model={roll_request.model}")
    try:
        if not roll_request.roll_id:
            roll_request.roll_id = str(uuid.uuid4())[:8]
            logger.info(f"roll_id is not provided, generated {roll_request.roll_id}")
        if roll_request.enable_diagnosis:
            logger.info(f"[diagnosis] roll_request={roll_request.model_dump_json()}")
        sess = http_request.app.state.caas.continue_session(
            roll_request.session_id,
        )
        logger.info(f"Successfully restored container.")
        tool = get_python_tool(sess)
        toolkit = DefaultMultiToolKit(tools=[tool])
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": f"Failed to start CAAS container tool with error:\n{e}"},
        )
    try:
        roller = async_roller_with_diagnosis if roll_request.enable_diagnosis else async_roller
        return StreamingResponse(
            roller(
                model=http_request.app.state.model,
                toolkit=toolkit,
                roll_request=roll_request,
            ),
            media_type="application/json",
        )
    except Exception as e:
        logger.error(f"roll failed: {e}. {traceback.format_stack()}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"message": f"Failed with error when streaming messages:\n{e}"},
        )


# @app.post("/roll_for_eval")
# async def roll(
#     http_request: Request,
#     roll_request: protocol.RollForEvalRequest,
# ):
#     logger.info(f"Received a roll_for_eval request, model={roll_request.model}")

#     try:
#         container = await start_caas_container(
#             caas_image=DEFAULT_CAAS_IMAGE,
#             idle_ttl=15*60,
#         )
#         caas_tool = get_caas_container_tool(container)
#         toolkit = DefaultMultiToolKit(tools=[caas_tool])
#         if not roll_request.roll_id:
#             roll_request.roll_id = str(uuid.uuid4())[:8]
#             logger.info(f"roll_id is not provided, generated {roll_request.roll_id}")
#         if roll_request.enable_diagnosis:
#             logger.info(f"[diagnosis] roll_request={roll_request.model_dump_json()}")

#         if roll_request.file_data or roll_request.file_name:
#             if not roll_request.file_data or not roll_request.file_name:
#                 return JSONResponse(
#                     status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#                     content={"message": f"Both file_data and file_name must be provided together"},
#                 )

#             try:
#                 unzip_target_folder, list_of_files = await upload_file_to_container(
#                     container, roll_request.file_data, f"test-demo/{roll_request.file_name}"
#                 )
#                 logger.info(
#                     f"Folder at {unzip_target_folder}.\nList of files at the folder:\n{list_of_files}"
#                 )
#             except Exception as e:
#                 logger.error(f"upload failed: {e}. {traceback.format_stack()}")
#                 return JSONResponse(
#                     status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#                     content={"message": f"Failed with error:\n{e}"},
#                 )
#     except Exception as e:
#         return JSONResponse(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             content={"message": f"Failed with error:\n{e}"},
#         )

#     try:
#         roller = async_roller_with_diagnosis if roll_request.enable_diagnosis else async_roller
#         chunks = []
#         async for chunk in roller(
#                 model=http_request.app.state.model,
#                 toolkit=toolkit,
#                 container=container,
#                 roll_request=roll_request,
#             ):
#             chunks.append(chunk)

#         return JSONResponse(
#             status_code=status.HTTP_200_OK,
#             content=json.dumps(chunks)
#         )

#     except Exception as e:
#         logger.error(f"roll_for_eval failed: {e}. {traceback.format_stack()}")
#         return JSONResponse(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             content={"message": f"Failed with error when streaming messages:\n{e}"},
#         )


def main(
    server_host: str = "0.0.0.0",
    server_port: int = 1999,
):
    reload = not is_prod()
    logger.info(f"Reload: {str(reload)}")
    uvicorn.run(
        "dice_msft.demo.server:app",
        host=server_host,
        port=server_port,
        log_config={
            "version": 1,
            "disable_existing_loggers": False,
        },
        reload=reload,
    )


if __name__ == "__main__":
    from dice_msft.demo.logs import initialize_logging

    initialize_logging("dice_msft.demo.server")
    main()
