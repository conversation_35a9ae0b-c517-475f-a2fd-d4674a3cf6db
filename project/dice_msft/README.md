## 🎲 `dice_msft`
Project DICE aka Analyst

## Prerequisites
- Complete onboarding to Orange [Orange: Getting Started](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/4583/Orange-Getting-Started). You also need to [setup Snowflake account](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/6826/4.2-Setup-Snowflake-for-Analyzing-Training-Logs) in order to analyse training and evaluation logs when using Peashooter.
- Onboard to WestUS2 CaaS clusters (for M365). Reach out to @[<PERSON>](<PERSON><PERSON>@microsoft.com) on [CaaS Support channel](https://teams.microsoft.com/l/channel/19%3A319ea9c815d942b988fc6083f7d95b38%40thread.tacv2/CaaS%20Support?groupId=ae36f2e8-3a1d-48d0-928a-6d6021e7a168&tenantId=72f988bf-86f1-41af-91ab-2d7cd011db47). 

## Setup
Install package through oaipkg

`oaipkg installoai dice_msft`

## Evaluation
**NOTE** 
- To easily manage model configurations for hosting any model either via **bus** or **twapi**, [install](https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?version=GBnguyen-model-manager&path=/project/model_manager) [OMoM: Orange Model Manager](https://dev.azure.com/project-argos/Mimco/_git/glass?path=/project/omom_msft&version=GBmain&_a=contents). You can find commands to host models already in config + add configs for your known checkpoints as well. Use this to host models for grading.
    ```
    ❯ omom cmd o3
    🚀 Generating command...
    RUST_BACKTRACE=1 nohup python -m harmony_scripts.engines.start_engine \
    --name o3-engine \
    --model_config=falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal \
    --mode=optimal \
    --snapshot_path="az://orngcresco/models/snapshots/o3-d64-step860-20250324-retransferred-20250401-decrypted" \
    --is_multimodal=true \
    --gpu_kind=A100_80G \
    --renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget" \
    --extra_config_string="load_inference_snapshots_with_tcv2=False raise_on_load_for_missing_tensor=False ixf_sampling_extension_gpu_to_cpu_async=False twapi_cpu_comm_backend=gloo ixf_batcher_response_loop_timeout=600 allow_embedding_prefix_loading=True tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0} n_op_shards=8" \
    --encoder_decoder_snapshots '{"clip":"az://orngcresco/models/snapshots/mm/oaiomni/rcall/crow-export/scallion2b/omni/model_final_ts.pt"}' \
    --bus_enable_qos=True \
    --bus_rate_limiter=KV_UTIL \
    --bus_topic_mode_or_user="msft" \
    --cluster=local \
    --n_replicas=1 \
    > /dev/null 2>&1 &
    ``` 
- Before launching peaval jobs or evaluation through scripts, always check if your bus for **grader** is up on the [Bus Metrics dashboard](https://ai-infra-ephgecb0cucpbkg8.wus2.grafana.azure.com/goto/Kc0cQXfHg?orgId=1). If you are using models hosted via twapi for grading, ensure your engine is ready to accept requests. You can additionally find more details on [Inference via bus](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/5205/Inference-via-Bus) and [local twapi engine](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/5203/Local-Twapi-Engine). 

### Peashooter Validation (peaval)
When submitting peaval jobs include the preset of the type
    
    `:dice_msft.presets.files:<preset-name>`
where `<preset-name>`is indicates your file-type and batch number.

An example preset for MultiFile batch8 is `mf_batch8_sandbox_eval`. For any new dataset with datagen csv format present locally, we can follow [this](dice_msft/scripts/upload_from_csv.py) to upload and use the input jsonl blob directory as `dataset_id` to minimally create a preset like below.

```
berry.preset_utils.eval_dataset_preset(
    [
        "dataset.dataset_id=",
        "dataset.datapoint_converters.0.kwargs.base_name=test_category",
        "dataset.datapoint_converters.0.kwargs.workspace=sandbox",
    ]
    + get_datagen_jsonl_args()
    + get_target_spi(5)
    + get_default_grader_args()
)
```
Verify your run on Lemon Dashboard. You can use the file [download_snowflake](dice_msft/scripts/download_snowflake.py) to download complete results as JSONL with prompt, conversation and grader results in "additional_metadata" field.

### Small(er) scale quick Validation
You can perform quick validations on your dataset by hosting the model on bus or twapi locally. You can modify tool and grader logic as per requirement. 
[simple_eval](dice_msft/scripts/simple_eval.py)     

### Updating grading logic
1. For peavals jobs create and add your custom grader by overriding the `_grade_batch_inner` method of berry grader. Checkout default [grader](dice_msft/graders/icl_grader.py)
2. For quick validation you can customize the grader `Conversation` before hitting your grader endpoint.

## Training

Baseline DBV4 run: [wandb metrics](https://msaip.wandb.io/orangewandb/db-test/runs/shtri-032825-dtr3-4ott_gr-05062025-222524?nw=nwusershtri)
TODO: Add training presets
