#!/bin/bash

# Default values
MODEL_KEYWORD="gpt5-mini"

# Parse command line arguments using getopts
usage() {
    echo "Usage: $0 [-m|--model-keyword MODEL_KEYWORD]"
    echo "  -m, --model-keyword  Model keyword (default: gpt5-mini)"
    exit 1
}

# Handle long options by converting them to short options
for arg in "$@"; do
    shift
    case "$arg" in
        "--model-keyword") set -- "$@" "-m" ;;
        "--help") set -- "$@" "-h" ;;
        *) set -- "$@" "$arg" ;;
    esac
done

# Parse short options with getopts
while getopts "m:h" opt; do
    case $opt in
        m) MODEL_KEYWORD="$OPTARG" ;;
        h) usage ;;
        \?) echo "Invalid option: -$OPTARG" >&2; usage ;;
        :) echo "Option -$OPTARG requires an argument." >&2; usage ;;
    esac
done

# Generate timestamp and output directory
TIMESTAMP=$(date +"%Y%m%d-%H-%M-%S")
OUTPUT_DIR="$HOME/lcb_outputs/${MODEL_KEYWORD}_${TIMESTAMP}"

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo "Using model keyword: $MODEL_KEYWORD"
echo "Output directory: $OUTPUT_DIR"

# Run Inference
export OPENAI_API_KEY='fake'
NUM_TEST=5
python run_livecodebench_async.py --num_tests ${NUM_TEST} --output "$OUTPUT_DIR" --max_tokens 32768 --concurrency 64 --model-keyword "$MODEL_KEYWORD" | tee "$OUTPUT_DIR/inference_stdout.log"

# Evaluate
cd ~/code/LiveCodeBench
# Hack to avoid an assertion failure while I'm testing things
# sed -i.bak '/assert len(custom_outputs) == len(benchmark), f"{len(custom_outputs)} != {len(benchmark)}"/s/^/# /' ~/code/LiveCodeBench/lcb_runner/runner/custom_evaluator.py && echo "Successfully commented out the assertion line" || echo "Failed to comment out the assertion line"
for idx in $(seq 0 $((NUM_TEST-1))); do
    python -m lcb_runner.runner.custom_evaluator --custom_output_file "$OUTPUT_DIR/test_${idx}.json" --release_version v5_v6 | tee "$OUTPUT_DIR/evaluation_${idx}_stdout.log"
done

# Display Results
cd ~/code/glass/project/evals_msft/benchmarks/livecodebench
python show_results.py --output-dir "$OUTPUT_DIR" | tee "$OUTPUT_DIR/final_results.txt"

# Upload results to blob storage
bbb cptree $OUTPUT_DIR "https://orngscuscresco.blob.core.windows.net/data/evals_msft/benchmarks/lcb/runs/${MODEL_KEYWORD}_${TIMESTAMP}"