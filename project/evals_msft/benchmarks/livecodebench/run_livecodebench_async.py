import argparse
import asyncio
import json
import os
import re
from contextlib import closing
from typing import Any, Dict, <PERSON>, <PERSON><PERSON>

from bus_token_completer import <PERSON><PERSON>oken<PERSON>ompleter, QoSType
from chat import chat
from chat.render.renderer_registry import get_renderer
from legacy_rest_token_completer import <PERSON>Rest<PERSON><PERSON>Completer
from message_completer import TokenMessageCompleter
from message_completer.message_completer import Parse<PERSON>rrorM<PERSON>
from PIL import Image
from token_completer import CompleterBackend
from tqdm import tqdm
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter

# TODO: We can move this to a common storage so that we don't have to update this often
DEFAULT_MODEL = "gpt5-mini"
BUS_CONFIG_FILE = "../multi_swe_bench/bus_config.json"
with open(BUS_CONFIG_FILE, "r") as f:
    MODEL_NAME_TO_BUS_INFO_MAPPING = json.load(f)

USER_PROMPT = """You will be given a competitive programming problem.
Analyze the maximum input constraints and identify the optimal algorithmic approach and data structures needed to process the largest possible test cases within the time and memory limits, then explain why your chosen implementation strategy is the most efficient solution. Please reason step by step about your solution approach, then provide a complete implementation in Python 3 that is thoroughly optimized for both speed and memory usage.

Your solution must read input from standard input (input()), write output to standard output (print()).
Do not include any debug prints or additional output.

Put your final solution within a single code block which looks like
```python
{starter_code}<your code here>
```

Problem:
{question}
"""


def extract_code_from_last_block(text: str) -> Tuple[str, bool]:
    """
    Extracts the code content from the last triple-backtick block.

    Args:
        text: Input text containing potential code blocks

    Returns:
        tuple: (extracted_text, match_found) where match_found is True if a
               triple-backtick block was found, otherwise False.
    """
    pattern = r"```[^\n]*\n([\s\S]*?)```"
    matches = re.findall(pattern, text)
    return (matches[-1], True) if matches else (text, False)


def setup_completer_and_renderer(model_keyword, max_tokens: int = 65535):
    """Initialize and return the token completer and renderer."""
    # token_completer_config = LegacyRestTokenCompleter.Config(
    #     api_base="http://127.0.0.1:5122/v1/inference",
    #     backend=CompleterBackend.FALCON_MM_BACKEND,
    # )

    # Use Bus:
    token_completer_config = BusTokenCompleter.Config(
        topic_mode_or_user=MODEL_NAME_TO_BUS_INFO_MAPPING[model_keyword]["user"],
        topic_or_snapshot=MODEL_NAME_TO_BUS_INFO_MAPPING[model_keyword]["snapshot"],
        bus_line="bus",
        qos_type=QoSType.FIFO,
    )

    renderer_name = MODEL_NAME_TO_BUS_INFO_MAPPING[model_keyword]["renderer"]
    message_completer_config = TokenMessageCompleter.Config(
        token_completer_config=token_completer_config,
        renderer=renderer_name,
        completion_params={"model": "model", "temperature": 1, "max_tokens": max_tokens},
    )

    return message_completer_config.build(), get_renderer(renderer_name)


def count_tokens(text: str) -> int:
    """
    Simple token counting function that approximates tokens by splitting on whitespace.
    For more accurate counting, you could use a proper tokenizer like tiktoken.
    """
    return len(text.split())


async def process_question(question_data, message_completer, renderer) -> Dict[str, Any]:
    """Process a single question and return the result."""
    question = question_data["question_content"].strip()
    question_id = question_data["question_id"]
    starter_code = question_data["starter_code"]
    difficulty = question_data["difficulty"]

    messages: list[chat.Message] = []
    system_message = chat.Message.system(
        model_identity_desc="You should carefully adhere to all formatting instructions.",
        tools_section=None,
        channel_config=chat.SystemChannelConfig(
            valid_channels=("analysis", "final"), channel_required=True
        ),
        metadata=chat.SystemContentMetadata(reward_multiplier=128),
    )
    messages.append(system_message)

    formatted_prompt = USER_PROMPT.format(question=question, starter_code=starter_code)
    messages.append(chat.Message.user(content=formatted_prompt))
    conversation = chat.Conversation(messages=messages)
    # print("Conversation ready to be sent")
    # print(conversation)
    for _ in range(5):
        try:
            completion = await message_completer.async_completion(
                conversations=[conversation],
                n=1,
            )

            choice_output = None
            for choice in completion.choices:
                new_messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
                all_parts = [p for m in new_messages for p in m.content.parts]
                choice_output = "\n\n".join(all_parts)
            break
        except Exception as e:
            completion = None
            choice_output = None

    if not choice_output:
        print(f"Error processing question {question_id}")
        return {
            "question_id": question_id,
            "code_list": [""],
            "matched": False,
            "question_content": question,
            "difficulty": difficulty,
            "starter_code": starter_code,
            "full_output": "",
            "token_count": 0,
        }

    # print("Conversation processed successfully.")
    extracted_code, code_matched = extract_code_from_last_block(choice_output)
    # print("Code extracted successfully")
    token_count = count_tokens(choice_output)
    # print("Tokens counted successfully")

    return {
        "question_id": question_id,
        "code_list": [extracted_code],
        "matched": code_matched,
        "question_content": question,
        "full_output": choice_output,
        "token_count": token_count,
        "difficulty": difficulty,
        "starter_code": starter_code,
    }


async def process_question_worker(
    model_keyword, question_data: Dict[str, str], max_tokens: int = 65535
) -> Dict[str, Any]:
    """Worker function to process a single question. Creates its own completer and renderer."""
    message_completer, renderer = setup_completer_and_renderer(model_keyword, max_tokens)
    # print(f"Processing question {question_data['question_id']}")
    return await process_question(question_data, message_completer, renderer)


def load_questions_from_file(file_path: str) -> List[Dict[str, str]]:
    """Load all questions from a file into memory."""
    questions = []
    with open(file_path, "r") as file:
        for line in file:
            if not line.strip():
                continue
            data = json.loads(line)
            questions.append(
                {
                    "question_content": data["question_content"],
                    "question_id": data["question_id"],
                    "starter_code": data["starter_code"],
                    "difficulty": data["difficulty"],
                }
            )
    return questions


async def process_input_file(
    model_keyword, file_path: str, concurrency_limit: int = 64, max_tokens: int = 65535
) -> Tuple[List[Dict[str, Any]], int]:
    """Process all questions in a single input file using asyncio with semaphore."""
    questions = load_questions_from_file(file_path)

    semaphore = asyncio.Semaphore(concurrency_limit)
    results = []

    async def process_with_semaphore(model_keyword, question_data):
        async with semaphore:
            return await process_question_worker(model_keyword, question_data, max_tokens)

    tasks = [process_with_semaphore(model_keyword, question_data) for question_data in questions]

    # Use asyncio.as_completed to show progress
    for task in tqdm(
        asyncio.as_completed(tasks),
        total=len(questions),
        desc=f"Processing {os.path.basename(file_path)}",
        unit="questions",
    ):
        result = await task
        results.append(result)

    non_match_count = sum(1 for result in results if not result["matched"])
    return results, non_match_count


def ensure_output_directory(directory: str = "./outputs") -> None:
    """Create output directory if it doesn't exist."""
    os.makedirs(directory, exist_ok=True)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run LiveCodeBench async processing")
    parser.add_argument(
        "--output",
        type=str,
        default="./outputs",
        help="Output directory for results (default: ./outputs)",
    )
    parser.add_argument("--num_tests", type=int, default=5, help="Number of test runs (default: 5)")
    parser.add_argument(
        "--concurrency",
        type=int,
        default=64,
        help="Maximum number of concurrent requests (default: 64)",
    )
    parser.add_argument(
        "--max_tokens",
        type=int,
        default=32768,
        help="Maximum number of tokens for completion (default: 32768)",
    )
    parser.add_argument(
        "--model-keyword",
        type=str,
        default=DEFAULT_MODEL,
        choices=list(MODEL_NAME_TO_BUS_INFO_MAPPING.keys()),
        help=f"Model keyword to use (default: {DEFAULT_MODEL})",
    )
    return parser.parse_args()


async def main():
    """Main execution function."""
    args = parse_arguments()

    input_files = [
        # "livecodebench/code_generation_lite/test.jsonl",
        # "livecodebench/code_generation_lite/test2.jsonl",
        # "livecodebench/code_generation_lite/test3.jsonl",
        # "livecodebench/code_generation_lite/test4.jsonl",
        # "livecodebench/code_generation_lite/test5.jsonl",
        # "livecodebench/code_generation_lite/test6.jsonl",
        "livecodebench/code_generation_lite/test5_6.jsonl",
        # "livecodebench/code_generation_lite/sample_3_test5_6.jsonl"
    ]

    ensure_output_directory(args.output)

    for test_idx in range(args.num_tests):
        all_results = []
        total_non_matches = 0

        for input_file in input_files:
            file_results, file_non_matches = await process_input_file(
                args.model_keyword, input_file, args.concurrency, args.max_tokens
            )
            all_results.extend(file_results)
            total_non_matches += file_non_matches

        # Calculate mean token count
        valid_token_counts = [
            result["token_count"] for result in all_results if result["token_count"] > 0
        ]
        mean_token_count = (
            sum(valid_token_counts) / len(valid_token_counts) if valid_token_counts else 0
        )

        output_file = f"{args.output}/test_{test_idx}.json"
        with open(output_file, "w") as file:
            json.dump(all_results, file, indent=4)

        print(f"Test {test_idx}: Invalid answers: {total_non_matches}")
        # print(f"Test {test_idx}: Mean full_output token count: {mean_token_count:.2f} tokens")


if __name__ == "__main__":
    asyncio.run(main())
