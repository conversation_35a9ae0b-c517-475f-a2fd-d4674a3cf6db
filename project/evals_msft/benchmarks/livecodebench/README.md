# LiveCodeBench Evaluation

## Contents
This document explains how to set up and run the [LiveCodeBench](https://github.com/LiveCodeBench/LiveCodeBench) evaluation benchmark in Orange.

## Setup

- Check out the `main` branch of the `glass` repository.
  - For more information about onboarding to the `glass` repository, see [that page](https://dev.azure.com/project-argos/Mimco/_git/glass?path=/README.md&_a=preview).

- Use the `orange/main` branch for torchflow-mirror. This benchmark has been last tested on 2025-08-14 with this branch (commit id `b5aca948cef562d95e584e2756bab8af4f0b61d8`)

- Set up the external LiveCodeBench repository:
  ```bash
  git clone https://github.com/LiveCodeBench/LiveCodeBench.git ~/code/LiveCodeBench
  cd ~/code/LiveCodeBench
  git switch --detach 28fef95ea8c9f7a547c8329f2cd3d32b92c1fa24
  ```
  Add it to your `~/rcall_config.py` file so it can be synced to your dev box:
  ```python
  CODE_INCLUDE_PATHS = [
    ...
    "LiveCodeBench",
  ]
  ```

- Start a _CPU_ dev box in your favorite southcentralus cluster with something like the following:
  ```bash
  b create --cluster=<your-favorite-cluster> --num-gpu=0 --num-cpu=8 --name=<your-alias>-devbox-8cpu --priority-class team-critical --team team-moonfire-genaicore
  ```
- **On the dev box**, run the `init.sh` script to bring in the evaluation data.
  ```bash
  bash ~/code/glass/project/evals_msft/benchmarks/livecodebench/init.sh
  ```


## Usage

_Everything below happens in your dev box._

To run the benchmark, you will need to use the [run_benchmark.sh](./run_benchmark.sh) script. This script will take care of running the benchmark and generating the results. It will sample and evaluate v5 and v6 data in livecodebench (test data released in 2025). It will sample 5 times. Then it will compute pass@1 5 times and take the mean pass@1 results.

Example usage:
```bash
cd ~/code/glass/project/evals_msft/benchmarks/livecodebench
bash run_benchmark.sh --model-keyword gpt5-mini
```

To control which model to evaluate, adjust the value of the `model-keyword` argument. Acceptable values and corresponding model path, renderer and bus topic name are stored in [bus_config.json](../multi_swe_bench/bus_config.json). If you're evaluating a new model, you might have to update the config and add the corresponding bus.

All outputs will be written to the following folder: `~/lcb_outputs/{model-keyword}_{timestamp}`, which will be uploaded to `orngscuscresco/data/evals_msft/benchmarks/lcb/runs/`.

- The script will first run inference using the evaluation data and a bus. The inference results will be named `test_{N}.json`. The standard output will be captured in `inference_stdout.log`.
- Then, the inference results from the previous step will be evaluated one by one. The standard outputs of each evaluation will be in `evaluation_{N}_stdout.log`.
- Finally, the results will be displayed in the standard output, and also captured in `final_results.txt`. 


## Notes
- _If you notice any issues with this README, please update it or reach out to thopo@._