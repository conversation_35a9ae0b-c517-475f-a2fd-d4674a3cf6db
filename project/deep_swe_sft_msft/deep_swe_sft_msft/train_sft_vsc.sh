set -ex

oaipkg installoai mini
bash run_before_mini_sft.sh # hot fix for sft
beam start

# wandb login --relogin# Run this on devbox if setting these don't help.
# export WANDB_BASE_URL="https://msaip.wandb.io"
# export WANDB_API_KEY="****************************************"

# Container location
export DATASET_CONTAINER="orngscuscresco"


# gpt5-mini
# CKPT="az://orngscuscresco/models/snapshots/zen-os4-nv4-cbv6-hh-rkld-4jul-orca-2-2025-07-10-00-04-decrypted" #gpt5-mini-07
CKPT="az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" # gpt5-mini-08

RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"

N_CTX=131072
# N_CTX=65536

dt=`date '+%Y%m%d-%H%M%S'`
NAME="swe_vsc_workflow_sft_debugjulyfi_gpt5mini_b128_lr1e-5-run3"

CMD=(
beam python -m mini.finetune.finetune2
name=$NAME

use_shmsnap=False
base_model.snapshot=scallion_lpe:d36

base_model.layout=finetune-80g-emb
base_model.ml_config="twberry.scallion_lpe.finetune enable_slow_replica_detection=False enable_tensorcache_v2=True ignore_parent_dataset_state=True"

base_model.local_cache_base=/tmp/tensorcachey
base_model.n_gpus=auto
base_model.n_ctx=$N_CTX
base_model.initial_checkpoint=$CKPT
root_config=twberry.root
dataset=mini.finetune.datasets.glob_dataset:BerryHarmonyGlobDataset
dataset.batch_size=128

...is_multimodal=True
...encoding_name=orion_200k
...renderer_name=$RENDERER_NAME
# this part is the data mix for SFT that needs to be changed
dataset.globs.0.path="az://orngscuscresco/data/yifangchen/swe/swe_vsc/rrb_ml_py_ts_js_v5/py_data.jsonl" #1491
dataset.globs.1.path="az://orngscuscresco/data/yifangchen/swe/swe_vsc/rrb_ml_py_ts_js_v5/nopy_data.jsonl" #777
dataset.globs.2.path="az://orngscuscresco/data/yifangchen/swe/swe_vsc/swe_bench_v2/part1.jsonl" #144
...postprocessors.0=deep_swe_sft_msft.postprocessors_vsc:MaybeReformatDataPoint
...postprocessors.1=mini.finetune.datasets.postprocessors.postprocess:LoadConvoPostprocess
...postprocessors.2=mini.finetune.datasets.postprocessors.postprocess:WeightNonAssistantMessagesPostprocess
...postprocessors.3=mini.finetune.datasets.postprocessors.postprocess:RemoveConfidencePostprocess
...postprocessors.4=mini.finetune.datasets.postprocessors.postprocess:EnsureEndTurn
# ...postprocessors.5=mini.finetune.datasets.postprocessors.postprocess:AdjustBudgetPostprocess
# ...postprocessors.5.max_num_yields=512
# ...postprocessors.5.max_token_budget=131072

dataset.bypass_exceptions=True
dataset.shuffle=True
duration="3 epochs"
between_saves="12 steps"
timeout.default=86400
hyperparam_manager=mini.finetune.hyperparam_managers.const:ConstLRHyperparamManager
hyperparam_manager.opt_config.lr_per_sample_d16=1e-5
# IMPORTANT: this should be 10% of the total number of rows in the dataset!!
hyperparam_manager.opt_config.ema_horizon=220
hyperparam_manager.warmup_samples=128

# Logging/misc
security_profile=msft-orng
github_upload=False
git_guard=False
wandb_enable=True
wandb.wandb_project=swe-vsc-sft
kafka_enable=False
use_shmsnap=False
load.restore_from_all_clusters=False
)

"${CMD[@]}" 2>&1 | tee "$NAME".log