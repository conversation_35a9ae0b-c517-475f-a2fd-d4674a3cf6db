"""
No-stage data collection system - plain rollout without stage-based approach.

This file provides a simplified approach that directly solves problems without
breaking them into stages, but still applies the same quality graders at the end.
"""

import asyncio
import copy
import json
import os
import random
import subprocess
import traceback
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, TypedDict
from uuid import uuid4

import structlog

# Test parsing functions
from berry_rfs.mrfs_eval_utils import passes_tests as test_parsing_py
from berry_rfs.mrfs_eval_utils import try_run_command
from bus_token_completer import BusTokenCompleter
from caas import ExecError
from caas.commands import Exec, RawBashScript, UploadFile
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from chat import Role, chat
from chat.render.renderer_registry import get_renderer
from chat.tools import DefaultMultiToolKit, take_one_step_with_tools

# Import conversation initialization function
from deep_swe_msft.swe_bench_train_v2_vsc.data_customization.conversation_init import (
    conversation_init_fn,
)
from deep_swe_msft.swe_bench_train_v2_vsc.graders.test_parsers import (
    PRECOMMIT_PREFIX,
    parse_python_test_results,
    parse_test_results,
    parse_typescript_test_results,
)
from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn_internal

# Import with fallbacks for specialized tools
from deep_swe_msft.tools.caas_vscode_tool import VSCodeTool
from deep_swe_msft.tools.vscode_copilot_tool import VSC_MNT, new_container

# Import VSCode system prompt
from deep_swe_msft.vsc_data.system_prompt import VSC_MODEL_IDENTITY, VSC_SYSTEM_PROMPT

# Constants and configuration
from deep_swe_sft_msft.data_generation_vsc.constants import (
    BUS_TOPIC,
    BUS_USER,
    CAAS_ENDPOINT,
    STUDENT_RENDERER,
    VSC_TOOL_ERROR_MSGS,
)

# Import the constraint extension function from utilities
from deep_swe_sft_msft.data_generation_vsc.constraint_utils import get_render_constrained_extension
from deep_swe_sft_msft.data_generation_vsc.corrector import (
    Corrector,
    has_run_in_terminal_discouraged_commands,
    has_runTests_empty_files_array,
    has_tool_error,
)

# Grading components
from deep_swe_sft_msft.data_generation_vsc.grading import (
    SWE_BEHAVIOR_RUBRIC,
    ConversationGrader,
    EfficientReadingGrader,
)
from deep_swe_tasks import DeepSWETaskMetadata, SWEBenchV2Task
from message_completer.message_completer import ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter

# Import metrics for initialization
from mini.metrics import capture_metrics

TEST_OUTPUT_MAX = 262_144

logger = structlog.stdlib.get_logger(component=__name__)

# SWE Behavior Requirements
SWE_BEHAVIOR = """<SWE_REQUIREMENTS_MSG>
IMPORTANT: Follow these SWE (Software Engineering) requirements while working:

1. **Forbidden Command Avoidance**: Never use discouraged terminal commands. Instead use appropriate VSCode tool alternatives:
- Instead of `ls directory` → use `list_dir` tool
- Instead of `cat file` or `head/tail file` → use `read_file` tool  
- Instead of `grep pattern file` → use `grep_search` tool (exact/regex) or `semantic_search` tool (natural language)
- Instead of `find -name pattern` → use `file_search` tool with glob patterns OR `test_search` tool for test files
- Instead of `pytest` or `bash run_tests.sh` → use `runTests` tool
- Instead of `sed/awk` commands for editing → use `apply_patch` tool with V4A diff format
- Instead of `mv`, `cp`, `rm` → use appropriate VSCode tools
- Avoid: `grep`, `find`, `ls`, `sed`, `cat`, `bash`, `oai`, `pytest`, `python -m pytest`, `run_tests.sh`

2. **Proper runTests Usage**: Always provide specific test files to the `runTests` tool:
- NEVER use missing files parameter: `{}`
- AVOID empty files array: `{"files": []}` (runs all tests, inefficient, may timeout)
- ALWAYS provide specific test files: `{"files": ["/root/code/tests/test_feature.py", "/root/code/tests/test_integration.py"]}`
- Use file discovery tools (`file_search`, `list_dir`) to find test files before calling `runTests`

3. **Efficient File Reading (No Repeated Reading)**: Avoid inefficient repeated reading patterns:
- PROHIBITED: Multiple reads of the same file with overlapping line ranges (unless justified by new context or verification needs)
- Line ranges calculated as [offset, offset+limit-1] with defaults offset=1 (offset always > 0, minimum 1), limit=2000
- PROHIBITED: Reading a file again immediately after a previous read
- PROHIBITED: Reading large portions when only specific sections needed
- PROHIBITED: Overlap includes identical, partial, superset, or subset ranges between multiple read_file calls
- PROHIBITED: 0 or negative offset
- REQUIRED: Use strategic reading with appropriate `offset` and `limit` parameters to avoid overlap
- REQUIRED: Use `grep_search` or `semantic_search` for finding specific content instead of reading entire files
- Acceptable exceptions: Re-examining code after gathering new context, verifying changes, or when earlier context was lost
- Acceptable exceptions: Re-examining code after gathering new context, verifying changes, or when earlier context was lost

4. **Path Handling Requirements**: Use correct absolute paths within the workspace:
- CORRECT: Use absolute paths starting with `/root/code` (workspace root)
- INCORRECT: Avoid relative paths (`.`, `..`) that cause "outside of workspace" errors
- Examples: `/root/code`, `/root/code/src`, `/root/code/tests/test_file.py`

5. **Test Verification After Changes**: Consider running tests after implementing code changes:
- SUGGESTED: Use `runTests` tool at least once after applying one or multiple code changes (unless the model is very confident in its change)
- Verify that changes don't break existing functionality
- Ensure tests pass before considering the task complete
- Use specific test files related to the changes when possible

Follow these requirements strictly to ensure efficient and proper development workflow.

General Workflow:
1. **Plan**: Identify relevant tools and files needed for the task
2. **Explore**: Use `file_search`, `list_dir`, or `semantic_search` to understand the codebase
3. **Read**: Use `read_file` strategically to examine specific files and sections
4. **Test First**: Create or run existing tests to understand current behavior
5. **Implement**: Make focused changes using appropriate editing tools
6. **Verify**: Build/run code iteratively to catch issues early
7. **Test Again**: Ensure tests pass and functionality works as expected

<SWE_REQUIREMENTS_MSG>
"""

systemMessage = VSC_MODEL_IDENTITY + VSC_SYSTEM_PROMPT

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================


class ContainerDeathException(Exception):
    """Exception raised when container dies and needs to be reset"""

    pass


class GradeReport(TypedDict):
    passed: bool
    model_patch: str
    test_output: str
    passed_tests: list[str]


async def _upload_and_apply(
    session: TerminalSession,
    patch: str,
    workdir: str,
) -> None:
    tmpfile = "/tmp/" + uuid4().hex
    await session.session.run(UploadFile(path=tmpfile, data=patch.encode()))
    await session.session.run(Exec(["git", "apply", "-v", tmpfile], workdir=workdir, timeout=60))


async def _grade_fn_v2_inner(
    grade_session: TerminalSession,
    metadata: DeepSWETaskMetadata,
    lang: str | None,
) -> GradeReport:
    assert isinstance(metadata.task, SWEBenchV2Task)

    # # Apply model test changes, verify that it fails
    # try:
    #     await _upload_and_apply(grade_session, model_patch.test_patch, metadata.cwd)
    #     exit_code, output = await grade_session.session.run(
    #         RawExec(["pytest", *model_patch.test_files], workdir=metadata.cwd, timeout=900)
    #     )
    #     if exit_code == 0:
    #         return {
    #             "passed": False,
    #             "model_patch": model_patch.compact_patch,
    #             "test_output": "Model test is not a regression test: "
    #             + output.decode(errors="ignore"),
    #             "passed_tests": [],
    #         }
    # except ExecError as e:
    #     return {
    #         "passed": False,
    #         "model_patch": model_patch.compact_patch,
    #         "test_output": "Error running model test: " + e.output.decode(errors="ignore"),
    #         "passed_tests": [],
    #     }

    # # Assert that the model real patch works
    try:
        # await run_with_retries(
        #     grade_session,
        #     f"cd {metadata.cwd} && git add -A && git reset --hard",
        #     attempts=3,
        # )
        # await _upload_and_apply(grade_session, model_patch.non_test_patch, metadata.cwd)
        # This should be non-conflicting since we filtered to non-test changes.
        await _upload_and_apply(grade_session, metadata.task.test_patch, metadata.cwd)
    except ExecError as e:
        print(f"🔧 Error applying model patch: {e}")
        return {
            "passed": False,
            "precommit_ok": False,
            "required_tests_pass": False,
            # "model_patch": model_patch.compact_patch,
            "test_output": "Error testing model patch: " + e.output.decode(errors="ignore"),
            "passed_tests": [],
        }

    if lang == "typescript":
        # Run eval script
        _eval_code, eval_output = await grade_session.session.run(
            RawBashScript(metadata.test_script, login=False, timeout=900)
        )
        _, eval_output = await grade_session.session.run(
            RawBashScript(f"cd {metadata.cwd}; cat report.json", login=False, timeout=900)
        )
        # with open('/var/log/supervisor/yunshengli7_ts.diff', 'a') as f:
        #     f.write("\n\n-------------------------------\n\n" + eval_output.decode(errors="ignore") + "\n")
        eval_output = eval_output.decode(errors="ignore")
        # Parse test output to ensure that all expected tests are passing
        passed, failed = parse_typescript_test_results(eval_output, metadata.cwd)
    elif lang == "python":
        # Run eval script
        _eval_code, eval_output = await grade_session.session.run(
            RawBashScript(
                f"export OMP_NUM_THREADS=8; export OPENBLAS_NUM_THREADS=8; export MKL_NUM_THREADS=8; {metadata.test_script}",
                login=False,
                timeout=900,
            )
        )
        with open("/var/log/supervisor/yunshengli7_py.diff", "a") as f:
            f.write(
                "\n\n-------------------------------\n\n"
                + eval_output.decode(errors="ignore")
                + "\n"
            )
        eval_output = eval_output.decode(errors="ignore")

        # Parse test output to ensure that all expected tests are passing
        passed, failed = parse_test_results(eval_output)
    else:
        _eval_code, eval_output = await grade_session.session.run(
            RawBashScript(metadata.test_script, login=True, timeout=900)
        )
        eval_output = eval_output.decode(errors="ignore")

        # Parse test output to ensure that all expected tests are passing
        passed, failed = parse_test_results(eval_output)
    precommit_fail = {t for t in failed if t.startswith(PRECOMMIT_PREFIX)}

    required_passing_tests = set(metadata.task.PASS_TO_PASS) | set(metadata.task.FAIL_TO_PASS)
    # Any precommit hook that failed in the solution is ok to fail here.
    precommit_ok = not (precommit_fail - set(metadata.task.PRECOMMIT_FAIL))
    # All required tests must pass.
    required_tests_pass = required_passing_tests.issubset(set(passed))

    if not required_tests_pass:
        eval_output = (
            f"Required tests failed: {required_passing_tests - set(passed)}\n\n{eval_output}"
        )
    if not precommit_ok:
        eval_output = f"Precommit hooks failed: {precommit_fail}\n\n{eval_output}"
    if len(eval_output) > TEST_OUTPUT_MAX:
        truncated_chars = len(eval_output) - TEST_OUTPUT_MAX
        eval_output = (
            eval_output[: TEST_OUTPUT_MAX // 2]
            + f"\n(...{truncated_chars} characters truncated...)\n"
            + eval_output[-TEST_OUTPUT_MAX // 2 :]
        )

    with open("/var/log/supervisor/yunshengli7_result.jsonl", "a") as f:
        result = {
            "passed": precommit_ok and required_tests_pass,
            "test_output": eval_output,
            "passed_tests": list(passed),
        }
        f.write(json.dumps(result) + "\n")

    return {
        "passed": precommit_ok and required_tests_pass,
        "precommit_ok": precommit_ok,
        "required_tests_pass": required_tests_pass,
        "test_output": eval_output,
        "passed_tests": list(passed),
    }


def log_container_failure(
    instance_id: str,
    step: int,
    error_content: str,
    outputs_dirname: str = "outputs",
    step_student_messages: List = None,
    error_msg=None,
    container_attempt: int = 1,
) -> None:
    """Log container failure with comprehensive tool execution context"""
    timestamp = datetime.now().isoformat()

    # Extract tool call information from student messages and error message
    tool_call_info = "Unknown tool"
    if step_student_messages:
        last_student_msg = step_student_messages[-1]
        if hasattr(last_student_msg, "recipient") and last_student_msg.recipient:
            tool_call_info = f"Tool: {last_student_msg.recipient}"
        # Try to extract more details from message content
        if hasattr(last_student_msg, "content"):
            content_str = str(last_student_msg.content)
            if content_str and len(content_str) > 0:
                # Extract first line or first 100 chars as tool call summary
                first_line = content_str.split("\n")[0][:100]
                tool_call_info += f" | Call: {first_line}"

    # Also check if error message has tool call ID
    if error_msg and hasattr(error_msg, "tool_call_id") and error_msg.tool_call_id:
        tool_call_info += f" | ID: {error_msg.tool_call_id}"

    log_entry = {
        "timestamp": timestamp,
        "instance_id": instance_id,
        "container_attempt": container_attempt,
        "step": step,
        "error_content": error_content,
        "tool_call_info": tool_call_info,
        "failure_type": "container_death",
    }

    # Create log directory
    os.makedirs(outputs_dirname, exist_ok=True)
    log_file = os.path.join(outputs_dirname, "container_failures.jsonl")

    # Append to log file
    with open(log_file, "a") as f:
        f.write(json.dumps(log_entry, default=str) + "\n")

    print(
        f"📝 CONTAINER FAILURE LOGGED: {instance_id} at step {step} (container attempt {container_attempt}), tool: {tool_call_info}, logged to {log_file}"
    )


def write_to_file_text(text: str, file_name: str = "aaa", folder: str = "log") -> None:
    """Write text to a file"""
    os.makedirs(folder, exist_ok=True)
    with open(os.path.join(folder, file_name), "w") as f:
        f.write(text)


def _categorize_error(error_details: str, outputs_dirname: str = "outputs") -> str:
    """Categorize error based on error message content"""
    error_lower = error_details.lower()
    
    # Network/connection errors
    if any(term in error_lower for term in ["connection", "network", "timeout", "socket"]):
        return "Network"
    
    # Memory errors
    if any(term in error_lower for term in ["memory", "oom", "out of memory"]):
        return "Memory"
    
    # JSON/Parsing errors
    if any(term in error_lower for term in ["json", "parse", "decode", "invalid json"]):
        return "Parsing"
    
    # Tool call errors
    if any(term in error_lower for term in ["tool", "function call", "invalid function"]):
        return "ToolCall"
    
    # File I/O errors
    if any(term in error_lower for term in ["file", "directory", "permission", "no such file"]):
        return "FileIO"
    
    # Python runtime errors
    if any(term in error_lower for term in ["attributeerror", "typeerror", "valueerror", "keyerror"]):
        return "Runtime"
    
    # Import/module errors
    if any(term in error_lower for term in ["import", "module", "modulenotfound"]):
        return "Import"
    
    # Default category for unknown errors - log the details for debugging
    # print(f"🔍 UNKNOWN ERROR DETECTED: {error_details}")
    
    # Also log to file for persistent tracking
    import os
    from datetime import datetime
    os.makedirs(outputs_dirname, exist_ok=True)
    log_file = os.path.join(outputs_dirname, "other_error.log")
    
    timestamp = datetime.now().isoformat()
    with open(log_file, "a") as f:
        f.write(f"[{timestamp}] UNKNOWN ERROR: {error_details}\n")
    
    return "Unknown"


def update_attempt_counters(outputs_dirname: str, status: str, failed_criteria: list = None, error_details: str = None):
    """Update simple counters in progress_details.txt"""
    counters_file = os.path.join(outputs_dirname, "progress_details.txt")
    
    
    # Initialize counters if file doesn't exist
    if not os.path.exists(counters_file):
        with open(counters_file, "w") as f:
            f.write("=== RETRY ATTEMPT TRACKING ===\n")
            f.write("Total attempts: 0\n")
            f.write("Success: 0\n")
            f.write("Tests failed: 0\n")
            f.write("Conversation failed: 0\n")
            f.write("Container errors: 0\n")
            f.write("Other errors: 0\n")
            f.write("\n=== CRITERION FAILURE COUNTS ===\n")
            f.write("Fail in criterion 1: 0\n")
            f.write("Fail in criterion 2: 0\n")
            f.write("Fail in criterion 3: 0\n")
            f.write("Fail in criterion 4: 0\n")
            f.write("\n=== DYNAMIC ERROR TRACKING ===\n")

    
    # Read current counters
    with open(counters_file, "r") as f:
        lines = f.readlines()
    
    # Handle dynamic error tracking for "other_error" types
    if "other_error" in status and error_details:
        error_type = _categorize_error(error_details, outputs_dirname)
        error_label = f"Error - {error_type}:"
        
        # Check if this error type already exists
        error_found = False
        for i, line in enumerate(lines):
            if line.startswith(error_label):
                current = int(line.split(":")[1].strip())
                lines[i] = f"{error_label} {current + 1}\n"
                error_found = True
                break
        
        # If error type not found, add it to the dynamic section
        if not error_found:
            # Find the dynamic section and add new error type
            for i, line in enumerate(lines):
                if line.startswith("=== DYNAMIC ERROR TRACKING ==="):
                    lines.insert(i + 1, f"{error_label} 1\n")
                    break
    
    # Update counters
    for i, line in enumerate(lines):
        if line.startswith("Total attempts:"):
            current = int(line.split(":")[1].strip())
            lines[i] = f"Total attempts: {current + 1}\n"
            # print(f"🔧 DEBUG: Updated total attempts to {current + 1}")
        elif status == "success" and line.startswith("Success:"):
            current = int(line.split(":")[1].strip())
            lines[i] = f"Success: {current + 1}\n"
            # print(f"🔧 DEBUG: Updated success to {current + 1}")
        elif status == "tests_failed" and line.startswith("Tests failed:"):
            current = int(line.split(":")[1].strip())
            lines[i] = f"Tests failed: {current + 1}\n"
            # print(f"🔧 DEBUG: Updated tests failed to {current + 1}")
        elif status == "conversation_failed" and line.startswith("Conversation failed:"):
            current = int(line.split(":")[1].strip())
            lines[i] = f"Conversation failed: {current + 1}\n"
            # print(f"🔧 DEBUG: Updated conversation failed to {current + 1}")
        elif "container_error" in status and line.startswith("Container errors:"):
            current = int(line.split(":")[1].strip())
            lines[i] = f"Container errors: {current + 1}\n"
            # print(f"🔧 DEBUG: Updated container errors to {current + 1}")
        elif "other_error" in status and line.startswith("Other errors:"):
            current = int(line.split(":")[1].strip())
            lines[i] = f"Other errors: {current + 1}\n"
            # print(f"🔧 DEBUG: Updated other errors to {current + 1}")

    # Update criterion failure counts if provided
    if failed_criteria:
        for criterion in failed_criteria:
            criterion_name = criterion.get('criterion', '')
            # Extract criterion number from name (e.g., "criterion1" -> "1" or "criterion_1" -> "1")
            criterion_num = None
            if 'criterion' in criterion_name:
                try:
                    # Handle both "criterion1" and "criterion_1" formats
                    if 'criterion_' in criterion_name:
                        criterion_num = criterion_name.split('_')[1]
                    else:
                        # Extract number from "criterion1", "criterion2", etc.
                        criterion_num = criterion_name.replace('criterion', '')
                    
                    if criterion_num:
                        criterion_line = f"Fail in criterion {criterion_num}:"
                        
                        for i, line in enumerate(lines):
                            if line.startswith(criterion_line):
                                current = int(line.split(":")[1].strip())
                                lines[i] = f"Fail in criterion {criterion_num}: {current + 1}\n"
                                break
                except (IndexError, ValueError):
                    continue
    
    # Write back updated counters
    with open(counters_file, "w") as f:
        f.writelines(lines)


def load_from_jsonl(jsonl_path: str) -> List[Dict]:
    """Load dataset from a jsonl file"""
    dataset = []
    with open(jsonl_path, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if line:  # Skip empty lines
                try:
                    data = json.loads(line)
                    dataset.append(data)
                except json.JSONDecodeError as e:
                    print(f"Error parsing line in {jsonl_path}: {e}")
                    continue
    return dataset


def load_processed_instances(all_processed_file: str) -> set:
    """Load set of already processed instance IDs"""
    processed_ids = set()
    if os.path.exists(all_processed_file):
        try:
            with open(all_processed_file, "r", encoding="utf-8") as f:
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            data = json.loads(line)
                            if "instance_id" in data:
                                processed_ids.add(data["instance_id"])
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"Warning: Could not load processed instances from {all_processed_file}: {e}")
    return processed_ids


def filter_unprocessed_instances(dataset: List[Dict], processed_ids: set) -> tuple[List[Dict], int]:
    """Filter out already processed instances"""
    unprocessed = []
    skipped_count = 0

    for instance in dataset:
        instance_id = instance.get("unique_id", instance.get("instance_id", "unknown"))
        if instance_id not in processed_ids:
            unprocessed.append(instance)
        else:
            skipped_count += 1

    return unprocessed, skipped_count


def process_swb_problem_statement(instance) -> tuple[str, str]:
    """Extract problem statement and return both statement and repo path (original version)"""
    # Find the position of "until the tests pass" and extract everything before it
    # repo_name = clean_up_repo_name(instance['metadata'].get("repo_name"))
    # root_repo = get_repo_directory(repo_name)
    # TODO: There is something wrong with the repo path in the original problem statement, so we use the hardcoded root_repo
    root_repo = "/root/code"
    problem_statement_list = conversation_init_fn(datapoint=instance)
    return problem_statement_list, root_repo


def clean_swe_requirements(conversation: chat.Conversation) -> chat.Conversation:
    """
    Remove the SWE requirements message from a conversation before saving to JSONL.

    The SWE requirements message is only needed during training to guide the model,
    but should not be part of the final training data.

    Args:
        conversation: The conversation to clean

    Returns:
        A new conversation with the SWE requirements message removed
    """
    # Since there's only one SWE requirements message, we can find and remove it efficiently
    cleaned_messages = []
    swe_msg_found = False

    for message in conversation.messages:
        # Skip the single SWE requirements message when found
        is_swe_msg = False
        if not swe_msg_found and message.author.role == Role.USER and message.content:

            # Handle different content structures
            content_text = ""
            if isinstance(message.content, str):
                content_text = message.content
            elif hasattr(message.content, "parts") and message.content.parts:
                # Handle content with parts array (typical chat format)
                content_text = str(message.content.parts[0]) if message.content.parts else ""
            elif hasattr(message.content, "__str__"):
                content_text = str(message.content)

            if "<SWE_REQUIREMENTS_MSG>" in content_text:
                is_swe_msg = True
                swe_msg_found = True

        if not is_swe_msg:
            cleaned_messages.append(message)

    # Create new conversation with cleaned messages
    cleaned_conversation = chat.Conversation(messages=cleaned_messages)

    # Preserve metadata from original conversation
    if hasattr(conversation, "metadata"):
        cleaned_conversation.metadata = conversation.metadata

    return cleaned_conversation


# ============================================================================
# NO-STAGE ROLLOUT FUNCTIONS
# ============================================================================


async def nostage_rollout(
    instance,
    student_juice: int = 1024,
    max_steps: int = 150,
    outputs_dirname: str = "outputs",
    debug_mode: bool = True,
    filter_pass_only: bool = False,
    retries: int = 5,
) -> Optional[Dict]:
    """
    Plain rollout without stages - just direct problem solving
    """
    data_dict = {}
    # dataset: rrb
    metadata = instance["metadata"]
    instance_id = instance["unique_id"]

    # Problem statement list return both workspace info and user request
    problem_statement_list, root_repo = process_swb_problem_statement(instance)

    docker_image = instance["metadata"]["docker_image"]

    lang = metadata.get("lang", "python")
    metadata = DeepSWETaskMetadata.model_validate(metadata)

    # Initialize Final Graders
    final_rollout_grader = ConversationGrader()
    # reading_grader = EfficientReadingGrader()  # Commented out - replaced with detailed criterion tracking

    container = None

    for attempt in range(retries):
        try:
            # Build CaaS Container
            container = await CaasContainer.new(
                caas_endpoint=CAAS_ENDPOINT,
                image_name=docker_image,
                idle_ttl=1200,
                memory_limit="16g",
                cpu_limit="16",
                disk_limit="32g",
                volume_mounts=VSC_MNT,
            )
            caas_session = container.caas_session

            # Start keepalive task to prevent container from dying
            # Optional: comment out because it is not that effective in this case.
            # caas_session.start_keepalive_task(keepalive_interval=30.0)

            terminal_session = TerminalSession(caas_session, endpoint=CAAS_ENDPOINT)

            # Setup repository and coreutils using VSCode approach based on language
            print(f"Starting setting up repo and coreutils for {instance_id} (language: {lang})")

            # Setup repository using the coreutils functions (which already call setup_repo_with_gt_metadata internally)
            with capture_metrics(step=0):
                await swe_bench_v2_setup_fn_internal(
                    terminal_session=terminal_session, language=lang, metadata=metadata
                )

            print(f"Finished setting up repo and coreutils for {instance_id}")

            # Set up tools
            tool = VSCodeTool(
                container=container,
                terminal_emulator_path="teammate_service.termites.lean_terminal:LeanTerminal",
            )
            toolkit = DefaultMultiToolKit(tools=[tool])
            tools_section = {tool.name: tool.instruction()}

            # Set up models
            renderer_name = STUDENT_RENDERER
            renderer = get_renderer(renderer_name)
            extension = get_render_constrained_extension(renderer_name)

            # Initialize corrector model
            corrector = Corrector(STUDENT_RENDERER, retries=retries)

            # Student model configuration
            student_bus_config = BusTokenCompleter.Config(
                topic_mode_or_user=BUS_USER,
                topic_or_snapshot=BUS_TOPIC,
            )

            completion_params = {"temperature": 1}
            if extension is not None:
                completion_params["extensions"] = [extension]

            student_message_completer_config = TokenMessageCompleter.Config(
                token_completer_config=student_bus_config,
                completion_params=completion_params,
                renderer=renderer,
            )

            student_message_completer = student_message_completer_config.build()

            # Initialize conversation with problem statement using proper conversation_init_fn
            system_msg = chat.Message.system(
                model_identity_desc=systemMessage,
                tools_section=tools_section,
                channel_config=chat.SystemChannelConfig(
                    valid_channels=("analysis", "final"), channel_required=True
                ),
                metadata=chat.SystemContentMetadata(reward_multiplier=student_juice),
            )

            # Add SWE requirements message
            swe_requirements_msg = chat.Message.user(SWE_BEHAVIOR)

            base_convo = chat.Conversation(
                messages=[
                    system_msg,
                    swe_requirements_msg,
                    *problem_statement_list,
                ]
            )

            # Set up budget for the conversation
            base_convo.metadata.header_yields_budget_total_override = max_steps
            base_convo.metadata.header_yields_budget_for_action = max_steps

            valid_nontool_recipients = {
                m.author.display_name() for m in base_convo.messages if m.author.role != Role.TOOL
            }
            valid_recipients = (
                valid_nontool_recipients
                | ({tool.name for tool in toolkit.tools} if toolkit else set())
                | {"all"}
            )

            # Execute plain rollout without stages
            print(f"\n{'='*60}")
            print(f"Starting Plain Rollout: {instance_id} (max_steps: {max_steps})")
            print(f"{'='*60}")

            # Execute conversation directly
            current_convo = base_convo
            student_messages = []

            for step in range(max_steps):
                is_last_step = step == max_steps - 1

                # Execute single step with corrector
                (
                    step_student_messages,
                    step_tool_messages,
                    should_continue,
                ) = await execute_single_step(
                    student_message_completer,
                    current_convo,
                    toolkit,
                    valid_recipients,
                    retries,
                    is_last_step,
                    corrector=corrector,
                    instance_id=instance_id,
                    step=step,
                    outputs_dirname=outputs_dirname,
                    container_attempt=attempt + 1,
                )

                if not step_student_messages:
                    break

                # Apply corrector for tool errors AFTER tool execution if provided
                if step_tool_messages:

                    # First, check for container death in ALL tool messages (not just error messages)
                    # This ensures we catch container death regardless of error classification
                    for tool_msg in step_tool_messages:
                        tool_content = str(tool_msg.content)
                        if (
                            "Container is no longer alive" in tool_content
                            or "Caas client error on exec" in tool_content
                        ):
                            # Log the container failure with all context
                            if instance_id is not None and step is not None:
                                log_container_failure(
                                    instance_id,
                                    step,
                                    tool_content,
                                    outputs_dirname,
                                    step_student_messages,
                                    tool_msg,
                                    attempt,
                                )
                            raise ContainerDeathException(f"Container died: {tool_content}")

                    # Check if there are any tool errors
                    error_tool_messages = [msg for msg in step_tool_messages if has_tool_error(msg)]

                    if corrector and error_tool_messages:
                        print(
                            f"Found {len(error_tool_messages)} tool errors, attempting correction after tool execution..."
                        )

                        correction_result = await corrector.correct_step_if_needed(
                            step_student_messages,
                            error_tool_messages,
                            current_convo,
                            toolkit,
                            valid_recipients,
                            "tool_error",
                        )

                        if correction_result.success:
                            # Use corrected messages
                            step_student_messages = correction_result.corrected_student_messages
                            step_tool_messages = correction_result.corrected_tool_messages
                            print("✅ Tool errors corrected successfully")
                        else:
                            print(
                                f"⚠️ Tool error correction failed: {correction_result.error_message} -> Continue with original messages"
                            )

                student_messages.extend(step_student_messages)

                # Update conversation with student messages
                try:
                    current_convo = current_convo.with_suffix(*step_student_messages)
                except Exception as e:
                    print(f"❌ ERROR: Failed to update current_convo with student messages: {e}")
                    print(f"❌ ERROR: Exception type: {type(e)}")
                    print(f"❌ ERROR: Traceback: {traceback.format_exc()}")
                    raise e

                # Update conversation with tool messages if any
                if step_tool_messages:
                    try:
                        current_convo = current_convo.with_suffix(*step_tool_messages)
                    except Exception as e:
                        print(f"❌ ERROR: Failed to update current_convo with tool messages: {e}")
                        print(f"❌ ERROR: Exception type: {type(e)}")
                        print(f"❌ ERROR: Traceback: {traceback.format_exc()}")
                        raise e

                if not should_continue:
                    break

                # Check if we reached max steps
                if step == max_steps - 1:
                    print(f"⏰ Reached maximum steps ({max_steps}) for {instance_id}")
                    data_dict["reached_max_steps"] = True

            # Evaluate tests - use actual repository root path from setup
            test_results = await _grade_fn_v2_inner(terminal_session, metadata, lang)
            passed = test_results["passed"]
            precommit_ok = test_results["precommit_ok"]
            required_tests_pass = test_results["required_tests_pass"]
            print(
                f"Tests output: {test_results['test_output'][:200]}..."
            )  # Print first 200 chars of output
            print(f"Tests passed: {passed}")

            # Extract clean conversation (already clean since no stage prompts)
            clean_convo = current_convo
            # write_to_file_text(str(clean_convo), f"{instance_id}_nostage.txt", folder='sft_data/clean')
            # write_to_file_text(str(current_convo), f"{instance_id}_nostage.txt", folder='sft_data/original')

            # Final Grader Check
            evaluation = await final_rollout_grader.evaluate_conversation(
                clean_convo, SWE_BEHAVIOR_RUBRIC, "format_test"
            )
            is_good_conversation = evaluation.is_good_conversation
            
            # Extract failed criteria details
            failed_criteria = []
            if not is_good_conversation:
                for criterion_name, details in evaluation.criterion_scores.items():
                    if not details.get("passed", False):
                        failed_criteria.append({
                            "criterion": criterion_name,
                            "explanation": details.get("explanation", "No explanation provided")
                        })

            # Comment out EfficientReadingGrader Check - replaced with detailed criterion tracking
            # reading_evaluation = reading_grader.evaluate_conversation(clean_convo)
            # has_efficient_reading = reading_evaluation.is_good_conversation


            # Return different results based on what criteria are met
            result_data = {
                "is_good_conversation": is_good_conversation,
                "reached_max_steps": data_dict.get("reached_max_steps", False),
                "failed_criteria": failed_criteria,  # Add detailed failure information
                "criterion_scores": evaluation.criterion_scores,  # Add full criterion scores
            }

            if container:
                await container.teardown()

            final_clean_convo = clean_swe_requirements(clean_convo)

            data_dict["prompt"] = system_msg.model_dump(mode="json")
            data_dict["conversation"] = final_clean_convo.model_dump(mode="json")
            data_dict["accuracy"] = passed
            data_dict["precommit_ok"] = precommit_ok
            data_dict["required_tests_pass"] = required_tests_pass
            data_dict["unique_id"] = instance_id
            data_dict.update(result_data)

            with open(os.path.join(outputs_dirname, f"all_processed_each_try.jsonl"), "a") as f:
                f.write(json.dumps(data_dict, default=str) + "\n")

            # Only populate conversation data when tests pass and conversation is good
            if passed and is_good_conversation:
                # Log successful attempt
                update_attempt_counters(outputs_dirname, "success")
                
                # Clean the conversation by removing SWE requirements message before saving
                print("✅ Completed successfully with passing tests and good conversation quality")
                return data_dict
            elif passed and not is_good_conversation:
                # Log failed conversation quality attempt
                update_attempt_counters(outputs_dirname, "conversation_failed", failed_criteria)
                
                print("⚠️ Tests passed but: conversation quality check failed")
                if failed_criteria:
                    failed_criterion_names = [c['criterion'] for c in failed_criteria]
                    print(f"⚠️ Failed criteria: {failed_criterion_names}")
                    print(f"📊 Logging {len(failed_criteria)} failed criteria to counters")
                continue
            else:
                # Log failed tests attempt
                # print("🔧 DEBUG: Calling update_attempt_counters with 'tests_failed'")
                update_attempt_counters(outputs_dirname, "tests_failed")
                
                # Tests failed
                print("❌ Failed: tests failed")
                continue

        except Exception as e:
            # Check if this is a container-related error and provide extra logging
            is_container_error = (
                isinstance(e, ContainerDeathException)
                or "Container is no longer alive" in str(e)
                or "Caas client error" in str(e)
            )

            if is_container_error:
                print(f"🔴 CONTAINER ERROR DETECTED on attempt {attempt + 1}/{retries}")

            if attempt < retries - 1:
                # Log the retry attempt details
                if is_container_error:
                    # print("🔧 DEBUG: Calling update_attempt_counters with 'container_error'")
                    update_attempt_counters(outputs_dirname, "container_error")
                    print(
                        f"🔄 Container error - will retry attempt {attempt + 2}/{retries} after cleanup"
                    )
                else:
                    update_attempt_counters(outputs_dirname, "other_error", error_details=str(e))
                    print(f"[Other error] Rollout attempt {attempt + 1} failed: {e}")

                if container:
                    try:
                        await container.teardown()
                        if is_container_error:
                            print(f"🧹 Container cleanup completed for attempt {attempt + 1}")
                    except Exception as cleanup_error:
                        if is_container_error:
                            print(f"⚠️ Container cleanup failed: {cleanup_error}")
                        pass
                await asyncio.sleep(4)
                continue
            else:
                # Log final failure after all retries
                if is_container_error:
                    update_attempt_counters(outputs_dirname, "container_error_final")
                    print(
                        f"❌ CONTAINER ERROR: Instance {instance_id} failed after {retries} attempts due to container issues - skipping"
                    )
                    # Return a special marker for infrastructure failures
                    return {"failure_type": "infrastructure", "error_details": str(e)}
                else:
                    update_attempt_counters(outputs_dirname, "other_error_final", error_details=str(e))
                    print(f"❌ Instance {instance_id} failed after {retries} attempts - skipping")

                if container:
                    try:
                        await container.teardown()
                    except Exception:
                        pass
                return None  # Skip failed instances instead of raising exception
    return None  # If all attempts fail, return None


# ============================================================================
# CONVERSATION EXECUTION FUNCTIONS
# ============================================================================


async def execute_single_step(
    message_completer,
    current_convo,
    toolkit,
    valid_recipients,
    retries: int,
    is_last_step: bool,
    corrector=None,
    instance_id=None,
    step=None,
    outputs_dirname="outputs",
    container_attempt=1,
):
    """
    Execute a single step in the conversation.

    Args:
        message_completer: The message completer for student responses
        current_convo: Current conversation state
        toolkit: Tool kit for executing tools
        valid_recipients: Set of valid message recipients
        retries: Number of retries for operations
        is_last_step: Whether this is the last step
        corrector: Optional corrector for fixing errors and discouraged commands
        instance_id: Instance ID for logging (used with corrector)
        step: Current step number for logging (used with corrector)
        outputs_dirname: Output directory for logging (used with corrector)
        container_attempt: Container attempt number for logging (used with corrector)

    Returns:
        tuple: (student_messages_to_append, tool_messages_to_append, should_continue)
    """
    # Student generates response
    new_messages = await generate_student_response(
        message_completer, current_convo, is_last_step, retries
    )

    if not new_messages:
        return [], [], False

    # Apply corrector for student message errors (discouraged commands) BEFORE tool execution
    if corrector:
        # 1. Check for discouraged commands specifically in run_in_terminal messages
        terminal_discouraged = [
            msg for msg in new_messages if has_run_in_terminal_discouraged_commands(msg)
        ]

        # 2. Check for empty files array in runTests messages
        runtests_empty = [msg for msg in new_messages if has_runTests_empty_files_array(msg)]

        # The first error only happens in recipient = functions.run_in_terminal
        # The second error only happens in recipient = functions.runTests
        correction_case = None
        if terminal_discouraged:
            print(f"  🐛 {len(terminal_discouraged)} run_in_terminal discouraged commands")
            # Debug: Print all terminal discouraged messages
            for i, msg in enumerate(terminal_discouraged):
                print(f"🐛 terminal_discouraged[{i}]: {msg}")

            correction_case = "discouraged_command"
        elif runtests_empty:
            print(f"  🐛 {len(runtests_empty)} runTests empty files array")
            correction_case = "empty_runtests"

        if correction_case:
            print(f"Using correction case: {correction_case}")
            # Error message can be empty since in these two cases we don't need error messages
            correction_result = await corrector.correct_step_if_needed(
                new_messages, [], current_convo, toolkit, valid_recipients, correction_case
            )

            if correction_result.success:
                # Use corrected messages for subsequent tool execution
                new_messages = correction_result.corrected_student_messages
                print("✅ Student messages corrected successfully before tool execution")
            else:
                print(
                    f"⚠️ Student message correction failed: {correction_result.error_message} -> Continue with original messages"
                )

    # Create a copy of conversation with student messages for tool execution
    try:
        step_convo = current_convo.with_suffix(*new_messages)
    except Exception as e:
        print(f"❌ ERROR: Failed to create step_convo with with_suffix: {e}")
        print(f"❌ ERROR: Exception type: {type(e)}")
        print(f"❌ ERROR: Traceback: {traceback.format_exc()}")
        raise e

    last_message = new_messages[-1]

    # Check termination conditions
    should_continue = not (
        is_last_step
        or last_message.end_turn
        or (
            last_message.recipient not in valid_recipients
            and last_message.recipient.split(".")[0] not in valid_recipients
        )
    )

    tool_messages = []

    if should_continue:
        # Execute tools if needed using the copied conversation
        try:
            prefix_convo = step_convo.prefix(-1)
        except Exception as e:
            print(f"❌ ERROR: Failed to create prefix_convo: {e}")
            print(f"❌ ERROR: Exception type: {type(e)}")
            print(f"❌ ERROR: Traceback: {traceback.format_exc()}")
            raise e

        try:
            async for tool_message in take_one_step_with_tools(
                prefix_convo=prefix_convo,
                message=last_message,
                toolkit=toolkit,
            ):
                tool_messages.append(tool_message)

                # Update the step conversation for subsequent tool calls
                try:
                    if step_convo.messages[-1].id == tool_message.id:
                        step_convo.messages[-1] = tool_message
                    else:
                        step_convo = step_convo.with_suffix(tool_message)
                except Exception as e:
                    print(f"❌ ERROR during step_convo update: {e}")
                    print(f"❌ ERROR type: {type(e)}")
                    print(f"❌ ERROR: Traceback: {traceback.format_exc()}")
                    raise e
        except Exception as e:
            print(f"❌ ERROR: Failed in take_one_step_with_tools: {e}")
            print(f"❌ ERROR: Exception type: {type(e)}")
            print(f"❌ ERROR: Traceback: {traceback.format_exc()}")
            raise e

    return new_messages, tool_messages, should_continue


async def generate_student_response(message_completer, convo, is_last_step: bool, retries: int):
    """Generate student model response with retries"""

    for completion_attempt in range(retries):
        try:
            completion = await message_completer.async_completion(
                conversations=[convo], n=1, seed=0, end_header=is_last_step
            )
            choice = completion.choices[0]
            messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)

            return messages
        except Exception as e:
            print(f"❌ ERROR in generate_student_response: {e}")
            print(f"❌ ERROR type: {type(e)}")
            if completion_attempt < retries - 1:
                print(f"Student completion attempt {completion_attempt + 1} failed: {e}")
                await asyncio.sleep(1)
                continue
            else:
                print(f"Student completion failed after {retries} attempts: {e}")
                return None


async def process_instance_nostage(
    instance,
    student_juice: int = 1024,
    max_steps: int = 150,
    outputs_dirname: str = "outputs",
    debug_mode: bool = True,
    filter_pass_only: bool = False,
    retries: int = 5,
) -> Optional[Dict]:
    """Process a single instance with no-stage approach"""
    instance_id = instance.get("unique_id", instance.get("instance_id", "unknown"))
    try:
        result = await nostage_rollout(
            instance=instance,
            student_juice=student_juice,
            max_steps=max_steps,
            outputs_dirname=outputs_dirname,
            debug_mode=debug_mode,
            filter_pass_only=filter_pass_only,
            retries=retries,
        )
        return result
    except Exception as e:
        print(f"❌ Failed to process instance {instance_id}: {e}")
        return None


async def process_all_nostage(
    dataset,
    student_juice: int = 1024,
    max_steps: int = 150,
    concurrency_limit: int = 2,
    outputs_dirname: str = "outputs",
    debug_mode: bool = False,
    filter_pass_only: bool = False,
    retries: int = 5,
) -> List[Dict]:
    """Process all instances with no-stage approach"""
    semaphore = asyncio.Semaphore(concurrency_limit)
    results = []
    total_instances = len(dataset)
    processed_count = 0
    passed_count = 0
    failed_count = 0

    # Detailed counters for different failure/success categories
    reached_max_steps_count = 0
    tests_not_pass_count = 0
    infrastructure_failure_count = 0  # Container deaths, setup failures, etc.
    pass_but_not_good_conversation_count = 0
    # pass_but_not_efficient_reading_count = 0  # Removed since not using efficient reading grader

    # Create output directory and initialize files
    os.makedirs(outputs_dirname, exist_ok=True)
    data_file = f"{outputs_dirname}/data.jsonl"  # All three conditions passed
    data_file_tests_only = f"{outputs_dirname}/data_tests_pass.jsonl"  # Only tests pass
    data_file_tests_and_reading = (
        f"{outputs_dirname}/data_tests_pass_efficient_reading.jsonl"  # Tests + efficient reading
    )
    data_file_all_processed = f"{outputs_dirname}/all_processed.jsonl"  # ALL processed instances
    progress_file = f"{outputs_dirname}/progress.txt"

    async def process_with_semaphore(instance):
        nonlocal processed_count, passed_count, failed_count
        nonlocal reached_max_steps_count, tests_not_pass_count, infrastructure_failure_count, pass_but_not_good_conversation_count
        async with semaphore:
            result = await process_instance_nostage(
                instance,
                student_juice=student_juice,
                max_steps=max_steps,
                outputs_dirname=outputs_dirname,
                debug_mode=debug_mode,
                filter_pass_only=filter_pass_only,
                retries=retries,
            )

            processed_count += 1
            instance_id = instance.get("unique_id", instance.get("instance_id", "unknown"))

            # Always record the processed instance with comprehensive status information
            processed_record = {
                "instance_id": instance_id,
                "timestamp": datetime.now().isoformat(),
                "processing_order": processed_count,
                "status": "unknown",  # Will be updated below
                "accuracy": None,
                "is_good_conversation": None,
                "reached_max_steps": None,
                "failure_type": None,
                "language": instance.get("metadata", {}).get("lang", "unknown"),
            }

            if result:
                # Check if this is an infrastructure failure
                if isinstance(result, dict) and result.get("failure_type") == "infrastructure":
                    infrastructure_failure_count += 1
                    failed_count += 1
                    processed_record["status"] = "infrastructure_failure"
                    processed_record["failure_type"] = "infrastructure"
                    processed_record["accuracy"] = False
                    print(
                        f"🔴 Progress: {processed_count}/{total_instances} processed, {passed_count} perfect, {failed_count} failed (infrastructure failure)"
                    )
                else:
                    # Get the result criteria
                    accuracy = result.get("accuracy", False)
                    is_good_conversation = result.get("is_good_conversation", False)
                    reached_max_steps = result.get("reached_max_steps", False)
                    failed_criteria = result.get("failed_criteria", [])

                    # Update processed record with detailed results
                    processed_record["accuracy"] = accuracy
                    processed_record["is_good_conversation"] = is_good_conversation
                    processed_record["reached_max_steps"] = reached_max_steps
                    processed_record["failed_criteria"] = failed_criteria  # Add failed criteria tracking
                    reached_max_steps = result.get("reached_max_steps", False)

                    # Track specific failure categories
                    if reached_max_steps:
                        reached_max_steps_count += 1

                    if not accuracy:
                        tests_not_pass_count += 1
                        processed_record["status"] = "tests_failed"
                    elif accuracy and not is_good_conversation:
                        pass_but_not_good_conversation_count += 1
                        processed_record["status"] = "tests_pass_bad_conversation"

                    # Categorize and write to appropriate files
                    if accuracy and is_good_conversation:
                        # Perfect sample - write to main file (removed has_efficient_reading requirement)
                        passed_count += 1
                        processed_record["status"] = "perfect_success"
                        print(
                            f"✅ Progress: {processed_count}/{total_instances} processed, {passed_count} perfect, {failed_count} failed"
                        )

                        with open(data_file, "a") as f:
                            f.write(json.dumps(result, default=str) + "\n")
                        with open(data_file_tests_only, "a") as f:
                            f.write(json.dumps(result, default=str) + "\n")
                        with open(data_file_tests_and_reading, "a") as f:
                            f.write(json.dumps(result, default=str) + "\n")

                    elif accuracy:
                        # Only tests pass (conversation quality failed) - write to tests-only file
                        failed_count += 1  # Count as failed since not all criteria met
                        print(
                            f"⚠️ Progress: {processed_count}/{total_instances} processed, {passed_count} perfect, {failed_count} failed"
                        )

                        with open(data_file_tests_only, "a") as f:
                            f.write(json.dumps(result, default=str) + "\n")

                    else:
                        # Tests failed but we got a result - count as failed
                        failed_count += 1
                        print(
                            f"❌ Progress: {processed_count}/{total_instances} processed, {passed_count} perfect, {failed_count} failed"
                        )
            else:
                # No result returned - this means complete failure (actual test failure or other issues)
                failed_count += 1
                tests_not_pass_count += 1  # Track that tests didn't pass when result is None
                processed_record["status"] = "complete_failure"
                processed_record["accuracy"] = False
                processed_record["is_good_conversation"] = False
                processed_record["reached_max_steps"] = False
                print(
                    f"❌ Progress: {processed_count}/{total_instances} processed, {passed_count} perfect, {failed_count} failed"
                )

            # Write the comprehensive status record to all_processed.jsonl
            with open(data_file_all_processed, "a") as f:
                f.write(json.dumps(processed_record, default=str) + "\n")

            # Update progress file with current run statistics
            with open(progress_file, "w") as f:
                timestamp = datetime.now().isoformat()
                f.write(f"=== Progress Report [{timestamp}] ===\n\n")

                # Current run statistics
                f.write("CURRENT RUN:\n")
                f.write(f"Processed: {processed_count}/{total_instances}\n")
                f.write(f"Perfect (all 3 criteria): {passed_count}\n")
                f.write(f"Failed: {failed_count}\n")
                f.write(f"Success Rate (perfect): {passed_count/processed_count*100:.1f}%\n")
                f.write("\n--- Current Run Breakdown ---\n")
                f.write(f"Reached max steps: {reached_max_steps_count}\n")
                f.write(f"Tests not pass: {tests_not_pass_count}\n")
                f.write(f"Infrastructure failures: {infrastructure_failure_count}\n")
                f.write(f"Pass but not good conversation: {pass_but_not_good_conversation_count}\n")

            return result

    tasks = [process_with_semaphore(instance) for instance in dataset]
    for task in asyncio.as_completed(tasks):
        result = await task
        if result:  # Only add results that passed tests
            results.append(result)

    print(
        f"\n🎯 Final Results: {passed_count}/{total_instances} instances passed all criteria and were included in main output"
    )

    # Print detailed breakdown summary
    print("\n📊 Detailed Breakdown:")
    print(f"   Reached max steps: {reached_max_steps_count}")
    print(f"   Tests not pass: {tests_not_pass_count}")
    print(f"   Infrastructure failures: {infrastructure_failure_count}")
    print(f"   Pass but not good conversation: {pass_but_not_good_conversation_count}")
    print(f"   Perfect (all criteria): {passed_count}")

    # Count results in each file to provide comprehensive summary
    try:
        with open(data_file, "r") as f:
            perfect_count = sum(1 for _ in f)
    except FileNotFoundError:
        perfect_count = 0

    try:
        with open(data_file_tests_only, "r") as f:
            tests_only_count = sum(1 for _ in f)
    except FileNotFoundError:
        tests_only_count = 0

    try:
        with open(data_file_tests_and_reading, "r") as f:
            tests_and_reading_count = sum(1 for _ in f)
    except FileNotFoundError:
        tests_and_reading_count = 0

    print("\n📊 Output File Summary:")
    print(
        f"   {data_file}: {perfect_count} samples (tests + conversation quality)"
    )
    print(f"   {data_file_tests_only}: {tests_only_count} samples (tests pass only)")
    print(
        f"   {data_file_tests_and_reading}: {tests_and_reading_count} samples (tests + conversation quality, old format)"
    )

    return results


# ============================================================================
# MAIN ENTRY POINT
# ============================================================================

# Data paths for different datasets
data_paths = {
    "swb": "az://orngscuscresco/data/luw/swe-bench-train-vsc/test_train/swe_bench_train_updated.jsonl"
}


def nostage_main(
    dataset_name: str = "all",
    outputs_dirname: str = "nostage_outputs",
    concurrency_limit: int = 100,
    max_steps: int = 150,
    debug_mode: bool = False,
    filter_pass_only: bool = False,
    skip_processed: bool = True,
    retries: int = 5,
) -> None:
    """Main function for no-stage data collection"""

    if dataset_name == "all":
        # Process all datasets by merging them
        print(f"Processing all datasets: {list(data_paths.keys())}")
        all_datasets = []

        for ds_name, azure_path in data_paths.items():
            dataset_file_path = f"/root/code/glass/data/jsonl/{ds_name}.jsonl"

            # Check if the dataset file exists, if not download it from Azure blob
            if not os.path.exists(dataset_file_path):
                print(f"Dataset file {dataset_file_path} not found. Downloading from Azure blob...")
                os.makedirs(os.path.dirname(dataset_file_path), exist_ok=True)

                download_cmd = f"bbb cp {azure_path} {dataset_file_path}"
                result = subprocess.run(download_cmd, shell=True)
                if result.returncode != 0:
                    raise RuntimeError(f"Failed to download dataset file: {download_cmd}")
                print(f"Dataset downloaded successfully to {dataset_file_path}")

            # Load and add to combined dataset
            ds_data = load_from_jsonl(dataset_file_path)
            print(f"Loaded {len(ds_data)} instances from {ds_name}")
            all_datasets.extend(ds_data)

        dataset = all_datasets
        print(f"Combined dataset has {len(dataset)} total instances")
    else:
        # Process single dataset
        if dataset_name in data_paths:
            azure_path = data_paths[dataset_name]
            dataset_name_or_path = f"/root/code/glass/data/jsonl/{dataset_name}.jsonl"
        else:
            # Assume it's a direct path
            dataset_name_or_path = dataset_name
            azure_path = dataset_name

        # Check if the dataset file exists, if not download it from Azure blob
        if not os.path.exists(dataset_name_or_path):
            print(f"Dataset file {dataset_name_or_path} not found. Downloading from Azure blob...")
            os.makedirs(os.path.dirname(dataset_name_or_path), exist_ok=True)

            download_cmd = f"bbb cp {azure_path} {dataset_name_or_path}"
            result = subprocess.run(download_cmd, shell=True)
            if result.returncode != 0:
                raise RuntimeError(f"Failed to download dataset file: {download_cmd}")
            print(f"Dataset downloaded successfully to {dataset_name_or_path}")

        # Load dataset
        dataset = load_from_jsonl(dataset_name_or_path)

    # Create output directory
    os.makedirs(f"./{outputs_dirname}", exist_ok=True)

    # Filter out already processed instances if skip_processed is enabled
    original_dataset_size = len(dataset)
    if skip_processed:
        all_processed_file = f"./{outputs_dirname}/all_processed.jsonl"
        processed_ids = load_processed_instances(all_processed_file)
        dataset, skipped_count = filter_unprocessed_instances(dataset, processed_ids)

        if skipped_count > 0:
            print(
                f"\n🔄 SKIP MODE: Found {len(processed_ids)} previously processed instances. Will process {len(dataset)} remaining instances (out of {original_dataset_size} total)"
            )
        else:
            print(
                f"\n🔄 SKIP MODE: No previously processed instances found, processing all {len(dataset)} instances"
            )
    else:
        print(
            f"\n🔄 SKIP MODE DISABLED: Processing all {len(dataset)} instances (may include duplicates)"
        )

    # Apply testing mode limit if enabled
    if debug_mode:
        original_length = len(dataset)
        dataset = dataset[:10]
        print(
            f"🔍 DEBUG/TESTING MODE: Limited dataset from {original_length} to {len(dataset)} instances (first 1 only)"
        )

    subprocess.run("mkdir /var/log/supervisor", shell=True)

    # Shuffle dataset for random processing order
    random.shuffle(dataset)

    # Configuration for no-stage generation
    student_juice = 128

    # Run no-stage processing
    results = asyncio.run(
        process_all_nostage(
            dataset=dataset,
            student_juice=student_juice,
            max_steps=max_steps,
            concurrency_limit=concurrency_limit if not debug_mode else 10,
            outputs_dirname=outputs_dirname,
            debug_mode=debug_mode,
            filter_pass_only=filter_pass_only,
            retries=retries,
        )
    )

    # Print results
    print("*" * 100)
    print("🎉 No-Stage Data Generation Complete!")
    print(f"Total instances that passed all criteria: {len(results)}")
    print(f"Main data file: {outputs_dirname}/data.jsonl")
    print(f"Tests-only data file: {outputs_dirname}/data_tests_pass.jsonl")
    print(
        f"Tests + efficient reading file: {outputs_dirname}/data_tests_pass_efficient_reading.jsonl"
    )
    print(f"ALL processed instances file: {outputs_dirname}/all_processed.jsonl")
    print(f"Progress tracking saved to: {outputs_dirname}/progress.txt")
    print(f"Detailed retry tracking saved to: {outputs_dirname}/progress_details.txt")

    if len(results) > 0:
        print(f"Successfully generated {len(results)} high-quality datapoints meeting all criteria")
        print(
            "All output files contain samples with different quality levels - check file summary above"
        )
    else:
        print("⚠️  No instances passed all criteria for main file - check other output files")

    if skip_processed:
        print(
            f"💾 RESUMABLE: {outputs_dirname}/all_processed.jsonl contains all processed instances for future skip logic"
        )
    print("*" * 100)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="No-stage data collection for SWE tasks")
    parser.add_argument(
        "--dataset",
        "-d",
        type=str,
        default="all",
        help=f"Dataset name or path. Use 'all' to process all datasets, or specify one: {list(data_paths.keys())}",
    )
    parser.add_argument(
        "--output", "-o", type=str, default="nostage_outputs", help="Output directory name"
    )
    parser.add_argument(
        "--concurrency",
        "-c",
        type=int,
        default=100,
        help="Concurrency limit for parallel processing",
    )
    parser.add_argument(
        "--max-steps", "-ms", type=int, default=100, help="Maximum steps for conversation"
    )
    parser.add_argument(
        "--debug-mode",
        "-dm",
        action="store_true",
        help="Enable debug/testing mode (detailed logging + only process first 3 instances)",
    )
    parser.add_argument(
        "--filter-pass-only",
        "-fpo",
        action="store_true",
        default=False,
        help="Only collect samples that pass tests (bypass quality checks)",
    )
    parser.add_argument(
        "--no-skip-processed",
        "-sp",
        action="store_true",
        default=False,
        help="Disable skipping of already processed instances (default: skip enabled)",
    )
    parser.add_argument(
        "--retries",
        "-r",
        type=int,
        default=5,
        help="Number of retries for each instance (default: 5)",
    )

    args = parser.parse_args()

    print("Running no-stage data collection with:")
    print(f"  Dataset: {args.dataset}")
    print(f"  Output directory: {args.output}")
    if args.debug_mode:
        print("  Concurrency limit: 10")
    else:
        print(f"  Concurrency limit: {args.concurrency}")
    print(f"  Max steps: {args.max_steps}")
    print(f"  Debug mode: {args.debug_mode}")
    print(f"  Filter pass only: {args.filter_pass_only}")
    print(f"  Skip processed: {not args.no_skip_processed}")
    print(f"  Retries: {args.retries}")

    nostage_main(
        dataset_name=args.dataset,
        outputs_dirname=args.output,
        concurrency_limit=args.concurrency,
        max_steps=args.max_steps,
        debug_mode=args.debug_mode,
        filter_pass_only=args.filter_pass_only,
        skip_processed=not args.no_skip_processed,
        retries=args.retries,
    )
