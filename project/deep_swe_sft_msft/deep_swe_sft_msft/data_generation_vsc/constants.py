# CAAS_ENDPOINT = "https://eastus2.caas.azure.com"
CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"

BUS_USER = "swe-main-run"  # "swe-main-run" #"swe-sft-data"
BUS_TOPIC = "az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted"
# BUS_TOPIC = "az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
STUDENT_RENDERER = (
    "harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"
)
TEACHER_RENDERER = (
    "harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"
)

VSC_TOOL_ERROR_MSGS = [
    "VSCode Copilot tool error:",
    "Failed to decode JSON response",
    "HTTP POST failed with",
    "ExecNetworkError",
    "timeout",
    "File not found",
    "Invalid input path",  
    "Error parsing function call",
    "Unknown function",
    "Missing required",
    "outside of the workspace",
    "ERROR: Invalid context",
    "line must be non-negative",
]
