"""
Corrector Model for fixing errors in student messages and tool calls.
"""

import json
import re
import asyncio
import copy
from typing import Any
from dataclasses import dataclass

from chat.render.renderer_registry import get_renderer
from chat import chat, Role, Conversation
from chat.chat import Text
from chat.tools import take_one_step_with_tools
from message_completer.token_message_completer import TokenMessageCompleter
from message_completer.message_completer import ParseErrorMode
from bus_token_completer import BusTokenCompleter

from deep_swe_sft_msft.data_generation_vsc.constants import (
    BUS_USER,
    BUS_TOPIC,
    VSC_TOOL_ERROR_MSGS
)

# Import the constraint extension function from utilities
from deep_swe_sft_msft.data_generation_vsc.constraint_utils import get_render_constrained_extension, detect_forbidden_commands


### TODO: Dynamically import forbidden commands instead of hardcoding

# Base corrector prompt structure - shared across all cases
CORRECTOR_BASE_PROMPT = """
You are an expert corrector that fixes errors in student software engineering work.

Context of the conversation (last 10 messages):
{context}

Student's tool call recipient:
{student_recipient}

Student's original tool call content:
{student_content}

{case_specific_section}

You must respond with a JSON object containing three fields:
- "action": either "skip" or "replace"
- "recipient": the tool recipient to use (e.g., "functions.read_file")
- "content": the content to use (MUST be a JSON string, not a dictionary object)

Decision criteria:
- Use "skip" if:
  * The error is acceptable/expected behavior
  * The tool call is exploratory and the error provides useful information
  * The error doesn't significantly impact the workflow
- Use "replace" if:
  * The error can be fixed with a corrected tool call
  * The tool call has syntax errors or invalid parameters
  * The error prevents meaningful progress

For "skip" action: set "recipient" to the original recipient and "content" to the original student content
For "replace" action: set "recipient" to the corrected tool name (or keep original if recipient is already correct) and "content" to the corrected tool call JSON string

CRITICAL: The "content" field must ALWAYS be a JSON string (enclosed in quotes), never a raw dictionary object.

Examples of recipient replacements:
- "functions.run_in_terminal" with pytest command -> "functions.runTests"
- Invalid tool names -> Correct VSCode tool names
- If recipient is already correct (e.g., "functions.grep_search"), keep the same recipient and only fix the content

IMPORTANT PATH CORRECTION RULES:
- If you see "outside of the workspace" errors, the path is wrong - fix it to be within /root/code
- If you see "Invalid input path" errors with relative paths, convert to absolute paths starting with /root/code
- Examples of CORRECT paths: /root/code, /root/code/src, /root/code/tests/test_file.py
- Examples of INCORRECT paths: ., .., ~/something

Respond with JSON only:
{{
  "action": "skip" or "replace",
  "recipient": "functions.tool_name",
  "content": "{{\"param\": \"value\"}}"
}}

IMPORTANT: The "content" field must be a JSON string (with escaped quotes), NOT a raw object like {{"param": "value"}}.

INCORRECT example (do NOT do this):
{{
  "action": "replace",
  "recipient": "functions.list_dir",
  "content": {{"path": "/root/code/pinch"}}
}}

DO NOT generate directly generate content without action and recipient like these:
- {{"pattern": "def save", "path": "/root/code/moira_client/models/trigger.py"}} (This is not a valid VSCode tool call format)
- {{'path': '/root/code/pinch'}} (Invalid format lack action and recipient)
"""
# - {{'filePath': '/root/code/onemetric/cv/object_detection/average_precision.py', 'offset': 0, 'limit': 300}} (Invalid format)

# Case-specific prompt sections
TOOL_ERROR_CASE = """
Tool error output:
{tool_error_output}

The student made a tool call that resulted in an error. Your job is to analyze the error and decide whether to fix it or skip it.

CRITICAL READ_FILE ERROR FIXES:
- "Illegal argument: line must be non-negative" -> ALWAYS REPLACE with non-negative value (read_file uses 1-based indexing, not 0-based)
- If you see offset=0 in read_file parameters, fix it to offset=1
- If offset is missing entirely, add offset=1
- If limit is missing, add a reasonable limit <=2000

CRITICAL APPLY_PATCH ERROR FIXES:
- "Invalid Line" -> ALWAYS REPLACE with corrected patch format
- Check for proper V4A diff format: *** Begin Patch, *** Update File: /path, @@context, -old, +new, *** End Patch
- Ensure context lines (@@) match the actual file content exactly
- Verify line numbers and indentation are correct

CRITICAL TOOL CALL SYNTAX ERROR FIXES:
- Invalid list_dir call with incorrect parameters -> ALWAYS REPLACE with correct parameter names like 'path'
- Invalid read_file call using 'path' instead of 'filePath' -> ALWAYS REPLACE with 'filePath' parameter

OTHER COMMON TOOL ERROR FIXES:
- Decide yourself.
"""

EMPTY_RUNTESTS_CASE = """
CRITICAL ISSUE: The student called runTests with either:
1. Empty files array: {{"files": []}}
2. Missing files parameter entirely: {{}} (NO_FILES_KEY case)

Both patterns are problematic because runTests needs specific test files to execute.

YOUR TASK: You MUST always use "replace" action and provide a corrected runTests call with actual test files.

To fix this:
1. Look at the conversation context to understand what the student is trying to test
2. Replace the empty/missing runTests call with a file discovery tool call (file_search or list_dir)
3. The student can then use the discovered files in a subsequent runTests call

EXAMPLE CORRECTION:
Instead of:
{
  "action": "replace",
  "recipient": "functions.runTests",
  "content": "{\"files\": []}"
}
OR:
{
  "action": "replace",
  "recipient": "functions.runTests",
  "content": "{}"
}

Use:
{
  "action": "replace",
  "recipient": "functions.runTests",
  "content": "{\"files\": [\"/root/code/tests/test_feature.py\", \"/root/code/tests/test_integration.py\"]}"
}

NOTE: The corrector should replace the empty/missing runTests call with a discovery tool call, NOT guess test file paths.
"""

DISCOURAGED_COMMAND_CASE = """
CRITICAL ISSUE: The student used a discouraged/forbidden command.

The student's tool call contains commands that should be replaced with VSCode tool alternatives.

YOUR TASK: You MUST always use "replace" action and convert the discouraged command to the appropriate VSCode tool.

FORBIDDEN COMMANDS that we want to replace:
- Try to avoid using these discouraged commands: grep, find, ls, sed, cat, bash, oai
- DO NOT use: pytest, python -m pytest, run_tests.sh, mv, cp, rm
- Use VSCode tool alternatives instead:
  * Instead of 'ls directory' -> use list_dir tool
  * Instead of 'cat file' or 'head/tail file' -> use read_file tool
  * Instead of 'grep pattern file' -> use grep_search tool (for exact/regex search) or semantic_search tool (for natural language search)
  * Instead of 'find -name pattern' -> use file_search tool with glob patterns OR test_search tool for finding test-related files
  * Instead of 'pytest' or 'bash run_tests.sh' -> use runTests tool
  * Instead of 'sed/awk commands' for editing files -> ALWAYS use apply_patch tool with V4A diff format
  * For symbol search -> use search_workspace_symbols or list_code_usages tools

TOOL CALL FORMAT REFERENCE (for content field only):
- list_dir requires: {{"path": "/absolute/path/to/directory"}}
- read_file requires: {{"filePath": "/absolute/path", "offset": 1, "limit": 2000}}
  NOTE: offset is 1-based line number to start reading from (>= 1), limit is number of lines to read
  Example: {{"filePath": "/root/code/file.py", "offset": 1, "limit": 50}}
- runTests requires: {{"files": ["/path/to/test1.py", "/path/to/test2.py"]}} 
  NOTE: Use runTests instead of pytest, bash run_tests.sh, or python -m pytest
- grep_search requires: {{"query": "search_pattern", "isRegexp": true/false, "includePattern": "glob_pattern"}}
  NOTE: Use for exact string or regex search in files
- file_search requires: {{"query": "glob_pattern", "maxResults": 100}}
  NOTE: Use for finding files by name/path patterns
- semantic_search requires: {{"query": "natural language description"}}
  NOTE: Use for natural language search across codebase
- apply_patch requires: {{"input": "*** Begin Patch\\n*** Update File: /absolute/path/to/file\\n@@context_line\\n-old_line\\n+new_line\\n*** End Patch"}}
  NOTE: Use V4A diff format for editing files. Structure: *** Begin Patch, *** [Add|Update|Delete] File: /path, @@context_lines, -old_lines, +new_lines, *** End Patch
- test_search requires: {{"filePaths": ["/path/to/source.py"]}}
  NOTE: Use to find test files for source files or vice versa
- search_workspace_symbols requires: {{"symbolName": "function_or_class_name"}}
  NOTE: Use to find symbols in workspace
- list_code_usages requires: {{"symbolName": "symbol_name", "filePaths": ["/optional/path/to/definition.py"]}}
  NOTE: Use to find all usages of a symbol


REMEMBER: NEVER skip discouraged commands - always replace with VSCode alternatives.
"""


@dataclass
class CorrectionResult:
    """Result of a correction attempt"""
    success: bool
    corrected_student_messages: list = None
    corrected_tool_messages: list = None
    error_message: str = ""


def has_tool_error(tool_message) -> bool:
    """Check if a tool message contains error messages from VSC_TOOL_ERROR_MSGS"""
    content = str(tool_message.content)
    return any(error_msg in content for error_msg in VSC_TOOL_ERROR_MSGS)



def has_run_in_terminal_discouraged_commands(student_message) -> bool:
    """Check if a message to functions.run_in_terminal contains discouraged commands"""
    if not hasattr(student_message, 'recipient') or student_message.recipient != "functions.run_in_terminal":
        return False
    
    content = str(student_message.content)
    return detect_forbidden_commands(content)


def has_runTests_empty_files_array(student_message) -> bool:
    """Check if a message to functions.runTests has empty files array or missing files parameter entirely"""
    if not hasattr(student_message, 'recipient') or student_message.recipient != "functions.runTests":
        return False
    
    try:
        content = str(student_message.content)
        # Check if content contains text content
        if hasattr(student_message.content, 'text'):
            content = student_message.content.text
        
        # Try to parse as JSON
        import json
        try:
            parsed = json.loads(content.strip())
            if isinstance(parsed, dict):
                # Check for empty files array: {"files": []}
                if "files" in parsed and parsed["files"] == []:
                    return True
                # Check for missing files parameter entirely: {} (NO_FILES_KEY case)
                if "files" not in parsed:
                    return True
        except json.JSONDecodeError:
            # If it's not valid JSON, it's not the pattern we're looking for
            pass
        
        # Also check for literal string patterns
        if ('{"files": []}' in content or '"files": []' in content or 
            content.strip() == '{}' or content.strip() == '{}\n'):
            return True
            
    except Exception:
        # If we can't process the content, assume it's not the problematic pattern
        pass
    
    return False


class Corrector:
    """Corrector model for fixing errors in student messages and tool calls"""
    
    def __init__(self, renderer_name: str, retries: int = 3, reward_multiplier: int = 32) -> None:
        self.renderer_name = renderer_name
        self.renderer = get_renderer(renderer_name)
        self.retries = retries
        self.reward_multiplier = reward_multiplier

        # Corrector model configuration
        bus_tc_config = BusTokenCompleter.Config(
            topic_mode_or_user=BUS_USER,
            topic_or_snapshot=BUS_TOPIC,
        )
        extension = get_render_constrained_extension(renderer_name)
        
        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=bus_tc_config,
            completion_params={"temperature": 1.0, "extensions": [extension]},
            renderer=self.renderer,
        )
        
        self.message_completer = message_completer_config.build()
    
    def _build_correction_prompt(self, case_type: str, context: str, student_recipient: str,
                                student_content: str, tool_error_output: str = "") -> str:
        """Build the appropriate correction prompt based on case type"""
        
        if case_type == "empty_runtests":
            case_specific_section = EMPTY_RUNTESTS_CASE
        elif case_type == "discouraged_command":
            case_specific_section = DISCOURAGED_COMMAND_CASE
        else:  # tool_error
            case_specific_section = TOOL_ERROR_CASE.format(tool_error_output=tool_error_output)
        
        return CORRECTOR_BASE_PROMPT.format(
            context=context,
            student_recipient=student_recipient,
            student_content=student_content,
            case_specific_section=case_specific_section
        )
    
    async def correct_step_if_needed(
        self, 
        step_student_messages: list,
        step_tool_messages: list,
        current_convo: Conversation,
        toolkit,
        valid_recipients: set,
        correction_case: str = "tool_error"
    ) -> CorrectionResult:
        """
        Correct the student messages based on tool errors.
        
        Args:
            step_student_messages: The student messages from this step
            step_tool_messages: The tool messages from this step (assumed to contain errors)
            current_convo: The current conversation state before this step
            toolkit: The toolkit for re-executing tools
            valid_recipients: Set of valid recipients for messages
            
        Returns:
            CorrectionResult with corrected messages
        """
        
        print(f"Attempting correction for {len(step_tool_messages)} tool error(s)...")
        
        # Try to correct the student messages
        try:
            # Use only the last 10 messages as context
            last_10_messages = current_convo.messages[-10:] if len(current_convo.messages) > 10 else current_convo.messages
            limited_convo = Conversation(messages=last_10_messages)
            current_context = self._format_conversation_for_evaluation(limited_convo)
            corrected_student_messages = await self._correct_student_messages(
                step_student_messages, step_tool_messages, context=current_context, correction_case=correction_case
            )
            if not corrected_student_messages:
                return CorrectionResult(
                    success=False,
                    error_message="Failed to generate corrected student messages"
                )

            # Rule-based validation of correction based on case type
            if not self._validate_correction(corrected_student_messages, correction_case):
                return CorrectionResult(
                    success=False,
                    error_message=f"Corrected messages failed validation for case: {correction_case}"
                )
            
            # Re-execute tools with corrected messages if tools error case
            # Otherwise, not needed for discouraged command or empty runTests cases because we haven't executed tools yet
            if correction_case == "tool_error":
                corrected_tool_messages = await self._re_execute_tools(
                    corrected_student_messages, current_convo, toolkit, valid_recipients
                )
            else:
                corrected_tool_messages = ["Correction case does not require tool re-execution"]

            return CorrectionResult(
                success=True,
                corrected_student_messages=corrected_student_messages,
                corrected_tool_messages=corrected_tool_messages
            )
            
        except Exception as e:
            return CorrectionResult(
                success=False,
                error_message=f"Correction failed: {str(e)}"
            )
    
    async def _correct_student_messages(self, 
                                        student_messages: list,
                                        error_tool_messages: list,
                                        context: str = "", 
                                        correction_case: str = "tool_error"
                                        ) -> list:
        """Correct student messages based on tool errors"""
        
        # Focus on the last student message that likely caused the error
        if not student_messages:
            return []
        
        last_student_message = student_messages[-1]
        
        # Combine all error outputs for context
        error_outputs = []
        for error_msg in error_tool_messages:
            error_outputs.append(str(error_msg.content))
        
        combined_error = "\n".join(error_outputs)
        
        # Extract the content from the student message
        try:
            student_content = str(last_student_message.content)
        except Exception as e:
            print(f"Failed to extract student content: {e}")
            return []
        
        try:
            student_recipient = last_student_message.recipient
        except Exception as e:
            print(f"Failed to extract student recipient: {e}")
            return []
        
        # Use the provided correction case
        print(f"Using correction case: {correction_case}")
        
        try:
            correction_prompt = self._build_correction_prompt(
                case_type=correction_case,
                context=context,
                student_recipient=student_recipient,
                student_content=student_content,
                tool_error_output=combined_error
            )
        except Exception as format_error:
            import traceback
            traceback.print_exc()
            raise format_error
        
        print("Requesting correction for student message...")
        
        # Create corrector conversation
        corrector_convo = chat.Conversation(
            messages=[
                chat.Message.system(
                    "You are an expert corrector that analyzes software engineering tool call errors and decides whether to fix or skip them. Respond with a JSON object containing 'action' (skip/replace) and 'content' fields only.",
                    metadata=chat.SystemContentMetadata(reward_multiplier=self.reward_multiplier)
                ),
                chat.Message.user(correction_prompt),
            ]
        )
        # print(corrector_convo) # for debugging
        
        # Get corrector response
        for attempt in range(self.retries):
            try:
                
                completion = await self.message_completer.async_completion(
                    conversations=[corrector_convo], n=1, seed=0, end_header=False,
                )
                
                choice = completion.choices[0]
                response_messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
                
                # Parse the corrected message
                response_text = str(response_messages[-1].content)
                print(f"Corrector response: {response_text[:200]}...")
                
                
                corrected_message = self._parse_corrected_message(response_text, last_student_message)
                
                if corrected_message:
                    # Return all original messages except the last one, plus the corrected last message
                    return student_messages[:-1] + [corrected_message]
                else:
                    print(f"Failed to parse corrected message, attempt {attempt + 1}")
                    
            except Exception as e:
                import traceback
                traceback.print_exc()
                print(f"Corrector attempt {attempt + 1} failed: {e}")
                if attempt < self.retries - 1:
                    await asyncio.sleep(1)
                    continue
        
        print("All correction attempts failed")
        return []
    
    async def _re_execute_tools(
        self, 
        corrected_student_messages: list, 
        current_convo: Conversation,
        toolkit,
        valid_recipients: set
    ) -> list:
        """Re-execute tools with corrected student messages"""
        
        if not corrected_student_messages:
            return []
        
        # Create conversation with corrected student messages
        step_convo = current_convo.with_suffix(*corrected_student_messages)
        last_message = corrected_student_messages[-1]
        
        # Check if tools should be executed
        should_execute_tools = not (
            last_message.end_turn
            or (last_message.recipient not in valid_recipients and last_message.recipient.split(".")[0] not in valid_recipients)
        )
        
        corrected_tool_messages = []
        if should_execute_tools:
            try:
                # Execute tools with corrected message
                async for tool_message in take_one_step_with_tools(
                    prefix_convo=step_convo.prefix(-1),
                    message=last_message,
                    toolkit=toolkit,
                ):
                    corrected_tool_messages.append(tool_message)
                    # Update the step conversation for subsequent tool calls
                    if step_convo.messages[-1].id == tool_message.id:
                        step_convo.messages[-1] = tool_message
                    else:
                        step_convo = step_convo.with_suffix(tool_message)
                        
                print(f"Re-executed tools, got {len(corrected_tool_messages)} tool messages")
                
            except Exception as e:
                print(f"Tool re-execution failed: {e}")
                return []
        
        return corrected_tool_messages
    
    def _validate_correction(self, corrected_student_messages: list, correction_case: str) -> bool:
        """Validate that the corrected messages are appropriate for the correction case"""
        
        if not corrected_student_messages:
            return False
        
        last_message = corrected_student_messages[-1]
        
        # For discouraged_command case, ensure no forbidden commands remain
        if correction_case == "discouraged_command":
            try:
                # Use the existing function to check for discouraged commands
                if has_run_in_terminal_discouraged_commands(last_message):
                    content = str(last_message.content)
                    print(f"Corrected message still contains forbidden commands: {content[:100]}...")
                    return False
                print("Correction validation passed: no forbidden commands detected")
                return True
            except Exception as e:
                print(f"Error validating corrected message for discouraged commands: {e}")
                return False
        
        # For empty_runtests case, ensure files array is not empty
        elif correction_case == "empty_runtests":
            try:
                content = str(last_message.content)
                if hasattr(last_message.content, 'text'):
                    content = last_message.content.text
                
                # Check if it still has empty files array
                if has_runTests_empty_files_array(last_message):
                    print(f"Corrected message still has empty files array: {content[:100]}...")
                    return False
                
                # Also check if the content is valid JSON with non-empty files
                try:
                    import json
                    parsed = json.loads(content.strip())
                    if isinstance(parsed, dict) and "files" in parsed:
                        if not parsed["files"] or len(parsed["files"]) == 0:
                            print("Corrected message has empty files array")
                            return False
                        print(f"Correction validation passed: files array has {len(parsed['files'])} entries")
                        return True
                except json.JSONDecodeError:
                    print("Corrected message is not valid JSON")
                    return False
                    
                return True
            except Exception as e:
                print(f"Error validating corrected message for empty runTests: {e}")
                return False
        
        # For tool_error case, we don't do pre-validation since we'll re-execute tools
        elif correction_case == "tool_error":
            return True
        
        # Unknown case
        print(f"Unknown correction case: {correction_case}")
        return False
    
    def _parse_corrected_message(self, response_text: str, original_message) -> Any | None:
        """Parse the corrected message from corrector response"""
        
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            try:
                # Create a new message object with corrected content
                corrected_message = copy.deepcopy(original_message)
                
                # The response should be a JSON with action and content fields
                response_text = response_text.strip()
                
                # Parse the JSON response
                try:
                    json_text = json_match.group()
                    
                    response_json = json.loads(json_text)
                except json.JSONDecodeError as e:
                    print(f"Response is not valid JSON: {response_text[:200]}...")
                    if hasattr(e, 'pos') and e.pos is not None:
                        error_context = json_match.group()[max(0, e.pos-10):e.pos+10]
                        print(f"🐛 DEBUG: Error context around position {e.pos}: '{error_context}'")
                    raise ValueError(f"Response is not valid JSON: {e.msg}") from None
                
                # Validate required fields
                if "action" not in response_json or "content" not in response_json or "recipient" not in response_json:
                    print(f"Response missing required fields: {response_json}")
                    
                    # FALLBACK: Try to handle direct tool call responses that are missing the wrapper format
                    if isinstance(response_json, dict) and any(key in response_json for key in ['query', 'command', 'filePath', 'path', 'files', 'pattern']):
                        print("🐛 DEBUG: Detected direct tool call format, attempting to wrap in corrector format...")
                        
                        # Try to infer the tool type and recipient from the parameters
                        if 'filePath' in response_json:
                            # Likely a read_file call, fix missing offset if needed
                            if 'offset' in response_json and response_json['offset'] == 0:
                                response_json['offset'] = 1  # Fix 0-based to 1-based indexing
                            if 'limit' not in response_json:
                                response_json['limit'] = 2000  # Add default limit
                            recipient = "functions.read_file"
                        elif 'path' in response_json:
                            recipient = "functions.list_dir"
                        elif 'query' in response_json:
                            # Could be grep_search, file_search, or semantic_search - default to file_search
                            recipient = "functions.file_search"
                            if 'maxResults' not in response_json:
                                response_json['maxResults'] = 100  # Add default maxResults
                        
                        # Wrap the direct tool call in the expected corrector format
                        corrected_tool_call = json.dumps(response_json)
                        corrected_message.recipient = recipient
                        corrected_message.content = Text.from_string(corrected_tool_call)
                        print(f"🐛 DEBUG: Wrapped tool call - recipient: {recipient}, content: {corrected_tool_call}")
                        return corrected_message
                    
                    raise ValueError("Response missing 'action', 'recipient', or 'content' field")
                
                action = response_json["action"]
                recipient = response_json["recipient"]
                content = response_json["content"]
                
                # Validate action value
                if action not in ["skip", "replace"]:
                    print(f"Invalid action value: {action}")
                    raise ValueError(f"Invalid action value: {action}")
                
                # For skip action, we could return None to indicate no change needed
                # But for consistency, we'll return the original message with original content
                if action == "skip":
                    print("Corrector decided to skip - using original content")
                    return original_message
                
                # For replace action, validate that content is valid JSON (tool call)
                if action == "replace":
                    # Handle both cases: content as dict or content as JSON string
                    if isinstance(content, dict):
                        # Content is already a dictionary, convert to JSON string
                        parsed_content = content
                        content = json.dumps(content)
                        print(f"🐛 DEBUG: Content was dict, converted to JSON string: {content}")
                    else:
                        # Content is a string, try to parse it as JSON
                        try:
                            parsed_content = json.loads(content)
                            print(f"🐛 DEBUG: Content parsed successfully as JSON: {parsed_content}")
                        except json.JSONDecodeError:
                            print(f"Replacement content is not valid JSON: {content[:200]}...")
                            raise ValueError("Replacement content is not valid JSON") from None
                    

                    
                    print("Corrector decided to replace - using corrected content and recipient")
                    corrected_message.recipient = recipient  # Update recipient
                    print(f"🐛 DEBUG: Corrected recipient: {corrected_message.recipient}")
                    corrected_message.content = Text.from_string(content)
                    return corrected_message
                    
            except Exception as e:
                print(f"Error creating corrected message: {e}")
                import traceback
                traceback.print_exc()
                return None
        else:
            raise ValueError(f"Could not find JSON in corrector response: {response_text[:200]}...")

    def _format_conversation_for_evaluation(self, conversation: Conversation, token_limit: int = 256, tool_truncation_rate: float = 0.5) -> str:
        """Format conversation for teacher evaluation"""
        formatted_messages = []
        for msg in conversation.messages:
            role = msg.author.role

            if role == Role.SYSTEM:
                continue # skip system messages for now
                # content = system_content_render(msg.content)
            else:
                content = str(msg.content)
                if role != Role.TOOL:
                    cur_token_limit = token_limit
                else:
                    cur_token_limit = int(token_limit * tool_truncation_rate)
                content = truncate_string(self.renderer, content, token_limit=cur_token_limit)
                
            formatted_messages.append(f"<author:{msg.author.role}> -> <recipient:{msg.recipient}>:  {content}\n")

        return "\n".join(formatted_messages)
    
    
def truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    """Truncate a string to a specified token limit"""
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"