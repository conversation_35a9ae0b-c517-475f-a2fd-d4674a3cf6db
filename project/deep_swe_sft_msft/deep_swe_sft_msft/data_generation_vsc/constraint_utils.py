"""
Constraint machine utilities for VSCode tool rendering.
"""

from random import random
import re
from qstar.common.tools import constraint_machine_spec
from qstar.common.tools import renderer_worker
from deep_swe_msft.tools.caas_vscode_tool import VSCodeTool
from deep_swe_msft.vsc_data.datapoint_converters import DISCOURAGED_COMMANDS
from deep_swe_msft.tools.caas_vscode_tool import _detect_hardcode_forbidden_tool

def get_render_constrained_extension(renderer_name: str):
    """Get the constrained extension for the renderer with VSCode tool constraints."""
    
    harmony_stop_tokens_utils = renderer_worker.HarmonyStopTokensUtils(
        harmony_renderer_name=renderer_name,
        enable_special_stop_token=False,
    )
    
    # Get actual VSCode tool names dynamically
    vscode_tool_names = VSCodeTool.get_names_of_functions_the_model_can_call()
    vscode_tools = tuple(f"functions.{name}" for name in vscode_tool_names)
    
    extension = constraint_machine_spec.constraint_machine_extension(
        harmony_renderer_name=renderer_name,
        encoding_name='orion_200k',
        final_token_sequences=harmony_stop_tokens_utils.constraint_machine_final_token_sequences(),
        json_eot_tokens=harmony_stop_tokens_utils.constrained_sampling_json_eot_tokens(),
        tools=vscode_tools,  # Use VSCode tool constraints
        channels=("analysis", "final"),
        response_format_names=(),
        enable_json_constraining=False,
        none_channel_allowed=False,
    )
    return extension


def detect_forbidden_commands(content: str) -> bool:
    """Check if the content contains any forbidden commands from DISCOURAGED_COMMANDS or hardcoded forbidden tools"""
    
    # Context-aware detection that distinguishes between JSON tool parameters and actual shell commands
    content_lower = content.lower()
    
    
    # Check against DISCOURAGED_COMMANDS list with context awareness
    for forbidden_cmd in DISCOURAGED_COMMANDS:
        cmd_lower = forbidden_cmd.lower()
        
        # Look for actual command usage patterns:
        # 1. Shell command patterns: "ls -la", "grep -r", "cat file.txt"
        command_patterns = [
            rf'\b{re.escape(cmd_lower)}\s+[-\w]',  # Command with flags: "ls -la"
            rf'\b{re.escape(cmd_lower)}\s+\w+\.\w+',  # Command with file: "cat file.txt"
            rf'\b{re.escape(cmd_lower)}$',  # Command at end of string
            rf'^{re.escape(cmd_lower)}\b',  # Command at start of string
        ]
        
        # Check if any shell command pattern matches
        for pattern in command_patterns:
            if re.search(pattern, content_lower):
                return cmd_lower

    # Check against hardcoded forbidden tools (pytest, bash run_tests.sh)
    if _detect_hardcode_forbidden_tool(content):
        return "Not use runTests"
    
    return False
