"""
Efficient Reading Grader - Evaluates conversation for efficient file reading patterns.

This grader checks for overlapping file reads and redundant reading patterns
in conversations, following the same pattern as ConversationGrader.
"""

from dataclasses import dataclass

from chat import Conversation, Role

from .conversation_grader import ConversationEvaluation


@dataclass
class ReadingAnalysis:
    """Result of reading pattern analysis"""
    is_efficient: bool
    total_file_reads: int
    unique_files_read: int
    files_with_overlaps: list[str]
    files_with_redundancy: list[str]
    efficiency_score: float
    reads_per_file: dict[str, int]
    overlap_details: list[str] = None
    redundant_details: list[str] = None


class EfficientReadingGrader:
    """
    Grader that evaluates conversations for efficient file reading patterns.

    Follows the same pattern as ConversationGrader but focuses specifically
    on analyzing file reading efficiency without needing an LLM.
    """

    def __init__(self) -> None:
        """Initialize the efficient reading grader."""
        pass
    
    def evaluate_conversation(self, conversation: Conversation) -> ConversationEvaluation:
        """
        Evaluate a conversation for reading efficiency.
        
        Args:
            conversation: The conversation to evaluate
            
        Returns:
            ConversationEvaluation with reading efficiency assessment
        """
        analysis = self.analyze_reading_patterns(conversation)
        
        # Convert to ConversationEvaluation format for consistency
        # Only care about redundant reading, not overlapping reads
        criterion_scores = {
            "criterion1": {
                "passed": len(analysis.files_with_redundancy) == 0,
                "explanation": "No redundant file reads detected" if len(analysis.files_with_redundancy) == 0
                              else f"Found redundant reads in {len(analysis.files_with_redundancy)} files: {', '.join(analysis.files_with_redundancy)}"
            }
        }
        
        is_good = analysis.is_efficient
        overall_score = analysis.efficiency_score
        
        feedback = []
        if analysis.total_file_reads == 0:
            feedback.append("No file reads detected in conversation")
        else:
            feedback.append(f"Total file reads: {analysis.total_file_reads}")
            feedback.append(f"Unique files read: {analysis.unique_files_read}")
            if analysis.files_with_redundancy:
                feedback.append(f"Files with redundant reads: {', '.join(analysis.files_with_redundancy)}")
        
        return ConversationEvaluation(
            is_good_conversation=is_good,
            overall_score=overall_score,
            criterion_scores=criterion_scores,
            overall_feedback="; ".join(feedback),
            rubric_name="efficient_reading"
        )
    
    def analyze_reading_patterns(self, conversation: Conversation) -> ReadingAnalysis:
        """
        Analyze the conversation to extract reading patterns.
        
        Properly iterates through conversation.messages to detect read_file tool calls.
        
        Args:
            conversation: The conversation to analyze
            
        Returns:
            ReadingAnalysis with detailed reading pattern information
        """
        try:
            # Track file read ranges and counts
            file_read_ranges = {}  # {filepath: [(start, end), ...]}
            file_read_counts = {}  # {filepath: count} - tracks all reads even without range info
            total_reads = 0
            
            # Iterate through conversation messages (like conversation_grader.py does)
            for msg in conversation.messages:
                # Check if this is a read_file tool call
                # Pattern: tool messages with author.name == "functions.read_file"
                if (hasattr(msg, 'author') and hasattr(msg.author, 'role') and
                    hasattr(msg.author, 'name') and
                    msg.author.role == Role.TOOL and
                    msg.author.name == 'functions.read_file'):
                    
                    # This is a read_file tool call
                    try:
                        # Extract parameters from metadata.exec_cmd.read_file
                        metadata = getattr(msg, 'metadata', {})
                        exec_cmd = metadata.get('exec_cmd', {})
                        read_file_params = exec_cmd.get('read_file', {})
                        
                        filepath = read_file_params.get('filePath')
                        start_line_zero = read_file_params.get('startLineNumberBaseZero', 0)
                        end_line_zero = read_file_params.get('endLineNumberBaseZero', 2000)
                        if start_line_zero is None:
                            start_line_zero = 1
                        if end_line_zero is None:
                            end_line_zero = 2000

                        if filepath:
                            # Count this as a read even if we don't have complete line info
                            total_reads += 1
                            file_read_counts[filepath] = file_read_counts.get(filepath, 0) + 1
                            
                            # Track ranges - use defaults if info is missing
                            if start_line_zero is not None and end_line_zero is not None:
                                # Convert from zero-based to one-based line numbers
                                start_line = start_line_zero + 1
                                end_line = end_line_zero + 1
                            else:
                                # Use default range when line info is missing
                                start_line = 1
                                end_line = 2000
                            
                            if filepath not in file_read_ranges:
                                file_read_ranges[filepath] = []
                            file_read_ranges[filepath].append((start_line, end_line))
                    except Exception:
                        # Skip if parsing fails
                        continue
            
            # Analyze for redundancy and overlaps
            redundant_files = []
            redundant_details = []
            overlapping_files = []
            overlap_details = []
            
            for filepath, count in file_read_counts.items():
                # Check for redundancy (more than 2 reads of same file)
                if count >= 2:
                    redundant_files.append(filepath)
                    redundant_details.append(f"File '{filepath}' read {count} times (more than 2)")
            
            # Check for overlapping ranges within each file
            for filepath, ranges in file_read_ranges.items():
                if len(ranges) >= 2:
                    # Check for overlaps between ranges
                    for i in range(len(ranges)):
                        for j in range(i + 1, len(ranges)):
                            start1, end1 = ranges[i]
                            start2, end2 = ranges[j]
                            # Check if ranges overlap
                            if not (end1 < start2 or end2 < start1):
                                if filepath not in overlapping_files:
                                    overlapping_files.append(filepath)
                                    overlap_details.append(f"File '{filepath}' has overlapping reads: lines {start1}-{end1} and {start2}-{end2}")
                                break
                        if filepath in overlapping_files:
                            break
            
            # Only consider redundancy for efficiency (overlaps are acceptable)
            is_efficient = len(redundant_files) == 0
            efficiency_score = 1.0 if is_efficient else 0.0
            
            return ReadingAnalysis(
                is_efficient=is_efficient,
                total_file_reads=total_reads,
                unique_files_read=len(file_read_counts),
                files_with_overlaps=[],  # Not tracking overlaps anymore
                files_with_redundancy=redundant_files,
                efficiency_score=efficiency_score,
                reads_per_file=file_read_counts,
                overlap_details=[],  # Not tracking overlaps anymore
                redundant_details=redundant_details
            )
            
        except Exception as e:
            print(f"Warning: Reading analysis failed: {e}")
            return ReadingAnalysis(
                is_efficient=False,
                total_file_reads=0,
                unique_files_read=0,
                files_with_overlaps=[],
                files_with_redundancy=[],
                efficiency_score=0.0,
                reads_per_file={}
            )


# Convenience functions for easy usage (same pattern as conversation_grader.py)
def check_efficient_reading(conversation: Conversation) -> bool:
    """
    Simple function to check if a conversation has efficient reading patterns.
    
    Args:
        conversation: The conversation to check
        
    Returns:
        bool: True if reading is efficient, False otherwise
    """
    grader = EfficientReadingGrader()
    evaluation = grader.evaluate_conversation(conversation)
    return evaluation.is_good_conversation


def evaluate_reading_efficiency_detailed(conversation: Conversation) -> ConversationEvaluation:
    """
    Detailed function to evaluate reading efficiency with full results.
    
    Args:
        conversation: The conversation to evaluate
        
    Returns:
        ConversationEvaluation: Detailed evaluation results
    """
    grader = EfficientReadingGrader()
    return grader.evaluate_conversation(conversation)


def get_reading_analysis(conversation: Conversation) -> ReadingAnalysis:
    """
    Get detailed analysis of reading patterns in the conversation.
    
    Args:
        conversation: The conversation to analyze
        
    Returns:
        ReadingAnalysis with reading pattern details
    """
    grader = EfficientReadingGrader()
    return grader.analyze_reading_patterns(conversation)
