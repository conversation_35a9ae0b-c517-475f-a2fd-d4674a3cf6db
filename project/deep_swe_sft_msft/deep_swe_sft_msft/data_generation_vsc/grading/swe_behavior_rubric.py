"""
SWE Behavior Rubric - Defines criteria for evaluating software engineering conversation quality.

This rubric focuses on identifying good vs bad SWE behavior patterns in conversations,
particularly around tool usage, file exploration patterns, and problem-solving approach.
"""

# Main SWE Behavior Rubric
SWE_BEHAVIOR_RUBRIC = """
Evaluate this software engineering conversation based on the following BINARY criteria. 
Each criterion must be evaluated as PASS or FAIL. ALL criteria must pass for a good conversation.

CRITERIA (ALL MUST PASS):

1. **Tool Call Reliability (criterion1)**
   - No tool call errors, all function calls are properly formatted and executed successfully
   - Any tool call syntax errors, parameter errors, JSON parsing errors are considered a FAIL
   - Execution failures are okay if they are handled properly
   - Should use runTests or proper test execution methods rather than calling pytest
   - When using runTests, avoid empty file input like "files": [] (a small portion is acceptable)
   - Uses the most appropriate tool for each task, avoids wrong tool choices (a small portion is acceptable).
   - Try to avoid using these discouraged commands: grep, find, ls, sed, cat, bash, oai. But use tools functions.xxx instead.

2. **File Exploration Efficiency (criterion2)**
   - overlap_reading: (read_file tool only) Multiple reads of the SAME file must NOT have overlapping line ranges unless justified.
     * Line range = [offset, offset+limit-1]; defaults: offset=1, limit=2000.
     * Overlap includes identical, partial, superset, or subset ranges.
     * Exceptions where overlap is acceptable, necessary and reasonable (e.g., re-examining code after gathering new context, verifying changes, or when earlier context was lost due to conversation length).

   - done_all_requests: The assistant must complete ALL tasks and actions requested by the user. INVALID if:
     * Any explicit user request is ignored or not attempted (e.g., "fix the bug AND add tests" but only bug is fixed).
     * Required subtasks are skipped (e.g., user asks to refactor code but assistant doesn't update related documentation/tests).
     * The assistant stops prematurely without completing all requested work or providing clear blockers.
     * Partial solutions are presented as complete without acknowledging remaining work.
     * User's specific requirements/constraints are not followed (e.g., "use regex" but assistant uses string methods instead).

3. **Focused Problem Solving (criterion3)**
   - Maintains clear focus on the problem throughout the conversation
   - Never gets distracted and avoids unnecessary context switching

4. **Reflection (criterion4)**
   - Model should reflect on its errors quickly and adjust its approach
   - If it makes a mistake, it should acknowledge it and correct its course of action

SPECIFIC FAILURE INDICATORS:
- Tool call syntax errors or JSON parsing failures
- Reading overlapping (read_file) line ranges in the same file with read_file unless justified exceptions apply:
  * Repeated reads of identical or very closely overlapping ranges without modification
  * Re-reading the same content without gathering new context or making changes
  * Reading overlapping ranges in quick succession without clear justification
- Using pytest directly instead of proper test execution methods (runTests)
- Choosing inappropriate tools for specific tasks
- Reading the same file content more than twice
- Jumping between unrelated files without purpose
- Making changes without proper analysis
- Ignoring error messages or test failures
- Not following systematic problem-solving approach
"""