# Conversation Grading System

This package provides a comprehensive grading system for evaluating software engineering conversation quality using **binary criteria evaluation**. It follows the `StageTeacherModel` pattern for consistency with the existing codebase.

## Overview

The grading system uses a **strict binary evaluation approach**:
- Each criterion is evaluated as **PASS** or **FAIL**
- **ALL criteria must pass** for a conversation to be considered "good"
- If **any criterion fails**, the conversation is automatically marked as "bad"
- This ensures only high-quality conversations that meet all SWE standards are accepted

## Key Components

1. **ConversationGrader** - Main grader class that evaluates conversations based on rubrics
2. **EfficientReadingGrader** - Specialized grader for analyzing file reading patterns
3. **SWE Behavior Rubrics** - Predefined binary rubrics for different evaluation scenarios  
4. **Utility Functions** - Simple interfaces for common grading tasks

## Binary Evaluation Logic

```python
# Conversation is good ONLY if ALL criteria pass
all_criteria_passed = True
for criterion, details in criterion_scores.items():
    if not details.get("passed", False):
        all_criteria_passed = False
        break

is_good_conversation = all_criteria_passed
```

## Quick Start

### Basic Usage

```python
from deep_swe_sft_msft.data_generation_vsc.grading import grade_conversation, SWE_BEHAVIOR_RUBRIC
from chat import Conversation

# Simple True/False evaluation
is_good = await grade_conversation(
    conversation=my_conversation,
    rubric_prompt=SWE_BEHAVIOR_RUBRIC,
    rubric_name="swe_behavior"
)
print(f"Conversation is {'good' if is_good else 'bad'}")
```

### Detailed Evaluation

```python
from deep_swe_sft_msft.data_generation_vsc.grading import evaluate_conversation_detailed, SWE_BEHAVIOR_RUBRIC

evaluation = await evaluate_conversation_detailed(
    conversation=my_conversation,
    rubric_prompt=SWE_BEHAVIOR_RUBRIC,
    rubric_name="swe_behavior"
)

print(f"Overall Score: {evaluation.overall_score:.2f} (fraction of criteria passed)")
print(f"Is Good: {evaluation.is_good_conversation}")
print(f"Feedback: {evaluation.overall_feedback}")

for criterion, details in evaluation.criterion_scores.items():
    passed = details.get('passed', False)
    status = "✅ PASS" if passed else "❌ FAIL"
    print(f"{criterion}: {status} - {details.get('explanation', '')}")
```

### Using the ConversationGrader Class

```python
from deep_swe_sft_msft.data_generation_vsc.grading import ConversationGrader, SWE_BEHAVIOR_RUBRIC

grader = ConversationGrader()
evaluation = await grader.evaluate_conversation(
    conversation=my_conversation,
    rubric_prompt=SWE_BEHAVIOR_RUBRIC,
    rubric_name="swe_behavior"
)
```

### Using the EfficientReadingGrader

```python
from deep_swe_sft_msft.data_generation_vsc.grading import EfficientReadingGrader

reading_grader = EfficientReadingGrader()
analysis = reading_grader.analyze_conversation(my_conversation)

print(f"Is Efficient: {analysis.is_efficient}")
print(f"Total File Reads: {analysis.total_file_reads}")
print(f"Unique Files Read: {analysis.unique_files_read}")
print(f"Efficiency Score: {analysis.efficiency_score:.2f}")
print(f"Files with Overlaps: {analysis.files_with_overlaps}")
```

## Available Rubrics

### 1. SWE_BEHAVIOR_RUBRIC (Main Rubric)
The main rubric for evaluating software engineering conversations using **binary criteria**:

- **Tool Call Reliability (criterion1)** - PASS: No tool call errors, uses appropriate tools like runTests instead of pytest; FAIL: Syntax/parsing errors, wrong tool choices
- **File Exploration Efficiency (criterion2)** - PASS: No overlapping file reads, completes all user requests; FAIL: Redundant reading patterns, incomplete tasks  
- **Focused Problem Solving (criterion3)** - PASS: Clear focus throughout; FAIL: Context switching or lack of direction
- **Reflection (criterion4)** - PASS: Quickly reflects on errors and adjusts approach; FAIL: Doesn't learn from mistakes

**Evaluation Rule**: Conversation is "good" ONLY if ALL 4 criteria PASS.

This is the only rubric currently available. The rubric includes specific guidance on:
- Avoiding discouraged terminal commands (grep, find, ls, sed, cat, bash, oai)
- Using VSCode tools appropriately (read_file, runTests, etc.)
- Efficient file reading patterns without unnecessary overlaps
- Systematic problem-solving approach

## Integration with Data Generation Pipeline

### Filtering Generated Conversations

```python
from deep_swe_sft_msft.data_generation_vsc.grading import ConversationGrader, SWE_BEHAVIOR_RUBRIC

async def filter_high_quality_conversations(conversations, min_score=0.7):
    grader = ConversationGrader()
    good_conversations = []
    
    for conversation in conversations:
        evaluation = await grader.evaluate_conversation(
            conversation=conversation,
            rubric_prompt=SWE_BEHAVIOR_RUBRIC,
            rubric_name="swe_behavior"
        )
        
        if evaluation.is_good_conversation and evaluation.overall_score >= min_score:
            good_conversations.append({
                'conversation': conversation,
                'grading_metadata': evaluation
            })
    
    return good_conversations
```

### Batch Processing

```python
from deep_swe_sft_msft.data_generation_vsc.grading import ConversationGrader, SWE_BEHAVIOR_RUBRIC

async def grade_conversation_batch(conversations):
    grader = ConversationGrader()
    results = []
    
    for conversation in conversations:
        evaluation = await grader.evaluate_conversation(
            conversation=conversation,
            rubric_prompt=SWE_BEHAVIOR_RUBRIC,
            rubric_name="swe_behavior"
        )
        results.append(evaluation)
    
    # Calculate statistics
    good_count = sum(1 for r in results if r.is_good_conversation)
    avg_score = sum(r.overall_score for r in results) / len(results)
    
    print(f"Good conversations: {good_count}/{len(conversations)} ({good_count/len(conversations)*100:.1f}%)")
    print(f"Average score: {avg_score:.2f}")
    
    return results
```

## Criteria Details

### Tool Call Reliability (criterion1)
- **Good**: All function calls properly formatted and executed, uses runTests instead of pytest, avoids discouraged commands (grep, find, ls, sed, cat, bash, oai)
- **Bad**: Frequent JSON parsing errors, syntax errors, parameter mismatches, using discouraged terminal commands

### File Exploration Efficiency (criterion2)  
- **Good**: Each file/region read only when necessary, completes ALL user requests, no overlapping read_file calls
- **Bad**: Repeatedly viewing overlapping content, ignoring user requests, incomplete task execution

### Focused Problem Solving (criterion3)
- **Good**: Maintains clear focus on the stated problem throughout
- **Bad**: Frequent context switching, lacks direction, gets distracted

### Reflection (criterion4)
- **Good**: Quickly acknowledges errors and adjusts approach, learns from mistakes
- **Bad**: Repeats the same mistakes, doesn't adapt to feedback or errors

## Configuration

The grader uses the same configuration as `StageTeacherModel`:

```python
from deep_swe_sft_msft.data_generation_vsc.constants import TEACHER_RENDERER

# Default configuration
grader = ConversationGrader(
    renderer_name=TEACHER_RENDERER,  # From constants.py
    retries=3,
    reward_multiplier=32
)
```

## Error Handling

The grader includes robust error handling:

- **Retries**: Automatic retry logic for transient failures
- **Graceful degradation**: Returns failed evaluation objects when grading fails
- **Detailed error messages**: Clear feedback when something goes wrong

## Custom Rubrics

You can create custom rubrics by following this format:

```python
CUSTOM_RUBRIC = """
Evaluate this conversation based on the following criteria:

CRITERIA:

1. **Custom Criterion (criterion1)**
   - PASS: Excellent performance on this aspect
   - FAIL: Poor performance on this aspect

2. **Another Criterion (criterion2)**
   - PASS: Meets requirements
   - FAIL: Does not meet requirements

Conversation is "good" if ALL criteria PASS.
"""

# Use with grader
evaluation = await grade_conversation(
    conversation=my_conversation,
    rubric_prompt=CUSTOM_RUBRIC,
    rubric_name="custom"
)
```

## Testing

Run the test script to verify functionality:

```bash
cd data_generation_vsc/grading/
python test_grader.py
```

Note: Tests require proper infrastructure setup (BusTokenCompleter, etc.) to run.

## API Reference

### ConversationGrader

#### `__init__(renderer_name, retries=3, reward_multiplier=32)`
Initialize the grader with configuration options.

#### `async evaluate_conversation(conversation, rubric_prompt, rubric_name)`
Evaluate a conversation and return detailed results.

### EfficientReadingGrader

#### `__init__()`
Initialize the reading pattern grader.

#### `analyze_conversation(conversation)`
Analyze a conversation for efficient file reading patterns and return ReadingAnalysis.

### Utility Functions

#### `async grade_conversation(conversation, rubric_prompt, rubric_name)`
Simple function that returns True/False for conversation quality.

#### `async evaluate_conversation_detailed(conversation, rubric_prompt, rubric_name)`
Returns full ConversationEvaluation object with detailed scores.

### ConversationEvaluation

Data class containing evaluation results:
- `is_good_conversation: bool`
- `overall_score: float` (0.0 to 1.0)
- `criterion_scores: Dict[str, Dict[str, Any]]`
- `overall_feedback: str`
- `rubric_name: str`

### ReadingAnalysis

Data class containing reading pattern analysis:
- `is_efficient: bool`
- `total_file_reads: int`
- `unique_files_read: int`
- `files_with_overlaps: list[str]`
- `files_with_redundancy: list[str]`
- `efficiency_score: float`
- `reads_per_file: dict[str, int]`

## Performance Considerations

- **Concurrency**: The grader is async-first and can handle concurrent evaluations
- **Token limits**: Conversations are automatically truncated to fit model context limits
- **Caching**: Consider implementing caching for repeated evaluations of the same conversations
- **Batch size**: For large-scale grading, process in batches to manage memory usage

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure all dependencies are installed and use the full module path `deep_swe_sft_msft.data_generation_vsc.grading`
2. **Authentication errors**: Verify BusTokenCompleter configuration with correct BUS_USER and BUS_TOPIC
3. **Timeout errors**: Increase retry count or check network connectivity
4. **JSON parsing errors**: Usually indicates model output format issues
5. **Constants import errors**: Import TEACHER_RENDERER from `deep_swe_sft_msft.data_generation_vsc.constants`

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```
