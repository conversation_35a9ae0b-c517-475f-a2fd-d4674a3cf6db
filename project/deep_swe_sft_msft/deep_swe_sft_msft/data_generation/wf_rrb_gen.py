"""
Multi-agent data collection system - refactored for better organization.

This file shows the improved structure with functions organized by purpose,
making the code more readable and maintainable.
"""

import asyncio
import copy
import json
import os
import random
import re
import subprocess
import time
import uuid
from collections import defaultdict
from contextlib import closing
from dataclasses import asdict, dataclass
from datetime import datetime
from functools import cache
from pathlib import Path
from typing import Any, Callable, Coroutine, Dict, List, Optional, Tuple, Unpack, cast

import birder
import chat
import chz
import numpy as np
import structlog
from bus_token_completer import BusTokenCompleter
from caas.commands import (
    BashScript,
    DownloadFileFromContainer,
    Exec,
    PythonProgram,
    RawExec,
    UploadFile,
)
from caas.protocol import NetworkMode, VolumeMount
from caas_tool.caas_container import CaasContainer
from caas_tool.caas_container_tool import CaasContainerTool
from chat import Conversation, ConversationMetadata, Message, Role, chat
from chat.render.common import render_content, system_content_render
from chat.render.renderer_registry import get_renderer
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from chat.tools import DefaultMultiToolKit, take_one_step_with_tools
from harmony_gym.env.convo_roller import Too<PERSON><PERSON><PERSON><PERSON><PERSON>oller
from harmony_gym.utils.types import (
    HarmonyRewardResultsForSamePrefixConvo,
    HarmonyRolloutPerStepResult,
    HarmonyRolloutResult,
    HarmonyRolloutResultsForSamePrefixConvo,
    LoggingResultQueue,
    RewardResultQueue,
    RolloutEnvidFinishQueue,
    RolloutResultQueue,
    RolloutVariant,
)
from legacy_rest_token_completer import LegacyRestTokenCompleter
from message_completer import TokenMessageCompleter
from message_completer.message_completer import MessageCompleter, ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter
from mini.metrics import capture_metrics, metrics
from qstar.common.utils import read_jsonl, write_jsonl
from smokey import Smokey
from token_completer import CompleterBackend, TokenCompleter
from token_completer.backend import CompleterBackend
from token_completer.retry_utils import NoRetryConfig
from token_completer.token_completer import TokenCompleter
from tool_use_free.tool_registry import create_toolkit
from turn_completer import SingleMessageTurnCompleter

logger = structlog.stdlib.get_logger(component=__name__)

# ============================================================================
# IMPORTS AND FALLBACKS
# ============================================================================

# Define systemMessage locally if not available from imports
systemMessage = "You are a helpful AI assistant specialized in software engineering tasks."

from berry_rfs.mrfs_eval_utils import passes_tests as test_parsing_py
from berry_rfs.mrfs_eval_utils import try_run_command
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.javascript.datasets.mrfs_setup import (
    setup_repo_with_gt_metadata as setup_repo_js,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.javascript.datasets.mrfs_setup_utils import (
    passes_tests as test_parsing_js,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.mrfs_setup import (
    clean_up_repo_name,
    get_repo_directory,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.mrfs_setup import (
    setup_repo_with_gt_metadata as setup_repo_py,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.typescript.datasets.mrfs_setup import (
    setup_repo_with_gt_metadata as setup_repo_ts,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.typescript.datasets.mrfs_setup_utils import (
    passes_tests as test_parsing_ts,
)
from deep_swe_msft.swe_bench_train_v2_padawan_v2.setup.setup import swe_bench_v2_setup_fn_internal
from deep_swe_msft.tools.caas_padawan_tool import DeepSWECaasPadawanTool
from deep_swe_msft.tools.get_coreutils import setup_coreutils
from deep_swe_msft.tools.utils import PDW_MNT
from deep_swe_sft_msft.data_generation.constants import (
    BUS_TOPIC,
    BUS_USER,
    CAAS_ENDPOINT,
    PADAWAN_TOOL_ERROR_MSGS,
    STUDENT_RENDERER,
    TEACHER_RENDERER,
)
from deep_swe_sft_msft.data_generation.corrector import Corrector, has_tool_error
from deep_swe_sft_msft.data_generation.grading import SWE_BEHAVIOR_RUBRIC, ConversationGrader
from deep_swe_sft_msft.data_generation.stage_teacher import StageEvaluation, StageTeacherModel
from deep_swe_tasks.task_metadata import DeepSWETaskMetadata

# Import with fallbacks for specialized tools
from qstar.common.tools import constraint_machine_spec, renderer_worker

# Problem Statement: $PROBLEM_STATEMENT

# Create a general SWE plan in markdown format explaining about how you are going to approach the SWE problem as few steps as possible.
# - Explore the repository, read files and locate the problem.
# - Don't run tests or write code here. Planning only.
# - Use as few steps as possible.
# And calling `report_progress` to report your analysis and plan in a markdown task list.

SWE_STAGE_DEFINITIONS = {
    "stage_1_analysis": {
        "name": "Problem Analysis and Planning",
        "max_steps": 20,
        "description": """
**Stage 1: Planning**

Create a general SWE plan in markdown format explaining about how you are going to approach a SWE problem as few steps as possible.
e.g., environment setup -> reproduce the problem -> implement the solution -> finalize the PR report.
And calling `report_progress` to report your analysis and plan in a markdown task list.
        """,
        "criteria": [
            # "Provided a clear root cause analysis",
            "criteria1: Called report_progress with analysis and plan summary in a markdown task list",
            "criteria2: The commitMessage in `report_progress` is in markdown format",
            "criteria3: never modified any files in this stage, such as call `str_replace_editor`",
            "criteria4: never run test in this stage",
            "criteria5: never fix or implemented any code in this stage; revert changes are also considered as modifications",
        ],
    },
    "stage_2_environment": {
        "name": "Environment Setup and Validation",
        "max_steps": 30,
        "description": """
**Stage 2: Environment Setup and Validation**
Your task is to set up the development environment and validate it's working.

Tasks to complete:
1. Build and set up the environment for the project.
2. Install the project in development mode if applicable (e.g., `pip install -e .`)
3. Install any missing tools or packages using appropriate package managers (apt, npm, pip, etc.)
4. Nicely and successfully build, e.g., you need to run both `npm install` and `npm run build` and `npm install` before `npm run build`.

End this stage by calling `report_progress` with environment setup status in a markdown task list.
        """,
        "criteria": [
            "criteria1: Must run set up and build the project environment to check for basic setup issues; tests are considered part of the build",
            "criteria2: For Node/JavaScript/TypeScript projects, must run `npm install` and `npm run build`; 'npm install' must be run before 'npm run build'",
            "criteria3: Installed the project in development mode if applicable (e.g., `pip install -e .`)",
            "criteria4: Installed missing packages using appropriate package managers",
            "criteria5: Called report_progress with environment setup status in a markdown task list",
        ],
    },
    "stage_3_reproduction": {
        "name": "Problem Reproduction",
        "max_steps": 30,
        "description": """
**Stage 3: Problem Reproduction**
Problem Statement: $PROBLEM_STATEMENT

Your task is to create or run tests that reproduce the reported problem.

Tasks to complete:
1. Look for existing test files that might demonstrate the issue; if the existing test files reproduce the issue, you can use them directly.
2. If existing test files didn't reproduce the issue, write your own tests to reproduce the problem.
    - If test suites are available, create tests into existing test suite files to reproduce the issue instead of creating new test files
    - If no test suites are available, create new test files to reproduce the problem
4. Prefer to create/modify test files and then run test files for reproduction instead of testing in command line, which is not good for debugging.
5. The test should fail initially, confirming the bug exists

End this stage by calling `report_progress` with reproduction results in a markdown task list.
        """,
        "criteria": [
            "criteria1: If the existing test files reproduce the issue, the model should use them directly and must run them",
            "criteria2: If the existing test files didn't reproduce the issue, and if test suites are available, the model should write tests into existing files to reproduce the issue instead of creating new test files",
            "criteria3: If the existing test files didn't reproduce the issue, and if no test suites are available, create new test files to reproduce the problem",
            "criteria4: If need to add tests, model should write/modify test files and then run test files for reproduction instead of testing in command line, which is not good for debugging.",
            "criteria5: Must run tests to check for issue reproduction",
            "criteria6: Called report_progress with reproduction status in a markdown task list",
        ],
    },
    "stage_4_implementation": {
        "name": "Solution Implementation",
        "max_steps": 100,
        "description": """
**Stage 4: Solution Implementation**
Problem Statement: $PROBLEM_STATEMENT

Your task is to implement the solution to solve the problem.

Tasks to complete:
1. Implement the fix
2. Use proper coding practices and maintain code quality
3. Test your changes incrementally as you implement them
4. Handle any build errors that arise during implementation
5. Use file system tools appropriately, avoid overusing bash tool
6. Test after your fix; retry build after your fix to make sure the project is buildable after fix.
7. The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items.

Properly call `report_progress` with implementation details when you made stage changes in a markdown task list.
        """,
        "criteria": [
            "criteria1: Made appropriate changes to the correct files",
            "criteria2: Test after fix; retry build after fix to make sure the project is buildable after fix",
            "criteria3: Resolved any build errors that occurred",
            "criteria4: Used appropriate tools (not overusing bash)",
            "criteria5: Called `report_progress` in a markdown task list with implementation details",
            "criteria6: The whole solution should never change `run_tests.sh` file.",
            "criteria7: The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items.",
        ],
    },
    "stage_5_finalization": {
        "name": "Final Documentation and PR Report",
        "max_steps": 10,
        "description": """
**Stage 5: Final Documentation and PR Report**
Your task is to create comprehensive documentation of your solution.

Tasks to complete:
1. Create a detailed PR description explaining what was changed and why
2. Document the fix with clear before/after behavior
3. List all files that were modified
4. Include the issue number in the PR description in format: `Fixes #{issue_number}` if applicable
5. Include testing information and any new tests created
6. Format as: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>
7. Don't use `report_progress` in this stage. Just write the PR description and report it.
        """,
        "criteria": [
            "criteria1: Created a comprehensive PR description in format: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>",
            "criteria2: PR description includes the issue number in format: `Fixes #{issue_number}` if applicable",
            "criteria3: Don't use `report_progress` in this stage",
        ],
    },
}

# ============================================================================
# DATA MODELS AND TYPE DEFINITIONS
# ============================================================================


@dataclass
class StageData:
    """Data for a single stage execution"""

    stage_name: str
    attempt_number: int
    student_messages: List[Dict]
    teacher_evaluation: Optional[StageEvaluation] = None
    stage_completed: bool = False


@dataclass
class EpisodeData:
    """Complete episode data for saving to JSONL"""

    instance_id: str
    problem_statement: str
    docker_image: str
    stages: List[StageData]
    total_stages: int
    completion_status: str  # "completed", "max_attempts", "error"
    timestamp: str
    metadata: Dict = None
    final_conversation: List[Dict] = None  # Clean conversation without stage prompts


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================


def write_messages_to_file(messages, file_name="aaa", folder="log"):
    """Write messages to a file"""
    if not isinstance(messages, list):
        messages = [messages]
    os.makedirs(folder, exist_ok=True)
    with open(os.path.join(folder, file_name), "w") as f:
        for m in messages:
            if hasattr(m, "model_dump_json"):
                f.write(m.model_dump_json() + "\n")
            else:
                f.write(json.dumps(m, default=str) + "\n")


def extract_clean_conversation(conversation_messages):
    """Extract clean conversation without stage prompts, keeping only initial prompt and responses"""
    clean_messages = []

    for msg in conversation_messages:
        # Skip stage-specific prompts (look for stage keywords in content)
        content = str(msg.content)
        if any(stage_key in content for stage_key in ["**Stage ", "End this stage by calling"]):
            continue

        # Keep the message if it's not a stage prompt
        clean_messages.append(msg)

    return clean_messages


def create_clean_conversation_with_problem(conversation_messages, problem_statement):
    """
    Create a clean conversation object with the problem statement as the first user message,
    followed by all non-stage-specific messages from the conversation.

    Args:
        conversation_messages: List of messages from the current conversation
        problem_statement: The original problem statement to include as first user message

    Returns:
        chat.Conversation: Clean conversation object
    """
    clean_messages = []

    error_msgs = PADAWAN_TOOL_ERROR_MSGS

    # Find the system message first
    system_message = None
    for msg in conversation_messages:
        if msg.author.role == Role.SYSTEM:
            system_message = msg
            break

    # Add system message if found
    if system_message:
        clean_messages.append(system_message)

    # Add the problem statement as the first user message
    problem_user_message = chat.Message.user(f"Problem: {problem_statement}")
    clean_messages.append(problem_user_message)

    # Process messages and identify error patterns
    messages_to_process = []
    for msg in conversation_messages:
        # Skip system messages (already added)
        if msg.author.role == Role.SYSTEM:
            continue

        # Skip stage-specific prompts (look for stage keywords in content)
        content = str(msg.content)
        if any(stage_key in content for stage_key in ["**Stage ", "End this stage by calling"]):
            continue

        messages_to_process.append(msg)

    # Filter out tool messages with errors and their preceding assistant messages
    i = 0
    while i < len(messages_to_process):
        msg = messages_to_process[i]

        # Check if this is a tool message with error content
        is_error_tool = False
        if msg.author.role == Role.TOOL:
            content = str(msg.content)
            if any(error_msg in content for error_msg in error_msgs):
                is_error_tool = True

        if is_error_tool:
            # Skip this tool message with error
            # Also check if the previous message was from assistant and skip it too
            if clean_messages and clean_messages[-1].author.role == Role.ASSISTANT:
                # Remove the previous assistant message
                clean_messages.pop()
            # Skip the current error tool message (don't add it to clean_messages)
            i += 1
            continue

        # Keep the message if it's not a stage prompt or error tool
        clean_messages.append(msg)
        i += 1

    # Post-process assistant messages to modify channel and end_turn for intermediate stages
    # Keep the last message (which is always from assistant) as channel='final' and end_turn=True
    for i, msg in enumerate(clean_messages):
        if msg.author.role == Role.ASSISTANT:
            # Only modify non-final assistant messages (all except the last message)
            if i < len(clean_messages) - 1 and hasattr(msg, "channel") and msg.channel == "final":
                # Create a copy and modify properties for continuous conversation flow
                modified_msg = copy.deepcopy(msg)
                modified_msg.channel = "analysis"
                modified_msg.end_turn = False
                clean_messages[i] = modified_msg
            if i == len(clean_messages) - 1:
                # Ensure the last assistant message is final and ends the turn
                modified_msg = copy.deepcopy(msg)
                modified_msg.channel = "final"
                modified_msg.end_turn = True
                clean_messages[i] = modified_msg

    # Create and return the clean conversation
    return chat.Conversation(messages=clean_messages)


def write_to_file_text(text, file_name="aaa", folder="log"):
    """Write text to a file"""
    os.makedirs(folder, exist_ok=True)
    with open(os.path.join(folder, file_name), "w") as f:
        f.write(text)


def safe_message_dump(msg):
    """Safely convert message to dict format with UUID handling"""
    if hasattr(msg, "model_dump"):
        try:
            return msg.model_dump()
        except TypeError:
            # Handle UUID and other non-serializable objects
            return json.loads(json.dumps(msg.model_dump(), default=str))
    elif hasattr(msg, "dict"):
        try:
            return msg.dict()
        except TypeError:
            # Handle UUID and other non-serializable objects
            return json.loads(json.dumps(msg.dict(), default=str))
    else:
        return str(msg)


def get_render_constrained_extension(renderer_name: str):
    """Get the constrained extension for the renderer."""

    harmony_stop_tokens_utils = renderer_worker.HarmonyStopTokensUtils(
        harmony_renderer_name=renderer_name,
        enable_special_stop_token=False,
    )

    extension = constraint_machine_spec.constraint_machine_extension(
        harmony_renderer_name=renderer_name,
        encoding_name="orion_200k",
        final_token_sequences=harmony_stop_tokens_utils.constraint_machine_final_token_sequences(),
        json_eot_tokens=harmony_stop_tokens_utils.constrained_sampling_json_eot_tokens(),
        tools=(
            "functions.str_replace_editor",
            "functions.report_progress",
            "functions.bash",
            "functions.write_bash",
            "functions.read_bash",
            "functions.stop_bash",
        ),
        channels=("analysis", "final"),
        response_format_names=(),
        enable_json_constraining=False,
        none_channel_allowed=False,
    )
    return extension


def truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    """Truncate a string to a specified token limit"""
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"


def get_processed_instances(outputs_folder):
    """Get list of already processed instances"""
    processed_instances = set()
    if os.path.exists(outputs_folder):
        for file in os.listdir(outputs_folder):
            if file.endswith(".jsonl") and file != "all_episodes.jsonl":
                instance_id = file.replace(".jsonl", "")
                processed_instances.add(instance_id)
    return processed_instances


def load_from_jsonl(jsonl_path):
    """Load dataset from a jsonl file"""
    dataset = []
    with open(jsonl_path, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if line:  # Skip empty lines
                try:
                    data = json.loads(line)
                    dataset.append(data)
                except json.JSONDecodeError as e:
                    print(f"Error parsing line in {jsonl_path}: {e}")
                    continue
    return dataset


# ============================================================================
# FILE I/O AND FORMATTING FUNCTIONS
# ============================================================================


def save_episode_data(episode_data: EpisodeData, output_dir: str):
    """Save episode data to JSONL format and readable conversation format"""
    os.makedirs(output_dir, exist_ok=True)

    # Convert to dict and save as JSONL
    episode_dict = asdict(episode_data)

    # Save individual episode file
    episode_file = os.path.join(output_dir, f"{episode_data.instance_id}.jsonl")
    with open(episode_file, "w") as f:
        f.write(json.dumps(episode_dict, default=str) + "\n")

    # Append to master JSONL file
    master_file = os.path.join(output_dir, "all_episodes.jsonl")
    with open(master_file, "a") as f:
        f.write(json.dumps(episode_dict, default=str) + "\n")

    # Save readable conversation format
    # save_final_conversation(episode_data, output_dir)


# ============================================================================
# STAGE-BASED ROLLOUT AND PROCESSING FUNCTIONS
# ============================================================================


def process_rrb_problem_statement(instance) -> str:
    """Extract problem statement before 'until the tests pass'"""
    # Find the position of "until the tests pass" and extract everything before it
    repo_name = clean_up_repo_name(instance["metadata"].get("repo_name"))
    root_repo = get_repo_directory(repo_name)
    problem_statement = instance["problem"]

    remove_phrase = "Please make sure the updated repo can pass unit tests by running `bash run_tests.sh`. Please do NOT modify the `run_tests.sh` file."
    problem_statement = (
        problem_statement.replace(remove_phrase, "")
        + f"\nRepo Path: `{root_repo}`. Don't explore unrelated folders/files such as /workspace and /project"
        + f"\nIssue Number: #{random.randint(1000, 9999)}"
    )
    return problem_statement, root_repo


# ============================================================================
# STAGE-BASED ROLLOUT AND PROCESSING FUNCTIONS
# ============================================================================


async def stage_based_rollout(
    instance, student_juice=1024, max_attempts_per_stage=3, outputs_dirname="outputs"
):
    """
    Stage-based rollout with student and teacher models
    """
    data_dict = {}
    # dataset: rrb
    metadata = instance["metadata"]
    instance_id = instance["unique_id"]
    problem_statement, root_repo = process_rrb_problem_statement(instance)
    docker_image = "actions-runner-terminal-server-padawan"  # 'aio'
    rrb_lang = metadata.get("lang", "python")

    # dataset: swe-bench-train-v2
    # metadata = instance['metadata']
    # metadata = DeepSWETaskMetadata.model_validate(metadata)
    # instance_id = f"{metadata.task.repo.replace('/', '__')}__{metadata.task.base_commit}"
    # problem_statement = instance["problem"] # may be different for different datasets
    # docker_image = metadata.docker_image

    # Initialize episode data
    episode_data = EpisodeData(
        instance_id=instance_id,
        problem_statement=problem_statement,
        docker_image=docker_image,
        stages=[],
        total_stages=len(SWE_STAGE_DEFINITIONS),
        completion_status="error",
        timestamp=datetime.now().isoformat(),
        metadata=metadata.dict()
        if hasattr(metadata, "dict")
        else dict(metadata)
        if hasattr(metadata, "__dict__")
        else {},
    )

    # Initialize Final Grader
    final_rollout_grader = ConversationGrader()

    container = None
    retries = 10

    for attempt in range(retries):
        try:
            # Build CaaS Container
            container = await CaasContainer.new(
                caas_endpoint=CAAS_ENDPOINT,
                image_name=docker_image,
                idle_ttl=1200,
                memory_limit="16g",
                cpu_limit="16",
                network=NetworkMode.BRIDGE,
                volume_mounts=PDW_MNT,
            )

            terminal_session = container.terminal_session
            caas_session = terminal_session.session

            # Setup repository
            print(f"Starting setting up repo {instance_id}")
            with capture_metrics(step=0):
                if rrb_lang == "python":
                    await setup_repo_py(
                        terminal_session=terminal_session,
                        gt_metadata=metadata,
                        random_drop_packages=True,
                    )
                elif rrb_lang == "typescript":
                    await setup_repo_ts(
                        terminal_session=terminal_session,
                        gt_metadata=metadata,
                        skip_install_packages=False,
                    )
                elif rrb_lang == "javascript":
                    await setup_repo_js(
                        terminal_session=terminal_session,
                        gt_metadata=metadata,
                        skip_install_packages=False,
                    )
            print(f"Finished setting up repo {instance_id}")

            print(f"Starting setting up coreutils in {instance_id}")
            if docker_image == "actions-runner-terminal-server-padawan":
                await setup_coreutils(
                    session=caas_session, datapoint={}, repo_root=root_repo, skip_tool_packages=True
                )
            else:
                await setup_coreutils(
                    session=caas_session,
                    datapoint={},
                    repo_root=root_repo,
                    skip_tool_packages=False,
                )
            print(f"Finished setting up coreutils in {instance_id}")

            # setup swe-bench-train-v2
            # print(f"Starting setting up repo {instance_id}")
            # await swe_bench_v2_setup_fn_internal(terminal_session=terminal_session, metadata=metadata)
            # print(f"Finished setting up repo {instance_id}")

            # Set up tools
            tool = DeepSWECaasPadawanTool(
                container=container,
                terminal_emulator_path="teammate_service.termites.lean_terminal:LeanTerminal",
            )
            toolkit = DefaultMultiToolKit(tools=[tool])
            tools_section = {tool.name: tool.instruction()}

            # Set up models
            renderer_name = STUDENT_RENDERER
            renderer = get_renderer(renderer_name)
            extension = get_render_constrained_extension(renderer_name)

            # Initialize teacher model
            teacher = StageTeacherModel(TEACHER_RENDERER, retries=retries)

            # Initialize corrector model
            corrector = Corrector(TEACHER_RENDERER, retries=retries)

            # Student model configuration
            student_bus_config = BusTokenCompleter.Config(
                topic_mode_or_user=BUS_USER,
                topic_or_snapshot=BUS_TOPIC,
            )

            completion_params = {"temperature": 1}
            if extension is not None:
                completion_params["extensions"] = [extension]

            student_message_completer_config = TokenMessageCompleter.Config(
                token_completer_config=student_bus_config,
                completion_params=completion_params,
                renderer=renderer,
            )

            student_message_completer = student_message_completer_config.build()

            # Initialize base conversation with problem statement only
            # base_user_prompt = f"Problem: {problem_statement}\n"
            # base_user_prompt += "Follow the stages below to approach the problem step by step.\n"
            system_msg = chat.Message.system(
                model_identity_desc=systemMessage,
                tools_section=tools_section,
                channel_config=chat.SystemChannelConfig(
                    valid_channels=("analysis", "final"), channel_required=True
                ),
                metadata=chat.SystemContentMetadata(reward_multiplier=student_juice),
            )

            base_convo = chat.Conversation(
                messages=[
                    system_msg,
                    # chat.Message.user(base_user_prompt),
                ]
            )

            valid_nontool_recipients = {
                m.author.display_name() for m in base_convo.messages if m.author.role != Role.TOOL
            }
            valid_recipients = (
                valid_nontool_recipients
                | (set([tool.name for tool in toolkit.tools]) if toolkit else set())
                | {"all"}
            )

            # Execute stages sequentially
            current_convo = base_convo
            cumulative_max_steps = 0

            for stage_key, stage_def in SWE_STAGE_DEFINITIONS.items():
                stage_name = stage_def["name"]
                stage_description = stage_def["description"]
                stage_criteria = stage_def["criteria"]
                stage_max_steps = stage_def.get("max_steps", 20)  # Default to 20 if not specified

                # Calculate cumulative max steps (current + all previous stages)
                cumulative_max_steps += stage_max_steps

                stage_prompt = f"\n{stage_description}"
                stage_prompt = stage_prompt.replace("$PROBLEM_STATEMENT", problem_statement)

                print(f"\n{'='*60}")
                print(
                    f"Starting Stage: {stage_name} (max_steps: {stage_max_steps}, cumulative: {cumulative_max_steps})"
                )
                print(f"{'='*60}")

                stage_completed = False
                attempt_number = 0

                while not stage_completed and attempt_number < max_attempts_per_stage:
                    attempt_number += 1
                    print(f"\nStage {stage_name} - Attempt {attempt_number}")

                    # Add stage prompt to conversation
                    stage_convo = current_convo.with_suffix(chat.Message.user(stage_prompt))
                    # print(f"Stage convo: {stage_convo}")

                    stage_convo.metadata.header_yields_budget_total_override = cumulative_max_steps
                    stage_convo.metadata.header_yields_budget_for_action = cumulative_max_steps

                    # Execute stage with student model
                    student_messages, final_convo = await execute_stage_with_student(
                        student_message_completer,
                        stage_convo,
                        toolkit,
                        valid_recipients,
                        retries,
                        max_steps=stage_max_steps,
                        stage_key=stage_key,
                        corrector=corrector,
                    )

                    if not student_messages:
                        print(f"Failed to get student response for stage {stage_name}")
                        break

                    # Teacher evaluates the stage
                    stage_evaluation = await teacher.evaluate_stage(
                        stage_name, stage_criteria, final_convo
                    )

                    # Record stage data
                    stage_data = StageData(
                        stage_name=stage_name,
                        attempt_number=attempt_number,
                        student_messages=[safe_message_dump(msg) for msg in student_messages],
                        teacher_evaluation=stage_evaluation,
                        stage_completed=stage_evaluation.stage_complete,
                    )
                    episode_data.stages.append(stage_data)

                    if stage_evaluation.stage_complete:
                        print(f"✅ Stage {stage_name} completed successfully!")
                        stage_completed = True
                        current_convo = final_convo
                    else:
                        print(
                            f"❌ Stage {stage_name} not completed. Feedback: {stage_evaluation.overall_feedback}"
                        )
                        if not stage_evaluation.should_resample:
                            print(f"Teacher advised not to resample. Moving to next stage.")
                            stage_completed = True
                            current_convo = final_convo
                        else:
                            print(f"Resampling stage {stage_name}...")
                            # Reset to the state before this stage for resampling
                            continue

                if not stage_completed:
                    print(
                        f"❌ Failed to complete stage {stage_name} after {max_attempts_per_stage} attempts"
                    )
                    episode_data.completion_status = "max_attempts"
                    # Raise exception to trigger instance retry
                    raise RuntimeError(
                        f"Stage {stage_name} failed to complete after {max_attempts_per_stage} attempts"
                    )

            # If all stages completed successfully
            episode_data.completion_status = "completed"

            # Evaluate tests
            ok, d = await try_run_command(
                terminal_session,
                f"source ~/.bashrc; cd {root_repo}; {metadata.get('exec_cmd', 'pytest')}",
                120,
            )
            if rrb_lang == "python":
                test_pass = test_parsing_py("py", d["output"])
            elif rrb_lang == "typescript":
                test_results = metadata.get("original_test_results", {})
                test_pass = test_parsing_ts("typescript", d["output"], test_results=test_results)
            elif rrb_lang == "javascript":
                test_results = metadata.get("original_test_results", {})
                test_pass = test_parsing_js("javascript", d["output"], test_results=test_results)

            if ok and d["output"] and test_pass:
                passed = True
            else:
                passed = False
            print(f"Tests output: {d['output']}...")  # Print first 200 chars of output
            print(f"Tests passed: {passed}")

            # Extract clean conversation (without stage prompts but with problem statement)
            clean_convo = create_clean_conversation_with_problem(
                current_convo.messages, problem_statement
            )
            write_to_file_text(str(clean_convo), f"{instance_id}.txt", folder="sft_data/clean")
            write_to_file_text(str(current_convo), f"{instance_id}.txt", folder="sft_data/original")
            # episode_data.final_conversation = [safe_message_dump(msg) for msg in clean_convo.messages]

            data_dict["prompt"] = system_msg.model_dump(mode="json")
            data_dict["conversation"] = clean_convo.model_dump(mode="json")
            data_dict["accuracy"] = passed

            # Final Grader Check
            evaluation = await final_rollout_grader.evaluate_conversation(
                clean_convo, SWE_BEHAVIOR_RUBRIC, "format_test"
            )
            is_good_conversation = evaluation.is_good_conversation

            if container:
                await container.teardown()

            # Only return data if tests passed
            if passed and is_good_conversation:
                print(f"✅ Instance {instance_id} completed successfully with passing tests")
                return data_dict
            else:
                print(f"❌ Instance {instance_id} completed but tests failed - skipping")
                return None

        except Exception as e:
            if attempt < retries - 1:
                print(f"Rollout attempt {attempt + 1} failed: {e}")
                if container:
                    try:
                        await container.teardown()
                    except:
                        pass
                await asyncio.sleep(4)
                continue
            else:
                print(f"❌ Instance {instance_id} failed after {retries} attempts - skipping")
                if container:
                    try:
                        await container.teardown()
                    except:
                        pass
                return None  # Skip failed instances instead of raising exception


async def execute_single_step(
    message_completer, current_convo, toolkit, valid_recipients, retries, is_last_step
):
    """
    Execute a single step in the conversation.

    Returns:
        tuple: (student_messages_to_append, tool_messages_to_append, should_continue)
    """
    # Student generates response
    new_messages = await generate_student_response(
        message_completer, current_convo, is_last_step, retries
    )

    if not new_messages:
        return [], [], False

    # Create a copy of conversation with student messages for tool execution
    step_convo = current_convo.with_suffix(*new_messages)
    last_message = new_messages[-1]

    # Check termination conditions
    should_continue = not (
        is_last_step
        or last_message.end_turn
        or (
            last_message.recipient not in valid_recipients
            and last_message.recipient.split(".")[0] not in valid_recipients
        )
    )

    tool_messages = []
    if should_continue:
        # Execute tools if needed using the copied conversation
        async for tool_message in take_one_step_with_tools(
            prefix_convo=step_convo.prefix(-1),
            message=last_message,
            toolkit=toolkit,
        ):
            tool_messages.append(tool_message)
            # Update the step conversation for subsequent tool calls
            if step_convo.messages[-1].id == tool_message.id:
                step_convo.messages[-1] = tool_message
            else:
                step_convo = step_convo.with_suffix(tool_message)

    return new_messages, tool_messages, should_continue


async def execute_stage_with_student(
    message_completer,
    stage_convo,
    toolkit,
    valid_recipients,
    retries,
    max_steps=20,
    stage_key="cur_stage",
    corrector=None,
):
    """Execute a single stage with the student model"""
    current_convo = stage_convo
    student_messages = []

    for step in range(max_steps):
        is_last_step = step == max_steps - 1

        # Execute single step
        step_student_messages, step_tool_messages, should_continue = await execute_single_step(
            message_completer, current_convo, toolkit, valid_recipients, retries, is_last_step
        )
        # print(step_tool_messages[-1].json())
        if not step_student_messages:
            break

        # Check for tool errors and apply corrector if needed
        if corrector and step_tool_messages:
            # Check if there are any tool errors
            error_tool_messages = [msg for msg in step_tool_messages if has_tool_error(msg)]

            if error_tool_messages:
                print(f"Found {len(error_tool_messages)} tool errors, attempting correction...")
                # print(error_tool_messages[-1].json())
                correction_result = await corrector.correct_step_if_needed(
                    step_student_messages,
                    error_tool_messages,
                    current_convo,
                    toolkit,
                    valid_recipients,
                )

                if correction_result.success:
                    # Use corrected messages
                    step_student_messages = correction_result.corrected_student_messages
                    step_tool_messages = correction_result.corrected_tool_messages
                    print(f"✅ Step corrected successfully")
                else:
                    print(
                        f"⚠️ Correction failed: {correction_result.error_message} -> Continue with original messages"
                    )
                    # Continue with original messages
            # else:
            #     print(f"No tool errors detected, continuing with original messages")

        student_messages.extend(step_student_messages)

        # Update conversation with student messages
        current_convo = current_convo.with_suffix(*step_student_messages)

        # Update conversation with tool messages if any
        if step_tool_messages:
            current_convo = current_convo.with_suffix(*step_tool_messages)

        # Log progress
        write_to_file_text(str(current_convo), f"{stage_key}.txt", folder="stage_logs")

        if not should_continue:
            break

    return student_messages, current_convo


async def generate_student_response(message_completer, convo, is_last_step, retries):
    """Generate student model response with retries"""
    for completion_attempt in range(retries):
        try:
            completion = await message_completer.async_completion(
                conversations=[convo], n=1, seed=0, end_header=is_last_step
            )
            choice = completion.choices[0]
            messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
            return messages
        except Exception as e:
            if completion_attempt < retries - 1:
                print(f"Student completion attempt {completion_attempt + 1} failed: {e}")
                await asyncio.sleep(1)
                continue
            else:
                print(f"Student completion failed after {retries} attempts: {e}")
                return None


async def process_instance_stage_based(
    instance, student_juice=1024, max_attempts_per_stage=3, outputs_dirname="outputs"
):
    """Process a single instance with stage-based approach"""
    instance_id = instance.get("unique_id", instance.get("instance_id", "unknown"))
    try:
        result = await stage_based_rollout(
            instance=instance,
            student_juice=student_juice,
            max_attempts_per_stage=max_attempts_per_stage,
            outputs_dirname=outputs_dirname,
        )
        return result
    except Exception as e:
        print(f"❌ Failed to process instance {instance_id}: {e}")
        return None


async def process_all_stage_based(
    dataset,
    student_juice=1024,
    max_attempts_per_stage=3,
    concurrency_limit=2,
    outputs_dirname="outputs",
):
    """Process all instances with stage-based approach"""
    semaphore = asyncio.Semaphore(concurrency_limit)
    results = []
    total_instances = len(dataset)
    processed_count = 0
    passed_count = 0
    failed_count = 0

    # Create output directory and initialize files
    os.makedirs(outputs_dirname, exist_ok=True)
    data_file = f"{outputs_dirname}/data.jsonl"
    progress_file = f"{outputs_dirname}/progress.txt"

    async def process_with_semaphore(instance):
        nonlocal processed_count, passed_count, failed_count
        async with semaphore:
            result = await process_instance_stage_based(
                instance,
                student_juice=student_juice,
                max_attempts_per_stage=max_attempts_per_stage,
                outputs_dirname=outputs_dirname,
            )

            processed_count += 1
            if result:
                passed_count += 1
                # Save result immediately to avoid data loss
                with open(data_file, "a") as f:
                    f.write(json.dumps(result, default=str) + "\n")
                print(
                    f"✅ Progress: {processed_count}/{total_instances} processed, {passed_count} passed, {failed_count} failed"
                )
            else:
                failed_count += 1
                print(
                    f"❌ Progress: {processed_count}/{total_instances} processed, {passed_count} passed, {failed_count} failed"
                )

            # Update progress file
            with open(progress_file, "w") as f:
                f.write(f"Processed: {processed_count}/{total_instances}\n")
                f.write(f"Passed: {passed_count}\n")
                f.write(f"Failed: {failed_count}\n")
                f.write(f"Success Rate: {passed_count/processed_count*100:.1f}%\n")

            return result

    tasks = [process_with_semaphore(instance) for instance in dataset]
    for task in asyncio.as_completed(tasks):
        result = await task
        if result:  # Only add results that passed tests
            results.append(result)

    print(
        f"\n🎯 Final Results: {passed_count}/{total_instances} instances passed tests and were included in output"
    )
    return results


# ============================================================================
# MAIN ENTRY POINT
# ============================================================================

data_paths = {
    "rrb_ml_py": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_py/rrb_py_ml_data_merge.jsonl",
    "rrb_ml_ts1": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_ts/rrb_ts_ml_data_merge.jsonl",
    "rrb_ml_ts2": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_ts/rrb_ts_ml_data_merge.jsonl",
    "rrb_ml_js1": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_js/rrb_js_ml_data_merge.jsonl",
    "rrb_ml_js2": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_js/rrb_js_ml_data_merge.jsonl",
}


def stage_based_main(
    dataset_name="all",
    outputs_dirname="stage_based_outputs",
    concurrency_limit=100,
    max_attempts_per_stage=10,
):
    """Main function for stage-based data collection"""

    if dataset_name == "all":
        # Process all datasets by merging them
        print(f"Processing all datasets: {list(data_paths.keys())}")
        all_datasets = []

        for ds_name, azure_path in data_paths.items():
            dataset_file_path = f"/root/code/glass/data/jsonl/{ds_name}.jsonl"

            # Check if the dataset file exists, if not download it from Azure blob
            if not os.path.exists(dataset_file_path):
                print(f"Dataset file {dataset_file_path} not found. Downloading from Azure blob...")
                os.makedirs(os.path.dirname(dataset_file_path), exist_ok=True)

                download_cmd = f"bbb cp {azure_path} {dataset_file_path}"
                result = subprocess.run(download_cmd, shell=True)
                if result.returncode != 0:
                    raise RuntimeError(f"Failed to download dataset file: {download_cmd}")
                print(f"Dataset downloaded successfully to {dataset_file_path}")

            # Load and add to combined dataset
            ds_data = load_from_jsonl(dataset_file_path)
            print(f"Loaded {len(ds_data)} instances from {ds_name}")
            all_datasets.extend(ds_data)

        dataset = all_datasets
        print(f"Combined dataset has {len(dataset)} total instances")

    else:
        # Process single dataset (existing behavior)
        if dataset_name in data_paths:
            azure_path = data_paths[dataset_name]
            dataset_name_or_path = f"/root/code/glass/data/jsonl/{dataset_name}.jsonl"
        else:
            # Assume it's a direct path
            dataset_name_or_path = dataset_name
            azure_path = dataset_name

        # Check if the dataset file exists, if not download it from Azure blob
        if not os.path.exists(dataset_name_or_path):
            print(f"Dataset file {dataset_name_or_path} not found. Downloading from Azure blob...")
            os.makedirs(os.path.dirname(dataset_name_or_path), exist_ok=True)

            download_cmd = f"bbb cp {azure_path} {dataset_name_or_path}"
            result = subprocess.run(download_cmd, shell=True)
            if result.returncode != 0:
                raise RuntimeError(f"Failed to download dataset file: {download_cmd}")
            print(f"Dataset downloaded successfully to {dataset_name_or_path}")

        # Load dataset
        dataset = load_from_jsonl(dataset_name_or_path)

    subprocess.run("mkdir /var/log/supervisor", shell=True)

    # Shuffle dataset for random processing order
    random.shuffle(dataset)

    # Remove dataset limit for large-scale generation
    print(f"Loaded {len(dataset)} instances for large-scale data generation")

    # Create output directory
    os.makedirs(f"./{outputs_dirname}", exist_ok=True)

    print(f"Running stage-based data collection on {len(dataset)} instances.")

    # Configuration for large-scale generation
    student_juice = 128

    # Run stage-based processing
    results = asyncio.run(
        process_all_stage_based(
            dataset,
            student_juice=student_juice,
            max_attempts_per_stage=max_attempts_per_stage,
            concurrency_limit=concurrency_limit,
            outputs_dirname=outputs_dirname,
        )
    )

    # Print results
    print("*" * 100)
    print(f"🎉 Large-Scale Data Generation Complete!")
    print(f"Total instances that passed tests: {len(results)}")
    print(f"Data incrementally saved to: {outputs_dirname}/data.jsonl")
    print(f"Progress tracking saved to: {outputs_dirname}/progress.txt")

    if len(results) > 0:
        print(f"Successfully generated {len(results)} high-quality datapoints with passing tests")
        # Data is already saved incrementally, no need to write again
        print(f"All results are already saved in {outputs_dirname}/data.jsonl")
    else:
        print("⚠️  No instances passed tests - no data generated")
    print("*" * 100)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Stage-based data collection for SWE tasks")
    parser.add_argument(
        "--dataset",
        "-d",
        type=str,
        default="all",
        help=f"Dataset name or path. Use 'all' to process all datasets, or specify one: {list(data_paths.keys())}",
    )
    parser.add_argument(
        "--output", "-o", type=str, default="stage_based_outputs", help="Output directory name"
    )
    parser.add_argument(
        "--concurrency",
        "-c",
        type=int,
        default=100,
        help="Concurrency limit for parallel processing",
    )
    parser.add_argument(
        "--max-attempts", "-m", type=int, default=10, help="Maximum attempts per stage"
    )

    args = parser.parse_args()

    print(f"Running stage-based data collection with:")
    print(f"  Dataset: {args.dataset}")
    print(f"  Output directory: {args.output}")
    print(f"  Concurrency limit: {args.concurrency}")
    print(f"  Max attempts per stage: {args.max_attempts}")

    stage_based_main(
        dataset_name=args.dataset,
        outputs_dirname=args.output,
        concurrency_limit=args.concurrency,
        max_attempts_per_stage=args.max_attempts,
    )
