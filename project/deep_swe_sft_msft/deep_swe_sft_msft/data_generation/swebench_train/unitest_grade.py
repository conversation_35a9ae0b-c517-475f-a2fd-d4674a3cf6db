import shlex
from typing import TypedDict
from uuid import uuid4

import structlog
from caas.terminal.api import TerminalSession
from deep_swe_msft.swe_bench_train_v2_padawan_v2.graders.unitest_grader import grade_fn_v2_internal
from deep_swe_tasks import DeepSWETaskMetadata

logger = structlog.get_logger(__name__)


async def grade_fn_v2(
    dp_metadata: dict,
    terminal_session: TerminalSession,
    fast: bool = True,
) -> bool:
    metadata = DeepSWETaskMetadata.model_validate(dp_metadata)
    # Extract lang from raw metadata and pass it separately
    lang = dp_metadata.get("lang", None)
    report = await grade_fn_v2_internal(
        terminal_session=terminal_session,
        metadata=metadata,
        lang=lang,
        fast=fast,
    )
    logger.info("Grading finished", **report)
    return report
