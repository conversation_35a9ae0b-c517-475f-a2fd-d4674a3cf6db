"""
Multi-agent data collection system - refactored for better organization.

This file shows the improved structure with functions organized by purpose,
making the code more readable and maintainable.
"""

import asyncio
import copy
import json
import os
import random
import re
import subprocess
import time
import uuid
from collections import defaultdict
from contextlib import closing
from dataclasses import asdict, dataclass
from datetime import datetime
from functools import cache
from pathlib import Path
from typing import Any, Callable, Coroutine, Dict, List, Optional, Tuple, Unpack, cast

import birder
import chat
import chz
import numpy as np
import structlog
from berry_rfs.mrfs_eval_utils import passes_tests as test_parsing_py
from bus_token_completer import BusTokenCompleter
from caas.protocol import NetworkMode, VolumeMount
from caas_tool.caas_container import CaasContainer
from chat import Conversation, Role, chat
from chat.render.renderer_registry import get_renderer
from chat.tools import DefaultMultiToolKit, take_one_step_with_tools
from deep_swe_msft.padawan_data.system_prompt import (
    INSTRUCTION_PADAWAN,
    PADAWAN_MODEL_IDENTITY,
    PADAWAN_SYSTEM_PROMPT,
)
from deep_swe_msft.swe_bench_train_v2_padawan_v2.data_customization.conversation_init import (
    conversation_init_fn,
)
from deep_swe_msft.swe_bench_train_v2_padawan_v2.setup.setup import swe_bench_v2_setup_fn
from deep_swe_msft.tools.caas_padawan_tool import DeepSWECaasPadawanTool
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CAAS_ENVS, CAAS_IMAGE, PDW_MNT
from deep_swe_sft_msft.data_generation.constants import (
    BUS_TOPIC,
    BUS_USER,
    STUDENT_RENDERER,
    TEACHER_RENDERER,
)
from deep_swe_sft_msft.data_generation.corrector import Corrector, has_tool_error
from deep_swe_sft_msft.data_generation.grading import SWE_BEHAVIOR_RUBRIC, ConversationGrader
from deep_swe_sft_msft.data_generation.sbhv2.swebenchhard_wf_prompts import SWE_STAGE_DEFINITIONS
from deep_swe_sft_msft.data_generation.swebench_train.unitest_grade import grade_fn_v2
from message_completer import TokenMessageCompleter
from message_completer.message_completer import MessageCompleter, ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter
from qstar.common.tools import constraint_machine_spec, renderer_worker

# Import with fallbacks for specialized tools

logger = structlog.stdlib.get_logger(component=__name__)


# Define systemMessage locally if not available from imports
systemMessage = "You are a helpful AI assistant specialized in software engineering tasks."

MODEL_BEHAVIOR_PROMPT = """
You should act like a perfect software engineer. You will be working through a structured workflow with multiple stages.

General Guidelines:
1. Enumerate the tools that can help with this task.
2. List out the files that need to be updated
3. Read the files to get the parts that need to be updated
4. Build the code to see if to is buildable
5. Create test
6. Run the test to see if it fails
7. Fix the issue, using tools to automate portions, to reduce the odds of a mistake. Rebuild, fix new build errors iteratively.
8. Run the test to see if it passes.

Your goal is to solve the software engineering problem systematically by following the stage instructions you'll receive.
"""

# Simplified teacher evaluation prompt for stage-based approach
STAGE_TEACHER_PROMPT = """
You are evaluating whether a student has completed the current stage properly.

Current Stage: {stage_name}
Stage Criteria:
{criteria_list}

Student's work in this stage:
{conversation_messages}

Evaluate each criterion and respond with JSON:
{{
  "stage_complete": true/false,
  "criteria_met": [
    {{"criterion": "criteria1", "met": true/false, "explanation": "brief explanation"}},
    ...
  ],
  "overall_feedback": "brief feedback about what's missing or what was done well",
  "should_resample": true/false
}}

If should_resample is true, the student will try the stage again.
"""


# ============================================================================
# DATA MODELS AND TYPE DEFINITIONS
# ============================================================================


@dataclass
class StageEvaluation:
    """Teacher's evaluation of a stage"""

    stage_complete: bool
    criteria_met: List[Dict[str, Any]]
    overall_feedback: str
    should_resample: bool
    stage_name: str


@dataclass
class StageData:
    """Data for a single stage execution"""

    stage_name: str
    attempt_number: int
    student_messages: List[Dict]
    teacher_evaluation: Optional[StageEvaluation] = None
    stage_completed: bool = False


@dataclass
class EpisodeData:
    """Complete episode data for saving to JSONL"""

    instance_id: str
    problem_statement: str
    docker_image: str
    stages: List[StageData]
    total_stages: int
    completion_status: str  # "completed", "max_attempts", "error"
    timestamp: str
    metadata: Dict = None
    final_conversation: List[Dict] = None  # Clean conversation without stage prompts
    grade_results: Dict = None


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================


def write_messages_to_file(messages, file_name="aaa", folder="log"):
    """Write messages to a file"""
    if not isinstance(messages, list):
        messages = [messages]
    os.makedirs(folder, exist_ok=True)
    with open(os.path.join(folder, file_name), "w") as f:
        for m in messages:
            if hasattr(m, "model_dump_json"):
                f.write(m.model_dump_json() + "\n")
            else:
                f.write(json.dumps(m, default=str) + "\n")


def extract_clean_conversation(conversation_messages):
    """Extract clean conversation without stage prompts, keeping only initial prompt and responses"""
    clean_messages = []

    for msg in conversation_messages:
        # Skip stage-specific prompts (look for stage keywords in content)
        content = str(msg.content)
        if any(stage_key in content for stage_key in ["**Stage ", "End this stage by calling"]):
            continue

        # Keep the message if it's not a stage prompt
        clean_messages.append(msg)

    return clean_messages


def create_clean_conversation_with_problem(
    conversation_messages, problem_statement, instruction_padawan=""
):
    """
    Create a clean conversation object with the problem statement as the first user message,
    followed by all non-stage-specific messages from the conversation.

    Args:
        conversation_messages: List of messages from the current conversation
        problem_statement: The original problem statement to include as first user message

    Returns:
        chat.Conversation: Clean conversation object
    """
    clean_messages = []

    error_msgs = ["Error parsing function call:", "Could not parse args as JSON:"]

    # Find the system message first
    system_message = None
    for msg in conversation_messages:
        if msg.author.role == Role.SYSTEM:
            system_message = msg
            break

    # Add system message if found
    if system_message:
        clean_messages.append(system_message)

    # Add the problem statement as the first user message
    problem_user_message = chat.Message.user(
        f"Problem: {problem_statement} \n{instruction_padawan}"
    )
    clean_messages.append(problem_user_message)

    # Process messages and identify error patterns
    messages_to_process = []
    for msg in conversation_messages:
        # Skip system messages (already added)
        if msg.author.role == Role.SYSTEM:
            continue

        # Skip stage-specific prompts (look for stage keywords in content)
        content = str(msg.content)
        if any(stage_key in content for stage_key in ["**Stage ", "End this stage by calling"]):
            continue

        messages_to_process.append(msg)

    # Filter out tool messages with errors and their preceding assistant messages
    i = 0
    while i < len(messages_to_process):
        msg = messages_to_process[i]

        # Check if this is a tool message with error content
        is_error_tool = False
        if msg.author.role == Role.TOOL:
            content = str(msg.content)
            if any(error_msg in content for error_msg in error_msgs):
                is_error_tool = True

        if is_error_tool:
            # Skip this tool message with error
            # Also check if the previous message was from assistant and skip it too
            if clean_messages and clean_messages[-1].author.role == Role.ASSISTANT:
                # Remove the previous assistant message
                clean_messages.pop()
            # Skip the current error tool message (don't add it to clean_messages)
            i += 1
            continue

        # Keep the message if it's not a stage prompt or error tool
        clean_messages.append(msg)
        i += 1

    # Post-process assistant messages to modify channel and end_turn for intermediate stages
    # Keep the last message (which is always from assistant) as channel='final' and end_turn=True
    for i, msg in enumerate(clean_messages):
        if msg.author.role == Role.ASSISTANT:
            # Only modify non-final assistant messages (all except the last message)
            if i < len(clean_messages) - 1 and hasattr(msg, "channel") and msg.channel == "final":
                # Create a copy and modify properties for continuous conversation flow
                modified_msg = copy.deepcopy(msg)
                modified_msg.channel = "analysis"
                modified_msg.end_turn = False
                clean_messages[i] = modified_msg
            if i == len(clean_messages) - 1:
                # Ensure the last assistant message is final and ends the turn
                modified_msg = copy.deepcopy(msg)
                modified_msg.channel = "final"
                modified_msg.end_turn = True
                clean_messages[i] = modified_msg

    # Create and return the clean conversation
    return chat.Conversation(messages=clean_messages)


def write_to_file_text(text, file_name="aaa", folder="log"):
    """Write text to a file"""
    os.makedirs(folder, exist_ok=True)
    with open(os.path.join(folder, file_name), "w") as f:
        f.write(text)


def safe_message_dump(msg):
    """Safely convert message to dict format with UUID handling"""
    if hasattr(msg, "model_dump"):
        try:
            return msg.model_dump()
        except TypeError:
            # Handle UUID and other non-serializable objects
            return json.loads(json.dumps(msg.model_dump(), default=str))
    elif hasattr(msg, "dict"):
        try:
            return msg.dict()
        except TypeError:
            # Handle UUID and other non-serializable objects
            return json.loads(json.dumps(msg.dict(), default=str))
    else:
        return str(msg)


def get_render_constrained_extension(renderer_name: str):
    """Get the constrained extension for the renderer."""

    harmony_stop_tokens_utils = renderer_worker.HarmonyStopTokensUtils(
        harmony_renderer_name=renderer_name,
        enable_special_stop_token=False,
    )

    extension = constraint_machine_spec.constraint_machine_extension(
        harmony_renderer_name=renderer_name,
        encoding_name="orion_200k",
        final_token_sequences=harmony_stop_tokens_utils.constraint_machine_final_token_sequences(),
        json_eot_tokens=harmony_stop_tokens_utils.constrained_sampling_json_eot_tokens(),
        tools=(
            "functions.str_replace_editor",
            "functions.report_progress",
            "functions.bash",
            "functions.write_bash",
            "functions.read_bash",
            "functions.stop_bash",
        ),
        channels=("analysis", "final"),
        response_format_names=(),
        enable_json_constraining=False,
        none_channel_allowed=False,
    )
    return extension


def truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    """Truncate a string to a specified token limit"""
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"


def get_processed_instances(outputs_folder):
    """Get list of already processed instances"""
    processed_instances = set()
    if os.path.exists(outputs_folder):
        for file in os.listdir(outputs_folder):
            if file.endswith(".jsonl") and file != "all_episodes.jsonl":
                instance_id = file.replace(".jsonl", "")
                processed_instances.add(instance_id)
    return processed_instances


def load_from_jsonl(jsonl_path):
    """Load dataset from a jsonl file"""
    dataset = []
    with open(jsonl_path, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if line:  # Skip empty lines
                try:
                    data = json.loads(line)
                    dataset.append(data)
                except json.JSONDecodeError as e:
                    print(f"Error parsing line in {jsonl_path}: {e}")
                    continue
    return dataset


# ============================================================================
# FILE I/O AND FORMATTING FUNCTIONS
# ============================================================================


def save_episode_data(episode_data: EpisodeData, output_dir: str):
    """Save episode data to JSONL format and readable conversation format"""
    os.makedirs(output_dir, exist_ok=True)

    # Convert to dict and save as JSONL
    episode_dict = asdict(episode_data)

    # Save individual episode file
    episode_file = os.path.join(output_dir, f"{episode_data.instance_id}.jsonl")
    with open(episode_file, "w") as f:
        f.write(json.dumps(episode_dict, default=str) + "\n")

    # Append to master JSONL file
    master_file = os.path.join(output_dir, "all_episodes.jsonl")
    with open(master_file, "a") as f:
        f.write(json.dumps(episode_dict, default=str) + "\n")

    # Save readable conversation format
    # save_final_conversation(episode_data, output_dir)


# ============================================================================
# STAGE-BASED TEACHER MODEL CLASS
# ============================================================================


class StageTeacherModel:
    """Simplified teacher model for stage-based evaluation"""

    def __init__(
        self, renderer_name: str = TEACHER_RENDERER, retries: int = 3, reward_multiplier: int = 32
    ):
        self.renderer_name = renderer_name
        self.renderer = get_renderer(renderer_name)
        self.retries = retries
        self.reward_multiplier = reward_multiplier

        # Teacher model configuration
        bus_tc_config = BusTokenCompleter.Config(
            topic_mode_or_user=BUS_USER,
            topic_or_snapshot=BUS_TOPIC,
            # topic_mode_or_user="swe-sft-data",
            # topic_or_snapshot="az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted",
        )
        extension = get_render_constrained_extension(renderer_name)

        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=bus_tc_config,
            completion_params={"temperature": 0.7, "extensions": [extension]},
            renderer=self.renderer,
        )

        self.message_completer = message_completer_config.build()

    async def evaluate_stage(
        self, stage_name: str, stage_criteria: List[str], conversation: Conversation
    ) -> StageEvaluation:
        """Evaluate if the student has completed the current stage properly"""

        # Format conversation for evaluation
        conversation_text = self._format_conversation_for_evaluation(conversation)

        # Format criteria as numbered list
        criteria_list = "\n".join(
            [f"{i+1}. {criterion}" for i, criterion in enumerate(stage_criteria)]
        )

        # Create evaluation prompt
        evaluation_prompt = STAGE_TEACHER_PROMPT.format(
            stage_name=stage_name,
            criteria_list=criteria_list,
            conversation_messages=conversation_text,
        )

        print(f"Evaluating stage: {stage_name}")

        # Create teacher conversation for evaluation
        teacher_convo = chat.Conversation(
            messages=[
                chat.Message.system(
                    "You are an expert teacher evaluating student software engineering work. Evaluate stage completion strictly based on the provided criteria.",
                    metadata=chat.SystemContentMetadata(reward_multiplier=self.reward_multiplier),
                ),
                chat.Message.user(evaluation_prompt),
            ]
        )

        # Get teacher evaluation
        for attempt in range(self.retries):
            try:
                completion = await self.message_completer.async_completion(
                    conversations=[teacher_convo],
                    n=1,
                    seed=0,
                    end_header=False,
                )
                choice = completion.choices[0]
                messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)

                # Parse the teacher's response
                response_text = str(messages[-1].content)
                print(f"Teacher response: {response_text[:200]}...")

                evaluation = self._parse_stage_evaluation(response_text, stage_name, stage_criteria)
                return evaluation

            except Exception as e:
                if attempt < self.retries - 1:
                    print(f"Teacher evaluation attempt {attempt + 1} failed: {e}")
                    await asyncio.sleep(1)
                    continue
                else:
                    # Return failed evaluation if all attempts fail
                    return StageEvaluation(
                        stage_complete=False,
                        criteria_met=[
                            {"criterion": c, "met": False, "explanation": f"Evaluation failed: {e}"}
                            for c in stage_criteria
                        ],
                        overall_feedback=f"Teacher evaluation failed after {self.retries} attempts: {e}",
                        should_resample=True,
                        stage_name=stage_name,
                    )

    def _format_conversation_for_evaluation(
        self, conversation: Conversation, token_limit: int = 256, tool_truncation_rate: float = 0.5
    ) -> str:
        """Format conversation for teacher evaluation"""
        formatted_messages = []
        for msg in conversation.messages:
            role = msg.author.role

            if role == Role.SYSTEM:
                continue  # skip system messages for now
                # content = system_content_render(msg.content)
            else:
                content = str(msg.content)
                if role != Role.TOOL:
                    cur_token_limit = token_limit
                else:
                    cur_token_limit = int(token_limit * tool_truncation_rate)
                content = truncate_string(self.renderer, content, token_limit=cur_token_limit)

            formatted_messages.append(
                f"<author:{msg.author.role}> -> <recipient:{msg.recipient}>:  {content}\n"
            )

        return "\n".join(formatted_messages)

    def _parse_stage_evaluation(
        self, response_text: str, stage_name: str, stage_criteria: List[str]
    ) -> StageEvaluation:
        """Parse teacher's stage evaluation response"""
        # Try to extract JSON from the response
        json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
        if json_match:
            try:
                response_data = json.loads(json_match.group())
                stage_complete = response_data.get("stage_complete", False)

                return StageEvaluation(
                    stage_complete=stage_complete,
                    criteria_met=response_data.get("criteria_met", []),
                    overall_feedback=response_data.get("overall_feedback", ""),
                    should_resample=response_data[
                        "should_resample"
                    ],  # should have this key otherwise it is an error
                    stage_name=stage_name,
                )
            except json.JSONDecodeError as e:
                raise ValueError(f"Failed to parse JSON from teacher response: {e}")
        else:
            raise ValueError(f"Could not find JSON in teacher response: {response_text[:200]}...")


# ============================================================================
# STAGE-BASED ROLLOUT AND PROCESSING FUNCTIONS
# ============================================================================


async def stage_based_rollout(
    instance,
    student_juice: int = 1024,
    eval_code_patch: bool = False,
    max_attempts_per_stage: int = 3,
    output_dir: str = "outputs",
    # passed_rating: List[str] = ["A+", "A", "A-"],
):
    """
    Stage-based rollout with student and teacher models
    """
    data_dict = {}
    metadata = instance["metadata"]
    instance_id = instance["unique_id"]
    # problem_statement = instance["problem"]
    # docker_image = CAAS_IMAGE
    # version = metadata.get("version", "sbh-12878")
    # git_volume_mount_path = None
    volume_mounts = PDW_MNT
    # if version.endswith("-noexec"):
    #     repo = metadata.get("repo")
    #     commit = metadata.get("base_commit")
    #     # Mount the git repo as a volume
    #     git_volume_mount_path = (
    #         f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{repo}/{commit}.tar"
    #     )
    #     volume_mounts = (volume_mounts or []) + [
    #         VolumeMount(
    #             host=git_volume_mount_path,
    #             container=git_volume_mount_path,
    #             deprecated_use_blobfuse=True,
    #         )
    #     ]
    #     print(f"Mounting git volume for {repo}@{commit}, {volume_mounts=}", instance_id)
    # else:
    #     print(f"Not mounting git volume, version={version}, {volume_mounts=}", instance_id)

    # # Initialize episode data
    # episode_data = EpisodeData(
    #     instance_id=instance_id,
    #     problem_statement=problem_statement,
    #     docker_image=metadata["docker_image"],
    #     stages=[],
    #     total_stages=len(SWE_STAGE_DEFINITIONS),
    #     completion_status="error",
    #     timestamp=datetime.now().isoformat(),
    #     metadata=metadata.dict()
    #     if hasattr(metadata, "dict")
    #     else dict(metadata)
    #     if hasattr(metadata, "__dict__")
    #     else {},
    # )

    # Initialize Final Grader
    final_rollout_grader = ConversationGrader()

    container = None
    retries = 10

    for attempt in range(retries):
        try:
            # Build CaaS Container
            # CAAS_ENDPOINT = "https://eastus2.caas.azure.com"
            CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
            # cert_path = "/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt"
            # env = {"NODE_EXTRA_CA_CERTS": cert_path}
            container = await CaasContainer.new(
                caas_endpoint=CAAS_ENDPOINT,
                image_name=metadata["docker_image"],
                cmd=["/server.py"],
                cpu_limit=str(metadata["limits"]["cpu"]),
                memory_limit=f"{metadata['limits']['memory']}m",
                idle_ttl=1200,
                # caas_disk_limit="32G",
                network=NetworkMode.BRIDGE,
                pids_limit=1024,
                volume_mounts=volume_mounts,
                # sandbox=False,
                # timeout=1200,
                env=CAAS_ENVS,
            )

            terminal_session = container.terminal_session
            caas_session = terminal_session.session

            # Set up container
            await swe_bench_v2_setup_fn(datapoint=instance, terminal_session=terminal_session)

            # # Set up repository
            # await sbh_setup_func_with_volumn(
            #     metadata=metadata,
            #     terminal_session=terminal_session,
            #     git_volume_mount_path=git_volume_mount_path,
            #     instance_id=instance_id,
            #     datapoint={},
            #     # workdir: str=REPO_DIRECTORY,
            #     apply_code_patch=False,
            #     test_script_file="run_tests.sh",
            # )
            # await sbh_setup_func(
            #     metadata=metadata,
            #     terminal_session=terminal_session,
            #     instance_id=instance_id,
            #     datapoint={},
            #     # workdir: str=REPO_DIRECTORY,
            #     apply_code_patch=False,
            #     test_script_file="run_tests.sh",
            # )

            # Set up tools
            tool = DeepSWECaasPadawanTool(
                container=container,
                terminal_emulator_path="teammate_service.termites.lean_terminal:LeanTerminal",
            )
            toolkit = DefaultMultiToolKit(tools=[tool])
            tools_section = {tool.name: tool.instruction()}

            # Set up models
            renderer_name = STUDENT_RENDERER
            renderer = get_renderer(renderer_name)
            extension = get_render_constrained_extension(renderer_name)

            # Initialize teacher model
            teacher = StageTeacherModel(TEACHER_RENDERER, retries=retries)

            # Initialize corrector model
            corrector = Corrector(TEACHER_RENDERER, retries=retries)

            # Student model configuration
            student_bus_config = BusTokenCompleter.Config(
                topic_mode_or_user=BUS_USER,
                topic_or_snapshot=BUS_TOPIC,
            )

            completion_params = {"temperature": 1}
            if extension is not None:
                completion_params["extensions"] = [extension]

            student_message_completer_config = TokenMessageCompleter.Config(
                token_completer_config=student_bus_config,
                completion_params=completion_params,
                renderer=renderer,
            )

            student_message_completer = student_message_completer_config.build()

            # Initialize base conversation with problem statement only
            # base_user_prompt = f"Problem: {problem_statement}\n"
            # base_user_prompt += "Follow the stages below to approach the problem step by step.\n"
            # instruction_padawan = (
            #     INSTRUCTION_PADAWAN_SBH_EASY_LANG
            #     if is_easy_language(metadata) else INSTRUCTION_PADAWAN_SBH_HARD_LANG
            # )
            systemMessage_instruction = systemMessage + PADAWAN_SYSTEM_PROMPT + INSTRUCTION_PADAWAN
            system_msg = chat.Message.system(
                model_identity_desc=systemMessage_instruction,
                tools_section=tools_section,
                channel_config=chat.SystemChannelConfig(
                    valid_channels=("analysis", "final"), channel_required=True
                ),
                metadata=chat.SystemContentMetadata(reward_multiplier=student_juice),
            )
            base_messages = [system_msg]
            base_convo = chat.Conversation(
                messages=base_messages,
            )
            problem_statement = str(
                conversation_init_fn(
                    datapoint=instance, instruction="", add_contributor_criteria=True
                )[0].content
            )

            valid_nontool_recipients = {
                m.author.display_name() for m in base_convo.messages if m.author.role != Role.TOOL
            }
            valid_recipients = (
                valid_nontool_recipients
                | (set([tool.name for tool in toolkit.tools]) if toolkit else set())
                | {"all"}
            )

            # Execute stages sequentially
            current_convo = base_convo
            cumulative_max_steps = 0
            selected_swe_stages = {
                stage_key: SWE_STAGE_DEFINITIONS[stage_key]
                for stage_key in [
                    "stage_1_analysis",
                    "stage_2_environment",
                    "stage_3_reproduction",
                    "stage_4_implementation",
                    "stage_5_finalization",
                ]
            }
            stage_eval_record = {}
            for stage_key, stage_def in SWE_STAGE_DEFINITIONS.items():
                stage_name = stage_def["name"]
                stage_description = stage_def["description"]
                stage_criteria = stage_def["criteria"]
                stage_max_steps = stage_def.get("max_steps", 20)  # Default to 20 if not specified

                # Calculate cumulative max steps (current + all previous stages)
                cumulative_max_steps += stage_max_steps

                stage_prompt = f"\n{stage_description}"
                stage_prompt = stage_prompt.replace("$PROBLEM_STATEMENT", problem_statement)

                print(f"\n{'='*60}")
                print(
                    f"Starting Stage: {stage_name} (max_steps: {stage_max_steps}, cumulative: {cumulative_max_steps})"
                )
                print(f"{'='*60}")

                stage_completed = False
                attempt_number = 0
                stage_eval_record[stage_name] = []

                while not stage_completed and attempt_number < max_attempts_per_stage:
                    attempt_number += 1
                    print(f"\nStage {stage_name} - Attempt {attempt_number}")

                    # Add stage prompt to conversation
                    stage_convo = current_convo.with_suffix(chat.Message.user(stage_prompt))
                    # print(f"Stage convo: {stage_convo}")

                    stage_convo.metadata.header_yields_budget_total_override = cumulative_max_steps
                    stage_convo.metadata.header_yields_budget_for_action = cumulative_max_steps

                    student_messages, final_convo = await execute_stage_with_student_corrector(
                        student_message_completer,
                        stage_convo,
                        toolkit,
                        valid_recipients,
                        retries,
                        max_steps=stage_max_steps,
                        stage_key=stage_key,
                        corrector=corrector,
                    )

                    if not student_messages:
                        print(f"Failed to get student response for stage {stage_name}")
                        break

                    # Teacher evaluates the stage
                    stage_evaluation = await teacher.evaluate_stage(
                        stage_name, stage_criteria, final_convo
                    )
                    # stage_evaluation.stage_complete = True
                    stage_eval_record[stage_name].append(
                        {
                            "attempt": attempt_number,
                            "evaluation": asdict(stage_evaluation),
                        }
                    )
                    try:
                        stage_log_folder = "stage"
                        write_to_file_text(
                            f"{json.dumps(stage_eval_record)}\n#######Conv\n{str(final_convo)}",
                            f"eval_record__{stage_name}__{attempt_number}.txt",
                            folder=f"{output_dir}/{stage_log_folder}_logs/{stage_name}/{instance_id}",
                        )
                        write_to_file_text(
                            f"{final_convo.model_dump(mode='json')}",
                            f"stage_conv__{stage_name}__{attempt_number}.txt",
                            folder=f"{output_dir}/{stage_log_folder}_conv/{stage_name}/{instance_id}/",
                        )
                    except Exception as e:
                        print(f"Error writing stage evaluation record: {e}")

                    # Record stage data
                    stage_data = StageData(
                        stage_name=stage_name,
                        attempt_number=attempt_number,
                        student_messages=[safe_message_dump(msg) for msg in student_messages],
                        teacher_evaluation=asdict(stage_evaluation),
                        stage_completed=stage_evaluation.stage_complete,
                    )
                    # episode_data.stages.append(stage_data)

                    if stage_evaluation.stage_complete:
                        print(f"✅ Stage {stage_name} completed successfully!")
                        stage_completed = True
                        current_convo = final_convo
                        # episode_data.stages.append(asdict(stage_data))
                    else:
                        print(
                            f"❌ Stage {stage_name} not completed. Feedback: {stage_evaluation.overall_feedback}"
                        )
                        if not stage_evaluation.should_resample:
                            print(f"Teacher advised not to resample. Moving to next stage.")
                            stage_completed = True
                            current_convo = final_convo
                        else:
                            print(f"Resampling stage {stage_name}...")
                            # Reset to the state before this stage for resampling
                            continue

                if not stage_completed:
                    print(
                        f"❌ Failed to complete stage {stage_name} after {max_attempts_per_stage} attempts"
                    )
                    # episode_data.completion_status = "max_attempts"
                    # Raise exception to trigger instance retry
                    raise RuntimeError(
                        f"Stage {stage_name} failed to complete after {max_attempts_per_stage} attempts"
                    )

            # # If all stages completed successfully
            # episode_data.completion_status = "completed"

            if eval_code_patch:
                eval_data_result = await grade_fn_v2(
                    dp_metadata=metadata,
                    terminal_session=terminal_session,
                )
                print(f"Eval data result: {eval_data_result}")

                # code_patch_grader = CodePatchGrader(retries=retries)
                # try:
                #     # patch = await get_patch(metadata=metadata, caas_container=container)
                #     patch = await get_patch_volumn(metadata=metadata, caas_container=container)
                # except Exception as e:
                #     print(f"Error getting patch: {e}")
                #     patch = None
                # if not patch or patch.strip() == "":
                #     print("ERROR!!! No patch found in sample, marking as incorrect", instance_id)
                #     eval_data_result = {
                #         "passed": False,
                #         "patch": patch,
                #         "eval_patch_result": {
                #             "is_correct": False,
                #             "rating": "NA",
                #             "explanation": "No patch",
                #             "criteria_met": {},
                #             "overall_feedback": "No patch",
                #         },
                #     }
                # else:
                #     try:
                #         issue_description = metadata.get("problem_statement")
                #         silver_patch = metadata.get("patch") + "\n" + metadata.get("test_patch", "")
                #         eval_patch_result = await code_patch_grader.evaluate_code_patch(
                #             prompt_statement=issue_description,
                #             code_patch=patch,
                #             golden_patch=silver_patch,
                #         )

                #         eval_data_result = {
                #             "passed": eval_patch_result.is_correct,
                #             "eval_patch_result": asdict(eval_patch_result),
                #             "patch": patch,
                #         }
                #     except Exception as e:
                #         # print(f"Error evaluating code patch: {e}")
                #         eval_data_result = {
                #             "passed": False,
                #             "patch": patch,
                #             "eval_patch_result": {
                #                 "is_correct": False,
                #                 "rating": "NA",
                #                 "explanation": f"Grader Error: {str(e)}",
                #                 "criteria_met": {},
                #                 "overall_feedback": "Grader Error",
                #             },
                #         }
            else:
                eval_data_result = {
                    "passed": False,
                    "model_patch": None,
                    "test_output": "No tests executed.",
                    "passed_tests": [],
                }
            # print(f"Tests output: {d['output']}...")  # Print first 200 chars of output
            print(f"Test Result: {eval_data_result['passed']} \n {eval_data_result}")
            # episode_data.grade_results = eval_data_result

            # Extract clean conversation (without stage prompts but with problem statement)
            clean_convo = create_clean_conversation_with_problem(
                current_convo.messages,
                problem_statement,
                instruction_padawan=INSTRUCTION_PADAWAN,
            )
            write_to_file_text(str(clean_convo), f"{instance_id}.txt", folder=f"{output_dir}/clean")
            write_to_file_text(
                str(current_convo), f"{instance_id}.txt", folder=f"{output_dir}/original"
            )
            # try:
            #     write_to_file_text(json.dumps(asdict(episode_data)), f"{instance_id}.txt", folder=f"{output_dir}/episode_data")
            # except Exception as e:
            #     print(f"Error saving episode data: {e}")
            # episode_data.final_conversation = [safe_message_dump(msg) for msg in clean_convo.messages]

            data_dict["prompt"] = system_msg.model_dump(mode="json")
            data_dict["conversation"] = clean_convo.model_dump(mode="json")
            data_dict["eval_data_result"] = eval_data_result
            data_dict["original_instance"] = instance
            data_dict["stage_eval_record"] = stage_eval_record
            # data_dict["accuracy"] = passed
            # print(eval_data_result)
            # print(asdict(episode_data))
            # data_dict["episode_data"] = asdict(episode_data)
            # print(json.dumps(data_dict))

            # Final Grader Check
            evaluation = await final_rollout_grader.evaluate_conversation(
                clean_convo, SWE_BEHAVIOR_RUBRIC, "format_test"
            )
            is_good_conversation = evaluation.is_good_conversation

            if container:
                await container.teardown()

            # Only return data if tests passed
            if eval_code_patch:
                passed = eval_data_result["passed"]
                # passed = eval_data_result["eval_patch_result"]["rating"] in passed_rating
                print(
                    f"{'✅✅' if passed else '❌❌'} Patch evaluation result: {eval_data_result['passed']}"
                )
            else:
                passed = True
            # passed = True
            if passed and is_good_conversation:
                print(f"✅✅✅ Instance {instance_id} completed successfully with passing tests")
                return data_dict
            else:
                print(f"❌❌ Instance {instance_id} completed but tests failed - skipping")
                return None

            # # Only return data if tests passed
            # if passed:
            #     print(f"✅ Instance {instance_id} completed successfully with passing tests")
            #     return data_dict
            # else:
            #     print(f"❌ Instance {instance_id} completed but tests failed - skipping")
            #     return None

        except Exception as e:
            if attempt < retries - 1:
                print(f"Rollout attempt {attempt + 1} failed: {e}")
                if container:
                    try:
                        await container.teardown()
                    except:
                        pass
                await asyncio.sleep(4)
                continue
            else:
                print(f"❌ Instance {instance_id} failed after {retries} attempts - skipping")
                if container:
                    try:
                        await container.teardown()
                    except:
                        pass
                return None  # Skip failed instances instead of raising exception


async def execute_single_step(
    message_completer, current_convo, toolkit, valid_recipients, retries, is_last_step
):
    """
    Execute a single step in the conversation.

    Returns:
        tuple: (student_messages_to_append, tool_messages_to_append, should_continue)
    """
    # Student generates response
    new_messages = await generate_student_response(
        message_completer, current_convo, is_last_step, retries
    )

    if not new_messages:
        return [], [], False

    # Create a copy of conversation with student messages for tool execution
    step_convo = current_convo.with_suffix(*new_messages)
    last_message = new_messages[-1]

    # Check termination conditions
    should_continue = not (
        is_last_step
        or last_message.end_turn
        or (
            last_message.recipient not in valid_recipients
            and last_message.recipient.split(".")[0] not in valid_recipients
        )
    )

    tool_messages = []
    if should_continue:
        # Execute tools if needed using the copied conversation
        async for tool_message in take_one_step_with_tools(
            prefix_convo=step_convo.prefix(-1),
            message=last_message,
            toolkit=toolkit,
        ):
            tool_messages.append(tool_message)
            # Update the step conversation for subsequent tool calls
            if step_convo.messages[-1].id == tool_message.id:
                step_convo.messages[-1] = tool_message
            else:
                step_convo = step_convo.with_suffix(tool_message)

    return new_messages, tool_messages, should_continue


async def execute_stage_with_student_corrector(
    message_completer,
    stage_convo,
    toolkit,
    valid_recipients,
    retries,
    max_steps=20,
    stage_key="cur_stage",
    corrector: Corrector = None,
):
    """Execute a single stage with the student model"""
    current_convo = stage_convo
    student_messages = []

    for step in range(max_steps):
        is_last_step = step == max_steps - 1

        # Execute single step
        step_student_messages, step_tool_messages, should_continue = await execute_single_step(
            message_completer, current_convo, toolkit, valid_recipients, retries, is_last_step
        )
        # print(step_tool_messages[-1].json())
        if not step_student_messages:
            break

        # Check for tool errors and apply corrector if needed
        if corrector and step_tool_messages:
            # Check if there are any tool errors
            error_tool_messages = [msg for msg in step_tool_messages if has_tool_error(msg)]

            if error_tool_messages:
                # print(
                #     f"Found {len(error_tool_messages)} tool errors, attempting correction...")
                # print(error_tool_messages[-1].json())
                correction_result = await corrector.correct_step_if_needed(
                    step_student_messages,
                    error_tool_messages,
                    current_convo,
                    toolkit,
                    valid_recipients,
                )

                if correction_result.success:
                    # Use corrected messages
                    step_student_messages = correction_result.corrected_student_messages
                    step_tool_messages = correction_result.corrected_tool_messages
                    print(f"Step corrected ✅ successfully")
                else:
                    print(
                        f"Correction failed ⚠️: {correction_result.error_message} -> Continue with original messages"
                    )
                    # Continue with original messages
            # else:
            #     print(f"No tool errors detected, continuing with original messages")

        student_messages.extend(step_student_messages)

        # Update conversation with student messages
        current_convo = current_convo.with_suffix(*step_student_messages)

        # Update conversation with tool messages if any
        if step_tool_messages:
            current_convo = current_convo.with_suffix(*step_tool_messages)

        # Log progress
        write_to_file_text(str(current_convo), f"{stage_key}.txt", folder="stage_logs")

        if not should_continue:
            break

    return student_messages, current_convo


async def execute_stage_with_student(
    message_completer,
    stage_convo,
    toolkit,
    valid_recipients,
    retries,
    max_steps=20,
    stage_key="cur_stage",
):
    """Execute a single stage with the student model"""
    current_convo = stage_convo
    student_messages = []

    for step in range(max_steps):
        is_last_step = step == max_steps - 1

        # Student generates response
        new_messages = await generate_student_response(
            message_completer, current_convo, is_last_step, retries
        )
        # print(f"Student response: {new_messages[-1].content}")
        if not new_messages:
            break

        student_messages.extend(new_messages)
        current_convo = current_convo.with_suffix(*new_messages)
        last_message = new_messages[-1]

        # Check termination conditions
        if (
            is_last_step
            or last_message.end_turn
            or (
                last_message.recipient not in valid_recipients
                and last_message.recipient.split(".")[0] not in valid_recipients
            )
        ):
            break

        # Execute tools if needed
        async for tool_message in take_one_step_with_tools(
            prefix_convo=current_convo.prefix(-1),
            message=last_message,
            toolkit=toolkit,
        ):
            if current_convo.messages[-1].id == tool_message.id:
                current_convo.messages[-1] = tool_message
            else:
                current_convo = current_convo.with_suffix(tool_message)

        # print(f'Tool: {tool_message.content}')

        write_to_file_text(str(current_convo), f"{stage_key}.txt", folder="stage_logs")

    return student_messages, current_convo


async def generate_student_response(message_completer, convo, is_last_step, retries):
    """Generate student model response with retries"""
    for completion_attempt in range(retries):
        try:
            completion = await message_completer.async_completion(
                conversations=[convo], n=1, seed=0, end_header=is_last_step
            )
            choice = completion.choices[0]
            messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
            return messages
        except Exception as e:
            if completion_attempt < retries - 1:
                print(f"Student completion attempt {completion_attempt + 1} failed: {e}")
                await asyncio.sleep(1)
                continue
            else:
                print(f"Student completion failed after {retries} attempts: {e}")
                return None


async def process_instance_stage_based(
    instance,
    student_juice=1024,
    eval_code_patch=False,
    max_attempts_per_stage=3,
    output_dir="outputs",
    # passed_rating=["A+", "A", "A-"],
):
    """Process a single instance with stage-based approach"""
    instance_id = instance.get("unique_id", instance.get("instance_id", "unknown"))
    try:
        result = await stage_based_rollout(
            instance=instance,
            student_juice=student_juice,
            eval_code_patch=eval_code_patch,
            max_attempts_per_stage=max_attempts_per_stage,
            output_dir=output_dir,
            # passed_rating=passed_rating,
        )
        return result
    except Exception as e:
        print(f"❌ Failed to process instance {instance_id}: {e}")
        return None


async def process_all_stage_based(
    dataset,
    student_juice=1024,
    eval_code_patch=False,
    max_attempts_per_stage=3,
    concurrency_limit=2,
    output_dir="outputs",
    # passed_rating=["A+", "A", "A-"],
):
    """Process all instances with stage-based approach"""
    semaphore = asyncio.Semaphore(concurrency_limit)
    results = []
    total_instances = len(dataset)
    processed_count = 0
    passed_count = 0
    failed_count = 0

    # Create output directory and initialize files
    os.makedirs(output_dir, exist_ok=True)
    data_file = f"{output_dir}/data.jsonl"
    progress_file = f"{output_dir}/progress.txt"

    async def process_with_semaphore(instance):
        nonlocal processed_count, passed_count, failed_count
        async with semaphore:
            result = await process_instance_stage_based(
                instance,
                student_juice=student_juice,
                eval_code_patch=eval_code_patch,
                max_attempts_per_stage=max_attempts_per_stage,
                output_dir=output_dir,
                # passed_rating=passed_rating,
            )

            processed_count += 1
            if result:
                passed_count += 1
                # Save result immediately to avoid data loss
                with open(data_file, "a") as f:
                    f.write(json.dumps(result, default=str) + "\n")
                print(
                    f"✅ Progress: {processed_count}/{total_instances} processed, {passed_count} passed, {failed_count} failed"
                )
            else:
                failed_count += 1
                print(
                    f"❌ Progress: {processed_count}/{total_instances} processed, {passed_count} passed, {failed_count} failed"
                )

            # Update progress file
            with open(progress_file, "w") as f:
                f.write(f"Processed: {processed_count}/{total_instances}\n")
                f.write(f"Passed: {passed_count}\n")
                f.write(f"Failed: {failed_count}\n")
                f.write(f"Success Rate: {passed_count/processed_count*100:.1f}%\n")

            return result

    tasks = [process_with_semaphore(instance) for instance in dataset]
    for task in asyncio.as_completed(tasks):
        result = await task
        if result:  # Only add results that passed tests
            results.append(result)

    print(
        f"\n🎯 Final Results: {passed_count}/{total_instances} instances passed tests and were included in output"
    )
    return results


# ============================================================================
# MAIN ENTRY POINT
# ============================================================================

data_paths = {
    "rrb_ml_py": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_py/rrb_py_ml_data_merge.jsonl",
    "rrb_ml_ts1": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_ts/rrb_ts_ml_data_merge.jsonl",
    "rrb_ml_ts2": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_ts/rrb_ts_ml_data_merge.jsonl",
    "rrb_ml_js1": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_js/rrb_js_ml_data_merge.jsonl",
    "rrb_ml_js2": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_js/rrb_js_ml_data_merge.jsonl",
    "sbh_v2_train": "az://orngscuscresco/data/damajercak/swe/upload06272025/sbhv2/train/train.jsonl",
    "swebench_train_rewrite1": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/swb_train_rewritten_4x/swe_bench_train_updated.rewritten1.jsonl",
    "swebench_train_rewrite2": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/swb_train_rewritten_4x/swe_bench_train_updated.rewritten2.jsonl",
    "swebench_train_rewrite3": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/swb_train_rewritten_4x/swe_bench_train_updated.rewritten3.jsonl",
    "swebench_train_rewrite4": "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/swb_train_rewritten_4x/swe_bench_train_updated.rewritten4.jsonl",
}


def stage_based_main(
    dataset_name="all",
    output_dir="stage_based_outputs",
    concurrency_limit=100,
    max_attempts_per_stage=10,
    eval_code_patch=False,
    sample_subset=None,
    # passed_rating=["A+", "A", "A-"],
):
    """Main function for stage-based data collection"""

    if dataset_name == "all":
        # Process all datasets by merging them
        print(f"Processing all datasets: {list(data_paths.keys())}")
        all_datasets = []

        for ds_name, azure_path in data_paths.items():
            dataset_file_path = f"/root/code/glass/data/jsonl/{ds_name}.jsonl"

            # Check if the dataset file exists, if not download it from Azure blob
            if not os.path.exists(dataset_file_path):
                print(f"Dataset file {dataset_file_path} not found. Downloading from Azure blob...")
                os.makedirs(os.path.dirname(dataset_file_path), exist_ok=True)

                download_cmd = f"bbb cp {azure_path} {dataset_file_path}"
                result = subprocess.run(download_cmd, shell=True)
                if result.returncode != 0:
                    raise RuntimeError(f"Failed to download dataset file: {download_cmd}")
                print(f"Dataset downloaded successfully to {dataset_file_path}")

            # Load and add to combined dataset
            ds_data = load_from_jsonl(dataset_file_path)
            print(f"Loaded {len(ds_data)} instances from {ds_name}")
            all_datasets.extend(ds_data)

        dataset = all_datasets
        print(f"Combined dataset has {len(dataset)} total instances")

    else:
        # Process single dataset (existing behavior)
        if dataset_name in data_paths:
            azure_path = data_paths[dataset_name]
            dataset_name_or_path = f"/root/code/glass/data/jsonl/{dataset_name}.jsonl"
        else:
            # Assume it's a direct path
            dataset_name_or_path = dataset_name
            azure_path = dataset_name

        # Check if the dataset file exists, if not download it from Azure blob
        if not os.path.exists(dataset_name_or_path):
            print(f"Dataset file {dataset_name_or_path} not found. Downloading from Azure blob...")
            os.makedirs(os.path.dirname(dataset_name_or_path), exist_ok=True)

            download_cmd = f"bbb cp {azure_path} {dataset_name_or_path}"
            result = subprocess.run(download_cmd, shell=True)
            if result.returncode != 0:
                raise RuntimeError(f"Failed to download dataset file: {download_cmd}")
            print(f"Dataset downloaded successfully to {dataset_name_or_path}")

        # Load dataset
        dataset = load_from_jsonl(dataset_name_or_path)

    subprocess.run("mkdir /var/log/supervisor", shell=True)

    # Shuffle dataset for random processing order
    if isinstance(sample_subset, str) and ":" in sample_subset:
        start_str, end_str = sample_subset.split(":")
        start = int(start_str)
        end = int(end_str)
        print(f"Selecting dataset subset from {start} to {end} (exclusive)")
        dataset = dataset[start:end]
    elif isinstance(sample_subset, int) and sample_subset < len(dataset):
        print(f"Limiting dataset to {sample_subset} samples")
        dataset = dataset[:sample_subset]
    else:
        print(f"No limit on dataset size, using all {len(dataset)} samples")

    # if sample_subset is not None and sample_subset < len(dataset):
    #     print(f"Limiting dataset to {sample_subset} samples")
    #     dataset = dataset[:sample_subset]
    # else:
    #     print(f"No limit on dataset size, using all {len(dataset)} samples")
    random.shuffle(dataset)

    # Remove dataset limit for large-scale generation
    print(f"Loaded {len(dataset)} instances for large-scale data generation")

    # Create output directory
    os.makedirs(f"{output_dir}", exist_ok=True)

    print(f"Running stage-based data collection on {len(dataset)} instances.")

    # Configuration for large-scale generation
    student_juice = 128

    # Run stage-based processing
    results = asyncio.run(
        process_all_stage_based(
            dataset,
            student_juice=student_juice,
            max_attempts_per_stage=max_attempts_per_stage,
            concurrency_limit=concurrency_limit,
            output_dir=output_dir,
            eval_code_patch=eval_code_patch,
            # passed_rating=passed_rating,
        )
    )

    # Print results
    print("*" * 100)
    print(f"🎉 Large-Scale Data Generation Complete!")
    print(f"Total instances that passed tests: {len(results)}")
    print(f"Data incrementally saved to: {output_dir}/data.jsonl")
    print(f"Progress tracking saved to: {output_dir}/progress.txt")

    with open(f"{output_dir}/data_collected.jsonl", "w") as f:
        for result in results:
            f.write(json.dumps(result, default=str) + "\n")

    if len(results) > 0:
        print(f"Successfully generated {len(results)} high-quality datapoints with passing tests")
        # Data is already saved incrementally, no need to write again
        print(f"All results are already saved in {output_dir}/data.jsonl")
    else:
        print("⚠️  No instances passed tests - no data generated")
    print("*" * 100)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Stage-based data collection for SWE tasks")
    parser.add_argument(
        "--dataset",
        "-d",
        type=str,
        default="sbh_v2_train",
        help=f"Dataset name or path. Use 'all' to process all datasets, or specify one: {list(data_paths.keys())}",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=str,
        default="/root/data/sft_data/sbhv2/exp",
        help="Output directory name",
    )
    parser.add_argument(
        "--concurrency",
        "-c",
        type=int,
        default=100,
        help="Concurrency limit for parallel processing",
    )
    parser.add_argument(
        "--max-attempts", "-m", type=int, default=10, help="Maximum attempts per stage"
    )
    parser.add_argument(
        "--eval_code_patch",
        action="store_true",
        help="Enable code patch evaluation after completion",
    )
    parser.add_argument("--subset", type=str, default=None, help="")
    parser.add_argument("--passed_rating", type=str, default="A+_A_A-", help="")

    args = parser.parse_args()

    print(f"Running stage-based data collection with:")
    print(f"  Dataset: {args.dataset}")
    print(f"  Output directory: {args.output_dir}")
    print(f"  Concurrency limit: {args.concurrency}")
    print(f"  Max attempts per stage: {args.max_attempts}")

    output_dir = Path(args.output_dir)
    output_dir = output_dir / datetime.now().strftime("%Y%m%d-%H%M%S")
    output_dir.mkdir(parents=True, exist_ok=True)

    # passed_rating = args.passed_rating.split('_')
    # print(f"  Passed rating criteria: {passed_rating}")

    stage_based_main(
        dataset_name=args.dataset,
        output_dir=str(output_dir),
        concurrency_limit=args.concurrency,
        max_attempts_per_stage=args.max_attempts,
        eval_code_patch=args.eval_code_patch,
        sample_subset=args.subset,
        # passed_rating=passed_rating,
    )
