"""
Extracts **correct** samples from a list of berry experiments
(should work with qbs, run_eval, run_experiment -- even if still in flight)
"""

import chz
from mini.finetune.datasets.jsonl import write_jsonl
from rlsnow import reader
from rlsnow.reader_impl import const


def main(
    experiment_ids: tuple[str, ...],
    output_path: str,
    security_profile: str = "strawberry",
    shard: int = 256,
    download_all_rollouts: bool = False,
    filter_by_dataset_id: str = None,
) -> None:
    r = reader.RLSnowReader(security_profile)
    experiment_selects = []
    for expt_id in experiment_ids:
        # shard = const.compute_shard(expt_id, 256)
        experiment_selects.append(
            # f"SELECT * FROM public_se_256.sample_events_s{shard} WHERE base_experiment_id = '{expt_id}'"
            f"SELECT * FROM public.sample_events WHERE base_experiment_id = '{expt_id}'"
        )
    samples_cte = " UNION ALL ".join(experiment_selects)

    if download_all_rollouts:
        if filter_by_dataset_id is not None:
            samples = r.execute(
                f"""
            WITH expt_samples AS ({samples_cte})
            SELECT
                b.*,
                a.data:prompt::string prompt,
                a.data:conversation::string conversation,
                a.data:accuracy::number accuracy
            FROM expt_samples a
            JOIN (
                SELECT DISTINCT
                    sample_id,
                    data:gt_datapoint_serializable:dataset_id::string dataset_id,
                    data:gt_datapoint_serializable:unique_id::string unique_id
                FROM expt_samples
                WHERE event_type = 'driver_upload_sample_batch'
                AND data:gt_datapoint_serializable:dataset_id::string NOT LIKE '%repo_os%'
                AND data:gt_datapoint_serializable:dataset_id::string = '{filter_by_dataset_id}'
            ) b
            ON a.sample_id = b.sample_id
                AND a.event_type = 'worker_sample_complete'
            """,
                parse_json_fields=["prompt", "conversation", "accuracy"],
            )
        else:
            samples = r.execute(
                f"""
            WITH expt_samples AS ({samples_cte})
            SELECT
                b.*,
                a.data:prompt::string prompt,
                a.data:conversation::string conversation,
                a.data:accuracy::number accuracy
            FROM expt_samples a
            JOIN (
                SELECT DISTINCT
                    sample_id,
                    data:gt_datapoint_serializable:dataset_id::string dataset_id,
                    data:gt_datapoint_serializable:unique_id::string unique_id
                FROM expt_samples
                WHERE event_type = 'driver_upload_sample_batch'
                AND data:gt_datapoint_serializable:dataset_id::string NOT LIKE '%repo_os%'
            ) b
            ON a.sample_id = b.sample_id
                AND a.event_type = 'worker_sample_complete'
            """,
                parse_json_fields=["prompt", "conversation", "accuracy"],
            )
    else:
        if filter_by_dataset_id is not None:
            samples = r.execute(
                f"""
            WITH expt_samples AS ({samples_cte})
            SELECT
                b.*,
                a.data:prompt::string prompt,
                a.data:conversation::string conversation
            FROM expt_samples a
            JOIN (
                SELECT DISTINCT
                    sample_id,
                    data:gt_datapoint_serializable:dataset_id::string dataset_id,
                    data:gt_datapoint_serializable:unique_id::string unique_id
                FROM expt_samples
                WHERE event_type = 'driver_upload_sample_batch'
                AND data:gt_datapoint_serializable:dataset_id::string = '{filter_by_dataset_id}'
            ) b
            ON a.sample_id = b.sample_id
                AND a.event_type = 'worker_sample_complete'
                AND a.data:accuracy = 1.0
            """,
                parse_json_fields=["prompt", "conversation"],
            )
        else:
            samples = r.execute(
                f"""
            WITH expt_samples AS ({samples_cte})
            SELECT
                b.*,
                a.data:prompt::string prompt,
                a.data:conversation::string conversation
            FROM expt_samples a
            JOIN (
                SELECT DISTINCT
                    sample_id,
                    data:gt_datapoint_serializable:dataset_id::string dataset_id,
                    data:gt_datapoint_serializable:unique_id::string unique_id
                FROM expt_samples
                WHERE event_type = 'driver_upload_sample_batch'
            ) b
            ON a.sample_id = b.sample_id
                AND a.event_type = 'worker_sample_complete'
                AND a.data:accuracy = 1.0
            """,
                parse_json_fields=["prompt", "conversation"],
            )

    write_jsonl(output_path, samples)
    print(f"Success: saved {len(samples)} samples to {output_path}")


if __name__ == "__main__":
    chz.entrypoint(main)
