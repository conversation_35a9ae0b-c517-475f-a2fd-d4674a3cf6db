import argparse
import copy
import json

import chat
from mill.common import read_jsonl, write_jsonl
from tqdm import tqdm


def main(input_file, output_file):
    all_samples = read_jsonl(input_file)
    print(f"Total samples: {len(all_samples)}")
    with open(output_file, "w") as f:
        for sample in tqdm(all_samples):
            new_sample = copy.deepcopy(sample)
            try:
                conv = chat.Conversation(
                    messages=sample["prompt"]["messages"] + sample["conversation"]["messages"]
                )
                new_sample["conversation"] = conv.model_dump(mode="json")
                f.write(json.dumps(new_sample, default=str) + "\n")
            except Exception as e:
                print(f"Exception: {e} \n {sample['sample_id']}")


if __name__ == "__main__":
    chz.entrypoint(main)
