import argparse
import json
import os
from copy import deepcopy
from datetime import datetime
from itertools import product
from pathlib import Path

from mill.common import read_jsonl
from tqdm import tqdm


def combine_jsonl_files(all_files, output_file):
    all_samples = []
    for input_file in tqdm(all_files, desc="Combining JSONL files"):
        all_samples.extend(read_jsonl(input_file))

    with open(output_file, "w") as f:
        for sample in all_samples:
            f.write(f"{json.dumps(sample)}\n")

    print(f"Combined {len(all_files)} files into {output_file}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Combine multiple JSONL files into one")
    parser.add_argument(
        "--input_files",
        type=str,
        nargs="+",
        required=True,
        help="List of input JSONL files to combine",
    )
    parser.add_argument(
        "--output_file", type=str, required=True, help="Output file path for combined JSONL"
    )

    args = parser.parse_args()
    Path(args.output_file).parent.mkdir(parents=True, exist_ok=True)

    combine_jsonl_files(args.input_files, args.output_file)
