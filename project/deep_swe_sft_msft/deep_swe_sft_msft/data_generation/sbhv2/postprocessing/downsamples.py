import json
import random
from collections import defaultdict
from pathlib import Path

import chat
import chz
from mini.finetune.datasets.jsonl import read_jsonl, write_jsonl
from tqdm import tqdm


def filter_good_samples(datapoints, reference_datapoints, topk, truncate):
    id2samples = defaultdict(list)
    for dp in datapoints:
        uid = dp["unique_id"]
        id2samples[uid].append(dp)

    refid2samples = defaultdict(list)
    for dp in reference_datapoints:
        uid = dp["unique_id"]
        refid2samples[uid].append(dp)

    # For each unique_id, pick the top-k by conversation length
    filtered = []
    for uid, dp_list in id2samples.items():

        passrate = float(len(dp_list)) / len(refid2samples[uid])
        dp_list.sort(key=lambda x: len(x["conversation"]["messages"]), reverse=True)

        if passrate > 0.75:
            print(
                f"{len(dp_list)} correct samples, passrate={float(len(dp_list)) / len(refid2samples[uid])} > 0.75, keep at most 3."
            )
            filtered.extend(dp_list[:3])
        elif passrate > 0.5:
            print(
                f"{len(dp_list)} correct samples, passrate={float(len(dp_list)) / len(refid2samples[uid])} > 0.5, keep at most 5."
            )
            filtered.extend(dp_list[:5])
        else:
            if len(dp_list) <= 3:
                print(
                    f"{len(dp_list)} correct samples, passrate={float(len(dp_list)) / len(refid2samples[uid])} <= 0.5, small samples, keep all."
                )
                filtered.extend(dp_list)
            elif len(dp_list) <= truncate:
                print(
                    f"{len(dp_list)} correct samples, passrate={float(len(dp_list)) / len(refid2samples[uid])} <= 0.5, moderate samples, keep {int((1-passrate) * len(dp_list) * topk) + 1}"
                )
                filtered.extend(dp_list[: int((1 - passrate) * len(dp_list) * topk) + 1])
            else:
                print(
                    f"{len(dp_list)} correct samples, passrate={float(len(dp_list)) / len(refid2samples[uid])} <= 0.5, lots of samples, keep {min(int((1-passrate) * len(dp_list) * topk) + 1, truncate)}."
                )
                filtered.extend(
                    dp_list[: min(int((1 - passrate) * len(dp_list) * topk) + 1, truncate)]
                )

    return filtered


def main(
    sample_path: str = "",
    sample_path_reference: str = "",
    output_path: str = "",
    target_size: int = 4096,
    topk: float = 1,
    truncate: int = 10,
    filter_by_dataset_id: str = None,
    filter_by_accuracy: bool = False,
    filter_by_good_samples: bool = True,
) -> None:

    datapoints = read_jsonl(sample_path)
    # reference_datapoints = read_jsonl(sample_path_reference)
    print("Original datapoints: ", len(datapoints))

    if filter_by_dataset_id is not None:
        print(f"Filtered by dataset_id {filter_by_dataset_id}")
        datapoints = [dp for dp in datapoints if dp["dataset_id"] in filter_by_dataset_id]
        print(f"After filtered by {filter_by_dataset_id}: ", len(datapoints))

    if filter_by_accuracy:
        print("Filtered by accuracy.")
        datapoints = [dp for dp in datapoints if dp["accuracy"] == 1.0]
        print(f"After filtered by accuracy: ", len(datapoints))

    if filter_by_good_samples:
        # datapoints = filter_good_samples(datapoints, reference_datapoints, topk, truncate)
        print(f"Filter long samples with unique instance id: {len(datapoints)}")

    datapoints = random.sample(datapoints, min(target_size, len(datapoints)))  # No replacement
    print("Subsampled datapoints: ", len(datapoints))

    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
    # write_jsonl(output_path, datapoints)
    with open(output_path, "w") as f:
        for sample in tqdm(datapoints, desc="Writing samples to output file"):
            f.write(f"{json.dumps(sample)}\n")


if __name__ == "__main__":
    chz.entrypoint(main)
