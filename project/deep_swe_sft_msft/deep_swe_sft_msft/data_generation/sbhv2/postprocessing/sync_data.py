import os
import subprocess
import time
from datetime import datetime

LOCAL_PATH = "/root/code/glass/data/qingruzhang/swe/sft_data/wf_sft/sbhv2_wf_v6/"
AZURE_PATH = "az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/sbhv2_wf_v6/"
SYNC_INTERVAL_SECONDS = 600  # 10 minutes


def sync(local_folder, azure_folder):
    cmd = [
        "bbb",
        "sync",
        local_folder,
        azure_folder,
    ]
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode == 0:
        print("Sync successful.")
    else:
        print(f"Sync failed with code {result.returncode}.")


if __name__ == "__main__":
    subset_folders = [
        "subset_0-1000",
        "subset_1000-2000",
        "subset_2000-3000",
        "subset_3000-4000",
        "subset_4000-5000",
        "subset_5000-6000",
        "subset_6000-7000",
        "subset_7000-8000",
        "subset_8000-9000",
        "subset_9000-10000",
        "subset_10000-11000",
        "subset_11000-12000",
        "subset_12000-13000",
        "subset_13000-14000",
        "subset_14000-15000",
        "subset_15000-16000",
        "subset_16000-17000",
        "subset_17000-18000",
        "subset_18000-19000",
        "subset_19000-20000",
        # "all_data",
    ]
    while True:
        for folder in subset_folders:
            local_folder = f"{LOCAL_PATH}{folder}"
            azure_folder = f"{AZURE_PATH}{folder}"
            if os.path.exists(local_folder):
                print(f"Syncing {local_folder} to {azure_folder}...")
                sync(local_folder, azure_folder)
            else:
                print(f"Local folder {local_folder} does not exist, skipping sync.")
        print(
            f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] All folders synced. Waiting for next sync interval..."
        )
        # Wait for the specified interval before the next sync
        for i in range(SYNC_INTERVAL_SECONDS, 0, -1):
            print(f"\rNext sync up will be after {i} seconds", end="", flush=True)
            time.sleep(1)
