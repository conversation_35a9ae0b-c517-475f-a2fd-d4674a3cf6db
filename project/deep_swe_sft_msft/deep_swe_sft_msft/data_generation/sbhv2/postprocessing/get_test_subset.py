import argparse
import json
import os
import random
from copy import deepcopy
from datetime import datetime
from itertools import product
from pathlib import Path

from mill.common import read_jsonl
from tqdm import tqdm


def get_devset_from_sft_data(input_files, output_file, num_samples=None):
    all_samples = []
    all_instance_ids = set()
    for input_file in tqdm(input_files, desc="Extracting devset samples"):
        samples = read_jsonl(input_file)
        for sample in samples:
            if sample["original_instance"]["unique_id"] not in all_instance_ids:
                all_instance_ids.add(sample["original_instance"]["unique_id"])
                all_samples.append(sample["original_instance"])

    # Randomly select num_samples from all_samples
    if num_samples is not None and num_samples > 0:
        devset_samples = random.sample(all_samples, k=num_samples)
    else:
        devset_samples = all_samples
    with open(output_file, "w") as f:
        for sample in devset_samples:
            f.write(f"{json.dumps(sample)}\n")

    print(f"Extracted {len(devset_samples)} samples into {output_file}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Combine multiple JSONL files into one")
    parser.add_argument(
        "--input_files",
        type=str,
        nargs="+",
        required=True,
        help="List of input JSONL files to combine",
    )
    parser.add_argument(
        "--output_file", type=str, required=True, help="Output file path for combined JSONL"
    )
    parser.add_argument("--num_samples", type=int, default=None, help="")

    args = parser.parse_args()
    output_file = Path(args.output_file)
    output_file.parent.mkdir(parents=True, exist_ok=True)

    get_devset_from_sft_data(args.input_files, str(output_file), args.num_samples)
