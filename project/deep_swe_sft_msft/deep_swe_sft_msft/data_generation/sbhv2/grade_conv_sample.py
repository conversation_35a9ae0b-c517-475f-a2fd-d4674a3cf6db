import asyncio
import copy
import json
import os
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Coroutine, Dict, List, Optional, Tuple, Unpack, cast

import chat
from chat import Conversation, ConversationMetadata, Message, Role
from deep_swe_sft_msft.data_generation.grading import SWE_BEHAVIOR_RUBRIC, ConversationGrader
from deep_swe_sft_msft.data_generation.grading.swe_workflow_grader import (
    RUBRIC_CONFIGS,
    SWEWorkFlowGrader,
)
from deep_swe_sft_msft.data_generation.sbhv2.gen_wf_sbhv2 import asdict, is_easy_language
from mill.common import read_jsonl, write_jsonl


def remove_wrong_tool_call_from_conv_messages(
    conv_messages: List[chat.Message],
) -> List[chat.Message]:
    error_msgs = ["Error parsing function call:", "Could not parse args as JSON:"]
    clean_messages = []
    if conv_messages[0].author.role == Role.SYSTEM:
        clean_messages.append(conv_messages[0])
        if conv_messages[1].author.role == Role.USER:
            clean_messages.append(conv_messages[1])
            start_idx = 2
        elif conv_messages[1].author.role == Role.ASSISTANT:
            start_idx = 1
        else:
            raise ValueError("Unexpected role for the second message")
    elif conv_messages[0].author.role == Role.USER:
        clean_messages.append(conv_messages[0])
        if conv_messages[1].author.role == Role.ASSISTANT:
            start_idx = 1
        else:
            raise ValueError("Unexpected role for the first message")

    i = start_idx
    while i < len(conv_messages):
        msg = conv_messages[i]

        # Check if this is a tool message with error content
        is_error_tool = False
        if msg.author.role == Role.TOOL:
            content = str(msg.content)
            if any(error_msg in content for error_msg in error_msgs):
                is_error_tool = True

        if is_error_tool:
            # Skip this tool message with error
            # Also check if the previous message was from assistant and skip it too
            if clean_messages and clean_messages[-1].author.role == Role.ASSISTANT:
                # Remove the previous assistant message
                clean_messages.pop()
            # Skip the current error tool message (don't add it to clean_messages)
            i += 1
            continue

        # Keep the message if it's not a stage prompt or error tool
        clean_messages.append(msg)
        i += 1

    return clean_messages


async def grade_conversation(
    instance,
    criteria_list=["report_progress"],
    max_attempts=3,
    output_dir="outputs",
    truncate_token_limit=256,
    apply_conv_grader=False,
    args=None,
):
    """Process a single instance with stage-based approach"""
    instance_id = instance.get("unique_id", instance.get("instance_id", "unknown"))
    conversation = instance.get("conversation", [])
    metadata = instance.get("metadata", {})
    if not conversation:
        print(f"❌ No conversation found for instance {instance_id}")
        return None

    if "pr_languages" in metadata and not is_easy_language(metadata):
        all_criteria_list = RUBRIC_CONFIGS["SP_FOLLOWING_RUBRIC_HARD"]
        criteria_list = [criteria for criteria in criteria_list if criteria in all_criteria_list]
        print(f"Updated criteria list for hard languages: {criteria_list}")

    swe_workflow_grader = SWEWorkFlowGrader(retries=max_attempts)
    conversation_grader = ConversationGrader(retries=max_attempts)
    if (
        args
        and hasattr(args, "combine_prompt_conv")
        and args.combine_prompt_conv
        and conversation["messages"][0]["author"]["role"] not in ["system", "user"]
    ):
        conv = chat.Conversation(messages=instance["prompt"]["messages"] + conversation["messages"])
    else:
        conv = chat.Conversation(messages=conversation["messages"])
    if args and hasattr(args, "clean_tool_call") and args.clean_tool_call:
        conv.messages = remove_wrong_tool_call_from_conv_messages(conv.messages)

    try:
        eval_result = await swe_workflow_grader.evaluate_conversation(
            conversation=conv,
            criteria_list=criteria_list,
            truncate_token_limit=truncate_token_limit,
        )
        eval_result = asdict(eval_result)
        result = copy.deepcopy(instance)
        result["conversation"] = conv.model_dump(mode="json")
        if not apply_conv_grader:
            result["grade_result"] = eval_result
            result["grade_passed"] = eval_result.get("is_passed", False)
            return result
        else:
            conv_grader_result = await conversation_grader.evaluate_conversation(
                conv, SWE_BEHAVIOR_RUBRIC, "conversation_grade"
            )
            conv_grader_result = asdict(conv_grader_result)
            result["grade_result"] = {
                "swe_workflow_grader": eval_result,
                "conversation_grader": conv_grader_result,
            }
            result["grade_passed"] = eval_result.get("is_passed", False) and conv_grader_result.get(
                "is_good_conversation", False
            )
            return result
    except Exception as e:
        print(f"❌ Failed to process instance {instance_id}: {e}")
        return None


async def grade_all_samples(
    dataset,
    criteria_list=["report_progress"],
    concurrency_limit=2,
    output_dir="outputs",
    max_attempts=3,
    truncate_token_limit=256,
    apply_conv_grader=False,
    args=None,
):
    """Process all instances with stage-based approach"""
    semaphore = asyncio.Semaphore(concurrency_limit)
    results = []
    total_instances = len(dataset)
    processed_count = 0
    passed_count = 0
    failed_count = 0

    # Create output directory and initialize files
    os.makedirs(output_dir, exist_ok=True)
    data_file = f"{output_dir}/data.jsonl"
    passed_data_file = f"{output_dir}/passed_data.jsonl"
    failed_data_file = f"{output_dir}/failed_data.jsonl"
    progress_file = f"{output_dir}/progress.txt"

    async def process_with_semaphore(instance):
        nonlocal processed_count, passed_count, failed_count
        async with semaphore:
            result = await grade_conversation(
                instance,
                criteria_list=criteria_list,
                max_attempts=max_attempts,
                output_dir=output_dir,
                truncate_token_limit=truncate_token_limit,
                apply_conv_grader=apply_conv_grader,
                args=args,
            )

            processed_count += 1
            if result is not None:
                with open(data_file, "a") as f:
                    f.write(json.dumps(result, default=str) + "\n")
                if result.get("grade_passed", False):
                    passed_count += 1
                    # Save result immediately to avoid data loss
                    with open(passed_data_file, "a") as f:
                        f.write(json.dumps(result, default=str) + "\n")
                    print(
                        f"✅ Progress: {processed_count}/{total_instances} processed, {passed_count} passed, {failed_count} failed"
                    )
                else:
                    failed_count += 1
                    with open(failed_data_file, "a") as f:
                        f.write(json.dumps(result, default=str) + "\n")
                    print(
                        f"❌ Progress: {processed_count}/{total_instances} processed, {passed_count} passed, {failed_count} failed"
                    )

            # Update progress file
            with open(progress_file, "w") as f:
                f.write(f"Processed: {processed_count}/{total_instances}\n")
                f.write(f"Passed: {passed_count}\n")
                f.write(f"Failed: {failed_count}\n")
                f.write(f"Success Rate: {passed_count/processed_count*100:.1f}%\n")

            return result

    tasks = [process_with_semaphore(instance) for instance in dataset]
    for task in asyncio.as_completed(tasks):
        result = await task
        if result:  # Only add results that passed tests
            results.append(result)

    print(
        f"\n🎯 Final Results: {passed_count}/{total_instances} instances passed tests and were included in output"
    )
    return results


def grade_sample_main(
    input_file,
    criteria_list=["report_progress"],
    output_dir="./outputs",
    concurrency_limit=100,
    sample_subset=None,
    max_attempts=3,
    truncate_token_limit=256,
    apply_conv_grader=False,
    args=None,
):
    # Load dataset
    dataset = read_jsonl(input_file)

    # Shuffle dataset for random processing order
    if isinstance(sample_subset, str) and ":" in sample_subset:
        start_str, end_str = sample_subset.split(":")
        start = int(start_str)
        end = int(end_str)
        print(f"Selecting dataset subset from {start} to {end} (exclusive)")
        dataset = dataset[start:end]
    elif isinstance(sample_subset, int) and sample_subset < len(dataset):
        print(f"Limiting dataset to {sample_subset} samples")
        dataset = dataset[:sample_subset]
    else:
        print(f"No limit on dataset size, using all {len(dataset)} samples")

    os.makedirs(f"{output_dir}", exist_ok=True)
    print(f"Loaded {len(dataset)} instances for conversation grading")
    print(f"Running conversation grading on {len(dataset)} instances.")

    # Run stage-based processing
    results = asyncio.run(
        grade_all_samples(
            dataset,
            criteria_list=criteria_list,
            concurrency_limit=concurrency_limit,
            output_dir=output_dir,
            max_attempts=max_attempts,
            truncate_token_limit=truncate_token_limit,
            apply_conv_grader=apply_conv_grader,
            args=args,
        )
    )

    # Print results
    print("*" * 100)
    print(f"Data Evaluation Completed")
    print(f"Data incrementally saved to: {output_dir}/data.jsonl")
    print(f"Progress tracking saved to: {output_dir}/progress.txt")

    with open(f"{output_dir}/data_collected.jsonl", "w") as f:
        for result in results:
            f.write(json.dumps(result, default=str) + "\n")

    print(f"Results saved to: {output_dir}/data_collected.jsonl")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Grade sample conversations")
    parser.add_argument(
        "--input_file",
        type=str,
        required=True,
        help=f"Input file containing sample conversations in JSON format",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=str,
        default="/root/data/sft_data/sbhv2/exp",
        help="Output directory name",
    )
    parser.add_argument(
        "--criteria_list",
        type=str,
        nargs="+",
        required=True,
        help="List of rubic ids to use for grading",
    )
    parser.add_argument(
        "--concurrency",
        "-c",
        type=int,
        default=100,
        help="Concurrency limit for parallel processing",
    )
    parser.add_argument(
        "--max_attempts", "-m", type=int, default=3, help="Maximum attempts per grader"
    )
    parser.add_argument("--truncate_token_limit", type=int, default=256, help="")
    parser.add_argument(
        "--apply_conv_grader",
        action="store_true",
        help="Enable conversation grader.",
    )
    parser.add_argument(
        "--combine_prompt_conv",
        action="store_true",
        help="Combine prompt and conv.",
    )
    parser.add_argument(
        "--clean_tool_call",
        action="store_true",
        help="",
    )
    parser.add_argument("--subset", type=str, default=None, help="")

    args = parser.parse_args()

    print(f"Running stage-based data collection with:")
    print(f"  Input file: {args.input_file}")
    print(f"  Output directory: {args.output_dir}")
    print(f"  Concurrency limit: {args.concurrency}")
    print(f"  Max attempts per stage: {args.max_attempts}")

    output_dir = Path(args.output_dir)
    output_dir = output_dir / datetime.now().strftime("%Y%m%d-%H%M%S")
    output_dir.mkdir(parents=True, exist_ok=True)

    all_criteria_list = RUBRIC_CONFIGS["SP_FOLLOWING_RUBRIC_EASY"]
    for criteria in args.criteria_list:
        if criteria not in all_criteria_list:
            raise ValueError(f"Invalid criteria: {criteria}. Must be one of {all_criteria_list}")

    print(f"Using Criteria List: {args.criteria_list}")

    grade_sample_main(
        input_file=args.input_file,
        output_dir=str(output_dir),
        criteria_list=args.criteria_list,
        concurrency_limit=args.concurrency,
        max_attempts=args.max_attempts,
        sample_subset=args.subset,
        truncate_token_limit=args.truncate_token_limit,
        apply_conv_grader=args.apply_conv_grader,
        args=args,
    )
