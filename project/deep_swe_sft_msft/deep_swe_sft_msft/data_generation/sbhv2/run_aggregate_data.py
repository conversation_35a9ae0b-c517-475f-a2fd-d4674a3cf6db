import argparse
import json
import os
from copy import deepcopy
from datetime import datetime
from itertools import product
from pathlib import Path

from mill.common import read_jsonl
from tqdm import tqdm

BASE_DIR = Path("/root/data/qingruzhang/swe/sft_data/wf_sft/sbhv2_wf_v6")
SUBSETS = [f"subset_{i}-{i+1000}" for i in range(0, 20000, 1000)]
RATING_DIRS = ("rating_3A", "rating_3A2B")
OUTPUT_DIR = BASE_DIR / "all_data"
PDW_INSTRUCTION_PREFIX = "You should act like a perfect software engineer. Your goal is to solve the software engineering problem systematically."


def iter_jsonl_paths(
    base_dir: Path = BASE_DIR,
    subsets: list[str] = SUBSETS,
    rating_dirs: tuple[str, ...] = RATING_DIRS,
) -> list[Path]:
    """Return a list of every data.jsonl file that actually exists."""
    paths: list[Path] = []
    for subset, rating in product(subsets, rating_dirs):
        rating_root = base_dir / subset / rating  # subset_xxx/rating_xxx
        if not rating_root.exists():
            continue
        # look for */data.jsonl one level below the rating folder
        paths.extend(rating_root.glob("*/*data.jsonl"))
    return paths


def check_and_correct_sample(sample: dict) -> dict:
    if not sample["conversation"]["messages"][-1]["end_turn"]:
        print(f"Correcting end_turn for sample: {sample['original_instance']['unique_id']}")
        sample["conversation"]["messages"][-1]["end_turn"] = True

    return sample


def main(
    base_dir: Path = BASE_DIR,
    subsets: list[str] = SUBSETS,
    rating_dirs: tuple[str, ...] = RATING_DIRS,
    passed_rating: list[str] = ["A+", "A", "A-"],
    output_dir: Path = OUTPUT_DIR,
) -> None:
    files = iter_jsonl_paths(
        base_dir=base_dir,
        subsets=subsets,
        rating_dirs=rating_dirs,
    )
    if not files:
        print("No data.jsonl files found — nothing to do.")
        return

    all_samples = []
    for fpath in tqdm(files, desc="Aggregating"):
        samples = read_jsonl(str(fpath))
        print(f"Processing {fpath} with {len(samples)} samples")
        for sample in samples:
            if sample["eval_data_result"]["eval_patch_result"]["rating"] not in passed_rating:
                continue
            new_sample = deepcopy(sample)
            new_sample = check_and_correct_sample(new_sample)
            all_samples.append(new_sample)

    file_name = f"data_sbhv2_rating-{''.join(passed_rating)}_{datetime.now().strftime('%Y%m%d-%H%M')}_{len(all_samples)}.jsonl"
    output_file = output_dir / file_name
    with output_file.open("w") as f_out:
        for new_sample in all_samples:
            f_out.write(f"{json.dumps(new_sample)}\n")

    print(f"Processed {len(all_samples)} samples from {len(files)} files into {output_file}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Stage-based data collection for SWE tasks")
    parser.add_argument("--base_dir", type=str, default=str(BASE_DIR))
    parser.add_argument("--subsets", type=str, nargs="+", default=SUBSETS)
    parser.add_argument("--rating_dirs", type=str, nargs="+", default=RATING_DIRS)
    parser.add_argument("--passed_rating", type=str, default="A+_A_A-")
    parser.add_argument("--output_dir", type=str, default=str(OUTPUT_DIR))
    args = parser.parse_args()

    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    passed_rating = args.passed_rating.split("_")

    main(
        base_dir=Path(args.base_dir),
        subsets=args.subsets,
        rating_dirs=tuple(args.rating_dirs),
        passed_rating=passed_rating,
        output_dir=output_dir,
    )
