SWE_STAGE_DEFINITIONS = {
    "stage_1_analysis": {
        "name": "Problem Analysis and Planning",
        "max_steps": 20,
        "description": """
**Stage 1: Planning**

Create a general SWE plan in markdown format explaining about how you are going to approach a SWE problem as few steps as possible.
And calling `report_progress` to report your analysis and plan in a markdown task list.
        """,
        "criteria": [
            # "Provided a clear root cause analysis",
            "criteria1: Called report_progress with analysis and plan summary in a markdown task list",
            "criteria2: The commitMessage in `report_progress` is in markdown format",
            "criteria3: never modified any files in this stage, such as call `str_replace_editor`",
            "criteria4: never run test in this stage",
            "criteria5: never fix or implemented any code in this stage",
        ],
    },
    "stage_2_environment": {
        "name": "Environment Setup and Validation",
        "max_steps": 30,
        "description": """
**Stage 2: Environment Setup and Validation**
Your task is to set up the development environment and validate it's working.

Tasks to complete:
1. Build and set up the environment for the project.
2. Install the project in development mode if applicable (e.g., `pip install -e .`)
3. Install any missing tools or packages using appropriate package managers (apt, npm, pip, etc.)
4. Nicely and successfully build, e.g., you need to run both `npm install` and `npm run build` and `npm install` before `npm run build`.

End this stage by calling `report_progress` with environment setup status in a markdown task list.
        """,
        "criteria": [
            "criteria1: Try to run set up and build the project environment to check for basic setup issues; tests are considered part of the build",
            # "criteria2: Called report_progress with environment setup status in a markdown task list",
            "criteria2: For Node/JavaScript/TypeScript projects, must run `npm install` and `npm run build`; 'npm install' must be run before 'npm run build'",
            "criteria3: Installed the project in development mode if applicable (e.g., `pip install -e .`)",
            "criteria4: Installed missing packages using appropriate package managers",
            "criteria5: Called report_progress with environment setup status in a markdown task list",
        ],
    },
    "stage_3_reproduction": {
        "name": "Problem Reproduction",
        "max_steps": 30,
        "description": """
**Stage 3: Problem Reproduction**
Problem Statement: $PROBLEM_STATEMENT

Your task is to create or run tests that reproduce the reported problem.

Tasks to complete:
1. Look for existing test files that might demonstrate the issue
2. If test suites are available, prefer writing tests into existing files to reproduce the issue instead of creating new test files
3. If no test suites are available, created new test files to reproduce the problem
4. The test should fail initially, confirming the bug exists

End this stage by calling `report_progress` with reproduction results in a markdown task list.
        """,
        "criteria": [
            "criteria1: Looked for and examined existing test files",
            "criteria2: If test suites are available, writing tests into existing files to reproduce the issue instead of creating new test files",
            "criteria3: If no test suites are available, created new test files to reproduce the problem",
            # "criteria4: Called report_progress with reproduction status in a markdown task list",
            "criteria4: If need to add tests, model should write/modify test files and then run test files for reproduction instead of testing in command line, which is not good for debugging.",
            "criteria5: Must run tests to check for issue reproduction",
            "criteria6: Called report_progress with reproduction status in a markdown task list",
        ],
    },
    "stage_4_implementation": {
        "name": "Solution Implementation",
        "max_steps": 128,
        "description": """
**Stage 4: Solution Implementation**
Problem Statement: $PROBLEM_STATEMENT

Your task is to implement the solution to solve the problem.

Tasks to complete:
1. Implement the fix
2. Use proper coding practices and maintain code quality
3. Test your changes incrementally as you implement them
4. Handle any build errors that arise during implementation
5. Use file system tools appropriately, avoid overusing bash tool
6. Test after your fix; retry build after your fix to make sure the project is buildable after fix.

Properly call `report_progress` with implementation details when you made stage changes in a markdown task list.
        """,
        "criteria": [
            "criteria1: Made appropriate changes to the correct files",
            "criteria2: Test after fix; retry build after fix to make sure the project is buildable after fix",
            "criteria3: Resolved any build errors that occurred",
            "criteria4: Used appropriate tools (not overusing bash)",
            "criteria5: Called `report_progress` in a markdown task list with implementation details",
            "criteria6: The whole solution should never change `run_tests.sh` file.",
            "criteria7: The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items. Be strict on this.",
        ],
    },
    "stage_5_finalization": {
        "name": "Final Documentation and PR Report",
        "max_steps": 10,
        "description": """
**Stage 5: Final Documentation and PR Report**
Your task is to create comprehensive documentation of your solution.

Tasks to complete:
1. Create a detailed PR description explaining what was changed and why
2. Document the fix with clear before/after behavior
3. List all files that were modified
4. Include the issue number in the PR description in format: `Fixes #{issue_number}` if applicable
5. Include testing information and any new tests created
6. Format as: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>
        """,
        "criteria": [
            "criteria1: Created a comprehensive PR description in format: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>",
            "criteria2: PR description includes the issue number in format: `Fixes #{issue_number}` if applicable",
            "criteria3: Don't use `report_progress` in this stage",
        ],
    },
}

SBH_SWE_EASY_LANG_STAGE_DEFINITIONS_V2 = {
    "stage_1_analysis": {
        "name": "Problem Analysis and Planning",
        "max_steps": 20,
        "description": """
Problem Statement: $PROBLEM_STATEMENT

**Stage 1: Planning**

Report a general SWE plan in markdown format via `report_progress` function, explaining about how you are going to approach the given problem as few steps as possible.
Analyze the problem statement thoroughly. Fully understand the issue and comments provided
Report your plan in a markdown task list format via `report_progress` function.
Do NOT implement any solutions yet - this is purely analysis and planning.
Do NOT create or edit any files.
Do NOT run tests in this stage.

And calling `report_progress` to report your analysis and plan in a markdown task list. The commitMessage in `report_progress` is in markdown format. 
        """,
        "criteria": [
            "criteria1: Called report_progress with analysis and plan summary in a markdown task list",
            "criteria2: The commitMessage in `report_progress` is in markdown format",
            "criteria3: never modified any files in this stage, such as call `str_replace_editor`",
            "criteria4: never run test in this stage",
            "criteria5: never fix or implemented any code in this stage",
        ],
    },
    "stage_2_environment": {
        "name": "Environment Setup and Validation",
        "max_steps": 30,
        "description": """
**Stage 2: Environment Setup and Validation**
Your task is to set up the development environment and validate it's working.

Tasks to complete:
1. Build and set up the environment for the project.
2. Install the project in development mode if applicable (e.g., `pip install -e .`)
3. Install any missing tools or packages using appropriate package managers (apt, npm, pip, etc.)
4. Nicely and successfully build, e.g., you need to run both `npm install` and `npm run build` and `npm install` before `npm run build`.

End this stage by calling `report_progress` with environment setup status in a markdown task list.
        """,
        "criteria": [
            "criteria1: Try to run set up and build the project environment to check for basic setup issues.",
            # "criteria2: Called report_progress with environment setup status in a markdown task list",
            "criteria2: For Node/JavaScript/TypeScript projects, must run `npm install` and `npm run build`; 'npm install' must be run before 'npm run build'",
            # "criteria3: Installed the project in development mode if applicable (e.g., `pip install -e .`)",
            # "criteria4: Installed missing packages using appropriate package managers",
            "criteria5: Called report_progress with environment setup status in a markdown task list",
        ],
    },
    "stage_3_reproduction": {
        "name": "Problem Reproduction",
        "max_steps": 30,
        "description": """
**Stage 3: Problem Reproduction**
Problem Statement: $PROBLEM_STATEMENT

Your task is to create or run tests that reproduce the reported problem.

Tasks to complete:
1. Look for existing test files that might demonstrate the issue
2. If test suites are available, prefer writing tests into existing files to reproduce the issue instead of creating new test files
3. If no test suites are available, created new test files to reproduce the problem
4. The test should fail initially, confirming the bug exists

End this stage by calling `report_progress` with reproduction results in a markdown task list.
        """,
        "criteria": [
            "criteria1: Looked for and examined existing test files",
            "criteria2: If test suites are available, writing tests into existing files instead of creating new test files",
            "criteria3: If no test suites are available, created new test files",
            # "criteria4: Called report_progress with reproduction status in a markdown task list",
            "criteria4: If need to add tests, model should write/modify test files and then run test files for reproduction instead of testing in command line, which is not good for debugging.",
            "criteria5: Must run tests to check for issue reproduction",
            "criteria6: Called report_progress with reproduction status in a markdown task list",
        ],
    },
    "stage_4_implementation": {
        "name": "Solution Implementation",
        "max_steps": 128,
        "description": """
**Stage 4: Solution Implementation**
Problem Statement: $PROBLEM_STATEMENT

Your task is to implement the solution to solve the problem.

Tasks to complete:
1. Implement the fix
2. Use proper coding practices and maintain code quality
3. Test your changes incrementally as you implement them
4. Handle any build errors that arise during implementation
5. Use file system tools appropriately, avoid overusing bash tool
6. Test after your fix; retry build after your fix to make sure the project is buildable after fix.
7. The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items.

Properly call `report_progress` with implementation details when you made stage changes in a markdown task list.
        """,
        "criteria": [
            "criteria1: Made appropriate changes to the correct files",
            # "criteria2: Used appropriate tools (not overusing bash)",
            # "criteria3: Called `report_progress` in a markdown task list with implementation details",
            # "criteria4: The whole solution should never change `run_tests.sh` file.",
            # "criteria5: The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items."
            "criteria1: Made appropriate changes to the correct files",
            "criteria2: Test after fix; retry build after fix to make sure the project is buildable after fix",
            "criteria3: Resolved any build errors that occurred",
            "criteria4: Used appropriate tools (not overusing bash)",
            "criteria5: Called `report_progress` in a markdown task list with implementation details",
            "criteria6: The whole solution should never change `run_tests.sh` file.",
            "criteria7: The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items.",
        ],
    },
    "stage_5_finalization": {
        "name": "Final Documentation and PR Report",
        "max_steps": 10,
        "description": """
**Stage 5: Final Documentation and PR Report**
Your task is to create comprehensive documentation of your solution.

Tasks to complete:
1. Create a detailed PR description explaining what was changed and why
2. Document the fix with clear before/after behavior
3. List all files that were modified
4. Include the issue number in the PR description in format: `Fixes #{issue_number}` if applicable
5. Include testing information and any new tests created
6. Format as: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>
        """,
        "criteria": [
            "criteria1: Created a comprehensive PR description in format: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>",
            "criteria2: PR description includes the issue number in format: `Fixes #{issue_number}` if applicable",
            "criteria3: Don't use `report_progress` in this stage",
        ],
    },
}


SBH_SWE_HARD_LANG_STAGE_DEFINITIONS_V2 = {
    "stage_1_analysis": {
        "name": "Problem Analysis and Planning",
        "max_steps": 20,
        "description": """
Problem Statement: $PROBLEM_STATEMENT

**Stage 1: Planning**

Report a general SWE plan in markdown format via `report_progress` function, explaining about how you are going to approach the given problem as few steps as possible.
Analyze the problem statement thoroughly. Fully understand the issue and comments provided
Report your plan in a markdown task list format via `report_progress` function.
Do NOT implement any solutions yet - this is purely analysis and planning.
Do NOT create or edit any files.
Do NOT run tests in this stage.

And calling `report_progress` to report your analysis and plan in a markdown task list. The commitMessage in `report_progress` is in markdown format. 
        """,
        "criteria": [
            "criteria1: Called report_progress with analysis and plan summary in a markdown task list",
            "criteria2: The commitMessage in `report_progress` is in markdown format",
            "criteria3: never modified any files in this stage, such as call `str_replace_editor`",
            "criteria4: never run test in this stage",
            "criteria5: never fix or implemented any code in this stage",
        ],
    },
    "stage_2_implementation": {
        "name": "Solution Implementation",
        "max_steps": 128,
        "description": """
**Stage 2: Solution Implementation**
Problem Statement: $PROBLEM_STATEMENT

Your task is to implement the solution to solve the problem.

Tasks to complete:
1. Implement the fix
2. Use proper coding practices and maintain code quality
3. Test your changes incrementally as you implement them
4. Handle any build errors that arise during implementation
5. Use file system tools appropriately, avoid overusing bash tool
6. Test after your fix; retry build after your fix to make sure the project is buildable after fix.
7. The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items.

Properly call `report_progress` with implementation details when you made stage changes in a markdown task list.
        """,
        "criteria": [
            "criteria1: Made appropriate changes to the correct files",
            # "criteria2: Used appropriate tools (not overusing bash)",
            # "criteria3: Called `report_progress` in a markdown task list with implementation details",
            # "criteria4: The whole solution should never change `run_tests.sh` file.",
            # "criteria5: The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items."
            "criteria1: Made appropriate changes to the correct files",
            "criteria2: Test after fix; retry build after fix to make sure the project is buildable after fix",
            "criteria3: Resolved any build errors that occurred",
            "criteria4: Used appropriate tools (not overusing bash)",
            "criteria5: Called `report_progress` in a markdown task list with implementation details",
            "criteria6: The whole solution should never change `run_tests.sh` file.",
            "criteria7: The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items.",
        ],
    },
    "stage_3_finalization": {
        "name": "Final Documentation and PR Report",
        "max_steps": 10,
        "description": """
**Stage 3: Final Documentation and PR Report**
Your task is to create comprehensive documentation of your solution.

Tasks to complete:
1. Create a detailed PR description explaining what was changed and why
2. Document the fix with clear before/after behavior
3. List all files that were modified
4. Include the issue number in the PR description in format: `Fixes #{issue_number}` if applicable
5. Include testing information and any new tests created
6. Format as: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>
        """,
        "criteria": [
            "criteria1: Created a comprehensive PR description in format: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>",
            "criteria2: PR description includes the issue number in format: `Fixes #{issue_number}` if applicable",
            "criteria3: Don't use `report_progress` in this stage",
        ],
    },
}


SBH_SWE_EASY_LANG_STAGE_DEFINITIONS = {
    "stage_1_analysis": {
        "name": "Problem Analysis and Planning",
        "max_steps": 20,
        "description": """
**Stage 1: Planning**

Create a general SWE plan in markdown format explaining about how you are going to approach a SWE problem as few steps as possible.
And calling `report_progress` to report your analysis and plan in a markdown task list.
        """,
        "criteria": [
            # "Provided a clear root cause analysis",
            "criteria1: Called report_progress with analysis and plan summary in a markdown task list",
            "criteria2: The commitMessage in `report_progress` is in markdown format",
            "criteria3: never modified any files in this stage, such as call `str_replace_editor`",
            "criteria4: never run test in this stage",
            "criteria5: never fix or implemented any code in this stage",
        ],
    },
    "stage_2_environment": {
        "name": "Environment Setup and Validation",
        "max_steps": 30,
        "description": """
**Stage 2: Environment Setup and Validation**
Your task is to set up the development environment and validate it's working.

Tasks to complete:
1. Build and set up the environment for the project.
2. Install the project in development mode if applicable (e.g., `pip install -e .`)
3. Install any missing tools or packages using appropriate package managers (apt, npm, pip, etc.)
4. Nicely and successfully build, e.g., you need to run both `npm install` and `npm run build` and `npm install` before `npm run build`.

End this stage by calling `report_progress` with environment setup status in a markdown task list.
        """,
        "criteria": [
            "criteria1: Try to run set up and build the project environment to check for basic setup issues.",
            "criteria2: Called report_progress with environment setup status in a markdown task list",
            # "criteria2: For Node/JavaScript/TypeScript projects, must run `npm install` and `npm run build`; 'npm install' must be run before 'npm run build'",
            # "criteria3: Installed the project in development mode if applicable (e.g., `pip install -e .`)",
            # "criteria4: Installed missing packages using appropriate package managers",
            # "criteria5: Called report_progress with environment setup status in a markdown task list",
        ],
    },
    "stage_3_reproduction": {
        "name": "Problem Reproduction",
        "max_steps": 30,
        "description": """
**Stage 3: Problem Reproduction**
Problem Statement: $PROBLEM_STATEMENT

Your task is to create or run tests that reproduce the reported problem.

Tasks to complete:
1. Look for existing test files that might demonstrate the issue
2. If test suites are available, prefer writing tests into existing files to reproduce the issue instead of creating new test files
3. If no test suites are available, created new test files to reproduce the problem
4. The test should fail initially, confirming the bug exists

End this stage by calling `report_progress` with reproduction results in a markdown task list.
        """,
        "criteria": [
            "criteria1: Looked for and examined existing test files",
            "criteria2: If test suites are available, writing tests into existing files instead of creating new test files",
            "criteria3: If no test suites are available, created new test files",
            "criteria4: Called report_progress with reproduction status in a markdown task list",
            # "criteria4: If need to add tests, model should write/modify test files and then run test files for reproduction instead of testing in command line, which is not good for debugging.",
            # "criteria5: Must run tests to check for issue reproduction",
            # "criteria6: Called report_progress with reproduction status in a markdown task list",
        ],
    },
    "stage_4_implementation": {
        "name": "Solution Implementation",
        "max_steps": 128,
        "description": """
**Stage 4: Solution Implementation**
Problem Statement: $PROBLEM_STATEMENT

Your task is to implement the solution to solve the problem.

Tasks to complete:
1. Implement the fix
2. Use proper coding practices and maintain code quality
3. Test your changes incrementally as you implement them
4. Handle any build errors that arise during implementation
5. Use file system tools appropriately, avoid overusing bash tool
6. Test after your fix; retry build after your fix to make sure the project is buildable after fix.

Properly call `report_progress` with implementation details when you made stage changes in a markdown task list.
        """,
        "criteria": [
            "criteria1: Made appropriate changes to the correct files",
            "criteria2: Used appropriate tools (not overusing bash)",
            "criteria3: Called `report_progress` in a markdown task list with implementation details",
            "criteria4: The whole solution should never change `run_tests.sh` file.",
            "criteria5: The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items."
            # "criteria1: Made appropriate changes to the correct files",
            # "criteria2: Test after fix; retry build after fix to make sure the project is buildable after fix",
            # "criteria3: Resolved any build errors that occurred",
            # "criteria4: Used appropriate tools (not overusing bash)",
            # "criteria5: Called `report_progress` in a markdown task list with implementation details",
            # "criteria6: The whole solution should never change `run_tests.sh` file.",
            # "criteria7: The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items."
        ],
    },
    "stage_5_finalization": {
        "name": "Final Documentation and PR Report",
        "max_steps": 10,
        "description": """
**Stage 5: Final Documentation and PR Report**
Your task is to create comprehensive documentation of your solution.

Tasks to complete:
1. Create a detailed PR description explaining what was changed and why
2. Document the fix with clear before/after behavior
3. List all files that were modified
4. Include the issue number in the PR description in format: `Fixes #{issue_number}` if applicable
5. Include testing information and any new tests created
6. Format as: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>
        """,
        "criteria": [
            "criteria1: Created a comprehensive PR description in format: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>",
            "criteria2: PR description includes the issue number in format: `Fixes #{issue_number}` if applicable",
            "criteria3: Don't use `report_progress` in this stage",
        ],
    },
}


SBH_SWE_HARD_LANG_STAGE_DEFINITIONS = {
    "stage_1_analysis": {
        "name": "Problem Analysis and Planning",
        "max_steps": 20,
        "description": """
**Stage 1: Planning**

Create a general SWE plan in markdown format explaining about how you are going to approach a SWE problem as few steps as possible.
And calling `report_progress` to report your analysis and plan in a markdown task list.
        """,
        "criteria": [
            # "Provided a clear root cause analysis",
            "criteria1: Called report_progress with analysis and plan summary in a markdown task list",
            "criteria2: The commitMessage in `report_progress` is in markdown format",
            "criteria3: never modified any files in this stage, such as call `str_replace_editor`",
            "criteria4: never run test in this stage",
            "criteria5: never fix or implemented any code in this stage",
        ],
    },
    "stage_2_implementation": {
        "name": "Solution Implementation",
        "max_steps": 196,
        "description": """
**Stage 2: Solution Implementation**
Problem Statement: $PROBLEM_STATEMENT

Your task is to implement the solution to solve the problem.

Tasks to complete:
1. Implement the fix
2. Use proper coding practices and maintain code quality
3. Test your changes incrementally as you implement them
4. Use file system tools appropriately, avoid overusing bash tool
5. Test after your fix; retry build after your fix to make sure the project is buildable after fix.

Properly call `report_progress` with implementation details when you made stage changes in a markdown task list.
        """,
        "criteria": [
            "criteria1: Made appropriate changes to the correct files",
            "criteria2: Used appropriate tools (not overusing bash)",
            "criteria3: Called `report_progress` in a markdown task list with implementation details",
            "criteria4: The whole solution should never change `run_tests.sh` file.",
            "criteria5: The last call to <functions.report_progress> should only contain completed items in the checklist, and should not contain any pending items.",
        ],
    },
    "stage_3_finalization": {
        "name": "Final Documentation and PR Report",
        "max_steps": 10,
        "description": """
**Stage 3: Final Documentation and PR Report**
Your task is to create comprehensive documentation of your solution.

Tasks to complete:
1. Create a detailed PR description explaining what was changed and why
2. Document the fix with clear before/after behavior
3. List all files that were modified
4. Include the issue number in the PR description in format: `Fixes #{issue_number}` if applicable
5. Include testing information and any new tests created
6. Format as: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>
        """,
        "criteria": [
            "criteria1: Created a comprehensive PR description in format: <pr_title>Title Here</pr_title> <pr_description>Description Here</pr_description>",
            "criteria2: PR description includes the issue number in format: `Fixes #{issue_number}` if applicable",
            "criteria3: Don't use `report_progress` in this stage",
        ],
    },
}


INSTRUCTION_PADAWAN_SBH_EASY_LANG = """
You should act like a perfect software engineer. Your goal is to solve the software engineering problem systematically. 

## Steps to Follow
0. Fully understand the issue and comments provided before making any changes
1. Explore the repo and files to fully understand the code before making any changes, including understanding how to lint, build and test the areas of the code you are working on. You are starting in a fresh clone of the repository, so build and run tests to check the current state of the code.
2. Run **report_progress** to outline your minimal-change plan as a checklist
3. Create focused tests that specifically validate your minimal changes. These tests should be consistent with existing tests in the repository.
4. Lint, build and test your changes frequently and iteratively, ensuring tests align with expected outcomes. (e.g., build after your fix to make sure the code is still buildable, run tests after your fix and verify that the solution passes the tests, etc.)
5. Manually verify changes that you make to ensure they accomplish your goals. Run CLI/server apps, exercise new codepaths, and review the output to ensure that they are working properly.
6. Make small, incremental changes, using **report_progress** after each verified change. Review files committed by **report_progress** and use \`.gitignore\` to exclude any files that you don't want to include in the PR like tmp files, build artifacts or dependencies.
7. Try nicely and successfully build, e.g., you need to run both `npm install` and `npm run build` and `npm install` must precede `npm run build`.

Ensure that you lint, build and test early and iteratively to validate each code change thoroughly.
You will be penalized if you don't write tests for your changes. If test suite exists, only modify existing tests to cover your changes. If no test suites are available, created new test files to reproduce the problem.
"""

INSTRUCTION_PADAWAN_SBH_HARD_LANG = """
You should act like a perfect software engineer. Your goal is to solve the software engineering problem systematically. 

## Steps to Follow
0. Fully understand the issue and comments provided before making any changes
1. Explore the repo and files to fully understand the code before making any changes, including understanding how to lint and test the areas of the code you are working on. 
2. Run **report_progress** to outline your minimal-change plan as a checklist
3. Make small, incremental changes, using **report_progress** after each change. Review files committed by **report_progress** and use \`.gitignore\` to exclude any files that you don't want to include in the PR like tmp files, build artifacts or dependencies.

You will be penalized if you don't write tests for your changes. If test suite exists, only modify existing tests to cover your changes. If no test suites are available, created new test files to reproduce the problem.
"""
