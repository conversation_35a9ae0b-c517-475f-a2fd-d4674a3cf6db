import asyncio
import dataclasses
import functools
import json
import logging
import traceback
from typing import Any, Protocol

import caas
import chz
import httpx
import structlog
import tenacity
from caas.commands import DownloadFileFromContainer, RawExec, UploadFile
from caas.protocol import NetworkMode, VolumeMount
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from deep_swe_msft.tools.get_coreutils import setup_coreutils
from prbot_msft.caas_handle_utils import <PERSON><PERSON>sHandle
from prbot_msft.graders.exceptions import (
    FreshCaasException,
    FreshExampleException,
    ReuseCaasException,
)
from prbot_msft.graders.swebenchhard_grader_base import (
    get_fresh_pre_untracked,
    get_instance_from_metadata,
    update_example_with_pre_untracked,
)
from prbot_msft.swebench_hard.container import RemoteError
from prbot_msft.swebench_hard.example import (
    REPO_DIRECTORY,
    Example,
    SetupError,
    initialize_resource,
)
from prbot_msft.terminal_session_handle_utils import TerminalSessionHandle

logger = structlog.get_logger(component=__name__)


def _retry_callback(retry_state):
    if retry_state.outcome:
        raise retry_state.outcome.exception()  # Raise the last exception
    raise RuntimeError("Unknown error occurred")  # Fallback


async def sbh_setup_func(
    metadata: dict[str, Any],
    terminal_session: TerminalSession,
    instance_id: str,
    datapoint: dict[str, Any] = {},
    workdir: str = REPO_DIRECTORY,
    apply_code_patch: bool = False,
    test_script_file: str = "run_tests.sh",
):
    try:
        print(f"Starting custom_setup_fn ...", instance_id)
        # determine whether to apply full setup or only clone
        version = metadata.get("version", "sbh-12878")
        # version = datapoint["metadata"].get("version", "sbh-12878")
        do_setup = version.endswith("-noexec")
        run_test_script = None if do_setup else metadata.get("test_script")
        print(
            f"Repo {metadata.get('repo')}@{metadata.get('base_commit')} (do_setup={do_setup})",
            instance_id,
        )

        # perform core setup (clone, initial diff, custom script)
        handle = TerminalSessionHandle()
        handle._ts_handle = terminal_session

        # Install prerequisite for the summarize_tests_tool
        await handle.exec(cmd=["pip", "install", "junitparser"], timeout=1_800_000, workdir="/")

        await initialize_resource(
            handle=handle,
            repo=metadata.get("repo"),
            commit=metadata.get("base_commit"),
            workdir=workdir,
            instance_id=instance_id,
            initial_gitdiff=metadata.get("initial_gitdiff", None),
            setup_script=metadata.get("setup_script", None),
            timeout=900_000,  # 15 minutes
            do_setup=do_setup,
        )
        # optional test tools only for full-exec instances
        if not do_setup:
            # upload summarize_tests tool
            with open(
                "/root/code/glass/project/prbot_msft/prbot_msft/summarize_tests_tool.py",
                "r",
            ) as f:
                await terminal_session.session.run(
                    UploadFile("/usr/local/bin/summarize_tests", f.read().encode())
                )
            await terminal_session.session.run(
                RawExec(["chmod", "+x", "/usr/local/bin/summarize_tests"])
            )
            # upload expected outcomes
            await terminal_session.session.run(
                UploadFile(
                    "/test_filter.json",
                    json.dumps(
                        {
                            "tests_results": metadata.get("tests_results"),
                            "tests_results_patched": metadata.get("tests_results_patched"),
                            "fail_to_pass": metadata.get("fail_to_pass"),
                            "pass_to_pass": metadata.get("pass_to_pass"),
                        }
                    ).encode(),
                )
            )

        # Modify the run
        if run_test_script is not None:
            run_test_script += "\nsummarize_tests"
            await terminal_session.session.run(
                UploadFile(f"/{workdir}/{test_script_file}", run_test_script.encode())
            )
            await terminal_session.session.run(
                RawExec(["chmod", "+x", f"/{workdir}/{test_script_file}"])
            )
            result = await terminal_session.session.run(
                DownloadFileFromContainer(f"/{workdir}/{test_script_file}")
            )
            print(f"{test_script_file}:\n{result.decode('utf-8')}", instance_id)

        if True:
            # if self.setup_fn is not None:
            print(f"Set up tool config.", instance_id)
            result = await setup_coreutils(
                session=terminal_session.session,
                datapoint=datapoint,
                repo_root=REPO_DIRECTORY,
                install_node=do_setup,
                add_package_json=True,
                skip_tool_packages=True,
            )
            # result = await self.setup_fn(
            #     datapoint=datapoint,
            #     session=terminal_session.session,
            #     workdir=workdir,
            # )
            print(f"setup_fn done...", instance_id)

        if apply_code_patch:
            patch = metadata.get("patch", "")
            if patch:
                print(f"Uploading code patch...", instance_id)
                # Upload patch
                await terminal_session.session.run(UploadFile("/code_patch", patch.encode()))
                # Apply patch
                print(f"Applying code patch...", instance_id)
                await terminal_session.session.run(
                    RawExec(
                        ["git", "apply", "--allow-empty", "--reject", "/code_patch"],
                    )
                )
                print(f"Code patch applied.", instance_id)
            else:
                print(f"No code patch to apply.", instance_id)
        print(f"custom_setup_fn done...", instance_id)
    except Exception as e:
        import traceback

        print(f"Exception(custom_setup_fn):\n{repr(e)}", instance_id)
        print(f"Traceback(custom_setup_fn):\n{traceback.format_exc()}", instance_id)
        raise e
    return result


@tenacity.retry(
    retry=tenacity.retry_if_exception_type(
        (
            caas.ClientError,
            caas.ServerError,
            caas.ExecError,
            caas.TransportError,
            RuntimeError,
            httpx.ReadTimeout,
        )
    ),
    wait=tenacity.wait_random_exponential(min=120, max=600, multiplier=100),
    stop=tenacity.stop_after_attempt(3),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
)
async def sbh_setup_func_with_volumn(
    metadata: dict[str, Any],
    terminal_session: TerminalSession,
    git_volume_mount_path: str,
    instance_id: str,
    datapoint: dict[str, Any] = {},
    workdir: str = REPO_DIRECTORY,
    apply_code_patch: bool = False,
    test_script_file: str = "run_tests.sh",
):
    try:
        print(f"Starting custom_setup_fn ...", instance_id)
        # determine whether to apply full setup or only clone
        version = metadata.get("version", "sbh-12878")
        # version = datapoint["metadata"].get("version", "sbh-12878")
        do_setup = version.endswith("-noexec")
        run_test_script = None if do_setup else metadata.get("test_script")
        print(
            f"Repo {metadata.get('repo')}@{metadata.get('base_commit')} (do_setup={do_setup})",
            instance_id,
        )

        # perform core setup (clone, initial diff, custom script)
        handle = TerminalSessionHandle()
        handle._ts_handle = terminal_session

        # Install prerequisite for the summarize_tests_tool
        await handle.exec(cmd=["pip", "install", "junitparser"], timeout=1_800_000, workdir="/")

        await initialize_resource(
            handle=handle,
            repo=metadata.get("repo"),
            commit=metadata.get("base_commit"),
            workdir=workdir,
            instance_id=instance_id,
            initial_gitdiff=metadata.get("initial_gitdiff", None),
            setup_script=metadata.get("setup_script", None),
            timeout=900_000,  # 15 minutes
            do_setup=do_setup,
            git_volume_mount_path=git_volume_mount_path,
        )
        # optional test tools only for full-exec instances
        if not do_setup:
            # upload summarize_tests tool
            with open(
                "/root/code/glass/project/prbot_msft/prbot_msft/summarize_tests_tool.py",
                "r",
            ) as f:
                await terminal_session.session.run(
                    UploadFile("/usr/local/bin/summarize_tests", f.read().encode())
                )
            await terminal_session.session.run(
                RawExec(["chmod", "+x", "/usr/local/bin/summarize_tests"])
            )
            # upload expected outcomes
            await terminal_session.session.run(
                UploadFile(
                    "/test_filter.json",
                    json.dumps(
                        {
                            "tests_results": metadata.get("tests_results"),
                            "tests_results_patched": metadata.get("tests_results_patched"),
                            "fail_to_pass": metadata.get("fail_to_pass"),
                            "pass_to_pass": metadata.get("pass_to_pass"),
                        }
                    ).encode(),
                )
            )

        # Modify the run
        if run_test_script is not None:
            run_test_script += "\nsummarize_tests"
            await terminal_session.session.run(
                UploadFile(f"/{workdir}/{test_script_file}", run_test_script.encode())
            )
            await terminal_session.session.run(
                RawExec(["chmod", "+x", f"/{workdir}/{test_script_file}"])
            )
            result = await terminal_session.session.run(
                DownloadFileFromContainer(f"/{workdir}/{test_script_file}")
            )
            print(f"{test_script_file}:\n{result.decode('utf-8')}", instance_id)

        if True:
            # if self.setup_fn is not None:
            print(f"Set up tool config.", instance_id)
            result = await setup_coreutils(
                session=terminal_session.session,
                datapoint=datapoint,
                repo_root=REPO_DIRECTORY,
                install_node=do_setup,
                add_package_json=True,
                skip_tool_packages=do_setup,
            )
            print(f"setup_fn done...", instance_id)

        if apply_code_patch:
            patch = metadata.get("patch", "")
            if patch:
                print(f"Uploading code patch...", instance_id)
                # Upload patch
                await terminal_session.session.run(UploadFile("/code_patch", patch.encode()))
                # Apply patch
                print(f"Applying code patch...", instance_id)
                await terminal_session.session.run(
                    RawExec(
                        ["git", "apply", "--allow-empty", "--reject", "/code_patch"],
                    )
                )
                print(f"Code patch applied.", instance_id)
            else:
                print(f"No code patch to apply.", instance_id)
        print(f"custom_setup_fn done...", instance_id)
    except Exception as e:
        import traceback

        print(f"Exception(custom_setup_fn):\n{repr(e)}", instance_id)
        print(f"Traceback(custom_setup_fn):\n{traceback.format_exc()}", instance_id)
        raise e
    return result


@tenacity.retry(
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(10),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
    retry_error_callback=_retry_callback,
)
async def get_patch(
    metadata: dict[str, Any],
    caas_container: CaasContainer,
) -> str | None:
    # TODO: this should be re-used by swebenchhard_repair_grader
    instance = get_instance_from_metadata(metadata)
    try:
        fresh_handle = await CaasHandle.create(image_name=instance.image_name)
    except Exception as e:
        raise FreshCaasException(e) from e
    try:
        pre_untracked = await get_fresh_pre_untracked(fresh_handle, instance)
    except SetupError as e:
        raise FreshExampleException(e) from e

    caas_handle = CaasHandle()
    caas_handle._caas_handle = caas_container
    async with caas_handle:
        try:
            ls_root_out = await caas_handle.exec(
                cmd=["ls", "-la", "/"],
                timeout=3600,
                workdir="/",
            )
            with open("/var/log/supervisor/presetup.log", "a+") as f:
                f.write(
                    f"get_patch {instance.instance_id} :ls /:\n{ls_root_out[1].decode('utf-8')}\n"
                )

            ls_workdir_out = await caas_handle.exec(
                cmd=["ls", "-la", "/testbed"],
                timeout=3600,
                workdir="/",
            )
            with open("/var/log/supervisor/presetup.log", "a+") as f:
                f.write(
                    f"get_patch {instance.instance_id} :ls /testbed:\n{ls_workdir_out[1].decode('utf-8')}\n"
                )

            example = await Example.create(
                swe_datum=instance,
                container_handle=caas_handle,
                timeout=3_600_000,
                run_setup=False,
                run_tests=False,
            )
        except SetupError as e:
            raise ReuseCaasException(e) from e
        await update_example_with_pre_untracked(example, pre_untracked)
        try:
            patch = await example.git_diff(include_new_untracked=True, timeout=60_000)
        except RemoteError as e:
            print(
                f"Unable to get patch for {instance.instance_id} re-using container_tool_state. Traceback:\n{traceback.format_exc()}",
                instance.instance_id,
            )
            patch = None
        print(
            f"get_patch {instance.instance_id}: Got patch:\n{patch if patch else 'None'}",
            instance.instance_id,
        )
    return patch


@tenacity.retry(
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(10),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
    retry_error_callback=_retry_callback,
)
async def get_patch_volumn(
    metadata: dict[str, Any],
    caas_container: CaasContainer,
) -> str:
    # TODO: this should be re-used by swebenchhard_repair_grader
    instance = get_instance_from_metadata(metadata)

    git_volume_mounts = None
    if instance.version.endswith("-noexec"):
        git_volume_mount_path = f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{instance.repo}/{instance.base_commit}.tar"
        git_volume_mounts = (git_volume_mounts or []) + [
            VolumeMount(
                host=git_volume_mount_path,
                container=git_volume_mount_path,
                deprecated_use_blobfuse=True,
            )
        ]

    try:
        fresh_handle = await CaasHandle.create(
            image_name=instance.image_name, git_volume_mounts=git_volume_mounts
        )
    except Exception as e:
        print(f"Unable to create fresh handle, {instance.image_name=}", instance.instance_id)
        raise FreshCaasException(e) from e
    try:
        pre_untracked = await get_fresh_pre_untracked(fresh_handle, instance)
    except SetupError as e:
        print(
            f"Unable to create fresh example to get pre_untracked, {instance.image_name=} Traceback:\n{traceback.format_exc()}",
            instance.instance_id,
        )
        raise FreshExampleException(e) from e

    caas_handle = CaasHandle(git_volume_mounts=git_volume_mounts)
    caas_handle._caas_handle = caas_container
    async with caas_handle:
        try:
            ls_root_out = await caas_handle.exec(
                cmd=["ls", "-la", "/"],
                timeout=3600,
                workdir="/",
            )
            with open("/var/log/supervisor/presetup.log", "a+") as f:
                f.write(
                    f"get_patch {instance.instance_id} :ls /:\n{ls_root_out[1].decode('utf-8')}\n"
                )

            ls_workdir_out = await caas_handle.exec(
                cmd=["ls", "-la", "/testbed"],
                timeout=3600,
                workdir="/",
            )
            with open("/var/log/supervisor/presetup.log", "a+") as f:
                f.write(
                    f"get_patch {instance.instance_id} :ls /testbed:\n{ls_workdir_out[1].decode('utf-8')}\n"
                )

            example = await Example.create(
                swe_datum=instance,
                container_handle=caas_handle,
                timeout=3_600_000,
                run_setup=False,
                run_tests=False,
            )
        except SetupError as e:
            print(
                f"Unable to create example, re-using container_tool_state. Traceback:\n{traceback.format_exc()}",
                instance.instance_id,
            )
            raise ReuseCaasException(e) from e
        await update_example_with_pre_untracked(example, pre_untracked)
        try:
            patch = await example.git_diff(include_new_untracked=True, timeout=60_000)
        except RemoteError as e:
            print(
                f"Unable to get patch for {instance.instance_id} re-using container_tool_state. Traceback:\n{traceback.format_exc()}",
                instance.instance_id,
            )
            patch = None
        print(
            f"get_patch {instance.instance_id}: Got patch:\n{patch if patch else 'None'}",
            instance.instance_id,
        )
    return patch


def is_easy_language(metadata: dict[str, Any]) -> bool:
    instance_id = metadata.get("instance_id", "unknown")
    try:
        top_languages = metadata["pr_languages"]
    except:
        try:
            top_languages = [
                sorted(
                    json.loads(metadata["language_count"]).items(),
                    key=lambda x: float(x[1]["percentage"]),
                    reverse=True,
                )[0][0]
            ]
        except:
            top_languages = ["Unknown"]
    is_easy_lang = len(
        {"Python", "TypeScript", "JavaScript", "Java", "Go"}.intersection(top_languages)
    ) == len(top_languages)
    print(f"[{instance_id}] Top languages are {top_languages}, is_easy_lang: {is_easy_lang}")
    if is_easy_lang:
        return True
    return False
