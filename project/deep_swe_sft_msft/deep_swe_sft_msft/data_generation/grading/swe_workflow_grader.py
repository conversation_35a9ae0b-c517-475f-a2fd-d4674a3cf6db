"""
Conversation Grader - Evaluates conversation quality based on rubrics.

This grader follows the StageTeacherModel pattern and can take in a rubric prompt 
and a whole conversation and output true or false to determine if it's a good conversation.
"""

import asyncio
import json
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from bus_token_completer import BusTokenCompleter
from chat import Conversation, Message, Role, chat
from chat.render.common import system_content_render
from chat.render.renderer_registry import get_renderer
from deep_swe_msft.padawan_graders.system_prompt_following_grader import (
    _extract_model_actions_with_token_limit,
)
from deep_swe_msft.padawan_graders.system_prompt_following_prompts import CRITERIA_DEFINITIONS
from deep_swe_sft_msft.data_generation.constants import BUS_TOPIC, BUS_USER, TEACHER_RENDERER
from message_completer.message_completer import MessageCompleter, ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter

RUBRIC_CONFIGS = {
    # "SP_FOLLOWING_RUBRIC_EASY": ["report_progress",],
    # "SP_FOLLOWING_RUBRIC_EASY": ["plan", "report_progress", "pr_description", "fix_issue_after_repro", "run_tests"],
    "SP_FOLLOWING_RUBRIC_EASY": [
        "plan",
        "report_progress",
        "pr_description",
        "fix_issue_after_repro",
        "setup_and_build",
        "reproduce_issue",
        "run_tests",
    ],
    # "SP_FOLLOWING_RUBRIC_HARD": ["report_progress",],
    "SP_FOLLOWING_RUBRIC_HARD": [
        "plan",
        "report_progress",
        "pr_description",
        "fix_issue_after_plan",
    ],
}


@dataclass
class SWEWorkFlowEvaluation:
    """Result of swe workflow evaluation"""

    is_passed: bool
    overall_score: float
    criterion_scores: Dict[
        str, Dict[str, Any]
    ]  # List of criteria with {criterion, met, explanation}
    overall_feedback: str
    criteria_list: list


class SWEWorkFlowGrader:
    """
    Grader that evaluates swe workflow on provided rubrics.

    Follows the StageTeacherModel pattern for consistency with existing codebase.
    """

    def __init__(
        self, renderer_name: str = TEACHER_RENDERER, retries: int = 3, reward_multiplier: int = 32
    ):
        self.renderer_name = renderer_name
        self.renderer = get_renderer(renderer_name)
        self.retries = retries
        self.reward_multiplier = reward_multiplier

        # Teacher model configuration (same as StageTeacherModel)
        bus_tc_config = BusTokenCompleter.Config(
            topic_mode_or_user=BUS_USER,
            topic_or_snapshot=BUS_TOPIC,
        )

        # Note: Extension setup may need to be added if required
        # extension = get_render_constrained_extension(renderer_name)

        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=bus_tc_config,
            completion_params={"temperature": 0.1},  # Lower temperature for more consistent grading
            renderer=self.renderer,
        )

        self.message_completer = message_completer_config.build()

    async def evaluate_conversation(
        self,
        conversation: Conversation,
        criteria_list: List[str] = RUBRIC_CONFIGS["SP_FOLLOWING_RUBRIC_EASY"],
        truncate_token_limit: int = 256,
    ) -> SWEWorkFlowEvaluation:
        """
        Evaluate a conversation based on the provided rubric.

        Args:
            conversation: The conversation to evaluate
            rubric_prompt: The rubric criteria and instructions
            rubric_name: Name of the rubric being used

        Returns:
            ConversationEvaluation with detailed results
        """

        # Format conversation for evaluation
        conversation_messages = _extract_model_actions_with_token_limit(
            conversation, self.renderer, token_limit=truncate_token_limit
        )
        sp_following_rubric = self._build_rubric(
            criteria_list=criteria_list,
        )
        evaluation_prompt = sp_following_rubric.replace(
            "{conversation_messages}", conversation_messages
        )
        print(f"Evaluating conversation with rubric: {criteria_list}")

        # conversation_text = self._format_conversation_for_evaluation(conversation)
        # # Create evaluation prompt
        # evaluation_prompt = self._create_evaluation_prompt(rubric_prompt, conversation_text)
        # print(f"Evaluating conversation with rubric: {rubric_name}")

        # Create teacher conversation for evaluation
        teacher_convo = Conversation(
            messages=[
                Message.system(
                    "You are an expert evaluator of software engineering conversations. "
                    "Evaluate conversations strictly based on the provided rubric and return precise JSON results.",
                    metadata=chat.SystemContentMetadata(reward_multiplier=self.reward_multiplier),
                ),
                Message.user(evaluation_prompt),
            ]
        )

        # Get teacher evaluation with retries
        for attempt in range(self.retries):
            try:
                completion = await self.message_completer.async_completion(
                    conversations=[teacher_convo],
                    n=1,
                    seed=0,
                    end_header=False,
                )
                choice = completion.choices[0]
                messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)

                # Parse the teacher's response
                response_text = str(messages[-1].content)
                print(f"Grader response: {response_text[:1000]}...")

                evaluation = self._parse_evaluation_response(response_text, criteria_list)
                return evaluation

            except Exception as e:
                if attempt < self.retries - 1:
                    print(f"Conversation evaluation attempt {attempt + 1} failed: {e}")
                    await asyncio.sleep(1)
                    continue
                else:
                    # Return failed evaluation if all attempts fail
                    return SWEWorkFlowEvaluation(
                        is_passed=False,
                        overall_score=0.0,
                        criterion_scores={},
                        overall_feedback=f"Evaluation failed after {self.retries} attempts: {e}",
                        criteria_list=criteria_list,
                    )

    def _parse_evaluation_response(
        self, response_text: str, criteria_list: str
    ) -> SWEWorkFlowEvaluation:
        """Parse the teacher's evaluation response"""
        # Try to extract JSON from the response
        json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
        if json_match:
            try:
                response_data = json.loads(json_match.group())

                # Extract criterion results and check if all passed
                criterion_scores = response_data.get("criterion_scores", {})
                all_criteria_passed = True
                passed_count = 0
                total_count = len(criterion_scores)
                passed_criteria = []

                # Check each criterion - all must pass for good conversation
                for criterion_name, details in criterion_scores.items():
                    if details.get("passed", False):
                        passed_count += 1
                    else:
                        all_criteria_passed = False

                # Calculate overall score as percentage of criteria passed
                overall_score = passed_count / total_count if total_count > 0 else 0.0

                # Override is_good_conversation based on all criteria passing
                is_passed = all_criteria_passed and (passed_count == total_count)

                return SWEWorkFlowEvaluation(
                    is_passed=is_passed,
                    overall_score=overall_score,
                    criterion_scores=criterion_scores,
                    overall_feedback=response_data.get("overall_feedback", ""),
                    criteria_list=criteria_list,
                )
            except json.JSONDecodeError as e:
                raise ValueError(f"Failed to parse JSON from grader response: {e}")
        else:
            raise ValueError(f"Could not find JSON in grader response: {response_text[:800]}...")

    def _build_rubric(self, criteria_list: str) -> str:
        """Build a rubric programmatically based on configuration and flags"""
        criteria_list = criteria_list.copy()

        # Build criteria section
        criteria_section = "## Criteria\n\n"
        for i, criteria_key in enumerate(criteria_list, 1):
            criteria_def = CRITERIA_DEFINITIONS[criteria_key]
            criteria_section += f"{i}. criteria{i}-{criteria_key}: **{criteria_def['title']}**{criteria_def['description']}\n\n"

        # # Build JSON example
        # json_example = "{\n"
        # example_criteria_num = 1

        # json_example += f'   "criteria{example_criteria_num}": {{\n     "satisfied": false,\n     "explanation": "The model run its own tests."\n   }}'
        # json_example += "\n   ...\n}"

        # Build full rubric
        rubric = f"""
You are given a model solution. Your task is to verify whether model completed the task as expected in the rubric. The model messages may be truncated for efficiency and focus on the action that the model took to complete the task, e.g., function calls. 
You should not consider the system prompt as a part of the model solution.

{criteria_section}§ Model Solution:
```
{{conversation_messages}}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria{len(criteria_list)}". Example is shown below. 
The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification. 
Example:
{{
  "is_passed": true/false,
  "criterion_scores": {{
    "criterion1-plan": {{"passed": true/false, "explanation": "Explanation for this criterion"}},
    "criterion2-report_progress": {{"passed": true/false, "explanation": "Explanation for this criterion"}},
    "criterion3-pr_description": {{"passed": true/false, "explanation": "Explanation for this criterion"}},
    ...
  }},
  "overall_feedback": "Brief overall assessment of the conversation quality"
}}

IMPORTANT: 
- Each criterion should be evaluated as PASS/FAIL (true/false)
- Use criterion1, criterion2, criterion3, etc. as keys (match the number of criteria in the rubric)
- The conversation is considered "good" ONLY if ALL criteria pass (all "passed": true)
- If ANY criterion fails, the conversation is automatically considered bad
- Provide clear explanations for why each criterion passed or failed
"""

        return rubric.strip()

    def _format_conversation_for_evaluation(
        self, conversation: Conversation, token_limit: int = 512, tool_truncation_rate: float = 0.3
    ) -> str:
        """Format conversation for evaluation (similar to StageTeacherModel)"""
        formatted_messages = []

        for msg in conversation.messages:
            role = msg.author.role

            if role == Role.SYSTEM:
                continue  # Skip system messages for evaluation
            else:
                content = str(msg.content)
                if role != Role.TOOL:
                    cur_token_limit = token_limit
                else:
                    cur_token_limit = int(token_limit * tool_truncation_rate)
                content = self._truncate_string(content, token_limit=cur_token_limit)

            formatted_messages.append(f"<{role.name}> {content}\n")

        return "\n".join(formatted_messages)

    def _truncate_string(
        self, string: str, token_limit: int = 512, truncate_behavior: str = "middle"
    ) -> str:
        """Truncate a string to a specified token limit"""
        toks = self.renderer.encode(string)
        if len(toks) < token_limit:
            return string

        if truncate_behavior == "middle":
            return (
                self.renderer.decode(toks[: token_limit // 2])
                + "...(truncated)..."
                + self.renderer.decode(toks[-token_limit // 2 :])
            )

        return self.renderer.decode(toks[:token_limit]) + "...(truncated)"
