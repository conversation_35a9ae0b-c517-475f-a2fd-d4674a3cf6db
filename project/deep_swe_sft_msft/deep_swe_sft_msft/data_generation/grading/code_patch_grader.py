"""
Conversation Grader - Evaluates conversation quality based on rubrics.

This grader follows the StageTeacherModel pattern and can take in a rubric prompt 
and a whole conversation and output true or false to determine if it's a good conversation.
"""

import asyncio
import json
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from bus_token_completer import BusT<PERSON><PERSON><PERSON>pleter
from chat import Conversation, Message, Role, chat
from chat.render.common import system_content_render
from chat.render.renderer_registry import get_renderer
from deep_swe_sft_msft.data_generation.constants import BUS_TOPIC, BUS_USER, TEACHER_RENDERER
from deep_swe_sft_msft.data_generation.grading.conversation_grader import ConversationGrader
from message_completer.message_completer import MessageCompleter, ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter


@dataclass
class CodePatchEvaluation:
    """Result of code patch evaluation"""

    is_correct: bool
    rating: str  # A+,A,A-,B+,B,B-,C+,C,D
    explanation: str
    criteria_met: List[Dict[str, Any]]  # List of criteria with {criterion, met, explanation}
    overall_feedback: str


def patch_prompt(problem: str, patch: str, silver_patch: str) -> str:
    """
    Prompt to compare a student's code implementation against a silver (reference) implementation.
    """
    return """
# Problem

You are given the following Task about a software repository.
It has been addressed by a Student Solution, which you must compare against a Silver Solution.
The Silver Solution is considered working but may also contain 
other files and non-essential changes.

§ Question

{problem}

§ Student Solution

{model_solution}

§ Silver Solution

{silver_patch}

§ Instructions

**Important Note:** Focus your evaluation on whether the Student Solution 
solves the task posed by the Question. To do so you should 
1. Understand how the Silver Solution addresses the problem.
2. Which parts of the Silver Solution are essential.
3. Understand how the Student Solution addresses the problem.
4. Assess whether the Student misunderstood the problem.
5. Assess whether the Student Solution missed crucial parts of the solution.
6. Assess whether the Student Solution missed some details of the solution.
7. Assess whether the Student Solution included superfluous code changes.
8. Assess whether the Student Solution includes tests that verify the solution.

Additional context may exist outside what is shown, but rely primarily on the patches here.
Note that the Silver Solution may contain additional files and changes such as
documentation changes and other non-essential changes. You should NOT
judge the Student Solution based on that. And disregard formatting differences.
The only aspect that matters here is functional correctness. 

Your task is to assign a rating to the student solution. Does it soundly and completely solve the task, similarly to the Silver Solution? 

### Examples of Outcomes

1. **Student did not understand the task**
    Modifying completely unrelated code to solve a different issue.

2. **Student solved the problem in a similar way to the Silver Solution**
    The patches do not have to fully agree but they are functionally equivalent.

3. **Student missed one or more important aspects of the task**
    The Student understood the problem, but the Student Solution missed at least one critical aspect.

4. **Student missed some details**
    The Student Solution is mostly correct apart for one or more small details.

5. **Student included superfluous code**
    The Student Solution is correct but messy and includes additional code that is not necessary.

6. **Stellar Student Solution**
    The Student Solution is equivalnt to the Silver Solution but more elegant.
    
7. **Student Solution does not include tests**
    The Student Solution does not include tests that verify the solution.

This list is not exhaustive. Remember -- we care about correctness and functionality, not complete agreement with the Silver which may contain non-essential changes

### Grading

- A+: Correct Student Solution, Higher Quality than Silver Solution, Student Solution tests are comprehensive and provide better coverage than Silver Solution
- A:  Correct Student Solution, Similar Quality to Silver Solution, Student Solution tests are comprehensive and provide similar coverage to Silver Solution
- A-: Correct Student Solution, Worse Quality than Silver Solution, Student Solution tests are comprehensive but provide worse coverage than Silver Solution
- B+: Student only missed one minor scenario, Student Solution tests are mostly comprehensive but omit coverage for one relevant case
- B:  Student only missed two or more minor scenarios, Student Solution tests cover basic behaviors but lack comprehensive coverage of several edge cases
- B-: Student missed significant scenarios, Student Solution tests are limited in scope and fail to cover important behaviors
- C+: Student missed one critical aspect, Student Solution tests verify some functionality but have limited coverage of key behaviors
- C:  Student missed multiple critical aspects, Student Solution tests have poor coverage and do not adequately verify the solution
- D:  was lost / did not understand the problem at all, Student Solution test suite is inadequate or missing, providing no meaningful coverage

Think through the Task by considering whether the Student Solution is supported by the Silver Solution.

Finally, format your answer as a JSON object with the following structure:

{{
    "explanation": "A brief summarization of your reasoning for the overall grade.",
    "rating": "A+, A , A-, B+, B, B-, C+, C, or D",
    "criteria_met": [
        {{"criterion": "criteria1", "met": true/false, "explanation": "brief explanation"}},
        ...
    ],
    "overall_feedback": "brief feedback about what's missing or what was done well",
}}
    """.format(
        problem=problem,
        model_solution=patch,
        silver_patch=silver_patch,
    ).strip()


class CodePatchGrader:
    """
    Grader that evaluates conversation quality based on provided rubrics.

    Follows the StageTeacherModel pattern for consistency with existing codebase.
    """

    def __init__(
        self, renderer_name: str = TEACHER_RENDERER, retries: int = 3, reward_multiplier: int = 32
    ):
        self.renderer_name = renderer_name
        self.renderer = get_renderer(renderer_name)
        self.retries = retries
        self.reward_multiplier = reward_multiplier

        # Teacher model configuration (same as StageTeacherModel)
        bus_tc_config = BusTokenCompleter.Config(
            topic_mode_or_user=BUS_USER,
            topic_or_snapshot=BUS_TOPIC,
        )

        # Note: Extension setup may need to be added if required
        # extension = get_render_constrained_extension(renderer_name)

        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=bus_tc_config,
            completion_params={"temperature": 0.1},  # Lower temperature for more consistent grading
            renderer=self.renderer,
        )

        self.message_completer = message_completer_config.build()

    async def evaluate_code_patch(
        self,
        # conversation: Conversation,
        # rubric_prompt: str,
        # rubric_name: str = "custom"
        prompt_statement,
        code_patch: str,
        golden_patch: str,
    ) -> CodePatchEvaluation:
        """
        Evaluate a conversation based on the provided rubric.

        Args:
            conversation: The conversation to evaluate
            rubric_prompt: The rubric criteria and instructions
            rubric_name: Name of the rubric being used

        Returns:
            ConversationEvaluation with detailed results
        """

        # # Format conversation for evaluation
        # conversation_text = self._format_conversation_for_evaluation(conversation)

        # Create evaluation prompt
        # evaluation_prompt = self._create_evaluation_prompt(rubric_prompt, conversation_text)
        evaluation_prompt = patch_prompt(
            problem=prompt_statement,
            patch=code_patch,
            silver_patch=golden_patch,
        )

        print(f"Evaluating patch with prompt:\n{evaluation_prompt[:800]}...")

        # Create teacher conversation for evaluation
        teacher_convo = Conversation(
            messages=[
                Message.system(
                    "You are an expert evaluator of software engineering conversations. "
                    "Evaluate conversations strictly based on the provided rubric and return precise JSON results.",
                    metadata=chat.SystemContentMetadata(reward_multiplier=self.reward_multiplier),
                ),
                Message.user(evaluation_prompt),
            ]
        )

        # Get teacher evaluation with retries
        for attempt in range(self.retries):
            try:
                completion = await self.message_completer.async_completion(
                    conversations=[teacher_convo],
                    n=1,
                    seed=0,
                    end_header=False,
                )
                choice = completion.choices[0]
                messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)

                # Parse the teacher's response
                response_text = str(messages[-1].content)
                print(f"Grader response: {response_text[:200]}...")

                evaluation = self._parse_code_patch_evaluation_response(response_text)
                return evaluation

            except Exception as e:
                if attempt < self.retries - 1:
                    print(f"Conversation evaluation attempt {attempt + 1} failed: {e}")
                    await asyncio.sleep(1)
                    continue
                else:
                    # Return failed evaluation if all attempts fail
                    return CodePatchEvaluation(
                        is_correct=False,
                        rating="D",
                        explanation=f"Evaluation failed after {self.retries} attempts: {e}",
                        criteria_met={},
                        overall_feedback=f"Evaluation failed after {self.retries} attempts: {e}",
                    )

    def _parse_code_patch_evaluation_response(
        self,
        response_text: str,
        passed_rating: list = ["A+", "A", "A-"],
    ) -> CodePatchEvaluation:
        """Parse the teacher's evaluation response"""
        # Try to extract JSON from the response
        json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
        if json_match:
            try:
                response_data = json.loads(json_match.group())

                # Extract criterion results and check if all passed
                rating = response_data.get("rating", {})
                explanation = response_data.get("explanation", "")
                criteria_met = response_data.get("criteria_met", [])
                overall_feedback = response_data.get("overall_feedback", "")
                is_correct = rating in passed_rating

                return CodePatchEvaluation(
                    is_correct=is_correct,
                    rating=rating,
                    explanation=explanation,
                    criteria_met=criteria_met,
                    overall_feedback=overall_feedback,
                )
            except json.JSONDecodeError as e:
                raise ValueError(f"Failed to parse JSON from grader response: {e}")
        else:
            raise ValueError(f"Could not find JSON in grader response: {response_text[:200]}...")
