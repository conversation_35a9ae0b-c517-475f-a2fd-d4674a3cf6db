"""
Grading package for evaluating conversation quality in software engineering contexts.

This package provides tools for grading conversations based on rubrics,
following the StageTeacherModel pattern for consistency with the existing codebase.
"""

from .code_patch_grader import CodePatchEvaluation, CodePatchGrader
from .conversation_grader import (
    ConversationEvaluation,
    ConversationGrader,
    evaluate_conversation_detailed,
    grade_conversation,
)
from .swe_behavior_rubric import SWE_BEHAVIOR_RUBRIC
from .swe_workflow_grader import SWEWorkFlowGrader

from .swe_behavior_rubric import (
    SWE_BEHAVIOR_RUBRIC,
)

__all__ = [
    # Main grader classes and functions
    "ConversationGrader",
    "ConversationEvaluation",
    "grade_conversation",
    "evaluate_conversation_detailed",
    # Rubric definitions
    "SWE_BEHAVIOR_RUBRIC",
]

# Version info
__version__ = "1.0.0"
__author__ = "Deep SWE SFT Team"
