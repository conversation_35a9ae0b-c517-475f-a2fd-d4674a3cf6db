import chz
import json
from mini.finetune.datasets.postprocessors import DatasetPostprocess

# I find there is 1/1525 conversation in the dataset that has recipient as an object, not sure why. 
# So this line is a hotfix to filter out those conversations.
#jq -c 'select([.conversation.messages[].recipient | type] | any(. == "object") | not)' data.jsonl > data_cleaned.jsonl

@chz.chz
class MaybeReformatDataPoint(DatasetPostprocess):
    """
    Reformats input data when it contains 'prompt' and/or 'conversation' structure.
    
    Input example:
    {
        "prompt": {<single_message_object>},
        "conversation": {"messages": [<message1> (including prompt), <message2>, ...]}
    }
    
    Output format:
    {
        "messages": [<message1>, <message2>, ...]
    }
    
    This processor:
    1. Ignores the prompt (since conversation is complete)
    2. Extracts messages from conversation.messages array (if present)
    3. Returns only the conversation messages
    """

    def postprocess_datapoint(self, fname: str, row: str | dict) -> dict:
        # Parse JSON if input is a string
        data = json.loads(row) if isinstance(row, str) else row
        
        # Check if we need to reformat
        if "conversation" in data:
            # Only extract conversation messages, ignore prompt
            conversation_data = data.get("conversation", {})
            conversation_messages = conversation_data.get("messages", [])
            if isinstance(conversation_messages, list):
                return {"messages": conversation_messages}

        return data
