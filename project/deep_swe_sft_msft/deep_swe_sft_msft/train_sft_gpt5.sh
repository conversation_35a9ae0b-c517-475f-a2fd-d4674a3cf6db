set -ex

oaipkg installoai mini
bash run_before_mini_sft.sh # hot fix for sft
beam start

# wandb login --relogin# Run this on devbox if setting these don't help.
export WANDB_BASE_URL="https://msaip.wandb.io"
export WANDB_API_KEY="local-****************************************"
# export WANDB_API_KEY="****************************************"

# Container location
export DATASET_CONTAINER="orngscuscresco"

# o4-mini based models
# CKPT="az://$DATASET_CONTAINER/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted/"
# CKPT="az://orngscuscresco/twapi/mini/e/zhendongwang-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run7/policy/step_000480/"
# CKPT="az://orngscuscresco/twapi/mini/e/zhendongwang-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run8/policy/step_000290/"
# RENDERER_NAME="harmony_v4.0.15_berry_v3_1mil_orion_lpe"

# # gpt5-mini
# CKPT="az://orngscuscresco/models/snapshots/zen-os4-nv4-cbv6-hh-rkld-4jul-orca-2-2025-07-10-00-04-decrypted"
# RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"

# gpt5 
CKPT="az://orngscuscresco/twapi/mini/e/chenliang1-merged-mix4-stage1-ev3-mixed-itc-mixed-spi-gpt5-d64-lr1e-5-run1-cnt5/policy/step_000050/"
# CKPT="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget"
# RENDERER_NAME="harmony_v4.0.15_berry_v3_1mil_orion_no_budget"
# RENDERER_NAME="harmony_v4.0.15_berry_v3_1mil_orion_lpe"
# RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"
# RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_mm_no_budget"


N_CTX=131072
lr=3.72666070e-05
bs=64

dt=`date '+%Y%m%d-%H%M%S'`
NAME="sft_wf_rrb-2k-wd-1k-swe-1k-sbh-3h_gpt5s50_bs-64_lr3e-5-$dt-run"
# NAME="swe_workflow_sft_after_mix15_run8_s290_lr1e-5-run1"


CMD=(
beam python -m mini.finetune.finetune2
name=$NAME

use_shmsnap=False
base_model.snapshot="scallion:d64"
base_model.layout=finetune-80g
# base_model.layout=finetune-80g-emb
base_model.ml_config="twberry.scallion.finetune enable_slow_replica_detection=False allow_embedding_prefix_loading=True decoupled_attention_token_mapping={(0,200166):0} enable_tensorcache_v2=True ignore_parent_dataset_state=True" \
# base_model.ml_config="twberry.scallion.finetune enable_slow_replica_detection=False ignore_parent_dataset_state=True include_audio_embeddings=False" \
# base_model.ml_config="twberry.scallion_lpe.finetune enable_slow_replica_detection=False ignore_parent_dataset_state=True enable_tensorcache_v2=False allow_embedding_prefix_loading=True image_encoder_type=vit tensorcache_v2_load_allow_missing_tensor=True"
# base_model.ml_config="twberry.scallion_lpe.finetune enable_slow_replica_detection=False ignore_parent_dataset_state=True image_encoder_type=vit enable_tensorcache_v2=False allow_embedding_prefix_loading=True"
# base_model.ml_config="twberry.scallion_lpe.finetune enable_slow_replica_detection=False enable_tensorcache_v2=True ignore_parent_dataset_state=True"
# base_model.mini_initial_load_skip_regex="block_expected|block_lg_scale.*"

base_model.local_cache_base=/tmp/tensorcache
base_model.n_gpus=auto
base_model.n_ctx=$N_CTX
base_model.initial_checkpoint=$CKPT
root_config=twberry.root
dataset=mini.finetune.datasets.glob_dataset:BerryHarmonyGlobDataset
dataset.batch_size=$bs

...is_multimodal=False
# ...encoding_name=orion_200k_mmgen
...encoding_name=orion_200k
# ...encoding_name=orion_200k
...renderer_name=$RENDERER_NAME
# this part is the data mix for SFT that needs to be changed
dataset.globs.0.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/sbhv2_wf_v6/grade_data/data_sbhv2_rating-A+AA-_20250803-0015_combined-2043-610_2653/plan-report_progress-pr_description-fix_issue_after_repro-run_tests/20250806-015308/passed_data.jsonl" #352
# dataset.globs.1.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/rl_data/mix16-arm3-run1/grade_data/swb_train_rewritten_4x/all_criteria_tool_clean_conv/20250808-095235/passed_data.jsonl" #2647
dataset.globs.1.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/rl_data/mix16-arm3-run2/grade_data/swb_train_rewritten_4x/all_criteria_tool_clean_conv/20250808-095706/passed_data.jsonl" #1105
# dataset.globs.3.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/rl_data/mix16-arm3-run2/grade_data/sbhv2_train/all_criteria_tool_clean_conv/20250808-095822/passed_data.jsonl" #3260
dataset.globs.2.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/rrb_gpt5/v1/data_mix/grade_data/rrb_py-414_js-311_ts-165_2025-0811/20250811-085655/passed_data.jsonl" #377
dataset.globs.3.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/rrb_v5/grade_data/rrb_ml_py_ts_js_prod_image_3858_with_stepintruct/all_criteria/20250806-015742/passed_data.jsonl" #2214
dataset.globs.4.path="az://orngscuscresco/data/keli/webdev-sft-data-wfgrad/singleturn_gpt5_692.jsonl"
dataset.globs.5.path="az://orngscuscresco/data/keli/webdev-sft-data-wfgrad/multiturn_gpt5_969.jsonl"
# dataset.globs.7.path="az://orngscuscresco/data/keli/webdev-sft-data-wfgrad/replycomment_gpt5_641.jsonl"


# dataset.globs.0.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/sbhv2_wf_v6/grade_data/data_sbhv2_rating-A+AA-_20250803-0015_combined-2043-610_2653/plan-report_progress-pr_description-fix_issue_after_repro-run_tests/20250806-015308/passed_data.jsonl" #352
# dataset.globs.1.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/gpt5mini_mix16-arm3_rollout/grade_data/sbhv2_train_full_20250805_v1_passed_data_refine-16-7079/all_criteria_conv/20250806-061637/passed_data_addsys.jsonl" #3652 
# dataset.globs.1.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/gpt5mini_mix16-arm3_rollout/grade_data/sbhv2_train_full_20250805_v1/all_criteria/20250806-020544/passed_data_refine-16_v2.jsonl" #7079 
# dataset.globs.2.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/rrb_v5/grade_data/rrb_ml_py_ts_js_prod_image_3858_with_stepintruct/all_criteria/20250806-015742/passed_data.jsonl" #2214
# dataset.globs.3.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/rrb_v5/grade_data/rrb_ml_py_ts_js_aio_3432_with_stepintruct/all_criteria/20250806-015829/passed_data.jsonl" #1908
# dataset.globs.4.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/webdev/grade_data/webdev-sft-data-0729/data_multiturn_all_with_plans_1185_20250730/plan-report_progress-pr_description/20250806-050922/passed_data.jsonl" #219
# dataset.globs.5.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/webdev/grade_data/webdev-sft-data-0729/data_singleturn_all_with_plans_1096_20250730/plan-report_progress-pr_description/20250806-050926/passed_data.jsonl" #126 


# dataset.globs.0.path="az://orngscuscresco/data/keli/webdev-sft-data-0729/data_multiturn_all_with_plans_1185_20250801.jsonl"
# # dataset.globs.0.path="az://orngscuscresco/data/keli/webdev-sft-data-0729/data_multiturn_all_with_plans_1185_20250730.jsonl"
# dataset.globs.1.path="az://orngscuscresco/data/keli/webdev-sft-data-0729/data_singleturn_all_with_plans_1096_20250801.jsonl"
# # dataset.globs.1.path="az://orngscuscresco/data/keli/webdev-sft-data-0729/data_singleturn_all_with_plans_1096_20250730.jsonl"
# dataset.globs.2.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/sbhv2_wf_v6/all_data/rating_3A/data_sbhv2_rating-A+AA-_20250803-0015_combined-1736-610_2346.jsonl"  # 2147
# # dataset.globs.2.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/sbhv2_wf_v6/all_data/rating_3A/data_sbhv2_rating-A+AA-_20250803-0015_combined-1537-610_2147.jsonl"  # 2147
# # dataset.globs.2.path="az://orngscuscresco/data/qingruzhang/swe/sft_data/wf_sft/sbhv2_wf_v6/all_data/rating_3A/data_sbhv2_rating-A+AA-_20250731-0555_1396.jsonl"  # 1396
# dataset.globs.3.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v5/rrb_ml_py_ts_js_prod_image_3858_with_stepintruct.jsonl"  # 3858

# dataset.globs.4.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v5/rrb_ml_py_ts_js_aio_3432_with_stepintruct.jsonl"         # 3432
# dataset.globs.0.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v1/swe_workflow_sft_py_data_1502.jsonl" # 1502
# dataset.globs.1.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v1/swe_workflow_sft_ts_data_700.jsonl"  # 700
# dataset.globs.2.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v1/swe_workflow_sft_ts_data_702.jsonl"  # 702
# dataset.globs.3.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v2/rrb_ml_py_ts_js_aio_1.jsonl"         # 592
# dataset.globs.4.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v2/rrb_ml_py_ts_js_prod_image_1.jsonl"  # 1510
# dataset.globs.5.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v2/rrb_ml_py_ts_js_prod_image_2.jsonl"  # 1641
# dataset.globs.0.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v3/rrb_ml_py_ts_js_aio_3207.jsonl"         # 3207
# dataset.globs.1.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v3/rrb_ml_py_ts_js_prod_image_3605.jsonl"  # 3605
# dataset.globs.2.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v3/rrb_ml_py_ts_js_prod_image_4035.jsonl"  # 4035
# dataset.globs.0.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v5/rrb_ml_py_ts_js_aio_3432_with_stepintruct.jsonl"         # 3432
# dataset.globs.1.path="az://orngscuscresco/data/zhendongw/swe_wf_sft_data/v5/rrb_ml_py_ts_js_prod_image_3858_with_stepintruct.jsonl"  # 3858
# dataset.globs.2.path="az://orngscuscresco/data/keli/webdev-sft-data-0729/data_multiturn_all_with_plans_v2.jsonl"                     # 198
# dataset.globs.3.path="az://orngscuscresco/data/keli/webdev-sft-data-0729/data_singleturn_all_with_plans_v2.jsonl"   

...postprocessors.0=deep_swe_sft_msft.postprocessors:MaybeReformatDataPoint
...postprocessors.1=mini.finetune.datasets.postprocessors.postprocess:LoadConvoPostprocess
...postprocessors.2=mini.finetune.datasets.postprocessors.postprocess:WeightNonAssistantMessagesPostprocess
...postprocessors.3=mini.finetune.datasets.postprocessors.postprocess:RemoveConfidencePostprocess
...postprocessors.4=mini.finetune.datasets.postprocessors.postprocess:EnsureEndTurn
...postprocessors.5=mini.finetune.datasets.postprocessors.postprocess:AdjustBudgetPostprocess
...postprocessors.5.max_num_yields=196
...postprocessors.5.max_token_budget=131072
# ...postprocessors.5.max_token_budget=131072

dataset.bypass_exceptions=True
dataset.shuffle=True
duration="50 steps"
between_saves="25 steps"
timeout.default=86400
hyperparam_manager=mini.finetune.hyperparam_managers.const:ConstLRHyperparamManager
hyperparam_manager.opt_config.lr_per_sample_d16=$lr
# IMPORTANT: this should be 10% of the total number of rows in the dataset!!
hyperparam_manager.opt_config.ema_horizon=1000
hyperparam_manager.warmup_samples=512
# ...add_missing_juice=True

# Logging/misc
security_profile=msft-orng
github_upload=False
git_guard=False
wandb_enable=True
wandb.wandb_project=swe-wf-sft
kafka_enable=False
use_shmsnap=False
load.restore_from_all_clusters=False
)

"${CMD[@]}" 2>&1 | tee "$NAME".log