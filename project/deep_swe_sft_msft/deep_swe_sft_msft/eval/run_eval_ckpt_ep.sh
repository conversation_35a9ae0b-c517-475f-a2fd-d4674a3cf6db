dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="pdw-sft-gpt5-mini-$dt-peaval"

INIT_CKPT="az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted/"
CKPT="az://orngscuscresco/twapi/mini/e/qingruzhang-sft_workflow_swe-3k-sbh-3k-web-2k-rrb-2k_gpt5miniv2_lr8e-5-20250809-013442-run1/51c519e2-4dbf-485c-b1f3-40ed3bdab1eb/checkpoint/model1/"
RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"

CMD=(
oaipkg run qstar.run_eval nostrict
name=${EXPERIMENT_NAME}
# :berry_models.scallion_lpe:d36_80g_mbg16_bf16
:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
root_config="mini.root.dev init_actors_rpc_timeout=600"

auto_inherit_training_args=False
policy.initial_checkpoint="$INIT_CKPT"
eval_settings.checkpoint_dir="$CKPT"

eval_settings.eval_initial_policy=True

# policy.model_config_name=falcon.multimodal.runs.scallion-d36-s64-lpe
# policy.sampling_ml_config='raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=2 pipe_depth=4 n_replicas=1'
policy.is_multimodal=True
...encoding_name=orion_200k
...harmony_renderer_name=${RENDERER_NAME}

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=1               # swang concurrent thread in one process
peashooter.num_sampling_processes=100            # swang concurrent process in one worker
# peashooter.tool_pool_config.num_tool_workers=64 # swang concurrent processes in tool pool
peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
peashooter.sampling_use_ev3=True

# swang, not support
#timeout.evaluate=80000
#timeout.rollout=3600

# Set this if you want to eval all checkpoints at once in parallel (Not needed if your training run just started.)
max_workers_per_step=3 # swang nodes per step, cannot exceed total sample nodes
# max_active_steps=2    # swang active steps

eval_settings.eval_every=1
eval_settings.min_step=50
eval_settings.max_step=500
eval_settings.exit_on_no_new_checkpoint=True

metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

# data
# # sbv data new
':deep_swe_eval_msft.swe_bench.peaval.padawan.presets:ev_sm_bench_juice_padawan(juice=768 override_target_samples_per_instance=1 max_num_yields=256)'

# # rrb wf py ts js
# ':deep_swe_sft_msft.eval.presets:eval_wf_py_ts_js'

# # # rrb py ts js
# ':deep_swe_sft_msft.eval.presets:eval_rrb_py_ts_js'


#policy.n_ctx=524288
#defaults.n_ctx=524288
policy.n_ctx=262144
defaults.n_ctx=262144
defaults.sampler.max_num_yields=256
# defaults.sampler.max_num_yields=100
defaults.sampler.harmony_constrained_sampling=True

# msft special
load.restore_from_all_clusters=False

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=swe-pevals
kafka_enable=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log
