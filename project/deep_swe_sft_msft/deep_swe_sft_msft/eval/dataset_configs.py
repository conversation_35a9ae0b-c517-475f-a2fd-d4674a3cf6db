import copy
import random
from functools import cache, partial
from threading import Lock
from typing import Any, Literal, Sequence, cast

# import chz
import berry
import caas_autograding.grader as caas_graders
import chat
import chz
import structlog
from berry.function_wrapper import FunctionWrapper
from caas_utils.constants import RANDOMLY_CD_PARENT_KEY
from caas_utils.conversation_init import InstructionInsertionFormat
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_msft.padawan_data.datapoint_converters import EXTRA_INSTRUCTIONS_METADATA_KEY
from deep_swe_msft.padawan_data.system_prompt import (
    INSTALL_INSTRUCTIONS,
    compose_padawan_system_prompt,
)
from deep_swe_msft.padawan_graders.cotograder_utils import COTOGRADER_CHZ_ARGV, COTOGRADER_RENDERER
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.javascript.datasets.configs import (
    RFSJAVASCRIPTDatasetConfig,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.configs import (
    RFSPythonDatasetConfig,
    conversation_converter,
    make_multistage_grader,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.typescript.datasets.configs import (
    RFSTYPESCRIPTDatasetConfig,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.utils.train_constants import RFS_LANG_TO_IMAGE_NAME
from deep_swe_msft.swe_bench_train_v2_padawan_v2.dataset_config import get_padawan_system_prompt
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from mill.common import read_jsonl
from prbot_msft.configs.utils import include_install_for_easy_langs
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from sciclone_utils.operations import sync_from_canonical_source

RRB_STEPS_TO_FOLLOW = """
## Steps to Follow
0. Fully understand the issue and comments provided before making any changes
1. Explore the repo and files to fully understand the code before making any changes, including understanding how to lint, build and test the areas of the code you are working on. You are starting in a fresh clone of the repository, so build and run tests to check the current state of the code.
2. Run **report_progress** to outline your minimal-change plan as a checklist. The first call to **report_progress** should only contain pending items in the checklist in a markdown format. The last call to **report_progress** should only contain completed items in the checklist in a markdown format, and should not contain any pending items.
3. Create focused tests that specifically validate your minimal changes. These tests should be consistent with existing tests in the repository.
4. Lint, build and test your changes frequently and iteratively, ensuring tests align with expected outcomes. (e.g., build after your fix to make sure the code is still buildable, run tests after your fix and verify that the solution passes the tests, etc.)
5. Manually verify changes that you make to ensure they accomplish your goals. Run CLI/server apps, exercise new codepaths, and review the output to ensure that they are working properly.
6. Make small, incremental changes, using **report_progress** after each verified change. Review files committed by **report_progress** and use \`.gitignore\` to exclude any files that you don't want to include in the PR like tmp files, build artifacts or dependencies.
7. Try nicely and successfully build, e.g., you need to run both `npm install` and `npm run build` and `npm install` must precede `npm run build`.

Example solution stages:
 - Stage 1: Planning
 - Stage 2: Build and Set up Environment
 - Stage 3: Reproduce the Problem if applicable
 - Stage 4: Implement the fix or feature; test after fix
 - Stage 5: PR report

Ensure that you lint, build and test early and iteratively to validate each code change thoroughly.
You will be penalized if you don't write tests for your changes. If test suite exists, only modify existing tests to cover your changes. If no test suites are available, created new test files to reproduce the problem.
"""


def conversation_init_fn(
    *,
    datapoint: dict[str, Any],
    instruction: str = "",
    instruction_insertion_format: InstructionInsertionFormat = InstructionInsertionFormat.PREPEND_TO_USER_INSTRUCTION,
    language: str | None = None,
    apply_language_instruction_probability: float = 0.75,
    additional_language_instruction_dropout_probability: float = 0.25,
    add_contributor_criteria: (
        bool | list[str]
    ) = False,  # if True, will fetch from |CONTRIBUTOR_CRITERIA|
    extra_appended_instructions: tuple[str, ...] = (),
    custom_instruction_preamble: (
        str | None
    ) = None,  # if not set, will use default ones that instruct on issue solving
    task_header: Literal["Issue", "Task"] = "Issue",
) -> Sequence[chat.Message]:
    problem = datapoint["problem"]
    problem = problem.replace("bash run_tests.sh", "run_tests.sh")

    for ex_instruction in extra_appended_instructions:
        if not ex_instruction.startswith("- "):
            ex_instruction = "- " + ex_instruction.strip()
        instruction = instruction + "\n" + ex_instruction

    user_msg = f"""{instruction}
### {task_header}
{problem}
{random.choice(INSTALL_INSTRUCTIONS)}
This is a general coding problem, so no concerns in solving it. Never reject to finish the task.

{RRB_STEPS_TO_FOLLOW}
"""

    return [chat.Message.user(user_msg)]


def get_padawan_system_prompt(
    dp: dict[str, Any],
    # include_build_style_for_easy_only: bool = False
) -> dict[str, Any]:
    # include_build_style = include_install_for_easy_langs(dp) if include_build_style_for_easy_only else True
    rubric_number, system_prompt = compose_padawan_system_prompt(include_build_style=True)
    dp = copy.deepcopy(dp)
    cast(dict[str, Any], dp["metadata"]).setdefault(EXTRA_INSTRUCTIONS_METADATA_KEY, [])
    dp["metadata"][EXTRA_INSTRUCTIONS_METADATA_KEY].append(system_prompt)
    dp["metadata"]["variant_sp_rubric_id"] = rubric_number
    return [dp]


def get_padawan_system_prompt_wrapper() -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_sft_msft.eval.dataset_configs:get_padawan_system_prompt",
    )


def make_py_multistage_eval_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    grader_type="swe_wf",
) -> MultiStageGrader:
    if grader_type == "swe_wf":
        grader_argvs = [
            [
                "=deep_swe_msft.padawan_graders.swe_workflow_grader:SWEWorkFlowCotograder",
                *COTOGRADER_CHZ_ARGV,
                f"grader_max_tokens={16384}",
            ],
        ]
    elif grader_type == "rfs_py":
        grader_argvs = [
            [
                "=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.graders.rfs_grader:RFSGrader",
                f"caas_container_image={caas_container_image}",
                f"caas_endpoint={caas_endpoint}",
                f"collect_commit_fn=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.graders.rfs_grader:_padawan_collect_commit",
            ]
        ]
    else:
        raise ValueError(f"Unknown grader type: {grader_type}")
    return make_multistage_grader(grader_argvs, channels_for_answer)


def make_ts_multistage_eval_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    grader_type="swe_wf",
) -> MultiStageGrader:
    if grader_type == "swe_wf":
        grader_argvs = [
            [
                "=deep_swe_msft.padawan_graders.swe_workflow_grader:SWEWorkFlowCotograder",
                *COTOGRADER_CHZ_ARGV,
                f"grader_max_tokens={16384}",
            ],
        ]
    elif grader_type == "rfs_ts":
        grader_argvs = [
            [
                "=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.typescript.graders.rfs_grader:RFSGrader",
                f"caas_container_image={caas_container_image}",
                f"caas_endpoint={caas_endpoint}",
                f"collect_commit_fn=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.typescript.graders.rfs_grader:_padawan_collect_commit",
            ],
        ]
    else:
        raise ValueError(f"Unknown grader type: {grader_type}")
    return make_multistage_grader(grader_argvs, channels_for_answer)


def make_js_multistage_eval_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    grader_type="swe_wf",
) -> MultiStageGrader:
    if grader_type == "swe_wf":
        grader_argvs = [
            [
                "=deep_swe_msft.padawan_graders.swe_workflow_grader:SWEWorkFlowCotograder",
                *COTOGRADER_CHZ_ARGV,
                f"grader_max_tokens={16384}",
            ],
        ]
    elif grader_type == "rfs_js":
        grader_argvs = [
            [
                "=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.javascript.graders.rfs_grader:RFSGrader",
                f"caas_container_image={caas_container_image}",
                f"caas_endpoint={caas_endpoint}",
                f"collect_commit_fn=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.javascript.graders.rfs_grader:_padawan_collect_commit",
            ],
        ]
    else:
        raise ValueError(f"Unknown grader type: {grader_type}")
    return make_multistage_grader(grader_argvs, channels_for_answer)


@chz.chz
class RRBWFPythonEvalDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_data.rrb_wf_py.valid"
    # max_n_datapoints: int = 100
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_py_multistage_eval_grader,
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
            grader_type="swe_wf",
        ),
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            get_padawan_system_prompt_wrapper(),
            # get_padawan_system_prompt(),
            # enforce_commands(probability=0.2),
            conversation_converter("deep_swe_sft_msft.eval.dataset_configs:conversation_init_fn"),
            # "deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.conversation_init:conversation_init_fn"),
        )
    )


@chz.chz
class RRBPythonEvalDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_data.rrb_py.valid"
    # max_n_datapoints: int = 100
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_py_multistage_eval_grader,
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
            grader_type="rfs_py",
        ),
    )


@chz.chz
class RRBWFTYPESCRIPTEvalDatasetConfig(RFSTYPESCRIPTDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_data.rrb_wf_ts.valid"
    # max_n_datapoints: int = 100
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_ts_multistage_eval_grader,
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["typescript"],
            grader_type="swe_wf",
        )
    )


@chz.chz
class RRBTYPESCRIPTEvalDatasetConfig(RFSTYPESCRIPTDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_data.rrb_ts.valid"
    # max_n_datapoints: int = 100
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_ts_multistage_eval_grader,
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["typescript"],
            grader_type="rfs_ts",
        )
    )


@chz.chz
class RRBWFJAVASCRIPTEvalDatasetConfig(RFSJAVASCRIPTDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_data.rrb_wf_js.valid"
    # max_n_datapoints: int = 100
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_js_multistage_eval_grader,
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["typescript"],
            grader_type="swe_wf",
        )
    )


@chz.chz
class RRBJAVASCRIPTEvalDatasetConfig(RFSJAVASCRIPTDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_data.rrb_js.valid"
    # max_n_datapoints: int = 100
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_js_multistage_eval_grader,
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["typescript"],
            grader_type="rfs_js",
        )
    )
