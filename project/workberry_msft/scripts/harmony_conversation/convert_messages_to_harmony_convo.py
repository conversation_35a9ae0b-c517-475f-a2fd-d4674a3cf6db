import json
import sys
import os
import uuid
import pdb
# Add the chat module to the Python path
sys.path.insert(0, '/home/<USER>/code/openai')

from chat.chat_completions_conversion import chat_completions_inputs_to_harmony_conversation
from chat.citation_format import _filter_weird_chars

def convert_sample_to_harmony(input_file, output_file):
    
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    api_messages = data.get("messages", [])
    
    filtered_messages = []
    for msg in api_messages:
        if "role" in msg and "content" in msg:
            clean_msg = {
                "role": msg["role"],
                "content": _filter_weird_chars(msg["content"])
            }
            if "name" in msg:
                clean_msg["name"] = msg["name"]
            filtered_messages.append(clean_msg)
    
    harmony_conversation = chat_completions_inputs_to_harmony_conversation(filtered_messages)
    
    conversation_dict = harmony_conversation.model_dump()
    
    output_dict = {
        "problem": conversation_dict,
        "metadata": {
            "context": {
                "ENTITIES": []
            }
        }
        "answer":""
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_dict, f, ensure_ascii=False, indent=2)
    
    return harmony_conversation

def convert_multiple_samples_to_jsonl():
    
    input_files = [
        "/mnt/c/Users/<USER>/source/strawberry/data/workberry/data.1/sample1.json",
        "/mnt/c/Users/<USER>/source/strawberry/data/workberry/data.1/sample2.json",
        "/mnt/c/Users/<USER>/source/strawberry/data/workberry/data.1/sample3.json"
    ]
    
    output_jsonl = "/mnt/c/Users/<USER>/source/strawberry/data/workberry/data.1/samples_harmony.jsonl"
    
    with open(output_jsonl, "w", encoding="utf-8") as fout:
        for input_file in input_files:
            if not os.path.exists(input_file):
                print(f"{input_file} not found")
                continue
                
            try:
                with open(input_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                api_messages = data.get("messages", [])
                # skip first message - system prompt
                api_messages = api_messages[1::]
                filtered_messages = []
                
                for msg in api_messages:
                    if "role" in msg and "content" in msg and isinstance(msg["content"], str):
                        clean_msg = {
                            "role": msg["role"],
                            "content": msg["content"]
                        }
                        if "name" in msg:
                            clean_msg["name"] = msg["name"]
                        filtered_messages.append(clean_msg)
                
                if not filtered_messages:
                    print(f"No valid messages found in {input_file}")
                    continue
                
                # Convert using official function
                harmony_conversation = chat_completions_inputs_to_harmony_conversation(filtered_messages)
                conversation_dict = harmony_conversation.model_dump(mode="json")
                pdb.set_trace()
                # Create output structure
                output_dict = {
                    "problem": conversation_dict,
                    "metadata": {
                        "context": {
                            "ENTITIES": []
                        }
                    }
                }
                
                fout.write(json.dumps(output_dict, ensure_ascii=False) + "\n")
                
                print(f"Processed: {input_file}")
                
            except Exception as e:
                print(f"Error processing {input_file}: {e}")
                continue
    
if __name__ == "__main__":
    # convert_sample_to_harmony(
    #     "/mnt/c/Users/<USER>/Downloads/sample1.json",
    #     "/mnt/c/Users/<USER>/Downloads/sample1_harmony.json"
    # )
    
    convert_multiple_samples_to_jsonl()