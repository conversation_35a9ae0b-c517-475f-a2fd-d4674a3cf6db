import json
import copy

def process_messages(messages):
    new_messages = []
    for msg in messages:
        if msg.get("role") == "assistant":
            continue
        if msg.get("role") == "tool":
            new_msg = copy.deepcopy(msg)
            new_msg["role"] = "assistant"
            new_msg.pop("name", None)
            new_msg.pop("tool_call_id", None)
            new_messages.append(new_msg)
        else:
            new_messages.append(msg)
    return new_messages

if __name__ == "__main__":
    file_path = "sample3.json" # change file path here
    with open(file_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    processed_messages = process_messages(data["messages"])

    output_path = "sample3_out.json"
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(processed_messages, f, ensure_ascii=False)