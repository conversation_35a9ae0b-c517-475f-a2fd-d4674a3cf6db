# `workberry_msft`

This guide outlines how to schedule clusters, run training, evaluation and common tips to post-train models for Workberry. This document was largely adapted from the [Researcher](https://dev.azure.com/project-argos/Mimco/_git/glass?path=/project/researcher_msft/README.md&version=GBmain&_a=preview).

# Setup

Follow the [Orange onboarding](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9022/Orange-Getting-Started) steps to set up your machine, configure your evnironment, and verify your setup by running a [sample run](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9031/3.3-Setup-peashooter-environment).

# Running manually

## Start peashooter

- Select one of the clusters available for M365:  `prod-westus2-19`, `prod-southcentralus-hpe-4`, `prod-uksouth-7`
- 7 nodes (56 gpus) should be sufficient for training `o4-mini` and `sonicberry` models. Adjust this if you are using larger models. 
  
```bash
python -m qstar.peashooter.run_peashooter \
...rapid_id=workberry-0904 \
rapid.cluster.name=<CLUSTER REGION> \
rapid.cluster.priority=team-high \
rapid.pull_git_on_restart=False \
num_controller_nodes=1 \
n_gpus_per_sampler=8 \
sampling_jobs=1 \
n_train_gpus=56 \
cpu_controller=False \
security_profile=msft-orng
```

## Start training

Check the status of the active devboxes: `b ls`.
You should see an output similar to the following when your devbox setup is ready:

```bash
NAME                       CLUSTER          SIZE     WORKERS   AGE   STATUS      PRIORITY   TEAM                 RESTARTS
devbox-controller-w0       prod-westus2-19   1        1/1/1     1d    Scheduled   high       team-moonfire-m365
devbox-rollout-worker-w0   prod-westus2-19   1        1/1/1     1d    Scheduled   high       team-moonfire-m365   0
devbox-train-w0            prod-westus2-19   1 (+6)   7/7/7     1d    Scheduled   high       team-moonfire-m365   0
```

### Troubleshooting

Sometimes your devbox won't be scheduled immediately, or has errors. Use the following commands to troubleshoot and make sure kubectl has the right context set to your cluster (`kubectl config use-context prod-westus2-19`)

- Checking quota: `orangectl quota -c prod-westus2-19`
- Check the status of your devbox in the queue: `orangectl pendingjob`  
- Check the logs of your devbox: `b tail devbox-train-w0`

## (optional) Sync your local code with devbox

To sync committed local changes to the devbox, use the following command:

```bash
b sync devbox-train-w0
```

## SSH to training job

To start training you need to ssh into your training devbox:

```bash
b ssh devbox-train-w0
```

## Run training

Once you've connected to your training devbox via SSH, it is **highly recommended** that you create a TMUX session (i.e., `tmux`). 

In your TMUX session, you can then initiate training. Below are commands using **dummy tools** and a **tiny corpus** to quickly validate your setup.

```bash
CMD=( oaipkg run --beam qstar.run_experiment
nostrict
name=workberry-o4mini-check-setup
seed=42
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:workberry_msft.presets.datasets:tr_wus2_19_ProjectsTiny_DummyTools_RougeGrader
:workberry_msft.presets.models:wus2_19_o4_mini
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
github_upload=False
wandb_enable=True
wandb.wandb_project="WorkBerry FT"
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
```

### Parameters

You can also experiment with different `workberry_msft.presets.datasets` preset values from those listed in `presets/datasets.py` or different `workberry_msft.presets.models` preset values from those listed in `presets/models.py`. For your experiments, you will likely add your own datasets and graders.

For instance, this command trains a sonic-berry model with the VertexEdgeLabsV1 dataset, sonic-style mock enteprise tools, and RougeGrader.

```bash
dt=`date '+%m%d-%H%M'`
CMD=( oaipkg run --beam qstar.run_experiment
nostrict
name=workberry-sb-vel_v1-prgrader-$dt
seed=42
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:workberry_msft.presets.datasets:tr_wus2_19_VertexEdgeLabsV1_SonicStyleEnterpriseBrowserTools_RougeGrader
:workberry_msft.presets.models:wus2_19_sonicberry
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
defaults.channel_config.channels=analysis,final,confidence
github_upload=False
wandb_enable=True
wandb.wandb_project="WorkBerry FT"
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
```

## Cleaning up

When you are done with your experiment, make sure to release the resources:
`b delete <name-of-your-devbox>`

Make sure you free resources for all nodes created with peashooter (i.e., controller, rollout workers, and trainer).

You can delete multiple nodes at once, e.g., `b delete "devbox-*"`

## Monitor Training

You can monitor your job's progress using the unified `Lemon` portal: **[Lemon Portal](https://aka.ms/lemon/)**

### Monitor Logs

**[Debugging advice, relevant logs](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/8615/Debugging-advice-relevant-logs)**

### Babysit Training Steps

If you've just started a training run, you can monitor the progress via the command below, which largely follows the logs in [steps.py](https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=%2Fproject%2Fpeashooter%2Fpeashooter%2Ftrain_worker%2Fstep.py&_a=contents&version=GBorange%2Fmain):

```bash
b ssh devbox-train-w0
supervisorctl tail -f train_worker_policy:0
```

### Navigate the log files

During training or post-training, the most useful logs could mainly be found in `training` and the `rollout-worker` devbox, since they both are involved in the model training ([overview of components](https://microsoft.sharepoint.com/teams/argos_mimco/Shared%20Documents/Forms/AllItems.aspx?viewid=7c58075d%2Dc8f0%2D4332%2D912b%2D402132552369&csf=1&web=1&e=8G2Nv7&CID=6b030a26%2D2054%2D4951%2Db74d%2Ddeaad74e4b9e&FolderCTID=0x0120009A95E4BA13E8E9458507E79AC7613BC5&id=%2Fteams%2Fargos%5Fmimco%2FShared%20Documents%2FTransferred%20Docs%2Fberry%2Fqstar%5Fintro%2FStrawberry%20components%20overview%2Epdf&parent=%2Fteams%2Fargos%5Fmimco%2FShared%20Documents%2FTransferred%20Docs%2Fberry%2Fqstar%5Fintro)).

Use the following commands to see logs around GPU usage and training progress:

```bash
b ssh devbox-train-w0 # (or your devbox-rollout-worker-w0)
vim /var/log/torchflow.log
```

### Stream and Search Logs

While connected via SSH, use the following command to stream logs in real-time:

```bash
b ssh devbox-rollout-worker-w0 # (or your devbox-train-w0)
kubectl logs devbox-rollout-worker-w0-0 
```

You can also search for specific error via:

```bash
kubectl logs devbox-rollout-worker-w0-0 --all-containers=true | grep your_beautiful_error_or_any_text
```

### Detailed Logs

Alternatively, use

```bash
b tail --n 10000 devbox-rollout-worker-w0
```

for detailed logs, but you might have to skip a lot of repetitive GPU shard download messages.

## Wandb

Wandb is useful for tracking key metrics during training and evaluation, such as mean reward, pass rate, tool usage, solution length, and inference time.

**[Setup Wandb for logging training metrics](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9033/4.1-Setup-Wandb-for-logging-training-metrics)**

Make sure to add `wandb_enable=True` and `wandb.wandb_project="WorkBerry FT"` arguments to your training commands for metrics to propagate to wandb version of our project.

## Snowflake

Snowflake is useful for everything else that Wandb does not have, since it enables us to download and analyze rollouts ourselves.

**[Setup Snowflake for Analyzing Training or Evaluation Logs](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9034/4.2-Setup-Snowflake-for-Analyzing-Training-Logs)**

See guidance (from Researcher) [Snowflake Download Samples](https://microsoft.sharepoint-df.com/:fl:/r/contentstorage/CSP_3c0b5b21-10e3-4ea9-b75c-0a29bedb9068/Document%20Library/LoopAppData/Snowflake%20download%20samples.loop?d=wa36cd27879cc4c369ee70f58dd38df07&csf=1&web=1&e=Iwqsjz&nav=cz0lMkZjb250ZW50c3RvcmFnZSUyRkNTUF8zYzBiNWIyMS0xMGUzLTRlYTktYjc1Yy0wYTI5YmVkYjkwNjgmZD1iJTIxSVZzTFBPTVFxVTYzWEFvcHZ0dVFhQ3gtRGE5b3RsZE5zT2ZpeDRVYUdqcXhrTDhCSHlEZVJvb2RmczBUbzZqWCZmPTAxQVRVR0M0M1kySldLSFREWkdaR0o1WllQTERPVFJYWUgmYz0lMkYmYT1Mb29wQXBwJnA9JTQwZmx1aWR4JTJGbG9vcC1wYWdlLWNvbnRhaW5lciZ4PSU3QiUyMnclMjIlM0ElMjJUMFJUVUh4dGFXTnliM052Wm5RdWMyaGhjbVZ3YjJsdWRDMWtaaTVqYjIxOFlpRkpWbk5NVUU5TlVYRlZOak5ZUVc5d2RuUjFVV0ZEZUMxRVlUbHZkR3hrVG5OUFptbDRORlZoUjJweGVHdE1PRUpJZVVSbFVtOXZaR1p6TUZSdk5tcFlmREF4UVZSVlIwTTBORTFJV2tNeU5ETXlWRk5XUVRNMlR6VkhOMU5STkRkQ1JUTSUzRCUyMiUyQyUyMmklMjIlM0ElMjI5MzgxYTU3MC0yZjBjLTQ3ZGQtYWE0OS00MWMzM2E0Mjg2NmUlMjIlN0Q%3D) to download and analyze samples from your evaluation or training run. This can be run from a CPU devbox.

In this project, note the `workberry_msft/scripts/snowflake/download_samples.py` script, which you can modify to scrape samples according to your own filters or to pull additional fields.

## Start BUS Nodes

[**Note**]: Guidance on BUS node creation needs to be updated. Ideally, the Workberry project should have critical nodes dedicated solely for running buses, which are then shared across the team (Researcher buses operate this way).

BUS nodes are needed for the setup where your tools or grader need to make an external LLM call.

Note that bus currently does not support auto-scale, and it suggest to run with only 1 replica, so we have to manually scale it up by creating new nodes and run script of the engine.

### Step 1: Create nodes

```bash
OAIPKG_OVERRIDE_WHEEL=unsafe_skip \
twdev create-ray-devbox \
cluster=prod-westus2-19 \
num_pods=1 \
setup_twapi=True \
num_gpu=8 \
job_name=bus-node-n \
priority_class=low-priority
```

### Step 2: Kick off bus engine

1. ssh to the node created in above
2. create a new tmux session
3. kick off bus engine by below command line. It uses o3-mini checkpoint, but it can be replaced by any other supported checkpoints.

```bash
export OPENAI_API_KEY="dummy_key"
RUST_BACKTRACE=1 python -m harmony_scripts.engines.start_engine \
--name o3-mini-engine \
--mode=optimal \
--snapshot_path="az://orngwus2cresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted" \
--renderer_name=harmony_v4.0.15_berry_v3_16k_orion_text \
--is_multimodal=False \
--gpu_kind=A100_80G \
--extra_config_string="raise_on_load_for_missing_tensor=False tensorcache_v2_load_allow_missing_tensor=True enable_tensorcache_v2=False" \
--cluster=local \
--bus_enable_qos=True \
--bus_rate_limiter=KV_UTIL \
--bus_topic_mode_or_user="workberry" \
--n_replicas=1
```

## Scale up

Repeat step 1~2 to scale up bus capacity. Note that node name need to be changed and unique.

## Monitor BUS nodes

[Bus v2 Dashboard - Singularity Orange - Dashboards - Grafana](https://ai-infra-ephgecb0cucpbkg8.wus2.grafana.azure.com/d/e972695d-bb0e-4049-8bb0-a07397f14dc4/bus-v2-dashboard?orgId=1&refresh=5m&var-Datasource=aeijxz20txtdsa&var-region=All&var-cluster=All&var-pod_user=All&var-topic=workberry&var-brix_pool=All&var-from_user=All&var-bus_line=bus&var-qos_type=All&var-brix_pool_regex=.%2A&var-topic_filter=.%2A&var-group_by=topic&var-unique_pod=All&var-join_alive_right=group%20by%20%28pool,%20user,%20cluster,%20topic%29%20%28default_oai_bus_alive%7Brole%3D&var-common_filter=topic%3D~%22workberry%22,%20pool%3D~%22.%2A%22,%20pool%3D~%22.%2A%22,%20topic%3D~%22%5Ebus:.%2A%22,%20user%3D~%22.%2A%22&var-_alive_signal_grouped_query_string=%28group%20by%20%28pool)


# Deployment 
Content adapted from [Penny Wiki](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9133/Deployment) and [DR-mini Step-by-Step](https://microsoft.sharepoint-df.com/:fl:/r/contentstorage/CSP_3c0b5b21-10e3-4ea9-b75c-0a29bedb9068/Document%20Library/LoopAppData/DR-mini%20Step-by-Step.loop?d=w975878dfa4c845f3af4b48fe6327b488&csf=1&web=1&e=3350vV&nav=cz0lMkZjb250ZW50c3RvcmFnZSUyRkNTUF8zYzBiNWIyMS0xMGUzLTRlYTktYjc1Yy0wYTI5YmVkYjkwNjgmZD1iJTIxSVZzTFBPTVFxVTYzWEFvcHZ0dVFhQ3gtRGE5b3RsZE5zT2ZpeDRVYUdqcXhrTDhCSHlEZVJvb2RmczBUbzZqWCZmPTAxQVRVR0M0NjdQQk1KUFNGRTZOQzI2UzJJN1pSU1BORUkmYz0lMkYmYT1Mb29wQXBwJnA9JTQwZmx1aWR4JTJGbG9vcC1wYWdlLWNvbnRhaW5lciZ4PSU3QiUyMnclMjIlM0ElMjJUMFJUVUh4dGFXTnliM052Wm5RdWMyaGhjbVZ3YjJsdWRDMWtaaTVqYjIxOFlpRkpWbk5NVUU5TlVYRlZOak5ZUVc5d2RuUjFVV0ZEZUMxRVlUbHZkR3hrVG5OUFptbDRORlZoUjJweGVHdE1PRUpJZVVSbFVtOXZaR1p6TUZSdk5tcFlmREF4UVZSVlIwTTBORTFJV2tNeU5ETXlWRk5XUVRNMlR6VkhOMU5STkRkQ1JUTSUzRCUyMiUyQyUyMmklMjIlM0ElMjJkMTFmYTgwNi0yOTQyLTQ3MzMtOTBlOS05Mzk2Y2QwZjY2ODclMjIlN0Q%3D)) 

## Step 1: Snapshot Preparation

You'll need 32 GPUs for converting a full scallion d64 model.

To convert weights tc->ixf, run this script.

```
python ~/code/glass/microsoft/tools/convert/prepare_snapshot.py \
--model_name="falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal" \
--falcon_path="az://path/to/model/train_step_xxxxxx/policy" \
--export_dir="your-model-save-name"
```
The snapshot preparation is executed through the bus node. As an example:

```
root@ischakra-bus-node-n-0:~/code/glass/microsoft/tools/convert$ python prepare_snapshot.py --model_name="falcon.multimodal.runs.scallion-d36-s64-lpe" --falcon_path="az://orngscuscresco/twapi/mini/e/ischakra-workberry-o4_mini/policy/step_000001/250828171342KSCUENAU-0/" --export_dir="o4_mini_v0"

```
## Step 2: Nimbus registration

1. Create ledger file: If you want to use LLM API self-serve deployment the registry must be "openai-devault". `snapshot_dir` is where the model was saved in (https://orngtransfer.blob.core.windows.net/devaultoutbound/models)  by the conversion process.
`snapshots->msft` is the snapshot.*.json in that model directory. Example below.

```
 {
     "snapshot_name": "some-name",
     "snapshot_id": "some-name",
     "snapshot_dir": "[alias]/your-model-save-name",
     "snapshots": {
         "msft": "snapshot.[*].json"
     },
     "description": "Test deployment of fine-tuned GG",
     "msft_tent": "orange",
     "asset_type": "IXF",
     "registry":
     {
         "registry_name": "openai-devault",
         "registry_primary_region": "centralus",
         "resource_group": "registry-openai-devault",
         "subscription_id": "699a8a35-07fd-475c-8586-4a145fd6a011"
     }
 }
```

3. Put your ledger file here and raise a PR: (https://dev.azure.com/project-argos/Mumford/_git/oai-engine-configs?path=/models/ledgers) (I think just about anyone with orange access can approve)
    - put your ledger file directly in the `models/ledgers/` folder as currently the automation pipeline doesn't traverse nested directories
Here's an example of a PR of the model snapshot created above.

```
{
     "snapshot_name": "workberry_o4_mini_v0",
     "snapshot_id": "77ee173a03a5be67466e72f7952886d9ffde8316391e00d7e5d45259da24ae10",
     "snapshot_dir": "ischakra/o4_mini_v0",
     "snapshots": {
         "msft": "snapshot.77ee173a03a5be67466e72f7952886d9ffde8316391e00d7e5d45259da24ae10.json"
     },
     "description": "Test deployment of FTed o4-mini for WorkBerry",
     "msft_tent": "orange",
     "asset_type": "IXF",
     "registry":
     {
         "registry_name": "openai-devault",
         "registry_primary_region": "centralus",
         "resource_group": "registry-openai-devault",
         "subscription_id": "699a8a35-07fd-475c-8586-4a145fd6a011"
     }
 }
```
- You can set any values you like for snapshot_name and snapshot_id
- snapshot_dir is <alias>/{export_dir} in above step.
- Go to IXF snapshot location: (az://orngtransfer/devaultoutbound/models/ischakra/o4_mini_v0/), find snapshot.{GUID}.json file and put its name in snapshots.msft
- Send out a pull request and ask someone to approve it.
Once the PR is approved and merged to main branch, go to [Pipelines - Runs for api-model-automated-onboarding](https://dev.azure.com/project-argos/Sorento/_build?definitionId=1472) and then you will find a new job there.

4. Wait for the model to get onboarded. Find your model here: [Model Snapshots | Nimbus](https://westcentralus.api.azureml.ms/nimbus/ux/models) (use your @microsoft.com account to log in)

## Step 3: Deploy to LLM API

Go to https://substrate.microsoft.net/v2/llmApi. Next, go to a scenario, LLM FT Deployments and hit New Deployment. (If you don't see LLM FT deployments under your scenario contact LLM API for adding you to the flight for no-code deployments).


For o4-mini: 

Model Feed is "zen-os4-mini-s350-4shard-2025-04-03-19-15=<model asset id>", and <model asset id> is "azureml://registries/openai-devault/models/..." from model information in Nimbus.
