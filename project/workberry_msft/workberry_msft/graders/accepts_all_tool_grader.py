import json
import traceback
from collections import defaultdict
from typing import Sequence

import chz
from berry.grader import FunctionalGrader, GraderOutput
from chat import chat
from qstar.common import types, utils
from qstar.sample_completers import sample_completer


@chz.chz
class AcceptsAllWithToolGrader(FunctionalGrader):
    """Grader that accepts solutions if there's any tool call in the conversation"""

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> list[GraderOutput]:
        graded_samples = []
        try:
            for s in samples:
                tool_calls = defaultdict(int)
                for msg in s.conversation.messages:
                    if msg.author.role == chat.Role.TOOL:
                        tool_calls[msg.author.name] += 1
                num_tool_calls = sum(tool_calls.values())

                given_answer = utils.extract_answer_from_sample(s)
                is_correct = num_tool_calls > 0

                graded_samples.append(
                    GraderOutput.with_binary_reward(
                        grader_nickname="AcceptsAllWithToolGrader",
                        sample_id=s.sample_id,
                        reward_name="accept_with_tool",
                        is_correct=is_correct,
                        given_answer=given_answer,
                        metrics={
                            "num_tool_calls": num_tool_calls,
                        },
                        telemetry_metadata={
                            "num_tool_calls": num_tool_calls,
                            "tool_calls": json.dumps(tool_calls),
                        },
                    )
                )
        except Exception as e:
            tb = traceback.format_exc()
            graded_samples = [
                GraderOutput.with_binary_reward(
                    grader_nickname="AcceptsAllWithToolGrader",
                    sample_id=s.sample_id,
                    reward_name="accept_with_tool",
                    is_correct=False,
                    given_answer=f"Fail to grade samples: {e}\n{tb}",
                )
                for s in samples
            ]

        return graded_samples
