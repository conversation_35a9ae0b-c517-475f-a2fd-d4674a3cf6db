import math
from typing import Sequence

import chz
from qstar.common import types, utils
from qstar.graders import answer_extraction
from qstar.graders import grader as grader_module
from qstar.sample_completers import sample_completer
from rouge_score.rouge_scorer import Rouge<PERSON>corer


def instantiate_rouge_scorer():
    return RougeScorer(["rouge1", "rouge2", "rougeL"], use_stemmer=True)


def grade_answer_with_rouge_scorer(
    scorer: RougeScorer,
    given_answer: str | None,
    gt_answer: str,
) -> float:
    """
    Computes log rewards based on the average of ROUGE scores
    """
    if given_answer is None:
        return float("-inf")

    scores = scorer.score(gt_answer, given_answer)
    grade = sum(i.fmeasure for i in scores.values()) / len(scores)
    if grade > 0:
        return math.log(grade)

    return float("-inf")


@chz.chz(typecheck=True)
class RougeGrader(grader_module.Grader):
    """
    Grader that grades according to the ROUGE score between output and
    reference answer
    """

    extractor: answer_extraction.AnswerExtractor[str] = chz.field(
        doc="Answer extractor to use for extracting answers from samples.",
        default_factory=answer_extraction.ChannelExtractor,
    )

    score_threshold: float = chz.field(
        doc="The threshold of grader score. If the score is below this threshold, the response will be considered as incorrect answer",
        default=float("-inf"),
    )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> list[types.SampleWithGrade]:
        given_answers = self._extract_answers(samples)
        gt_answers: list[str] = [utils.not_none(sample.gt_datapoint.answer) for sample in samples]

        scorer = instantiate_rouge_scorer()

        grades = [
            grade_answer_with_rouge_scorer(scorer, given_answer, gt_answer)
            for given_answer, gt_answer in zip(given_answers, gt_answers)
        ]

        result: list[types.SampleWithGrade] = [
            sample.with_grade(
                log_rewards={"rouge_grader": grade},
                is_correct=grade >= math.log(self.score_threshold + 0.000001),
                given_answer=given_answer,
                telemetry_metadata={"ground_truth_answer": gt_answer},
            )
            for sample, given_answer, gt_answer, grade in zip(
                samples, given_answers, gt_answers, grades, strict=True
            )
        ]

        return result

    def _extract_answers(self, samples: Sequence[types.SampleWithCompletion]) -> list[str | None]:
        """
        Determines how the samples are converted into gradable answers. RougeGrader uses utils.extract_answer,
        but this method can be overriden in subclasses to modify the behavior
        """
        answers: list[str | None] = []
        for sample in samples:
            assert len(self.channels_for_answer) == 1, "Only one channel for answer is supported"
            extraction_result = self.extractor.extract_answer(
                sample, channel_for_answer=self.channels_for_answer[0]
            )
            if isinstance(extraction_result, answer_extraction.FailedExtraction):
                answer = None
                sample.metadata.update({"extraction_errors": extraction_result.errors})
            else:
                answer = extraction_result.answer
                sample.metadata.update(extraction_result.metadata)
            answers.append(answer)
        return answers
