import traceback
from typing import Any, AsyncIterator

import chz
import structlog
from chat import chat, tools
from qstar.common import datapoint, types
from qstar.common.tools.berry_tool_interface import <PERSON><PERSON><PERSON>, ToolConfig
from research_ace.v2.client import JupyterKernelRuntime

logger = structlog.stdlib.get_logger(component="SearchWebToolConfig")

TOOL_INSTRUCTIONS = """Search web pages to gather public information from outside of the enterprise.  Useful for gathering information available outside of the enterprise.
web_search(query: str) -> list[dict[str]]

The query is a well-formed web search query
""".strip()


class DummySearchWebTool(BerryTool):
    """
    A mocked tool which always returns a single page.
    """

    def __init__(self):
        super().__init__()

    @property
    def name(self) -> str:
        return "web_search"

    @property
    def is_stateful(self) -> bool:
        return False

    def instruction(self) -> str:
        return TOOL_INSTRUCTIONS

    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        yield chat.Message(
            author=chat.Author(role=chat.Role.TOOL, name=self.name),
            content=chat.Text.from_string(
                """
{
    "type": "webpage",
    "url": "https://agi.com/agi/beautiful-webpage",
    "title": "AGI's Beautiful Webpage",
    "body": "I only find beautiful results!",
    "id": "531CC6D019295300"
}
"""
            ),
        )

    async def _process(self, message: chat.Message) -> AsyncIterator[chat.Message]:
        raise NotImplementedError(f"This is not used by {self.__class__.__name__}")


@chz.chz
class DummySearchWebToolConfig(ToolConfig):
    def get_tool_name(self) -> str:
        return "web_search"

    def unique_descriptor_for_variants(self) -> str:
        return "web_search"

    def instruction(self, datapoint: datapoint.HarmonyDatapoint | None = None) -> str:
        return TOOL_INSTRUCTIONS

    def _initialize_tool(
        self,
        ace_runtime: JupyterKernelRuntime | None = None,
        **kwargs: Any,
    ) -> BerryTool:
        return DummySearchWebTool()
