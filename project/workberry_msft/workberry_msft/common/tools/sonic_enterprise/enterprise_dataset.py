import hashlib
import json
import logging
import os
import pickle
import time
from datetime import datetime, timedelta
from functools import lru_cache
from pathlib import Path
from typing import Any, Callable, List, Optional, Tuple

import chz
import structlog
from sentence_transformers import SentenceTransformer, util
from workberry_msft.common.tools.sonic_enterprise.utils import ToolType, copy_blob

logger = structlog.stdlib.get_logger(component="sonic_enterprise.enterprise_dataset")


def get_cache_key(entities_dir: Path, transformer_model_name: str) -> str:
    """
    Generate a cache key based on the entities directory and transformer model.
    The key is a hash of the directory path, file contents of all entity files,
    and the transformer model name.
    """
    if not entities_dir.exists():
        return ""

    # Get all relevant files for hashing
    files = []
    if entities_dir.is_dir():
        files.extend(list(entities_dir.glob("*.jsonl")))
        files.extend(list(entities_dir.glob("*.json")))
    else:
        files = [entities_dir]

    if not files:
        return ""

    # Create a hash combining path, model name, and file content hashes
    hasher = hashlib.md5()
    # hasher.update(str(entities_dir).encode("utf-8"))
    # Ensure transformer_model_name is a string
    hasher.update(str(transformer_model_name).encode("utf-8"))

    for file in sorted(files):
        try:
            # Add file path to the hash
            hasher.update(str(file).encode("utf-8"))

            # Add file contents to the hash
            with open(file, "rb") as f:
                # Read in small chunks to handle large files efficiently
                chunk = f.read(8192)
                while chunk:
                    hasher.update(chunk)
                    chunk = f.read(8192)
        except Exception as e:
            logger.warning(f"Error reading file {file} for cache key: {e}")

    return hasher.hexdigest()


def load_entities(entities_dir_or_file: Path) -> List[dict]:
    """
    Load entities from a directory or file.
    If the input is a directory, it will load all JSON and JSONL files in the directory.
    If the input is a file, it will load the file directly.
    """
    entities = []
    if entities_dir_or_file.is_dir():
        for entity_file in entities_dir_or_file.glob("*.jsonl"):
            with open(entity_file, "r", encoding="utf-8") as f:
                for line in f.readlines():
                    entities.append(json.loads(line))
        for entity_file in entities_dir_or_file.glob("*.json"):
            with open(entity_file, "r", encoding="utf-8") as f:
                parsed = json.load(f)
                if isinstance(parsed, list):
                    entities.extend(parsed)
                else:
                    entities.append(parsed)
    elif entities_dir_or_file.is_file():
        with open(entities_dir_or_file, "r", encoding="utf-8") as f:
            parsed = json.load(f)
            if isinstance(parsed, list):
                entities.extend(parsed)
            else:
                entities.append(parsed)
    return entities


def ellipsize_string(s: str, max_length: int) -> str:
    """
    Ellipsize a string to a maximum length.
    """
    if len(s) > max_length:
        return s[:max_length] + "..."
    return s


def entity_to_text(entity: dict) -> str:
    """
    Convert an entity to a text representation.
    """
    if entity["type"] == "file":
        return entity["content"]
    elif entity["type"] == "webpage":
        return entity["body"]
    elif entity["type"] == "message":
        return entity["body"]
    elif entity["type"] == "email":
        return entity["body"]
    elif entity["type"] == "person":
        content = f"**{entity['displayName']}**"
        if "profile" in entity:
            content += f"**Profile**\n{entity['profile']}"
        if "myNotes" in entity:
            content += f"\n**My Notes**\n{entity['myNotes']}"
        if "projects" in entity:
            content += f"\n**Projects**\n{', '.join(entity['projects'])}"
        if "skills" in entity:
            content += f"\n**Skills**\n{', '.join(entity['skills'])}"
        return content
    elif entity["type"] == "meeting":
        return entity["description"]
    elif entity["type"].lower() == "meetingtranscript":
        return entity["content"]
    else:
        logger.warning(f"Unknown entity type {entity['type']}, falling back to JSON for text")
        return json.dumps(entity)


def get_snippet_from_entity(entity: dict, query: str, context_length: int = 100) -> str:
    """
    Extracts a snippet from an entity based on the query.

    Args:
        entity: The enterprise entity
        query: The search query
        context_length: Number of characters of context to include

    Returns:
        A snippet of text
    """
    try:
        content = entity_to_text(entity)

        query_lower = query.lower()
        content_lower = content.lower()

        if query_lower in content_lower:
            start_idx = content_lower.find(query_lower)
            snippet_start = max(0, start_idx - context_length)
            snippet_end = min(len(content), start_idx + len(query) + context_length)

            snippet = content[snippet_start:snippet_end]

            # Add ellipsis if necessary
            if snippet_start > 0:
                snippet = "..." + snippet
            if snippet_end < len(content):
                snippet = snippet + "..."

            return snippet

        # If query not found, return the first part of the content
        return content[:100] + ("..." if len(content) > 100 else "")
    except Exception:
        # Fallback to entity properties if we can't get the content
        if entity["type"] == "file":
            return entity.get("content", "")[:100] + "..."
        elif entity["type"] == "webpage":
            return entity.get("body", "")[:100] + "..."
        elif entity["type"] == "message":
            return entity.get("body", "")[:100] + "..."
        elif entity["type"] == "email":
            return entity.get("body", "")[:100] + "..."
        elif entity["type"] == "person":
            return entity.get("displayName", "") + " - " + entity.get("profile", "")[:50] + "..."
        elif entity["type"] == "meeting":
            return entity.get("description", "")[:100] + "..."
        elif entity["type"].lower() == "meetingtranscript":
            return entity.get("content", "")[:100] + "..."
        else:
            return "No snippet available"


def get_entity_title(entity: dict) -> str:
    # Determine the title based on entity type
    entity_id = entity["id"]
    if entity["type"] == "file":
        title = entity.get("title", f"File {entity_id}")
    elif entity["type"] == "webpage":
        title = entity.get("title", f"Web Page {entity_id}")
    elif entity["type"] == "message":
        title = entity.get("chatName", f"Message {entity_id}")
    elif entity["type"] == "email":
        title = entity.get("subject", f"Email {entity_id}")
    elif entity["type"] == "person":
        title = entity.get("displayName", f"Person {entity_id}")
    elif entity["type"] == "meeting":
        title = entity.get("subject", f"Meeting {entity_id}")
    elif entity["type"].lower() == "meetingtranscript":
        title = entity.get("title", f"Transcript {entity_id}")
    else:
        title = f"Entity {entity_id}"
    return title


def get_entity_url(entity: dict) -> str:
    if "webUrl" in entity:
        url = entity["webUrl"]
    elif "url" in entity:
        url = entity["url"]
    else:
        url = f"enterprise://{entity['id']}"
    return url


def entity_to_corpus_entries(entity: dict) -> list[tuple[str, dict[str, Any]]]:
    # Types {'object', 'file', 'webpage', 'message', 'email', 'person', 'meeting', 'MeetingTranscript'}
    entity_type = entity["type"]
    if entity_type == "file":
        return [(entity["content"], {**entity, "content": ellipsize_string(entity["content"], 20)})]
    elif entity_type == "webpage":
        return [(entity["body"], {**entity, "body": ellipsize_string(entity["body"], 20)})]
    elif entity_type == "message":
        return [(entity["body"], {**entity, "body": ellipsize_string(entity["body"], 20)})]
    elif entity_type == "email":
        return [(entity["body"], {**entity, "body": ellipsize_string(entity["body"], 20)})]
    elif entity_type == "person":
        entries = []
        if "displayName" in entity:
            entries.append((entity["displayName"], entity))
        if "profile" in entity:
            entries.append((entity["profile"], entity))
        if "myNotes" in entity:
            entries.append((entity["myNotes"], entity))
        return entries
    elif entity_type == "meeting":
        return [(entity["description"], entity)]
    elif entity_type.lower() == "meetingtranscript":
        return [(entity["content"], {**entity, "content": ellipsize_string(entity["content"], 20)})]
    fallback_properties = entity.get("properties", {})
    fallback_content = (
        entity.get("content")
        or fallback_properties.get("content")
        or fallback_properties.get("body")
        or fallback_properties.get("profile")
    )
    if fallback_content:
        return [(fallback_content, entity)]
    else:
        raise ValueError(f"Unknown entity type: {entity_type}")


def get_entity_timestamp(entity: dict) -> Optional[str]:
    entity_type = entity["type"].lower()
    if entity_type == ToolType.File.value:
        return entity.get("lastModifiedTime")
    elif entity_type == ToolType.Chat.value:
        return entity.get("timestamp")
    elif entity_type == ToolType.Email.value:
        return entity.get("dateTimeSent")
    elif entity_type == ToolType.People.value:
        return None
    elif entity_type == ToolType.Meeting.value:
        return entity.get("start")
    return None


def create_dataset_filter(
    source: Optional[str] = None, recency_days: Optional[int] = None
) -> Callable[[dict], bool]:
    def filter(entity: dict) -> bool:
        """
        Default filter that accepts all entities.
        """
        if source and entity["type"].lower() != source.lower():
            return False
        timestamp = get_entity_timestamp(entity)
        if recency_days is not None and timestamp:
            # Convert timestamp to datetime object
            entity_time = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
            # Check if the entity is within the recency_days range
            return entity_time >= datetime.now() - timedelta(days=recency_days)
        return True

    return filter


class EnterpriseDataset:
    def __init__(
        self,
        entities: list[dict[str, Any]],
        transformer: SentenceTransformer | None = None,
        name: str | None = None,
    ):
        transformer = transformer if transformer else SentenceTransformer("all-MiniLM-L6-v2")
        self.transformer = transformer
        self.name = name if name is not None else "Unknown"
        clean_entities = []
        for entity in entities:
            if isinstance(entity, dict) and "id" in entity and "type" in entity:
                # Prefix the ID with the type to make it clear its a string
                entity["id"] = f"{entity['type']}_{entity['id']}"
                # If no URL exists, make one up
                if "url" not in entity:
                    entity["url"] = f"enterprise://{entity['type']}/{entity['id']}"
                clean_entities.append(entity)
            else:
                logger.warning(f"Skipping entity {entity} because it is not a dictionary")
        entities = clean_entities
        self.entities_by_id = {entity["id"]: entity for entity in entities}
        self.entities_by_url = {entity["url"]: entity for entity in entities if "url" in entity}
        self.corpus: list[Tuple[str, dict[str, Any]]] = []
        for entity in self.entities_by_id.values():
            try:
                self.corpus.extend(entity_to_corpus_entries(entity))
            except Exception as e:
                logger.warning(f"Skipping entity {entity} due to error", exc_info=True)
        logger.info(
            f"Encoding {len(self.corpus)} entries from {len(entities)} entities into corpus"
        )
        self.embeddings = transformer.encode(
            [content for content, _ in self.corpus], show_progress_bar=True
        )

    @classmethod
    def from_entity_files(
        cls,
        entities_dir: str | Path,
        entity_file_uri: str,
        transformer: Optional[SentenceTransformer] = None,
    ):
        """
        Create an EnterpriseDataset from a directory of entities.
        Uses a local cache to improve startup time when repeatedly loading the same entities.
        """
        if isinstance(entities_dir, str):
            entities_dir = Path(entities_dir)
        if not entities_dir.exists():
            raise ValueError(f"Entities directory {entities_dir} does not exist.")

        # Set up cache directory
        cache_dir = Path.home() / ".cache" / "workberry"
        os.makedirs(cache_dir, exist_ok=True)

        # Get transformer model name for cache key
        transformer_model_name = "all-MiniLM-L6-v2"  # default
        if transformer is not None:
            # Try to get model name attribute if available
            transformer_model_name = getattr(transformer, "model_name_or_path", str(transformer))

        # Generate cache key and path
        # cache_key = get_cache_key(entities_dir, transformer_model_name)

        # TODO: Temporarily set cache key from file names to debug cache misses with get_cache_key()
        cache_key = hashlib.md5(
            f"{entity_file_uri}_{transformer_model_name}".encode("utf-8")
        ).hexdigest()
        cache_file = cache_dir / f"enterprise_dataset_{cache_key}.pkl"
        logger.info("Cache key", cache_key=cache_key)

        # Try to load from cache
        if cache_key and cache_file.exists():
            try:
                logger.info(f"Loading EnterpriseDataset from cache: {cache_file}")
                start_time = time.time()
                with open(cache_file, "rb") as f:
                    dataset = pickle.load(f)
                elapsed = time.time() - start_time
                logger.info(f"Loaded EnterpriseDataset from cache in {elapsed:.2f} seconds")
                return dataset
            except Exception as e:
                logger.warning(f"Failed to load from cache: {e}")
                # If cache loading fails, continue with normal loading

        # Normal loading process
        start_time = time.time()
        entities = load_entities(entities_dir)
        assert len(entities) > 0, f"No entities found in {entities_dir}"
        logger.info(f"Loaded {len(entities)} entities from {entities_dir}")

        # Extract name from the entities directory path (last segment)
        dataset_name = entities_dir.name if entities_dir.is_dir() else entities_dir.stem

        # Create dataset
        dataset = cls(entities, transformer, name=dataset_name)

        elapsed = time.time() - start_time
        logger.info(f"Created EnterpriseDataset from {entities_dir} in {elapsed:.2f} seconds")

        # Save to cache if we have a valid cache key
        if cache_key:
            try:
                logger.info(f"Saving EnterpriseDataset to cache: {cache_file}")
                with open(cache_file, "wb") as f:
                    pickle.dump(dataset, f)
            except Exception as e:
                logger.warning(f"Failed to save to cache: {e}")

        # Return the dataset
        return dataset

    def search(
        self,
        queries: str | list[str],
        top_k: int = 5,
        filter: Callable[[dict], bool] = lambda x: True,
    ) -> List[dict]:
        """
        Search the dataset for files matching the query and filter predicate.
        """
        if isinstance(queries, str):
            queries = [queries]
        results = util.semantic_search(
            self.transformer.encode(queries),  # type: ignore
            self.embeddings,  # type: ignore
            top_k=50,
        )
        results_by_id = {}
        for res in [item for sublist in results for item in sublist]:
            corpus_id = res["corpus_id"]
            if corpus_id not in results_by_id:
                results_by_id[corpus_id] = res
            else:
                existing_res = results_by_id[corpus_id]
                if res["score"] > existing_res["score"]:
                    results_by_id[corpus_id] = res

        sorted_results = sorted(results_by_id.values(), key=lambda x: x["score"], reverse=True)
        entities = [self.corpus[res["corpus_id"]][1] for res in sorted_results]
        filtered_entities = [entity for entity in entities if filter(entity)]

        return filtered_entities[:top_k]

    def get_entity(self, id: str) -> dict:
        """
        Read an entity by ID.
        """
        if id not in self.entities_by_id:
            raise ValueError(f"Entity with ID {id} not found.")
        return self.entities_by_id[id]

    def get_entity_by_url(self, url: str) -> dict:
        """
        Read an entity by URL.
        """
        if url not in self.entities_by_url:
            raise ValueError(f"Entity with URL {url} not found.")
        return self.entities_by_url[url]

    def open(self, id: str) -> str:
        entity = self.get_entity(id)
        return entity_to_text(entity)


@chz.chz
class SentenceTransformerConfig:
    model_path: str = chz.field(
        doc="Path to the sentence transformer model",
    )
    model_definition_path: str = chz.field(
        default="snapshots/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/",
        doc="Path to the model definition within the downloaded model assets",
    )

    local_model_download_path: str = "/tmp/sentence_transformer_model"

    def create_transformer(self) -> Any:
        logger.info(
            "Downloading sentence transformer model",
            model_path=self.model_path,
            local_path=self.local_model_download_path,
        )
        if not os.path.exists(self.local_model_download_path):
            copy_blob(self.model_path, self.local_model_download_path)

        return SentenceTransformer(
            os.path.join(self.local_model_download_path, self.model_definition_path), device="cpu"
        )


@lru_cache(maxsize=1)
def initialize_dataset_from_path(
    entity_file_uri: str, sentence_transformer: SentenceTransformerConfig
) -> EnterpriseDataset:
    transformer = sentence_transformer.create_transformer()
    entity_file_download_path = Path.home() / ".cache" / "enterprise_entity_files"
    if not os.path.exists(entity_file_download_path):
        logger.info(
            "Downloading entity files from URI",
            entity_file_uri=entity_file_uri,
            download_path=entity_file_download_path,
        )
        copy_blob(entity_file_uri, entity_file_download_path)
    return EnterpriseDataset.from_entity_files(
        entity_file_download_path, entity_file_uri, transformer=transformer
    )
