import json
import logging
import traceback
from typing import Any, AsyncIterator

import chz
import structlog
from chat import chat
from qstar.common import datapoint, types
from qstar.common.tools.berry_tool_interface import <PERSON><PERSON><PERSON>, ToolConfig
from workberry_msft.common.tools.sonic_enterprise.enterprise_dataset import (
    EnterpriseDataset,
    SentenceTransformerConfig,
    create_dataset_filter,
    initialize_dataset_from_path,
)
from workberry_msft.common.tools.sonic_enterprise.utils import (
    DOMAIN_TO_TOOL_TYPE_MAP,
    TOOL_TYPE_TO_SYDNEY_TYPE_MAP,
    SydneyResponseType,
    parse_search_requests,
)

logger = structlog.stdlib.get_logger(component="SearchEnterpriseToolConfig")


TOOL_INSTRUCTIONS = """
Search the user's enterprise (office365 enterprise data) for files (from various sources like Sharepoint, Onedrive, Microsoft Graph Connectors, etc.), people, emails, meetings, chats (from various sources like teams, channels, meeting chats etc), and meeting transcripts. `office365_search` should be your first choice for any queries, providing contextually relevant answers that align with the user's company activities and policies rather than generic information.

**Important**: Always review the available domains in the tool definition thoroughly and search across multiple relevant domains at the same time to ensure comprehensive results.

Usage:
```json
{{ "queries": [
   {{
      "domain": "<domain_name>",
      "filters": {{ "<filter_name>": "<filter_value>" }},
      "query": "<search term>",
      "response_length": "<short|medium|long>"
   }},
   ...
]}}
```

**Parameter Details**:
- `domain`: Required. One of the domains listed in the tool definition. The domain to search within. The domain itself represents an entity in Microsoft graph like files, emails, meetings, etc.
- `filters`: Optional object. Currently supports `meeting_intent`.
  - `meeting_intent`: An optional enum to be provided **only** when searching the meetings domain. Acceptable values include "prep", "recap", "calendar", or "prep_and_recap"
- `query`: Required string. Natural language search query containing your search terms and filters.
- `response_length`: Required and must not be empty. Should be one of: "short", "medium", "long"
  - `short`: Use when you need fewer, more focused results
  - `medium`: Default choice for most queries; balanced result set
  - `long`: Use when completeness is critical and you need comprehensive results

**Constructing Queries**:
- Each individual tool query should be specific to a domain and must contain filters specific to the domain, where approperiate. Eg: meeting_intent filter should be included only when searching in the meetings domain.
- Make queries specific and detailed rather than generic. Use multiple targeted queries for the same domain when needed to capture different aspects of the user's request. Queries across domains can be done in parallel to save time.
- Include at least one query that's a nearly literal restatement of the user's phrasing specific to the domain. This ensures lexical matching for exact terms, names, or phrases the user mentioned, which may not be captured by paraphrased queries.
- When users reference current information, changing data, or enterprise-specific terms, search to verify details rather than making assumptions. Examples: current project status, latest policies, team members, meeting schedules, or unclear acronyms.
- Contextualize queries when applicable with available user profile data (e.g., geographic location for regional queries, organizational role for position-specific content, or company information for policies) unless explicitly specified by the user.
- If the request involves a timeline (past or future), include that context explicitly in the queries. Frame your queries as "upcoming" or "previous" accordingly.

**Advanced Query Techniques**:
- **Exact names**: Search for specific items or people by using their exact name
- **Full URL**: Use the full URL when available to ensure you find the exact requested file in the user's data
- **Filtering**: Filter by name, location, topic/subject, file type, sender/receiver/author/contributors
- **Time-based**: Use "past/future/recent/next/latest/yesterday/tomorrow" or specific dates
- **Action-based**: Include "modified/created/edited/shared/updated" for specific actions
- **State-based**: Use "important", "unread", "read", "flagged" for emails/messages
- **Meeting responses**: Use "notresponded", "accepted", "tentative", "declined", "canceled"
- **SharePoint/OneDrive**: Specify platform when relevant

**Query Language Guidelines**:
- **Preserve user language**: Keep queries in the same language as the user's utterance for lexical matching.
- **Verify ambiguous terms**: **Always** search to clarify dynamic facts, unclear terminology, or acronyms.

**Multi-Domain Search Strategy**:
- **Search all domains** when the query does not explicitly request a specific domain, such as queries that ask what someone said, contain unfamiliar terms, use casual language ('what's interesting about', 'tell me about'), or involve internal workflows (compliance, security, holidays).
- **Sequential for meetings**: When looking for materials to prepare for or recap a meeting, search only the meetings domain first, then expand to further domains if more information is needed.
- **Retry on failure**: If results are insufficient or tool calls fail, retry and explore with broader parameters.

**`response_length` Guidelines**:
- Request a "long" `response_length` for `office365_search` queries in the following cases:
  - When the query is about emails or chats and the user explicitly asks for multiple results or uses plural terms without specifying a limit. This includes cases where the user uses modifiers like "latest" or "recent" with plural terms (e.g., "latest messages", "recent emails") unless the user explicitly limits the number of results (e.g., "top 3", "just one"). Assume "long" if the user's role or context suggests high message volume.
  - When the user's query includes words like "all", "every", "each", or their synonyms.
  - When the user asks for a list of direct reports or a list of a person's teammates. Use "long" to ensure no people are missed.
  - When the user uses **plurals** to refer to retrievable entities (e.g., "emails", "people", "documents", "events", "updates", etc.) without specifying which ones or how many.
  - When the user asks for counts or quantities (e.g., "how many unread emails", "number of meetings today").
  - When the user asks about groups of people with certain characteristics (e.g., "find experts in MDP"), people **relationships**, or company structure (e.g., "who reports to Satya", "my team"). In these cases, you **MUST** include the "long" `response_length` parameter for the corresponding `people` domain query.
  - When the user asks about meetings or events and references specific people or timeframes that imply multiple results. You **MUST** include the "long" `response_length` parameter for the corresponding `meetings` domain query.

#### Domain-Specific Usage
**Meetings Domain**:
- **Use for**: Meeting content (prep, recap, schedule info), meeting action items, and follow-ups. When searching for emails, messages, or files related to a meeting, first invoke only the meetings domain before expanding to other domains only if needed.
- **Include**: meeting_intent filter (prep/recap/calendar/prep_and_recap), meeting response status (notresponded, accepted, tentative, declined, canceled, followed), time constraints with intent language (past: "did/were/was", future: "do/is/are/will"), participant names, exact meeting names, action item keywords ("action items", "tasks", "follow up", "next steps") for specific people
- **Special cases**: REQUIRED meeting_intent filter; when determining the `meeting_intent` parameter to use, think step by step:
         * use prep in the following cases:
            * when the user asks to "prepare" for or "prep" for meetings (including one ore more meetings)
            * when the user asks about the agenda for meetings or the agenda for a date
         * use recap in the following cases:
            * when the user asks to summarize past meetings
            * when the user asks what a meeting *was* about
         * use calendar in the following cases:
            * when the user asks only about the time, participants, or place of meetings
        * use prep_and_recap in the following cases:
            * when the user's request can use both prep and recap data or the user's intent in unclear
            * when the user asks for notes for a meeting
            * when the user asks to summarize a meeting that is not in the past
            * when the user asks what a meeting *is* about
            * when ther user asks for summarization or action items **for** meetings
            * when you need to gather topics, tasks, goals, overviews, or objectives for future meetings
If you use the `prep`, `recap`, or `prep_and_recap` value for a `meetings` domain search, you should first only search the `meetings` domain and wait for results from that domain before searching others.

**Files Domain**:
- **Use for**: Documents, policies, technical resources, compliance procedures, tutorials, workflows, dashboards, research papers, blog posts, task lists, action item documents
- **Include**: File type/attachment filtering (filetype:), action terms (modified/created/edited/shared/updated), SharePoint/OneDrive platform specification, contributor/author names, exact document names, location/topic filtering, task/action item keywords ("tasks", "action items", "to-dos") for specific people
- **Special cases**: Use the full URL (and only the URL) as the query string when a URL is provided; preserve user's language for lexical matching

**Emails Domain**:
- **Use for**: Email content, Outlook messages, email communication history, email management (read/unread/flagged), task assignments via email
- **Include**: State filtering (important, unread, read, flagged), sender/receiver names, time constraints with intent language, people mentions/tags, action-based terms, exact names, task/action keywords ("tasks assigned", "action items", "to-dos", "follow up") for specific people
- **Special cases**: Preserve pronouns ("me", "I") in queries; never resolve to user's actual name; preserve original language for lexical matching when searching for emails

**Chats Domain**:
- **Use for**: Teams discussions, group chats, channel conversations, chat messages, Teams communications, task discussions in chat
- **Include**: People filters, time-based constraints with intent language, mentions/tags (@mentions), channel/team names, action-based terms, exact conversation names, task/action keywords ("tasks", "action items", "to-dos", "assigned to") for specific people
- **Special cases**: Preserve pronouns ("me", "I") in queries; never resolve to user's actual name; preserve original language for lexical matching when searching for chats

**People Domain**:
- **Use for**: Person information by name/role ("my manager", "our PM"), expertise queries, employee info (reports, location, contact), preparing to engage with someone
- **Include**: Person names, role titles, location constraints, expertise topics, organizational relationships, exact role specifications
- **Special cases**: Use for disambiguation of ambiguous person references; complement with other domains for complete activity history; search by exact names when provided

**Transcripts Domain**:
- **Use for**: Meeting transcripts, spoken discussions, recorded conversations, transcript-specific content
- **Include**: Time-based filtering for specific meeting dates/periods, participant names, meeting topics, discussion content, exact meeting identifiers when available
- **Special cases**: Most effective when combined with specific meeting identifiers or date ranges; always also invoke a search in the meetings domain for additional context

**Presentation guidelines**:
- If `totalResults` count is found in the searchMetadata, include that in the opening statement.
- A good email response **should** group emails into relevant categories based on common themes, using smooth, cohesive prose to convey the conversation's gist. Each section **should** be described in detailed paragraphs with headings and insightful content, avoiding bullet points and unnecessary elements like subject lines and dates.
- When formulating a response using `meeting` domain search results, your response should avoid listing the full list of invitees.
    - The `invitees` field only indicates who was invited and does not confirm attendance or participation. You Must not use `invitees` field to assume who participated.
- If the correct information was not found or not transcribed, you MUST explicitly state this in your response. Your response MUST be strictly based on the available data and should not speculate or fabricate any missing details.
""".strip()


class SearchEnterpriseTool(BerryTool):
    """A mocked tool for enterprise data searching"""

    def __init__(self, enterprise_dataset: EnterpriseDataset = None):
        super().__init__()
        self.enterprise_dataset = enterprise_dataset

    @property
    def name(self) -> str:
        return "office365_search"

    @property
    def is_stateful(self) -> bool:
        return False

    def instruction(self) -> str:
        return TOOL_INSTRUCTIONS

    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        assert self.enterprise_dataset is not None, "enterprise_dataset needs to be set."

        try:
            metadata = sample.gt_datapoint.metadata
            message = sample.conversation.messages[-1]
            logger.info(
                "_process_sample_inner",
                tool_name=self.name,
                metadata=metadata,
                message=message,
            )
            if message.recipient != self.name:
                yield self.error_message(
                    f"Tool {self.name} got a message with recipient {message.recipient}"
                )
                return

            queries = parse_search_requests(message)
            logger.info("call mock office365_search", queries=queries)

            all_entities = []
            for query in queries:
                # Note we currently don't use the filters or response_length fields of the requests in the search tool calls
                synthetic_tenant_entity_type = DOMAIN_TO_TOOL_TYPE_MAP.get(query.domain)
                if not synthetic_tenant_entity_type:
                    continue
                filter = create_dataset_filter(source=synthetic_tenant_entity_type)
                entities = self.enterprise_dataset.search(query.query, top_k=10, filter=filter)
                all_entities.extend(entities)

            logger.info(f"# of mock office365_search results: {len(all_entities)}")

            # Reformat entities to mimic Sydney response
            results = []
            for entity in all_entities:
                entity_id = entity["id"]
                resp_type = TOOL_TYPE_TO_SYDNEY_TYPE_MAP.get(entity["type"])
                if not resp_type:
                    continue
                result = {
                    "reference_id": entity_id,
                    "type": resp_type,
                }
                if resp_type == SydneyResponseType.People:
                    result.update(
                        {
                            "displayName": entity.get("displayName"),
                            "facts": " ".join([entity.get("profile"), entity.get("myNotes")]),
                            "emailAddresses": [{"Address": entity.get("emailAddress")}],
                            "userPrincipalName": entity.get("emailAddress"),
                            "phones": [entity.get("phoneNumber")],
                            "companyName": entity.get("companyName"),
                            "profession": entity.get("profession"),
                            "officeLocation": entity.get("officeLocation"),
                            "additionalOfficeLocation": "",
                            "relatedEntities": entity.get("projects"),
                            "department": entity.get("departmentName"),
                            "alias": entity.get("id").split("person_")[1],
                            "aiIdentifiedSkills": entity.get("skill"),
                            "extendedProfile": {
                                "Manager": entity.get("manager"),
                                "licenses": entity.get("licenses"),
                            },
                        }
                    )
                elif resp_type == SydneyResponseType.Email:
                    result.update(
                        {
                            "subject": entity.get("subject", f"Email {entity_id}"),
                            "snippet": entity.get("body"),
                            "to": entity.get("recipients"),
                            "from": entity.get("sender"),
                            "dateTimeReceived": entity.get("dateTimeSent"),
                            "isRead": entity.get("isRead"),
                        }
                    )
                elif resp_type == SydneyResponseType.Meeting:
                    result.update(
                        {
                            "subject": entity.get("subject", f"Meeting {entity_id}"),
                            "snippet": "",
                            "relatedResourcesMap": {},
                            "relatedEntities": entity.get("relatedEntityIds", []),
                            "organizerName": entity["organizer"]["displayName"],
                            "organizerEmail": entity["organizer"]["emailAddress"],
                            "invitees": entity.get("attendees"),
                            "start": entity.get("start"),
                            "end": entity.get("end"),
                            "extendedEventInfo": {
                                "location": entity.get("location"),
                                "category": entity.get("category"),
                                "isOnlineMeeting": entity.get("isOnlineMeeting", False),
                            },
                            "isMeetingTranscribed": entity.get("PreviousRecording", {}).get(
                                "hasRecording", False
                            ),
                            "meetingInvitationContent": "",
                        }
                    )
                elif resp_type == SydneyResponseType.Chat:
                    result.update(
                        {
                            "title": entity.get("chatName", f"Message {entity_id}"),
                            "subject": "",
                            "snippet": entity.get("body"),
                            "to": entity.get("recipients"),
                            "from": entity.get("sender"),
                            "dateTimeSent": entity.get("timestamp"),
                        }
                    )
                elif resp_type == SydneyResponseType.File:
                    result.update(
                        {
                            "title": entity.get("title", f"File {entity_id}"),
                            "snippet": entity.get("description", ""),
                            "author": f'<Person>{entity["author"]["displayName"]}</Person>',
                            "lastModifiedTime": entity.get("lastModifiedDateTime", ""),
                            "fileType": entity.get("fileType", ""),
                            "fileName": entity.get("filename", ""),
                            "lastModifiedBy": entity["lastModifiedBy"]["displayName"],
                            "snippetMetadata": {},
                        }
                    )
                else:
                    continue
                results.append(result)

            yield chat.Message(
                author=chat.Author(role=chat.Role.TOOL, name=self.name),
                content=chat.Text.from_string(json.dumps({"results": results})),
            )
        except Exception as e:
            tb = traceback.format_exc()
            yield self.error_message(f"Failed to call '{self.name}':\n{e}\n{tb}\n")

    async def _process(self, message: chat.Message) -> AsyncIterator[chat.Message]:
        raise NotImplementedError("This is not used by SearchEnterpriseTool")


@chz.chz
class SearchEnterpriseToolConfig(ToolConfig):
    tool_timeout: int = 60 * 60
    sentence_transformer: SentenceTransformerConfig
    entity_file_uri: str

    def get_tool_name(self) -> str:
        return "office365_search"

    def unique_descriptor_for_variants(self) -> str:
        return "office365_search"

    def instruction(self, datapoint: datapoint.HarmonyDatapoint | None = None) -> str:
        return TOOL_INSTRUCTIONS

    def _initialize_dataset(self) -> EnterpriseDataset:
        assert (
            self.entity_file_uri is not None
        ), "SearchEnterpriseToolConfig requires a list of entities in entity_file_uri"
        assert (
            self.sentence_transformer is not None
        ), "SearchEnterpriseToolConfig requires a sentence_transformer"

        dataset = initialize_dataset_from_path(
            entity_file_uri=self.entity_file_uri, sentence_transformer=self.sentence_transformer
        )

        return dataset

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        dataset = self._initialize_dataset()
        tool = SearchEnterpriseTool(enterprise_dataset=dataset)
        return tool
