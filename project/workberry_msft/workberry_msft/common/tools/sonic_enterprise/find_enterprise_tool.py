import json
import logging
import traceback
from typing import Any, AsyncIterator

import chz
import structlog
from chat import chat
from qstar.common import datapoint, types
from qstar.common.tools.berry_tool_interface import <PERSON><PERSON><PERSON>, ToolConfig
from workberry_msft.common.tools.sonic_enterprise.enterprise_dataset import (
    EnterpriseDataset,
    SentenceTransformerConfig,
    get_snippet_from_entity,
    initialize_dataset_from_path,
)
from workberry_msft.common.tools.sonic_enterprise.utils import (
    SonicStyleFindRequest,
    parse_find_requests,
)

logger = structlog.stdlib.get_logger(component="FindEnterpriseToolConfig")


TOOL_INSTRUCTIONS = """
Takes a selected result's index from office365_search and searches within it for the specified patterns.
Usage:
```json
{{  "reference_id": "source `reference_id` from office365_search results", "patterns": ["search_term1", "search_term2"]}}
```

**Usage guidelines**:
- You can use `office365_find` tool to search for content within a search result. Use the `reference_id` of the search result to indicate the targets, and use patterns to specify search terms.
- Look over the search results - if they already give you enough detail, you can skip ahead. If you need to get a focused view of the result in relation to certain terms, call `office365_find`.
""".strip()


class FindEnterpriseTool(BerryTool):
    """A mocked tool for finding passages for patterns within enterprise search results"""

    def __init__(self, enterprise_dataset: EnterpriseDataset = None):
        super().__init__()
        self.enterprise_dataset = enterprise_dataset

    @property
    def name(self) -> str:
        return "office365_find"

    @property
    def is_stateful(self) -> bool:
        return False

    def instruction(self) -> str:
        return TOOL_INSTRUCTIONS

    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        assert self.enterprise_dataset is not None, "enterprise_dataset needs to be set."

        try:
            metadata = sample.gt_datapoint.metadata
            message = sample.conversation.messages[-1]
            logger.info(
                "_process_sample_inner",
                tool_name=self.name,
                metadata=metadata,
                message=message,
            )
            if message.recipient != self.name:
                yield self.error_message(
                    f"Tool {self.name} got a message with recipient {message.recipient}"
                )
                return

            find_request: SonicStyleFindRequest = parse_find_requests(message)
            logger.info("call mock office365_find", reqs=find_request)

            if not find_request:
                yield self.error_message(f"Failed to call '{self.name}'")
                return

            snippets = []
            ref_id = find_request.reference_id
            patterns = find_request.patterns

            try:
                entity = self.enterprise_dataset.get_entity(ref_id)
                for pattern in patterns:
                    snippet = get_snippet_from_entity(entity, pattern)
                    snippets.append(snippet)
            except Exception as e:
                logger.error(f"Failed to find {patterns} in {ref_id}: {e}")

            msg = f"Find results for text: `{patterns}` in {ref_id}\n{f'\n\n'.join(snippets)}"

            yield chat.Message(
                author=chat.Author(role=chat.Role.TOOL, name=self.name),
                content=chat.Text.from_string(json.dumps(msg)),
            )
        except Exception as e:
            tb = traceback.format_exc()
            yield self.error_message(f"Failed to call '{self.name}':\n{e}\n{tb}\n")

    async def _process(self, message: chat.Message) -> AsyncIterator[chat.Message]:
        raise NotImplementedError("This is not used by FindEnterpriseTool")


@chz.chz
class FindEnterpriseToolConfig(ToolConfig):
    tool_timeout: int = 60 * 60
    sentence_transformer: SentenceTransformerConfig
    entity_file_uri: str

    def get_tool_name(self) -> str:
        return "office365_find"

    def unique_descriptor_for_variants(self) -> str:
        return "office365_find"

    def instruction(self, datapoint: datapoint.HarmonyDatapoint | None = None) -> str:
        return TOOL_INSTRUCTIONS

    def _initialize_dataset(self) -> EnterpriseDataset:
        assert (
            self.entity_file_uri is not None
        ), "FindEnterpriseToolConfig requires a list of entities in entity_file_uri"
        assert (
            self.sentence_transformer is not None
        ), "FindEnterpriseToolConfig requires a sentence_transformer"

        dataset = initialize_dataset_from_path(
            entity_file_uri=self.entity_file_uri, sentence_transformer=self.sentence_transformer
        )

        return dataset

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        dataset = self._initialize_dataset()
        tool = FindEnterpriseTool(enterprise_dataset=dataset)
        return tool
