import json
from enum import StrEnum
from pathlib import Path
from typing import Literal, Optional

import blobfile
import structlog
from chat import chat
from chat.render import common as render_common
from pydantic import BaseModel, Field

logger = structlog.stdlib.get_logger(component="sonic_enterprise_tool_utils")


# Synthetic tenant entities segment types
class ToolType(StrEnum):
    People = "person"
    Email = "email"
    Meeting = "meeting"
    Chat = "message"
    File = "file"


# Prod Sydney "response" segment types
class SydneyResponseType(StrEnum):
    People = "PeopleInferenceAnswer"
    Email = "EmailMessage"
    Meeting = "Event"
    Chat = "TeamsMessage"
    File = "File"


# Prod Sydney "request" segment types
class SydneyRequestType(StrEnum):
    People = "people"
    Email = "emails"
    Meeting = "meetings"
    Chat = "chats"
    File = "files"


class SonicStyleSearchRequest(BaseModel):
    domain: str
    filters: dict[str, str]
    query: str
    response_length: Literal["short", "medium", "long"]


class SonicStyleOpenRequest(BaseModel):
    ref_id: str
    lineno: int | None = None


class SonicStyleFindRequest(BaseModel):
    reference_id: str
    patterns: list[str] = Field(default_factory=list)


# Prod Sydney "request" -> Synthetic tenant
DOMAIN_TO_TOOL_TYPE_MAP = {
    SydneyRequestType.Meeting: ToolType.Meeting,
    SydneyRequestType.File: ToolType.File,
    SydneyRequestType.Email: ToolType.Email,
    SydneyRequestType.Chat: ToolType.Chat,
    SydneyRequestType.People: ToolType.People,
}

TOOL_TYPE_TO_SYDNEY_TYPE_MAP = {
    ToolType.Meeting: SydneyResponseType.Meeting,
    ToolType.File: SydneyResponseType.File,
    ToolType.Email: SydneyResponseType.Email,
    ToolType.Chat: SydneyResponseType.Chat,
    ToolType.People: SydneyResponseType.People,
}


def parse_search_requests(message: chat.Message) -> list[SonicStyleSearchRequest]:
    message_text = render_common.render_content(message, msg_idx=0)

    try:
        jmsg = json.loads(message_text)
        if "queries" not in jmsg:
            return []

        queries = jmsg["queries"]
        if not isinstance(queries, list):
            return []

        return [SonicStyleSearchRequest(**query) for query in queries]

    except Exception as e:
        return []


def parse_open_requests(message: chat.Message) -> list[SonicStyleOpenRequest]:
    message_text = render_common.render_content(message, msg_idx=0)

    try:
        jmsg = json.loads(message_text)
        if "results" not in jmsg:
            return []

        results = jmsg["results"]
        if not isinstance(results, list):
            return []

        return [SonicStyleOpenRequest(**result) for result in results]

    except Exception as e:
        return []


def parse_find_requests(message: chat.Message) -> Optional[SonicStyleFindRequest]:
    message_text = render_common.render_content(message, msg_idx=0)

    try:
        jmsg = json.loads(message_text)
        return SonicStyleFindRequest(**jmsg)

    except Exception as e:
        return None


def copy_blob(src: str, dst: str) -> None:
    if not blobfile.isdir(src):
        if not blobfile.exists(dst):
            blobfile.copy(src, dst)
        return
    if not blobfile.exists(dst):
        blobfile.makedirs(dst)
    for file_path in blobfile.listdir(src):
        src_path = blobfile.join(src, file_path)
        dst_path = blobfile.join(dst, file_path)
        copy_blob(src_path, dst_path)
