import json
import logging
import traceback
from typing import Any, AsyncIterator

import chz
import structlog
from chat import chat
from qstar.common import datapoint, types
from qstar.common.tools.berry_tool_interface import <PERSON><PERSON><PERSON>, ToolConfig
from workberry_msft.common.tools.sonic_enterprise.enterprise_dataset import (
    EnterpriseDataset,
    SentenceTransformerConfig,
    entity_to_text,
    get_entity_title,
    initialize_dataset_from_path,
)
from workberry_msft.common.tools.sonic_enterprise.utils import (
    SonicStyleOpenRequest,
    parse_open_requests,
)

logger = structlog.stdlib.get_logger(component="OpenEnterpriseToolConfig")


TOOL_INSTRUCTIONS = """
Takes a selected result's reference_id from office365_search and opens it to get the full content of that search result.
Usage:
```json
{{ "results": [ {{ "ref_id": "source `reference_id` from office365_search results", "lineno": "optional line number to center on." }} ] }}
```

**Usage guidelines**:
- You can use `office365_open` tool to get full the contents of one or more search results. Use the `reference_id` of the search result to indicate the result for which the content needs to be retrieved.
- Look over the search results - if they already give you enough detail, you can skip ahead. If not, pick the most promising results and use `office365_open` to pull in more content.
- Note: You can use `office365_open` to get the content of multiple search results.
""".strip()


class OpenEnterpriseTool(BerryTool):
    """A mocked tool for opening specific enterprise data"""

    def __init__(self, enterprise_dataset: EnterpriseDataset = None):
        super().__init__()
        self.enterprise_dataset = enterprise_dataset

    @property
    def name(self) -> str:
        return "office365_open"

    @property
    def is_stateful(self) -> bool:
        return False

    def instruction(self) -> str:
        return TOOL_INSTRUCTIONS

    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        assert self.enterprise_dataset is not None, "enterprise_dataset needs to be set."

        try:
            metadata = sample.gt_datapoint.metadata
            message = sample.conversation.messages[-1]
            logger.info(
                "_process_sample_inner",
                tool_name=self.name,
                metadata=metadata,
                message=message,
            )
            if message.recipient != self.name:
                yield self.error_message(
                    f"Tool {self.name} got a message with recipient {message.recipient}"
                )
                return

            open_requests: list[SonicStyleOpenRequest] = parse_open_requests(message)
            logger.info("call mock office365_open", reqs=open_requests)

            results = []
            for open_request in open_requests:
                ref_id = open_request.ref_id
                try:
                    entity = self.enterprise_dataset.get_entity(ref_id)
                    title = get_entity_title(entity)
                    content = entity_to_text(entity)
                    entity_type = entity.get("type")
                    results.append(f"[{ref_id}] {entity_type}\n{title}\n{content}")
                except Exception as e:
                    logger.error(f"Failed to open {ref_id}: {e}")
                    continue

            yield chat.Message(
                author=chat.Author(role=chat.Role.TOOL, name=self.name),
                content=chat.Text.from_string(json.dumps("\n\n".join(results))),
            )
        except Exception as e:
            tb = traceback.format_exc()
            yield self.error_message(f"Failed to call '{self.name}':\n{e}\n{tb}\n")

    async def _process(self, message: chat.Message) -> AsyncIterator[chat.Message]:
        raise NotImplementedError("This is not used by OpenEnterpriseTool")


@chz.chz
class OpenEnterpriseToolConfig(ToolConfig):
    tool_timeout: int = 60 * 60
    sentence_transformer: SentenceTransformerConfig
    entity_file_uri: str

    def get_tool_name(self) -> str:
        return "office365_open"

    def unique_descriptor_for_variants(self) -> str:
        return "office365_open"

    def instruction(self, datapoint: datapoint.HarmonyDatapoint | None = None) -> str:
        return TOOL_INSTRUCTIONS

    def _initialize_dataset(self) -> EnterpriseDataset:
        assert (
            self.entity_file_uri is not None
        ), "OpenEnterpriseToolConfig requires a list of entities in entity_file_uri"
        assert (
            self.sentence_transformer is not None
        ), "OpenEnterpriseToolConfig requires a sentence_transformer"

        dataset = initialize_dataset_from_path(
            entity_file_uri=self.entity_file_uri, sentence_transformer=self.sentence_transformer
        )

        return dataset

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        dataset = self._initialize_dataset()
        tool = OpenEnterpriseTool(enterprise_dataset=dataset)
        return tool
