import argparse
import json
import logging

import chat


def validate_jsonl(file_path):
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            for line_number, line in enumerate(file, start=1):
                logging.info("Line number:", line_number)
                try:
                    data_entry = json.loads(line.strip())  # Attempt to parse the line as JSON
                    problem = json.loads(line.strip())["problem"]
                    logging.info(f"Validating a 'problem' json ({len(problem)} characters)")
                    chat.Conversation.model_validate(problem)
                    for message in chat.Conversation(**problem).messages:
                        print(message)
                    print(
                        f"Metadata has entities: {len(data_entry['metadata']['context']['ENTITIES'])}"
                    )

                except json.JSONDecodeError as e:
                    print(f"Invalid JSON at line {line_number}: {e}")
                    return False
        print("The JSONL file is valid.")
        return True
    except FileNotFoundError:
        print("File not found.")
        return False
    except Exception as e:
        print(f"An error occurred: {e}")
        return False


parser = argparse.ArgumentParser(description="Validate a JSONL file.")
parser.add_argument("--jsonl_file_path", type=str, help="Path to the JSONL file.")
args = parser.parse_args()

validate_jsonl(args.jsonl_file_path)