import chz
from mini.finetune.datasets.jsonl import write_jsonl
from rlsnow import reader
from rlsnow.reader_impl import const


def main(
    experiment_ids: tuple[str, ...],
    output_path: str,
    security_profile: str = "msft-orng",
    correct_only: bool = True,
) -> None:
    """
    Extracts samples from a list of berry experiments. Works for run_eval, and run_experiment runs, even if they are still in progress.

    We are mainly interested in the following fields:
    - `prompt`: the prompt used for the sample
    - `conversation`: the conversation used for the sample
    - `metadata_slim`: slimmed down metadata for the sample
    - `grades_serializable`: the grades for the sample, serialized as a JSON object
    - `given_answer`: the answer given by the worker for the sample

    Based on: https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/personal/weijianxu/inference/download_samples_more_fields.py

    Args:
        experiment_ids (tuple[str, ...]): A tuple of experiment IDs to extract samples from, these are the names of experiments in Lemon.
        output_path (str): The path to save the extracted samples in JSONL format.
        security_profile (str): The security profile to use for the Snowflake reader.
        correct_only (bool): Whether to only include correct samples
    """
    r = reader.RLSnowReader(security_profile)
    experiment_selects = []
    for expt_id in experiment_ids:
        shard = 0
        experiment_selects.append(
            f"SELECT * FROM public_se_256.sample_events_s{shard} WHERE base_experiment_id = '{expt_id}'"
        )
    samples_cte = " UNION ALL ".join(experiment_selects)

    samples = r.execute(
        f"""
    WITH expt_samples AS ({samples_cte})
    SELECT
        b.*,
        a.data:prompt::string prompt,
        a.data:conversation::string conversation,
        a.data:metadata_slim metadata_slim,
        a.data:grades_serializable grades_serializable,
        a.data:given_answer::string given_answer
    FROM expt_samples a
    JOIN (
        SELECT DISTINCT
            sample_id,
            data:gt_datapoint_serializable:dataset_id::string dataset_id,
            data:gt_datapoint_serializable:unique_id::string unique_id
        FROM expt_samples
        WHERE event_type = 'driver_upload_sample_batch'
    ) b
    ON a.sample_id = b.sample_id
        AND a.event_type = 'worker_sample_complete'
        { "AND a.data:accuracy = 1.0" if correct_only else "" }
    """,
        parse_json_fields=["prompt", "conversation", "grades_serializable"],
    )

    write_jsonl(output_path, samples)
    print(f"Success: saved {len(samples)} samples to {output_path}")


if __name__ == "__main__":
    chz.entrypoint(main)
