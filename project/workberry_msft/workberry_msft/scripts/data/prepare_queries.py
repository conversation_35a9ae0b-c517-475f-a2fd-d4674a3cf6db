"""Script for converting .yml files with queries and assertions to JSONL format for Q* training.

Sample call:

python -m workberry_msft.scripts.data.prepare_queries \
    input_pattern.0="/home/<USER>/data/SyntheticTenantUserQueries/GreatPlainsManufacturingAccountingServices.yml" \
    output_path="az://orngwus2cresco/data/<YOURALIAS>/qstar/GreatPlainsManufacturingAccountingServices"
    prefix="train_" \
    suffix="_v1"

Example .yml input file format:
- query: "Compile a concise summary of every file, chat, email, or meeting note created since July 25 that mentions either the “varianceThreshold” parameter or the “SmoothingAdjustment” measure, and list any related action items, owners, and deadlines that were recorded."
  segmentOne: "Ask"
  segmentTwo: "Q&A: User Data"
  assertions:
  - text: "Response searches across all user-accessible data sources (files, chats, emails, meeting transcripts, events) dated July 25 or later."
    level: "critical"
  - text: "Response identifies at least the document VarianceThreshold_FallbackLogic_Guide.docx and the Q3 Pivot Refresh chat messages referencing SmoothingAdjustment."
    level: "critical"
  - text: "Response extracts action items with owners and deadlines when such information exists (e.g., pivot refresh deadline Aug 2, upload tasks)."
    level: "expected"
  - text: "Response groups findings into a clear, concise summary rather than listing raw messages."
    level: "expected"
  - text: "Response highlights any gaps or missing owners if referenced in the source material."
    level: "aspirational"
"""

import json
import os
from enum import StrEnum

import blobfile as bf
import chz
import yaml
from workberry_msft.scripts.data.utils import get_path_to_input_files, is_az


class AssertionLevel(StrEnum):
    CRITICAL = "critical"
    EXPECTED = "expected"
    ASPIRATIONAL = "aspirational"


@chz.chz
class PrepareQueriesConfig:
    """Configuration for the queries preparation script."""

    input_pattern: list[str]
    # The input pattern can be:
    # - A path to a directory in that case all yml files will be considered as input
    # - A path to one or more yml files in that case those file(s) will be used as input

    output_path: str
    # The output path can be:
    # - A path to a directory
    # - A path to an "az://" path

    prefix: str = ""
    # Optional prefix to add to the beginning of each output file (e.g., "train_")

    suffix: str = ""
    # Optional suffix to add to the end of each output file (e.g., "_v1")


def convert_queryset_yaml_to_examples(yaml_file_path: str):
    with open(yaml_file_path, "r") as file:
        yaml_data = yaml.safe_load(file)

    examples = []
    for entry in yaml_data:
        query = entry.get("query")
        assertions = []
        for assertion in entry.get("assertions", []):
            assertion_text = assertion.get("text")
            if not assertion_text:
                continue
            assertions.append(
                {
                    "text": assertion_text,
                    "level": assertion.get("level", AssertionLevel.ASPIRATIONAL),
                }
            )

        if not query or not assertions:
            continue
        example = {
            "problem": query,
            "answer": "",
            "metadata": {
                "segmentOne": entry.get("segmentOne", ""),
                "segmentTwo": entry.get("segmentTwo", ""),
                "assertions": assertions,
            },
        }
        examples.append(example)

    return examples


def main(config: PrepareQueriesConfig) -> None:
    input_files = get_path_to_input_files(config.input_pattern, ".yml")
    print(f"Found {len(input_files)} input files.")
    print(f"Input files: {input_files}")

    if not is_az(config.output_path):
        os.makedirs(config.output_path, exist_ok=True)

    for input_file in input_files:
        try:
            examples = convert_queryset_yaml_to_examples(input_file)
            print(f"Converted {input_file} to {len(examples)} examples")

            out_fp = os.path.join(
                config.output_path,
                f"{config.prefix}{os.path.basename(input_file).replace('.yml', '')}{config.suffix}.jsonl",
            )
            if is_az(config.output_path):
                with bf.BlobFile(out_fp, "w") as f:
                    for example in examples:
                        f.write(json.dumps(example, ensure_ascii=False) + "\n")
            else:
                with open(out_fp, "w") as f:
                    for example in examples:
                        f.write(json.dumps(example, ensure_ascii=False) + "\n")

            print(f"Wrote examples to {out_fp}")
        except Exception as e:
            print(f"Error processing {input_file}: {e}")


if __name__ == "__main__":
    chz.nested_entrypoint(main)
