import os


def is_az(path: str) -> bool:
    """Check if the path is an Azure Blob Storage path."""
    return path.startswith("az://")


def is_dir(path: str) -> bool:
    """Check if the path is a directory."""
    return os.path.isdir(path)


def is_file(path: str) -> bool:
    """Check if the path is a file."""
    return os.path.exists(path) and not is_dir(path)


def get_path_to_input_files(input_pattern: str, extension: str) -> list[str]:
    input_files = []
    for input_dir_or_file in input_pattern:
        if is_dir(input_dir_or_file):
            input_files.extend(
                [
                    os.path.join(input_dir_or_file, f)
                    for f in os.listdir(input_dir_or_file)
                    if f.endswith(extension)
                ]
            )
        elif os.path.isfile(input_dir_or_file) and input_dir_or_file.endswith(extension):
            input_files.append(input_dir_or_file)
    return input_files
