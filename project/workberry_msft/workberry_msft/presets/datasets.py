from typing import Any, Callable, Dict, List

import structlog
from berry import preset_utils

logger = structlog.stdlib.get_logger(component=__name__)

"""
Home for presets that relate to datasets, and corresponding grader and tool dependencies
"""

SYSTEM_IDENTITY = f"""
You are M365 Smart Enterprise Browser, created by Microsoft.
Knowledge cutoff: 2024-06
Current date: Thu, 04 Sep 2025 09:01:41 GMT-07:00
Location: The user is located in REDMOND.User's language may differ from the language commonly spoken in their location.

Your task is to provide thorough, accurate, and comprehensive responses to user queries by conducting efficient browsing of the user's enterprise data. Use `office365_search`, `office365_open` and `office365_find` to investigate and gather detailed grounding data, then distill all collected information into a comprehensive response.

Break down complex questions and try to research or gather data with a step-by-step solution.
**MAKE SURE to do a secondary consistency check on all your tool outputs before responding to the user, and if necessary, call failed tools again.**

## Enterprise Tools
You have access to enterprise tools that connect directly to the user's organizational data and systems. They provide contextually relevant, organization-specific information that aligns with company policies, procedures, and current activities.

# General guidelines
If you search, you MUST CITE AT LEAST ONE OR TWO SOURCES per statement (this is EXTREMELY important).
If the user explicitly asks for in-depth analysis of a topic that needs search, this means they want at least 700 words, thorough, diverse citations (at least 2 per paragraph), and a perfectly structured answer using markdown, unless otherwise asked.
For time-related and meeting queries, prioritize more recent events, ensuring you compare publish dates and the date that the event happened.

Very important: The user's timezone is given in standard GMT format. The current date is Thu, 04 Sep 2025 09:01:41 GMT-07:00. Any dates before this are in the past, and any dates after this are in the future. When dealing with modern entities/companies/people, and the user asks for the 'latest', 'most recent', 'today's', etc., don't assume your knowledge is up to date; you MUST carefully confirm what the *true* 'latest' is first. If the user seems confused about a date, you MUST include specific, concrete dates in your response (e.g. 'January 1, 2010')

<system-reminder>
IMPORTANT: This is a reminder that you are in an enterprise setting. Even if the user's request sounds familiar, you **must always** invoke the search tool to ensure that there are no acronyms, code names, or topics that have a different meaning within the enterprise.
</system-reminder>


# Begin!
Now, given the conversation context below with a new user, consider your next action.
- Time at the start of this conversation is Thu, 04 Sep 2025 09:01:41 GMT-07:00. The user is located in REDMOND.

The Yap score measures verbosity; aim for responses at most Yap words long. Overly verbose responses when Yap is low (or overly terse when Yap is high) may be penalized. Today's Yap score is **2048**.
Juice indicates the reasoning effort. Use juice: 64
"""

CLUSTER_MAP = {
    "uksouth_7": "orngcresco",
    "wus2_19": "orngwus2cresco",
    "scus_hpe_4": "orngscuscresco",
}
CLUSTER_TO_PROFILE_MAP = {
    "uksouth_7": "msft-orng",
    "wus2_19": "msft-orng",
    "scus_hpe_4": "msft-orng",
}

DATASET_MAP = {
    "ProjectsTiny": "data.researcher.projects.train_tiny_20250702",
    "ProjectsSmall": "data.zhongfu.researcher.train_20250629_small",
    "ProjectsMedium": "data.zhongfu.researcher.train_20250722",
    "VertexEdgeLabsV1": "data.joclausm.researcher.train_vertexedgelabs_v1",
    "VertexEdgeLabsSimple": "data.raymondfok.qstar.vertexedgelabs_dp_simple",
    "GreatPlainsV1": "data.raymondfok.qstar.GreatPlainsManufacturingAccountingServices",
}

GRADER_BUS_MODEL_O3_MINI = "az://orngcresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-********-decrypted"
GRADER_BUS_MODEL_O3_MINI_RENDERER = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"
GRADER_BUS_USER = "researcher"


def get_grader_map(
    cluster: str,
) -> Dict[str, List[str]]:
    return get_single_stage_grader_map(cluster)


def get_single_stage_grader_map(
    cluster: str,
) -> Dict[str, List[str]]:
    """
    Returns a map of single-stage graders with their configuration arguments.
    """
    return {
        "AcceptsAllWithToolGrader": [
            # Grader that accepts all solutions if there's tool call in the conversation.
            "grader=workberry_msft.graders.accepts_all_tool_grader:AcceptsAllWithToolGrader"
        ],
        "RougeGrader": [
            "grader=workberry_msft.graders.rouge_grader:RougeGrader",
            "grader.score_threshold=0.25",
        ],
        "ProjectReportGrader": [
            "grader=researcher_msft.graders.project_report_grader:ProjectReportGrader",
            f"grader.model=az://{cluster}/models/snapshots/4o-tt-v5-transferred-2025-04-09-decrypted",
            "grader.score_threshold=0.6",
            "grader.bus_user=researcher",
        ],
    }


def get_entities_file_uri(cluster: str, dataset_tag: str) -> str:
    if dataset_tag in ["VertexEdgeLabsV1", "VertexEdgeLabsSimple"]:
        return f"az://{cluster}/data/joclausm/researcher/entities_vertexedgelabs_v1/VertexEdgeLabs_entities.json"
    elif dataset_tag == "GreatPlainsV1":
        return f"az://{cluster}/data/raymondfok/synthetic_entities/GreatPlainsManufacturingAccountingServices.json"
    else:
        return ""


def get_sentence_transformer_model_path(cluster: str) -> str:
    return f"az://{cluster}/data/joclausm/models--sentence-transformers--all-MiniLM-L6-v2"


def get_tools_map(cluster: str, dataset_tag: str) -> Dict[str, List[str]]:
    entities_file_uri = get_entities_file_uri(cluster, dataset_tag)
    sentence_transformer_model_path = get_sentence_transformer_model_path(cluster)
    return {
        "DummyTools": [
            "tool_configs.0=workberry_msft.common.tools.dummy.dummy_search_web_tool:DummySearchWebToolConfig",
        ],
        "EnterpriseBrowserTools": [
            "tool_configs.0=workberry_msft.common.tools.enterprise.enterprise_browser_tool:EnterpriseBrowserToolConfig",
            f"tool_configs.0.sentence_transformer.model_path={sentence_transformer_model_path}",
            f"tool_configs.0.entity_file_uri={entities_file_uri}",
        ],
        "SonicStyleEnterpriseBrowserTools": [
            "tool_configs.0=workberry_msft.common.tools.sonic_enterprise.search_enterprise_tool:SearchEnterpriseToolConfig",
            f"tool_configs.0.sentence_transformer.model_path={sentence_transformer_model_path}",
            f"tool_configs.0.entity_file_uri={entities_file_uri}",
            "tool_configs.1=workberry_msft.common.tools.sonic_enterprise.open_enterprise_tool:OpenEnterpriseToolConfig",
            f"tool_configs.1.sentence_transformer.model_path={sentence_transformer_model_path}",
            f"tool_configs.1.entity_file_uri={entities_file_uri}",
            "tool_configs.2=workberry_msft.common.tools.sonic_enterprise.find_enterprise_tool:FindEnterpriseToolConfig",
            f"tool_configs.2.sentence_transformer.model_path={sentence_transformer_model_path}",
            f"tool_configs.2.entity_file_uri={entities_file_uri}",
        ],
    }


def _make_presets(
    preset_fn: Callable[[List[str]], Any],
    key_prefix: str,
    preset_prefix_str: str = "",
    extra_args: List[str] = [],
) -> Dict[str, Any]:
    return {
        f"{key_prefix}_{cluster_tag}_{dataset_tag}_{tool_tag}_{grader_tag}": preset_fn(
            [
                f"{preset_prefix_str}model_identity_str={SYSTEM_IDENTITY}",
                f"{preset_prefix_str}dataset_container={CLUSTER_MAP[cluster_tag]}",
                f"{preset_prefix_str}dataset_id={DATASET_MAP[dataset_tag]}",
                *extra_args,
                *[
                    f"{preset_prefix_str}{cfg}"
                    for cfg in get_tools_map(CLUSTER_MAP[cluster_tag], dataset_tag=dataset_tag)[
                        tool_tag
                    ]
                ],
                *[
                    f"{preset_prefix_str}{cfg}"
                    for cfg in get_grader_map(CLUSTER_MAP[cluster_tag])[grader_tag]
                ],
            ]
        )
        for cluster_tag in CLUSTER_MAP
        for dataset_tag in DATASET_MAP
        for tool_tag in get_tools_map(CLUSTER_MAP[cluster_tag], dataset_tag=dataset_tag)
        for grader_tag in get_grader_map(CLUSTER_MAP[cluster_tag])
    }


TRAINING_DATASET_PRESETS = _make_presets(
    preset_utils.training_dataset_preset,
    key_prefix="tr",
)

EVAL_DATASET_PRESETS = _make_presets(
    preset_utils.eval_dataset_preset,
    key_prefix="eval",
    preset_prefix_str="dataset.",
    extra_args=[
        "dataset.override_target_samples_per_instance=4",
    ],
)

globals().update(TRAINING_DATASET_PRESETS)
globals().update(EVAL_DATASET_PRESETS)
