from itertools import product

import berry.preset_utils
import structlog
from workberry_msft.presets.datasets import CLUSTER_MAP, CLUSTER_TO_PROFILE_MAP

logger = structlog.stdlib.get_logger(component=__name__)

MODEL_MAP = {
    "o3_mini": "models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted/",
    "o4_mini": "oseries-nv4-cbv5-0331-step350-decrypted/",
    "gpt5_nano": "gpt5n-t7-resume2-s170-2025-08-04-21-13-decrypted/",
    "gpt5_mini": "zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted/",
    "sonicberry": "sonicberry_20250516-1-decrypted/",
}

peashooter_preset = berry.preset_utils.args_preset(
    # Mostly the default Peashooter settings, but with some tweaks for performance we've seen in other jobs.
    [
        "peashooter.kv_spilling=False",  # Disable spilling KVs to CPU. This should lower latency since keeping KVs in GPU memory eliminates the transfer overhead, at a cost of higher memory usage.
        "peashooter.timeout_seconds.stalled_datapoint=3600",  # Timeout for stalled datapoints. (default 14400 seconds)
        "peashooter.num_instance_workers=64",  # Defines number of processes to schedule instance-related work. (default 32)
        "peashooter.timeout_seconds.pause_sampling_after_controller_crash=1800",  # Pause sampling for 30 minutes if controller crashes. (default 3600)
        "peashooter.timeout_seconds.pause_sampling_if_training_n_steps_behind=4",  # Pause sampling if training is more than 4 steps behind.
        "peashooter.delegation_concurrency_multiple=2.0",  # Defines how many sampling processes can be delegated to each instance worker. (default 1.0)
    ]
)

sampling_preset = berry.preset_utils.args_preset(
    [
        # Defines the number of batches to keep rolling out at any given time. (Default 30) Larger values increase sampling throughput at the cost of more memory.
        # I don't think the sampling is the bottleneck in our case, so we can afford to lower this.
        "batch_completer.n_batches_in_flight=16",
        # The number of "successful" (information content > 0) instances to aim for to reach information_per_batch in each training batch. (Default 64)
        # During sampling, the batch completer sums the information content of completed instances until it reaches this target. This value controls your effective batch size in terms of
        # datapoints covered. If you set it too low you'll get very small batches; if you set it high the system will hold batches open until enough instances complete to meet the quota.
        "batch_completer.instances_per_batch=16",
        "defaults.instances_per_batch=16",
        # Number of positive samples we want to get per instance.
        "...instance_completer.target_samples_per_instance=8",
        "defaults.target_samples_per_instance=8",
    ]
)

policy_preset = berry.preset_utils.args_preset(
    [
        "policy.sampling_ml_config=ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=128 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True ixf_sampling_extension_gpu_to_cpu_async=True",
        # policy.n_ctx should always be multiple of 1024 (for systems reasons), sampling_n_ctx can be whatever
        "policy.n_ctx=131072",
        # default n_ctx also sets the sampling n_ctx, which defined as maximum context length sampler can even see. It sets max tokens to n_ctx - prompt len.
        "defaults.n_ctx=131072",
        # Each datapoint in the underlying dataset will be staged multiple times, as specified by the `num_epochs` parameter.
        "berry_curriculum=berry_curriculums.MultiEpochCurriculum",
        "berry_curriculum.max_epochs=2",
    ]
)

MODEL_PRESETS = {
    f"{cluster_tag}_{model_tag}": berry.preset_utils.compose_presets(
        peashooter_preset,
        berry.preset_utils.args_preset(
            [
                f"policy.initial_checkpoint=az://{CLUSTER_MAP[cluster_tag]}/models/snapshots/{MODEL_MAP[model_tag]}",
                "root_config=mini.root.dev driver_rpc_timeout=6000 init_actors_rpc_timeout=6000 poll_for_error_timeout=6000 dedicated_driver_node=False",
                f"security_profile={CLUSTER_TO_PROFILE_MAP[cluster_tag]}",
            ]
        ),
        sampling_preset,
        policy_preset,
    )
    for cluster_tag, model_tag in product(
        CLUSTER_MAP.keys(),
        MODEL_MAP.keys(),
    )
}
globals().update(MODEL_PRESETS)
