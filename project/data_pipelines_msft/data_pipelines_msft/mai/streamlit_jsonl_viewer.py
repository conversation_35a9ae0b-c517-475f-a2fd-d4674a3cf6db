import streamlit as st
import json
from ruamel.yaml import <PERSON><PERSON><PERSON>
from ruamel.yaml.scalarstring import LiteralScalarString
import blobfile as bf
import io
import zstandard as zstd
import gzip

# Configure Streamlit to use wide mode
st.set_page_config(page_title="JSONL File Viewer", layout="wide")

# Set up YAML configuration
yaml = YAML()
yaml.indent(mapping=4, sequence=4, offset=2)

def multiline_to_literal(obj):
    if isinstance(obj, str):
        # First try to parse as JSON if it looks like a JSON object or array
        if (obj.startswith('{') or obj.startswith('[')) and len(obj) > 1:
            try:
                # Convert the JSON string to a Python object
                parsed_obj = json.loads(obj)
                # Process the parsed object instead of the string
                return multiline_to_literal(parsed_obj)
            except json.JSONDecodeError:
                # Not valid JSON, continue with string processing
                pass
                
        # Now handle the string itself (if JSON parsing didn't succeed)
        if '\n' in obj:
            # Strings with actual newlines should be literal blocks
            return LiteralScalarString(obj)
        elif len(obj) > 80:
            # Long strings look better as literal blocks too
            return LiteralScalarString(obj)
        else:
            # Short strings without newlines stay as regular strings
            return obj
    elif isinstance(obj, dict):
        return {k: multiline_to_literal(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [multiline_to_literal(i) for i in obj]
    else:
        return obj

def open_file_with_decompression(file_path):
    """Open a file with appropriate decompression based on file extension."""
    if file_path.endswith('.jsonl.zst'):
        # Handle zstandard compressed files
        dctx = zstd.ZstdDecompressor()
        # Open the compressed file as binary
        with bf.BlobFile(file_path, 'rb') as compressed_file:
            compressed_data = compressed_file.read()
            
            # Try different decompression methods
            try:
                # First try regular decompression
                decompressed_data = dctx.decompress(compressed_data).decode('utf-8')
                return io.StringIO(decompressed_data)
            except Exception as first_error:
                try:
                    # If that fails, try streaming decompression
                    # This handles files without content size in header
                    decompressed_chunks = []
                    stream_reader = dctx.stream_reader(io.BytesIO(compressed_data))
                    while True:
                        chunk = stream_reader.read(8192)  # Read in 8KB chunks
                        if not chunk:
                            break
                        decompressed_chunks.append(chunk)
                    
                    decompressed_data = b''.join(decompressed_chunks).decode('utf-8')
                    return io.StringIO(decompressed_data)
                except Exception as second_error:
                    try:
                        # Third attempt: try with different decompressor settings
                        dctx_alt = zstd.ZstdDecompressor(max_window_size=2**31-1)
                        decompressed_data = dctx_alt.decompress(compressed_data).decode('utf-8')
                        return io.StringIO(decompressed_data)
                    except Exception as third_error:
                        # Provide detailed error information
                        file_size = len(compressed_data)
                        header_info = compressed_data[:16].hex() if len(compressed_data) >= 16 else compressed_data.hex()
                        raise Exception(
                            f"Failed to decompress zstandard file '{file_path}' "
                            f"(size: {file_size} bytes, header: {header_info}). "
                            f"Attempts: 1) {str(first_error)[:100]}... "
                            f"2) {str(second_error)[:100]}... "
                            f"3) {str(third_error)[:100]}..."
                        )
    elif file_path.endswith('.jsonl.gz'):
        # Handle gzip compressed files
        # Open the compressed file as binary
        with bf.BlobFile(file_path, 'rb') as compressed_file:
            # Read and decompress the entire file
            try:
                compressed_data = compressed_file.read()
                decompressed_data = gzip.decompress(compressed_data).decode('utf-8')
                # Return as StringIO for line-by-line reading
                return io.StringIO(decompressed_data)
            except Exception as e:
                raise Exception(f"Failed to decompress gzip file: {str(e)}")
    else:
        # Handle regular uncompressed files
        return bf.BlobFile(file_path, 'r')

def read_jsonl_line(file_path, line_number):
    """Read a specific line from a JSONL file without loading the entire file."""
    try:
        with open_file_with_decompression(file_path) as f:
            for i, line in enumerate(f):
                if i == line_number:
                    return json.loads(line.strip())
        return None  # Line number out of range
    except Exception as e:
        st.error(f"Error reading file: {str(e)}")
        return None

def check_line_exists(file_path, line_number):
    """Check if a specific line exists in the file."""
    try:
        with open_file_with_decompression(file_path) as f:
            for i, line in enumerate(f):
                if i == line_number:
                    return True
        return False
    except Exception as e:
        return False

def get_nested_value(data, path):
    """Get a nested value from a dictionary using a path like 'chat_context/turns/0/message'."""
    if not path:
        return None
    
    keys = path.split('/')
    current = data
    
    try:
        for key in keys:
            if isinstance(current, dict):
                current = current[key]
            elif isinstance(current, list):
                # Try to convert key to integer for list indexing
                try:
                    index = int(key)
                    current = current[index]
                except (ValueError, IndexError):
                    return None
            else:
                return None
        return current
    except (KeyError, TypeError):
        return None

def get_wildcard_values(data, path):
    """Get all nested values matching a path with wildcards (*) like 'chat_context/turns/*/message'."""
    if not path:
        return []
    
    keys = path.split('/')
    results = []
    
    def _traverse(current_data, key_index, current_path=""):
        if key_index >= len(keys):
            # We've processed all keys, add this value to results
            # Accept any non-dict, non-list values (strings, numbers, booleans, etc.)
            if not isinstance(current_data, (dict, list)):
                results.append({
                    'path': current_path.rstrip('/'),
                    'value': current_data
                })
            return
        
        key = keys[key_index]
        
        if key == '*':
            # Wildcard - iterate through all items
            if isinstance(current_data, dict):
                for dict_key in current_data.keys():
                    new_path = f"{current_path}{dict_key}/"
                    _traverse(current_data[dict_key], key_index + 1, new_path)
            elif isinstance(current_data, list):
                for i, item in enumerate(current_data):
                    new_path = f"{current_path}{i}/"
                    _traverse(item, key_index + 1, new_path)
        else:
            # Specific key
            try:
                if isinstance(current_data, dict) and key in current_data:
                    new_path = f"{current_path}{key}/"
                    _traverse(current_data[key], key_index + 1, new_path)
                elif isinstance(current_data, list):
                    index = int(key)
                    if 0 <= index < len(current_data):
                        new_path = f"{current_path}{index}/"
                        _traverse(current_data[index], key_index + 1, new_path)
            except (ValueError, KeyError, IndexError):
                pass
    
    _traverse(data, 0)
    return results

def fix_math_delimiters(text):
    r"""Convert LaTeX math delimiters from \( \) to $ $ and \[ \] to $$ $$ for better Streamlit rendering."""
    if not isinstance(text, str):
        return text
    
    # Replace \( with $ and \) with $ (inline math)
    text = text.replace('\\(', '$')
    text = text.replace('\\)', '$')
    
    # Replace \[ with $$ and \] with $$ (display math)
    text = text.replace('\\[', '$$')
    text = text.replace('\\]', '$$')
    
    return text

def list_azure_files(blob_path):
    """List files in an Azure blob storage path."""
    try:
        # Use blobfile to list files
        files = list(bf.listdir(blob_path))
        # Filter for .jsonl, .jsonl.gz, and .jsonl.zst files
        jsonl_files = [f for f in files if f.endswith('.jsonl') or f.endswith('.jsonl.gz') or f.endswith('.jsonl.zst')]
        return sorted(jsonl_files)
    except Exception as e:
        st.error(f"Error listing files from {blob_path}: {str(e)}")
        return []

def main():
    st.title("JSONL File Viewer")
    st.markdown("View JSONL files row by row as formatted YAML (supports .jsonl, .jsonl.gz, .jsonl.zst)")
    
    # Sidebar for YAML path input
    with st.sidebar:
        st.header("Markdown View Settings")
        yaml_path = st.text_input(
            "YAML Paths to show as Markdown (comma-separated):", 
            value="chat_context/turns/*/message,chat_context/turns/*/annotations/*/groundtruth,chat_context/turns/*/annotations/*/pass_rate,chat_context/turns/*/annotations/*/responses/*",
            placeholder="e.g., chat_context/turns/*/message,problem",
            help="Use '/' to separate nested keys, numbers for list indices, '*' for wildcards, and ',' to separate multiple paths"
        )
        st.markdown("**Examples:**")
        st.code("chat_context/turns/0/message", language="text")
        st.code("chat_context/turns/*/message", language="text")
        st.code("chat_context/turns/*/annotations/*/responses/*", language="text")
        st.code("problem,solution,answer", language="text")
    
    # File input with Azure file picker
    st.subheader("File Selection")
    st.markdown("**Supported formats:** `.jsonl`, `.jsonl.gz`, `.jsonl.zst`")
    
    # Option to use Azure file picker or manual input
    use_picker = st.checkbox("Browse Azure files", value=True)
    
    if use_picker:
        # Allow custom Azure base path
        default_path = "az://oaiteammoonfiregenaisout/datasets/berry_data/mai_verifiable_data/cots/"
        azure_base_path = st.text_input(
            "Azure base path:", 
            value=default_path,
            help="Enter an Azure blob storage path to browse for JSONL files"
        )
        
        if azure_base_path:
            # List files from Azure
            cache_key = f'azure_files_{hash(azure_base_path)}'
            if cache_key not in st.session_state:
                with st.spinner(f"Loading files from {azure_base_path}..."):
                    st.session_state[cache_key] = list_azure_files(azure_base_path)
            
            azure_files = st.session_state[cache_key]
            if azure_files:
                selected_file = st.selectbox(
                    "Select a JSONL file:",
                    azure_files,
                    help=f"Files from {azure_base_path}"
                )
                jsonl_file = azure_base_path + selected_file if selected_file else ""
            else:
                st.warning(f"No JSONL files found in {azure_base_path}")
                jsonl_file = ""
                
            # Refresh button
            if st.button("🔄 Refresh file list"):
                st.session_state[cache_key] = list_azure_files(azure_base_path)
                st.rerun()
        else:
            jsonl_file = ""
    else:
        # Manual file path input
        jsonl_file = st.text_input(
            "Enter JSONL file path:", 
            placeholder="az://bucket/path/to/file.jsonl.zst or local/path/file.jsonl",
            help="Supports .jsonl, .jsonl.gz, and .jsonl.zst files"
        )
    
    if not jsonl_file:
        st.info("Please enter a JSONL file path to begin.")
        return
    
    # Initialize session state
    if 'current_row' not in st.session_state:
        st.session_state.current_row = 0
    
    if st.session_state.get('current_file') != jsonl_file:
        st.session_state.current_file = jsonl_file
        st.session_state.current_row = 0
    
    # Navigation controls
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col1:
        if st.button("← Previous", disabled=st.session_state.current_row == 0):
            st.session_state.current_row = max(0, st.session_state.current_row - 1)
            st.rerun()
    
    with col2:
        st.markdown(f"**Row {st.session_state.current_row + 1}**")
        
        # Row number input for jumping to specific row
        new_row = st.number_input(
            "Jump to row:", 
            min_value=1, 
            value=st.session_state.current_row + 1,
            key="row_input"
        ) - 1
        
        if new_row != st.session_state.current_row:
            st.session_state.current_row = new_row
            st.rerun()
    
    # Check if next row exists before enabling Next button
    next_row_exists = check_line_exists(jsonl_file, st.session_state.current_row + 1)
    
    with col3:
        if st.button("Next →", disabled=not next_row_exists):
            st.session_state.current_row += 1
            st.rerun()
    
    # Load and display current row
    current_data = read_jsonl_line(jsonl_file, st.session_state.current_row)
    
    if current_data is None:
        st.error("Unable to load the current row.")
        return
    
    # Process data through multiline_to_literal
    processed_data = multiline_to_literal(current_data)
    
    # Convert to YAML string
    yaml_buffer = io.StringIO()
    yaml.dump(processed_data, yaml_buffer)
    yaml_string = yaml_buffer.getvalue()
    
    # Create two columns for YAML and markdown view
    col_yaml, col_markdown = st.columns([1, 1])
    
    with col_yaml:
        # Display YAML
        st.subheader("YAML Representation")
        st.code(yaml_string, language="yaml")
        
        # Optional: Show raw JSON for comparison
        if st.checkbox("Show raw JSON"):
            st.subheader("Raw JSON")
            st.json(current_data)
    
    with col_markdown:
        if yaml_path:
            st.subheader(f"Markdown View")
            
            # Split paths by comma and process each
            yaml_paths = [path.strip() for path in yaml_path.split(',') if path.strip()]
            
            for path_idx, single_path in enumerate(yaml_paths):
                st.markdown(f"### Path {path_idx + 1}: `{single_path}`")
                
                # Check if path contains wildcards
                if '*' in single_path:
                    # Get all matching values
                    wildcard_results = get_wildcard_values(current_data, single_path)
                    
                    if wildcard_results:
                        for i, result in enumerate(wildcard_results):
                            with st.expander(f"Match {i+1}: `{result['path']}`", expanded=True):
                                if isinstance(result['value'], str):
                                    # Fix math delimiters before rendering as markdown
                                    fixed_content = fix_math_delimiters(result['value'])
                                    st.markdown(fixed_content)
                                else:
                                    st.code(str(result['value']), language="text")
                    else:
                        st.warning(f"No matches found for wildcard path `{single_path}`")
                else:
                    # Single path - use existing logic
                    markdown_content = get_nested_value(current_data, single_path)
                    
                    if markdown_content is not None:
                        if isinstance(markdown_content, str):
                            # Fix math delimiters before rendering as markdown
                            fixed_content = fix_math_delimiters(markdown_content)
                            st.markdown(fixed_content)
                        else:
                            # If it's not a string, show it as code
                            st.code(str(markdown_content), language="text")
                    else:
                        st.warning(f"Path `{single_path}` not found in current row")
                
                # Add separator between paths (except for the last one)
                if path_idx < len(yaml_paths) - 1:
                    st.divider()
        else:
            st.info("Enter YAML paths in the sidebar to view content as markdown")
            st.markdown("**Tip:** Use `*` as wildcards and `,` to separate multiple paths!")
            st.markdown("Example: `chat_context/turns/*/message,problem,solution`")

if __name__ == "__main__":
    main()
