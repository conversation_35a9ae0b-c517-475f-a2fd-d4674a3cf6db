import traceback
from .jsonl_iterator import jsonl_iterator
from collections import defaultdict
from bs4 import BeautifulSoup
from tqdm import tqdm
import json
import os, re

class QA_Pairer():

    def __init__(self, json_path, name=None, out_folder="out", min_score=3, max_responses=3, out_format="jsonl", archiver=None):
        """Makes a text dataset from StackExchange dumps"""
        self.json_path = json_path
        if name is None:
            self.name = os.path.dirname(json_path).replace("dumps/", "")
        else:
            self.name = name
        self.category = self.name.replace(".meta.stackexchange", "")
        # dict to save questions
        self.questions = defaultdict(lambda: None, {})
        # folder to save txt files to
        self.out_folder = out_folder
        # min_score required to parse an answer
        self.min_score = min_score
        self.max_responses = max_responses
        self.should_zip = True if archiver is not None else False
        assert out_format == "jsonl" 
        self.out_format = out_format

        self.jsonl_file_path = os.path.join(out_folder, f"{self.name}.jsonl")
        # Create/clear the file
        with open(self.jsonl_file_path, 'w', encoding='utf-8') as f:
            pass

    def main(self):
        """iterates through SE jsons and:

        - stores PostTypeId="1" with AcceptedAnswerIds / Answers.
        - when an AcceptedAnswerId or Answer > min_score is reached, it should:
            > concat the Question & Accepted answer
            > Clean markup / HTML
            > Output to txt file
            > Delete from memory

        """
        os.makedirs(self.out_folder, exist_ok=True)
        iterator = jsonl_iterator(self.json_path, encoding='utf-8-sig')
        for attribs in tqdm(iterator, desc="Parsing {} json file".format(self.name)):
            try:
                if is_question(attribs):
                    if has_answers(attribs):
                        trim_attribs(attribs, "question")
                        self.questions[attribs["Id"]] = attribs
                    else:
                        # if the question has no answers, discard it
                        continue
                elif is_answer(attribs):
                    # if is accepted answer, append answer Body to relevant questions "AcceptedAnswer" field
                    # if the answer's score > min_score
                    # append the answer to the relevant question's OtherAnswers dict
                    self.add_answer(attribs)
                    self.check_complete(attribs)
            except:
                traceback.print_exc()

    def is_above_threshold(self, a_attribs):
        """
        Determines whether an answer is above the min_score threshold

        :param a_attribs: Answer's attribute dict
        :return:
        """
        assert is_answer(a_attribs), "Must be an answer to be above threshold"
        if a_attribs["Score"] is not None:
            if int(a_attribs["Score"]) >= self.min_score:
                return True
        return False

    def add_answer(self, a_attribs):
        """
        Adds answer to its parent question in self.questions if it's either an accepted answer or above self.min_score.
         If answer is an accepted answer, it gets appended to the AcceptedAnswer field, otherwise it gets appended to
         OtherAnswers.

         Also increments the question's 'ParsedAnswers' field. When ParsedAnswers = AnswerCount, the question is deleted
         from memory and saved to a text file.

        :param a_attribs: Answer's attribute dict
        """
        assert is_answer(a_attribs), "Must be an answer to add to parent"
        if a_attribs is not None and self.questions[a_attribs["ParentId"]] is not None:
            if is_accepted_answer(a_attribs, self.questions[a_attribs["ParentId"]]):
                self.questions[a_attribs["ParentId"]]["Answers"][a_attribs["Id"]] = trim_attribs(a_attribs, "answer")
                self.questions[a_attribs["ParentId"]]["ParsedAnswers"] += 1
            elif self.is_above_threshold(a_attribs):
                if a_attribs["Id"] is not None:
                    parent = self.questions[a_attribs["ParentId"]]
                    if parent is not None:
                        self.questions[a_attribs["ParentId"]]["Answers"][a_attribs["Id"]] = trim_attribs(a_attribs, "answer")
                        self.questions[a_attribs["ParentId"]]["ParsedAnswers"] += 1
                else:
                    self.questions[a_attribs["ParentId"]]["ParsedAnswers"] += 1
            else:
                self.questions[a_attribs["ParentId"]]["ParsedAnswers"] += 1

    def check_complete(self, a_attribs):
        """
        checks if the parent question of the previously added answer has no future answers, and if so,
        removes from dict and prints to file.
        """
        keys_to_del = []
        parent = self.questions[a_attribs["ParentId"]]
        if a_attribs is not None and parent is not None:
            if parent["AnswerCount"] is not None and parent["ParsedAnswers"] is not None:
                if int(parent["ParsedAnswers"]) == int(parent['AnswerCount']):
                    keys_to_del.append(a_attribs["ParentId"])
                    if parent["Answers"] is not None and len(parent["Answers"]) > 0:
                        out_name = "{}_{}.txt".format(self.name, str(parent["Id"]).zfill(10))
                        out_str = ""
                        
                        # Create new JSON format with cleaned text
                        json_output = {
                            "question": {
                                "Id": parent["Id"],
                                "AcceptedAnswerId": parent.get("AcceptedAnswerId"),
                                "CreationDate": parent.get("CreationDate"),
                                "ClosedDate": parent.get("ClosedDate"),
                                "Score": parent.get("Score"),
                                "ViewCount": parent.get("ViewCount"),
                                "Body": None,
                                "LastEditDate": parent.get("LastEditDate"),
                                "LastActivityDate": parent.get("LastActivityDate"),
                                "Title": None,
                                "Tags": None,
                                "AnswerCount": parent.get("AnswerCount"),
                                "CommentCount": parent.get("CommentCount"),
                                "ContentLicense": parent.get("ContentLicense"),
                                "ImageWasStripped": parent.get("ImageWasStripped", False)
                            },
                            "answers": []
                        }
                        
                        out_str += 'Q:\n\n'
                        if parent["Title"] is not None:
                            parsed_title = BeautifulSoup(parent["Title"], "html.parser").get_text()
                            out_str += '{}\n\n'.format(parsed_title)
                            json_output["question"]["Title"] = parsed_title
                        if parent["Body"] is not None:
                            parsed_body = BeautifulSoup(parent["Body"], "html.parser").get_text()
                            out_str += '{}\n\n'.format(parsed_body)
                            json_output["question"]["Body"] = parsed_body
                        if parent["Tags"] is not None:
                            tags = parent["Tags"]
                            parsed_tags = [t.replace(">", "").replace("<", "") for t in tags.split("><")]
                            json_output["question"]["Tags"] = parsed_tags
                        if parent["Answers"] is not None:
                            key_score_dict = {}
                            for k, a in parent["Answers"].items():
                                key_score_dict[k] = int(a["Score"])
                            key_score_dict = {k: v for k, v in sorted(key_score_dict.items(), key=lambda item: item[1], reverse=True)}
                            count = 0
                            for k in key_score_dict:
                                if count >= self.max_responses:
                                    break
                                answer_data = parent["Answers"][k]
                                parsed_answer = BeautifulSoup(answer_data["Body"], "html.parser").get_text()
                                
                                # Add to new JSON format with cleaned text
                                json_output["answers"].append({
                                    "Id": answer_data.get("Id", k),
                                    "CreationDate": answer_data.get("CreationDate"),
                                    "Score": answer_data.get("Score"),
                                    "Body": parsed_answer,
                                    "LastEditDate": answer_data.get("LastEditDate"),
                                    "LastActivityDate": answer_data.get("LastActivityDate"),
                                    "CommentCount": answer_data.get("CommentCount"),
                                    "ContentLicense": answer_data.get("ContentLicense"),
                                    "AcceptedAnswer": k == parent.get("AcceptedAnswerId"),
                                    "ImageWasStripped": answer_data.get("ImageWasStripped", False)
                                })
                                
                                out_str += 'A:\n\n{}\n\n'.format(parsed_answer)
                                count += 1

                        if self.out_format == "jsonl":
                            # Write to JSONL file (one JSON object per line)
                            with open(self.jsonl_file_path, 'a', encoding='utf-8') as f:
                                json.dump(json_output, f, ensure_ascii=False)
                                f.write('\n')
                        
                        elif self.out_format == "txt":
                            with open("{}/{}".format(self.out_folder, out_name), 'w') as f:
                                try:
                                    f.write(filter_newlines(out_str))
                                except:
                                    f.write(filter_newlines(handle_unicode_errors(out_str)))
                        if self.out_format == "json":
                            out_filename = out_name.replace(".txt", ".json")
                            if not self.should_zip:
                                with open("{}/{}".format(self.out_folder, out_filename), 'w', encoding="utf-8") as f:
                                    json.dump(json_output, f, ensure_ascii=False)
                            else:
                                self.ar.writestr(out_filename, json.dumps(json_output, ensure_ascii=False).encode('utf-8'))
                        elif self.out_format == "zip":
                            try:
                                self.ar.writestr(out_name, filter_newlines(out_str))
                            except:
                                self.ar.writestr(out_name, filter_newlines(handle_unicode_errors(out_str)))
                        elif self.out_format == "lm_dataformat":
                            try:
                                self.ar.add_data(filter_newlines(out_str), meta={
                                    'name': out_name})
                            except:
                                self.ar.add_data(filter_newlines(handle_unicode_errors(out_str)), meta={
                                    'name': out_name})
        for key in keys_to_del:
            self.questions.pop(key, None)


import os, re




def handle_unicode_errors(txt):
    return txt.encode('utf-8', 'replace').decode()


def is_question(elem_attribs):
    return elem_attribs.get("PostTypeId") == 1


def is_answer(elem_attribs):
    return elem_attribs.get("PostTypeId") == 2


def filter_newlines(text):
    return re.sub("\n{3,}", "\n\n", text)


def is_accepted_answer(a_attribs, q_attribs):
    assert is_question(q_attribs), "Must be a question to have an accepted answer"
    assert is_answer(a_attribs), "Must be an answer to be an accepted answer"
    if q_attribs["AcceptedAnswerId"] is not None:
        if q_attribs["AcceptedAnswerId"] == a_attribs["Id"]:
            return True
    else:
        return False


def has_answers(elem_attribs):
    assert is_question(elem_attribs), "Must be a question to have answers"
    if elem_attribs["AnswerCount"] is not None:
        if int(elem_attribs["AnswerCount"]):
            return True
    return False


def trim_attribs(elem_attribs, attrib_type="question"):
    """deletes non-useful data from attribs dict for questions / answers, returns remaining"""
    if attrib_type == "question":
        # preserve key metadata fields for JSON output
        to_keep = [
            'Id', 'Body', 'Title', 'Tags', 'AnswerCount', 'AcceptedAnswerId', 'PostTypeId',
            'CreationDate', 'ClosedDate', 'Score', 'ViewCount',
            'LastEditDate', 'LastActivityDate', 'CommentCount', 'ContentLicense'
        ]
        to_delete = [x for x in elem_attribs.keys() if x not in to_keep]
        [elem_attribs.pop(x, None) for x in to_delete]
        elem_attribs["ParsedAnswers"] = 0
        elem_attribs["Answers"] = {}
    elif attrib_type == "answer":
        # preserve key metadata fields for JSON output
        to_keep = [
            'Id', 'Body', 'Score', 'CreationDate', 'LastEditDate',
            'LastActivityDate', 'CommentCount', 'ContentLicense'
        ]
        new_dict = {}
        for item in to_keep:
            new_dict[item] = elem_attribs[item]
        return new_dict
    else:
        raise Exception('Unrecognized attribute type - please specify either question or answer')
