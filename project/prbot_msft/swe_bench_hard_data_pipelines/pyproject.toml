[project]
name = "code_data_utils"
version = "0.0.2"
dependencies = [
    "azure-identity",
    "azure-keyvault-secrets",
    "azure-storage-blob",
    "azure-ai-ml",
    "black",
    "chardet",
    "flake8",
    "GitPython",
    "datasets",
    "pre-commit",
    "pydantic",
    "unidiff",
    "cachetools",
    # TODO: remove when datasets supports numpy-2
    "numpy<2"
]

[build-system]
requires = ["setuptools>=64.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
include-package-data = true

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''
