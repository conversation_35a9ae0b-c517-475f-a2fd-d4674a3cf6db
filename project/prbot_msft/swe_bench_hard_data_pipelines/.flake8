[flake8]
max-line-length = 160
# W293 - blank line contains whitespace
# W503 - line break before binary operator
# E203 - whitespace before ':'
# E226 - missing whitespace around arithmetic operator
# E302 - one line between functions is fine
# E402 - module level import not at top of file
ignore = W293,W503,E203,E226,E302,E402
# F401 - module imported but unused
# allow unused imports in __init__.py files

per-file-ignores = 
    */__init__.py:F401

# No need to traverse our git directory
# There's no value in checking cache directories

exclude = 
    .git,
    __pycache__,
    *.pyc
