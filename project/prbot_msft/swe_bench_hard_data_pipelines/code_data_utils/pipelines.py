import json
import os
import tempfile
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from time import sleep
from typing import Optional
from uuid import uuid4

from azure.ai.ml import MLClient
from azure.ai.ml.dsl import pipeline
from azure.ai.ml.entities import Job, ManagedIdentityConfiguration
from azure.identity import DefaultAzureCredential
from code_data_utils.blob_ops import get_codemodeldata_data
from code_data_utils.components import (  # scrape_github,
    ranking_prompt_crafter,
    swe_bench_data_curator,
)
from code_data_utils.logger import get_logger
from code_data_utils.utils import MLClientInfo, get_ml_client_info

logger = get_logger(__name__)

UAI = (
    "/subscriptions/48bbc269-ce89-4f6f-9a12-c6f91fcb772d/"
    + "resourcegroups/code-model-rg/providers/Microsoft.ManagedIdentity/"
    + "userAssignedIdentities/code-model-mi"
)


def set_cpu_runsettings(component, compute, is_singularity: bool):
    """Set the runsettings for a component"""
    if is_singularity:
        component.compute = compute

        component.resources = {
            "instance_type": "Singularity.D4_v3",
            "instance_count": 1,
            "shm_size": "2048G",
            "properties": {
                "singularity": {
                    "slaTier": "Premium",  # Basic, Standard, Premium
                    "priority": "High",  # Low, Medium, High
                }
            },
        }
        env_vars = {
            "_AZUREML_SINGULARITY_JOB_UAI": UAI,
        }
        if env_vars:
            for key, value in env_vars.items():
                component.environment_variables[key] = value
    else:
        component.identity = ManagedIdentityConfiguration(
            client_id="b6fbd023-10ca-4b2f-a869-433b60d90336",
            principal_id="18ee9058-0b28-4a61-91da-740683575a6b",
            resource_id="code-model-mi",
        )

    return component


def clean_name(name: str):
    # save only alphanumeric characters and _
    return "".join([c for c in name if c.isalnum() or c == "_"])


def create_data_pipeline(is_singularity: bool, compute_target: str, **kwargs):
    @pipeline(
        name="swe_bench_data_curator",
        description="SWE Bench Data Curator",
    )
    def data_pipeline(
        base_path: str, split: str, instance_id: str, base_commit_sha: str, metadata: str
    ):
        copilot_ws_data = swe_bench_data_curator(
            base_path=base_path,
            split=split,
            instance_id=instance_id,
            base_commit_sha=base_commit_sha,
            metadata=metadata,
        )
        set_cpu_runsettings(copilot_ws_data, compute_target, is_singularity)

    return data_pipeline(**kwargs)


def create_ranking_prompt_pipeline(is_singularity: bool, compute_target: str, **kwargs):
    @pipeline(
        name="ranking_prompt_crafter",
        description="ranking_prompt_crafter",
    )
    def ranking_prompt_pipeline(
        instance_id: str,
        input_base_path: str,
        summary_base_path: str,
        output_base_path: str,
        split: str,
    ):
        crafted_prompt = ranking_prompt_crafter(
            input_base_path=input_base_path,
            summary_base_path=summary_base_path,
            output_base_path=output_base_path,
            instance_id=instance_id,
            split=split,
        )
        set_cpu_runsettings(crafted_prompt, compute_target, is_singularity)

    return ranking_prompt_pipeline(**kwargs)


def submit_data_pipeline(
    metadata: str, client_info: MLClientInfo, base_path: str, pipeline_name: str, tag: str
):
    client = MLClient(credential=DefaultAzureCredential(), **client_info.client)
    # save metadata to a temp file
    with tempfile.TemporaryDirectory() as temp_dir:
        # XXX (mercerchen): create tmp AML data results in auth error.
        # To unblock, I am going to upload the metadata to a blob storage
        metadata_filename = "metadata.jsonl"
        jsonl_file_path = os.path.join(temp_dir, metadata_filename)
        with open(jsonl_file_path, "w") as f:
            f.write(metadata + "\n")
            f.flush()
        container_client = get_codemodeldata_data()
        blob_path = f"tmp/{str(uuid4())}/{metadata_filename}"
        with open(jsonl_file_path, "rb") as f:
            blob_client = container_client.get_blob_client(blob_path)
            blob_client.upload_blob(f, overwrite=True)
        metadata_as_json = json.loads(metadata)
        instance_id = metadata_as_json["instance_id"]
        pipeline: Job = create_data_pipeline(
            is_singularity=client_info.is_singularity,
            compute_target=client_info.compute,
            base_path=base_path,
            split=metadata_as_json["split"],
            instance_id=metadata_as_json["instance_id"],
            base_commit_sha=metadata_as_json["base_commit_sha"],
            metadata=blob_path,
        )
        pipeline.name = str(uuid4())
        pipeline.display_name = clean_name(instance_id)
        pipeline.tags = {"tag": tag, "instance_id": instance_id}
        pipeline_run = client.jobs.create_or_update(
            pipeline, experiment_name=pipeline_name, compute=client_info.compute
        )
        logger.info(f"\n\n\033[93mWorkspace Job link: \n{pipeline_run.studio_url}\033[0m")


def submit_ranking_prompt_pipeline(
    instance_id: str,
    client_info: MLClientInfo,
    input_base_path: str,
    summary_base_path: Optional[str],
    output_base_path: str,
    pipeline_name: str,
    tag: str,
    split: str,
):
    client = MLClient(credential=DefaultAzureCredential(), **client_info.client)
    pipeline: Job = create_ranking_prompt_pipeline(
        is_singularity=client_info.is_singularity,
        compute_target=client_info.compute,
        input_base_path=input_base_path,
        summary_base_path=summary_base_path,
        output_base_path=output_base_path,
        instance_id=instance_id,
        split=split,
    )
    uuid4_suffix = str(uuid4())
    pipeline.name = uuid4_suffix
    pipeline.display_name = clean_name(instance_id)
    pipeline.tags = {"tag": tag, "instance_id": instance_id}
    pipeline_run = client.jobs.create_or_update(
        pipeline, experiment_name=pipeline_name, compute=client_info.compute
    )
    logger.info(f"\n\n\033[93mWorkspace Job link: \n{pipeline_run.studio_url}\033[0m")


def submit_pipelines(pipeline_to_submit, data_slice: list, client: MLClientInfo, **kwargs):
    clients = get_ml_client_info()
    logger.info(f"Loaded {len(clients)} clients")
    client_info = clients[client]
    logger.info(f"Client info: {client_info}")
    sleep(10)
    finished, unsuccessful_runs = 0, []
    start = datetime.now()
    with ThreadPoolExecutor() as executor:
        futures = []
        for d in data_slice:
            futures.append(
                (
                    executor.submit(pipeline_to_submit, d, client_info, **kwargs),
                    d,
                )
            )

        for future, d in futures:
            try:
                future.result(timeout=600)
                finished += 1
                time_elapsed = datetime.now() - start
                logger.info(
                    f"\x1b[31;1mFinished {finished}/{len(data_slice)} submissions, {time_elapsed} time elapsed\x1b[0m"
                )
            except Exception as e:
                logger.error(f"Error in submission: {e}")
                unsuccessful_runs.append(d)
                continue

    logger.info(
        f"Finished {finished}/{len(data_slice)} runs, errors for {len(unsuccessful_runs)} runs."
    )
    logger.info(f"Total time elapsed: {datetime.now() - start}")
