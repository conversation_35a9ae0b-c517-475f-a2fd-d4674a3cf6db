import requests

endpoint = "https://aims-oai-research-inference-uks.openai.azure.com/openai/deployments/tscience-uks-gpt-4o/chat/completions?api-version=2024-08-01-preview"


def call_aoai(prompt: str, credential):
    """Calls an Azure OpenAI endpoint."""
    token = credential.get_token("https://cognitiveservices.azure.com/.default").token

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }
    response = requests.post(
        url=endpoint,
        headers=headers,
        json={
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0,
            "top_p": 1,
        },
    )
    try:
        result = response.json()
        if "choices" not in result:
            raise Exception(f"No choices: {response.text}")
        return result
    except Exception:
        raise Exception(response.text)
