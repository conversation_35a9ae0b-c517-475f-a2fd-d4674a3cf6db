import json
import os
from pathlib import Path
from typing import Optional

from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobServiceClient, ContainerClient
from cachetools import TTLCache, cached
from code_data_utils.logger import get_logger
from code_data_utils.utils import make_dirs

logger = get_logger(__name__)


@cached(cache=TTLCache(maxsize=1, ttl=300))
def get_credential():
    return DefaultAzureCredential()


@cached(cache=TTLCache(maxsize=1, ttl=300))
def get_codemodeldata_data(credential=get_credential()) -> ContainerClient:
    blob_service = BlobServiceClient(
        account_url="https://codemodeldata.blob.core.windows.net",
        credential=credential,
    )
    container_client = blob_service.get_container_client("data")
    return container_client


def check_if_blob_exists(
    blob_name: str, container_client: ContainerClient = get_codemodeldata_data()
) -> bool:
    blob_client = container_client.get_blob_client(blob_name)
    return blob_client.exists()


def list_blob_names(container_client, blob_prefix: str) -> bool:
    blobs = container_client.walk_blobs(name_starts_with=blob_prefix, delimiter="/")
    blobs = [os.path.basename(os.path.dirname(blob.name)) for blob in blobs]
    return [blob for blob in blobs if blob]


def download_blob(
    blob_path: str,
    local_path: str,
    container_client: ContainerClient = get_codemodeldata_data(),
) -> None:
    blob_client = container_client.get_blob_client(blob_path)
    with open(local_path, "wb") as f:
        blob_client.download_blob().readinto(f)


def upload_paths_to_blobs(paths: list[Path], container_client: ContainerClient):
    """Given a list of paths, upload to blob storage with the same path.
    If the path starts with "/", remove it.
    """
    logger.info(f"Uploading {paths} to Azure")
    for path in paths:
        # hacky way to get the blob path
        if str(path)[0] == "/":
            blob_path = str(path.absolute())[1:]
        else:
            blob_path = str(path)
        logger.info(f"Uploading {path} to {blob_path}")
        # check if the local file exists
        if not os.path.exists(path):
            logger.error(f"File {path} does not exist")
            # list its parent directory
            logger.info(list(path.parent.iterdir()))
            raise FileNotFoundError(f"File {path} does not exist")
        with open(path, "rb") as f:
            blob_client = container_client.get_blob_client(blob_path)
            blob_client.upload_blob(f, overwrite=True)


def load_json_from_blob(blob_path: str, local_path: str, container_client: ContainerClient) -> dict:
    """Download a json file from blob storage."""
    logger.info(f"Downloading from {blob_path} to {local_path}")
    download_blob(blob_path, local_path, container_client=container_client)
    with open(local_path, "r") as f:
        json_data = json.loads(f.readline())
    logger.info(f"Loaded:\n\n{json.dumps(json_data, indent=4)}")

    json_data["base_commit"] = json_data["base_commit_sha"]
    del json_data["base_commit_sha"]

    json_data["merge_commit"] = json_data["merge_commit_sha"]
    del json_data["merge_commit_sha"]

    json_data["problem_statement"] = json_data["issue_title"] + "\n" + json_data["issue_body"]
    del json_data["issue_body"]
    del json_data["issue_title"]

    # json_data["pull_number"] = json_data["issue_number"]
    json_data["issue_numbers"] = [json_data["issue_number"]]
    del json_data["issue_number"]

    json_data["hints_text"] = ""
    json_data["version"] = "ptsdv1-2025-08-12-2025-09-04"
    json_data["FAIL_TO_PASS"] = []
    json_data["PASS_TO_FAIL"] = []
    json_data["env_install_commmit"] = ""

    logger.info(f"Transformed:\n\n{json.dumps(json_data, indent=4)}")

    return json_data


def load_data(
    container_client,
    split: str,
    source_path: str,
    done_path: Optional[str],
    local_base_dir: str,
    exclude_instances: list[str] = [],
) -> list[str]:
    local_fixed_path = f"{local_base_dir}/{source_path}"
    if not os.path.exists(local_fixed_path):
        logger.info(f"Downloading from {source_path} to {local_fixed_path}")
        make_dirs(Path(os.path.dirname(local_fixed_path)))
        download_blob(
            source_path,
            local_fixed_path,
            container_client=container_client,
        )
    with open(local_fixed_path, "r") as f:
        data = [json.loads(x) for x in f.readlines()]
    logger.info(f"Loaded {len(data)} instances from SWE Bench {split} split")
    data = [sample for sample in data if sample["instance_id"] not in exclude_instances]
    logger.info(f"Filtered {len(data)} instances from SWE Bench {split} split")
    for d in data:
        d["split"] = split
    if done_path:
        local_done_path = f"{local_base_dir}/done.txt"
        download_blob(
            done_path,
            local_done_path,
            container_client=container_client,
        )
        if os.path.exists(local_done_path):
            with open(local_done_path, "r") as f:
                done = set([x.strip() for x in f.readlines()])
            logger.info("Checking for instances that have already been processed")
            data = [d for d in data if d["instance_id"] not in done]
    logger.info(f"Have {len(data)} instances to submit")
    data = [json.dumps(d) for d in data]
    return data
