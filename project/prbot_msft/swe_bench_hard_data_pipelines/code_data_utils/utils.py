import json
import shutil
from dataclasses import dataclass
from pathlib import Path

from azure.identity import ManagedIdentityCredential
from azure.keyvault.secrets import SecretClient
from code_data_utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class MLClientInfo:
    client: dict
    compute: str
    is_singularity: bool


def get_ml_client_info():
    alexander256_client = {
        "subscription_id": "79f57c16-00fe-48da-87d4-5192e86cd047",
        "resource_group_name": "Alexander256",
        "workspace_name": "Alexander256V100",
    }
    tscience_research_client = {
        "subscription_id": "9ec1d932-0f3f-486c-acc6-e7d78b358f9b",
        "resource_group_name": "TScience",
        "workspace_name": "tscience_research",
    }

    genaicpu_client = {
        "subscription_id": "9bd2a334-cbd8-4e2c-9ef8-74ce1f442924",
        "resource_group_name": "singularity-genaicpuvc",
        "workspace_name": "genaicpu",
    }

    codemodelws_client = {
        "subscription_id": "48bbc269-ce89-4f6f-9a12-c6f91fcb772d",
        "resource_group_name": "code-model-rg",
        "workspace_name": "code-model-ws",
    }

    genaicpu = (
        "/subscriptions/9bd2a334-cbd8-4e2c-9ef8-74ce1f442924/resourceGroups/"
        + "singularity-genaicpuvc/providers/Microsoft.MachineLearningServices/virtualClusters/genaicpu"
    )

    return [
        MLClientInfo(alexander256_client, "e2v3", False),
        MLClientInfo(alexander256_client, "e4sv3", False),
        MLClientInfo(alexander256_client, genaicpu, True),
        MLClientInfo(genaicpu_client, genaicpu, True),
        MLClientInfo(tscience_research_client, "d1v2", False),
        MLClientInfo(codemodelws_client, genaicpu, True),
        MLClientInfo(genaicpu_client, "e2v3", False),
    ]


def get_secret(key_vault_url: str, secret_name: str, client_id: str) -> str:
    """Retrieve a secret from a key vault."""
    credential = ManagedIdentityCredential(client_id=client_id)
    client = SecretClient(vault_url=key_vault_url, credential=credential)
    secret = client.get_secret(secret_name)
    secret_value = secret.value
    return secret_value


def dumps_all_objs(metadata: dict) -> None:
    """Convert all objects to str using json.dumps
    so that the dataset will be Huggingface-compatible.
    """
    for key, value in metadata.items():
        if isinstance(value, list) or isinstance(value, dict):
            metadata[key] = json.dumps(value)


def make_dirs(dir_path: Path, overwrite=True) -> None:
    """Create dirs for dir_path and overwrite anything that was pre-existing."""
    if dir_path.exists() and overwrite:
        shutil.rmtree(dir_path)
    dir_path.mkdir(exist_ok=True, parents=True)


def dump_json(filename, obj):
    """Dump an object to a file."""
    with open(filename, "w") as f:
        logger.info(f"Dumping data to {filename}: {json.dumps(obj, indent=4)}")
        f.write(f"{json.dumps(obj)}\n")


def unpack_instance_id(instance_id: str):
    """Unpack an instance id to owner, repo, and PR number."""
    owner, rest = instance_id.split("__")
    repo, pr_number = rest.rsplit("-", 1)
    return owner, repo, pr_number
