import json
from dataclasses import dataclass, field
from typing import Optional

from code_data_utils.swe_bench import SWEbenchInstance
from pydantic import model_validator


@dataclass
class APIResponse:
    body: dict
    status_code: int


class WorkspaceBenchInstance(SWEbenchInstance):
    filenames: str
    number_of_files: int
    language_count: str
    PASS_TO_FAIL: str
    pull_number: int
    env_install_commmit: str
    split: str
    merge_commit: Optional[str]
    issue_numbers: str = "[]"

    # duplicate of env_install_commmit
    environment_setup_commit: Optional[str] = None

    # duplicate of PASS_TO_FAIL, maybe?
    PASS_TO_PASS: Optional[str] = None

    raw_patch: Optional[str] = None
    raw_test_patch: Optional[str] = None

    class Config:
        extra = "forbid"

    @classmethod
    def _assign_if_missing(cls, values, field1: str, field2: str):
        val1, val2 = values.get(field1), values.get(field2)
        if val1 is not None and val2 is None:
            values[field2] = val1  # Set field2 to field1's value if field2 is missing
        elif val2 is not None and val1 is None:
            values[field1] = val2  # Set field1 to field2's value if field1 is missing

    @model_validator(mode="before")
    def set_fields(cls, values):
        cls._assign_if_missing(values, "environment_setup_commit", "env_install_commmit")
        cls._assign_if_missing(values, "PASS_TO_PASS", "PASS_TO_FAIL")
        return values


@dataclass
class FileRankingInstance:
    CURRENT_VERSION = 1

    instance_id: str = field(
        metadata={"description": "The instance id in the format of REPO__OWNER-PRNUMBER"}
    )
    ranking_prompt: str = field(metadata={"description": "The file selection prompt"})
    unpadded_golden_indexes: list[int] = field(
        metadata={
            "description": "List of indexes corresponding to files that were modified or deleted. "
            "This may be a subset of modified_files as 1) some files may be excluded due to "
            "capping based on ranking and 2) some files are excluded due to the blocklist."
        }
    )
    golden_indexes: list[int] = field(
        metadata={
            "description": "unpadded_golden_indexes padded to length 20 " "with top ranked files."
        }
    )
    modified_files: list[str] = field(
        metadata={
            "description": "List of all filenames from the patch, including "
            "modified and deleted files."
        }
    )
    golden_set_hit: float = field(
        metadata={
            "description": "Total number of files from modified_files "
            "included in the prompt 1) that existed in the base_commit and "
            "2) after capping based on ranking."
        }
    )
    golden_set_hit_ratio: float = field(
        metadata={"description": "golden_set_hit_ratio / len(modified_files)"}
    )
    index_to_file: dict[str, str] = field(
        metadata={"description": "Dictionary of index to file name"}
    )
    file_to_char_count: dict[str, int] = field(
        metadata={"description": "Dictionary of file name to character count"}
    )
    regex: str = field(
        metadata={
            "description": "The model-generated regex used to search the contents of a repo "
            "for content relevant to the task."
        }
    )
    question: str = field(
        metadata={
            "description": "The model-generated question summarizing an issue. This is"
            "included in the prompt"
        }
    )
    file_to_encoding: dict[str, int] = field(
        default=None,  # Added this field 10/31/2024
        metadata={
            "description": "Filename to encoding. Only includes files whose encoding was successfully"
            "detected by chardet."
        },
    )

    version: str = field(
        default=CURRENT_VERSION,
        metadata={"description": "Version 1: Includes file_to_encoding field."},
    )

    def __getattr__(self, item):
        if item == "version":
            return None

        # Raise an AttributeError for any other missing attribute
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{item}'")

    def to_json(self) -> str:
        dict_copy = self.__dict__.copy()
        self.__dump_dicts_and_lists(dict_copy)
        return json.dumps(dict_copy)

    def __dump_dicts_and_lists(self, obj: dict) -> None:
        """Convert all dicts and lists to str using json.dumps
        so that the dataset will be Huggingface-compatible.
        """
        for key, value in obj.items():
            if isinstance(value, dict) or isinstance(value, list):
                obj[key] = json.dumps(value)
