import json
import os
import re
import time

import git
import requests
from code_data_utils.logger import get_logger
from code_data_utils.models import APIResponse
from code_data_utils.utils import get_secret
from unidiff import PatchSet

logger = get_logger(__name__)


def get_pr_data(
    owner: str, repo: str, pull_number: str, client_id: str, use_token: bool = True
) -> APIResponse:
    """Call GH API to get the diff_url from pull request data."""
    url = f"https://api.github.com/repos/{owner}/{repo}/pulls/{pull_number}"
    return call_api(url, client_id, use_token)


def get_pr_files(
    owner: str, repo: str, pull_number: str, client_id: str, use_token: bool = True
) -> APIResponse:
    """Call GH API to get the diff_url from pull request data."""
    url = f"https://api.github.com/repos/{owner}/{repo}/pulls/{pull_number}/files"
    return call_api(url, client_id, use_token)


def call_api(url: str, client_id: str, use_token: bool):
    """Call GH API.
    Retry loop adapted from https://github.com/princeton-nlp/SWE-bench/blob/main/swebench/collect/utils.py#L37.
    """
    logger.info(f"Getting PR data: {url}")
    headers = {
        "Accept": "application/vnd.github+json",
        "X-GitHub-Api-Version": "2022-11-28",
    }
    if use_token:
        gh_token = get_secret(
            key_vault_url="https://code-model.vault.azure.net/",
            secret_name="damajercak-gh-token",
            client_id=client_id,
        )
        headers["Authorization"] = f"Bearer {gh_token}"
    while True:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            pull_request_data = response.json()
            return APIResponse(body=pull_request_data, status_code=200)
        elif response.status_code == 403:
            rate_limit_response = requests.get("https://api.github.com/rate_limit", headers=headers)
            if rate_limit_response.status_code == 200:
                rate_limit = rate_limit_response.json()
                remaining = rate_limit["resources"]["core"]["remaining"]
                reset_time = rate_limit["resources"]["core"]["reset"]
                seconds_to_wait = min(3600, reset_time - time.time() + 60)
                logger.info(
                    f"Rate limit exceeded for token, "
                    f"waiting for {int(seconds_to_wait) / 60} minutes, remaining calls: {remaining}"
                )
                if remaining == 0:
                    time.sleep(seconds_to_wait)
        else:
            logger.error(
                f"Failed to get pull request for {url}. Status code: {response.status_code}"
            )
            logger.error(response.text)
            return APIResponse(body={"error": response.json()}, status_code=response.status_code)


def extract_patches(pull: dict) -> tuple[str, str]:
    """
    Get patch and test patch from PR.
    https://github.com/princeton-nlp/SWE-bench/blob/main/swebench/collect/utils.py#L310

    Args:
        pull (dict): PR dictionary object from GitHub
        repo (Repo): Repo object
    Return:
        patch_change_str (str): gold patch
        patch_test_str (str): test patch
    """
    patch = requests.get(pull["diff_url"]).text
    patch_test = ""
    patch_fix = ""
    for hunk in PatchSet(patch):
        if any(test_word in hunk.path for test_word in ["test", "tests", "e2e", "testing"]):
            patch_test += str(hunk)
        else:
            patch_fix += str(hunk)
    return patch_fix, patch_test


def decode_octo_escape(s):
    return s.encode().decode("unicode-escape").encode("latin-1").decode("utf-8")


def extract_source_filename(source_file: str) -> str:
    """Extract the source filename from a PatchedFile's source_file field.
    When there are spaces in the filename, there may be quotes present.
    PatchSet has a bug where the target file name may sneak into the source file name
    when there are spaces in the filename.
    See instance id: GCTC-NTGC__gc-digital-talent-5995
    """
    pattern = r'^"?a\/([^"]+)"?(?: "b\/[^"]+"?)?'
    match = re.search(pattern, source_file)
    if match:
        return match.group(1)  # Return the filename part from the "a/" block
    else:
        raise ValueError(f"No valid source file found for {source_file}")


def get_modified_files_from_patch(patch: str) -> list[str]:
    """Get modified and removed files from patch."""
    patch_set = PatchSet(patch)
    modified_files = []
    logger.info(f"Patch size: {len(patch_set)}")
    logger.info(f"Total modified files: {len(patch_set.modified_files)}")
    logger.info(f"Total removed files: {len(patch_set.removed_files)}")
    for filename in patch_set.modified_files + patch_set.removed_files:
        # remove a/ from the beginning of the filename
        modified_files.append(filename.source_file)

    modified_files = list(
        set([extract_source_filename(decode_octo_escape(f)) for f in modified_files])
    )
    logger.info(f"Gold list of modified files extracted from patch:\n{modified_files}")
    return modified_files


def get_language_count(repo_path: str) -> dict:
    """Use github-linguist to get a language breakdown by percentage and file size
    https://github.com/github-linguist/linguist
    """
    language_count_command = f"cd {str(repo_path.absolute())} && github-linguist --breakdown --json"

    language_count = os.popen(language_count_command).read()
    return json.loads(language_count)


def checkout_repo_at_commit(repo_url: str, repo_path: str, commit: str) -> None:
    """Clone a repo to a path and checkout at a commit."""
    repo: git.Repo = git.Repo.clone_from(repo_url, repo_path)
    repo.git.fetch("origin", commit)
    logger.info(f"Checkout out commit {commit}")
    repo.git.checkout(commit)
