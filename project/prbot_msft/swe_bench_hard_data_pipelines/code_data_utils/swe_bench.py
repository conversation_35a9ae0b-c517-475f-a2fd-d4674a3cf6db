from pathlib import Path

import datasets
from code_data_utils.logger import get_logger
from pydantic import BaseModel

logger = get_logger(__name__)


class SWEbenchInstance(BaseModel):
    """Copied from https://github.com/princeton-nlp/SWE-bench/blob/c7d4d9d481937f4bbfabab323a300abb1fbe4c0b/swebench/harness/constants.py#L12"""

    repo: str
    instance_id: str
    base_commit: str
    patch: str
    test_patch: str
    problem_statement: str
    hints_text: str
    created_at: str
    version: str
    FAIL_TO_PASS: str
    PASS_TO_PASS: str
    environment_setup_commit: str

    # exclude any other fields
    class Config:
        extra = "forbid"


class SWE_BENCH_PROCESSOR:
    def __init__(self, *, save_folder: str | Path = None):
        if isinstance(save_folder, str):
            save_folder = Path(save_folder)

        self.data: list[SWEbenchInstance] = []

    def create_dataset(self):
        assert (
            len(self.data) > 0
        ), "self.data is empty. Load data first by calling load_from_metadata or load_from_huggingface"

    def load_from_metadata(self, metadata: list[SWEbenchInstance]):
        self.data = metadata

    def load_from_huggingface(self, *, split: str = "train"):
        self.data = list(
            datasets.load_dataset("princeton-nlp/SWE-bench", split=split, streaming=True)
        )
