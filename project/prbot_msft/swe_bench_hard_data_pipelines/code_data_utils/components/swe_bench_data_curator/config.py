import os
from pathlib import Path

from azure.identity import DefaultAzureCredential, ManagedIdentityCredential
from code_data_utils.utils import make_dirs


class SWEBenchCuratorConfig:
    CLIENT_ID = "b6fbd023-10ca-4b2f-a869-433b60d90336"  # code-model-mi
    PRDATA_FILENAME = "prdata.json"
    PRFILES_FILENAME = "prfiles.json"
    METADATA_FILENAME = "metadata.json"

    def __init__(
        self,
        is_local: bool,
        split: str,
        instance_id: str,
        base_path: str,
        base_commit_sha: str,
    ):
        self.is_local = is_local
        self.split = split
        self.instance_id = instance_id
        self.base_path = base_path
        self.base_commit_sha = base_commit_sha

        self.repo_save_path = Path(f"{self.base_path}/{self.split}/{self.instance_id}")
        self.repo_path = self.repo_save_path / self.base_commit_sha
        self.metadata_path = self.repo_save_path / self.METADATA_FILENAME
        self.prdata_path = self.repo_save_path / self.PRDATA_FILENAME
        self.prfiles_path = self.repo_save_path / self.PRFILES_FILENAME
        self.tar_path = self.repo_save_path / f"{self.base_commit_sha}.tar"
        self.local_metadata_output_pathname = os.path.join(self.base_path, self.METADATA_FILENAME)
        self.local_prdata_output_pathname = os.path.join(self.base_path, self.PRDATA_FILENAME)

        make_dirs(self.repo_path)

    def get_credential(self):
        if self.is_local:
            return DefaultAzureCredential()
        else:
            return ManagedIdentityCredential(client_id=self.CLIENT_ID)
