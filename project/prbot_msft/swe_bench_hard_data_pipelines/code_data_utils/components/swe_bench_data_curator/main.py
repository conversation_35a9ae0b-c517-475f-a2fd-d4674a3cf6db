import argparse
import shutil

from azureml.core.run import Run
from code_data_utils.blob_ops import (
    check_if_blob_exists,
    get_codemodeldata_data,
    load_json_from_blob,
    upload_paths_to_blobs,
)
from code_data_utils.components.swe_bench_data_curator.config import SWEBenchCuratorConfig
from code_data_utils.git_utils import (
    checkout_repo_at_commit,
    extract_patches,
    get_language_count,
    get_pr_data,
    get_pr_files,
)
from code_data_utils.logger import get_logger
from code_data_utils.models import APIResponse, WorkspaceBenchInstance
from code_data_utils.utils import dump_json, dumps_all_objs, unpack_instance_id
from unidiff.errors import UnidiffParseError

logger = get_logger(__name__)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--metadata",
        type=str,
        required=True,
        help="Path to metadata json file in codemodeldata blob storage",
    )
    parser.add_argument(
        "--split",
        type=str,
        required=True,
        help="What split the data is",
    )
    parser.add_argument(
        "--instance_id",
        type=str,
        required=True,
        help="The instance id of the example",
    )
    parser.add_argument(
        "--base_commit_sha",
        type=str,
        required=True,
        help="The base commit sha of the repo",
    )
    parser.add_argument(
        "--base_path",
        type=str,
        default="swe_bench",
        required=False,
        help="The base output path in blob storage",
    )
    parser.add_argument("--output_dataset", type=str, help="The local base output path")
    parser.add_argument("--local", action="store_true", help="Whether this is a local run.")
    parser.add_argument(
        "--skip_upload",
        action="store_true",
        help="Whether to skip upload to blob storage",
    )
    return parser.parse_args()


def main(args: dict):
    run = Run.get_context()
    logger.info(f"run context: {run}")
    config = SWEBenchCuratorConfig(
        args["local"],
        args["split"],
        args["instance_id"],
        args["base_path"],
        args["base_commit_sha"],
    )
    container_client = get_codemodeldata_data(credential=config.get_credential())
    core_paths = [
        config.metadata_path,
        config.tar_path,
        config.prdata_path,
    ]
    paths_to_upload = core_paths + [config.prfiles_path]
    logger.info(f"Checking if all core paths exist in blob storage: {core_paths}")
    all_core_paths_exist = all(
        check_if_blob_exists(str(path), container_client) for path in core_paths
    )
    if not all_core_paths_exist:
        logger.info("Not all core paths exist--re-creating files.")
        metadata, pr_data, pr_files = create_copilot_ws_example(
            args=args,
            container_client=container_client,
            config=config,
            run=run,
        )
        dump_files(config, metadata, pr_data, pr_files)
        if not args["skip_upload"]:
            upload_paths_to_blobs(paths=paths_to_upload, container_client=container_client)
    else:
        logger.info("All core paths exist--downloading metadata and pr data from blob storage.")
        metadata = load_json_from_blob(
            blob_path=str(config.metadata_path),
            local_path=config.local_metadata_output_pathname,
            container_client=container_client,
        )
        pr_data = load_json_from_blob(
            blob_path=str(config.prdata_path),
            local_path=config.local_prdata_output_pathname,
            container_client=container_client,
        )

    if not check_if_blob_exists(str(config.prfiles_path), container_client):
        # This is a side effect of how we ran the pipeline.
        # We did not initially call the list files for a PR API
        logger.info(f"PR files do not exist at {str(config.prfiles_path)}. Calling GH API.")
        owner, repo_name, pr_number = unpack_instance_id(args["instance_id"])
        pr_files = log_and_get_prfiles(
            owner=owner,
            repo_name=repo_name,
            pr_number=pr_number,
            run=run,
            config=config,
        )
        dump_json(config.prfiles_path, pr_files)
        if not args["skip_upload"]:
            upload_paths_to_blobs(paths=[config.prfiles_path], container_client=container_client)
    run.flush()


def create_copilot_ws_example(
    args: dict,
    container_client,
    config: SWEBenchCuratorConfig,
    run: Run,
) -> tuple[dict, dict, dict]:
    """Create a copilot_ws example, which includes
    1. metadata of the type WorkspaceBenchInstance
    2. pr_data, which is the response from the GitHub get a pull request API
    3. the repo
    """
    metadata = load_json_from_blob(args["metadata"], config.METADATA_FILENAME, container_client)
    logger.info(
        f"repo_path: {config.repo_path}\nmetadata_path: {config.metadata_path}\nprdata_path: {config.prdata_path}"
    )
    download_repo_at_base_commit(metadata, config.repo_path)
    owner, repo_name, pr_number = unpack_instance_id(args["instance_id"])
    pr_data = log_and_get_prdata(
        owner=owner,
        repo_name=repo_name,
        pr_number=pr_number,
        run=run,
        config=config,
    )

    pr_files = log_and_get_prfiles(
        owner=owner,
        repo_name=repo_name,
        pr_number=pr_number,
        run=run,
        config=config,
    )

    if "merge_commit" not in metadata:
        metadata["merge_commit"] = pr_data["merge_commit_sha"]
    if "pull_number" not in metadata:
        metadata["pull_number"] = pr_number

    patch_fix, patch_test = log_and_extract_patches(pr_data, run)
    if "patch" in metadata:
        metadata["raw_patch"] = patch_fix
        metadata["raw_test_patch"] = patch_test
        if metadata["patch"] != patch_fix:
            logger.info("Patch from GH doesn't match patch from data")
            logger.info(f"gh_patch = {patch_fix}")
            logger.info(f"swe_bench_patch = {metadata['patch']}")
            run.log("patch_matches", 0)
        else:
            run.log("patch_matches", 1)
        if metadata["test_patch"] != patch_test:
            logger.info("Test patch from GH doesn't match test patch from data")
            logger.info(f"gh_patch_test = {patch_test}")
            logger.info(f"swe_bench_patch_test = {metadata['test_patch']}")
            run.log("test_patch_matches", 0)
        else:
            run.log("test_patch_matches", 1)
        run.flush()
    else:
        metadata["patch"] = patch_fix
        metadata["test_patch"] = patch_test

    # Stats are based on the base_commit
    repo_files = [p for p in config.repo_path.rglob("*") if p.is_file()]
    metadata["filenames"] = [str(p.relative_to(config.repo_path)) for p in repo_files]
    metadata["number_of_files"] = len(repo_files)
    metadata["language_count"] = get_language_count(config.repo_path)

    dumps_all_objs(metadata)
    metadata = WorkspaceBenchInstance.model_validate(metadata)
    metadata_as_json = metadata.model_dump()
    return metadata_as_json, pr_data, pr_files


def dump_files(config: SWEBenchCuratorConfig, metadata: dict, pr_data: dict, pr_files: dict):
    """Tar repo, dumps metadata to file (and dumps all dicts and lists to str),
    and dumps the pull request data from the GitHub API to file.
    """
    shutil.make_archive(config.repo_path, "tar", config.repo_path)
    dump_json(config.metadata_path, metadata)
    dump_json(config.prdata_path, pr_data)
    dump_json(config.prfiles_path, pr_files)


def log_and_get_prdata(
    owner: str, repo_name: str, pr_number: str, run: Run, config: SWEBenchCuratorConfig
) -> dict:
    """Get pull request data from GitHub and log status code of request."""
    use_token = False if config.is_local else True
    pr_data_response = get_pr_data(
        owner=owner,
        repo=repo_name,
        pull_number=pr_number,
        client_id=config.CLIENT_ID,
        use_token=use_token,
    )
    log_status_code("status_code_prdata", pr_data_response, run)

    return pr_data_response.body


def log_and_get_prfiles(
    owner: str, repo_name: str, pr_number: str, run: Run, config: SWEBenchCuratorConfig
) -> dict:
    """Get pull request data from GitHub and log status code of request."""
    use_token = False if config.is_local else True

    pr_files_response = get_pr_files(
        owner=owner,
        repo=repo_name,
        pull_number=pr_number,
        client_id=config.CLIENT_ID,
        use_token=use_token,
    )
    log_status_code("status_code_prfiles", pr_files_response, run)
    return pr_files_response.body


def log_status_code(metric_name: str, api_response: APIResponse, run: Run):
    """Log the status code of the API response to the AML Run."""
    logger.info(f"Status code: {api_response.status_code}")
    run.log(metric_name, api_response.status_code)
    run.flush()
    if api_response.status_code != 200:
        raise Exception(api_response.body["error"]["message"])


def log_and_extract_patches(pr_data: dict, run: Run) -> tuple[str, str]:
    """Extract non-test patch and test patch and log status of success
    of using PatchSet to parse the diff.
    """
    try:
        patch_fix, patch_test = extract_patches(pr_data)
        run.log("unidiff_success", 1)
        run.flush()
    except UnidiffParseError as e:
        logger.error("Encountered Unidiff Parse Error")
        run.log("unidiff_success", 0)
        run.flush()
        raise e
    return patch_fix, patch_test


def download_repo_at_base_commit(metadata: dict, repo_path: str) -> None:
    """Clone and checkout repo at base commit."""
    base_commit = metadata["base_commit"]
    logger.info(
        f"Cloning repo {metadata['repo']} to {repo_path} and checking out base commit {base_commit}"
    )
    repo_url = f"https://github.com/{metadata['repo']}.git"
    checkout_repo_at_commit(repo_url=repo_url, repo_path=repo_path, commit=base_commit)


if __name__ == "__main__":
    args = parse_args()
    main(vars(args))
