from pathlib import Path

from azure.ai.ml.entities import CommandComponent
from code_data_utils import PACKAGE_ROOT

folder = Path(__file__).parent

command = (
    "cd code_data_utils && pip install -e . && "
    "python -m code_data_utils.components.swe_bench_data_curator.main "
    " --base_path ${{inputs.base_path}}"
    " --split ${{inputs.split}}"
    " --instance_id ${{inputs.instance_id}}"
    " --base_commit_sha ${{inputs.base_commit_sha}}"
    " --metadata ${{inputs.metadata}}"
)

inputs = {
    "base_path": {
        "type": "string",
        "description": "name of the base dir to save outputs to",
    },
    "split": {
        "type": "string",
        "description": "the split, e.g. copilot_ws, swe_bench_verified",
    },
    "instance_id": {
        "type": "string",
        "description": "the  instance id",
    },
    "base_commit_sha": {
        "type": "string",
        "description": "the base commit sha of the repo",
    },
    "metadata": {
        "type": "string",
        "description": "a jsonl file that contains the metadata of a SWE Bench like record",
    },
}

swe_bench_data_curator = CommandComponent(
    name=folder.stem,
    display_name=folder.stem,
    description="This component is used to curate the SWE Bench dataset",
    tags={"tag": "code-model", "owner": "data-prep"},
    version="2",
    inputs=inputs,
    outputs={"output_dataset": {"type": "uri_folder"}},
    command=command,
    code=PACKAGE_ROOT,
    environment="swe_bench_data_curator:1",
)
