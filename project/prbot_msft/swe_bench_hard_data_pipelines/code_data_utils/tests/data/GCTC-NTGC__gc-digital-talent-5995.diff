diff --git "a/.github/ISSUE_TEMPLATE/ \360\237\220\233-bug-report.md" "b/.github/ISSUE_TEMPLATE/ \360\237\220\233-bug-report.md"
index 29b0c7755d1..e55b938d48b 100644
--- "a/.github/ISSUE_TEMPLATE/ \360\237\220\233-bug-report.md"	
+++ "b/.github/ISSUE_TEMPLATE/ \360\237\220\233-bug-report.md"	
@@ -40,3 +40,7 @@ A set of assumptions which, when tested, verify that the bug was addressed.
 
 - [ ] Criteria 1
 - [ ] Criteria 2
+
+## 🛑 Blockers
+
+Blocked by {issue numbers}
diff --git "a/.github/ISSUE_TEMPLATE/\342\231\273\357\270\217-debt-refactor.md" "b/.github/ISSUE_TEMPLATE/\342\231\273\357\270\217-debt-refactor.md"
index f8fa5b6a216..2a1d6233af0 100644
--- "a/.github/ISSUE_TEMPLATE/\342\231\273\357\270\217-debt-refactor.md"
+++ "b/.github/ISSUE_TEMPLATE/\342\231\273\357\270\217-debt-refactor.md"
@@ -28,3 +28,7 @@ A set of assumptions which, when tested, verify that the debt was addressed and
 
 - [ ] Criteria 1
 - [ ] Criteria 2
+
+## 🛑 Blockers
+
+Blocked by {issue numbers}
diff --git "a/.github/ISSUE_TEMPLATE/\342\234\250-feature-request.md" "b/.github/ISSUE_TEMPLATE/\342\234\250-feature-request.md"
index a8292f253a7..fcd0be6345d 100644
--- "a/.github/ISSUE_TEMPLATE/\342\234\250-feature-request.md"
+++ "b/.github/ISSUE_TEMPLATE/\342\234\250-feature-request.md"
@@ -37,11 +37,6 @@ A set of assumptions which, when tested, verify that the feature was properly im
 - [ ] Criteria 1
 - [ ] Criteria 2
 
-## :stop_sign: Blockers
-
-List any issues which must be completed before this one.
-
-```[tasklist]
-### Blockers
-```
+## 🛑 Blockers
 
+Blocked by {issue numbers}
diff --git "a/.github/ISSUE_TEMPLATE/\360\237\233\240\357\270\217-tooling.md" "b/.github/ISSUE_TEMPLATE/\360\237\233\240\357\270\217-tooling.md"
index 398e414969f..975e3e1b2e9 100644
--- "a/.github/ISSUE_TEMPLATE/\360\237\233\240\357\270\217-tooling.md"
+++ "b/.github/ISSUE_TEMPLATE/\360\237\233\240\357\270\217-tooling.md"
@@ -24,3 +24,7 @@ A set of assumptions which, when tested, verify that the debt tooling was proper
 
 - [ ] Criteria 1
 - [ ] Criteria 2
+
+## 🛑 Blockers
+
+Blocked by {issue numbers}
diff --git "a/.github/ISSUE_TEMPLATE/\360\237\247\252-tests.md" "b/.github/ISSUE_TEMPLATE/\360\237\247\252-tests.md"
index 049014b10aa..42dc0ac56f5 100644
--- "a/.github/ISSUE_TEMPLATE/\360\237\247\252-tests.md"
+++ "b/.github/ISSUE_TEMPLATE/\360\237\247\252-tests.md"
@@ -24,3 +24,7 @@ A set of assumptions which, when tested, verify that the debt tests were properl
 
 - [ ] Criteria 1
 - [ ] Criteria 2
+
+## 🛑 Blockers
+
+Blocked by {issue numbers}