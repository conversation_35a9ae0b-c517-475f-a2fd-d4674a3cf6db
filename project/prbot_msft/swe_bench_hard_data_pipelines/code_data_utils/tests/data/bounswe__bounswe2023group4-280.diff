diff --git a/prediction-polls/frontend/src/Assets/icons/ArrowDown.svg b/prediction-polls/frontend/src/Assets/icons/ArrowDown.svg
new file mode 100644
index 00000000..b9701aaa
--- /dev/null
+++ b/prediction-polls/frontend/src/Assets/icons/ArrowDown.svg
@@ -0,0 +1,3 @@
+<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M39.5833 18.75L25 33.3333L10.4167 18.75" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
+</svg>
diff --git a/prediction-polls/frontend/src/Assets/icons/ArrowUp.svg b/prediction-polls/frontend/src/Assets/icons/ArrowUp.svg
new file mode 100644
index 00000000..4b0b7779
--- /dev/null
+++ b/prediction-polls/frontend/src/Assets/icons/ArrowUp.svg
@@ -0,0 +1,5 @@
+<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
+<g id="Arrow / Chevron_Up">
+<path id="Vector" d="M10.4167 33.3333L25 18.75L39.5833 33.3333" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
+</g>
+</svg>
diff --git a/prediction-polls/frontend/src/Assets/icons/Comment.svg b/prediction-polls/frontend/src/Assets/icons/Comment.svg
new file mode 100644
index 00000000..e3f3b329
--- /dev/null
+++ b/prediction-polls/frontend/src/Assets/icons/Comment.svg
@@ -0,0 +1,5 @@
+<svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
+<g id="Communication / Chat_Circle">
+<path id="Vector" d="M9.38721 23.2523C11.0391 24.1472 12.9557 24.6593 14.9996 24.6593C21.2128 24.6593 26.25 19.9278 26.25 14.0911C26.25 8.25448 21.2132 3.52295 15 3.52295C8.7868 3.52295 3.75 8.25448 3.75 14.0911C3.75 16.0112 4.29505 17.8116 5.24774 19.3633L5.25143 19.3693C5.3431 19.5186 5.38932 19.5939 5.41026 19.6651C5.43001 19.7322 5.43552 19.7925 5.43047 19.862C5.42503 19.9366 5.39825 20.014 5.34335 20.1688L4.38232 22.8771L4.38111 22.8807C4.17835 23.4521 4.07696 23.7378 4.14923 23.9282C4.21224 24.0942 4.35211 24.2252 4.52881 24.2844C4.73102 24.3521 5.03381 24.2573 5.63943 24.0677L5.64697 24.0651L8.53006 23.1623C8.69422 23.1109 8.77767 23.0848 8.85699 23.0797C8.93094 23.075 8.99472 23.0812 9.06616 23.0998C9.1421 23.1195 9.22229 23.1629 9.38204 23.2495L9.38721 23.2523Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
+</g>
+</svg>
diff --git a/prediction-polls/frontend/src/Assets/icons/CreateIcon.jsx b/prediction-polls/frontend/src/Assets/icons/CreateIcon.jsx
index 21a68705..e9203477 100644
--- a/prediction-polls/frontend/src/Assets/icons/CreateIcon.jsx
+++ b/prediction-polls/frontend/src/Assets/icons/CreateIcon.jsx
@@ -1,7 +1,20 @@
-import styles from './Icon.module.css';
+import styles from "./Icon.module.css";
 const CreateIcon = ({ width, height }) => (
-<svg width={width ?? "50"} height={height ?? "50"} viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg" >
-<path className={styles.iconStroke} d="M20.8338 8.33317H15.0004C12.6669 8.33317 11.4992 8.33317 10.608 8.78731C9.82394 9.18678 9.18699 9.82374 8.78751 10.6077C8.33337 11.499 8.33337 12.6667 8.33337 15.0002V35.0002C8.33337 37.3338 8.33337 38.5 8.78751 39.3913C9.18699 40.1753 9.82394 40.8133 10.608 41.2128C11.4984 41.6665 12.6646 41.6665 14.9936 41.6665H35.0065C37.3355 41.6665 38.5 41.6665 39.3904 41.2128C40.1745 40.8133 40.8135 40.1747 41.213 39.3907C41.6667 38.5002 41.6667 37.3353 41.6667 35.0063V29.1665M33.3334 10.4165L20.8334 22.9165V29.1665H27.0834L39.5834 16.6665M33.3334 10.4165L39.5834 4.1665L45.8334 10.4165L39.5834 16.6665M33.3334 10.4165L39.5834 16.6665" stroke="#464B4F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
-</svg>
+  <svg
+    width={width ?? "50"}
+    height={height ?? "50"}
+    viewBox="0 0 50 50"
+    fill="none"
+    xmlns="http://www.w3.org/2000/svg"
+  >
+    <path
+      className={styles.iconStroke}
+      d="M20.8338 8.33317H15.0004C12.6669 8.33317 11.4992 8.33317 10.608 8.78731C9.82394 9.18678 9.18699 9.82374 8.78751 10.6077C8.33337 11.499 8.33337 12.6667 8.33337 15.0002V35.0002C8.33337 37.3338 8.33337 38.5 8.78751 39.3913C9.18699 40.1753 9.82394 40.8133 10.608 41.2128C11.4984 41.6665 12.6646 41.6665 14.9936 41.6665H35.0065C37.3355 41.6665 38.5 41.6665 39.3904 41.2128C40.1745 40.8133 40.8135 40.1747 41.213 39.3907C41.6667 38.5002 41.6667 37.3353 41.6667 35.0063V29.1665M33.3334 10.4165L20.8334 22.9165V29.1665H27.0834L39.5834 16.6665M33.3334 10.4165L39.5834 4.1665L45.8334 10.4165L39.5834 16.6665M33.3334 10.4165L39.5834 16.6665"
+      stroke="#464B4F"
+      strokeWidth="2"
+      strokeLinecap="round"
+      strokeLinejoin="round"
+    />
+  </svg>
 );
-export default CreateIcon;
\ No newline at end of file
+export default CreateIcon;
diff --git a/prediction-polls/frontend/src/Assets/icons/FeedIcon.jsx b/prediction-polls/frontend/src/Assets/icons/FeedIcon.jsx
index ede59bb6..b7c0ee78 100644
--- a/prediction-polls/frontend/src/Assets/icons/FeedIcon.jsx
+++ b/prediction-polls/frontend/src/Assets/icons/FeedIcon.jsx
@@ -11,9 +11,9 @@ const FeedIcon = ({width, height }) => (
     className={styles.iconStroke}
       d="M41.6667 35.4169V23.8587C41.6667 22.7455 41.6658 22.1886 41.5304 21.6706C41.4104 21.2115 41.2135 20.7771 40.9471 20.3845C40.6464 19.9416 40.2283 19.5742 39.3905 18.8412L29.3905 10.0912C27.8351 8.73017 27.0573 8.05001 26.1821 7.79117C25.4109 7.5631 24.5888 7.5631 23.8176 7.79117C22.943 8.04982 22.1664 8.72933 20.6133 10.0883L10.61 18.8412C9.77221 19.5742 9.35429 19.9416 9.05359 20.3845C8.78711 20.7771 8.58868 21.2115 8.46873 21.6706C8.33337 22.1886 8.33337 22.7455 8.33337 23.8587V35.4169C8.33337 37.3583 8.33337 38.3287 8.65054 39.0944C9.07343 40.1153 9.88404 40.9274 10.905 41.3503C11.6707 41.6675 12.6414 41.6675 14.5828 41.6675C16.5243 41.6675 17.496 41.6675 18.2618 41.3503C19.2827 40.9274 20.0931 40.1155 20.516 39.0946C20.8332 38.3288 20.8334 37.3581 20.8334 35.4167V33.3334C20.8334 31.0322 22.6989 29.1667 25 29.1667C27.3012 29.1667 29.1667 31.0322 29.1667 33.3334V35.4167C29.1667 37.3581 29.1667 38.3288 29.4839 39.0946C29.9068 40.1155 30.7174 40.9274 31.7383 41.3503C32.504 41.6675 33.4747 41.6675 35.4162 41.6675C37.3576 41.6675 38.3294 41.6675 39.0951 41.3503C40.116 40.9274 40.9264 40.1153 41.3493 39.0944C41.6665 38.3287 41.6667 37.3583 41.6667 35.4169Z"
       stroke="#464B4F"
-      stroke-width="2"
-      stroke-linecap="round"
-      stroke-linejoin="round"
+      strokeWidth="2"
+      strokeLinecap="round"
+      strokeLinejoin="round"
     />
   </svg>
 );
diff --git a/prediction-polls/frontend/src/Assets/icons/NotificationsIcon.jsx b/prediction-polls/frontend/src/Assets/icons/NotificationsIcon.jsx
index f28cd13f..7cc33028 100644
--- a/prediction-polls/frontend/src/Assets/icons/NotificationsIcon.jsx
+++ b/prediction-polls/frontend/src/Assets/icons/NotificationsIcon.jsx
@@ -11,9 +11,9 @@ const NotificationIcon = ({ color, width, height }) => (
       className={styles.iconStroke}
       d="M31.25 35.4168V37.5002C31.25 40.9519 28.4518 43.7502 25 43.7502C21.5483 43.7502 18.75 40.9519 18.75 37.5002V35.4168M31.25 35.4168H18.75M31.25 35.4168H38.7303C39.5272 35.4168 39.9275 35.4168 40.2502 35.308C40.8666 35.1001 41.3489 34.6161 41.5568 33.9997C41.6661 33.6757 41.6661 33.2741 41.6661 32.4709C41.6661 32.1194 41.6657 31.9437 41.6382 31.7761C41.5863 31.4594 41.4634 31.1592 41.2762 30.8985C41.1773 30.7608 41.0516 30.6351 40.8038 30.3873L39.9923 29.5758C39.7305 29.3139 39.5834 28.9588 39.5834 28.5885V20.8335C39.5834 12.7793 33.0542 6.25014 25 6.25016C16.9459 6.25018 10.4167 12.7794 10.4167 20.8335V28.5886C10.4167 28.9588 10.2693 29.3139 10.0075 29.5758L9.196 30.3872C8.94748 30.6358 8.82304 30.7607 8.724 30.8986C8.53682 31.1593 8.41284 31.4594 8.36087 31.7761C8.33337 31.9437 8.33337 32.1194 8.33337 32.4709C8.33337 33.2741 8.33337 33.6757 8.44265 33.9996C8.65056 34.616 9.13504 35.1001 9.75142 35.308C10.0741 35.4168 10.473 35.4168 11.2699 35.4168H18.75M37.5387 4.19531C40.4121 6.36055 42.6757 9.23301 44.1092 12.5329M12.4625 4.19531C9.5891 6.36055 7.32545 9.23301 5.89197 12.5329"
       stroke="#464B4F"
-      stroke-width="2"
-      stroke-linecap="round"
-      stroke-linejoin="round"
+      strokeWidth="2"
+      strokeLinecap="round"
+      strokeLinejoin="round"
     />
   </svg>
 );
diff --git a/prediction-polls/frontend/src/Assets/icons/ProfileIcon.jsx b/prediction-polls/frontend/src/Assets/icons/ProfileIcon.jsx
index d2f53836..f1f0d681 100644
--- a/prediction-polls/frontend/src/Assets/icons/ProfileIcon.jsx
+++ b/prediction-polls/frontend/src/Assets/icons/ProfileIcon.jsx
@@ -11,9 +11,9 @@ const ProfileIcon = ({ width, height }) => (
       className={styles.iconStroke}
       d="M41.6667 43.75C41.6667 37.997 34.2048 33.3333 25 33.3333C15.7953 33.3333 8.33337 37.997 8.33337 43.75M25 27.0833C19.2471 27.0833 14.5834 22.4196 14.5834 16.6667C14.5834 10.9137 19.2471 6.25 25 6.25C30.753 6.25 35.4167 10.9137 35.4167 16.6667C35.4167 22.4196 30.753 27.0833 25 27.0833Z"
       stroke="#464B4F"
-      stroke-width="2"
-      stroke-linecap="round"
-      stroke-linejoin="round"
+      strokeWidth="2"
+      strokeLinecap="round"
+      strokeLinejoin="round"
     />
   </svg>
 );
diff --git a/prediction-polls/frontend/src/Assets/icons/SettingsIcon.jsx b/prediction-polls/frontend/src/Assets/icons/SettingsIcon.jsx
index 3de01e40..62d77a68 100644
--- a/prediction-polls/frontend/src/Assets/icons/SettingsIcon.jsx
+++ b/prediction-polls/frontend/src/Assets/icons/SettingsIcon.jsx
@@ -10,17 +10,17 @@ const SettingsIcon = ({ width, height }) => (
     <path
       d="M42.3957 18.5894L41.6328 18.165C41.5143 18.0991 41.456 18.066 41.3988 18.0317C40.8299 17.6909 40.3503 17.2201 40.0004 16.6567C39.9652 16.6 39.932 16.5406 39.8642 16.4231C39.7964 16.3058 39.762 16.2463 39.7305 16.1875C39.4166 15.6014 39.2468 14.9482 39.2367 14.2835C39.2356 14.2166 39.2359 14.1483 39.2382 14.0126L39.2531 13.1266C39.2769 11.7089 39.2889 10.9978 39.0896 10.3596C38.9127 9.79278 38.6166 9.27068 38.2212 8.82761C37.7743 8.32677 37.1557 7.96946 35.9172 7.25576L34.8884 6.66293C33.6533 5.95121 33.0355 5.59523 32.3798 5.45952C31.7997 5.33945 31.2011 5.34502 30.6231 5.47477C29.9707 5.62122 29.3607 5.98648 28.1414 6.71658L28.1345 6.71989L27.3973 7.16127C27.2808 7.23106 27.2218 7.26624 27.1634 7.29872C26.5836 7.62105 25.9364 7.79928 25.2734 7.82056C25.2066 7.8227 25.1386 7.8227 25.0027 7.8227C24.8677 7.8227 24.7968 7.8227 24.7301 7.82056C24.0657 7.79919 23.4171 7.61999 22.8365 7.29632C22.778 7.2637 22.7201 7.22824 22.6033 7.15813L21.8614 6.71277C20.6338 5.97576 20.0191 5.6067 19.3631 5.45952C18.7827 5.3293 18.182 5.3257 17.5998 5.44731C16.9424 5.58462 16.3245 5.94324 15.0887 6.66048L15.0832 6.66293L14.0673 7.25258L14.056 7.25946C12.8313 7.97024 12.2175 8.32649 11.7744 8.82529C11.3811 9.26791 11.0872 9.78919 10.9112 10.3545C10.7124 10.9936 10.723 11.7062 10.7469 13.1306L10.7618 14.0153C10.7641 14.1493 10.768 14.2159 10.767 14.2818C10.7572 14.9479 10.5851 15.6024 10.2703 16.1895C10.2392 16.2476 10.2056 16.3056 10.1386 16.4216C10.0716 16.5377 10.0391 16.5954 10.0043 16.6514C9.65284 17.2178 9.17112 17.6914 8.59861 18.0328C8.54198 18.0665 8.4823 18.099 8.36503 18.164L7.61177 18.5814C6.35851 19.2759 5.73202 19.6235 5.27616 20.1181C4.87288 20.5557 4.56823 21.0747 4.38227 21.6399C4.17206 22.2789 4.17224 22.9954 4.17549 24.4282L4.17815 25.5993C4.18138 27.0226 4.18582 27.7337 4.3965 28.3684C4.58289 28.9298 4.88531 29.4458 5.28633 29.8807C5.73962 30.3723 6.35987 30.7177 7.60363 31.4096L8.35018 31.8249C8.47723 31.8956 8.54116 31.9305 8.60243 31.9674C9.16975 32.309 9.64769 32.7813 9.99621 33.3444C10.0339 33.4053 10.07 33.4684 10.1423 33.5947C10.2137 33.7194 10.2502 33.7818 10.2832 33.8443C10.5888 34.4228 10.7524 35.0656 10.7635 35.7197C10.7647 35.7904 10.7637 35.8619 10.7613 36.0056L10.7469 36.8546C10.7228 38.2839 10.7123 38.9994 10.9123 39.6402C11.0893 40.2071 11.3851 40.7291 11.7805 41.1722C12.2274 41.6731 12.847 42.0301 14.0856 42.7439L15.1141 43.3366C16.3492 44.0483 16.9666 44.4038 17.6223 44.5396C18.2024 44.6596 18.8013 44.6549 19.3793 44.5252C20.0326 44.3785 20.6448 44.012 21.8676 43.2798L22.6047 42.8385C22.7213 42.7686 22.7804 42.7336 22.8388 42.7011C23.4185 42.3788 24.0651 42.1996 24.7281 42.1784C24.7949 42.1762 24.8628 42.1762 24.9988 42.1762C25.135 42.1762 25.2029 42.1762 25.2698 42.1784C25.9343 42.1998 26.5848 42.3795 27.1654 42.7032C27.2165 42.7316 27.2677 42.7624 27.3576 42.8164L28.1412 43.2869C29.369 44.024 29.9824 44.3919 30.6385 44.5391C31.2188 44.6693 31.82 44.6747 32.4022 44.553C33.0594 44.4158 33.6786 44.0564 34.9137 43.3396L35.9449 42.7411C37.1704 42.0298 37.7849 41.6732 38.2282 41.1742C38.6214 40.7316 38.9157 40.2106 39.0917 39.6452C39.2891 39.0108 39.2772 38.3037 39.2536 36.8998L39.2382 35.9842C39.2359 35.8502 39.2356 35.7836 39.2366 35.7177C39.2465 35.0516 39.4157 34.3966 39.7305 33.8095C39.7616 33.7515 39.7954 33.693 39.8622 33.5774C39.9292 33.4614 39.9639 33.4035 39.9987 33.3475C40.3502 32.7811 40.8324 32.3071 41.4049 31.9657C41.4609 31.9324 41.5184 31.9005 41.6329 31.837L41.6368 31.8352L42.3901 31.4178C43.6434 30.7233 44.2711 30.3753 44.7269 29.8807C45.1302 29.4431 45.4344 28.9249 45.6204 28.3596C45.8294 27.7244 45.8278 27.0121 45.8245 25.596L45.8218 24.3997C45.8186 22.9765 45.8168 22.2654 45.6062 21.6308C45.4198 21.0693 45.1157 20.5534 44.7146 20.1184C44.2618 19.6273 43.6407 19.2818 42.3993 18.5912L42.3957 18.5894Z"
       stroke="#464B4F"
-      stroke-width="2"
-      stroke-linecap="round"
-      stroke-linejoin="round"
+      strokeWidth="2"
+      strokeLinecap="round"
+      strokeLinejoin="round"
       className={styles.iconStroke}
     />
     <path
       d="M16.6674 24.9999C16.6674 29.6023 20.3983 33.3333 25.0007 33.3333C29.6031 33.3333 33.334 29.6023 33.334 24.9999C33.334 20.3975 29.6031 16.6666 25.0007 16.6666C20.3983 16.6666 16.6674 20.3975 16.6674 24.9999Z"
       stroke="#464B4F"
-      stroke-width="2"
-      stroke-linecap="round"
-      stroke-linejoin="round"
+      strokeWidth="2"
+      strokeLinecap="round"
+      strokeLinejoin="round"
       className={styles.iconStroke}
     />
   </svg>
diff --git a/prediction-polls/frontend/src/Assets/icons/Share.svg b/prediction-polls/frontend/src/Assets/icons/Share.svg
new file mode 100644
index 00000000..1f5089b5
--- /dev/null
+++ b/prediction-polls/frontend/src/Assets/icons/Share.svg
@@ -0,0 +1,3 @@
+<svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M11.25 7.16581L15 3.58301M15 3.58301L18.75 7.16581M15 3.58301V15.5257M8.75029 11.9429C7.58543 11.9429 7.00301 11.9429 6.54358 12.1247C5.93101 12.3671 5.44404 12.8324 5.1903 13.4176C5 13.8566 5 14.4128 5 15.5257V21.2582C5 22.5959 5 23.2643 5.27248 23.7752C5.51217 24.2246 5.89434 24.5907 6.36475 24.8197C6.899 25.0798 7.59874 25.0798 8.99614 25.0798H21.0045C22.4019 25.0798 23.1006 25.0798 23.6349 24.8197C24.1053 24.5907 24.4881 24.2246 24.7278 23.7752C25 23.2648 25 22.5969 25 21.2619V15.5257C25 14.4128 24.9999 13.8566 24.8096 13.4176C24.5558 12.8324 24.0693 12.3671 23.4567 12.1247C22.9973 11.9429 22.4149 11.9429 21.25 11.9429" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
+</svg>
diff --git a/prediction-polls/frontend/src/Assets/icons/VoteIcon.jsx b/prediction-polls/frontend/src/Assets/icons/VoteIcon.jsx
index b6c1cd43..36b866e6 100644
--- a/prediction-polls/frontend/src/Assets/icons/VoteIcon.jsx
+++ b/prediction-polls/frontend/src/Assets/icons/VoteIcon.jsx
@@ -1,5 +1,5 @@
 import styles from "./Icon.module.css";
-const VoteIcon = ({width, height }) => (
+const VoteIcon = ({ width, height }) => (
   <svg
     width={width ?? "50"}
     height={height ?? "50"}
@@ -9,10 +9,10 @@ const VoteIcon = ({width, height }) => (
   >
     <path
       d="M16.6667 25.0002L22.9167 31.2502L33.3334 18.7502M8.33337 35.0006V15.0006C8.33337 12.667 8.33337 11.4994 8.78751 10.6081C9.18699 9.82406 9.82394 9.18711 10.608 8.78764C11.4992 8.3335 12.6669 8.3335 15.0004 8.3335H35.0004C37.334 8.3335 38.4991 8.3335 39.3904 8.78764C40.1745 9.18711 40.8135 9.82406 41.213 10.6081C41.6667 11.4985 41.6667 12.6647 41.6667 14.9937V35.0076C41.6667 37.3366 41.6667 38.5012 41.213 39.3916C40.8135 40.1756 40.1745 40.8137 39.3904 41.2131C38.5 41.6668 37.3355 41.6668 35.0065 41.6668H14.9936C12.6646 41.6668 11.4984 41.6668 10.608 41.2131C9.82394 40.8137 9.18699 40.1756 8.78751 39.3916C8.33337 38.5003 8.33337 37.3341 8.33337 35.0006Z"
-      stroke= "#464B4F"
-      stroke-width="2"
-      stroke-linecap="round"
-      stroke-linejoin="round"
+      stroke="#464B4F"
+      strokeWidth="2"
+      strokeLinecap="round"
+      strokeLinejoin="round"
       className={styles.iconStroke}
     />
   </svg>
diff --git a/prediction-polls/frontend/src/Assets/icons/Warning.svg b/prediction-polls/frontend/src/Assets/icons/Warning.svg
new file mode 100644
index 00000000..0f958be6
--- /dev/null
+++ b/prediction-polls/frontend/src/Assets/icons/Warning.svg
@@ -0,0 +1,3 @@
+<svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M15 10.7483V15.5253M5.47363 18.1525C4.33684 20.0337 3.76861 20.9746 3.85352 21.7465C3.92758 22.4197 4.2974 23.0313 4.87061 23.4292C5.52755 23.8852 6.66363 23.8852 8.93567 23.8852H21.0643C23.3364 23.8852 24.4723 23.8852 25.1292 23.4292C25.7024 23.0313 26.0724 22.4197 26.1465 21.7465C26.2314 20.9746 25.6633 20.0337 24.5265 18.1525L18.4644 8.12064C17.3276 6.23945 16.7589 5.29902 16.0168 4.98336C15.3696 4.70802 14.63 4.70802 13.9827 4.98336C13.241 5.29888 12.6727 6.23933 11.5368 8.11895L5.47363 18.1525ZM15.0635 19.1081V19.2276L14.9377 19.2278V19.1081H15.0635Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
+</svg>
diff --git a/prediction-polls/frontend/src/Components/Menu/index.jsx b/prediction-polls/frontend/src/Components/Menu/index.jsx
index bf2927b9..0cac47b5 100644
--- a/prediction-polls/frontend/src/Components/Menu/index.jsx
+++ b/prediction-polls/frontend/src/Components/Menu/index.jsx
@@ -3,10 +3,10 @@ import Sidebar from "../Sidebar";
 import MobileMenu from "../MobileMenu";
 
 function Menu({currentPage}) {
-  return <div>
+  return <>
     <Sidebar currentPage={currentPage}/>
     <MobileMenu />
-  </div>;
+  </>;
 }
 
 export default Menu;
diff --git a/prediction-polls/frontend/src/Components/PointsButton/PointsButton.module.css b/prediction-polls/frontend/src/Components/PointsButton/PointsButton.module.css
new file mode 100644
index 00000000..be6a2ea0
--- /dev/null
+++ b/prediction-polls/frontend/src/Components/PointsButton/PointsButton.module.css
@@ -0,0 +1,65 @@
+.pointsContainer {
+    position: fixed;
+    top: 10px;
+    right: 50px;
+    max-height: 100vh;
+    border-radius: 10px;
+    overflow: hidden;
+    font-family: Arial, sans-serif;
+    background-color: var(--primary-500);
+    box-shadow: 2px 4px 8px var(--neutral-shadow);
+    z-index: 3;
+}
+  
+.pointsButton {
+    width: 100%;
+    background-color:#2d87da;
+    border: none;
+    color: white;
+    padding: 10px 25px; 
+    text-align: left;
+    cursor: pointer;
+    outline: none;
+    font-size: 16px;
+    display: flex;
+    justify-content: space-between;
+    align-items: center;
+    z-index: 3;
+    font-size: 28px;
+}
+  
+.pointsButton:hover {
+    background-color: var(--primary-400);
+}
+  
+.pointsButton span {
+    font-size: 18px;
+    margin-left: 8px;
+}
+  
+.pointsList {
+    max-height: calc(100vh - 75px);
+    overflow-y: auto;
+}
+  
+.point {
+    padding: 20px 25px; 
+    font-size: 20px;
+    color: var(--neutral-black);
+    background-color: var(--primary-200);
+    border-bottom: 1px solid var(--neutral-50);
+
+}
+  
+.point:last-child {
+    border-bottom: none;
+}
+  
+.point:hover {
+    background-color: var(--primary-100);
+}
+
+.iconStyle {
+    width: 40px;
+    height: 40px;
+}
diff --git a/prediction-polls/frontend/src/Components/PointsButton/index.jsx b/prediction-polls/frontend/src/Components/PointsButton/index.jsx
new file mode 100644
index 00000000..b6737371
--- /dev/null
+++ b/prediction-polls/frontend/src/Components/PointsButton/index.jsx
@@ -0,0 +1,35 @@
+import React, { useState } from "react";
+import styles from "./PointsButton.module.css";
+import { ReactComponent as ArrowDown } from "../../Assets/icons/ArrowDown.svg";
+import { ReactComponent as ArrowUp } from "../../Assets/icons/ArrowUp.svg";
+
+function PointsButton({ points }) {
+  const [isOpen, setIsOpen] = useState(false);
+
+  const GPValue = points.find((point) => "GP" in point).GP; 
+
+  return (
+    <div className={styles.pointsContainer}>
+      <button
+        onClick={() => setIsOpen(!isOpen)}
+        className={styles.pointsButton}
+      >
+        {GPValue} GP <span>{isOpen ? <ArrowUp className={styles.iconStyle}/> : <ArrowDown className={styles.iconStyle}/>}</span>
+      </button>
+      {isOpen && (
+        <div className={styles.pointsList}>
+          {points.slice(1).map((point, index) => {
+            const [category, value] = Object.entries(point)[0]; 
+            return (
+              <div className={styles.point} key={index}>
+                {category}: {value}
+              </div>
+            );
+          })}
+        </div>
+      )}
+    </div>
+  );
+}
+
+export default PointsButton;
diff --git a/prediction-polls/frontend/src/Components/PollCard/PollCard.module.css b/prediction-polls/frontend/src/Components/PollCard/PollCard.module.css
new file mode 100644
index 00000000..529031ea
--- /dev/null
+++ b/prediction-polls/frontend/src/Components/PollCard/PollCard.module.css
@@ -0,0 +1,159 @@
+.card {
+  display: flex;
+  flex-direction: row;
+  justify-content: space-between;
+  width: 90%;
+  box-shadow: 2px 4px 8px var(--neutral-shadow);
+  padding: 30px;
+  border-radius: 8px;
+  gap: 20px;
+  background-color: var(--neutral-pollCard);
+  margin-bottom: 20px;
+}
+.question {
+  display: flex;
+  flex-direction: column;
+  flex-grow: 1;
+}
+.info {
+  display: flex;
+  flex-direction: column;
+}
+.tags {
+  display: flex;
+  flex-wrap: wrap;
+  gap: 20px;
+}
+.creator {
+  display: flex;
+  align-items: center;
+  gap: 5px;
+}
+.creatorImage {
+  width: 35px;
+  height: 35px;
+  border-radius: 100px;
+}
+.creatorName {
+  font-size: 16px;
+  font-weight: 700;
+  color: var(--neutral-black);
+}
+.questionPoints {
+  display: flex;
+  flex-direction: row;
+  flex-wrap: wrap;
+  justify-content: space-between;
+  width: 100%;
+  gap: 3%;
+  align-items: start;
+}
+.question {
+  font-size: 20px;
+  font-weight: 700;
+  width: 75%;
+  color: var(--neutral-black);
+}
+.pointsPlaced {
+  font-size: 14px;
+  padding-top: 10px;
+  font-weight: normal;
+  width: 22%;
+  text-align: center;
+  color: var(--neutral-black);
+  
+}
+.textDescription {
+  font-size: 14px;
+  text-align: right;
+  color: var(--neutral-black);
+}
+.textDetail {
+  font-size: 16px;
+  font-weight: 700;
+  color: var(--warning-500);
+  text-align: right;
+}
+.textGroup {
+  padding-top: 10px;
+}
+.optionList {
+  display: flex;
+  flex-direction: column;
+  gap: 10px;
+  width: 100%;
+}
+
+.actionButtons {
+  display: flex;
+  gap: 10px;
+  justify-content: space-between;
+  padding-top: 30px;
+}
+
+.commentButton,
+.shareButton,
+.reportButton {
+  display: flex;
+  border: none;
+  align-items: center;
+  border-radius: 8px;
+  cursor: pointer;
+  padding: 4px 12px;
+  gap: 5px;
+  color: #ffffff;
+  font-weight: 700;
+  font-size: 20px;
+}
+
+.commentButton {
+  background-color: var(--primary-400); 
+}
+.buttonWrapper {
+  display: flex;
+  flex-direction: column;
+  gap: 3px;
+  align-items: center;
+  justify-content: start;
+}
+
+.commentCount {
+  color: var(--neutral-black);
+  font-size: 14px;
+  font-weight: normal;
+}
+
+.shareButton {
+  background-color: var(--primary-400);
+}
+
+.reportButton {
+  background-color: var(--warning-500);
+}
+
+.commentButton:hover,
+.shareButton:hover,
+.reportButton:hover {
+  opacity: 0.7;
+}
+
+.customOptionWrapper{
+  display: flex;
+  flex-direction: column;
+  width: 100%;
+}
+.customOptionText{
+  font-size: 16px;
+  font-weight: 500;
+}
+.customOption{
+  width: 50%;
+  padding: 10px;
+  box-sizing: border-box;
+  border: 2px solid var(--secondary-200);
+  border-radius: 10px;
+  font-size: 20px;
+  background-color: var(--neutral-pollCard);
+  color: var(--neutral-black);
+
+}
diff --git a/prediction-polls/frontend/src/Components/PollCard/index.jsx b/prediction-polls/frontend/src/Components/PollCard/index.jsx
new file mode 100644
index 00000000..9fa9c569
--- /dev/null
+++ b/prediction-polls/frontend/src/Components/PollCard/index.jsx
@@ -0,0 +1,103 @@
+import React from "react";
+import styles from "./PollCard.module.css";
+import PollTag from "../PollTag";
+import { useNavigate } from "react-router-dom";
+import { ReactComponent as CommentIcon } from "../../Assets/icons/Comment.svg";
+import { ReactComponent as ShareIcon } from "../../Assets/icons/Share.svg";
+import { ReactComponent as ReportIcon } from "../../Assets/icons/Warning.svg";
+import PollOption from "../PollOption";
+
+function PollCard({ PollData }) {
+  const totalPoints = !PollData.isCustomPoll
+    ? PollData.options.reduce((acc, curr) => acc + curr.votes, 0)
+    : 0;
+
+  const navigate = useNavigate();
+  return (
+    <div className={styles.card}>
+      <div className={styles.question}>
+        <div className={styles.tags}>
+          {PollData.tags.map((tag, index) => (
+            <PollTag TagName={tag} key={index} />
+          ))}
+        </div>
+        <div className={styles.questionPoints}>
+          <div className={styles.question}>
+            <p>{PollData.question}</p>
+          </div>
+
+        </div>
+        {!PollData.isCustomPoll ? (
+          <div className={styles.optionList}>
+            {PollData.options.map((option, index) => {
+              const widthPercentage = (option.votes / totalPoints) * 100;
+              return (
+                <PollOption
+                  widthPercentage={widthPercentage}
+                  navigate={navigate}
+                  option={option}
+                  index={index}
+                />
+              );
+            })}
+          </div>
+        ) : (
+          <div className={styles.customOptionWrapper}>
+            <p className={styles.customOptionText}>
+              Enter a {PollData.optionType}
+            </p>
+            <input
+              className={styles.customOption}
+              type={PollData.optionType}
+              onClick={() => navigate("/vote")}
+            ></input>
+          </div>
+        )}
+        <div className={styles.actionButtons}>
+          <div className={styles.buttonWrapper}>
+            <button className={styles.commentButton}>
+              <CommentIcon /> Comments
+            </button>
+            <span className={styles.commentCount}>
+              {`${PollData.comments.length} comment${
+                PollData.comments.length > 1 ? "s" : ""
+              }`}
+            </span>
+          </div>
+
+          <div className={styles.buttonWrapper}>
+            <button className={styles.shareButton}>
+              <ShareIcon /> Share
+            </button>
+          </div>
+          <div className={styles.buttonWrapper}>
+            <button className={styles.reportButton}>
+              <ReportIcon />
+              Report
+            </button>
+          </div>
+        </div>
+      </div>
+      <div className={styles.info}>
+        <div className={styles.creator}>
+          <img
+            src={PollData.creatorImage}
+            alt="user"
+            className={styles.creatorImage}
+          />
+          <div className={styles.creatorName}>{PollData.creatorName}</div>
+        </div>
+        <div className={styles.textGroup}>
+          <p className={styles.textDescription}>Closing In</p>
+          <p className={styles.textDetail}>{PollData.closingDate}</p>
+        </div>
+        <div className={styles.textGroup}>
+          <p className={styles.textDescription}>Reject Votes In</p>
+          <p className={styles.textDetail}>Last {PollData.rejectVotes}</p>
+        </div>
+      </div>
+    </div>
+  );
+}
+
+export default PollCard;
diff --git a/prediction-polls/frontend/src/Components/PollOption/PollOption.module.css b/prediction-polls/frontend/src/Components/PollOption/PollOption.module.css
new file mode 100644
index 00000000..e2f61de4
--- /dev/null
+++ b/prediction-polls/frontend/src/Components/PollOption/PollOption.module.css
@@ -0,0 +1,40 @@
+.option {
+  display: flex;
+
+  width: 100%;
+}
+.optionText {
+  flex-direction: row;
+  justify-content: space-between;
+  align-items: center;
+  border: 2px solid var(--secondary-200);
+  border-radius: 10px;
+  font-size: 16px;
+  font-weight: 500;
+  width: 100%;
+  padding: 0px 10px ;
+  box-sizing: border-box;
+  font-weight: 700;
+  position: relative;
+  cursor: pointer;
+  display: flex;
+}
+.optionPoints {
+  font-size: 14px;
+  font-weight: normal;
+  z-index: 2;
+}
+.backgroundDiv {
+  position: absolute;
+  top: 0;
+  left: 0;
+  height: 100%;
+  background-color: var(--secondary-50);
+  border-radius: 8px;
+  z-index: 1;
+}
+
+.textDiv {
+  z-index: 2;
+  position: relative;
+}
diff --git a/prediction-polls/frontend/src/Components/PollOption/index.jsx b/prediction-polls/frontend/src/Components/PollOption/index.jsx
new file mode 100644
index 00000000..167bb494
--- /dev/null
+++ b/prediction-polls/frontend/src/Components/PollOption/index.jsx
@@ -0,0 +1,19 @@
+import React from "react";
+import styles from "./PollOption.module.css";
+
+function PollOption({ widthPercentage, navigate, option, index }) {
+  return (
+    <div className={styles.optionText} onClick={() => navigate("/vote")}>
+      <div
+        className={styles.backgroundDiv}
+        style={{ width: `${widthPercentage}%` }}
+      ></div>
+      <div className={styles.textDiv}>{option.title}</div>
+      <div className={styles.optionPoints}>
+        <p>{option.votes}</p>
+      </div>
+    </div>
+  );
+}
+
+export default PollOption;
diff --git a/prediction-polls/frontend/src/Components/PollTag/PollTag.module.css b/prediction-polls/frontend/src/Components/PollTag/PollTag.module.css
new file mode 100644
index 00000000..8b9866a3
--- /dev/null
+++ b/prediction-polls/frontend/src/Components/PollTag/PollTag.module.css
@@ -0,0 +1,10 @@
+.pollTag{
+    padding: 8px;
+    background-color: var(--secondary-500);
+    color: var(--neutral-white);
+    align-items: center;
+    border-radius: 8px;
+    font-weight: 700;
+    font-size: 15px;
+    text-align: center;
+}
\ No newline at end of file
diff --git a/prediction-polls/frontend/src/Components/PollTag/index.jsx b/prediction-polls/frontend/src/Components/PollTag/index.jsx
new file mode 100644
index 00000000..4f72fe1f
--- /dev/null
+++ b/prediction-polls/frontend/src/Components/PollTag/index.jsx
@@ -0,0 +1,8 @@
+import React from "react";
+import styles from "./PollTag.module.css";
+
+function PollTag({ TagName }) {
+  return <div className={styles.pollTag}>{TagName}</div>;
+}
+
+export default PollTag;
diff --git a/prediction-polls/frontend/src/Components/Sidebar/Sidebar.module.css b/prediction-polls/frontend/src/Components/Sidebar/Sidebar.module.css
index 2bcf7685..6885b0a8 100644
--- a/prediction-polls/frontend/src/Components/Sidebar/Sidebar.module.css
+++ b/prediction-polls/frontend/src/Components/Sidebar/Sidebar.module.css
@@ -9,6 +9,8 @@
   padding-bottom: 20px;
   background-color: var(--primary-50);
   justify-content: space-between;
+  position: sticky;
+  top: 0;
 }
 
 .menuItem {
@@ -31,7 +33,7 @@
 }
 
 .selectedMenuItem {
-  box-shadow: 2px 2px 4px 0px rgba(0, 0, 0, 0.25);
+  box-shadow: 2px 2px 8px var(--neutral-shadow);
   background-color: var(--primary-100);
 }
 
diff --git a/prediction-polls/frontend/src/MockData/PointList.json b/prediction-polls/frontend/src/MockData/PointList.json
new file mode 100644
index 00000000..457e8e86
--- /dev/null
+++ b/prediction-polls/frontend/src/MockData/PointList.json
@@ -0,0 +1,14 @@
+{
+  "points": [
+    { "GP": 1500 },
+    { "Art": 200 },
+    { "Music": 350 },
+    { "Politics": 250 },
+    { "Earthquake": 180 },
+    { "Science": 275 },
+    { "Technology": 320 },
+    { "History": 210 },
+    { "Literature": 230 },
+    { "Environment": 300 }
+  ]
+}
diff --git a/prediction-polls/frontend/src/MockData/PollData.json b/prediction-polls/frontend/src/MockData/PollData.json
new file mode 100644
index 00000000..1c00355d
--- /dev/null
+++ b/prediction-polls/frontend/src/MockData/PollData.json
@@ -0,0 +1,107 @@
+{
+  "pollList": [
+    {
+      "id": 1,
+      "question": "What is your favorite color?",
+      "tags": ["Color", "Favorite"],
+      "creatorName": "Zehra Kaya",
+      "creatorId": 6327697326,
+      "creatorImage": "https://www.w3schools.com/howto/img_lights_wide.jpg",
+      "isCustomPoll": false,
+      "options": [
+        {
+          "id": 1,
+          "title": "Red",
+          "votes": 10
+        },
+        {
+          "id": 2,
+          "title": "Blue",
+          "votes": 15
+        },
+        {
+          "id": 3,
+          "title": "Green",
+          "votes": 25
+        },
+        {
+          "id": 4,
+          "title": "Yellow",
+          "votes": 35
+        }
+      ],
+      "closingDate": "2023-06-18",
+      "rejectVotes": "5 days",
+      "isOpen": true,
+      "comments": [
+        {
+          "userId": 34892738947,
+          "comment": "I love the color red!"
+        },
+        {
+          "userId": 340537547,
+          "comment": "Blue has always been my favorite."
+        },
+        {
+          "userId": 98432934,
+          "comment": "Why isn't purple on the list?"
+        }
+      ]
+    },
+    {
+      "id": 2,
+      "question": "When is your birthday?",
+      "tags": ["Birthday", "Personal"],
+      "creatorName": "Can Gezer",
+      "creatorId": 6578493045,
+      "creatorImage": "https://www.w3schools.com/howto/img_woods_wide.jpg",
+      "isCustomPoll": true,
+      "optionType": "date",
+      "closingDate": "2023-07-20",
+      "rejectVotes": "6 days",
+      "isOpen": true,
+      "comments": [
+        {
+          "userId": 34892738947,
+          "comment": "I don't like sharing my birthday."
+        },
+        {
+          "userId": 340537547,
+          "comment": "It's in September!"
+        }
+      ]
+    },
+    {
+      "id": 3,
+      "question": "How many books have you read this year?",
+      "tags": ["Reading", "Hobby"],
+      "creatorName": "Pelin Kaya",
+      "creatorId": 9472638473,
+      "creatorImage": "https://www.w3schools.com/howto/img_mountains_wide.jpg",
+      "isCustomPoll": true,
+      "optionType": "number",
+      "closingDate": "2023-08-12",
+      "rejectVotes": "7 days",
+      "isOpen": true,
+      "comments": [
+        {
+          "userId": 34892738947,
+          "comment": "I've read 20 books so far."
+        },
+        {
+          "userId": 34053547,
+          "comment": "Only 5, been a busy year."
+        },
+        {
+          "userId": 2843234,
+          "comment": "I'm at 30 and counting!"
+        },
+        {
+          "userId": 984329,
+          "comment": "I'm at 40!"
+
+        }
+      ]
+    }
+  ]
+}
diff --git a/prediction-polls/frontend/src/Pages/Create/Create.module.css b/prediction-polls/frontend/src/Pages/Create/Create.module.css
index a3061649..a8398eb3 100644
--- a/prediction-polls/frontend/src/Pages/Create/Create.module.css
+++ b/prediction-polls/frontend/src/Pages/Create/Create.module.css
@@ -2,7 +2,6 @@
     display: flex;
     background-color: var(--neutral-white);
     width: 100%;
-    height: 100vh;
 }
 @media (max-width: 640px) {
     .page{
diff --git a/prediction-polls/frontend/src/Pages/Feed/Feed.module.css b/prediction-polls/frontend/src/Pages/Feed/Feed.module.css
index a3061649..ba3df987 100644
--- a/prediction-polls/frontend/src/Pages/Feed/Feed.module.css
+++ b/prediction-polls/frontend/src/Pages/Feed/Feed.module.css
@@ -2,10 +2,16 @@
     display: flex;
     background-color: var(--neutral-white);
     width: 100%;
-    height: 100vh;
 }
 @media (max-width: 640px) {
     .page{
         flex-direction: column;
     }
+}
+.pollList{
+    display: flex;
+    flex-direction: column;
+    padding-left: 20px;
+    width: 60%;
+    margin-top: 30px;
 }
\ No newline at end of file
diff --git a/prediction-polls/frontend/src/Pages/Feed/index.jsx b/prediction-polls/frontend/src/Pages/Feed/index.jsx
index 370575bc..5996afc1 100644
--- a/prediction-polls/frontend/src/Pages/Feed/index.jsx
+++ b/prediction-polls/frontend/src/Pages/Feed/index.jsx
@@ -1,12 +1,22 @@
 import React from "react";
 import Menu from "../../Components/Menu";
 import styles from "./Feed.module.css";
+import pollData from "../../MockData/PollData.json"
+import PollCard from "../../Components/PollCard";
+import PointsButton from "../../Components/PointsButton";
+import pointData from "../../MockData/PointList.json"
 
 function Feed() {
   return (
     <div className={styles.page}>
       <Menu currentPage="Feed" />
-      <h1>Feed Page</h1>
+      <div className = {styles.pollList}>     
+      {
+        pollData.pollList.map((poll, index) => (
+          <PollCard PollData={poll} key={index}/>
+        ))
+      }</div>
+      <PointsButton points={pointData.points}/>
     </div>
   );
 }
diff --git a/prediction-polls/frontend/src/Pages/Leaderboard/Leaderboard.module.css b/prediction-polls/frontend/src/Pages/Leaderboard/Leaderboard.module.css
index a3061649..a8398eb3 100644
--- a/prediction-polls/frontend/src/Pages/Leaderboard/Leaderboard.module.css
+++ b/prediction-polls/frontend/src/Pages/Leaderboard/Leaderboard.module.css
@@ -2,7 +2,6 @@
     display: flex;
     background-color: var(--neutral-white);
     width: 100%;
-    height: 100vh;
 }
 @media (max-width: 640px) {
     .page{
diff --git a/prediction-polls/frontend/src/Pages/Moderation/Moderation.module.css b/prediction-polls/frontend/src/Pages/Moderation/Moderation.module.css
index a3061649..a8398eb3 100644
--- a/prediction-polls/frontend/src/Pages/Moderation/Moderation.module.css
+++ b/prediction-polls/frontend/src/Pages/Moderation/Moderation.module.css
@@ -2,7 +2,6 @@
     display: flex;
     background-color: var(--neutral-white);
     width: 100%;
-    height: 100vh;
 }
 @media (max-width: 640px) {
     .page{
diff --git a/prediction-polls/frontend/src/Pages/Notifications/Notifications.module.css b/prediction-polls/frontend/src/Pages/Notifications/Notifications.module.css
index a3061649..a8398eb3 100644
--- a/prediction-polls/frontend/src/Pages/Notifications/Notifications.module.css
+++ b/prediction-polls/frontend/src/Pages/Notifications/Notifications.module.css
@@ -2,7 +2,6 @@
     display: flex;
     background-color: var(--neutral-white);
     width: 100%;
-    height: 100vh;
 }
 @media (max-width: 640px) {
     .page{
diff --git a/prediction-polls/frontend/src/Pages/Profile/Profile.module.css b/prediction-polls/frontend/src/Pages/Profile/Profile.module.css
index a3061649..a8398eb3 100644
--- a/prediction-polls/frontend/src/Pages/Profile/Profile.module.css
+++ b/prediction-polls/frontend/src/Pages/Profile/Profile.module.css
@@ -2,7 +2,6 @@
     display: flex;
     background-color: var(--neutral-white);
     width: 100%;
-    height: 100vh;
 }
 @media (max-width: 640px) {
     .page{
diff --git a/prediction-polls/frontend/src/Pages/Settings/Settings.module.css b/prediction-polls/frontend/src/Pages/Settings/Settings.module.css
index a3061649..a8398eb3 100644
--- a/prediction-polls/frontend/src/Pages/Settings/Settings.module.css
+++ b/prediction-polls/frontend/src/Pages/Settings/Settings.module.css
@@ -2,7 +2,6 @@
     display: flex;
     background-color: var(--neutral-white);
     width: 100%;
-    height: 100vh;
 }
 @media (max-width: 640px) {
     .page{
diff --git a/prediction-polls/frontend/src/Pages/Vote/Vote.module.css b/prediction-polls/frontend/src/Pages/Vote/Vote.module.css
index a3061649..a8398eb3 100644
--- a/prediction-polls/frontend/src/Pages/Vote/Vote.module.css
+++ b/prediction-polls/frontend/src/Pages/Vote/Vote.module.css
@@ -2,7 +2,6 @@
     display: flex;
     background-color: var(--neutral-white);
     width: 100%;
-    height: 100vh;
 }
 @media (max-width: 640px) {
     .page{
diff --git a/prediction-polls/frontend/src/theme/colors.css b/prediction-polls/frontend/src/theme/colors.css
index 013a535d..9593823f 100644
--- a/prediction-polls/frontend/src/theme/colors.css
+++ b/prediction-polls/frontend/src/theme/colors.css
@@ -36,6 +36,8 @@
     --neutral-800: #363a3d;
     --neutral-900: #292c2f;
     --neutral-black: #000000;
+    --neutral-pollCard:#FFFFFF;
+    --neutral-shadow: rgba(0, 0, 0, 0.25);
   
     /* Warning Colors */
     --warning-50: #fcebeb;
@@ -88,6 +90,9 @@
     --neutral-800: #ced1d2;
     --neutral-900: #eff0f1;
     --neutral-black: #FFFFFF;
+    --neutral-pollCard:#292c2f;
+    --neutral-shadow: rgba(255, 255, 255, 0.25);
+    
   
     /* Dark Theme - Warning Colors */
     --warning-50: #5c1818;
@@ -140,6 +145,8 @@
     --neutral-800: #363a3d;
     --neutral-900: #292c2f;
     --neutral-black: #000000;
+    --neutral-pollCard:#FFFFFF;
+    --neutral-shadow: rgba(0, 0, 0, 0.25);
   
     /* Light Theme - Warning Colors */
     --warning-50: #fcebeb;