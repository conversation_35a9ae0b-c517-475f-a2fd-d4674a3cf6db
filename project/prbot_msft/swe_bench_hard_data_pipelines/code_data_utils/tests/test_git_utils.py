import time
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
from code_data_utils.git_utils import call_api, get_modified_files_from_patch
from code_data_utils.models import APIResponse


def test_get_modified_files_from_patch():
    diff_filename = Path(__file__).parent / "data" / "bounswe__bounswe2023group4-280.diff"
    with open(diff_filename) as f:
        patch_string = f.readlines()

    result = get_modified_files_from_patch(patch_string)
    assert len(result) == 18
    gold_list_of_files = [
        "prediction-polls/frontend/src/Assets/icons/CreateIcon.jsx",
        "prediction-polls/frontend/src/Assets/icons/FeedIcon.jsx",
        "prediction-polls/frontend/src/Assets/icons/NotificationsIcon.jsx",
        "prediction-polls/frontend/src/Assets/icons/ProfileIcon.jsx",
        "prediction-polls/frontend/src/Assets/icons/SettingsIcon.jsx",
        "prediction-polls/frontend/src/Assets/icons/VoteIcon.jsx",
        "prediction-polls/frontend/src/Components/Menu/index.jsx",
        "prediction-polls/frontend/src/Components/Sidebar/Sidebar.module.css",
        "prediction-polls/frontend/src/Pages/Create/Create.module.css",
        "prediction-polls/frontend/src/Pages/Feed/Feed.module.css",
        "prediction-polls/frontend/src/Pages/Feed/index.jsx",
        "prediction-polls/frontend/src/Pages/Leaderboard/Leaderboard.module.css",
        "prediction-polls/frontend/src/Pages/Moderation/Moderation.module.css",
        "prediction-polls/frontend/src/Pages/Notifications/Notifications.module.css",
        "prediction-polls/frontend/src/Pages/Profile/Profile.module.css",
        "prediction-polls/frontend/src/Pages/Settings/Settings.module.css",
        "prediction-polls/frontend/src/Pages/Vote/Vote.module.css",
        "prediction-polls/frontend/src/theme/colors.css",
    ]
    assert set(gold_list_of_files) == set(result)


def test_get_modified_files_from_patch_with_quotes():
    diff_filename = Path(__file__).parent / "data" / "GCTC-NTGC__gc-digital-talent-5995.diff"
    with open(diff_filename) as f:
        patch_string = f.readlines()

    result = get_modified_files_from_patch(patch_string)
    assert len(result) == 5
    gold_list_of_files = [
        ".github/ISSUE_TEMPLATE/ 🐛-bug-report.md",
        ".github/ISSUE_TEMPLATE/♻️-debt-refactor.md",
        ".github/ISSUE_TEMPLATE/✨-feature-request.md",
        ".github/ISSUE_TEMPLATE/🛠️-tooling.md",
        ".github/ISSUE_TEMPLATE/🧪-tests.md",
    ]

    assert set(result) == set(gold_list_of_files)


def test_get_modified_files_from_patch_with_a_slash():
    diff_filename = Path(__file__).parent / "data" / "sympy__sympy-13091.diff"
    with open(diff_filename) as f:
        patch_string = f.readlines()

    result = get_modified_files_from_patch(patch_string)
    assert len(result) == 23
    gold_list_of_files = [
        "sympy/core/basic.py",
        "sympy/core/exprtools.py",
        "sympy/core/numbers.py",
        "sympy/core/tests/test_basic.py",
        "sympy/core/tests/test_numbers.py",
        "sympy/geometry/entity.py",
        "sympy/physics/optics/medium.py",
        "sympy/physics/vector/dyadic.py",
        "sympy/physics/vector/frame.py",
        "sympy/physics/vector/vector.py",
        "sympy/polys/agca/modules.py",
        "sympy/polys/domains/domain.py",
        "sympy/polys/domains/expressiondomain.py",
        "sympy/polys/domains/pythonrational.py",
        "sympy/polys/domains/quotientring.py",
        "sympy/polys/fields.py",
        "sympy/polys/monomials.py",
        "sympy/polys/polyclasses.py",
        "sympy/polys/polytools.py",
        "sympy/polys/rings.py",
        "sympy/polys/rootoftools.py",
        "sympy/tensor/array/ndim_array.py",
        "sympy/utilities/enumerative.py",
    ]

    assert set(result) == set(gold_list_of_files)


# Mock time.sleep to avoid delays in testing
@pytest.fixture(autouse=True)
def mock_sleep():
    with patch("time.sleep", return_value=None) as _mock_sleep:
        yield _mock_sleep


@patch("requests.get")
def test_successful_response(mock_get):
    # Mock a successful response
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {"key": "value"}
    mock_get.return_value = mock_response

    url = "https://api.github.com/some_endpoint"
    headers = {
        "Accept": "application/vnd.github+json",
        "X-GitHub-Api-Version": "2022-11-28",
    }

    result = call_api(url, headers, False)
    mock_get.assert_called_once_with(url, headers=headers)
    assert result == APIResponse(body={"key": "value"}, status_code=200)


@patch("requests.get")
def test_rate_limit_exceeded(mock_get):
    mock_response_200 = MagicMock()
    mock_response_200.status_code = 200
    mock_response_200.json.return_value = {"key": "value"}

    mock_response_403 = MagicMock()
    mock_response_403.status_code = 403

    mock_rate_limit_no_remaining_response = MagicMock()
    mock_rate_limit_no_remaining_response.status_code = 200
    mock_rate_limit_no_remaining_response.json.return_value = {
        "resources": {"core": {"remaining": 0, "reset": time.time() + 1000}}
    }

    mock_rate_limit_response = MagicMock()
    mock_rate_limit_response.status_code = 200
    mock_rate_limit_response.json.return_value = {
        "resources": {"core": {"remaining": 1, "reset": time.time() + 1000}}
    }

    mock_get.side_effect = [
        mock_response_403,
        mock_rate_limit_no_remaining_response,
        mock_response_403,
        mock_rate_limit_response,
        mock_response_200,
    ]

    url = "https://api.github.com/some_endpoint"
    headers = {
        "Accept": "application/vnd.github+json",
        "X-GitHub-Api-Version": "2022-11-28",
    }

    result = call_api(url, headers, False)
    mock_get.assert_any_call("https://api.github.com/rate_limit", headers=headers)
    assert mock_get.call_count == 5
    assert result == APIResponse(body={"key": "value"}, status_code=200)


@patch("requests.get")
def test_failure_response(mock_get):
    mock_response = MagicMock()
    mock_response.status_code = 500
    mock_response.text = "Internal Server Error"
    mock_response.json.return_value = {"error": "Some error"}
    mock_get.return_value = mock_response

    url = "https://api.github.com/some_endpoint"
    headers = {
        "Accept": "application/vnd.github+json",
        "X-GitHub-Api-Version": "2022-11-28",
    }

    result = call_api(url, headers, False)
    mock_get.assert_called_once_with(url, headers=headers)
    assert result == APIResponse(body={"error": {"error": "Some error"}}, status_code=500)
