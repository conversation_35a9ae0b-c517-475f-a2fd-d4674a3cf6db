import pytest
from code_data_utils.base_prompts.regex import parse_model_answer


def stop_inside():
    return """```json
{
  "question": "Has validation been implemented for the necessary fields at the domain level using a separate VO?",
  "useLineNumbers": false,
  "searchRegexp": "validation|domain|VO"
}
StopStopStop
```
"""


def stop_outside():
    return """```json
{
  "question": "Has validation been implemented for the necessary fields at the domain level using a separate VO?",
  "useLineNumbers": false,
  "searchRegexp": "validation|domain|VO"
}
```
StopStopStop
"""


def correct_format():
    return (
        'Answer: { "question": "Does the settings interface include an option to choose the Role Login Redirect '
        + 'Referrer with a note on wp_get_referer() reliability?", "useLineNumbers": false, '
        + '"searchRegexp": "Role|Login|Redirect|Referrer|wp_get_referer" }\nStopStopStop'
    )


def invalid_escape():
    return (
        'Answer: { "question": "Does the .gitignore file include entries for Maven artifacts '
        + 'and \`\`\`${third_parties.dir}\`\`\`?", "useLineNumbers": false, "searchRegexp": '  # noqa: W605
        + '"maven|third_parties\\.dir" }'
    )


@pytest.mark.parametrize(
    "model_ans, question, regex",
    [
        (
            stop_inside(),
            "Has validation been implemented for the necessary fields at the domain level using a separate VO?",
            "validation|domain|VO",
        ),
        (
            correct_format(),
            "Does the settings interface include an option to choose the Role Login Redirect Referrer with a note on wp_get_referer() reliability?",
            "Role|Login|Redirect|Referrer|wp_get_referer",
        ),
        (
            stop_outside(),
            "Has validation been implemented for the necessary fields at the domain level using a separate VO?",
            "validation|domain|VO",
        ),
        (
            invalid_escape(),
            "Does the .gitignore file include entries for Maven artifacts and \\`\\`\\`${third_parties.dir}\\`\\`\\`?",
            "maven|third_parties\\.dir",
        ),
    ],
)
def test_parse_model_answer(model_ans, question, regex):
    parsed_model_ans = parse_model_answer(model_ans)
    assert parsed_model_ans["question"] == question
    assert parsed_model_ans["searchRegexp"] == regex
