import json
import re

from code_data_utils.api_utils import call_aoai
from code_data_utils.logger import get_logger

logger = get_logger(__name__)


def parse_model_answer(model_ans: str) -> dict:
    """Try to parse valid json out of the model's answer."""
    try:
        split_by_answer = model_ans.split("Answer:")
        if len(split_by_answer) > 1:
            without_answer = split_by_answer[1].strip()
        else:
            without_answer = split_by_answer[0].strip()
        json_pattern = r"```(?:json|JSON)?\n?([\s\S]+)\n?```"
        match = re.search(json_pattern, without_answer, flags=re.DOTALL)
        remove_json = match.group(1) if match else without_answer
        remove_stop = remove_json.split("StopStopStop")[0].strip()
        fix_escapes = fix_invalid_escape_sequences(remove_stop)
        model_ans = json.loads(fix_escapes)
    except Exception as e:
        raise Exception(f"Error in parsing model answer: {model_ans}, {e}")
    return model_ans


def fix_invalid_escape_sequences(json_string: str) -> str:
    """Detect and escape any unescaped backslashes (e.g., turns one backslash into two)."""
    json_string = re.sub(r'(?<!\\)\\(?![\\"])', r"\\\\", json_string)
    return json_string


def generate_regex(title: str, description: str, repo: str, credential) -> tuple[str, str]:
    """Call model to generate a regex and question"""
    prompt = make_regex_prompt(title=title, repo_name=repo, description=description)
    logger.info(f"Topic generation prompt about to be sent to the model:\n{prompt}")
    model_ans = call_aoai(prompt=prompt, credential=credential)["choices"][0]["message"][
        "content"
    ].strip()
    logger.info(f"GPT-4o on topic generation:\n{model_ans}")
    model_ans = parse_model_answer(model_ans)
    question = model_ans["question"]
    regex = model_ans["searchRegexp"]
    logger.info(
        f"Extracted question and regex from model answer:\nQuestion: {question}\nRegex: {regex}"
    )
    return question, regex


def make_regex_prompt(title: str, repo_name: str, description: str) -> str:
    """Create a prompt given a title, repo name, and description.
    The purpose of this prompt is to ask the model to summarize an issue into a question.
    """
    prompt = [
        "\nYou are an expert coding assistant. Your job is to write a question characterising accurately, perceptively and incisively whether ",
        "an issue in a code repository is solved or not.",
        "\n",
        "\n§ The Issue",
        "\n",
        f"\nTitle: {title}",
        f"\nRepository: {repo_name}",
        "\nDescription:",
        "\n",
        description,
        "\n",
        "\n§ Instructions",
        "\n",
        "\nUse JSON format in output.",
        "\n",
        '\nIn "question" write a question characterising whether the issue is completed or not. When writing the question use specific terminology ',
        "relevant to the issue, not generalities - that is, don't refer to whether a feature or functionality has been implemented, or a bug has been ",
        'resolved. Just ask whether the specified behavior exists, without using time adverbs such as "now", "still", or "been". And don\'t ',
        "refer to the current repository/project name, since that context is implied by the issue.",
        "\n* If the issue is related to anything that may be harmful to someone physically or emotionally, or to illegal or criminal activity, or is hateful, ",
        'racist, sexist, lewd or violent, or is about generating code or content with these characteristics, then write "redacted" for the question.',
        "\n* Use 40 words or less.",
        "\n* Write one answer only on a single line.",
        "\n* Do not include any phrase to do with the following:",
        "\n  * Nothing related to cloning a repository",
        "\n  * Nothing related to renaming the repo",
        "\n  * Nothing related to checking out a branch",
        "\n  * Nothing related to fetching a branch",
        "\n  * Nothing related to creating branches",
        "\n  * Nothing related to opening files",
        "\n  * Nothing related to saving files",
        "\n  * Nothing related to finding specific code, just assume it's already found",
        "\n  * Nothing related to verifying or confirming a change through user testing or user reports",
        '\n  * Don\'t use phrases like "The requested feature has been implemented", or "The issue has been resolved" - assume the reader knows the context',
        "\n  * Nothing related to pulling changes from branches",
        "\n  * Nothing related to switching branches",
        "\n  * Nothing related to pushing branches",
        "\n  * Nothing related to merging branches",
        "\n  * Nothing related to committing code",
        "\n  * Nothing related to creating a pull request",
        "\n  * Nothing related to closing an issue",
        "\n",
        '\nIn "useLineNumbers" write a flag indicating if the issue refers explicitly to particular line numbers within the code, except via github.com ',
        "links to lines of source code that include a specific SHA or branch.",
        "\n",
        '\nIn "searchRegexp" write a regular expression to search the contents of source and other text files in the repository for words, ',
        "code identifiers and content relevant to the task. Use 'null' if there's nothing useful to search for. Use the format \"word1|word2|word3\" ",
        "for up to 5 words, identifiers or content, each at least 3 characters. For identifiers, use key individual parts of identifiers rather than compound ",
        "identifiers, and search for key parts of code identifiers from the issue that might find content relevant to the issue. Avoid using character ",
        "classes or nesting. Use Javascript regexp syntax, escaped properly to be valid JSON. Only match within a single line. Prefer not to match ",
        'spaces. The match will be executed as case-insensitive. Do not include leading/trailing "/" or any flags.',
        "\n",
        "\nThen write *exactly* StopStopStop on a new line. Use the format:",
        "\n",
        '\nAnswer: { "question": "redacted|<question>", "useLineNumbers": <true|false>, "searchRegexp": "<regexp>" }',
        "\n",
        "\n§ Result",
        "\n",
        '\nAnswer: { "question": "',
    ]
    return "\n".join(prompt)
