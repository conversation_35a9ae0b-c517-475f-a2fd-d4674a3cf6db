import os

from code_data_utils.components.ranking_prompt_crafter.extensions import (
    IGNORED_DIRECTORY_REGEXPS,
    IGNORED_EXTENSIONS,
    IGNORED_FILES,
)
from code_data_utils.logger import get_logger

logger = get_logger(__name__)


def should_ignore_file(filename: str) -> bool:
    """Return whether a file should be ignored based on blocklists."""
    _, extension = os.path.splitext(filename)
    for ext in IGNORED_EXTENSIONS:
        if extension == f".{ext}":
            logger.info(f"Ignore {filename} with .{ext} extension.")
            return True
    if os.path.basename(filename) in IGNORED_FILES:
        logger.info(f"Ignore {filename} from ignored files.")
        return True
    for dir_re in IGNORED_DIRECTORY_REGEXPS:
        if dir_re.search(filename):
            logger.info(f"Ignore {filename} matching dir regex {dir_re}")
            return True
    return False
