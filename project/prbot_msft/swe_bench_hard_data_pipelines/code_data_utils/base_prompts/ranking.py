"""
The functions in this file were translated from Typescript to python.
"""

import re
from dataclasses import dataclass

from code_data_utils.logger import get_logger

logger = get_logger(__name__)


def get_cap(input_number: int) -> int:
    """Returns a capped number."""
    if input_number >= 200000:
        return 14300
    elif input_number >= 100000:
        return 9569
    elif input_number >= 50000:
        return 6772
    elif input_number >= 20000:
        return 4732
    elif input_number >= 10000:
        return 3878
    elif input_number >= 5000:
        return 3336
    elif input_number >= 4000:
        return 3197
    elif input_number >= 3000:
        return 3000
    else:
        return input_number


def _rank_single_file(file_path: str, regex: str, title_and_description: str) -> float:
    """Ranks a single file based on heuristics and a model-generated regex."""
    file_path = file_path.lower()

    matches = False
    # see if the file path matches the regex
    if re.search(regex, file_path):
        matches = True

    ranking = len(file_path.split("/")) * 10 + len(file_path) / 10

    if "src/" in file_path or "app/" in file_path or "include/" in file_path:
        ranking -= 20

    if "test" in file_path:
        ranking += 10

    if "eng/" in file_path:
        ranking += 20

    if "deps/" in file_path:
        ranking += 20

    if (
        "example/" in file_path
        or "data/" in file_path
        or "examples/" in file_path
        or "resources/" in file_path
        or "samples/" in file_path
        or "man/" in file_path
        or "doc/" in file_path
        or "docs/" in file_path
        or "documentation/" in file_path
        or "benchmark/" in file_path
        or "manual/" in file_path
    ):
        ranking += 10

    if matches:
        ranking -= 100

    if (
        file_path.endswith(".json")
        or file_path.endswith(".xml")
        or file_path.endswith(".csv")
        or file_path.endswith(".tsv")
        or file_path.endswith(".yaml")
        or file_path.endswith(".yml")
        or file_path.endswith(".html")
        or file_path.endswith(".txt")
    ):
        ranking += 10

    if (
        file_path.endswith(".xlf")
        or file_path.endswith(".grdp")
        or file_path.endswith(".resx")
        or file_path.endswith(".po")
        or file_path.endswith(".pot")
        or file_path.endswith(".mo")
    ):
        ranking += 20

    if file_path.endswith("/owners") or "license" in file_path:
        ranking += 20

    filename = file_path.split("/")[-1]
    # filename may have multiple . in it
    filename_without_extension = ".".join(filename.split(".")[:-1])
    if file_path in title_and_description:
        ranking -= 50
    elif filename in title_and_description:
        ranking -= 30
    elif filename_without_extension in title_and_description:
        ranking -= 15
    return round(ranking * 10) / 10


@dataclass
class FileRankResponse:
    ranked_list_of_files: list[tuple[int, str]]
    golden_indexes: list[int]
    unpadded_golden_indexes: list[int]
    golden_set_hit: int

    @property
    def ranked_filenames(self) -> list[str]:
        return [filename for _, filename in self.ranked_list_of_files]

    @property
    def rank_to_file(self) -> dict:
        """Computed property to convert ranked_list_of_files to index to filename"""
        return dict(self.ranked_list_of_files)

    def format_ranked_list_of_files(self, ranked_file_to_summary: dict = {}) -> list[str]:
        """Computed property to format ranked_list_of_files"""
        formatted_list = []
        for index, file in self.ranked_list_of_files:
            summary = ranked_file_to_summary.get(file, "")
            formatted_list.append(f"\n{index} File {file} [{summary}]")
            formatted_list.append("\n")
        if formatted_list[-1] == "\n":
            formatted_list.pop()
        return formatted_list


def rank_files(
    files: list[str],
    regex: str,
    title_and_description: str,
    gold_list_of_files: list,
) -> FileRankResponse:
    golden_set = set(gold_list_of_files)
    res = []
    for file in files:
        ranking = _rank_single_file(file, regex, title_and_description)
        res.append((ranking, file))
    file_to_rank = {file: ranking for ranking, file in res}
    # keep only the cap number of files
    cap = get_cap(len(files))
    logger.info(f"Cap based on len(files) = {len(files)} is {cap}")
    # sort by ranking, low to high
    ranked_res = sorted(res, key=lambda x: x[0])
    capped_res = ranked_res[:cap]
    # save a copy of sorted based on ranking
    ranked_and_capped_res = capped_res.copy()
    logger.info(f"Top ranked files: {ranked_and_capped_res}")
    # sort by alphabetical order, from low to high
    alphabet_res = sorted(capped_res, key=lambda x: x[1])
    ranked_file_list, golden_index_set = [], set()
    file_index_lookup = dict()
    for i, (_, file) in enumerate(alphabet_res):
        index = i + 1
        if file in golden_set:
            golden_index_set.add(index)
        ranked_file_list.append((index, file))
        file_index_lookup[file] = index
    unpadded_golden_index_set = golden_index_set.copy()
    # if golden index list's length is less than 20, add the top ranked files
    logger.info(f"file_index_lookup:\n{file_index_lookup}")
    for _, file in ranked_and_capped_res:
        if len(golden_index_set) >= 20:
            break
        index = file_index_lookup[file]
        if index not in golden_index_set:
            golden_index_set.add(index)
    golden_set_hit = 0
    for file in golden_set:
        if file in file_index_lookup:
            golden_set_hit += 1
        else:
            logger.info(f"Golden file {file} missing. Cap is {cap}")
            rank = file_to_rank.get(file, None)
            if rank is not None:
                logger.info(f"Index in ranked_res: {ranked_res.index((rank, file))}\n")
    logger.info(f"Ranked_file_list: {ranked_file_list}")
    logger.info(f"Golden index set: {golden_index_set}")
    logger.info(f"Unpadded golden indexes: {unpadded_golden_index_set}")
    logger.info(f"Golden set hit: {golden_set_hit}/{len(golden_set)}")
    return FileRankResponse(
        ranked_list_of_files=ranked_file_list,
        golden_indexes=list(golden_index_set),
        unpadded_golden_indexes=list(unpadded_golden_index_set),
        golden_set_hit=golden_set_hit,
    )


def make_prompts(
    ranked_list_of_files_with_summary: list[str],
    ranked_list_of_files: list[str],
    **kwargs,
) -> tuple[str, str]:
    """Given a list of ranked files with summaries and a ranked list of files
    without summaries, return a string prompt for both.
    """
    ranking_with_summary_prompt = make_ranking_with_summary_prompt(
        ranked_list_of_files=ranked_list_of_files_with_summary, **kwargs
    )
    ranking_prompt = make_ranking_prompt(ranked_list_of_files=ranked_list_of_files, **kwargs)
    return ranking_prompt, ranking_with_summary_prompt


def make_ranking_with_summary_prompt(
    *,
    title: str,
    repo_name: str,
    description: str,
    question: str,
    ranked_list_of_files: list[str],
) -> str:
    opening_instruction = [
        "\nYou are an expert coding assistant. You are working on a issue. Your job is to rank what files are most relevant to solving the ",
        "issue based on their file paths, a summary of the file, and everything else you know. ",
    ]

    for_every_file_path = [
        "\nFor every file path, you are also given a summary of the file in square brackets, extracted by another tool. ",
        "Use these to help judge whether the file is relevant. The summary may be empty.",
        "\nConsider this list of files and associated summaries carefully to find the files most relevant to solving the issue:",
    ]

    return make_base_prompt(
        title,
        repo_name,
        description,
        question,
        ranked_list_of_files,
        opening_instruction,
        for_every_file_path,
    )


def make_ranking_prompt(
    *,
    title: str,
    repo_name: str,
    description: str,
    question: str,
    ranked_list_of_files: list[str],
) -> str:
    opening_instruction = [
        "\nYou are an expert coding assistant. You are working on a issue. Your job is to rank what files are most relevant to solving the ",
        "issue based on their file paths, some symbols and snippets extracted from the files, and everything else you know. ",
    ]

    for_every_file_path = [
        "\nFor every file path, you are also given a list of symbols and snippets that occur in that file, extracted by another tool. ",
        "Use these to help judge whether the file is relevant. This list may be empty.",
        "\nConsider this list of files and associated symbols carefully to find the files most relevant to solving the issue:",
    ]

    return make_base_prompt(
        title,
        repo_name,
        description,
        question,
        ranked_list_of_files,
        opening_instruction,
        for_every_file_path,
    )


def make_base_prompt(
    title: str,
    repo_name: str,
    description: str,
    question: str,
    ranked_list_of_files: list[str],
    opening_instruction: list[str],
    for_every_file_path: list[str],
) -> str:
    prompt = [
        *opening_instruction,
        f'A topic summarizing the issue is: "{question}".',
        "\n",
        "\n§ The Issue",
        "\n",
        f"\nTitle: {title}",
        f"\nRepository: {repo_name}",
        "\nDescription:",
        "\n",
        description,
        "\n",
        "\n§ The Files",
        "\n",
        "\nRank the following list of files in this repository. ",
        *for_every_file_path,
        "\n* Prefer code files that will need to be modified when solving the issue.",
        "\n* Prefer code and config files over data, content or sample files unless the files are certainly relevant.",
        "\n* Prefer implementation files over test files unless the topic is explicitly about testing.",
        "\n",
        "\n~~~",
        "\n",
        *ranked_list_of_files,
        "\n~~~",
        "\n",
        "\n§ Instructions",
        "\n",
        "\n",
        f'\nList in ranked order the identifiers of the 20 entries most relevant to the issue "{question}".',
        "\nVERY IMPORTANT: Order them from most relevant to least relevant.",
        "\nVERY IMPORTANT: Strongly avoid listing entries in the sequential order they appear, but rather consider each choice very ",
        "carefully and make the best choices possible from across the list.",
        "\n",
        "\nList only the numbers on a single line, comma separated, no spaces, with most-relevant first. Use the format:",
        "\nAnswer: <id1>,<id2>,...,<idn>",
        "\n",
        "\nAnswer: ",
    ]
    return "".join(prompt)
