{"cells": [{"cell_type": "code", "execution_count": 5, "id": "014198a2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import locale"]}, {"cell_type": "code", "execution_count": 6, "id": "2251a13f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo</th>\n", "      <th>issue_number</th>\n", "      <th>issue_state</th>\n", "      <th>issue_title</th>\n", "      <th>issue_body</th>\n", "      <th>pull_number</th>\n", "      <th>pull_state</th>\n", "      <th>pull_title</th>\n", "      <th>pull_body</th>\n", "      <th>created_at</th>\n", "      <th>accepted</th>\n", "      <th>instance_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>abxba0/fluentai-dotnet</td>\n", "      <td>29</td>\n", "      <td>closed</td>\n", "      <td>Make an example project</td>\n", "      <td>You are an Example Project Generator specializ...</td>\n", "      <td>30</td>\n", "      <td>merged</td>\n", "      <td>Create comprehensive Universal AI SDK Console ...</td>\n", "      <td>This PR creates a new comprehensive console ex...</td>\n", "      <td>8/12/2025, 1:12:32 AM</td>\n", "      <td>True</td>\n", "      <td>abxba0__fluentai-dotnet-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>r-mutax/kakeibo-app</td>\n", "      <td>5</td>\n", "      <td>closed</td>\n", "      <td>[feat] エントリー一覧API＆ページ</td>\n", "      <td>## 背景\\n登録済みの収支データを閲覧できるようにする。\\n\\n## 内容\\n- GET ...</td>\n", "      <td>24</td>\n", "      <td>merged</td>\n", "      <td>[feat] エントリー一覧API＆ページ - Entry List API and Pag...</td>\n", "      <td>This PR implements a comprehensive entry list ...</td>\n", "      <td>8/12/2025, 1:27:54 AM</td>\n", "      <td>True</td>\n", "      <td>r-mutax__kakeibo-app-24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>InterCooperative-Network/icn-mcp</td>\n", "      <td>25</td>\n", "      <td>closed</td>\n", "      <td>PR #5: feat(tools): economic model simulator a...</td>\n", "      <td>PR #5: feat(tools): economic model simulator a...</td>\n", "      <td>30</td>\n", "      <td>merged</td>\n", "      <td>feat(tools): economic model simulator and advisor</td>\n", "      <td>This PR implements a comprehensive suite of ec...</td>\n", "      <td>8/12/2025, 1:31:13 AM</td>\n", "      <td>True</td>\n", "      <td>InterCooperative-Network__icn-mcp-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>r-mutax/kakeibo-app</td>\n", "      <td>6</td>\n", "      <td>closed</td>\n", "      <td>[feat] 月次集計API＋テスト</td>\n", "      <td>## 背景\\n月単位で収入・支出・差額、カテゴリ別、日別推移を取得できるAPIを作成する。\\...</td>\n", "      <td>25</td>\n", "      <td>merged</td>\n", "      <td>Implement monthly summary API with comprehensi...</td>\n", "      <td>This PR implements the requested monthly summa...</td>\n", "      <td>8/12/2025, 1:53:33 AM</td>\n", "      <td>True</td>\n", "      <td>r-mutax__kakeibo-app-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>mnaylor5/climbing-weather-ts</td>\n", "      <td>9</td>\n", "      <td>closed</td>\n", "      <td>Change daily forecast table into a row of tiles</td>\n", "      <td>Currently, the daily forecast information is s...</td>\n", "      <td>10</td>\n", "      <td>merged</td>\n", "      <td>Transform daily forecast table into horizontal...</td>\n", "      <td>This PR transforms the daily forecast display ...</td>\n", "      <td>8/12/2025, 2:01:24 AM</td>\n", "      <td>True</td>\n", "      <td>mnaylor5__climbing-weather-ts-10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>folo-rs/folo</td>\n", "      <td>87</td>\n", "      <td>closed</td>\n", "      <td>Review callback-based APIs for panic safety</td>\n", "      <td>I am not convinced that we always leave things...</td>\n", "      <td>88</td>\n", "      <td>merged</td>\n", "      <td>Review and improve panic safety for callback-b...</td>\n", "      <td>This PR addresses critical panic safety issues...</td>\n", "      <td>8/12/2025, 2:12:41 AM</td>\n", "      <td>True</td>\n", "      <td>folo-rs__folo-88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>AfterShip/clickhouse-sql-parser</td>\n", "      <td>179</td>\n", "      <td>closed</td>\n", "      <td>SHOW/DESC syntax is not supported</td>\n", "      <td>show create table `rs-package-hub` or desc `rs...</td>\n", "      <td>180</td>\n", "      <td>merged</td>\n", "      <td>Add support for SHOW and DESC/DESCRIBE statements</td>\n", "      <td>This PR adds support for parsing SHOW and DESC...</td>\n", "      <td>8/12/2025, 2:15:28 AM</td>\n", "      <td>True</td>\n", "      <td>AfterShip__clickhouse-sql-parser-180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>ncrmro/catalyst</td>\n", "      <td>65</td>\n", "      <td>closed</td>\n", "      <td>Web has a kubernetes client</td>\n", "      <td>During the integration tests we have access to...</td>\n", "      <td>66</td>\n", "      <td>merged</td>\n", "      <td>Add Kubernetes client integration with real te...</td>\n", "      <td>This PR adds comprehensive Kubernetes integrat...</td>\n", "      <td>8/12/2025, 2:25:06 AM</td>\n", "      <td>True</td>\n", "      <td>ncrmro__catalyst-66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>alpacax/alpacon-cli</td>\n", "      <td>38</td>\n", "      <td>closed</td>\n", "      <td>Enhance CLI error message</td>\n", "      <td>I've decided to improve some of the less user-...</td>\n", "      <td>39</td>\n", "      <td>merged</td>\n", "      <td>Enhance CLI error messages with clear, actiona...</td>\n", "      <td>This PR significantly improves the user experi...</td>\n", "      <td>8/12/2025, 2:50:30 AM</td>\n", "      <td>True</td>\n", "      <td>alpacax__alpacon-cli-39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>mylonics/zephyr-ide</td>\n", "      <td>125</td>\n", "      <td>closed</td>\n", "      <td>Add new west config command</td>\n", "      <td>Add west config command to panel and create it...</td>\n", "      <td>134</td>\n", "      <td>merged</td>\n", "      <td>Add new west config command with UI integratio...</td>\n", "      <td>This PR implements a new `zephyr-ide.west-conf...</td>\n", "      <td>8/12/2025, 2:52:40 AM</td>\n", "      <td>True</td>\n", "      <td>mylonics__zephyr-ide-134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>mylonics/zephyr-ide</td>\n", "      <td>135</td>\n", "      <td>closed</td>\n", "      <td>✨Set up Copilot instructions</td>\n", "      <td>Configure instructions for this repository as ...</td>\n", "      <td>136</td>\n", "      <td>merged</td>\n", "      <td>Add comprehensive GitHub Copilot instructions ...</td>\n", "      <td>This PR adds a comprehensive `.github/copilot-...</td>\n", "      <td>8/12/2025, 2:53:34 AM</td>\n", "      <td>True</td>\n", "      <td>mylonics__zephyr-ide-136</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>PatNeedham/Polyglottos</td>\n", "      <td>3</td>\n", "      <td>closed</td>\n", "      <td>Design and Implement Data Export Functionality</td>\n", "      <td>**Add comprehensive data export system with JS...</td>\n", "      <td>33</td>\n", "      <td>merged</td>\n", "      <td>Design and Implement Data Export Functionality...</td>\n", "      <td>This PR implements a comprehensive data export...</td>\n", "      <td>8/12/2025, 2:54:08 AM</td>\n", "      <td>True</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>__Polyglottos-33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>InterCooperative-Network/icn-mcp</td>\n", "      <td>26</td>\n", "      <td>closed</td>\n", "      <td>PR #6: feat(tools): governance process orchest...</td>\n", "      <td>PR #6: feat(tools): governance process orchest...</td>\n", "      <td>31</td>\n", "      <td>merged</td>\n", "      <td>feat(tools): comprehensive governance process ...</td>\n", "      <td>This PR implements a comprehensive governance ...</td>\n", "      <td>8/12/2025, 2:55:39 AM</td>\n", "      <td>True</td>\n", "      <td>InterCooperative-Network__icn-mcp-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>QuantEcon/QuantEcon.py</td>\n", "      <td>782</td>\n", "      <td>closed</td>\n", "      <td>INFR: write a Timer context manager</td>\n", "      <td>There are a lot of code patterns that use a ti...</td>\n", "      <td>783</td>\n", "      <td>merged</td>\n", "      <td>INFR: Add Timer context manager for modern tim...</td>\n", "      <td>This PR implements a `Timer` context manager t...</td>\n", "      <td>8/12/2025, 3:04:56 AM</td>\n", "      <td>True</td>\n", "      <td>QuantEcon__QuantEcon.py-783</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>hossain-khan/device-catalog-webapp</td>\n", "      <td>77</td>\n", "      <td>closed</td>\n", "      <td>Make the app more deligtful</td>\n", "      <td>This webapp is working nice. Make the UI/UX mo...</td>\n", "      <td>78</td>\n", "      <td>merged</td>\n", "      <td>✨ Enhance UI/UX with delightful animations and...</td>\n", "      <td>This PR transforms the Android Device Catalog ...</td>\n", "      <td>8/12/2025, 3:05:29 AM</td>\n", "      <td>True</td>\n", "      <td>hossain-khan__device-catalog-webapp-78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>EvilBit-Labs/dbsurveyor</td>\n", "      <td>1</td>\n", "      <td>closed</td>\n", "      <td>[TASK-001] Dual-Binary Architecture Setup</td>\n", "      <td>## 🎯 Feature Description\\n\\n**Dual-Binary Arch...</td>\n", "      <td>42</td>\n", "      <td>merged</td>\n", "      <td>feat: implement dual-binary architecture with ...</td>\n", "      <td>This PR implements the foundational dual-binar...</td>\n", "      <td>8/12/2025, 3:07:22 AM</td>\n", "      <td>True</td>\n", "      <td>EvilBit-Labs__dbsurveyor-42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>QuantEcon/QuantEcon.py</td>\n", "      <td>784</td>\n", "      <td>closed</td>\n", "      <td>✨Set up Copilot instructions</td>\n", "      <td>Configure instructions for this repository as ...</td>\n", "      <td>785</td>\n", "      <td>merged</td>\n", "      <td>Add comprehensive GitHub Copilot instructions ...</td>\n", "      <td>This PR adds a comprehensive `.github/copilot-...</td>\n", "      <td>8/12/2025, 3:13:30 AM</td>\n", "      <td>True</td>\n", "      <td>QuantEcon__QuantEcon.py-785</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Lingwuxin/ScreenSoundSwitch</td>\n", "      <td>9</td>\n", "      <td>closed</td>\n", "      <td>✨Set up Copilot instructions</td>\n", "      <td>Configure instructions for this repository as ...</td>\n", "      <td>10</td>\n", "      <td>merged</td>\n", "      <td>Add comprehensive GitHub Copilot instructions ...</td>\n", "      <td>This PR creates a comprehensive `.github/copil...</td>\n", "      <td>8/12/2025, 3:18:58 AM</td>\n", "      <td>True</td>\n", "      <td><PERSON><PERSON><PERSON>__ScreenSoundSwitch-10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Azure/autorest.typescript</td>\n", "      <td>3431</td>\n", "      <td>open</td>\n", "      <td>Allow to only generate one integration test ca...</td>\n", "      <td>In TypeSpec integration testing we need to gen...</td>\n", "      <td>3432</td>\n", "      <td>closed</td>\n", "      <td>Add substring filtering support for TypeSpec i...</td>\n", "      <td>This PR enhances the `--filter` option in Type...</td>\n", "      <td>8/12/2025, 3:44:41 AM</td>\n", "      <td>False</td>\n", "      <td>Azure__autorest.typescript-3432</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>PSInclusive/PowerShellLocalization</td>\n", "      <td>1</td>\n", "      <td>closed</td>\n", "      <td>Logging Level Options</td>\n", "      <td>Currently we log all \"levels\" to the output pa...</td>\n", "      <td>3</td>\n", "      <td>merged</td>\n", "      <td>Add configurable logging levels to control deb...</td>\n", "      <td>This PR implements configurable logging levels...</td>\n", "      <td>8/12/2025, 3:53:59 AM</td>\n", "      <td>True</td>\n", "      <td>PSInclusive__PowerShellLocalization-3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  repo  issue_number issue_state  \\\n", "0               abxba0/fluentai-dotnet            29      closed   \n", "1                  r-mutax/kakeibo-app             5      closed   \n", "2     InterCooperative-Network/icn-mcp            25      closed   \n", "3                  r-mutax/kakeibo-app             6      closed   \n", "4         mnaylor5/climbing-weather-ts             9      closed   \n", "5                         folo-rs/folo            87      closed   \n", "6      AfterShip/clickhouse-sql-parser           179      closed   \n", "7                      ncrmro/catalyst            65      closed   \n", "8                  alpacax/alpacon-cli            38      closed   \n", "9                  mylonics/zephyr-ide           125      closed   \n", "10                 mylonics/zephyr-ide           135      closed   \n", "11              PatNeedham/Polyglottos             3      closed   \n", "12    InterCooperative-Network/icn-mcp            26      closed   \n", "13              QuantEcon/QuantEcon.py           782      closed   \n", "14  hossain-khan/device-catalog-webapp            77      closed   \n", "15             EvilBit-Labs/dbsurveyor             1      closed   \n", "16              QuantEcon/QuantEcon.py           784      closed   \n", "17         Lingwuxin/ScreenSoundSwitch             9      closed   \n", "18           Azure/autorest.typescript          3431        open   \n", "19  PSInclusive/PowerShellLocalization             1      closed   \n", "\n", "                                          issue_title  \\\n", "0                             Make an example project   \n", "1                               [feat] エントリー一覧API＆ページ   \n", "2   PR #5: feat(tools): economic model simulator a...   \n", "3                                  [feat] 月次集計API＋テスト   \n", "4     Change daily forecast table into a row of tiles   \n", "5         Review callback-based APIs for panic safety   \n", "6                   SHOW/DESC syntax is not supported   \n", "7                         Web has a kubernetes client   \n", "8                           Enhance CLI error message   \n", "9                         Add new west config command   \n", "10                       ✨Set up Copilot instructions   \n", "11     Design and Implement Data Export Functionality   \n", "12  PR #6: feat(tools): governance process orchest...   \n", "13                INFR: write a Timer context manager   \n", "14                        Make the app more deligtful   \n", "15          [TASK-001] Dual-Binary Architecture Setup   \n", "16                       ✨Set up Copilot instructions   \n", "17                       ✨Set up Copilot instructions   \n", "18  Allow to only generate one integration test ca...   \n", "19                              Logging Level Options   \n", "\n", "                                           issue_body  pull_number pull_state  \\\n", "0   You are an Example Project Generator specializ...           30     merged   \n", "1   ## 背景\\n登録済みの収支データを閲覧できるようにする。\\n\\n## 内容\\n- GET ...           24     merged   \n", "2   PR #5: feat(tools): economic model simulator a...           30     merged   \n", "3   ## 背景\\n月単位で収入・支出・差額、カテゴリ別、日別推移を取得できるAPIを作成する。\\...           25     merged   \n", "4   Currently, the daily forecast information is s...           10     merged   \n", "5   I am not convinced that we always leave things...           88     merged   \n", "6   show create table `rs-package-hub` or desc `rs...          180     merged   \n", "7   During the integration tests we have access to...           66     merged   \n", "8   I've decided to improve some of the less user-...           39     merged   \n", "9   Add west config command to panel and create it...          134     merged   \n", "10  Configure instructions for this repository as ...          136     merged   \n", "11  **Add comprehensive data export system with JS...           33     merged   \n", "12  PR #6: feat(tools): governance process orchest...           31     merged   \n", "13  There are a lot of code patterns that use a ti...          783     merged   \n", "14  This webapp is working nice. Make the UI/UX mo...           78     merged   \n", "15  ## 🎯 Feature Description\\n\\n**Dual-Binary Arch...           42     merged   \n", "16  Configure instructions for this repository as ...          785     merged   \n", "17  Configure instructions for this repository as ...           10     merged   \n", "18  In TypeSpec integration testing we need to gen...         3432     closed   \n", "19  Currently we log all \"levels\" to the output pa...            3     merged   \n", "\n", "                                           pull_title  \\\n", "0   Create comprehensive Universal AI SDK Console ...   \n", "1   [feat] エントリー一覧API＆ページ - Entry List API and Pag...   \n", "2   feat(tools): economic model simulator and advisor   \n", "3   Implement monthly summary API with comprehensi...   \n", "4   Transform daily forecast table into horizontal...   \n", "5   Review and improve panic safety for callback-b...   \n", "6   Add support for SHOW and DESC/DESCRIBE statements   \n", "7   Add Kubernetes client integration with real te...   \n", "8   Enhance CLI error messages with clear, actiona...   \n", "9   Add new west config command with UI integratio...   \n", "10  Add comprehensive GitHub Copilot instructions ...   \n", "11  Design and Implement Data Export Functionality...   \n", "12  feat(tools): comprehensive governance process ...   \n", "13  INFR: Add Timer context manager for modern tim...   \n", "14  ✨ Enhance UI/UX with delightful animations and...   \n", "15  feat: implement dual-binary architecture with ...   \n", "16  Add comprehensive GitHub Copilot instructions ...   \n", "17  Add comprehensive GitHub Copilot instructions ...   \n", "18  Add substring filtering support for TypeSpec i...   \n", "19  Add configurable logging levels to control deb...   \n", "\n", "                                            pull_body             created_at  \\\n", "0   This PR creates a new comprehensive console ex...  8/12/2025, 1:12:32 AM   \n", "1   This PR implements a comprehensive entry list ...  8/12/2025, 1:27:54 AM   \n", "2   This PR implements a comprehensive suite of ec...  8/12/2025, 1:31:13 AM   \n", "3   This PR implements the requested monthly summa...  8/12/2025, 1:53:33 AM   \n", "4   This PR transforms the daily forecast display ...  8/12/2025, 2:01:24 AM   \n", "5   This PR addresses critical panic safety issues...  8/12/2025, 2:12:41 AM   \n", "6   This PR adds support for parsing SHOW and DESC...  8/12/2025, 2:15:28 AM   \n", "7   This PR adds comprehensive Kubernetes integrat...  8/12/2025, 2:25:06 AM   \n", "8   This PR significantly improves the user experi...  8/12/2025, 2:50:30 AM   \n", "9   This PR implements a new `zephyr-ide.west-conf...  8/12/2025, 2:52:40 AM   \n", "10  This PR adds a comprehensive `.github/copilot-...  8/12/2025, 2:53:34 AM   \n", "11  This PR implements a comprehensive data export...  8/12/2025, 2:54:08 AM   \n", "12  This PR implements a comprehensive governance ...  8/12/2025, 2:55:39 AM   \n", "13  This PR implements a `Timer` context manager t...  8/12/2025, 3:04:56 AM   \n", "14  This PR transforms the Android Device Catalog ...  8/12/2025, 3:05:29 AM   \n", "15  This PR implements the foundational dual-binar...  8/12/2025, 3:07:22 AM   \n", "16  This PR adds a comprehensive `.github/copilot-...  8/12/2025, 3:13:30 AM   \n", "17  This PR creates a comprehensive `.github/copil...  8/12/2025, 3:18:58 AM   \n", "18  This PR enhances the `--filter` option in Type...  8/12/2025, 3:44:41 AM   \n", "19  This PR implements configurable logging levels...  8/12/2025, 3:53:59 AM   \n", "\n", "    accepted                             instance_id  \n", "0       True              abxba0__fluentai-dotnet-30  \n", "1       True                 r-mutax__kakeibo-app-24  \n", "2       True    InterCooperative-Network__icn-mcp-30  \n", "3       True                 r-mutax__kakeibo-app-25  \n", "4       True        mnaylor5__climbing-weather-ts-10  \n", "5       True                        folo-rs__folo-88  \n", "6       True    AfterShip__clickhouse-sql-parser-180  \n", "7       True                     ncrmro__catalyst-66  \n", "8       True                 alpacax__alpacon-cli-39  \n", "9       True                mylonics__zephyr-ide-134  \n", "10      True                mylonics__zephyr-ide-136  \n", "11      <PERSON>              <PERSON><PERSON>__Polyglottos-33  \n", "12      True    InterCooperative-Network__icn-mcp-31  \n", "13      True             QuantEcon__QuantEcon.py-783  \n", "14      True  hossain-khan__device-catalog-webapp-78  \n", "15      True             EvilBit-Labs__dbsurveyor-42  \n", "16      True             QuantEcon__QuantEcon.py-785  \n", "17      True         Lingwuxin__ScreenSoundSwitch-10  \n", "18     False         Azure__autorest.typescript-3432  \n", "19      True   PSInclusive__PowerShellLocalization-3  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"/Users/<USER>/Downloads/ptsd-2025-08-12-2025-09-04-full.csv\", encoding=\"utf-8\")\n", "df[\"issue_number\"] = df.issue_number.apply(lambda x: int(x.replace(\",\", \"\")))\n", "df[\"pull_number\"] = df.pull_number.apply(lambda x: int(x.replace(\",\", \"\")))\n", "df[\"instance_id\"] = df.repo.str.replace(\"/\", \"__\") + \"-\" + df.pull_number.astype(str)\n", "df.head(20)"]}, {"cell_type": "code", "execution_count": 7, "id": "ba1faccf", "metadata": {}, "outputs": [], "source": ["df.to_json(\"/Users/<USER>/Downloads/ptsd-2025-08-12-2025-09-04-full.jsonl\", orient=\"records\", lines=True, force_ascii=False)"]}, {"cell_type": "code", "execution_count": 8, "id": "596f85e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"repo\": \"PSInclusive/PowerShellLocalization\",\n", "  \"issue_number\": 1,\n", "  \"issue_state\": \"closed\",\n", "  \"issue_title\": \"Logging Level Options\",\n", "  \"issue_body\": \"Currently we log all \\\"levels\\\" to the output panel. It would be good to avoid the debug logs unless specifically enabled in the settings..\",\n", "  \"pull_number\": 3,\n", "  \"pull_state\": \"merged\",\n", "  \"pull_title\": \"Add configurable logging levels to control debug message visibility\",\n", "  \"pull_body\": \"This PR implements configurable logging levels to address the issue of debug logs cluttering the output panel. Previously, all log levels (including 23+ debug messages) were always displayed, creating noise for users.\\n\\n## Changes Made\\n\\n### New Configuration Option\\nAdded `powershellLocalization.logLevel` setting with four hierarchical levels:\\n- `error` - Only error messages\\n- `warn` - Error and warning messages  \\n- `info` - Error, warning, and info messages (default)\\n- `debug` - All messages including debug output\\n\\n### Smart Log Filtering\\nEnhanced the Logger class with intelligent filtering that respects the configured log level:\\n\\n```typescript\\nprivate shouldLog(messageLevel: LogLevel): boolean {\\n  const currentLevel = ConfigurationManager.getLogLevel();\\n  return Logger.LOG_LEVELS[messageLevel] <= Logger.LOG_LEVELS[currentLevel];\\n}\\n```\\n\\nAll logging methods (`info`, `warn`, `error`, `debug`) now check the current log level before outputting messages.\\n\\n### User Experience\\n- **Default behavior**: Debug messages are filtered out, providing a clean output panel\\n- **Developer experience**: Debug logs can be enabled via VS Code settings when troubleshooting\\n- **Configuration**: Accessible through Settings → Extensions → PowerShell Localization → Log Level\\n- **Backward compatibility**: No changes to existing logging API\\n\\n## Impact\\nWith the default \\\"info\\\" level, users will no longer see verbose debug messages like:\\n- `\\\"Updating decorations for: ${filePath}\\\"`\\n- `\\\"Applied ${decorations.length} decorations\\\"`  \\n- `\\\"Using cached localization data\\\"`\\n- `\\\"Analyzing module file...\\\"`\\n\\nThese debug messages remain available by changing the log level to \\\"debug\\\" when needed for troubleshooting.\\n\\nFixes #1.\\n\\n<!-- START COPILOT CODING AGENT TIPS -->\\n---\\n\\n✨ Let Copilot coding agent [set things up for you](https://github.com/PSInclusive/PowerShellLocalization/issues/new?title=✨Set+up+Copilot+instructions&body=Configure%20instructions%20for%20this%20repository%20as%20documented%20in%20%5BBest%20practices%20for%20Copilot%20coding%20agent%20in%20your%20repository%5D%28https://gh.io/copilot-coding-agent-tips%29%0A%0A%3COnboard%20this%20repo%3E&assignees=copilot) — coding agent works faster and does higher quality work when set up for your repo.\\n\",\n", "  \"created_at\": \"8/12/2025, 3:53:59 AM\",\n", "  \"accepted\": true,\n", "  \"instance_id\": \"PSInclusive__PowerShellLocalization-3\"\n", "}\n"]}], "source": ["with open(\"/Users/<USER>/Downloads/ptsd-2025-08-12-2025-09-04-full.jsonl\", \"r\", encoding=\"utf-8\") as f:\n", "    lines = f.readlines()\n", "    print(json.dumps(json.loads(lines[19]), indent=2, ensure_ascii=False))"]}, {"cell_type": "code", "execution_count": null, "id": "837d7a84", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "3.12.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}