# Scripts

## Create SWE-Bench artifact

Based on a list of PR metadata shared from GitHub (GH), this creates a SWE-Bench like artifact for
each instance (owner, repo, PR).

The PR metadata shared from GH has the following form:
```json
{
    "base_commit": "e971e4ee51adfb6366a5f3e574a940e06f7b2791",
    "created_at": "2024-05-24T16:41:57Z",
    "hints_text": "",
    "instance_id": "microsoft__vscode-213405",
    "issue_numbers": [212910],
    "patch": None,
    "merge_commit": "0d350085d6000ddd47d24a77cee01d8908cbc594",
    "problem_statement": "Create test for selection in `editorGroupModel.test.ts`\nCurrently there are only tests for the groupview but not for the model. Add some test to make sure there will not be any regressions in the future.",
    "pull_number": 213405,
    "test_patch": "",
    "version": "",
    "FAIL_TO_PASS": [],
    "PASS_TO_FAIL": [],
    "env_install_commmit": "",
    "repo": "microsoft/vscode"
}
```

For each PR metadata, the AML pipeline
- downloads the repo at the base commit of the PR and zips it
- calls the GitHub API to get a [pull request](https://docs.github.com/en/rest/pulls/pulls?apiVersion=2022-11-28#get-a-pull-request)
- augments the metadata with the following fields:
```json
{
    "patch": "diff --git a/src/vs/editor/common/cursor/cursorMoveOperations.ts b/src/vs/editor/common/cursor/cursorMoveOperations.ts\n...",
    "test_patch": "",
    "filenames": "[]",
    "number_of_files": 123,
    "language_count": "{\"Dockerfile\": {\"size\": 959, \"percentage\": \"0.00\", \"files\": ...}"
}
```
- and dumps all objects to string

The repo.zip, metadata.json, and prdata.json get written to Azure Blob Storage under a dir whose name is the `instance_id`.

### Usage

```bash
  python -m scripts.create_swe_bench_artifact \
  --local_base_dir ../../../tmp_local_in \
  --output_base_dir damajercak/ptsd-2025-08-12-2025-09-07 \
  --pipeline_name damajercak_ptsdv1-2025-08-12-2025-09-07-5 \
  --source_path damajercak/ptsd-2025-08-12-2025-09-04.jsonl  \
  --split copilot_ws \
  --start 0 \
  --end -1 \
  --client 0 \
  --tag "ptsd-2025-08-12-2025-09-07"
```

### Local debug
```bash
genai/code/code_data_utils/components/swe_bench_data_curator$ python -m pdb main.py \
--metadata jadhuang/swe_bench_verified_sample.jsonl \
--base_path /home/<USER>/code \
--local --skip_upload
```

## Create file-selection data

Based on SWE-Bench-like data, this creates file selection prompts similar to
the prompts that GitHub uses to prompt models for Copilot Workspace.

For each instance id, the AML pipeline
- downloads the `metadata.json` and `repo.zip` created from the previous step
- extracts a list of modified and deleted files from the patch
- selects files from the repo to include in the prompt. There is a ranking heuristic, blocklist,
and a cap of maximum files.
- the issue is extracted from the `metadata.json` and sent to a GPT-4o endpoint
to 1) summarize as a question and 2) generate a regex to search for contents
of the repo that might be relevant to the task.
- then we craft the file-selection prompt and save it to blob storage.
See `code_data_utils.components.ranking_prompt_crafter.models.py`
for a description of the fields in the final json file.

### Usage

#### Submitting a pipeline

```bash
genai/code/code_data_utils$ python create_ranking_prompts.py \
--input_base_dir swe_bench_20241011_2 \
--summary_base_dir swe_bench_file_summary \
--output_base_dir file_selection_prompts_20241016 \
--pipeline_name jadhuang_file_selection_20241016 \
--client 0 \
--tag "sample-1002-test" \
--sample_size 2 \
--split copilot_ws
```

#### Local debug

```bash
python main.py \
--input_base_path swe_bench_20241011_2 \
--output_base_path /home/<USER>/code \
--local --skip_upload \
--instance_id TeamDon-tBe__SERVER-62 \
--summary_base_path swe_bench_file_summary  \
--split copilot_ws
```

## Create HF splits

### Usage

```bash
  python -m scripts.create_swe_bench_artifact \
  --local_base_dir ../../../tmp_local_in \
  --output_base_dir damajercak/ptsd-2025-08-12-2025-09-07 \
  --pipeline_name damajercak_ptsdv1-2025-08-12-2025-09-07-5 \
  --source_path damajercak/ptsd-2025-08-12-2025-09-04.jsonl  \
  --split copilot_ws \
  --start 0 \
  --end -1 \
  --client 0 \
  --tag "ptsd-2025-08-12-2025-09-07"
```

## Prepare CaaS mounts

### Usage

```bash
  python -m scripts.prepare_caas_mounts \
  --source_directory "az://orngtransfer/grninbound/data/damajercak/ptsd-2025-08-12-2025-09-07/copilot_ws" \
  --target_directory: str = "az://orngcaas/data/damajercak/ptsd"
```