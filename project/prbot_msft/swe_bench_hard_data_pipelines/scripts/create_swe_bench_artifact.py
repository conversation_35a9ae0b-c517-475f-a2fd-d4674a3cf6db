import argparse
import logging

from azure.identity import DefaultAzureCredential
from code_data_utils.blob_ops import get_codemodeldata_data, list_blob_names, load_data
from code_data_utils.pipelines import submit_data_pipeline, submit_pipelines

logging.basicConfig(
    format="%(asctime)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S", level=logging.INFO
)
logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--local_base_dir", type=str, required=True)
    parser.add_argument("--output_base_dir", type=str, required=True)
    parser.add_argument("--pipeline_name", type=str, required=True)
    parser.add_argument(
        "--source_path",
        type=str,
        choices=[
            "shared_from_gh/fixed.jsonl",  # Set of examples to create Workspace-Bench
            "jadhuang/copilot_ws_processed_examples.jsonl",  # Set of Workspace-Bench examples successfully created
            "swe_bench_20241011_2/princeton-nlp___swe-bench_verified/swe_bench_verified.jsonl",
            "swe_bench_20241011_2/princeton-nlp___swe-bench/swe_bench_train.jsonl",
            "damajercak/gh-2024-09-04-2025-03-04-top2k.jsonl",  # Set of Workspace-Bench examples successfully created
            "damajercak/ptsd-2025-08-12-2025-09-04.jsonl",
        ],
    )
    parser.add_argument("--done_path", type=str, default=None)
    parser.add_argument("--start", type=int, required=True)
    parser.add_argument("--end", type=int, required=True)
    parser.add_argument("--client", type=int, required=False, default=0)
    parser.add_argument("--tag", type=str, required=True)
    parser.add_argument(
        "--split",
        type=str,
        choices=["copilot_ws", "swe_bench_verified", "swe_bench_train"],
    )
    return parser.parse_args()


def main(args):
    credential = DefaultAzureCredential()
    container_client = get_codemodeldata_data(credential=credential)
    existing_blobs = list_blob_names(
        container_client, args["output_base_dir"] + "/" + args["split"] + "/"
    )
    data = load_data(
        container_client,
        args["split"],
        args["source_path"],
        args["done_path"],
        args["local_base_dir"],
        exclude_instances=existing_blobs,
    )

    output_base_dir = args["output_base_dir"]
    logger.info(f"Writing output to {output_base_dir}")
    if args["end"] < 0:
        args["end"] = len(data)
    logger.info(f"Slicing data[{args['start']}: {args['end']}]")
    data_slice = data[args["start"] : args["end"]]
    submit_pipelines(
        pipeline_to_submit=submit_data_pipeline,
        data_slice=data_slice,
        client=args["client"],
        base_path=output_base_dir,
        pipeline_name=args["pipeline_name"],
        tag=args["tag"],
    )


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logging.getLogger("azure").setLevel(logging.WARNING)
    args = parse_args()
    main(vars(args))
