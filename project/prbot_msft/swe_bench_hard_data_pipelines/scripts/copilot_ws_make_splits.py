#! /usr/bin/env python

# pip install azure-identity azure-storage-blob more-itertools python-dateutil tqdm
# ./make-splits.py > splits.jsonl

import json
import re
import sys
from concurrent.futures import ThreadPoolExecutor

from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobServiceClient
from more_itertools import chunked
from tqdm import tqdm

credential = DefaultAzureCredential()

account_url = "https://codemodeldata.blob.core.windows.net/"
container_name = "data"
subdirectory_prefix = "swe_bench/copilot_ws"

splits = ["train", "validation", "test"]
weights = [0.8, 0.1, 0.1]

# ----------------------------------------------

assert sum(weights) == 1


class Splitter(object):
    from enum import Enum

    class RepoSplitType(str, Enum):
        FULL = ("Full",)
        TEMPORAL = "Temporal"

    def __init__(self, *, splits, weights, temporal_frac=0.5, seed=31337):
        import random
        from collections import defaultdict

        super().__init__()

        self._splits = splits
        self._weights = weights
        self._temporal_frac = temporal_frac

        self._repo_type = {}
        self._full_split_repos = {}
        self._time_split_repos = defaultdict(list)

        self._instances_by_split = defaultdict(list)

        self._rgen = random.Random(seed)

    def assign_split(self, metadata):
        from dateutil import parser

        def robust_float(v):
            try:
                return float(v)
            except ValueError:
                print("*", file=sys.stderr, end=False)
                return 0.0

        instance_id = metadata["instance_id"]
        repo = metadata["repo"]
        timestamp = parser.isoparse(metadata["created_at"]).timestamp()
        language_dist = {
            lang: robust_float(info["percentage"])
            for lang, info in metadata["language_count"].items()
        }
        modified_count = sum(
            1 for line in metadata["patch"].splitlines() if line.startswith("diff --git")
        )

        if repo not in self._repo_type:
            self._repo_type[repo] = (
                Splitter.RepoSplitType.FULL
                if self._rgen.random() < self._temporal_frac
                else Splitter.RepoSplitType.TEMPORAL
            )

        concise_metadata = {
            "instance_id": instance_id,
            "language_dist": language_dist,
            "modified_count": modified_count,
            "split_type": self._repo_type[repo].value,
        }

        if self._repo_type[repo] == Splitter.RepoSplitType.FULL:
            if repo not in self._full_split_repos:
                self._full_split_repos[repo] = self._rgen.choices(self._splits, self._weights)[0]

            my_split = self._full_split_repos[repo]
            self._instances_by_split[my_split].append(concise_metadata)
        elif self._repo_type[repo] == Splitter.RepoSplitType.TEMPORAL:
            self._time_split_repos[repo].append((timestamp, concise_metadata))

    def finalize_assignments(self):
        import itertools

        for repo, time_md_list in self._time_split_repos.items():
            sorted_md = sorted(time_md_list, key=lambda v: v[0])
            split_points = list(itertools.accumulate([int(w * len(sorted_md)) for w in weights]))
            split_points[-1] = -1
            split_points.insert(0, 0)
            for split, (a, b) in zip(splits, zip(split_points, split_points[1:])):
                self._instances_by_split[split].extend([v[1] for v in sorted_md[a:b]])


blob_service_client = BlobServiceClient(account_url=account_url, credential=credential)
container_client = blob_service_client.get_container_client(container_name)

splitter = Splitter(splits=splits, weights=weights)

blobs = container_client.list_blobs(name_starts_with=subdirectory_prefix)
pattern = re.compile(r".*metadata\.jsonl$")

# swe_bench/copilot_ws/01-edu__public-1748/metadata.jsonl

max_workers = 16
for n, blob_chunk in enumerate(tqdm(chunked(blobs, max_workers))):
    # if n * max_workers > 10000:
    #    break

    def download_and_process(blob):
        if pattern.match(blob.name):
            blob_client = container_client.get_blob_client(blob)
            blob_data = blob_client.download_blob().readall()
            return json.loads(blob_data.decode("utf-8").split("\n", 1)[0])
        else:
            return None

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(download_and_process, blob_chunk))

        for metadata in results:
            if metadata is not None:
                splitter.assign_split(metadata)

splitter.finalize_assignments()

print(
    f"\n{'split':10}\t{'n_instances':15}\t{'mean modified count':20}\tlanguage distribution",
    file=sys.stderr,
)
for split, instances in splitter._instances_by_split.items():
    from collections import defaultdict

    n_instances = len(instances)
    mean_modified_count = sum(i["modified_count"] for i in instances) / max(1, n_instances)
    language_dist = defaultdict(float)
    for i in instances:
        for lang, frac in i["language_dist"].items():
            language_dist[lang] += frac

    language_dist = {
        lang: round(frac, 3)
        for lang, total in language_dist.items()
        for frac in (total / max(1, n_instances),)
        if frac > 1
    }

    sorted_language_dist = dict(sorted(language_dist.items(), key=lambda v: -v[1]))

    print(
        f"{split:10}\t{n_instances:15}\t{round(mean_modified_count, 3):20}\t{sorted_language_dist}",
        file=sys.stderr,
    )

print(json.dumps(splitter._instances_by_split))
