import json
import logging
import re
from argparse import Argument<PERSON>arser


def main(args):
    with open(args.input_file) as f:
        lines = [json.loads(line) for line in f]

    if args.prompt_type == "completions":
        formatted_lines = []
        for line in lines:
            expected_files = line["expected_files"]
            expected_file_found_indices = line["expected_file_found_indices"]
            indices = [expected_file_found_indices.get(fname) for fname in expected_files]
            # remove none, some files are not going to be found
            indices = [index for index in indices if index is not None]
            formatted_line = {
                "prompt": "\n".join(line["prompt"]),
                "completion": ",".join(indices),
                **line,
            }
            formatted_lines.append(formatted_line)

        output_path = re.sub("\.jsonl", f"_{args.prompt_type}.jsonl", args.input_file)  # noqa: W605
        logger.info(f"Writing out to {output_path}")
        with open(output_path, "w") as f:
            for line in formatted_lines:
                f.write(f"{json.dumps(line)}\n")


if __name__ == "__main__":
    logger = logging.getLogger("azure")
    logger.setLevel(logging.WARNING)
    argparser = ArgumentParser()
    argparser.add_argument("--prompt_type", type=str, default="completions")
    argparser.add_argument("--input_file", type=str, default="gh.jsonl", required=True)
    args = argparser.parse_args()
    main(args)
