import json
import shlex
import subprocess
from concurrent.futures import ThreadPoolExecutor, as_completed

import blobfile as bf
import typer
from tqdm import tqdm

app = typer.Typer()


def process_instance(source_directory: str, instance: str, target_directory: str):
    """Process a single instance with improved error handling and logging."""
    try:
        # Implement the processing logic for each instance
        with bf.BlobFile(f"{source_directory}/{instance}/metadata.json", "r") as src_file:
            metadata = json.load(src_file)
            repo, base_commit = metadata.get("repo"), metadata.get("base_commit")

            if not repo or not base_commit:
                raise ValueError(f"Missing repo or base_commit in metadata for {instance}")

            # Process the metadata as needed
            source_path = f"{source_directory}/{instance}/{base_commit}.tar"
            target_path = f"{target_directory}/{repo}/{base_commit}.tar"

            # Write the processed metadata to the target directory
            # bf.copy(source_path, target_path, overwrite=True)

            # Copy using subprocess.run() - safer and more robust
            try:
                result = subprocess.run(
                    ["bbb", "cp", source_path, target_path],
                    check=True,
                    capture_output=True,
                    text=True,
                    timeout=1800,  # 30 minute timeout
                )
                # Optionally log the output
                if result.stdout:
                    print(f"bbb cp output: {result.stdout.strip()}")

            except subprocess.CalledProcessError as e:
                raise Exception(f"bbb cp failed: {e.stderr}")
            except subprocess.TimeoutExpired:
                raise Exception("bbb cp command timed out after 5 minutes")

            return f"✅ {instance} -> {repo}/{base_commit}"

    except Exception as e:
        raise Exception(f"Failed to process {instance}: {str(e)}")


@app.command()
def prepare_mounts(
    source_directory: str = "az://orngtransfer/grninbound/data/damajercak/ptsd-2025-08-12-2025-09-07/copilot_ws",
    target_directory: str = "az://orngcaas/data/damajercak/ptsd",
):
    """
    Prepare the CAAS mounts for the application.
    """
    instances = list(bf.listdir(source_directory))

    print(f"📁 Found {len(instances)} instances to process")
    print(f"🔄 Processing with parallel workers...")

    # for instance in tqdm(instances):
    #     try:
    #         process_instance(source_directory, instance, target_directory)
    #         tqdm.write(f"✅ {instance} processed successfully")
    #     except Exception as e:
    #         tqdm.write(f"❌ Error processing {instance}: {str(e)}")

    # Make processing of instances in parallel with enhanced tqdm progress
    with ThreadPoolExecutor(max_workers=16) as executor:
        futures = []
        for instance in instances:
            futures.append(
                executor.submit(process_instance, source_directory, instance, target_directory)
            )

        # Enhanced progress bar with better formatting
        with tqdm(
            as_completed(futures),
            total=len(futures),
            desc="🚀 Processing instances",
            unit="instance",
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]",
            colour="green",
        ) as pbar:
            for future in pbar:
                try:
                    future.result()
                    pbar.set_postfix_str("✅ Success")
                except Exception as e:
                    pbar.set_postfix_str(f"❌ Error: {str(e)[:50]}...")

    print("🎉 All instances processed successfully!")


if __name__ == "__main__":
    app()
