import argparse
import json

from azure.identity import DefaultAzureCredential
from code_data_utils.blob_ops import get_codemodeldata_data, load_data
from code_data_utils.logger import get_logger
from code_data_utils.pipelines import submit_pipelines, submit_ranking_prompt_pipeline

logger = get_logger(__name__)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--local_base_dir", type=str, required=True)
    parser.add_argument("--input_base_dir", type=str, required=True)
    parser.add_argument("--summary_base_dir", type=str, required=False, default=None)
    parser.add_argument("--output_base_dir", type=str, required=True)
    parser.add_argument("--pipeline_name", type=str, required=True)
    parser.add_argument(
        "--source_path",
        type=str,
    )
    parser.add_argument("--done_path", type=str, default=None)
    parser.add_argument("--start", type=int, required=True)
    parser.add_argument("--end", type=int, required=True)
    parser.add_argument("--client", type=int, required=False, default=0)
    parser.add_argument("--tag", type=str, required=True)
    parser.add_argument(
        "--split",
        type=str,
        choices=["copilot_ws", "swe_bench_verified", "swe_bench_train"],
    )
    return parser.parse_args()


def main(args):
    credential = DefaultAzureCredential()
    container_client = get_codemodeldata_data(credential=credential)
    examples = load_data(
        container_client,
        args["split"],
        args["source_path"],
        args["done_path"],
        args["local_base_dir"],
    )
    instance_ids = [json.loads(ex)["instance_id"] for ex in examples]
    logger.info(f"Loaded {len(instance_ids)} instance_ids")
    logger.info(f"Slicing data[{args['start']}: {args['end']}]")
    data_slice = instance_ids[args["start"] : args["end"]]
    submit_pipelines(
        pipeline_to_submit=submit_ranking_prompt_pipeline,
        data_slice=data_slice,
        client=args["client"],
        input_base_path=args["input_base_dir"],
        summary_base_path=args["summary_base_dir"],
        output_base_path=args["output_base_dir"],
        pipeline_name=args["pipeline_name"],
        tag=args["tag"],
        split=args["split"],
    )


if __name__ == "__main__":
    args = parse_args()
    main(vars(args))
