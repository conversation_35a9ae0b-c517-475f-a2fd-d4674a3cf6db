let swe_agent_dotcom_id = 198982749;
//the dates below define version v1 of PTSD
let start_date = datetime('20250812');
let cutoff_date = datetime('20250904');
let issue_table = (database('canonical').issues_current()
    | where array_length(linked_pull_request_ids) == 1
    | where state == "closed"
    | extend issue_state = state
    | where strlen(body) >= 50
    | extend pr_id=tolong(linked_pull_request_ids[0])  // Ensure that the linked PR only has this one fixing it
    | project-away linked_pull_request_ids
    | extend issue_id = id
    | extend issue_number = number
    | extend issue_title = ['title']
    | extend issue_body = body
    | project issue_id, repository_id, pr_id, issue_number, issue_state, issue_title, issue_body
);
let prs_table = (database('canonical').pull_requests_current()
| where user_dotcom_id ==swe_agent_dotcom_id and created_at >= start_date and created_at <= cutoff_date
| project pull_request_id = id, repository_id, user_dotcom_id, pr_number=number, num_commenters, state, created_at, ['title']
| join kind=inner ( 
    database('canonical').repositories_current
    | project repository_id=id, nwo, owner_dotcom_id, owner_type, is_public, is_open_source, type
) on repository_id
| where is_open_source
| extend pr_url=strcat("https://github.com/", nwo, "/pull/",   pr_number)
| extend pr_id=tolong(pull_request_id)
| project repo_nwo=nwo, pr_id, pull_request_id, repository_id, pr_number, pr_url, created_at, state, type, num_commenters
);
let pr_issue = (
prs_table
| join kind=inner(issue_table) on repository_id and pr_id
| project
    repository_id, repo_nwo,
    pr_id, pr_number,
    pr_url, created_at, state, type,
    issue_id, issue_number, issue_state, issue_title, issue_body, num_commenters
);
let hydro_pr = (
    database('hydro').github_v1_pull_request_merge
    | extend pr_id = tolong(pull_request.id)
    | extend head_sha = pull_request.head_sha
    | extend base_sha = pull_request.base_sha
    | extend num_changed_files = array_length(pull_request.changed_files)
//    | where pr_approved // noisy, ignore.
    | where num_changed_files <= 20 and num_changed_files > 0 // do we care about this? YES!
    | project pr_id, pull_request, head_sha, base_sha, merge_commit_sha, pr_approved, num_changed_files, timestamp
);
let hydrated_pr = pr_issue | join  kind=inner (hydro_pr) on pr_id; 
let pr_count = hydrated_pr | summarize countpr = count() by repository_id;
hydrated_pr | join kind=inner(pr_count) on repository_id | where countpr > 3 
| project-away pr_id, pr_id1, repository_id, repository_id1, issue_id, countpr
| project repo=repo_nwo, issue_number, issue_title, issue_body, pull_number=pr_number, created_at=created_at, base_commit_sha=base_sha, merge_commit_sha // map to json format (as much as possible)
