dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="p0-pareto-pdw-$dt-peaval"
RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_tools_v2"

CMD=(
beam python --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}

auto_inherit_training_args=False

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'

policy.initial_checkpoint=az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted/
eval_settings.checkpoint_dir=az://orngscuscresco/twapi/mini/e/pmineiro-sbhv2-group-test-20250827-2344/policy

policy.is_multimodal=True
...encoding_name=orion_200k
...harmony_renderer_name=${RENDERER_NAME}
...tools_format_version=v2

peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.use_stale_kv_cache=False
load.restore_from_all_clusters=False
max_active_steps=2
policy.n_ctx=131072
defaults.n_ctx=131072

peashooter.sampling_concurrency=1
peashooter.num_sampling_processes=100
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
...max_num_yields=256
defaults.sampler.max_num_yields=256
policy.encoding_name=orion_200k
defaults.sampler.harmony_constrained_sampling=True

eval_settings.eval_initial_policy=True
eval_settings.eval_every=20
# eval_settings.min_step=0
# eval_settings.exit_on_no_new_checkpoint=True

metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

peashooter.sampling_use_ev3=True

...dataset_container=orngscuscresco
':deep_swe_eval_msft.p0_test.peaval.padawan.presets:padawan_p0_tests_fn(juice=128 override_target_samples_per_instance=5 max_num_yields=256)'
':deep_swe_eval_msft.swe_bench.peaval.padawan.presets:ev_sm_bench_juice_padawan(juice=128 override_target_samples_per_instance=1 max_num_yields=256)'
# Enable the COMMENTARY channel
defaults.channel_config.channels="analysis,commentary,final"
defaults.sampler.interleave_channels.0=analysis
defaults.sampler.interleave_channels.1=commentary

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=p0-pevals
kafka_enable=False
enable_slackbot=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log
