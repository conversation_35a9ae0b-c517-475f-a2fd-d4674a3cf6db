datetime=$(date +"%Y%m%d-%H%M")
# CKPT="az://orngscuscresco/twapi/mini/e/zhendongwang-mix13-pdw2-ev3-ivt-mixed-32x32-o4mini-tef03-5-tpm1-rm-lr1e-5-run8/policy/step_000160/"
# INIT_CKPT="az://orngscuscresco/models/snapshots/zen-os4-nv4-cbv6-hh-rkld-4jul-orca-2-2025-07-10-00-04-decrypted/"
# CKPT="az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted/"
INIT_CKPT="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
CKPT="az://orngscuscresco/twapi/mini/e/damajercak-mix17-pdw2-ev3-itc-256-mixed-spi-gpt5-lr1e-5-run-20250828-185604/policy"
CMD=(
beam python --use-cwd -m qstar.run_eval
nostrict
name=sbhv2-repair-$datetime-peaval
# experiment_id=jadhuang-mix16-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run2
auto_inherit_training_args=False
:berry_models.scallion:d64_chicken_80g_bf16
eval_settings.checkpoint_dir="$CKPT"
policy.initial_checkpoint="$INIT_CKPT"
load.restore_from_all_clusters=False

# :berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
# root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
# policy.ml_config='ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
# peashooter.sampling_use_ev3=True
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0}'

policy.is_multimodal=True
...encoding_name=orion_200k
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_tools_v2
...tools_format_version=v2

peashooter.timeout_seconds.stalled_datapoint=3600
peashooter.timeout_seconds.lost_datapoint=1800
peashooter.use_stale_kv_cache=False
load.restore_from_all_clusters=False
# max_workers_per_step=5

policy.n_ctx=131072
defaults.n_ctx=131072
...max_num_yields=256
defaults.sampler.max_num_yields=256
policy.encoding_name=orion_200k
defaults.sampler.harmony_constrained_sampling=True
eval_settings.eval_initial_policy=True
eval_settings.eval_every=400

:prbot_msft.presets.swebench_hard:swebenchhard_repair_06202025_sbhv2_testval_padawan_silver
# :prbot_msft.presets.swebench_hard:swebenchhard_12878_test_hq_padawan_or_testval
:prbot_msft.presets.swebench_hard:gpt5mini_hpe_cotograder_bus
...dataset_container=orngscuscresco
...instance_objective.aux_objective_0=caas_autograding.tool_penalty_objective:ToolPenaltyObjective
...peer_grader_minimum_group_size=0
# instance_completer.instance_objective.aux_objective_0.inverse_tool_duration_cost=50
# instance_completer.instance_objective.aux_objective_0.tool_duration_cost_scale_with_token_cost_multiplier=True
...max_n_datapoints=1000

# Enable the COMMENTARY channel
defaults.channel_config.channels="analysis,commentary,final"
defaults.sampler.interleave_channels.0=analysis
defaults.sampler.interleave_channels.1=commentary
...tool_channel="commentary"

security_profile=msft-orng
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}" 2>&1 | tee -a peaval-$datetime.log