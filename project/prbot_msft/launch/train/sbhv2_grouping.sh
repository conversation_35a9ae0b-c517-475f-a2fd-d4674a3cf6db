datetime=$(date +"%Y%m%d-%H%M")
#CKPT="az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted/"
CKPT="az://orngscuscresco/twapi/mini/e/zhendongwang-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run7/policy/step_000480/250713101112Y7TNMZOQ-0/"
CMD=(
beam python --use-cwd -m qstar.run_experiment nostrict
name=sbhv2-group-test-$datetime
seed=20250404

skip_validate_config=True

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'

policy.initial_checkpoint=$CKPT
policy.n_gpus=64
policy.is_multimodal=True
#...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc
...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe
...encoding_name=orion_200k

# Systems settings
:qstar.presets.common:longer_timeout
peashooter.sampling_use_ev3=True
peashooter.num_instance_workers=64
peashooter.num_sampling_processes=32
peashooter.timeout_seconds.stalled_datapoint=7500
timeout.default=None

# Model settings
policy.n_ctx=131072
defaults.n_ctx=131072
# defaults.sampler.harmony_constrained_sampling=True
...harmony_constrained_sampling=True

# Empirically this seems to be a safer LR for d32/d36 scale.
# https://oai-blackberry.slack.com/archives/C07Q6RSDC92/p1738051715695349
optimizer.hparam_scaler.lr_per_instance_d16=1e-5
...save_every=5

# Dataset configs!
:prbot_msft.presets.swebench_hard:swebenchhard_train_repair_06202025_sbhv2_padawan_silver_noexec
:prbot_msft.presets.swebench_hard:gpt5mini_hpe_cotograder_bus
...override_target_samples_per_instance=8
...dataset_container=orngscuscresco
...max_num_yields=256
berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=100
...graders_always_correct=True
...use_padawan_enforcement_grader=True
...peer_grader_minimum_group_size=5

# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
defaults.inverse_token_cost=16384
defaults.instances_per_batch=32
defaults.target_samples_per_instance=8
batch_completer.n_batches_in_flight=10
...override_pass_rate_minimum=1e-6

defaults.instance_completer=prbot_msft.instance_completers:SWEBenchHardGroupedSimpleInstanceCompleter
defaults.instance_completer.instance_objective.main_objective.c=1
defaults.instance_completer.instance_objective.main_objective.d=1
defaults.instance_completer.grouped_sample_allocation_args.sample_group_size=8
defaults.instance_completer.grouped_sample_allocation_args.completion_wait_timeout=3600
defaults.instance_completer.grouped_sample_allocation_args.grading_wait_timeout=3600
defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer
...instance_completer.target_samples_per_instance=8

# Enable the COMMENTARY channel
defaults.channel_config.channels="analysis,final"
...tool_channel="analysis"

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}" 2>&1 | tee -a train-$datetime.log
