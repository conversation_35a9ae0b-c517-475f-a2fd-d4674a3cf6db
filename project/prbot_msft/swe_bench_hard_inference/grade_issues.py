import asyncio
import json
import os
import sys
from collections import Counter
from functools import cache
from typing import Awaitable, Callable, Literal

import blobfile as bf
import chat
import tqdm
import typer
from bus_token_completer import BusTokenCompleter
from chat.render.renderer_registry import get_renderer
from legacy_rest_token_completer import LegacyRestTokenCompleter
from message_completer.message_completer import ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter
from tenacity import retry, stop_after_attempt, wait_exponential
from token_completer import CompleterBackend
from token_completer.backend import CompleterBackend

os.environ["OPENAI_API_KEY"] = "NO_API_KEY"
app = typer.Typer()

CRESCO_STORAGE_NAME = (
    "orngcresco" if "uksouth" in os.environ.get("RCALL_KUBE_CLUSTER", "") else "orngscuscresco"
)
# BUS configuration for CotoGrader
COTOGRADER_BUS_USER = "swe-main-run"
COTOGRADER_BUS_TOPIC = f"az://{CRESCO_STORAGE_NAME}/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted"
COTOGRADER_RENDERER = "harmony_v4.0.16_berry_v3_128k_orion_mm_no_budget_truncate"


@cache
def get_message_completer():
    engine_url = os.environ["ENGINE_URL"]
    renderer = get_renderer(os.environ["RENDERER_NAME"])

    if engine_url.startswith("bus:"):
        topic_or_snapshot = engine_url[4:]
        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=BusTokenCompleter.Config(
                topic_mode_or_user=COTOGRADER_BUS_USER,
                topic_or_snapshot=topic_or_snapshot,
                bus_line="bus",
            ),
            completion_params={"temperature": 1},
            renderer=renderer,
        )
        return message_completer_config.build()
    else:
        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=LegacyRestTokenCompleter.Config(
                api_base=engine_url,
                backend=CompleterBackend.TEXT_BACKEND,  # use TEXT_BACKEND for research (non-api.openai.com) engines
            ),
            completion_params={"temperature": 1},
            renderer=renderer,
        )
        return message_completer_config.build()


async def process_all(
    iterable,
    process_function: Callable[..., Awaitable[dict[str, int]]],
    concurrency_limit: int,
    output_path: str,
    **kwargs,
):
    semaphore = asyncio.Semaphore(concurrency_limit)
    sum_results = Counter()

    async def process_with_semaphore(instance):
        async with semaphore:
            return await process_function(instance, **kwargs)

    # TODO: make this producer-consumer so we don't have to consume the entire dataset to start working
    tasks = [process_with_semaphore(instance) for instance in iterable]

    with tqdm.tqdm(total=len(tasks), disable=not sys.stderr.isatty()) as pbar:
        for task in asyncio.as_completed(tasks):
            result = await task
            with open(output_path, "a+") as f:
                f.write(json.dumps(result) + "\n")
                pbar.set_postfix({k: v for k, v in sum_results.items() if k != "total"})
                pbar.update(1)


@retry(stop=stop_after_attempt(10), wait=wait_exponential(multiplier=2, min=1, max=100))
async def process_instance(data, data_type):
    with open(f"./grade/prompt_{data_type}.txt", "r") as f:
        prompt = f.read()

    if data_type == "raw":
        prompt = prompt.replace("{{issue_title}}", data["issue_title"]).replace(
            "{{issue_body}}", data["issue_body"]
        )
    else:  # rendered
        prompt = prompt.replace("{{silver_patch}}", data["patch"] + "\m" + data["test_patch"])
    convo = chat.Conversation(
        messages=[
            chat.Message.system(
                model_identity_desc="You are ChatGPT, a large language model trained by OpenAI, based on the GPT-4 architecture.",
                tools_section={},
                channel_config=chat.SystemChannelConfig(
                    valid_channels=("analysis", "final"),
                    channel_required=True,
                ),
                metadata=chat.SystemContentMetadata(reward_multiplier=int(os.environ["JUICE"])),
            ),
            chat.Message.user(prompt),
        ]
    )

    completion = await get_message_completer().async_completion(
        conversations=[convo],
        n=1,
        seed=0,
        end_header=True,
    )
    choice = completion.choices[0]
    messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
    convo = convo.with_suffix(*messages)
    result = json.loads("\n".join([part for part in convo.messages[-1].content.parts]))
    return {**data, **result}


async def process_instance_safe(instance_id, src_path, data_type):
    try:
        if type(instance_id) is str:
            metadata_file = os.path.join(src_path, instance_id, "metadata.json")
            with open(metadata_file, "r") as f:
                line = f.readline().strip()
            data = json.loads(line)
        else:
            data = instance_id
        if not data.get("test_patch") and data_type != "raw":
            return {"instance_id": instance_id, "exception": "No test_patch in metadata"}
        return await process_instance(data, data_type=data_type)
    except Exception as e:
        return {"instance_id": instance_id, "exception": repr(e)}


@app.command()
async def main(
    engine_url: str = f"bus:{COTOGRADER_BUS_TOPIC}",
    renderer_name: str = COTOGRADER_RENDERER,
    juice: int = 1024,
    concurrency_limit: int = 50,
    data_type: Literal["raw", "rendered"] = "raw",
    dataset_path: str = "az://orngscuscresco/data/damajercak/swe/upload09082025/ptsdv1/ptsd-2025-08-12-2025-09-04-full.jsonl",
    output_path: str = "./results.jsonl",
):
    existing_instances = []
    if os.path.exists(output_path):
        with open(output_path, "r") as f:
            for line in f:
                try:
                    data = json.loads(line.strip())
                    existing_instances.append(data["instance_id"])
                except Exception as e:
                    continue

    print(f"Found {len(existing_instances)} existing instances in {output_path}")

    os.environ["ENGINE_URL"] = engine_url
    os.environ["RENDERER_NAME"] = renderer_name
    os.environ["JUICE"] = str(juice)

    if os.path.isdir(dataset_path):
        prs = [f for f in os.listdir(dataset_path)]
        print(
            f"Found {len(prs)} instances in {dataset_path}, filtering out {len(existing_instances)} existing instances"
        )
        prs = [pr for pr in prs if pr not in existing_instances]
    else:
        with bf.BlobFile(dataset_path, "r") as f:
            lines = f.readlines()
        prs = [json.loads(line.strip()) for line in lines if line.strip()]

    print(f"Processing {len(prs)} instances")

    await process_all(
        prs,
        process_instance_safe,
        concurrency_limit=concurrency_limit,
        output_path=output_path,
        src_path=dataset_path,
        data_type=data_type,
    )


if __name__ == "__main__":
    asyncio.run(main())
