"""
Use the VSCode "read_project_structure" tool to get a formatted list of filenames for an instance's repo.
"""

import asyncio
import re
import traceback
from datetime import datetime
from typing import cast

import structlog
from async_utils import process_all
from count_settings_json import FakeDatapoint, data_loader, print_with_time
from deep_swe_msft.tools.vscode_copilot_tool import exec, setup_vscutils
from mini.metrics import metrics_init
from prbot_msft.configs.caas_container_tool import (
    SweBenchHardSetupFn,
    SweBenchHardVSCCaasContainerResourceConfig,
)
from smokey import Smokey

metrics_init(config=None, initial_step=0)
logger = structlog.stdlib.get_logger(component=__name__)


def extract_code_from_last_block(text: str):
    """
    Extracts the code content from the last triple-backtick block.

    Args:
        text: Input text containing potential code blocks

    Returns:
        tuple: (extracted_text, match_found) where match_found is True if a
               triple-backtick block was found, otherwise False.
    """
    pattern = r"```[^\n]*\n([\s\S]*?)```"
    matches = re.findall(pattern, text)
    return (matches[-1], "success") if matches else (text, "no_match")


async def extract_project_structure(dp):
    caas_session = None
    try:
        resource_config = SweBenchHardVSCCaasContainerResourceConfig(
            setup_fn=cast(SweBenchHardSetupFn, setup_vscutils),
            caas_container_image="aio",
        )
        container = await resource_config.initialize_resource(dp=dp)
        caas_session = container.terminal_session.session
        assert caas_session is not None
        project_structure = await exec(caas_session, "read_project_structure", {})
        return project_structure, "success"
    except Exception as e:
        print_with_time(f"Error: {traceback.format_exc()}")
        return None, "error"
    finally:
        if caas_session:
            await caas_session.close()


async def process_one(instance, output_dir):
    """Run a single test instance"""
    val_dp = FakeDatapoint(metadata=instance)
    project_structure, status = await extract_project_structure(val_dp)
    if status == "success":
        project_structure, status = extract_code_from_last_block(project_structure[0])
    return {"project_structure": project_structure, status: 1}


async def main(
    early_stop: int = -1,
    max_concurrent: int = 5,
    input_file: str = "az://orngscuscresco/data/damajercak/swe/upload07312025/sbhv2/train/train.jsonl",
    output_dir: str = "az://orngscuscresco/data/jadhuang/swe/upload07312025-projstruct-output",
):
    start_time = datetime.now()

    instance_results, agg_results = await process_all(
        dataset=data_loader(input_file, early_stop),
        process_function=process_one,
        output_dir=output_dir,
        concurrency_limit=max_concurrent,
    )
    return agg_results, datetime.now() - start_time


if __name__ == "__main__":
    reward, time_elapsed = asyncio.run(Smokey(main))
    print(f"{time_elapsed=}, {reward=}")
