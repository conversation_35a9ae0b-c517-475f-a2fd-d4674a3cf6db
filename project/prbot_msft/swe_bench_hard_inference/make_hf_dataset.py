import json
import os

import datasets
import tqdm
import typer
import xxhash


def generate_multiswebench_hashes(seed: int):
    multiswebench_repos = [
        "facebook/zstd",
        "jqlang/jq",
        "ponylang/ponyc",
        "catchorg/Catch2",
        "fmtlib/fmt",
        "nlohmann/json",
        "simdjson/simdjson",
        "yhirose/cpp-httplib",
        "cli/cli",
        "grpc/grpc-go",
        "zeromicro/go-zero",
        "alibaba/fastjson2",
        "elastic/logstash",
        "mockito/mockito",
        "anuraghazra/github-readme-stats",
        "axios/axios",
        "expressjs/express",
        "iamkun/dayjs",
        "Kong/insomnia",
        "sveltejs/svelte",
        "BurntSushi/ripgrep",
        "clap-rs/clap",
        "nushell/nushell",
        "serde-rs/serde",
        "sharkdp/bat",
        "sharkdp/fd",
        "rayon-rs/rayon",
        "tokio-rs/bytes",
        "tokio-rs/tokio",
        "tokio-rs/tracing",
        "darkreader/darkreader",
        "mui/material-ui",
        "vuejs/core",
    ]
    hashes = set(
        map(lambda repo: xxhash.xxh32(repo.encode(), seed=seed).intdigest(), multiswebench_repos)
    )
    print(f"MULTISWEBENCH_REPO_HASHES = {hashes}")


def generate_swebench_hashes(seed: int):
    ds = datasets.load_dataset("princeton-nlp/SWE-bench")
    swe_dev = set(ds["dev"]["repo"])
    swe_test = set(ds["test"]["repo"])
    swe_dev_hashes = set(
        map(lambda repo: xxhash.xxh32(repo.encode(), seed=seed).intdigest(), swe_dev)
    )
    swe_test_hashes = set(
        map(lambda repo: xxhash.xxh32(repo.encode(), seed=seed).intdigest(), swe_test)
    )
    print(f"SWEBENCH_TEST_REPO_HASHES = {swe_test_hashes}")
    print(f"SWEBENCH_DEV_REPO_HASHES = {swe_dev_hashes}")


def get_split(owner_slash_repo: str):
    # N.B. these were generated by running generate_swebench_hashes(0)
    # If you need to change the seed please rerun to ensure we are not training on swebench verified
    SWEBENCH_TEST_REPO_HASHES = {
        3987523584,
        117584000,
        3055614945,
        680106179,
        3800698274,
        349472416,
        2279502984,
        411760110,
        1246131631,
        1397489742,
        2390677329,
        203853850,
    }
    SWEBENCH_DEV_REPO_HASHES = {
        1291735107,
        2319351948,
        956362733,
        2891079955,
        2095329784,
        3998754009,
    }

    # N.B. these were generated by running generate_multiswebench_hashes(0)
    # If you need to change the seed please rerun to ensure we are not training on multiswebench
    MULTISWEBENCH_REPO_HASHES = {
        847418370,
        2987972999,
        1067188618,
        845069972,
        3802639514,
        2008444061,
        2236661405,
        509349663,
        1614695712,
        1562285730,
        2946235428,
        1050917161,
        412164526,
        3725813939,
        355802035,
        3392726072,
        407952189,
        2556533054,
        320255170,
        3618599492,
        2662932297,
        1151719625,
        3160137804,
        1424840396,
        3767054693,
        861701095,
        2901514472,
        3971965176,
        239287801,
        2464404346,
        2189764091,
        429208569,
        3193599743,
    }

    TEST_REPO_HASHES = (
        SWEBENCH_TEST_REPO_HASHES | MULTISWEBENCH_REPO_HASHES
    )  #  N.B. if more datasets are added, this will need to be updated

    repo_hash = xxhash.xxh32(owner_slash_repo.encode(), seed=0).intdigest()
    if repo_hash in TEST_REPO_HASHES:
        return "test"
    elif repo_hash in SWEBENCH_DEV_REPO_HASHES:
        return "validation"
    else:
        # original swebench splits are about 88.3% (train), 10.7% (test), and 1% (dev)
        # for swe-bench-hard 1010: increase the dev to 4% here b/c it ends up really small
        if repo_hash < 3792456122:  # (2**32)*883//1000
            return "train"
        elif repo_hash < (2**32) * 96 // 100:
            return "test"
        else:
            return "validation"


def is_test_file(filename: str):
    return any(test_word in filename for test_word in ["test", "tests", "e2e", "testing"])


def has_tests(repo_dir: str, instance_id: str, use_prfiles: bool):
    prfiles_path = os.path.join(repo_dir, instance_id, "prfiles.json")
    metadata_path = os.path.join(repo_dir, instance_id, "metadata.json")
    if use_prfiles and os.path.exists(prfiles_path):
        with open(prfiles_path) as inp:
            prfiles = json.load(inp)
            for prfile in prfiles:
                if is_test_file(prfile["filename"]):
                    return True
        return False
    elif os.path.exists(metadata_path):
        with open(metadata_path) as inp:
            metadata = json.load(inp)
            return len(metadata["test_patch"]) > 0
    else:
        return None


def make_hf_datasets_from_dir(repo_dir: str, output_dir: str, use_prfiles: bool = False):
    # Shared helpers keep code DRY with the JSONL variant
    output_handles = _init_output_handles(output_dir)
    for entry in tqdm.tqdm(os.scandir(repo_dir)):
        if not entry.is_dir():
            continue
        dirname = entry.name
        if "__" not in dirname:
            continue
        instance_id = dirname
        with_tests = has_tests(repo_dir, instance_id, use_prfiles)
        if with_tests is None:
            continue
        owner_slash_repo, _ = _parse_instance_id(dirname)
        split = get_split(owner_slash_repo)
        _write_entry(output_handles, with_tests, split, {"instance_id": instance_id})
    _close_output_handles(output_handles)


def make_hf_datasets_from_jsonl(
    file_path: str, output_dir: str, use_prfiles: bool = False, with_tests: bool = True
):
    output_handles = _init_output_handles(output_dir)
    entries = []
    with open(file_path, "r") as f:
        for line in f:
            entries.append(json.loads(line.strip()))
    for entry in tqdm.tqdm(entries):
        instance_id = entry["instance_id"]
        if "__" not in instance_id:
            continue
        owner_slash_repo, _ = _parse_instance_id(instance_id)
        split = get_split(owner_slash_repo)
        _write_entry(output_handles, with_tests, split, {**entry, "instance_id": instance_id})
    _close_output_handles(output_handles)


# -------------------------- Internal Helper Functions -------------------------- #

_SPLITS = ("train", "validation", "test")


def _init_output_handles(output_dir: str):
    """Create directory structure and open one handle per (test_presence, split).

    Returns:
        Dict[bool, Dict[str, IO]]: Nested mapping: with_tests -> split -> handle
    """
    output_handles = {True: {}, False: {}}
    for split in _SPLITS:
        os.makedirs(f"{output_dir}/with/{split}", exist_ok=True)
        os.makedirs(f"{output_dir}/sans/{split}", exist_ok=True)
        output_handles[True][split] = open(f"{output_dir}/with/{split}/{split}.jsonl", "w")
        output_handles[False][split] = open(f"{output_dir}/sans/{split}/{split}.jsonl", "w")
    return output_handles


def _close_output_handles(output_handles):
    for split in _SPLITS:
        # Some workflows might only write to one of the True/False buckets, but both exist.
        output_handles[True][split].close()
        output_handles[False][split].close()


def _parse_instance_id(instance_id: str):
    """Normalize an instance id to derive owner/repo while preserving original id.

    Handles the pattern where a numeric PR suffix may be appended after a dash.
    Returns (owner_slash_repo, normalized_repo_part)
    """
    if "-" in instance_id:
        *prefix, maybe_pr = instance_id.split("-")
        repo_underbars = "-".join(prefix) if all(c.isdigit() for c in maybe_pr) else instance_id
    else:
        repo_underbars = instance_id
    owner, repo = repo_underbars.split("__", 1)
    return f"{owner}/{repo}", repo_underbars


def _write_entry(output_handles, with_tests: bool, split: str, payload: dict):
    """Write a single JSON line entry ensuring required fields are present."""
    output_handles[with_tests][split].write(
        json.dumps({**payload, "split_type": payload.get("split_type", "Full")}) + "\n"
    )


def make_hf_datasets(
    input_path: str, output_dir: str, use_prfiles: bool = False, with_tests: bool = True
):
    if input_path.endswith(".jsonl"):
        make_hf_datasets_from_jsonl(input_path, output_dir, use_prfiles, with_tests)
    else:
        make_hf_datasets_from_dir(input_path, output_dir, use_prfiles)


if __name__ == "__main__":
    typer.run(make_hf_datasets)
