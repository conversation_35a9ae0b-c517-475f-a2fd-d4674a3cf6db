"""
Add two fields to SBHv2 instances for VSCode.
"""
import json

import blobfile as bf
from smokey import <PERSON><PERSON>
from tqdm import tqdm

SETTINGS_FILE = "az://orngscuscresco/data/jadhuang/swe/upload07312025-settingsjson/sbhv2/train/instance_results.jsonl"
PROJ_STRUCTURE_FILE = (
    "az://orngscuscresco/data/jadhuang/swe/upload07312025-projstruct-output/instance_results.jsonl"
)
SBHV2_FILE = "az://orngscuscresco/data/damajercak/swe/upload07312025/sbhv2/train/train.jsonl"
OUTPUT_FILE = "az://orngscuscresco/data/jadhuang/swe/upload08182025/sbhv2/vsc/train/train.jsonl"


def main(
    settings_file: str = SETTINGS_FILE,
    proj_structure_file: str = PROJ_STRUCTURE_FILE,
    sbhv2_file: str = SBHV2_FILE,
    output_file: str = OUTPUT_FILE,
    dry_run: bool = True,
    dry_run_write: bool = False,
):
    instance_to_settings_json = {}
    instance_to_proj_structure = {}
    found_settings_json, total_settings_processed = 0, 0
    with bf.BlobFile(settings_file, "r") as f:
        for line in tqdm(f, desc="Processing settings.json"):
            line = json.loads(line)
            instance_id = line["instance_id"]
            maybe_settings_json = line["result"].get("settings_json")
            if maybe_settings_json:
                instance_to_settings_json[instance_id] = maybe_settings_json
                found_settings_json += 1
            total_settings_processed += 1
    # print distribution of settings found
    print(
        f"Found settings.json for {found_settings_json}/{total_settings_processed}={found_settings_json/total_settings_processed:.2f} instances"
    )
    all_num_filenames = []
    found_proj_structure, total_proj_structure_processed = 0, 0
    with bf.BlobFile(proj_structure_file, "r") as f:
        for line in tqdm(f, desc="Processing project structure"):
            line = json.loads(line)
            instance_id = line["instance_id"]
            maybe_proj_structure = line["result"].get("project_structure")
            if maybe_proj_structure:
                instance_to_proj_structure[instance_id] = maybe_proj_structure
                num_filenames = len(maybe_proj_structure.split("\n"))
                all_num_filenames.append(num_filenames)
                found_proj_structure += 1
            total_proj_structure_processed += 1
    print(
        f"Found project structure for {found_proj_structure}/{total_proj_structure_processed}={found_proj_structure/total_proj_structure_processed:.2f} instances"
    )

    # print distribution of filenames, min, avg, max
    if all_num_filenames:
        print("Distribution of filenames:")
        print(f"  Min: {min(all_num_filenames)}")
        print(f"  Avg: {sum(all_num_filenames) / len(all_num_filenames)}")
        print(f"  Max: {max(all_num_filenames)}")
    examples_to_add = []
    if not dry_run:
        with bf.BlobFile(sbhv2_file, "r") as f:
            for i, line in enumerate(tqdm(f, desc="Adding fields to SBHv2")):
                if dry_run_write:
                    if i == 10:
                        break
                instance = json.loads(line)
                instance_id = instance["metadata"]["instance_id"]
                if instance_id in instance_to_settings_json:
                    # see project/deep_swe_msft/deep_swe_msft/swe_bench_train_v2_vsc/setup/setup.py
                    # which references this field
                    instance["metadata"]["task"] = {
                        "vscode_settings": instance_to_settings_json[instance_id]
                    }
                else:
                    continue
                if instance_id in instance_to_proj_structure:
                    instance["metadata"]["project_structure"] = instance_to_proj_structure[
                        instance_id
                    ]
                else:
                    continue
                examples_to_add.append(instance)
    with bf.BlobFile(output_file, "w") as f:
        for instance in tqdm(examples_to_add, desc="Writing to blobfile"):
            f.write(json.dumps(instance) + "\n")


if __name__ == "__main__":
    Smokey(main)
