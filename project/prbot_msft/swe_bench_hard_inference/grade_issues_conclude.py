"""Summarize grading results produced by `grade_issues.py`.

This script reads a newline-delimited JSON results file (currently hard-coded
to `/root/copilot_ws_2_results.jsonl`) generated by `grade_issues.py`, groups
instances by their assigned grade (e.g. A+, A, A-, ...), prints counts for a
fixed ordered list of grade buckets, and then reports the frequency of
programming languages that appeared in high-scoring instances (A range only).

Typical usage:
    python grade_issues_conclude.py  # after results jsonl has been produced

To adapt for a different input path, modify the hard-coded path in `__main__`.
"""

import json
from collections import defaultdict


def read_data(path):
    """Load a newline-delimited JSONL file into a list of dicts.

    Each line is expected to be a standalone JSON object. Lines that cannot be
    parsed will raise an exception (no silent skipping is performed).

    Args:
        path: Filesystem path to the JSONL results file.

    Returns:
        List of parsed JSON objects (dicts) in file order.
    """
    data = []
    with open(path, "r") as f:
        for line in f:
            data.append(json.loads(line))
    return data


if __name__ == "__main__":
    data = read_data("/root/copilot_ws_2_results.jsonl")
    res = defaultdict(list)
    langs = defaultdict(int)
    for sample in data:
        instance_id = sample.get("instance_id", "")
        grade = sample.get("rating", "")
        languages = sample.get("languages", None)
        if languages is None:
            continue
        res[grade].append(instance_id)
        if not languages:
            languages = ["unknown"]
        if grade in ["A+", "A", "A-"]:
            for lang in languages:
                langs[lang] += 1

    for k in ["A+", "A", "A-", "B+", "B", "B-", "C+", "C", "D"]:
        print(f"{k}: {len(res[k])}")

    langs_sorted = sorted(langs.items(), key=lambda x: x[1], reverse=True)
    langs = {k: v for k, v in langs_sorted if v > 0}
    print("Languages used in A+, A, A-:")
    for k in langs:
        print(f"{k}: {langs[k]}")
