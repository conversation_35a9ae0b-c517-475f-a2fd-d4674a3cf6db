import json
import logging
import os
import re
import sys
import uuid
from enum import Enum
from typing import Dict, Literal, Optional, Tuple

import fire
import structlog
from async_utils import process_all
from datasets import load_dataset
from prbot_msft.caas_handle_utils import <PERSON>aasHandle
from prbot_msft.swebench_hard.example import REPO_DIRECTORY, Example
from prbot_msft.swebench_hard.stream import StreamSWEDataset, SweDatum
from torch.utils.data import DataLoader

logger = logging.getLogger(__name__)
file_abs_path = os.path.dirname(os.path.abspath(__file__))


class Scenario(str, Enum):
    SETUP = "setup"
    REPRO = "repro"
    REPAIR = "repair"
    REPAIR_NOEXEC = "repair-noexec"


def set_global_logging_level(level):
    logging.getLogger().setLevel(level)  # Set root logger level
    for logger_name in logging.root.manager.loggerDict:
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)


async def main(
    output_base_path: str,
    dataset_path: str = "az://orngscuscresco/data/damajercak/ptsd-2025-08-12-2025-09-04-hf",
    repo_dir: str = "az://orngtransfer/grninbound/data/damajercak/ptsd-2025-08-12-2025-09-07/copilot_ws",
    split: str = "train",
    filter_for_tests: bool = False,
    filter_for_resolved: bool = False,
    scenario: Scenario = "repair-noexec",
    concurrency_limit: int = 10,
    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "CRITICAL",
    early_stop: Optional[int] = None,
):
    """Renders the SWE-Bench Hard dataset in jsonl format, where each line contains messages and metadata."""
    structlog.configure(
        wrapper_class=structlog.make_filtering_bound_logger(getattr(logging, log_level.upper()))
    )
    set_global_logging_level(getattr(logging, log_level.upper()))
    scenario = Scenario(scenario)
    os.makedirs(output_base_path, exist_ok=True)
    if dataset_path.startswith("az://"):
        # download with blobfile
        import blobfile as bf
        from bf_utils import get_files

        bf_output_path = f"{output_base_path}/temp_dataset_path/{split}"
        os.makedirs(bf_output_path, exist_ok=True)
        bf_files = get_files(dataset_path)
        for fname in bf_files:
            data = []
            with bf.BlobFile(fname, "r") as f:
                for line in f:
                    sample = json.loads(line)
                    # Remove hints if they exist, as they may have either string or array type and HF datasets then complains about it
                    if "hints" in sample:
                        del sample["hints"]
                    data.append(sample)
            new_dataset_path = f"{bf_output_path}/{bf.basename(fname)}"
            with open(new_dataset_path, "w") as f:
                contents = "\n".join([json.dumps(sample) for sample in data]) + "\n"
                f.write(contents)
        dataset_path = bf_output_path
        print(f"new dataset path: {dataset_path}")
    dataset = load_dataset(dataset_path, streaming=True)[split]
    if early_stop:
        dataset = dataset.take(early_stop)
    ssd = StreamSWEDataset(rank=0, world_size=1, repo_dir=repo_dir, metadata=dataset, seekto=None)
    ssdl = DataLoader(
        ssd, num_workers=0, prefetch_factor=None, collate_fn=lambda x: x, batch_size=None
    )

    def process_function(instance, *args, **kwargs):
        return render_instance(
            instance=instance,
            filter_for_tests=filter_for_tests,
            filter_for_resolved=filter_for_resolved,
            scenario=scenario,
        )

    print(f"🚀 Starting to process dataset with scenario: {scenario}")
    print(f"📊 Concurrency limit: {concurrency_limit}")
    if early_stop:
        print(f"⚡ Early stop enabled, processing only {early_stop} instances")
    else:
        print("📈 Processing full dataset (no early stop)")

    print("⏳ Processing instances...")
    await process_all(
        dataset=ssdl,
        process_function=process_function,
        output_dir=output_base_path,
        concurrency_limit=concurrency_limit,
    )

    print("✅ Finished processing dataset")
    print("📋 Reading results...")

    with open(f"{output_base_path}/instance_results.jsonl") as f:
        results = [json.loads(line) for line in f]

    print(f"📊 Total results processed: {len(results)}")

    results_to_keep = [
        instance_result
        for instance_result in results
        if instance_result["result"].get("success", 0) == 1
    ]
    print(
        f"✅ Total results kept: {len(results_to_keep)}/{len(results)} ({len(results_to_keep)/len(results)*100:.1f}% success rate)"
    )

    if len(results_to_keep) > 0:
        print(f"💾 Writing {len(results_to_keep)} successful tasks to {split}.jsonl")
        with open(f"{output_base_path}/{split}.jsonl", "w") as f:
            for result in results_to_keep:
                task = result["result"].get("task", None)
                if task is not None:
                    f.write(f"{json.dumps(task)}\n")
        print(f"🎉 Successfully created {output_base_path}/{split}.jsonl")
    else:
        print("⚠️  No successful results to write")


def make_repair_prompt(instance: SweDatum) -> Tuple[str, str]:
    # This is replaced during runtime so we can keep it simple
    return None, None


async def render_instance(
    instance: SweDatum,
    filter_for_tests: bool,
    filter_for_resolved: bool,
    scenario: Scenario,
) -> dict:
    print(f"🔄 Processing instance: {instance.instance_id} ({scenario.value} scenario)")

    if filter_for_tests and len(instance.test_patch) == 0:
        print(f"⏭️  Skipping {instance.instance_id}: No test patch")
        return {"skipped": 1}
    if (
        scenario == Scenario.REPAIR
        and filter_for_resolved
        and not await is_instance_suitable_for_repair(instance)
    ):
        return {"skipped": 1}
    if scenario == Scenario.REPAIR or scenario == Scenario.REPAIR_NOEXEC:
        user_prompt, dev_prompt = make_repair_prompt(
            instance=instance,
        )
    else:
        raise ValueError(f"Unknown scenario: {scenario}")
    if user_prompt is None:
        print(f"⏭️  Skipping {instance.instance_id}: No user prompt generated")
        return {"skipped": 1}
    task_id = str(uuid.uuid4())
    task = {
        "task_id": task_id,
        "unique_id": instance.instance_id + "_" + scenario.value + "_" + task_id,
        "problem": user_prompt,
        "metadata": instance.as_dict(),
    }
    print(f"✅ Completed instance: {instance.instance_id}")
    return {"success": 1, "task": task}


async def is_instance_suitable_for_repair(instance: SweDatum) -> bool:
    async with await CaasHandle.create(image_name=instance.image_name) as handle:
        example = await Example.create(
            swe_datum=instance, container_handle=handle, timeout=3_600_000
        )
        base_resolved = example.base_report["resolved"]
        gold_resolved = example.gold_report["resolved"]
        if gold_resolved and not base_resolved:
            suitable = True
        else:
            suitable = False
        return suitable


if __name__ == "__main__":
    fire.Fire(main)
