# Problem

You are given a Task: a Question prompt (§ Question) and a corresponding Silver Solution patch (§ Silver Solution) against a software repository. As a seasoned developer, you must evaluate both:

1. **The Question itself** – its clarity, completeness, and potential ambiguities.  
2. **The Silver Solution** – how accurately and elegantly it implements what the Question asks, including any inferred assumptions.

§ Question

{{problem_statement}}

§ Silver Solution

{{silver_patch}}

§ Instructions

### Structure your evaluation around these five pillars

1. **Question Quality**  
   - Are requirements stated unambiguously?  
   - Is any crucial context or constraint missing?  
   - Could different developers reasonably interpret the task in conflicting ways?

2. **Coverage of Requirements**  
   - Map each explicit requirement in the Question to concrete code changes in the Silver Solution.  
   - Identify any requirements the solution omits or misimplements.

3. **Design & Maintainability**  
   - Assess the architecture, modularity, naming, and readability of the patch.  
   - Highlight any over‑engineering or shortcuts that compromise future maintenance.

4. **Assumptions & Edge Cases**  
   - List any unstated assumptions the solution relies on.  
   - Evaluate how it handles edge cases implied (or absent) in the Question.

5. **Reproducibility by a Seasoned Developer**  
   - Could an experienced engineer, given only the Question and access to the codebase, arrive at an equivalent solution without undocumented knowledge?  
   - Does the solution fill in gaps sensibly, or does it require hidden context?

### Grading

- A+: Question is precise; Silver Solution implements every requirement cleanly with elegant design and no hidden assumptions.
- A: Requirements clear; solution correct and maintainable; minor non‑essential refactoring.
- A-: Question slightly under‑specified but solution fills gaps gracefully; small design nit may omit a minor detail or edge case.
- B+: Mostly clear Question; solution meets core needs but contains test that require specific wording, not mentioned in the Question, that may be interperted differently by other developer.
- B: Question has modest ambiguities; solution covers primary functionality but handles some details poorly.
- B-: Multiple ambiguities in the Question; solution works but embeds hidden assumptions or is over‑complex.
- C+: Question poorly scoped; solution only partially implements requirements.
- C: Question unclear or contradictory; solution misses several key aspects.
- D: Question is unanswerable as written; Silver Solution is misaligned or irrelevant.

### Language

Given a § Silver Solution, identify the **programming languages** used in the PR based **only on file extensions and contents**. You must:
- **Identify programming or scripting languages** from file extensions (e.g., `.py` → Python, `.js` → JavaScript).
- **Ignore non-code files**, including but not limited to:
  - Plain text or markdown: `.txt`, `.md`
  - Configuration: `.yml`, `.yaml`, `.json`, `.toml`
  - Assets or data: `.csv`, `.png`, `.svg`, `.jpg`, `.ico`, etc.
- Do **not guess** based on filenames or paths—only use the file extension and contents.
- Output a **deduplicated list** of programming languages.
- If no relevant files are found, return an empty list

### Category

Given a § Silver Solution, classify it into one of the following categories:
- feature_development
- bug_fixing
- refactoring
- minor_changes_and_chores
- test_writing
- writing_and_updating_documentation
- devops


Finally, format your answer as a JSON object with the following structure:

```json
{
  "question_issues":    "Concise summary of any ambiguities, missing context, or potential misinterpretations in the Question.",
  "solution_alignment": "Evaluation of how thoroughly and cleanly the Silver Solution meets the clarified requirements.",
  "hints":  "In case of ambiguous question, provide hints that would help developer to address issue such that the their patch is much more likely to be aligned with the Silver Solution. This may for example include suggested class names, parameter names, other signatures and exception, however ***must not*** contain any direct hint on how to resolve the Question"
  "rating":             "A+, A, A-, B+, B, B-, C+, C, or D",
  "languages":          ["C#", "JavaScript"],
  "category":           "feature_development"
}
```