# Problem

You are given a Task: a Github issue title (§ Issue Title) and its body (§ Issue Body). Your task is to assign category to it.

§ Issue Title

{{issue_title}}

§ Issue Body

{{issue_body}}

§ Instructions

### Category

Given a § Issue Title and § Issue Body, classify it into one of the following categories:
- feature_development
- bug_fixing
- refactoring
- minor_changes_and_chores
- test_writing
- writing_and_updating_documentation

If it does not fit into any of the categories, feel free to create new one.


Finally, format your answer as a JSON object with the following structure:

```json
{
  "category":           "feature_development"
  "reasoning":          "place any reasoning on why was the issue assigned a particular category, be succint"
}
```