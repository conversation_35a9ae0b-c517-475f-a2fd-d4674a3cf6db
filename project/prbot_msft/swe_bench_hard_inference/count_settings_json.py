"""
Check the existence of .vscode/settings.json in an instance's repo.
If it exists, read and return its content.
"""

import argparse
import asyncio
import dataclasses
import json
import traceback
from datetime import datetime
from typing import cast

import blobfile
from async_utils import process_all
from deep_swe_msft.tools.vscode_copilot_tool import setup_vscutils
from prbot_msft.caas_handle_utils import CaasHandle
from prbot_msft.configs.caas_container_tool import (
    SweBenchHardSetupFn,
    SweBenchHardVSCCaasContainerResourceConfig,
)
from prbot_msft.graders.swebenchhard_grader_base import get_instance_from_metadata
from prbot_msft.swebench_hard.container import RemoteError, success_output
from prbot_msft.swebench_hard.example import Example

WORKDIR = "/testbed"


async def main(dp):
    resource_config = SweBenchHardVSCCaasContainerResourceConfig(
        setup_fn=cast(SweBenchHardSetupFn, setup_vscutils),
        caas_container_image="aio",
    )
    caas_session = None
    try:
        container = await resource_config.initialize_resource(dp=dp)
        caas_session = container.terminal_session.session
        assert caas_session is not None

        example = await create_example(dp, container)
        try:
            settings_json = await success_output(
                handle=example.container_handle,
                cmd=["cat", ".vscode/settings.json"],
                workdir=WORKDIR,
                timeout=900,
                return_bytes=False,
            )
            return settings_json, "has"
        except RemoteError:
            # settings json doesn't exist
            return None, "not_found"
    except Exception as e:
        print_with_time("Error: ", traceback.format_exc())
        return None, "error"
    finally:
        if caas_session:
            await caas_session.close()


async def create_example(dp, container):
    caas_handle = CaasHandle()
    caas_handle._caas_handle = container
    instance = get_instance_from_metadata(dp.metadata)
    example = await Example.create(
        swe_datum=instance,
        container_handle=caas_handle,
        timeout=3_600_000,
        run_setup=False,
        run_tests=False,
    )
    return example


@dataclasses.dataclass
class FakeDatapoint:
    # Mocking a HarmonyDatapoint
    metadata: dict


def print_with_time(*args, **kwargs):
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}]", *args, **kwargs)


async def run_single_test(instance, output_dir):
    """Run a single test instance"""
    val_dp = FakeDatapoint(metadata=instance)
    settings_json, status = await main(val_dp)
    return {"settings_json": settings_json, status: 1}


def data_loader(path, early_stop):
    with blobfile.BlobFile(path, "r") as f:
        for i, line in enumerate(f):
            data = json.loads(line.strip())
            yield data["metadata"]
            if early_stop != -1 and i >= early_stop:
                break


async def run_long_mode(args):
    start_time = datetime.now()
    if args.mode == "long":
        val_path = args.input_path
    else:
        val_path = "az://orngscuscresco/data/jadhuang/sbhv2_train_sample/sbhv2_train_sample.jsonl"
    instance_results, agg_results = await process_all(
        dataset=data_loader(val_path, args.early_stop),
        process_function=run_single_test,
        output_dir=args.output_dir,
        concurrency_limit=args.max_concurrent,
    )
    return agg_results, datetime.now() - start_time


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run quick or long test mode.")
    parser.add_argument(
        "--mode", choices=["quick", "long"], default="quick", help="Test mode: quick or long"
    )
    parser.add_argument("--early_stop", type=int, default=-1, help="Early stop iteration")
    parser.add_argument(
        "--max_concurrent",
        type=int,
        default=5,
        help="Maximum number of concurrent tasks for long mode",
    )

    parser.add_argument(
        "--input_path",
        type=str,
        default="az://orngscuscresco/data/damajercak/swe/upload07312025/sbhv2/train/train.jsonl",
        help="Input path",
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default="az://orngscuscresco/data/jadhuang/swe/upload07312025-settingsjson/sbhv2/train",
        help="Directory to save output files",
    )

    args = parser.parse_args()
    reward, time_elapsed = asyncio.run(run_long_mode(args))
    print(f"{time_elapsed=}, {reward=}")
