import asyncio
import json
import logging
import numbers
import sys
import traceback
from collections import Counter
from typing import Any, Awaitable, Callable

import aiofiles
import blobfile as bf
import tqdm
from torch.utils.data import DataLoader

logger = logging.getLogger(__name__)


async def _process_object(
    instance: Any,
    process_function: Callable[..., Awaitable[dict[str, int]]],
    output_dir: str,
    **kwargs,
):
    instance_id = instance["instance_id"]
    try:
        results = await process_function(instance=instance, output_dir=output_dir, **kwargs)
        return instance_id, True, results
    except Exception as e:
        logger.error(f"Skipping {instance_id} because {e}")
        logger.info(traceback.format_exc())
        return instance_id, False, {"exception": str(e), "traceback": traceback.format_exc()}


async def process_all(
    dataset: DataLoader,
    process_function: Callable[..., Awaitable[dict[str, int]]],
    output_dir: str,
    concurrency_limit: int,
    **kwargs,
):
    """
    Process all instances in the dataset with improved memory efficiency and progress reporting.
    Uses a producer-consumer pattern to avoid loading all tasks into memory at once.
    """
    semaphore = asyncio.Semaphore(concurrency_limit)
    results = {}
    sum_results = Counter()

    # Create output files
    reward_file = f"{output_dir}/reward.json"
    results_file = f"{output_dir}/instance_results.jsonl"

    # Clear previous results file
    with bf.BlobFile(results_file, "w") as f:
        pass  # Just create/clear the file

    async def process_with_semaphore(instance):
        async with semaphore:
            return await _process_object(instance, process_function, output_dir, **kwargs)

    # Convert dataset to iterator for memory efficiency
    dataset_iter = iter(dataset)

    # Initialize progress tracking
    processed_count = 0
    total_tasks = 0
    active_tasks = set()

    print(f"📊 Starting processing with concurrency limit: {concurrency_limit}")

    # Producer-consumer pattern
    try:
        with tqdm.tqdm(
            desc="Processing instances", unit="instance", disable=not sys.stderr.isatty()
        ) as pbar:
            # Start initial batch of tasks
            while len(active_tasks) < concurrency_limit:
                try:
                    instance = next(dataset_iter)
                    task = asyncio.create_task(process_with_semaphore(instance))
                    active_tasks.add(task)
                    total_tasks += 1
                except StopIteration:
                    break

            # Process tasks as they complete and start new ones
            while active_tasks:
                # Wait for at least one task to complete
                done, pending = await asyncio.wait(
                    active_tasks, return_when=asyncio.FIRST_COMPLETED
                )
                active_tasks = pending

                # Process completed tasks
                for task in done:
                    try:
                        instance_id, status, result = await task
                        results[instance_id] = result
                        processed_count += 1

                        # Update counters
                        sum_results["total"] += 1
                        if not status:
                            sum_results["error"] += 1
                            print(
                                f"❌ Error processing {instance_id}: {result.get('exception', 'Unknown error')}"
                            )
                        else:
                            sum_results += {
                                k: v
                                for k, v in result.items()
                                if isinstance(v, numbers.Number) and not k.startswith("_")
                            }
                            print(f"✅ Completed {instance_id}")

                        # Update progress bar
                        pbar.set_postfix(
                            {
                                **{k: v for k, v in sum_results.items() if k != "total"},
                                "success_rate": f"{((sum_results['total'] - sum_results.get('error', 0)) / max(sum_results['total'], 1) * 100):.1f}%",
                            }
                        )
                        pbar.update(1)

                        # Write results incrementally
                        with bf.BlobFile(reward_file, "w") as f:
                            f.write(json.dumps(dict(sum_results)))

                        with bf.BlobFile(results_file, "a") as f:
                            task_res = {
                                "instance_id": instance_id,
                                "status": status,
                                "result": result,
                            }
                            f.write(json.dumps(task_res) + "\n")

                    except Exception as e:
                        print(f"❌ Unexpected error processing task: {e}")
                        logger.error(f"Unexpected error: {e}")

                # Start new tasks to maintain concurrency
                while len(active_tasks) < concurrency_limit:
                    try:
                        instance = next(dataset_iter)
                        task = asyncio.create_task(process_with_semaphore(instance))
                        active_tasks.add(task)
                        total_tasks += 1
                    except StopIteration:
                        break

    except Exception as e:
        print(f"❌ Fatal error during processing: {e}")
        logger.error(f"Fatal error: {e}")
        raise

    print(f"🎉 Processing complete! Processed {processed_count} instances")
    print(
        f"📊 Success rate: {((sum_results['total'] - sum_results.get('error', 0)) / max(sum_results['total'], 1) * 100):.1f}%"
    )

    return results, sum_results
