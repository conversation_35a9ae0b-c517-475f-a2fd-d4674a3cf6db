"""
Since setups are currently not permanent (the container are created on the go and not commited), it may happen that they go stale.
This utility offer an easy way to go over current setups and filter them down to only ones that are still paasing predefined rules.
It also update metadata along the way to reflect current state of matters.
"""
import argparse
import asyncio
import json
import os
import shlex
import sys
import time
from collections import defaultdict
from dataclasses import dataclass
from threading import Lock

from caas_tool.caas_container import CaasContainer
from prbot_msft.caas_handle_utils import CaasHandle
from prbot_msft.swebench_hard.example import Example
from prbot_msft.swebench_hard.stream import SweDatum
from tqdm import tqdm

CAAS_IMAGE = "acrbuiltincaasglobalame.azurecr.io/prbot:20250131a"
CAAS_ENDPOINT = "http://eastus2-01.caas.net/"
ENGINE_URL = "http://localhost:5122/v1/inference"
RENDERER_NAME = "harmony_v4.0.15_berry_v3_128k_orion_mm_w_truncation"
TIMEOUT = 3600 * 1000
IDLE_TTL = 3600
MIN_TESTS = 1


def get_env():
    return {"CAAS_CLIENT_ID": f"prbot-onboarding"}


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--data_path",
        type=str,
        default="/scratch/AzureBlobStorage_CODE/scratch/workspaceblobstore/damajercak/copilot_ws_output_merged_v4",
    )
    parser.add_argument("--is_repo", action="store_true")
    parser.add_argument(
        "--meta_path",
        type=str,
        default="/scratch/AzureBlobStorage_CODE/scratch/workspaceblobstore/damajercak/copilot_ws/copilot_ws/",
    )
    parser.add_argument(
        "--prev_output_path",
        type=str,
        default="/scratch/AzureBlobStorage_CODE/scratch/workspaceblobstore/damajercak/copilot_ws_output_merged_v3_updated",
    )
    parser.add_argument(
        "--output_path",
        type=str,
        default="/scratch/AzureBlobStorage_CODE/scratch/workspaceblobstore/damajercak/copilot_ws_output_merged_v4_updated",
    )
    parser.add_argument("--omit_datetime", action="store_true")

    parser.add_argument("--parallel", type=int, default=100)
    parser.add_argument("--retries_parallel", type=int, default=2)
    return parser.parse_args()


@dataclass
class SweDatumSetup(SweDatum):
    top_language: str = ""
    tests_total: int = 0
    tests_passing: int = 0
    tests_total_patched: int = 0
    tests_passing_patched: int = 0

    def get(self, key, default=None):
        try:
            return self.__getattribute__(key)
        except:
            return default

    def from_metadata(metadata: dict):
        instance = SweDatumSetup(
            instance_id=metadata["instance_id"],
            repo=metadata["repo"],
            base_commit=metadata["base_commit"],
            split_type=metadata.get("split_type", "Full"),
            problem_statement=metadata["problem_statement"],
            hints_text=metadata["hints_text"],
            version=metadata["version"],
            patch=metadata["patch"],
            test_patch=metadata["test_patch"],
            setup_time=-1,
            setup_script=metadata.get("setup_script"),
            test_script=metadata.get("test_script"),
            initial_gitdiff=metadata.get("initial_gitdiff"),
            filenames=metadata.get("filenames"),
            language_count=metadata.get("language_count"),
            merge_commit=metadata.get("merge_commit", None),
        )
        instance.top_language = metadata.get("top_language", "")
        return instance

    def update_from_metadata(self, metadata: dict):
        skipped_attr = []
        for k, v in metadata.items():
            if hasattr(self, k):
                self.__setattr__(k, v)
            else:
                skipped_attr.append(k)
        if len(skipped_attr) > 0:
            print(f"Skipped attributes: {skipped_attr}")
        return skipped_attr

    def update_from_example(self, example: Example):
        base_results = example._base_results
        gold_code_results = example._gold_code_results

        self.tests_results = base_results
        self.tests_total = len(base_results)
        self.tests_passing = sum(1 for k, v in base_results.items() if v.lower() == "passed")

        self.tests_results_patched = gold_code_results
        self.tests_total_patched = len(gold_code_results)
        self.tests_passing_patched = sum(
            1 for k, v in gold_code_results.items() if v.lower() == "passed"
        )

        self.fail_to_pass = example.fail_to_pass
        self.pass_to_pass = example.pass_to_pass
        self.other_to_other = example.other_to_other


class CustomCaasContainer(CaasContainer):
    async def exec(
        self,
        cmd: list[str],
        workdir: str | None,
        timeout: int | None,
        env: dict[str, str] | None,
        user: str | None = None,
    ) -> tuple[int, bytes]:
        retries = 3
        cmd = ["timeout", "3000", "sh", "-c", " ".join(map(shlex.quote, cmd)) + " | tail -c 100000"]
        while retries > 0:
            try:
                return await super().exec(cmd=cmd, workdir=workdir, timeout=timeout, env=env)
            except Exception as e:
                retries -= 1
                print(f"Exec {cmd}, retries left: {retries}, failed with: {repr(e)}")
                if retries <= 0:
                    raise e


def validate_instance(instance, is_repo=False):
    if (
        instance.get("tests_total", 0) >= MIN_TESTS
        and instance.get("tests_passing", 0) >= MIN_TESTS
        and len(instance.get("tests_results", {})) >= MIN_TESTS
    ):
        if is_repo:
            return True
        if (
            instance.get("tests_total_patched", 0) >= MIN_TESTS
            and instance.get("tests_passing_patched", 0) >= MIN_TESTS
            and len(instance.get("tests_results_patched", {})) >= MIN_TESTS
        ):
            if instance.get("tests_total_patched", 0) != instance.get(
                "tests_total", 0
            ) or instance.get("tests_passing_patched", 0) != instance.get("tests_passing", 0):
                return True
    return False


def write_instance(output_dir, instance: SweDatumSetup):
    os.makedirs(f"{output_dir}/{instance.instance_id}", exist_ok=True)
    with open(f"{output_dir}/{instance.instance_id}/setup.sh", "w") as f:
        f.write(instance.setup_script)
    with open(f"{output_dir}/{instance.instance_id}/run_tests.sh", "w") as f:
        f.write(instance.test_script)
    with open(f"{output_dir}/{instance.instance_id}/gitdiff.patch", "w") as f:
        f.write(instance.initial_gitdiff)
    with open(f"{output_dir}/{instance.instance_id}/metadata.json", "w") as f:
        f.write(json.dumps(instance.as_dict(), indent=2))


def update_instance(path: str, instance: SweDatumSetup):
    if (
        os.path.exists(f"{path}/metadata.json")
        and os.path.exists(f"{path}/setup.sh")
        and os.path.exists(f"{path}/run_tests.sh")
        and os.path.exists(f"{path}/gitdiff.patch")
    ):
        with open(f"{path}/setup.sh", "r") as f:
            instance.setup_script = f.read()
        with open(f"{path}/run_tests.sh", "r") as f:
            instance.test_script = f.read()
        with open(f"{path}/gitdiff.patch", "r") as f:
            instance.initial_gitdiff = f.read()
        with open(f"{path}/metadata.json", "r") as f:
            instance.update_from_metadata(json.loads(f.read()))
    else:
        return instance


async def process_instance(
    repo,
    instance: SweDatumSetup,
    meta_path=None,
    input_dir=None,
    output_dir=None,
    prev_output_dir=None,
) -> bool:
    assert repo is not None, "Repo cannot be None"

    if instance is None:
        instance = SweDatumSetup.from_metadata(
            dict(
                repo=repo,
                instance_id=repo.replace("/", "__"),
                base_commit="",
                patch="",
                test_patch="",
                problem_statement="",
                hints_text="",
                version="",
                split_type="copilot_ws",
                setup_time=-1,
            )
        )
    if type(instance) == str:
        instance_id = instance
    else:
        instance_id = instance.instance_id
    if os.path.exists(os.path.join(meta_path, instance_id, "metadata.json")):
        with open(os.path.join(meta_path, instance_id, "metadata.json"), "r") as f:
            metadata = json.loads(f.read())
            instance = SweDatumSetup.from_metadata(metadata)

    update_instance(os.path.join(input_dir, instance_id), instance)
    if prev_output_dir is not None:
        update_instance(os.path.join(prev_output_dir, instance_id), instance)
    update_instance(os.path.join(output_dir, instance_id), instance)

    async with await CaasHandle.create(image_name=instance.image_name) as caas_handle:
        example: Example = await Example.create(
            swe_datum=instance, container_handle=caas_handle, timeout=TIMEOUT
        )
        instance.update_from_example(example)
        if validate_instance(instance) and instance.image_name is None:
            instance.image_name = await caas_handle.commit(
                f"swe-bench-hard-{instance.instance_id.lower()}", "v3"
            )

    if output_dir is not None and validate_instance(instance):
        write_instance(output_dir, instance)

    return instance


async def process_all(
    dataset,
    output_dir,
    prev_output_dir=None,
    input_dir=None,
    concurrency_limit=10,
    is_repo=False,
    meta_path=None,
    ratio_range=[0, 1],
    retries_parallel=3,
):
    semaphore = asyncio.Semaphore(concurrency_limit)
    mutex = Lock()

    results = []
    metrics_success = defaultdict(list)
    metric_reuse = defaultdict(list)
    metrics_failure = defaultdict(list)

    async def map_with_concurrency_limit(func, generator, max_concurrent_tasks):
        with tqdm(total=len(generator)) as pbar:
            queue = asyncio.Queue()
            output = {}

            async def worker():
                while True:
                    idx, kwargs = await queue.get()
                    try:
                        output[idx] = await func(**kwargs)
                    except Exception as e:
                        output[idx] = None
                        print(f"Exception in task: {e}")
                    finally:
                        queue.task_done()
                        pbar.update(1)

            workers = [asyncio.create_task(worker()) for _ in range(max_concurrent_tasks)]

            for idx, kwargs in enumerate(generator):
                await queue.put((idx, kwargs))

            await queue.join()

            for w in workers:
                w.cancel()
            cancelled_errors = await asyncio.gather(*workers, return_exceptions=True)
            final_outputs = []
            for i in sorted(output):
                final_outputs.extend(output[i])
            return final_outputs

    async def process_with_semaphore(repo, instance_list, prev_instances=None):
        async with semaphore:
            if prev_instances is None:
                prev_instances = []
            process_results = []
            for instance in instance_list:
                start_time = time.perf_counter()
                try:
                    result = await process_instance(
                        repo,
                        instance,
                        meta_path=meta_path,
                        input_dir=input_dir,
                        output_dir=output_dir,
                        prev_output_dir=prev_output_dir,
                    )
                    with mutex:
                        if validate_instance(result):
                            metrics_success[repo].append(instance)
                        else:
                            metrics_failure[repo].append(instance)
                    process_results.append(result.tests_passing > 0)
                except Exception as e:
                    with mutex:
                        metrics_failure[repo].append(instance)
                    process_results.append(False)
                finally:
                    end_time = time.perf_counter()
                    with mutex:
                        success_prs = sum([len(prs) for prs in metrics_success.values()])
                        reuse_prs = sum([len(prs) for prs in metric_reuse.values()])
                        failure_prs = sum([len(prs) for prs in metrics_failure.values()])
                        print(
                            f"Repos: {len(metrics_success)}/{len(set(metrics_success).union(metrics_failure))}, PRs: {success_prs}({reuse_prs} reused)/{success_prs + failure_prs}, instance: {instance}, repo: {repo}, elapsed time: {end_time - start_time} (seconds)"
                        )
        return process_results

    dataset_dict = defaultdict(list)

    for instance in dataset:
        if is_repo:
            dataset_dict[instance] = [None]
        else:
            dataset_dict["-".join(instance.split("-")[:-1])].append(instance)
    if not is_repo:
        for repo in dataset_dict:
            dataset_dict[repo].sort(key=lambda instance: int(instance.split("-")[-1]), reverse=True)

    if ratio_range[0] > 0 or ratio_range[1] < 1:
        dict_keys = sorted(list(dataset_dict.keys()), key=str.casefold)
        start_index, end_index = int(ratio_range[0] * len(dict_keys)), int(
            ratio_range[1] * len(dict_keys)
        )
        dataset_dict = {k: dataset_dict[k] for k in dict_keys[start_index:end_index]}

    ordered_instances = []
    instance_appended = True
    curr_instance_index = 0
    while instance_appended:
        instance_appended = False
        for repo in dataset_dict:
            if len(dataset_dict[repo]) > curr_instance_index:
                ordered_instances.append(
                    (dict(repo=repo, instance_list=[dataset_dict[repo][curr_instance_index]]))
                )
                instance_appended = True
        curr_instance_index += 1

    ordered_instances_full = []
    for _ in range(retries_parallel):
        ordered_instances_full += ordered_instances
    results.extend(
        await map_with_concurrency_limit(
            process_with_semaphore, ordered_instances_full, concurrency_limit
        )
    )
    return results


def get_checked_instances(outputs_folder):
    checked_instances = set()
    # Iterate through subfolders
    for subfolder in os.listdir(outputs_folder):
        subfolder_path = os.path.join(outputs_folder, subfolder)
        if os.path.isdir(subfolder_path):  # Ensure it's a directory
            report_file = os.path.join(subfolder_path, "report.json")
            if os.path.exists(report_file):
                checked_instances.add(subfolder)
    return checked_instances


if __name__ == "__main__":
    args = parse_args()
    dataset = [
        instance
        for instance in os.listdir(args.data_path)
        if os.path.isdir(os.path.join(args.data_path, instance))
    ]

    async_process_all = process_all(
        dataset,
        is_repo=args.is_repo,
        concurrency_limit=args.parallel,
        meta_path=args.meta_path,
        input_dir=args.data_path,
        output_dir=args.output_path,
        prev_output_dir=args.prev_output_path,
        ratio_range=[0, 1],
        retries_parallel=args.retries_parallel,
    )
    passes = asyncio.run(async_process_all)

    print("*" * 100)
    print(f"Evaluation Results: ")
    print(passes)
    print(f"mean = {sum(passes)/len(passes)} | len = {len(passes)}")
