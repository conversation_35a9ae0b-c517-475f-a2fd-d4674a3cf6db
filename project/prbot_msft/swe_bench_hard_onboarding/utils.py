import os
import shlex
import time

from caas.commands import RawExec, UploadFile
from caas.protocol import NetworkMode
from caas_tool.caas_container import CaasContainer
from enhanced_caas_tool.enhanced_caas_container_tool import _retry_callback
from tenacity import retry, stop_after_attempt, wait_random_exponential

CAAS_IMAGE = "acrbuiltincaasglobalame.azurecr.io/aio:20250617-1236968-official"
CAAS_ENDPOINT = "https://eastus2.caas.azure.com"
ENGINE_URL = "bus:az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted"
RENDERER_NAME = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"
TIMEOUT = 1800 * 1000
IDLE_TTL = 3600
MIN_TESTS = 10


def get_env():
    return None


async def install_certs(terminal_session):
    mitproxy_cert = """\
-----BEGIN CERTIFICATE-----
MIIDmTCCAoGgAwIBAgIUQ+pd05c+7eJXBTMnXRHMddE4oRswDQYJKoZIhvcNAQEL
BQAwXDELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAldBMRAwDgYDVQQHDAdSRURNT05E
MRYwFAYDVQQKDA1NSUNST1NPRlQuQ09NMRYwFAYDVQQDDA1NaXRtUHJveHlDZXJ0
MB4XDTI1MDMxNDE2Mzc1MVoXDTM1MDMxMjE2Mzc1MVowXDELMAkGA1UEBhMCVVMx
CzAJBgNVBAgMAldBMRAwDgYDVQQHDAdSRURNT05EMRYwFAYDVQQKDA1NSUNST1NP
RlQuQ09NMRYwFAYDVQQDDA1NaXRtUHJveHlDZXJ0MIIBIjANBgkqhkiG9w0BAQEF
AAOCAQ8AMIIBCgKCAQEAumaxiKNAud7dApuULcoU4YcskmTOJbTeGYv+eoEztitI
wjKFBAjLoHxeTJGwjsdTsNN/lj3d3DTzBSf5uQ96iNGlUm1A06NzQvk69G5sqKue
1zeA5m+4MWSiUMs5elXljRbZFeF4MaDvFLbok3AlaxHA3XdVywrzKJIdDQgfqcJO
vfB08qRkEgD60Gyi9Av4VJtY8fEfr0ona+h8CHuJRuNUYR1auU4CVzgWhXv0z1+0
6TZo9TxgPSih8eQpU8WERiHE6bxn26eqEo2Zqg29Zy0toRDfblgDYJyWB7RtwrCz
mSeUSPZvZ/8dDkd/To3NFYLpy5uJ9RoXFPnLi7bauQIDAQABo1MwUTAdBgNVHQ4E
FgQU8TbJ7yHKim/Mz6QkhsROyw3fR0gwHwYDVR0jBBgwFoAU8TbJ7yHKim/Mz6Qk
hsROyw3fR0gwDwYDVR0TAQH/BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAuMRz
7FArWreh52Zh9/Ub98IM+PfZjSiI4lnzb2Dv4ig3TgthNCCVOg1Z0asL1p1SS8A4
ifWbmgONBLfsy1HxCJ9iu+xUO9N89ocAIFHq/dCeUoe2rNUjIzmbpvB9oyuIR7XR
DKbpfW5mz/Bf3a9EFpibNap2iEXGLuaH+Ej9qw3NqbCYqR5V8eRhHS6rZSXSHc+6
4qW4MCz5CFUD06G2AKHVr+siy4fTemfcr/s5rDEs9OHlf6yVJ4NEnDzAkpRAQpLF
GcfxkNnPjJ4gpBQF90OsAQWDezjyBGSRGCYpTv9XCUoMQIMdHn4FGYr6nDZwRhen
csKs3Yucoj5Ot3IskA==
-----END CERTIFICATE-----"""
    await terminal_session.session.run(
        UploadFile(
            "/usr/local/share/ca-certificates/my_custom_certificate.crt", mitproxy_cert.encode()
        )
    )
    await terminal_session.session.run(
        RawExec(["bash", "-c", "set -e\nset -x\nupdate-ca-certificates"])
    )
    await terminal_session.session.run(RawExec(["bash", "-c", "ls /etc/ssl/certs | grep my"]))


class CustomCaasContainer(CaasContainer):
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_random_exponential(multiplier=1, max=64),
        retry_error_callback=_retry_callback,
    )
    async def exec(
        self,
        cmd: list[str],
        workdir: str | None,
        timeout: int | None,
        env: dict[str, str] | None,
        user: str | None = None,
    ) -> tuple[int, bytes]:
        cmd = [
            "execute_with_truncation",
            "--swebench-hard-lib-timeout",
            "3000",
            "--swebench-hard-lib-bytes",
            "100000",
            *cmd,
        ]
        return await super().exec(cmd=cmd, workdir=workdir, timeout=timeout, env=env)


def make_clone_script(repo, repo_directory, base_commit):
    """
    Create a list of bash commands to set up the repository for testing.
    This is the setup script for the instance image.
    """
    setup_commands = [
        "#!/bin/bash",
        "set -euxo pipefail",
        f"git clone -o origin https://github.com/{repo} {repo_directory}",
        f"chmod -R 777 {repo_directory}",  # So nonroot user can run tests
        f"cd {repo_directory}",
        f"git reset --hard {base_commit}",
        # Remove the remote so the agent won't see newer commits.
        "git remote remove origin",
    ]
    return "\n".join(setup_commands)


async def upload_local_file(container, local_name, remote_name):
    with open(local_name, "r") as f:
        local_contents = f.read()
    remote_base_name = os.path.basename(remote_name)
    await container.terminal_session.session.run(
        UploadFile(f"/{remote_base_name}", local_contents.encode())
    )
    if f"/{remote_base_name}" != remote_name:
        await container.exec(
            ["mv", f"/{remote_base_name}", remote_name],
            timeout=TIMEOUT,
            workdir="/testbed",
            env=get_env(),
        )


async def get_fresh_container():
    try:
        container = await CaasContainer.new(
            caas_endpoint=CAAS_ENDPOINT,
            image_name=CAAS_IMAGE,
            idle_ttl=IDLE_TTL,
            memory_limit="32g",
            cpu_limit="16.0",
            env=get_env(),
            network=NetworkMode.CAAS_PUBLIC_ONLY,
        )
        await install_certs(container.terminal_session)
        with open(
            os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                "enhanced_caas_tool",
                "execute_with_truncation.py",
            )
        ) as f:
            execute_with_truncation = f.read().encode()
        await container.terminal_session.session.run(
            UploadFile("/usr/local/bin/execute_with_truncation", execute_with_truncation)
        )
        await container.exec(
            ["chmod", "+x", "/usr/local/bin/execute_with_truncation"],
            timeout=TIMEOUT,
            workdir="/",
            env=get_env(),
        )
        container = CustomCaasContainer(
            caas_endpoint=container.caas_endpoint,
            image_name=container.image_name,
            caas_session_state=container.caas_session_state,
        )
        return container
    except Exception as e:
        raise e


@retry(
    stop=stop_after_attempt(10),
    wait=wait_random_exponential(multiplier=1, max=64),
    retry_error_callback=_retry_callback,
)
async def get_fresh_container_with_repo(repo, repo_directory, base_commit):
    clone_script = make_clone_script(repo, repo_directory, base_commit)
    container = await get_fresh_container()

    # Unlike swebench verified, we have to clone the repo ourselves
    await container.terminal_session.session.run(UploadFile("/clone.sh", clone_script.encode()))
    clone_out = await container.exec(
        ["bash", "/clone.sh"], timeout=TIMEOUT, workdir="/", env=get_env()
    )
    assert clone_out[0] == 0, f"Clone script failed: {clone_out[1].decode()}"

    await upload_local_file(container, "./mask_tests.py", "/mask_tests.py")
    await container.exec(
        ["chmod", "+x", "/mask_tests.py"], timeout=TIMEOUT, workdir="/", env=get_env()
    )

    await container.exec(
        ["apt-get", "install", "tree"], timeout=TIMEOUT, workdir="/", env=get_env()
    )
    return container
