import argparse
import asyncio
import json
import os
import re
import shlex
import sys
import time
import traceback
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from functools import cache
from threading import Lock
from typing import Dict, Optional

os.environ["OPENAI_API_KEY"] = "NO_API_KEY"

import blobfile as bf
import datasets
from bus_token_completer import BusTokenCompleter
from caas.commands import DownloadFileFromContainer
from caas_tool.caas_container import C<PERSON>sContainer
from chat import Message, Role, chat
from chat.render.renderer_registry import get_renderer
from chat.tools import DefaultMultiToolKit, Tool, take_one_step_with_tools
from enhanced_caas_tool.enhanced_caas_container_tool import Enhanced<PERSON><PERSON>s<PERSON>ontainerTool
from legacy_rest_token_completer import LegacyRestTokenCompleter
from message_completer import TokenMessageCompleter
from message_completer.message_completer import ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter
from prbot_msft.caas_handle_utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from prbot_msft.swebench_hard.example import Example
from prbot_msft.swebench_hard.stream import SweDatum
from token_completer import CompleterBackend
from token_completer.backend import CompleterBackend
from utils import (
    ENGINE_URL,
    MIN_TESTS,
    RENDERER_NAME,
    TIMEOUT,
    get_fresh_container_with_repo,
    install_certs,
)

# RATIO_RANGE = [0.5, 1]
# CONTAINER_PATH = "orngcresco"
# BUS_TOPIC = "msft-swebenchhard"

RATIO_RANGE = [0, 0.5]
CONTAINER_PATH = "orngscuscresco"
BUS_TOPIC = "msft-swebenchhard-scus"


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_path", type=str, default=None)
    parser.add_argument("--is_repo", action="store_true")
    parser.add_argument(
        "--meta_path",
        type=str,
        default=f"az://{CONTAINER_PATH}/data/damajercak/gh-2024-09-04-2025-03-04-top2k/gh-2024-09-04-2025-03-04-top2k/copilot_ws",
    )
    parser.add_argument(
        "--progress_file",
        type=str,
        default=f"az://{CONTAINER_PATH}/data/damajercak/copilot_ws_2_onboarding_progress.jsonl",
    )
    parser.add_argument("--gpt_logs_output_dir", type=str, default="./gpt-logs")
    parser.add_argument("--intermediate_output_dir", type=str, default="./intermediate-output")
    parser.add_argument("--prev_output_path", type=str, default=None)
    parser.add_argument(
        "--output_path",
        type=str,
        default=f"az://{CONTAINER_PATH}/data/damajercak/gh-2024-09-04-2025-03-04-top2k-setups",
    )
    parser.add_argument("--omit_datetime", action="store_true")

    parser.add_argument("--juice", type=int, default=128)
    parser.add_argument("--max_episode_steps", type=int, default=100)
    parser.add_argument("--try_fix_test_patch", action="store_true")
    parser.add_argument("--parallel", type=int, default=50)
    parser.add_argument("--retries_sequential", type=int, default=1)
    parser.add_argument("--retries_parallel", type=int, default=3)
    return parser.parse_args()


@dataclass
class SweDatumSetup(SweDatum):
    top_language: str = ""

    tests_total: int = 0
    tests_passing: int = 0
    tests_results: Optional[Dict] = None

    tests_total_patched: int = 0
    tests_passing_patched: int = 0
    tests_results_patched: Optional[Dict] = None

    error_msg: str = None
    error_stacktrace: str = None

    def get(self, key, default=None):
        try:
            return self.__getattribute__(key)
        except:
            return default

    async def get_postmortem_prompt(self):
        if self.error_msg is None:
            return ""

        prompt = f"Last execution for this instance failed with following error: {self.error_msg}"

        if self.error_stacktrace:
            prompt += f"\nStacktrace:\n{self.error_stacktrace}"

        prompt += f"\nsetup.sh contents:\n{self.setup_script}"
        prompt += f"\nrun_tests.sh contents:\n{self.test_script}"

        return prompt

    def from_metadata(metadata: dict):
        instance = SweDatumSetup(
            instance_id=metadata["instance_id"],
            repo=metadata["repo"],
            base_commit=metadata["base_commit"],
            split_type="Full",
            problem_statement=metadata["problem_statement"],
            hints_text=metadata["hints_text"],
            version=metadata["version"],
            patch=metadata["patch"],
            test_patch=metadata["test_patch"],
            setup_time=-1,
            setup_script=metadata.get("setup_script"),
            test_script=metadata.get("test_script"),
            initial_gitdiff=metadata.get("initial_gitdiff"),
            language_count=metadata["language_count"],
            filenames=metadata["filenames"],
        )
        instance.top_language = metadata.get("top_language", "")
        return instance

    def update_from_metadata(self, metadata: dict):
        skipped_attr = []
        for k, v in metadata.items():
            if hasattr(self, k):
                self.__setattr__(k, v)
            else:
                skipped_attr.append(k)
        if len(skipped_attr) > 0:
            print(f"Skipped attributes: {skipped_attr}")
        return skipped_attr

    def update_from_example(self, example: Example):
        base_results = example._base_results
        gold_code_results = example._gold_code_results

        self.tests_results = base_results
        self.tests_total = len(base_results)
        self.tests_passing = sum(1 for k, v in base_results.items() if v.lower() == "passed")

        self.tests_results_patched = gold_code_results
        self.tests_total_patched = len(gold_code_results)
        self.tests_passing_patched = sum(
            1 for k, v in gold_code_results.items() if v.lower() == "passed"
        )


class DefaultMultiToolMatchKit(DefaultMultiToolKit):
    def _get_tool_by_name(self, name) -> tuple[Tool | None, chat.Message | None]:
        for tool in self.tools:
            if tool.name == name or name.startswith(tool.name + "."):
                return tool, None

        errm = chat.Message(
            author=chat.Author(role=chat.Role.SYSTEM),
            role=chat.Role.SYSTEM,
            content=chat.SystemError(
                name="InvalidRecipient",
                text=f'Recipient "{name}" is not recognized',
            ),
        )
        errm.metadata["failure_type"] = "bad_recipient"
        return None, errm


class CustomCaasContainer(CaasContainer):
    async def exec(
        self,
        cmd: list[str],
        workdir: str | None,
        timeout: int | None,
        env: dict[str, str] | None,
        user: str | None = None,
    ) -> tuple[int, bytes]:
        retries = 3
        cmd = ["timeout", "3000", "sh", "-c", " ".join(map(shlex.quote, cmd)) + " | tail -c 100000"]
        while retries > 0:
            try:
                return await super().exec(cmd=cmd, workdir=workdir, timeout=timeout, env=env)
            except Exception as e:
                retries -= 1
                print(f"Exec {cmd}, retries left: {retries}, failed with: {repr(e)}")
                if retries <= 0:
                    raise e


@cache
def get_message_completer():
    renderer = get_renderer(RENDERER_NAME)

    if ENGINE_URL.startswith("bus:"):
        topic_or_snapshot = ENGINE_URL[4:]
        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=BusTokenCompleter.Config(
                topic_mode_or_user=BUS_TOPIC,
                topic_or_snapshot=topic_or_snapshot,
                bus_line="bus",
            ),
            completion_params={"temperature": 1},
            renderer=renderer,
        )
        return message_completer_config.build()
    else:
        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=LegacyRestTokenCompleter.Config(
                api_base=ENGINE_URL,
                backend=CompleterBackend.TEXT_BACKEND,  # use TEXT_BACKEND for research (non-api.openai.com) engines
            ),
            completion_params={"temperature": 1},
            renderer=renderer,
        )
        return message_completer_config.build()


async def prompt_llm(messages: list, juice: int = 128):
    convo = chat.Conversation(
        messages=[
            Message.system(
                model_identity_desc="You are ChatGPT, a large language model trained by OpenAI, based on the GPT-4 architecture.",
                tools_section={"no op": "does nothing"},
                channel_config=chat.SystemChannelConfig(
                    valid_channels=("analysis", "final"), channel_required=True
                ),
                metadata=chat.SystemContentMetadata(reward_multiplier=juice),
            ),
            *messages,
        ]
    )

    completion = await get_message_completer().async_completion(
        conversations=[convo], n=1, seed=0, end_header=True
    )

    choice = completion.choices[0]
    messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
    convo = convo.with_suffix(*messages)
    return "\n".join([part for part in convo.messages[-1].content.parts])


async def summarize_text(output: str):
    user_prompt = f"""\
Instruction:
Please read the following script execution output and provide a concise summary of what happened. Pay special attention to any errors, warnings, or anomalies.

Clearly list any errors and describe their potential causes or impact.
If there are no errors, indicate that the script ran successfully.
Keep your response brief and focused.
Script Output:
{output}"""

    return prompt_llm([chat.Message.user(user_prompt)])


async def summarize_convo(convo):
    messages_copy = convo.messages.copy()
    user_prompt = f"""\
Please read previous conversation containing chain of thought of an agent with responses from the system.
Provide a concise summary of what happened. Pay special attention to any errors, warnings, or anomalies.
Put more focus on later messages since they better reflect current state of the system. Make sure that the output is in natural language.
Make sure to not to generate any code or tool calls at all."""
    convo = chat.Conversation(
        messages=[
            Message.system(
                model_identity_desc="You are ChatGPT, a large language model trained by OpenAI, based on the GPT-4 architecture.",
                tools_section={},
                channel_config=chat.SystemChannelConfig(
                    valid_channels=("analysis", "final"), channel_required=True
                ),
                metadata=chat.SystemContentMetadata(reward_multiplier=512),
            ),
            *messages_copy[2:],
            chat.Message.user(user_prompt),
        ]
    )

    summary = prompt_llm(
        [
            *messages_copy[2:],
            chat.Message.user(user_prompt),
        ]
    )

    return chat.Conversation(
        messages=[
            *messages_copy[:2],
            chat.Message.user(summary),
            messages_copy[-1],
        ]
    )


async def summarize_project_documentation(container, instance: SweDatumSetup) -> Dict[str, str]:
    filenames = json.loads(instance.filenames)
    doc_files = ["README.md", "INSTALL.md", "CONTRIBUTING.md"]
    documentation_files: Dict[str, str] = {}
    documentation_hints: Dict[str, str] = {}
    for file_name in doc_files:
        relevant_paths = [
            name for name in filenames if name and re.search(file_name, name, re.IGNORECASE)
        ]
        for file_path in relevant_paths:
            try:
                file_contents = await container.terminal_session.session.run(
                    DownloadFileFromContainer(f"/testbed/{file_path}")
                )
                documentation_files[file_path] = file_contents.decode()
            except Exception as e:
                print(f"Could not find file: {file_path}, exception: {repr(e)}")
                pass

    for file_path, file_contents in documentation_files.items():
        try:
            await container.send_heartbeat()
            user_prompt = (
                "You are an assistant that helps analyze markdown files of a project.\n"
                f"Here is the content of the {file_path} file. Extract relevant details "
                "about language, version, and anything else important for "
                f"setting up this repository.\n\nContents:\n{file_contents}"
            )
            doc_summary = await prompt_llm(messages=[chat.Message.user(user_prompt)])
            documentation_hints[file_name] = doc_summary
        except Exception as e:
            print(f"Could not summarize file: {file_path}, exception: {repr(e)}")
            pass
    return documentation_hints


async def summarize_project_cicd(container, instance: SweDatumSetup) -> Dict[str, str]:
    filenames = json.loads(instance.filenames)
    ci_cd_path_regexes = [
        "\.github\/workflows\/.*s\.y.?ml",
        "ci\/.*s\.y.?ml" "cicd\/.*s\.y.?ml" "\.gitlab-ci\/.*s\.y.?ml",
    ]
    ci_cd_scripts: Dict[str, str] = {}
    ci_cd_hints: Dict[str, str] = {}
    for ci_cd_path_regex in ci_cd_path_regexes:
        relevant_paths = [name for name in filenames if name and re.search(ci_cd_path_regex, name)]
        for file_path in relevant_paths:
            try:
                file_contents = await container.terminal_session.session.run(
                    DownloadFileFromContainer(f"/testbed/{file_path}")
                )
                ci_cd_scripts[file_path] = file_contents.decode()
            except Exception as e:
                print(f"Could not find file: {file_path}, exception: {repr(e)}")
                pass

    for script_path, script_content in ci_cd_scripts.items():
        try:
            await container.send_heartbeat()
            user_prompt = (
                f"Here is a content of the {script_path}, CI/CD script. Extract the following information:\n"
                "- Dependencies: List of tools or libraries required.\n"
                "- Build Commands: Steps for building the project.\n"
                "- Test Commands: Steps for running tests.\n"
                f"- Other Notes: Any additional setup or execution details.\n\nContents:\n{script_content}"
            )
            script_hint = await prompt_llm(messages=[chat.Message.user(user_prompt)])
            ci_cd_hints[script_path] = script_hint
        except Exception as e:
            print(f"Could not summarize file: {file_path}, exception: {repr(e)}")
            pass
    return ci_cd_hints


def write_convo_to_file(convo: chat.Conversation, file_name: str, folder: str):
    os.makedirs(folder, exist_ok=True)
    with open(os.path.join(folder, file_name), "w") as f:
        print(convo, file=f)


def validate_instance(instance, is_repo=False):
    if (
        instance.get("tests_total", 0) >= MIN_TESTS
        and instance.get("tests_passing", 0) >= MIN_TESTS
        and instance.get("tests_results", {})
        and len(instance.get("tests_results", {})) >= MIN_TESTS
    ):
        if is_repo:
            return True
        if (
            instance.get("tests_total_patched", 0) >= MIN_TESTS
            and instance.get("tests_passing_patched", 0) >= MIN_TESTS
            and instance.get("tests_results_patched", {})
            and len(instance.get("tests_results_patched", {})) >= MIN_TESTS
        ):
            if instance.get("tests_total_patched", 0) != instance.get(
                "tests_total", 0
            ) or instance.get("tests_passing_patched", 0) != instance.get("tests_passing", 0):
                return True
    return False


def write_instance(output_dir, instance: SweDatumSetup):
    with bf.BlobFile(f"{output_dir}/{instance.instance_id}/setup.sh", "w") as f:
        f.write(instance.setup_script)
    with bf.BlobFile(f"{output_dir}/{instance.instance_id}/run_tests.sh", "w") as f:
        f.write(instance.test_script)
    with bf.BlobFile(f"{output_dir}/{instance.instance_id}/gitdiff.patch", "w") as f:
        f.write(instance.initial_gitdiff)
    with bf.BlobFile(f"{output_dir}/{instance.instance_id}/metadata.json", "w") as f:
        f.write(json.dumps(instance.as_dict(), indent=2))


async def process_instance(
    repo,
    instance: SweDatumSetup,
    past_instances=None,
    juice=1024,
    max_episode_steps=50,
    retries=3,
    meta_path=None,
    gpt_logs_output_dir=None,
    intermediate_output_dir=None,
    prev_output_dir=None,
    output_dir=None,
    try_fix_test_patch=False,
) -> bool:
    assert repo is not None, "Repo cannot be None"

    is_repo = instance is None
    if instance is None:
        instance = SweDatumSetup.from_metadata(
            dict(
                repo=repo,
                instance_id=repo.replace("/", "__"),
                base_commit="",
                patch="",
                test_patch="",
                problem_statement="",
                hints_text="",
                version="",
                split_type="copilot_ws",
                setup_time=-1,
            )
        )
    if type(instance) == str:
        instance_id = instance
    else:
        instance_id = instance.instance_id
    if bf.exists(os.path.join(meta_path, instance_id, "metadata.json")):
        with bf.BlobFile(os.path.join(meta_path, instance_id, "metadata.json"), "r") as f:
            metadata = json.loads(f.read())
            instance = SweDatumSetup.from_metadata(metadata)

    # We want only PR instances with test changes
    if not is_repo and not instance.test_patch and not try_fix_test_patch:
        raise ValueError("No test patch found, skipping instance.")

    try:
        instance.top_language = sorted(
            json.loads(instance.language_count).items(),
            key=lambda x: float(x[1]["percentage"]),
            reverse=True,
        )[0][0]
    except:
        raise ValueError(
            f"Could not determine top language for instance {instance.instance_id} with languages {instance.language_count}."
        )

    if instance.top_language not in ["Rust", "C#", "C", "C++"]:
        raise ValueError(
            f"Top language {instance.top_language} for instance {instance.instance_id} not in Rust, C#, C, and C++."
        )

    success, loaded = False, False
    past_instances = [instance_id] if past_instances is None else [instance_id, *past_instances]
    for past_instance in past_instances:
        for base_path in [prev_output_dir]:
            if (
                bf.exists(f"{base_path}/{past_instance}/metadata.json")
                and bf.exists(f"{base_path}/{past_instance}/setup.sh")
                and bf.exists(f"{base_path}/{past_instance}/run_tests.sh")
                and bf.exists(f"{base_path}/{past_instance}/gitdiff.patch")
            ):
                with bf.BlobFile(f"{base_path}/{past_instance}/setup.sh", "r") as f:
                    setup_script = f.read()
                with bf.BlobFile(f"{base_path}/{past_instance}/run_tests.sh", "r") as f:
                    test_script = f.read()
                with bf.BlobFile(f"{base_path}/{past_instance}/gitdiff.patch", "r") as f:
                    gitdiff_patch = f.read()
                with bf.BlobFile(f"{base_path}/{past_instance}/metadata.json", "r") as f:
                    metadata = json.loads(f.read())
                    instance.update_from_metadata(metadata)

                if len(setup_script.splitlines()) > 1:
                    lines = setup_script.splitlines()
                    if lines[1].startswith("set") and lines[1].endswith("pipefail"):
                        setup_script = "\n".join(lines[:1] + lines[2:])
                if len(test_script.splitlines()) > 1:
                    lines = test_script.splitlines()
                    if lines[1].startswith("set") and lines[1].endswith("pipefail"):
                        test_script = "\n".join(lines[:1] + lines[2:])

                instance.setup_script = setup_script
                instance.test_script = test_script
                instance.initial_gitdiff = gitdiff_patch

                loaded = True
                try:
                    async with await CaasHandle.create() as caas_handle:
                        await install_certs(caas_handle._caas_handle.terminal_session)
                        example: Example = await Example.create(
                            swe_datum=instance, container_handle=caas_handle, timeout=TIMEOUT
                        )
                        instance.update_from_example(example)
                        success = validate_instance(instance)
                except Exception as e:
                    instance.error_msg = traceback.format_exc()
                break
        if loaded:
            break

    if success:
        write_instance(output_dir=output_dir, instance=instance)
        # We loaded instance in current output folder
        return instance

    caas_err_retry = 1
    while retries > 0 and not success:
        try:
            instance = await execute_instace(
                instance,
                juice=juice,
                max_episode_steps=max_episode_steps,
                gpt_logs_output_dir=gpt_logs_output_dir,
                intermediate_output_dir=intermediate_output_dir,
                is_repo=is_repo,
            )
            if not instance.error_msg:
                success = True
                break
        except Exception as e:
            print(
                f"Failed executing instance: {instance_id}, retries left: {retries - 1}, failed with error {e}, retry ..."
            )
            instance.error_msg = traceback.format_exc()
            if (
                "Container is no longer alive" in instance.error_msg
                or "ReadError('')" in instance.error_msg
            ):
                retries += caas_err_retry
                caas_err_retry -= 1
        finally:
            retries -= 1

    if output_dir is not None and validate_instance(instance) and not instance.error_msg:
        write_instance(output_dir=output_dir, instance=instance)

    return instance


async def execute_instace(
    instance: SweDatumSetup,
    juice=1024,
    max_episode_steps=50,
    gpt_logs_output_dir=None,
    intermediate_output_dir=None,
    is_repo=False,
):
    instance_id = instance.instance_id

    current_time = datetime.now().strftime("%Y%m%d-%H-%M-%S")
    gpt_logs_output_dir = os.path.join(gpt_logs_output_dir, instance_id + f"-{current_time}")
    intermediate_output_dir = os.path.join(
        intermediate_output_dir, instance_id + f"-{current_time}"
    )

    container = None
    try:
        container = await get_fresh_container_with_repo(
            instance.repo, "/testbed", instance.base_commit
        )
        cicd_summary = await summarize_project_cicd(container, instance)
        doc_summary = await summarize_project_documentation(container, instance)
        await container.send_heartbeat()
        if instance and not instance.test_patch:
            _, output = await container.exec(
                ["python", "mask_tests.py", "--repo_path", "/testbed"],
                timeout=TIMEOUT,
                workdir="/",
                env={},
            )
            output = output.strip().decode()
            if not output:
                return instance
            else:
                instance.test_patch = output

        # Get CaaS supported tool_kit
        toolkit = DefaultMultiToolMatchKit(
            tools=[
                EnhancedCaasContainerTool(
                    container=container,
                    terminal_emulator_path="teammate_service.termites.lean_terminal:LeanTerminal",
                )
            ]
        )

        tools_section = {tool.name: tool.instruction() for tool in toolkit.tools}

        message_completer = get_message_completer()
        # renderer = message_completer.get_renderer()
        user_prompt = f"""\
You are an AI assistant specialized in preparing a given project for successful installation and test execution by creating and configuring all required components for project in /testbed directory (specified as workdir for example commands). Your tasks are as follows:

1. **Identify Requirements**: Gather detailed information about the project’s environment needs (e.g., language version, dependencies, frameworks).

2. **Provide Two Complete Bash Scripts**:  
- **setup.sh**: Must install all necessary dependencies and set up the environment so the project can run on a fresh system or container.  
- **run_tests.sh**: Must execute the project’s test suite and produce JUnit XML reports in a `junit_xml_reports` directory at the project root.  

3. **Ensure Scripts Cover Everything**: Both scripts must contain all commands needed to accomplish their respective goals. Do not consider the setup complete until these scripts exist and can run without error to install dependencies and successfully generate test results.

4. **Refine as Needed**: If any issues arise—such as missing dependencies or misconfigurations—update the scripts accordingly to ensure a smooth, reproducible setup.

5. **Analyze Test Outcomes**: After running the test suite, verify that JUnit XML reports are generated in the specified directory. Check for failing tests that might indicate incorrect setup or missing dependencies, and propose fixes if required.

6. **Provide contents of two script in json format**: After verifying that JUnit XML reports are generated make sure to provide the contents of the two shell scripts (setup.sh and run_tests.sh) in json format, where the key is the file name and the value is the content of the file.
"""

        if instance.top_language:
            lang_specific_prompt_path = os.path.join(
                os.path.abspath(os.path.dirname(__file__)),
                "language_specific_prompts",
                f"{instance.top_language.lower()}_guidelines.md",
            )
            if os.path.exists(lang_specific_prompt_path):
                with open(lang_specific_prompt_path, "r") as f:
                    user_prompt += "\n**Here are some language specific hints***\n"
                    user_prompt += f.read()

        if len(cicd_summary) > 0:
            user_prompt += "\n**Here is summary of important CI/CD pipelines***\n"
            for pth, content in cicd_summary.items():
                user_prompt += f"""
**Path**:\n{pth}
**Content**:\n{content}
"""
        if len(doc_summary) > 0:
            user_prompt += "**Here is summary of important markdown files***\n"
            for pth, content in doc_summary.items():
                user_prompt += f"""
**Path**:\n{pth}
**Content**:\n{content}
"""

        os.makedirs(gpt_logs_output_dir, exist_ok=True)
        post_mortem_prompt = await instance.get_postmortem_prompt()
        instance.error_msg = ""  # Delete error message before proceeding
        if post_mortem_prompt is not None and len(post_mortem_prompt) > 0:
            user_prompt += f"\n{post_mortem_prompt}"

        user_prompt += """
**Your task is not done until**:
- Setup is correctly done for a given repository, trying to leverage its pipelines if available
- Both scripts exist in the /testbed directory (/testbed/setup.sh and /testbed/run_tests.sh)
- Each script contains all the necessary commands to reliably set up the project and run its tests on a clean environment.
- JUnit XML reports are correctly generated.

Remember, completeness and reproducibility are the core objectives. Do not return result before running and verifying the outcome and majority of tests are verified to be passing.
"""

        convo = chat.Conversation(
            messages=[
                Message.system(
                    model_identity_desc="You are ChatGPT, a large language model trained by OpenAI, based on the GPT-4 architecture.",
                    tools_section=tools_section,
                    channel_config=chat.SystemChannelConfig(
                        valid_channels=("analysis", "final"), channel_required=True
                    ),
                    metadata=chat.SystemContentMetadata(reward_multiplier=juice),
                ),
                chat.Message.user(user_prompt),
            ]
        )
        write_convo_to_file(convo, f"episode_000.jsonl", folder=gpt_logs_output_dir)

        valid_nontool_recipients = {
            m.author.display_name() for m in convo.messages if m.author.role != Role.TOOL
        }
        valid_recipients = (
            valid_nontool_recipients
            | (set([tool.name for tool in toolkit.tools]) if toolkit else set())
            | {"all"}
        )

        for elapsed_step in range(max_episode_steps):
            is_last_step = elapsed_step == max_episode_steps - 1

            completion = await message_completer.async_completion(
                conversations=[convo], n=1, seed=0, end_header=is_last_step
            )

            choice = completion.choices[0]
            messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
            convo = convo.with_suffix(*messages)

            if (
                is_last_step
                or messages[-1].end_turn
                or (
                    messages[-1].recipient not in valid_recipients
                    and messages[-1].recipient.split(".")[0] not in valid_recipients
                )
            ):
                write_convo_to_file(
                    convo, f"episode_{elapsed_step+1:03}.jsonl", folder=gpt_logs_output_dir
                )
                break

            if messages[-1].recipient.startswith("container"):
                try:
                    messages[-1].content.text = json.dumps(
                        {
                            **json.loads(messages[-1].content.text),
                            "timeout": TIMEOUT,
                            "env": {},
                        }
                    )
                except Exception as e:
                    print(e)

            if (
                messages[-1].recipient.startswith("container")
                and "timeout" not in messages[-1].content.text
            ):
                messages[-1].content.text = (
                    messages[-1].content.text[:-1] + f', "timeout": {TIMEOUT}' + "}"
                )

            # call tool once
            async for tool_message in take_one_step_with_tools(
                prefix_convo=convo.prefix(-1),
                message=messages[-1],
                toolkit=toolkit,
            ):
                if convo.messages[-1].id == tool_message.id:
                    convo.messages[-1] = tool_message
                else:
                    convo = convo.with_suffix(tool_message)
            write_convo_to_file(
                convo, f"episode_{elapsed_step+1:03}.jsonl", folder=gpt_logs_output_dir
            )
        print(f"{instance_id}: sampling for convo completed, total episode: {elapsed_step}")

        try:
            file_contents = json.loads(messages[-1].content.parts[0])
            rsetup = file_contents["setup.sh"].encode()
            rtest = file_contents["run_tests.sh"].encode()
            await container.exec(
                ["touch", "/tmp/gitdiff.patch", "&&", "git", "diff", ">", "/tmp/gitdiff.patch"],
                timeout=TIMEOUT,
                workdir="/testbed",
                env={},
            )
            gitdiff_patch = await container.terminal_session.session.run(
                DownloadFileFromContainer("/tmp/gitdiff.patch")
            )
        except Exception as e:
            print(f"Failed to get contents of files from last message, Error: {e}")
            try:
                rsetup = await container.terminal_session.session.run(
                    DownloadFileFromContainer("/testbed/setup.sh")
                )
                rtest = await container.terminal_session.session.run(
                    DownloadFileFromContainer("/testbed/run_tests.sh")
                )
                await container.exec(
                    ["touch", "/tmp/gitdiff.patch", "&&", "git", "diff", ">", "/tmp/gitdiff.patch"],
                    timeout=TIMEOUT,
                    workdir="/testbed",
                    env={},
                )
                gitdiff_patch = await container.terminal_session.session.run(
                    DownloadFileFromContainer("/tmp/gitdiff.patch")
                )
            except Exception as e:
                print(f"Failed to get contents of files from container, Error: {e}")
                instance.error_msg = repr(
                    ValueError(
                        f"setup and run_tests scripts were not created for instance. Traceback:\n{traceback.format_exc()}"
                    )
                )
                return instance
    finally:
        if container:
            await container.teardown()
    instance.setup_script = rsetup.decode("utf-8").strip()
    instance.test_script = rtest.decode("utf-8").strip()
    instance.initial_gitdiff = gitdiff_patch.decode("utf-8")

    async with await CaasHandle.create() as caas_handle:
        await install_certs(caas_handle._caas_handle.terminal_session)
        example: Example = await Example.create(
            swe_datum=instance, container_handle=caas_handle, timeout=TIMEOUT
        )
        instance.update_from_example(example)

    if instance.tests_passing == 0 or instance.tests_passing_patched == 0:
        instance.error_msg = repr(ValueError(f"No tests passed are passing"))

    os.makedirs(intermediate_output_dir, exist_ok=True)
    for loc, data in zip(
        [
            "setup.sh",
            "run_tests.sh",
            "gitdiff.patch",
            "structured_test_results.json",
            "structured_patched_test_results.json",
        ],
        [
            instance.setup_script,
            instance.test_script,
            instance.initial_gitdiff,
            json.dumps(instance.tests_results, indent=2),
            json.dumps(instance.tests_results_patched, indent=2),
        ],
    ):
        with open(os.path.join(intermediate_output_dir, loc), "w") as f:
            f.write("" if data is None else data)

    if (
        not is_repo
        and instance.tests_total == instance.tests_total_patched
        and instance.tests_passing == instance.tests_passing_patched
        and instance.tests_passing > 0
        and instance.tests_total > 0
    ):
        instance.error_msg = repr(
            ValueError(
                "Ensure that the setup is correct for this particular repository. The number of tests did not change after applying the test patch from pull request."
            )
        )
        return instance
    elif is_repo or (instance.tests_total < MIN_TESTS or instance.tests_passing < MIN_TESTS):
        instance.error_msg = repr(
            ValueError(
                "Ensure that the setup is correct for this particular repository. Too few test are passing or have been found."
            )
        )
        return instance

    return instance


async def process_all(
    dataset,
    output_dir,
    prev_output_dir=None,
    juice=1024,
    max_episode_steps=100,
    concurrency_limit=10,
    is_repo=False,
    gpt_logs_output_dir=None,
    intermediate_output_dir=None,
    meta_path=None,
    progress_file=None,
    ratio_range=[0, 1],
    retries_parallel=3,
    retries_sequential=1,
    try_fix_test_patch=False,
):
    semaphore = asyncio.Semaphore(concurrency_limit)
    mutex = Lock()

    results = []
    repo_finished = []
    metrics_success = defaultdict(list)
    metric_reuse = defaultdict(list)
    metrics_failure = defaultdict(list)

    with open(os.path.join(intermediate_output_dir, "progress.txt"), "w") as f:
        f.write("")

    async def map_with_concurrency_limit(func, generator, max_concurrent_tasks):
        queue = asyncio.Queue()
        output = {}

        async def worker():
            while True:
                idx, kwargs = await queue.get()
                try:
                    output[idx] = await func(**kwargs)
                except Exception as e:
                    output[idx] = None
                    print(f"Exception in task: {e}")
                finally:
                    queue.task_done()

        workers = [asyncio.create_task(worker()) for _ in range(max_concurrent_tasks)]

        for idx, kwargs in enumerate(generator):
            await queue.put((idx, kwargs))

        await queue.join()

        for w in workers:
            w.cancel()

        cancelled_errors = await asyncio.gather(*workers, return_exceptions=True)
        final_outputs = []
        for i in sorted(output):
            final_outputs.extend(output[i])
        return final_outputs

    async def process_with_semaphore(repo, instance_list, prev_instances=None):
        async with semaphore:
            if prev_instances is None:
                prev_instances = []
            process_results = []
            for instance in instance_list:
                start_time = time.perf_counter()
                error_msg = None
                try:
                    result: SweDatumSetup = await process_instance(
                        repo,
                        instance,
                        past_instances=prev_instances,
                        juice=juice,
                        max_episode_steps=max_episode_steps,
                        meta_path=meta_path,
                        gpt_logs_output_dir=gpt_logs_output_dir,
                        intermediate_output_dir=intermediate_output_dir,
                        prev_output_dir=prev_output_dir,
                        output_dir=output_dir,
                        retries=retries_sequential,
                        try_fix_test_patch=try_fix_test_patch,
                    )
                    error_msg = result.error_msg
                    with mutex:
                        if validate_instance(result) and not result.error_msg:
                            metrics_success[repo].append(instance)
                        else:
                            metrics_failure[repo].append(instance)
                    process_results.append(result.tests_passing > 0)
                    prev_instances = [instance, *prev_instances]
                except Exception as e:
                    error_msg = traceback.format_exc()
                    with mutex:
                        metrics_failure[repo].append(instance)
                    process_results.append(False)
                finally:
                    end_time = time.perf_counter()
                    with mutex:
                        success_prs = sum([len(prs) for prs in metrics_success.values()])
                        reuse_prs = sum([len(prs) for prs in metric_reuse.values()])
                        failure_prs = sum([len(prs) for prs in metrics_failure.values()])
                        print(
                            f"Repos: {len(metrics_success)}/{len(set(metrics_success).union(metrics_failure))}, PRs: {success_prs}({reuse_prs} reused)/{success_prs + failure_prs}, instance: {instance}, repo: {repo}, elapsed time: {end_time - start_time} (seconds)"
                        )
                        with open(os.path.join(intermediate_output_dir, "progress.txt"), "a+") as f:
                            f.write(
                                f"Repos: {len(metrics_success)}/{len(set(metrics_success).union(metrics_failure))}({len(repo_finished)}), PRs: {success_prs}({reuse_prs} reused)/{success_prs + failure_prs}, instance: {instance}, repo: {repo}, elapsed time: {end_time - start_time} (seconds), active tasks: {concurrency_limit - semaphore._value}\n"
                            )
                        with bf.BlobFile(progress_file, "a") as f:
                            f.write(json.dumps(dict(instance=instance, error_msg=error_msg)) + "\n")
        with mutex:
            repo_finished.append(repo)
        return process_results

    dataset_dict = defaultdict(list)

    for instance in dataset:
        if is_repo:
            dataset_dict[instance] = [None]
        else:
            dataset_dict["-".join(instance.split("-")[:-1])].append(instance)
    if not is_repo:
        for repo in dataset_dict:
            dataset_dict[repo].sort(key=lambda instance: int(instance.split("-")[-1]), reverse=True)

    if ratio_range[0] > 0 or ratio_range[1] < 1:
        dict_keys = sorted(list(dataset_dict.keys()), key=str.casefold)
        start_index, end_index = int(ratio_range[0] * len(dict_keys)), int(
            ratio_range[1] * len(dict_keys)
        )
        dataset_dict = {k: dataset_dict[k] for k in dict_keys[start_index:end_index]}

    existing_instances = set()
    if bf.exists(progress_file):
        with bf.BlobFile(progress_file, "r") as f:
            existing_instances = set(
                [json.loads(line).get("instance", "") for line in f.readlines() if line.strip()]
            )

    # Filter out instances that have already been processed
    for repo in list(dataset_dict.keys()):
        dataset_dict[repo] = [
            instance for instance in dataset_dict[repo] if instance not in existing_instances
        ]
        if not dataset_dict[repo]:
            del dataset_dict[repo]

    ordered_instances = []
    instance_appended = True
    curr_instance_index = 0
    while instance_appended:
        instance_appended = False
        for repo in dataset_dict:
            if len(dataset_dict[repo]) > curr_instance_index:
                ordered_instances.append(
                    (dict(repo=repo, instance_list=[dataset_dict[repo][curr_instance_index]]))
                )
                instance_appended = True
        curr_instance_index += 1
    with open(os.path.join(intermediate_output_dir, "instances.txt"), "w+") as f:
        f.write(json.dumps(ordered_instances))

    ordered_instances_full = []
    for _ in range(retries_parallel):
        ordered_instances_full += ordered_instances
    results.extend(
        await map_with_concurrency_limit(
            process_with_semaphore, ordered_instances_full, concurrency_limit
        )
    )
    return results


if __name__ == "__main__":
    args = parse_args()
    dataset = [f for f in bf.listdir(args.meta_path)]

    if not args.omit_datetime:
        current_time = datetime.now().strftime("%Y%m%d-%H-%M-%S")
        args.gpt_logs_output_dir = os.path.join(args.gpt_logs_output_dir, current_time)
        args.intermediate_output_dir = os.path.join(args.intermediate_output_dir, current_time)

    os.makedirs(args.gpt_logs_output_dir, exist_ok=True)
    os.makedirs(args.intermediate_output_dir, exist_ok=True)

    async_process_all = process_all(
        dataset,
        try_fix_test_patch=args.try_fix_test_patch,
        is_repo=args.is_repo,
        juice=args.juice,
        max_episode_steps=args.max_episode_steps,
        concurrency_limit=args.parallel,
        meta_path=args.meta_path,
        progress_file=args.progress_file,
        gpt_logs_output_dir=args.gpt_logs_output_dir,
        intermediate_output_dir=args.intermediate_output_dir,
        prev_output_dir=args.prev_output_path,
        output_dir=args.output_path,
        ratio_range=RATIO_RANGE,
        retries_sequential=args.retries_sequential,
        retries_parallel=args.retries_parallel,
    )
    passes = asyncio.run(async_process_all)

    print("*" * 100)
    print(f"Evaluation Results: ")
    print(passes)
    print(f"mean = {sum(passes)/len(passes)} | len = {len(passes)}")
