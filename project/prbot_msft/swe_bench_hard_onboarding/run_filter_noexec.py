"""
This is very simple utility that checks if every repo is still cloneable (may not be relevant since we are mounting the volumes now).
"""
import argparse
import asyncio
import json
import os
import shlex
import sys
import time
from collections import defaultdict
from dataclasses import dataclass
from threading import Lock

from prbot_msft.caas_handle_utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from prbot_msft.swebench_hard.example import Example
from prbot_msft.swebench_hard.stream import SweDatum
from tqdm import tqdm
from utils import CAAS_IMAGE, TIMEOUT


def get_env():
    return {"CAAS_CLIENT_ID": f"prbot-onboarding"}


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--data_path",
        type=str,
        default="/root/copilot_ws_2_results.jsonl",
    )
    parser.add_argument("--is_repo", action="store_true")
    parser.add_argument(
        "--meta_path",
        type=str,
        default="/root/copilot_ws_2",
    )
    parser.add_argument(
        "--output_path_processed",
        type=str,
        default="/root/copilot_ws_2_results_filtered.jsonl",
    )
    parser.add_argument(
        "--output_path_success",
        type=str,
        default="/root/copilot_ws_2_results_filtered_success.jsonl",
    )
    parser.add_argument(
        "--output_path_failure",
        type=str,
        default="/root/copilot_ws_2_results_filtered_failure.jsonl",
    )

    parser.add_argument("--parallel", type=int, default=100)
    parser.add_argument("--retries_parallel", type=int, default=2)
    return parser.parse_args()


@dataclass
class SweDatumSetup(SweDatum):
    top_language: str = ""
    tests_total: int = 0
    tests_passing: int = 0
    tests_total_patched: int = 0
    tests_passing_patched: int = 0

    def get(self, key, default=None):
        try:
            return self.__getattribute__(key)
        except:
            return default

    def from_metadata(metadata: dict):
        instance = SweDatumSetup(
            instance_id=metadata["instance_id"],
            repo=metadata["repo"],
            base_commit=metadata["base_commit"],
            split_type=metadata.get("split_type", "Full"),
            problem_statement=metadata["problem_statement"],
            hints_text=metadata["hints_text"],
            version=metadata["version"],
            patch=metadata["patch"],
            test_patch=metadata["test_patch"],
            setup_time=-1,
            setup_script=metadata.get("setup_script"),
            test_script=metadata.get("test_script"),
            initial_gitdiff=metadata.get("initial_gitdiff"),
            filenames=metadata.get("filenames"),
            language_count=metadata.get("language_count"),
        )
        instance.top_language = metadata.get("top_language", "")
        return instance

    def update_from_metadata(self, metadata: dict):
        skipped_attr = []
        for k, v in metadata.items():
            if hasattr(self, k):
                self.__setattr__(k, v)
            else:
                skipped_attr.append(k)
        if len(skipped_attr) > 0:
            print(f"Skipped attributes: {skipped_attr}")
        return skipped_attr

    def update_from_example(self, example: Example):
        base_results = example._base_results
        gold_code_results = example._gold_code_results

        self.tests_results = base_results
        self.tests_total = len(base_results)
        self.tests_passing = sum(1 for k, v in base_results.items() if v.lower() == "passed")

        self.tests_results_patched = gold_code_results
        self.tests_total_patched = len(gold_code_results)
        self.tests_passing_patched = sum(
            1 for k, v in gold_code_results.items() if v.lower() == "passed"
        )

        self.fail_to_pass = example.fail_to_pass
        self.pass_to_pass = example.pass_to_pass
        self.other_to_other = example.other_to_other


async def process_instance(
    repo,
    instance: SweDatumSetup,
    meta_path=None,
) -> bool:
    assert repo is not None, "Repo cannot be None"

    if instance is None:
        instance = SweDatumSetup.from_metadata(
            dict(
                repo=repo,
                instance_id=repo.replace("/", "__"),
                base_commit="",
                patch="",
                test_patch="",
                problem_statement="",
                hints_text="",
                version="",
                split_type="copilot_ws",
                setup_time=-1,
            )
        )
    if type(instance) == str:
        instance_id = instance
    else:
        instance_id = instance.instance_id
    if os.path.exists(os.path.join(meta_path, instance_id, "metadata.json")):
        with open(os.path.join(meta_path, instance_id, "metadata.json"), "r") as f:
            metadata = json.loads(f.read())
            instance = SweDatumSetup.from_metadata(metadata)

    async with await CaasHandle.create(image_name=CAAS_IMAGE) as caas_handle:
        example: Example = await Example.create(
            swe_datum=instance, container_handle=caas_handle, timeout=TIMEOUT
        )
    return instance


async def process_all(
    dataset,
    output_path_processed=None,
    concurrency_limit=10,
    is_repo=False,
    meta_path=None,
    ratio_range=[0, 1],
    retries_parallel=3,
):
    semaphore = asyncio.Semaphore(concurrency_limit)
    mutex = Lock()

    results = []
    metrics_success = defaultdict(set)
    metrics_failure = defaultdict(set)

    async def map_with_concurrency_limit(func, generator, max_concurrent_tasks):
        with tqdm(total=len(generator)) as pbar:
            queue = asyncio.Queue()
            output = {}

            async def worker():
                while True:
                    idx, kwargs = await queue.get()
                    try:
                        output[idx] = await func(**kwargs)
                    except Exception as e:
                        output[idx] = None
                        print(f"Exception in task: {e}")
                    finally:
                        queue.task_done()
                        pbar.update(1)

            workers = [asyncio.create_task(worker()) for _ in range(max_concurrent_tasks)]

            for idx, kwargs in enumerate(generator):
                await queue.put((idx, kwargs))

            await queue.join()

            for w in workers:
                w.cancel()
            cancelled_errors = await asyncio.gather(*workers, return_exceptions=True)
            final_outputs = []
            for i in sorted(output):
                final_outputs.extend(output[i])
            return final_outputs

    async def process_with_semaphore(repo, instance_list, prev_instances=None):
        async with semaphore:
            if prev_instances is None:
                prev_instances = []
            process_results = []
            for instance in instance_list:
                error = None
                start_time = time.perf_counter()
                try:
                    await process_instance(repo, instance, meta_path=meta_path)
                    with mutex:
                        metrics_success[repo].add(instance)
                    process_results.append(True)
                except Exception as e:
                    with mutex:
                        metrics_failure[repo].add(instance)
                    process_results.append(False)
                    error = repr(e)
                finally:
                    end_time = time.perf_counter()
                    with mutex:
                        if output_path_processed is not None:
                            with open(output_path_processed, "a+") as f:
                                if error is not None:
                                    f.write(
                                        json.dumps(
                                            {
                                                "instance_id": instance,
                                                "repo": repo,
                                                "exception": error,
                                            }
                                        )
                                        + "\n"
                                    )
                                else:
                                    f.write(
                                        json.dumps({"instance_id": instance, "repo": repo}) + "\n"
                                    )

                        success_prs = sum([len(prs) for prs in metrics_success.values()])
                        failure_prs = sum([len(prs) for prs in metrics_failure.values()])
                        print(
                            f"Repos: {len(metrics_success)}/{len(set(metrics_success).union(metrics_failure))}, PRs: {success_prs}/{success_prs + failure_prs}, instance: {instance}, repo: {repo}, elapsed time: {end_time - start_time} (seconds)"
                        )
        return process_results

    dataset_dict = defaultdict(list)

    for instance in dataset:
        if is_repo:
            dataset_dict[instance] = [None]
        else:
            dataset_dict["-".join(instance.split("-")[:-1])].append(instance)
    if not is_repo:
        for repo in dataset_dict:
            dataset_dict[repo].sort(key=lambda instance: int(instance.split("-")[-1]), reverse=True)

    if ratio_range[0] > 0 or ratio_range[1] < 1:
        dict_keys = sorted(list(dataset_dict.keys()), key=str.casefold)
        start_index, end_index = int(ratio_range[0] * len(dict_keys)), int(
            ratio_range[1] * len(dict_keys)
        )
        dataset_dict = {k: dataset_dict[k] for k in dict_keys[start_index:end_index]}

    ordered_instances = []
    instance_appended = True
    curr_instance_index = 0
    while instance_appended:
        instance_appended = False
        for repo in dataset_dict:
            if len(dataset_dict[repo]) > curr_instance_index:
                ordered_instances.append(
                    (dict(repo=repo, instance_list=[dataset_dict[repo][curr_instance_index]]))
                )
                instance_appended = True
        curr_instance_index += 1

    ordered_instances_full = []
    for _ in range(retries_parallel):
        ordered_instances_full += ordered_instances
    results.extend(
        await map_with_concurrency_limit(
            process_with_semaphore, ordered_instances_full, concurrency_limit
        )
    )
    return results


def get_checked_instances(outputs_folder):
    checked_instances = set()
    # Iterate through subfolders
    for subfolder in os.listdir(outputs_folder):
        subfolder_path = os.path.join(outputs_folder, subfolder)
        if os.path.isdir(subfolder_path):  # Ensure it's a directory
            report_file = os.path.join(subfolder_path, "report.json")
            if os.path.exists(report_file):
                checked_instances.add(subfolder)
    return checked_instances


if __name__ == "__main__":
    args = parse_args()
    dataset_dict = {}
    with open(args.data_path, "r") as f:
        dataset = [json.loads(line.strip()) for line in f.readlines()]
        dataset = {
            instance["instance_id"]: instance
            for instance in dataset
            if "rating" in instance and instance["rating"] in ["A+", "A", "A-"]
        }

    print(f"Loaded {len(dataset)} instances from {args.data_path} with rating A+/A/A-")
    processed_instances = []
    if os.path.exists(args.output_path_processed):
        with open(args.output_path_processed, "r") as f:
            processed_instances = [
                json.loads(line.strip())["instance_id"]
                for line in f.readlines()
                if line.strip() != ""
            ]
    filtered_dataset = [instance for instance in dataset if instance not in processed_instances]
    print(f"Filtered down to {len(filtered_dataset)} instances that are not processed yet")

    async_process_all = process_all(
        filtered_dataset,
        is_repo=args.is_repo,
        concurrency_limit=args.parallel,
        meta_path=args.meta_path,
        output_path_processed=args.output_path_processed,
        ratio_range=[0, 1],
        retries_parallel=args.retries_parallel,
    )
    passes = asyncio.run(async_process_all)

    success_instances = {}
    failed_instances = {}
    with open(args.output_path_processed, "r") as f:
        for line in f.readlines():
            if line.strip() == "":
                continue
            data = json.loads(line.strip())
            if "exception" in data:
                if data["instance_id"] not in success_instances:
                    failed_instances[data["instance_id"]] = {
                        "instance_id": data["instance_id"],
                        **dataset.get(data["instance_id"]),
                        **data,
                    }
            else:
                success_instances[data["instance_id"]] = {
                    "instance_id": data["instance_id"],
                    **dataset.get(data["instance_id"]),
                    **data,
                }
                if data["instance_id"] in failed_instances:
                    del failed_instances[data["instance_id"]]

    with open(args.output_path_success, "w") as f_success:
        for instance in success_instances:
            f_success.write(json.dumps(success_instances[instance]) + "\n")

    with open(args.output_path_failure, "w") as f_failure:
        for instance in failed_instances:
            f_failure.write(json.dumps(failed_instances[instance]) + "\n")

    print("*" * 100)
    print(f"Evaluation Results: ")
    print(passes)
    print(f"mean = {sum(passes)/len(passes)} | len = {len(passes)}")
