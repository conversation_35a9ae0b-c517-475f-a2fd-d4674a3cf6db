import json
from datetime import datetime
from typing import Any


def log_with_timestamp(message: str):
    with open(f"/var/log/supervisor/swebenchhard_dataset_config.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}]: {message}\n")


def get_top_languages(dp: dict[str, Any]) -> str:
    try:
        top_languages = dp["metadata"]["pr_languages"]
    except:
        try:
            log_with_timestamp(
                f"Failed to get pr_languages from {dp['metadata'].get('pr_languages', 'N/A')}"
            )
            top_languages = [
                sorted(
                    json.loads(dp["metadata"]["language_count"]).items(),
                    key=lambda x: float(x[1]["percentage"]),
                    reverse=True,
                )[0][0]
            ]
        except:
            log_with_timestamp(
                f"Failed to parse language count from {json.dumps(dp['metadata']['language_count'])}"
            )
            top_languages = ["Unknown"]
    return top_languages


def include_install_for_easy_langs(dp: dict[str, Any]) -> bool:
    instance_id = dp["metadata"].get("instance_id", "unknown")
    top_languages = get_top_languages(dp)
    is_easy_lang = len(
        {"Python", "TypeScript", "JavaScript", "Java", "Go"}.intersection(top_languages)
    ) == len(top_languages)
    log_with_timestamp(
        f"[{instance_id}] Top languages are {top_languages}, is_easy_lang: {is_easy_lang}"
    )
    if is_easy_lang:
        return True
    return False
