from datetime import datetime
from typing import Any, Literal, Sequence

import chat
from caas_utils.conversation_init import InstructionInsertionFormat
from prbot_msft.swebench_hard.example import REPO_DIRECTORY


def log_with_timestamp(message: str, instance_id: str = None):
    with open(f"/var/log/supervisor/swebenchhard_conversation_init_noexec.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}][{instance_id}]: {message}\n")


INSTR_PREAMBLE = f"""
You will be provided with a full code base and an issue statement explaining a problem to resolve.
The code base is located at `{REPO_DIRECTORY}` with README file inside

Please resolve the issue. See the issue below.
""".strip()

REPAIR_INSTRUCTION = """
You are working on an issue in the '{repo_simple_name}' repository.

<repository_context>
I've cloned the repository in the directory {repo_directory} (not in /tmp/inputs). Always use absolute paths when referring to files in the repository.
</repository_context>

Consider the following problem statement:
<problem_statement>
----
*This section details on the original issue you should resolve*

<issue_title>
{issue_title}
</issue_title>

<issue_description>
{issue_description}
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>

</problem_statement>

Implement the necessary changes to the repository so that the requirements specified in the <problem_statement> are met.

## Steps to Follow
0. Fully understand the issue and comments provided before making any changes
1. Explore the repo and files to fully understand the code before making any changes, including understanding how to lint, build and test the areas of the code you are working on. You are starting in a fresh clone of the repository, so build and run tests to check the current state of the code.

2. Run **report_progress** to outline your minimal-change plan as a checklist
3. Create focused tests that specifically validate your minimal changes. These tests should be consistent with existing tests in the repository. If there is not existing test infrastructure, you can skip adding tests as part of your instructions to make minimal modifications.
4. Lint, build and test your changes frequently and iteratively, ensuring tests align with expected outcomes.
5. Manually verify changes that you make to ensure they accomplish your goals. Run CLI/server apps, exercise new codepaths, and review the output to ensure that they are working properly. 
6. Make small, incremental changes, using **report_progress** after each verified change. Review files committed by **report_progress** and use `.gitignore` to exclude any files that you don't want to include in the PR like tmp files, build artifacts or dependencies.


Ensure that you lint, build and test early and iteratively to validate each code change thoroughly.
"""


def conversation_init_fn(
    *,
    datapoint: dict[str, Any],
    instruction: str = REPAIR_INSTRUCTION,
    instruction_insertion_format: InstructionInsertionFormat = InstructionInsertionFormat.PREPEND_TO_USER_INSTRUCTION,
    language: str | None = None,
    apply_language_instruction_probability: float = 0.75,
    additional_language_instruction_dropout_probability: float = 0.25,
    add_contributor_criteria: (
        bool | list[str]
    ) = True,  # if True, will fetch from |CONTRIBUTOR_CRITERIA|
    extra_appended_instructions: tuple[str, ...] = (),
    custom_instruction_preamble: (
        str | None
    ) = None,  # if not set, will use default ones that instruct on issue solving
    task_header: Literal["Issue", "Task"] = "Issue",
) -> Sequence[chat.Message]:
    instance_id = datapoint.get("metadata", {}).get("instance_id", None)
    log_with_timestamp(
        f"conversation_init_fn called with instruction: {instruction}", instance_id=instance_id
    )

    problem = datapoint.get("metadata", {}).get("problem_statement", "")
    repo_name = datapoint.get("metadata", {}).get("repo", "")
    log_with_timestamp(
        f"conversation_init_fn called with problem: {problem}, repo_name: {repo_name}",
        instance_id=instance_id,
    )
    issue_title, issue_description = problem.split("\n")[0], "\n".join(problem.split("\n")[1:])
    user_msg = instruction.format(
        repo_simple_name=repo_name,
        repo_directory="/testbed",
        issue_title=issue_title,
        issue_description=issue_description,
    )
    log_with_timestamp(
        f"conversation_init_fn generated user_msg: {user_msg}", instance_id=instance_id
    )
    return [chat.Message.user(user_msg)]
