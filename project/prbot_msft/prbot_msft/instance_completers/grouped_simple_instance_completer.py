import dataclasses
import math

import berry
import chz
import structlog
from berry.grouped_sample import GroupedSampleAllocationArgs, maybe_add_group_info
from berry.variant_utils import get_variants, group_samples_by_variant
from qstar.instance_completers.simple_passratefilter_instance_completer import (
    SimplePassRateFilterInstanceCompleter,
)
from typing_extensions import override

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz(typecheck=True)
class SWEBenchHardGroupedSimpleInstanceCompleter(SimplePassRateFilterInstanceCompleter):
    """
    A specialized instance completer that groups instances for efficient processing.
    Inherits from SimpleInstanceCompleter to leverage its basic functionality.
    """

    grouped_sample_allocation_args: GroupedSampleAllocationArgs = chz.field(
        doc="Grouped sample allocation args.",
        blueprint_unspecified=GroupedSampleAllocationArgs,
        default_factory=GroupedSampleAllocationArgs,
    )

    @override
    def _get_samples(
        self, ctx: berry.InstanceCompleterCtx, datapoint: berry.Datapoint
    ) -> berry.InstanceSamples[berry.Datapoint]:
        variants: list[Variant] = get_variants(datapoint, ctx.metrics)
        if not variants:
            logger.warning(
                "Bad datapoint! It has no variants, ignoring...",
                dataset_id=datapoint.dataset_config.dataset_id,
                idx_in_dataset=datapoint.idx_in_dataset,
                unique_id=datapoint.unique_id,
                information_content=datapoint.initial_information_content,
                uid=datapoint.uid,
                _print=True,
            )
            return berry.InstanceSamples(samples=[], information_content=0.0)
        target_spi = (
            datapoint.dataset_config.override_target_samples_per_instance
            or self.target_samples_per_instance
        )

        # Enqueue samples
        samples_to_enqueue: list[berry.Sample] = []
        for variant in variants:
            num_samples_needed = (
                math.ceil(target_spi * variant.datapoint.initial_information_content)
                if self.share_spi_across_variants
                else target_spi
            )
            batch_samples = berry.sample.expand_datapoints_to_samples(
                [variant.datapoint],
                seed=("main_instance_completion_batch",),
                samples_per_datapoint=num_samples_needed,
                offset=len(samples_to_enqueue),
                additional_metadata={
                    "variant_id": variant.variant_id,
                    "initial_sample": True,
                },
            )
            maybe_add_group_info(self.grouped_sample_allocation_args, batch_samples)
            samples_to_enqueue += batch_samples
            logger.info(
                "Sampling main batch for variant",
                variant_id=variant.variant_id,
                n_additional_variant_samples=num_samples_needed,
            )

        logger.info(
            "Sampling main batch for instance",
            n_additional_samples=len(samples_to_enqueue),
            n_variants_needing_samples=len(variants),
        )
        samples = self.sample_roller.rollout_samples(ctx=ctx, samples=samples_to_enqueue)
        samples = [
            dataclasses.replace(
                sample, metadata={**sample.metadata, "rollout_is_active_instance": True}
            )
            for sample in samples
        ]

        samples_by_variant = group_samples_by_variant(variants, samples)
        all_statistics = {}
        for variant in variants:
            variant_samples = samples_by_variant[variant.variant_id]
            variant_statistics = berry.SampleStatistics.from_samples(variant_samples)
            all_statistics[variant.variant_id] = variant_statistics
            assert len(variant_samples) > 0
            n_correct = len([sample for sample in variant_samples if sample.is_correct])
            accuracy = n_correct / len(variant_samples)

            average_reward = variant_statistics.average_reward

            # Log objective metrics for the variant samples.
            objective_results = self.instance_objective.evaluate(
                [berry.Rollout(root_sample=sample) for sample in variant_samples]
            )
            for sample, objective_result in zip(variant_samples, objective_results):
                sample.metrics["total_objective"] = objective_result.total_objective
                for k, v in objective_result.objectives.items():
                    sample.metrics[f"objective/{k}"] = v
                for k, v in objective_result.rewards.items():
                    sample.metrics[f"objective_reward/{k}"] = v

            # Naming to be consistent with VariantsInstanceCompleter
            for prefix in [
                "initial_rollout",
                f"initial_rollout/{variant.datapoint.dataset_config.name()}",
            ]:
                ctx.metrics.all_mean(f"{prefix}/estimated_accuracy", accuracy, 1)
                ctx.metrics.all_mean(f"{prefix}/estimated_accuracy_active_instances", accuracy, 1)
                ctx.metrics.all_mean(f"{prefix}/estimated_average_reward", average_reward, 1)
                ctx.metrics.all_max(
                    f"{prefix}/max_instance_reward_all_variants", variant_statistics.max_reward
                )
                ctx.metrics.all_min(
                    f"{prefix}/min_instance_reward_all_variants", variant_statistics.min_reward
                )
                ctx.metrics.all_mean(
                    f"{prefix}/estimated_std_reward", variant_statistics.std_reward, 1
                )

        samples = self.samples_postprocess(ctx, samples, variants, all_statistics)

        logger.info(
            "Finished successful sampling for instance",
            n_samples=len(samples),
            n_successful_variants=len(variants),
        )
        any_rollout_active = any(
            sample.metadata.get("rollout_is_active_instance", False) for sample in samples
        )
        return berry.InstanceSamples(samples=samples, information_content=float(any_rollout_active))
