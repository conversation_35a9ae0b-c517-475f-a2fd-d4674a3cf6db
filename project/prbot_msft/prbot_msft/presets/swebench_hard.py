from berry import preset_utils
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format

format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)

swebenchhard_repair_msft_train = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
    ]
)

swebenchhard_repair_bugfixing_msft = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload04142025.rcr_5636.train_bugfixing",
    ]
)

swebenchhard_repro_msft = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardReproDatasetConfig",
        "dataset_id=data.jadhuang.the5636mre.the5636mre.train",
    ]
)

# RCR dataset formatted for repro
swebenchhard_train_repro_on_rcr_msft = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardReproDatasetConfig",
        "dataset_id=data.jadhuang.the5636rcr.repro.train",
    ]
)

# MRE subset of the5636RCR dataset
swebenchhard_train_repro_msft = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardReproDatasetConfig",
        "dataset_id=data.jadhuang.the5636mre.train_full",
    ]
)

# Intersection of MRE subset, new RCR subset, and bug-fixing subset
swebenchhard_train_repro_rcr_bugfix_mre = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardReproDatasetConfig",
        "dataset_id=data.jadhuang.the5636mre.newrcr.bug_fixing.rendered.train",
    ]
)

swebenchhard_repair_msft_val = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            f"dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload04012025.rcr_5636.validation",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

swebenchhard_12878_repair_msft_val = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            f"dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05052025.rcr_12878.validation",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, filtered for validation instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (155 instances)
swebenchhard_12878_repair_msft_easy_gradea_val = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            f"dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05122025.rcr_12878.validation_easy_gradea",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for validation instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (155 instances)
swebenchhard_12878_repair_msft_easy_gradea_junit_val = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            f"dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05132025.rcr_12878.validation_easy_gradea",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (76 instances)
swebenchhard_12878_repair_msft_hq_val = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            f"dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05202025.rcr_12878.validation_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# update of swebenchhard 5636 test split with fixed unique_id, size 193
swebenchhard_5636_repair_msft_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05052025.rcr_5636.test",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# update of swebenchhard 12878 test split with fixed unique_id, size 457
swebenchhard_12878_repair_msft_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05052025.rcr_12878.test",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (244 instances)
swebenchhard_12878_repair_msft_easy_gradea_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05122025.rcr_12878.test_easy_gradea",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (244 instances)
# also, use Padawan tools v2
swebenchhard_12878_repair_msft_easy_padawan_gradea_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairEvalPadawanDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05122025.rcr_12878.test_easy_gradea",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (244 instances)
swebenchhard_12878_repair_msft_easy_gradea_junit_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05132025.rcr_12878.test_easy_gradea",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (244 instances)
swebenchhard_12878_repair_msft_easy_gradea_junit_vsc_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairVSCEvalDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05132025.rcr_12878.test_easy_gradea",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (107 instances)
swebenchhard_12878_repair_msft_hq_vsc_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairVSCEvalDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05202025.rcr_12878.test_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (107 instances)
swebenchhard_12878_repair_msft_hq_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05202025.rcr_12878.test_hq",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (183 instances)
swebenchhard_12878_repair_msft_hq_testval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (183 instances), eval against (exec | silver patch) grader
swebenchhard_12878_repair_msft_hq_or_testval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainWithOrDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (183 instances)
swebenchhard_12878_repair_msft_hq_testval_or = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairEvalPadawanOrDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (183 instances)
swebenchhard_12878_repair_msft_hq_vsc_testval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairVSCEvalDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (183 instances)
swebenchhard_12878_repair_msft_hq_padawan_testval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairEvalPadawanDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
            "dataset.max_num_yields=256",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (183 instances)
# + Padawan tools
swebenchhard_12878_repair_msft_repro_hq_padawan_testval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardReproTrainPadawanDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (183 instances)
# + Padawan tools and OR grader
swebenchhard_12878_repair_msft_hq_padawan_or_testval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairEvalPadawanOrDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (107 instances)
swebenchhard_12878_repair_msft_hq_eval_silver = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainWithSilverDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05202025.rcr_12878.test_hq",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# Latest SBHv2 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (3373 instances)
swebenchhard_repair_06202025_sbhv2_testval_silver = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainNoExecWithSilverDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload06272025.sbhv2.testval",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# Latest SBHv2 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (3373 instances). Use Padawan v2 tools.
swebenchhard_repair_06202025_sbhv2_testval_padawan_silver = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader",
            "dataset.dataset_id=data.damajercak.swe.upload06272025.sbhv2.testval",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    ),
    format,
)

# Latest SBHv2 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (3373 instances). Use Padawan v2 tools.
swebenchhard_repair_06202025_sbhv2_evaltrain_vsc_silver = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=deep_swe_msft.swe_bench_train_v2_vsc.swebench_hard.SWEBenchHardRepairV2VSCTrainDatasetConfig",
            "dataset.dataset_id=data.jadhuang.swe.upload08182025.sbhv2.vsc.train",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    ),
    format,
)

# Latest SBHv2 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (~20K instances). Use Padawan v2 tools.
swebenchhard_repair_06202025_sbhv2_train_padawan_silver = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader",
            "dataset.dataset_id=damajercak.swe.upload07312025.sbhv2.train",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    ),
    format,
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (107 instances)
swebenchhard_12878_repair_msft_hq_eval_padawan = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairEvalPadawanDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05202025.rcr_12878.test_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (107 instances)
swebenchhard_12878_repair_msft_hq_python_eval_padawan = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader",
            "dataset.dataset_id=data.damajercak.swe.upload07312025.rcr_12878.train_hq_python",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

swebenchhard_repair_12878_05052025_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05052025.rcr_12878.test",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, train set
swebenchhard_train_repair_12878_05052025_train_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05052025.rcr_12878.train",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

swebenchhard_repro_msft_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardReproDatasetConfig",
            "dataset.dataset_id=data.jadhuang.the5636mre.test",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

swebenchhard_train_repair_easy_04182025 = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload04182025.rcr_5636.easy_train",
    ]
)

swebenchhard_train_repair_easy_04222025 = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload04222025.rcr_5636.easy_train",
    ]
)

# update of swebenchhard_train_repair_easy_05012025 with fixed unique_id, size 2k
swebenchhard_train_repair_easy_05052025 = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05052025.rcr_5636.train_easy",
    ]
)

# update of swebenchhard_train_repair_easy_04302025 with fixed unique_id, size 600
swebenchhard_train_repair_easy_easy_05052025 = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05052025.rcr_5636.train_easy_easy",
    ]
)

# latest 12878 dataset, train set
swebenchhard_train_repair_12878_05052025 = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05052025.rcr_12878.train",
    ]
)

# update of swebenchhard_train_repair_easy_04302025 with fixed unique_id, size 600
swebenchhard_train_repair_or_easy_easy_05052025 = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainWithOrDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05052025.rcr_5636.train_easy_easy",
    ]
)

# latest 12878 dataset, filtered for instances that needs only short tool timeout (717 instances)
swebenchhard_train_repair_12878_05062025_easy_short_timeout = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05062025.rcr_12878.train_easy_short_timeout",
    ]
)

# latest 12878 dataset, filtered for instances succeeded in at least 1 of 4 rollouts for tbv3/D64 (1351 instances)
swebenchhard_train_repair_12878_05082025_easy_60p_tbv3 = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05082025.rcr_12878.train_easy_60p_tbv3",
    ]
)

# latest 12878 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (3840 instances)
swebenchhard_train_repair_12878_05122025_easy_gradea = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05122025.rcr_12878.train_easy_gradea",
    ]
)

# latest 12878 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (3840 instances)
swebenchhard_train_repair_12878_05122025_easy_gradea_vsc = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairVSCTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05122025.rcr_12878.train_easy_gradea",
    ]
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (3840 instances)
swebenchhard_train_repair_12878_05122025_or_easy_gradea = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainWithOrDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05122025.rcr_12878.train_easy_gradea",
    ]
)

swebenchhard_train_repair_12878_05122025_easy_gradea_junit = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05132025.rcr_12878.train_easy_gradea",
    ]
)

# Latest SBHv2 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (20109 instances)
swebenchhard_train_repair_06202025_sbhv2_silver = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainNoExecWithSilverDatasetConfig",
        "dataset_id=damajercak.swe.upload07312025.sbhv2.train",
    ]
)

# Latest SBHv2 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (20109 instances). Use Padawan v2 tools.
swebenchhard_train_repair_06202025_sbhv2_padawan_silver = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainPadawanWithSilverDatasetConfig",
        "dataset_id=data.damajercak.swe.upload07312025.sbhv2.train",
    ]
)

# Latest SBHv2 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (20109 instances). Focuses on test writing ability.
swebenchhard_train_06202025_sbhv2_test_silver = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardTestTrainWithSilverDatasetConfig",
        "dataset_id=damajercak.swe.upload07312025.sbhv2.train",
    ]
)

# Latest SBHv2 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (20109 instances). Focuses on test writing ability. Use Padawan v2 tools.
swebenchhard_train_06202025_sbhv2_test_padawan_silver = preset_utils.compose_presets(
    preset_utils.training_dataset_preset(
        [
            "=prbot_msft.configs.swebench_hard:SWEBenchHardTestTrainWithSilverPadawanDatasetConfig",
            "dataset_id=data.damajercak.swe.upload07312025.sbhv2.train",
        ],
    ),
    format,
)

# Latest SBHv2 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (20109 instances). Use Padawan v2 tools.
swebenchhard_train_repair_06202025_sbhv2_padawan_silver_noexec = preset_utils.compose_presets(
    preset_utils.training_dataset_preset(
        [
            "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader",
            "dataset_id=data.damajercak.swe.upload07312025.sbhv2.train",
        ],
    ),
    format,
)


# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (1790 instances)
# also, use Padawan tools v2
swebenchhard_train_repair_12878_05202025_hq_padawan = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainPadawanDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq",
    ]
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (1790 instances)
# also, use Padawan tools v2
swebenchhard_train_repair_12878_05202025_repro_hq_padawan = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardReproTrainPadawanDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq",
    ]
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (1790 instances)
swebenchhard_train_repair_12878_05202025_hq = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq",
    ]
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (1790 instances)
swebenchhard_train_repair_12878_05202025_hq_or = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainWithOrDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq",
    ]
)

swebenchhard_train_repair_12878_05202025_hq_vsc = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairVSCTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq",
    ]
)

swebenchhard_train_repair_12878_05202025_hq_silver = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainWithSilverDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq",
    ]
)

# latest 12878 dataset, prompt with junitxml hints filtered for well specified instances with flaky test suites (2050 instances)
swebenchhard_train_repair_12878_05232025_easy_gradea_complement_hq = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05232025.rcr_12878.train_easy_gradea_complement_hq",
    ]
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (1790 instances)
# + RKLD
swebenchhard_train_repair_rkld_12878_05202025_hq = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairRKLDTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq",
    ]
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (1790 instances)
# + RKLD, Padawan tools v2
swebenchhard_train_repair_rkld_12878_05202025_hq_padawan = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairRKLDTrainPadawanDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq",
    ]
)

# latest 12878 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (3840 instances)
swebenchhard_train_repair_rkld_12878_05122025_easy_gradea = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairRKLDTrainDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05122025.rcr_12878.train_easy_gradea",
    ]
)

# latest 12878 dataset, filtered for instances that received grades A+, A, A- from an agent based on alignment between the issues description and code changes in the PR (3840 instances)
swebenchhard_train_repair_rkld_12878_05122025_easy_gradea_padawan = (
    preset_utils.training_dataset_preset(
        [
            "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairRKLDTrainPadawanDatasetConfig",
            "dataset_id=data.damajercak.swe.upload05122025.rcr_12878.train_easy_gradea",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for well specified instances with flaky test suites (2050 instances)
swebenchhard_train_repair_12878_05232025_easy_gradea_complement_hq_silver = preset_utils.training_dataset_preset(
    [
        "=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainWithSilverDatasetConfig",
        "dataset_id=data.damajercak.swe.upload05232025.rcr_12878.train_easy_gradea_complement_hq",
    ]
)

swebenchhard_12878_repair_msft_hq_testval_or = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainWithOrDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# ibid, eval against (exec | silver patch) grader
swebenchhard_12878_repair_msft_hq_or_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairTrainWithOrDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05202025.rcr_12878.test_hq",
            "dataset.datapoint_converters=",  # Remove augmentations
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# ibid, eval against (exec | silver patch) grader
# + Padawan
swebenchhard_12878_repair_msft_hq_or_padawan_eval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardRepairEvalOrPadawanDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05202025.rcr_12878.test_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    )
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (183 instances). Use Padawan v2 tools.
swebenchhard_12878_test_hq_padawan_testval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardTestTrainExecPadawanDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    ),
    format,
)

# latest 12878 dataset, prompt with junitxml hints filtered for instances for which evaluation passes for gold patch (183 instances). Use Padawan v2 tools.
swebenchhard_12878_test_hq_padawan_or_testval = preset_utils.compose_presets(
    preset_utils.eval_dataset_preset(
        [
            "dataset=prbot_msft.configs.swebench_hard:SWEBenchHardTestTrainORPadawanDatasetConfig",
            "dataset.dataset_id=data.damajercak.swe.upload05282025.rcr_12878.testval_hq",
            # overriding target samples per instance is required for eval
            "dataset.override_target_samples_per_instance=4",
        ]
    ),
    format,
)

swebenchhard_rkld = preset_utils.args_preset(
    [
        "defaults.instance_completer=qstar.instance_completers:SimpleInstanceCompleter",
        "defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer",
        "defaults.instance_completer.instance_optimizer.reinforce_strategy.sample_selector=qstar.sample_selectors:SimpleSampleSelector",
        "defaults.instance_completer.instance_optimizer.reinforce_strategy.reward_reinforcer=qstar.optimizers.strategies.reward_reinforcers:ZeroReinforcer",
        "optimizer.rkld_alpha=1",
    ]
)

nv4_hpe_cotograder_bus = preset_utils.args_preset(
    [
        "...topic_mode_or_user=msft-swebenchhard-scus",
        "...topic_or_snapshot=az://orngscuscresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
        "...bus_renderer=harmony_v4.0.15_berry_v3_256k_orion_lpe_wtruncation",
        "...bus_redis_id=msft-swebenchhard-1",
    ],
)

nv4_uks_cotograder_bus = preset_utils.args_preset(
    [
        "...topic_mode_or_user=msft-swebenchhard",
        "...topic_or_snapshot=az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
        "...bus_renderer=harmony_v4.0.15_berry_v3_256k_orion_lpe_wtruncation",
        "...bus_redis_id=msft-swebenchhard-1",
    ],
)

o3_hpe_cotograder_bus = preset_utils.args_preset(
    [
        "...topic_mode_or_user=swe-main-run",
        "...topic_or_snapshot=az://orngcresco/models/snapshots/o3-0402-410-8shard-decrypted",
        "...bus_renderer=harmony_v4.0.16_berry_v3_1mil_orion_mm_except_tools_no_budget",
        "...bus_redis_id=cotograder-290",
    ],
)

gpt5mini_hpe_cotograder_bus = preset_utils.args_preset(
    [
        "...topic_mode_or_user=swe-main-run",
        "...topic_or_snapshot=az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted",
        "...bus_renderer=harmony_v4.0.16_berry_v3_128k_orion_mm_no_budget_truncate",
        "...bus_redis_id=cotograder-290",
    ],
)
