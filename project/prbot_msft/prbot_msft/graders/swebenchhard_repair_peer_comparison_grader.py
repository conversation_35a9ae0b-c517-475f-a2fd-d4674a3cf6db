from __future__ import annotations

import asyncio
import dataclasses
import json
import traceback
from typing import Any, Sequence, final

import chz
import structlog
from berry.sample import GraderError
from prbot_msft.graders.pareto import pareto_front
from prbot_msft.graders.swebenchhard_criteria_prompts import patch_prompt
from prbot_msft.graders.swebenchhard_grader_base import (
    log_to_file,
    log_to_file_sync,
    make_error_sample,
)
from qstar.common import datapoint, types
from qstar.graders import grader as grader_module
from qstar.graders.repo_graders_v3.cotograder_mixin import GradeFnOutput
from qstar.graders.repo_graders_v3.rfs_grader_base import close_loop, get_loop
from qstar.sample_completers import sample_completer


def verify_same_variant_and_instance_id(samples: Sequence[types.SampleWithCompletion]) -> None:
    # validate that all the samples are from the same problem
    unique_ids = {s.gt_datapoint.unique_id for s in samples}
    assert len(unique_ids) == 1, "these should be the same problem"

    instance_ids = {s.gt_datapoint.metadata["instance_id"] for s in samples}
    assert len(instance_ids) == 1, "these should have the same instance id"


Seed = tuple[Any, ...]
logger = structlog.getLogger(__name__)
LOG_FILENAME = "/var/log/supervisor/swebenchhard_repair_peer_comparison_grader_results.log"


def log_wrapper(message: str, instance_id: str):
    return log_to_file_sync(message, instance_id, LOG_FILENAME)


async def log_wrapper_async(message: str, instance_id: str):
    return await log_to_file(message, instance_id, LOG_FILENAME)


def get_valid_indices_samples(samples):
    valid_tuples = [
        (idx, s)
        for idx, s in enumerate(samples)
        if s.valid and all(s.ephemeral_metadata.get("ranking_strict", []))
    ]
    if valid_tuples:
        return zip(*valid_tuples)
    else:
        return [], []


@chz.chz(typecheck=True)
class SWEBenchHardRepairPeerComparisonGrader(
    grader_module.Grader[datapoint.HarmonyCompletionDatapoint]
):
    """Grade SWE-Bench Hard tasks by comparing the student solution with the silver solution."""

    reward_name: str = "sbh_repair_peer_comparison_grader"

    minimum_group_size: int = chz.field(default=4)

    @property
    def accepts_invalid(self) -> bool:
        return True

    @property
    def loop(self) -> asyncio.AbstractEventLoop:
        return get_loop()

    @classmethod
    def close_loop(cls) -> None:
        return close_loop()

    @final
    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> list[types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]]:
        verify_same_variant_and_instance_id(samples)

        if len(samples) < self.minimum_group_size:
            error_string = f"group_too_small_{self.minimum_group_size}_{len(samples)}"
            exc = ValueError(error_string)
            return [
                make_error_sample(
                    s,
                    exc,
                    grader_name="SWEBenchHardRepairPeerComparisonGrader",
                    reward_name=self.reward_name,
                )
                for s in samples
            ]

        if any("ranking_info_list" not in s.ephemeral_metadata for s in samples if s.valid):
            error_string = f"missing_ranking_info_list"
            exc = ValueError(error_string)
            return [
                make_error_sample(
                    s,
                    exc,
                    grader_name="SWEBenchHardRepairPeerComparisonGrader",
                    reward_name=self.reward_name,
                )
                for s in samples
            ]

        # Reward only the pareto front of ranking_info_list

        logged_additional_metrics = {}

        valid_indices, valid_samples = get_valid_indices_samples(samples)
        frontier_valid_indices = pareto_front(
            [s.ephemeral_metadata.get("ranking_info_list", []) for s in valid_samples]
        )
        frontier_indices = {valid_indices[valid_idx] for valid_idx in frontier_valid_indices}
        logged_additional_metrics["pareto_input_size"] = len(valid_samples)
        logged_additional_metrics["pareto_front_size"] = len(frontier_indices)

        return [
            sample.with_correctness(
                reward_name=self.reward_name,
                is_correct=(idx in frontier_indices),
                given_answer=None,
                additional_metadata={
                    "grader": {
                        **logged_additional_metrics,
                    }
                },
                additional_metrics=logged_additional_metrics,
            )
            for idx, sample in enumerate(samples)
        ]
