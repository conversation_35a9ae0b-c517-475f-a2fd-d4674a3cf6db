from typing import List, Set


def p_dominates_q(p: List, q: List):
    return all(pi >= qi for pi, qi in zip(p, q, strict=True)) and any(
        pi > qi for pi, qi in zip(p, q, strict=True)
    )


def pareto_front(rewards: List[List]) -> Set[int]:
    if not rewards:
        return set()

    n = len(rewards)
    dominated = [False] * n

    for i in range(n):
        if dominated[i]:
            continue

        for j in range(i + 1, n):
            if dominated[j]:
                continue

            if p_dominates_q(rewards[i], rewards[j]):
                dominated[j] = True
            elif p_dominates_q(rewards[j], rewards[i]):
                dominated[i] = True
                break

    return {idx for idx, dom in enumerate(dominated) if not dom}
