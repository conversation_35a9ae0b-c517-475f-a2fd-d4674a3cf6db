import json
from typing import Any

from caas_tool.caas_container import CaasSession
from deep_swe_msft.tools.vscode_copilot_tool import setup_vscutils
from prbot_msft.configs.utils import get_top_languages, log_with_timestamp


async def setup_fn_vscode(
    *, datapoint: dict[str, Any], session: CaasSession, workdir: str | None
) -> None:
    try:
        session.start_keepalive_task(keepalive_interval=60)
        language = get_top_languages(datapoint)[0]
        vscode_settings = datapoint["metadata"].get("task", {}).get("vscode_settings", None)
        log_with_timestamp(
            f"setup_fn_vscode, {language=}, {vscode_settings=}, {datapoint['metadata']['instance_id']=}"
        )
        await setup_vscutils(
            datapoint=datapoint,
            session=session,
            workdir=workdir,
            language=language,
            vscode_settings=vscode_settings,
            dump_settings=False,
        )
    finally:
        await session.stop_keepalive_task()
