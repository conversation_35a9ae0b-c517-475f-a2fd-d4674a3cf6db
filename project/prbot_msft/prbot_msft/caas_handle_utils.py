import asyncio
import importlib
import logging
import os
from dataclasses import KW_ONLY, dataclass, field
from typing import Optional, Type

import msgpack
from caas.commands import DownloadFileFromContainer, RawExec, UploadFile
from caas.internal.errors import Container<PERSON><PERSON><PERSON><PERSON><PERSON>r, Exec<PERSON>rror, TimedOutError
from caas.protocol import VolumeMount
from caas_tool.caas_container import <PERSON><PERSON>sContainer
from caas_tool.caas_container_tool import C<PERSON>sC<PERSON>r<PERSON>ool
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CAAS_IMAGE
from prbot_msft.swebench_hard.container import Handle
from pydantic import ConfigDict, TypeAdapter, dataclasses
from tenacity import retry, stop_after_attempt, wait_fixed, wait_random_exponential

logger = logging.getLogger(__name__)


def _retry_callback(retry_state):
    if retry_state.outcome:
        raise retry_state.outcome.exception()  # Raise the last exception
    raise RuntimeError("Unknown error occurred")  # Fallback


@dataclasses.dataclass(config=ConfigDict(arbitrary_types_allowed=True))
class CaasHandle(Handle):
    _: KW_ONLY
    caas_endpoint: str = CAAS_ENDPOINT
    image_name: str = CAAS_IMAGE
    idle_ttl: int = 3_600  # seconds
    cpu_limit: str = "16.0"
    memory_limit: str = "64g"
    git_volume_mounts: list[VolumeMount] = field(default_factory=list)
    _caas_handle: CaasContainer = field(default=None, init=False, repr=True)

    @property
    def id(self):
        return self._caas_handle.caas_session.id

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.graceful_teardown(self._caas_handle)

    async def graceful_teardown(self, caas_handle):
        try:
            await caas_handle.teardown()
        except Exception as e:
            logger.exception(f"(ignored) Failed to teardown _caas_handle: {e!r}")

    def dump(self) -> bytes:
        obj = TypeAdapter(self.__class__).dump_python(self, exclude={"_caas_handle"})
        obj["_caas_handle"] = {
            "type": (
                self._caas_handle.__class__.__module__,
                self._caas_handle.__class__.__qualname__,
            ),
            "data": self._caas_handle.dump(),
        }
        return msgpack.dumps(obj)

    @classmethod
    async def load(cls, data: bytes) -> "CaasHandle":
        obj = msgpack.loads(data)
        _caas_handle_dict = obj.pop("_caas_handle")

        _caas_handle_module_name, _caas_handle_class_name = _caas_handle_dict["type"]
        _caas_handle_data = _caas_handle_dict["data"]
        _caas_handle_module = importlib.import_module(_caas_handle_module_name)
        _caas_handle_klass: Type[Handle] = getattr(_caas_handle_module, _caas_handle_class_name)
        _caas_handle = await _caas_handle_klass.load(_caas_handle_data)

        handle = TypeAdapter(cls).validate_python(obj)
        handle._caas_handle = _caas_handle
        return handle

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_random_exponential(multiplier=1, max=64),
        retry_error_callback=_retry_callback,
    )
    async def commit(self, name, tag):
        # Cannot exceed lifetime of container
        async with asyncio.timeout(30 * 60):
            image_name = await self._caas_handle.terminal_session.session.commit(
                name=name,
                tag=tag,
                push=True,
            )
            verify_session = await CaasContainer.new(
                caas_endpoint=self.caas_endpoint,
                image_name=image_name,
                idle_ttl=self.idle_ttl,
                cpu_limit=self.cpu_limit,
                memory_limit=self.memory_limit,
            )
            await self.graceful_teardown(verify_session)
            return image_name

    @property
    def raw_handle(self) -> CaasContainer:
        return self._caas_handle

    @retry(
        stop=stop_after_attempt(10),
        wait=wait_random_exponential(multiplier=1, max=64),
        retry_error_callback=_retry_callback,
    )
    async def __async_post_init__(self):
        self._caas_handle = await CaasContainer.new(
            caas_endpoint=self.caas_endpoint,
            image_name=self.image_name,
            idle_ttl=self.idle_ttl,
            cpu_limit=self.cpu_limit,
            memory_limit=self.memory_limit,
            volume_mounts=self.git_volume_mounts,
        )

    @classmethod
    async def create(cls, **kwargs):
        if "image_name" in kwargs:
            if kwargs["image_name"] is None:
                kwargs.pop("image_name")
            elif "acrcommitcaaseastus2ame" in kwargs["image_name"]:
                kwargs["caas_endpoint"] = "https://eastus2.caas.azure.com"
        if "git_volume_mounts" in kwargs:
            if kwargs["git_volume_mounts"] is None:
                kwargs.pop("git_volume_mounts")
        instance = cls(**kwargs)
        await instance.__async_post_init__()
        return instance

    async def exec(
        self,
        cmd: list[str],
        *,
        timeout: int,
        workdir: str,
        env: Optional[dict] = None,
        return_bytes: bool = True,
        user: Optional[str] = None,
        detach: bool = False,
    ) -> tuple[int, str | bytes]:
        timeout = timeout // 1000  # Convert milliseconds to seconds

        try:
            user = "" if user is None else user
            env = {} if env is None else env
            with open("/var/log/supervisor/caas_handle_exec.log", "a") as log_file:
                log_file.write(
                    f"Executing command: {cmd} in {workdir} with env: {env}, timeout: {timeout}, and user: {user}\n"
                )
            exit_code, stdout_bytes = await self._caas_handle.terminal_session.session.run(
                RawExec(
                    cmd=cmd, workdir=workdir, timeout=timeout, env=env, user=user, detach=detach
                )
            )
        except ExecError as e:
            if isinstance(e, (TimedOutError, ContainerKilledError)):
                raise
            else:
                if len(e.output) > 0:
                    raise
                else:
                    exit_code, stdout_bytes = e.status, e.output
        except Exception as e:
            exit_code, stdout_bytes = 1, repr(e).encode()
        if return_bytes:
            return exit_code, stdout_bytes
        else:
            return exit_code, stdout_bytes.decode("utf-8", errors="replace")

    async def upload_file(self, remote_filename: str, contents: bytes):
        await self._caas_handle.terminal_session.session.run(UploadFile(remote_filename, contents))

    async def download_file(self, remote_filename: str):
        return await self._caas_handle.terminal_session.session.run(
            DownloadFileFromContainer(remote_filename)
        )

    def __getattr__(self, name):
        return getattr(self._caas_handle, name)


class CaasHandleTool(CaasContainerTool):
    def __init__(self, *, handle: CaasHandle, **kwargs):
        super().__init__(container=handle, **kwargs)


if __name__ == "__main__":
    import asyncio

    async def mega():
        import json

        handle = await CaasHandle.create()
        rv, out = await handle.exec(
            ["echo", "-n", "hello world"], timeout=30, workdir="/", return_bytes=False
        )
        assert rv == 0
        assert out == "hello world", json.dumps(out)

        await handle.upload_file(
            remote_filename="/hello", contents="echo -n 'hello again'".encode()
        )
        rv, out = await handle.exec(["bash", "/hello"], timeout=30, workdir="/")
        assert rv == 0
        assert out.decode("utf-8") == "hello again", json.dumps(out)

    asyncio.run(mega())
