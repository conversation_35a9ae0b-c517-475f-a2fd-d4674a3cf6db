import copy
import json
import logging
import os
import time
import traceback
from collections.abc import Mapping
from dataclasses import KW_ONLY, dataclass, fields
from enum import Enum
from itertools import islice
from typing import Any, Optional

from datasets import IterableDataset as HFIterableDataset
from datasets.iterable_dataset import DistributedConfig
from torch.utils.data import IterableDataset as TorchIterableDataset
from torch.utils.data import get_worker_info

logger = logging.getLogger(__name__)


class SplitType(Enum):
    TEMPORAL = "Temporal"
    FULL = "Full"


def is_test_file(filename):
    return any(test_word in filename for test_word in ["test", "tests", "e2e", "testing"])


@dataclass
class SweDatum(Mapping):
    _: KW_ONLY
    instance_id: str
    repo: str
    base_commit: str
    split_type: str
    problem_statement: str
    hints_text: str
    patch: str
    test_patch: str
    setup_time: float
    setup_script: Optional[str] = None
    test_script: Optional[str] = None
    initial_gitdiff: Optional[str] = None
    filenames: Optional[str] = None
    language_count: Optional[str] = None
    pr_languages: Optional[list] = None
    tests_results_patched: Optional[dict] = None
    tests_results: Optional[dict] = None
    fail_to_pass: Optional[list] = None
    pass_to_pass: Optional[list] = None
    other_to_other: Optional[list] = None
    image_name: Optional[str] = None
    version: Optional[str] = None
    task: Optional[dict] = None  # This dict may have "vscode_settings_json"
    project_structure: Optional[
        str
    ] = None  # This is similar to filenames, but derived using the VSCode read_project_structure tool

    def __getitem__(self, key: str) -> Any:
        if key in self.__annotations__:
            return getattr(self, key)
        raise KeyError(f"{key} is not a valid attribute")

    def __iter__(self):
        return iter(self.__annotations__)

    def __len__(self):
        return len(self.__annotations__)

    def as_dict(self) -> dict:
        return {field.name: getattr(self, field.name) for field in fields(self)}


class StreamSWEDataset(TorchIterableDataset):
    def __init__(
        self,
        *,
        rank,
        world_size,
        repo_dir: str,
        metadata: HFIterableDataset,
        seekto: Optional[int],
        seed: int = 98052,
        max_epochs: int = 1,
    ):
        super().__init__()
        self.rank = rank
        self.world_size = world_size
        self.repo_dir = repo_dir
        self.metadata = metadata
        self.seekto = seekto if seekto else 0
        self.seed = seed
        self.max_epochs = max_epochs
        self.dataset = None

    def __iter__(self):
        worker_info = get_worker_info()
        if worker_info is not None:
            worker_id = worker_info.id
            num_workers = worker_info.num_workers
        else:
            logger.warning(
                "prefetching not enabled. data loading will be slow. Set num_workers=1 in the pytorch dataloader"
            )
            worker_id = 0
            num_workers = 1
        assert (
            num_workers == 1
        ), "We assume one worker per rank. Sharding across ranks is done with HF."

        # Create a new HFIterableDataset with updated distributed config
        self.dataset = HFIterableDataset(
            ex_iterable=self.metadata._ex_iterable,
            info=copy.deepcopy(self.metadata._info),
            split=self.metadata._split,
            formatting=self.metadata._formatting,
            distributed=DistributedConfig(self.rank, self.world_size),
            token_per_repo_id=self.metadata._token_per_repo_id,
        )
        shuffle_buffer_size = int(os.environ.get("shuffle_buffer_size", "1000"))
        if shuffle_buffer_size > 0:
            self.dataset = self.dataset.shuffle(seed=self.seed, buffer_size=shuffle_buffer_size)

        # Set the initial epoch
        self.dataset.set_epoch(0)
        iterable = islice(iter(self.dataset), self.seekto // self.world_size, None)

        while True:
            try:
                metadata = next(iterable)
            except StopIteration:
                self.dataset.set_epoch(self.dataset._epoch + 1)
                if self.dataset._epoch == self.max_epochs:
                    return
                else:
                    iterable = islice(iter(self.dataset), self.seekto // self.world_size, None)
                    continue
            try:
                start_time = time.time()
                swedatum = self.to_swe_datum(metadata=metadata)
                end_time = time.time()
                swedatum.setup_time = end_time - start_time
                yield swedatum
            except Exception as e:
                logger.warning(f"Skipping {metadata['instance_id']} because {e}")
                logger.info(traceback.format_exc())
                continue

    @staticmethod
    def make_patch_from_prfiles(prfiles, *, test, modified_only):
        lines = []
        for prfile in prfiles:
            if "patch" not in prfile:
                # TODO: this happens with added files, we only have a content url
                continue
            status = prfile["status"]
            if status == "added":
                previous_filename = "dev/null"
                filename = prfile["filename"]
                if modified_only:
                    continue
            elif status == "removed":
                previous_filename = prfile.get("previous_filename", prfile.get("filename"))
                filename = "dev/null"
                if modified_only:
                    continue
            else:
                filename = prfile.get("filename")
                previous_filename = prfile.get("previous_filename", filename)

            if test != any(is_test_file(name) for name in (filename, previous_filename)):
                continue

            hunks = prfile["patch"]
            if status == "added":
                lines.append(
                    f"""diff --git a/{filename} b/{filename}
new file mode 100644
--- /dev/null
+++ b/{filename}
{hunks}\n"""
                )
            elif status == "removed":
                lines.append(
                    f"""diff --git a/{previous_filename} b/{previous_filename}
deleted file mode 100644
--- a/{previous_filename}
+++ /dev/null
{hunks}\n"""
                )
            else:
                lines.append(
                    f"""diff --git a/{previous_filename} b/{filename}
--- a/{previous_filename}
+++ b/{filename}
{hunks}\n"""
                )

        return "\n".join(lines)

    def repo_from_instance_id(self, *, instance_id):
        instance_id_no_pr = "-".join(instance_id.split("-")[:-1])
        return instance_id_no_pr.replace("__", "/", 1)

    def extract_scripts_from_workflow_info(self, workflow_info):
        workflow_contents = workflow_info.get("workflow_contents", [{}])
        jobs = workflow_contents[0].get("jobs", {})
        build = jobs.get("build", {})
        steps = build.get("steps", [])

        # Ensure there are enough steps
        if len(steps) >= 2:
            # "run" in the last step is the test script
            test_script = steps[-1].get("run", "")
            # "run" in the second last step is the setup script
            setup_script = steps[-2].get("run", "")
            return setup_script, test_script
        else:
            return "", ""

    def to_swe_datum(self, *, metadata):
        # If patch is missing, try reading from local metadata and prfiles
        if "patch" not in metadata:
            json_path = os.path.join(self.repo_dir, metadata["instance_id"], "metadata.json")
            with safe_open(json_path) as inp:
                more_metadata = json.loads(inp.read())
                metadata.update(more_metadata)

            # NB: we are using matadata['(test_)?patch'] in upstream processing now
            """
            prfiles_path = os.path.join(self.repo_dir, metadata["instance_id"], "prfiles.json")
            if os.path.exists(prfiles_path):
               with open(prfiles_path) as prfh:
                   prfiles = json.loads(prfh.read())
                   metadata["patch"] = self.make_patch_from_prfiles(prfiles, test=False, modified_only=False)
                   metadata["test_patch"] = self.make_patch_from_prfiles(prfiles, test=True, modified_only=False)
            """

            workflow_info_path = os.path.join(
                self.repo_dir, metadata["instance_id"], "workflow_info.json"
            )
            if os.path.exists(workflow_info_path):
                with safe_open(workflow_info_path) as workflow_info_fh:
                    workflow_info = json.loads(workflow_info_fh.read())
                    (
                        metadata["setup_script"],
                        metadata["test_script"],
                    ) = self.extract_scripts_from_workflow_info(workflow_info)

            for what, where in (
                ("setup_script", "setup.sh"),
                ("test_script", "run_tests.sh"),
                ("initial_gitdiff", "gitdiff.patch"),
            ):
                where_path = os.path.join(self.repo_dir, metadata["instance_id"], where)
                if os.path.exists(where_path):
                    with safe_open(where_path) as where_fh:
                        metadata[what] = where_fh.read()

        return SweDatum(
            instance_id=metadata["instance_id"],
            repo=self.repo_from_instance_id(instance_id=metadata["instance_id"]),
            base_commit=metadata["base_commit"],
            split_type=SplitType(metadata.get("split_type", "Full")).value,
            problem_statement=metadata["problem_statement"],
            hints_text=metadata["hints_text"],  # we would have to collect these separately
            version=metadata["version"],  # we would have to collect these separately
            patch=metadata["patch"],
            test_patch=metadata["test_patch"],
            setup_time=-1,
            setup_script=metadata.get("setup_script"),
            test_script=metadata.get("test_script"),
            initial_gitdiff=metadata.get("initial_gitdiff"),
            filenames=metadata.get("filenames", None),
            language_count=metadata.get("language_count", None),
            pr_languages=metadata.get("languages", None),
            tests_results=metadata.get("tests_results", None),
            tests_results_patched=metadata.get("tests_results_patched", None),
            fail_to_pass=metadata.get("fail_to_pass", None),
            pass_to_pass=metadata.get("pass_to_pass", None),
            other_to_other=metadata.get("other_to_other", None),
            image_name=metadata.get("image_name", None),
        )


if __name__ == "__main__":
    from pprint import pformat

    import datasets

    try:
        ds = datasets.load_dataset(
            "/home/<USER>/code/piyushmadan/nikos_tm-msft/tm-msft/microsoft/personal/zhendong/SWE-bench/tiny",
            streaming=True,
        )
        ssd = StreamSWEDataset(
            rank=0,
            world_size=1,
            repo_dir="/scratch/AzureBlobStorage_CODE/scratch/workspaceblobstore/jadhuang/workflows_ea_v1_filtered/copilot_ws",
            metadata=ds["test"],
            seekto=None,
        )
        iterator = iter(ssd)
        yo = next(iterator)
        print(pformat(yo))
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()


def safe_open(filepath, mode="r"):
    """
    Tries to open a file using the built-in open function. If the file is not found,
    it falls back to using blobfile.BlobFile.

    Args:
        filepath (str): Path to the file.
        mode (str): Mode in which the file should be opened (default is "r").

    Returns:
        file object: A file-like object for the specified file.
    """
    try:
        return open(filepath, mode)
    except FileNotFoundError:
        import blobfile

        logger.warning(f"File not found: {filepath}. Falling back to blobfile.BlobFile.")
        return blobfile.BlobFile(filepath, mode)
