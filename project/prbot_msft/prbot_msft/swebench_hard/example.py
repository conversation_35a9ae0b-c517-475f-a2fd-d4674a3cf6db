import importlib
import os
import shlex
import traceback
import uuid
from dataclasses import KW_ONLY, dataclass
from textwrap import dedent
from typing import Any, Optional, Type

import msgpack
from prbot_msft.swebench.harness.constants import (
    FAIL_TO_PASS,
    KEY_INSTANCE_ID,
    PASS_TO_PASS,
    ResolvedStatus,
)
from prbot_msft.swebench.harness.grading import get_eval_tests_report, get_resolution_status
from pydantic import ConfigDict, TypeAdapter, dataclasses

from .container import Handle as ContainerHandle
from .container import RemoteError, success_output
from .junitxmlutil import chunks_to_flat_result, compute_test_lists
from .stream import SweDatum

REPO_DIRECTORY = "/testbed"
SETUP_DEFAULT_TIMEOUT = 45 * 60 * 1000  # 45 minutes
NUM_PRESETUP_ATTEMPT_CHECKS = 15


def log_with_timestamp(message: str, instance_id: str = None):
    from datetime import datetime

    os.makedirs("/var/log/supervisor", exist_ok=True)  # Ensure the directory exists
    with open(f"/var/log/supervisor/swebenchhard_example.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}][{instance_id}]: {message}\n")


def get_eval_report_from_dict(
    test_spec: dict[str, list[str]],
    agent_results: dict[str, str],
    include_tests_status: bool,
    include_new_tests: bool = False,
) -> dict[str, Any]:
    """
    Generate an evaluation report using the agent_results dictionary instead of log parsing.

    Args:
        test_spec: The test specification object that has attributes:
            - instance_id: identifier of the test instance.
            - FAIL_TO_PASS: list of tests that must change from failing to passing.
            - PASS_TO_PASS: list of tests that should remain passing.
        agent_results (dict): A dictionary mapping test names to their statuses.
        include_tests_status (bool): Whether to include the full tests status details in the report.

    Returns:
        dict: Report containing evaluation metrics, mirroring the original log-parsing report.
    """
    report_map = {}
    instance_id = test_spec["instance_id"]

    # Initialize the report_map with default values.
    report_map[instance_id] = {
        "patch_is_None": False,
        "patch_exists": True,
        # Since agent_results comes from running the agent on the patched version,
        # we assume that the patch was applied successfully.
        "patch_successfully_applied": True,
        "resolved": False,
    }

    # Create a reference dictionary containing ground truth test lists.
    # (If you have computed FAIL_TO_PASS and PASS_TO_PASS from base/gold dictionaries,
    #  then ensure that test_spec.FAIL_TO_PASS and test_spec.PASS_TO_PASS hold those lists.)
    eval_ref = {
        KEY_INSTANCE_ID: test_spec["instance_id"],
        FAIL_TO_PASS: test_spec["FAIL_TO_PASS"],
        PASS_TO_PASS: test_spec["PASS_TO_PASS"],
    }

    # Compute the tests report using your helper function.
    report = get_eval_tests_report(agent_results, eval_ref, include_new_tests=include_new_tests)

    # Determine resolution status.
    if get_resolution_status(report) == ResolvedStatus.FULL.value:
        report_map[instance_id]["resolved"] = True

    # Optionally include the detailed test status information.
    if include_tests_status:
        report_map[instance_id]["tests_status"] = report

    return report_map[instance_id]


async def check_pkg_url(handle: ContainerHandle, env_var_name):
    output = await success_output(
        handle=handle, cmd=["bash", "-c", f"echo ${env_var_name}"], timeout=5000, workdir="/"
    )
    pkg_url = output.decode("utf-8")
    log_with_timestamp("Checking pkg_url: " + pkg_url)
    assert pkg_url != "", f"{env_var_name} is not set."
    assert pkg_url.startswith("https://"), f"{env_var_name} is not a valid URL."
    return "/".join(pkg_url.split("/")[:2])


async def run_setup(
    handle: ContainerHandle,
    repo: str,
    workdir: str,
    commit: str,
    instance_id: str | None = None,
    initial_gitdiff: str | None = None,
    setup_script: Optional[str] = None,
    timeout: int = 3_600_000,
    git_volume_mount_path: str | None = None,
):
    """
    Initialize the resource by cloning the repository, applying any initial patch,
    and running optional custom setup scripts.

    Args:
        handle (ContainerHandle): The container handle to run commands.
        repo (str): The GitHub repository (owner/repo) to clone.
        workdir (str): The target directory where the repository will be cloned.
        commit (str): The Git SHA or ref to checkout after cloning.
        instance_id (str, optional): Identifier for logging purposes. Defaults to None.
        initial_gitdiff (str, optional): Git diff string to apply immediately after clone. Defaults to None.
        setup_script (Optional[str], optional): Additional shell script content to execute
            inside the cloned repository after presetup. Defaults to None.
        timeout (int, optional): Maximum time in milliseconds allowed for each step.
            Defaults to 3_600_000 (1 hour).

    Raises:
        SetupError: If any of the presetup or custom setup commands fail.
        RemoteError: If an uploaded script or command returns a non-zero exit code.
    """
    await check_pkg_url(handle, "CAAS_GIT_MIRROR_URL")
    mirror_setup_cmd = dedent(
        r"""
        git config --global url.$CAAS_GIT_MIRROR_URL/github.com/.insteadOf https://github.com/
    """
    ).strip()
    await success_output(
        handle=handle, cmd=["bash", "-lc", mirror_setup_cmd], timeout=timeout, workdir="/"
    )

    log_with_timestamp(
        f"Running setup for {repo}@{commit}, {git_volume_mount_path=}", instance_id=instance_id
    )
    output = await handle.exec(
        cmd=["ls", "/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/"],
        timeout=timeout,
        workdir="/",
    )
    if git_volume_mount_path:
        presetup_commands = [
            "set -e",
            "DEBIAN_FRONTEND=noninteractive apt update -y -qq > /dev/null 2>&1",
            "DEBIAN_FRONTEND=noninteractive apt install -y -qq zip coreutils > /dev/null 2>&1",
            f"mkdir -p {workdir}",
            f"tar -xf {git_volume_mount_path} -C {workdir}",
            f"chmod -R 777 {workdir}",  # So nonroot user can run tests
            f"cd {workdir}",
            "rm -rf .git",  # Remove the git history
            "git init",
            f"git config --global --add safe.directory {workdir}",
            f"git config --global user.name 'Your Name'",
            f"git config --global user.email '<EMAIL>'",
            # If git_volume_mounts is set, we don't need to clone the repo, it's already there
            # but we need to create an initial commit if it doesn't exist
            "git add .",
            f"git commit -am 'Initial commit'",
            "echo PATH=$PATH >> /root/.bashrc",
            "echo '===SBH PRESETUP DONE==='",
        ]
    else:
        presetup_commands = [
            "set -e",
            "DEBIAN_FRONTEND=noninteractive apt update -y -qq > /dev/null 2>&1",
            "DEBIAN_FRONTEND=noninteractive apt install -y -qq zip coreutils > /dev/null 2>&1",
            f"git clone -o origin https://github.com/{repo} {workdir}",
            f"chmod -R 777 {workdir}",  # So nonroot user can run tests
            f"cd {workdir}",
            f"git reset --hard {commit}",
            # Remove the remote so the agent won't see newer commits.
            "git remote remove origin",
            f"git config --global --add safe.directory {workdir}",
            "echo PATH=$PATH >> /root/.bashrc",
            "echo '===SBH PRESETUP DONE==='",
        ]
    if initial_gitdiff:
        delim = Example.heredoc_delimiter("_do_setup")
        presetup_commands.append(f"set +e")
        presetup_commands.append(
            f"git apply --reject --allow-empty -v - <<'{delim}'\n{initial_gitdiff}\n{delim}"
        )
        presetup_commands.append(f"true")

    presetup_sh = "\n".join(presetup_commands) + "\n"
    log_with_timestamp(f"presetup_sh contents:\n{presetup_sh}", instance_id=instance_id)
    await handle.upload_file("/presetup.sh", presetup_sh.encode())
    await success_output(
        handle=handle,
        cmd=["chmod", "+x", "/presetup.sh"],
        timeout=timeout,
        workdir="/",
    )

    try:
        presetup_done = False
        await success_output(
            handle=handle,
            cmd=["touch", "/presetup.log"],
            timeout=timeout,
            workdir="/",
        )
        await success_output(
            handle=handle,
            cmd=["bash", "-c", f"/presetup.sh 2>&1 | tee /presetup.log"],
            timeout=timeout,
            workdir="/",
            detach=True,
        )

        for attempt in range(NUM_PRESETUP_ATTEMPT_CHECKS):
            import asyncio

            await asyncio.sleep(60)  # sleep for one minute
            output = await success_output(
                handle=handle,
                cmd=["bash", "-c", "tail -n 100 /presetup.log"],
                timeout=timeout,
                workdir="/",
                return_bytes=False,
            )
            if "===SBH PRESETUP DONE===" in output:
                log_with_timestamp(f"[DEBUG] DONE at attempt {attempt}: {output}")
                presetup_done = True
                break
            else:
                if attempt % 100 == 0:
                    log_with_timestamp(f"[DEBUG] {instance_id}: attempt {attempt}")
        if not presetup_done:
            log_with_timestamp(f"[DEBUG] PRESETUP NOT DONE")
            raise SetupError(
                instance_id=instance_id,
                returncode=1,
                cmd=["bash", "/presetup.sh"],
                output="SBH PRESETUP NOT DONE\n" + output,
            )
    except RemoteError as e:
        raise SetupError(
            instance_id=instance_id,
            returncode=e.rv,
            cmd=e.cmd,
            output=traceback.format_exc(),
        )

    if git_volume_mount_path:
        git_log_out = await handle.exec(
            cmd=["git", "log"],
            timeout=timeout,
            workdir=workdir,
        )
        log_with_timestamp(
            "Created initial commit:\n" + git_log_out[1].decode("utf-8"), instance_id=instance_id
        )

    if setup_script:
        await handle.upload_file(f"{workdir}/setup.sh", setup_script.encode())
        # NB: turns out, when we run the agentic onboarding, we only care that run_tests succeeds, not that setup succeeds, so ignore this error
        #     if things are f-d then run_tests will fail to compute base_resolved and/or gold_resolved properties
        await handle.exec(
            cmd=["bash", f"{workdir}/setup.sh"],
            timeout=timeout,
            workdir=workdir,
        )
        await handle.exec(
            cmd=["rm", "-f", f"{workdir}/setup.sh"],
            timeout=timeout,
            workdir=workdir,
        )


async def initialize_resource(
    handle: ContainerHandle,
    repo: str,
    workdir: str,
    commit: str,
    instance_id: str | None = None,
    initial_gitdiff: str | None = None,
    setup_script: Optional[str] = None,
    timeout: int = 3_600_000,
    do_setup: bool = True,
    git_volume_mount_path: str | None = None,
):
    if do_setup:
        await run_setup(
            handle=handle,
            repo=repo,
            workdir=workdir,
            commit=commit,
            instance_id=instance_id,
            initial_gitdiff=initial_gitdiff,
            setup_script=setup_script,
            timeout=timeout,
            git_volume_mount_path=git_volume_mount_path,
        )


class TestError(Exception):
    def __init__(self, instance_id, gold_test_patch, gold_code_patch, returncode, cmd, output):
        super().__init__(f"{instance_id}: run tests failed {gold_test_patch=} {gold_code_patch=}")
        self.instance_id = instance_id
        self.gold_test_patch = gold_test_patch
        self.gold_code_patch = gold_code_patch
        self.returncode = returncode
        self.cmd = cmd
        self.output = output


class SetupError(Exception):
    def __init__(self, instance_id, returncode, cmd, output):
        super().__init__(f"{instance_id}: setup failed")
        self.instance_id = instance_id
        self.returncode = returncode
        self.cmd = cmd
        self.output = output


@dataclasses.dataclass(config=ConfigDict(arbitrary_types_allowed=True))
class Example:
    _: KW_ONLY
    swe_datum: SweDatum
    container_handle: ContainerHandle
    _dont_call_the_constructor_directly_use_async_def_create: bool = True
    _pre_untracked: Optional[set[str]] = None
    _fail_to_pass: Optional[list[str]] = None
    _pass_to_pass: Optional[list[str]] = None
    _other_to_other: Optional[list[tuple[str, str, str]]] = None
    _base_results: Optional[dict[str, str]] = None
    _gold_code_results: Optional[dict[str, str]] = None

    def dump(self) -> bytes:
        obj = TypeAdapter(self.__class__).dump_python(self, exclude={"container_handle"})
        obj["container_handle"] = {
            "type": (
                self.container_handle.__class__.__module__,
                self.container_handle.__class__.__qualname__,
            ),
            "data": self.container_handle.dump(),
        }
        if obj["_pre_untracked"]:
            obj["_pre_untracked"] = list(obj["_pre_untracked"])

        return msgpack.dumps(obj)

    @classmethod
    async def load(cls, data: bytes) -> "Example":
        obj = msgpack.loads(data)

        if obj["_pre_untracked"]:
            obj["_pre_untracked"] = set(obj["_pre_untracked"])

        container_module_name, container_class_name = obj["container_handle"]["type"]
        container_data = obj["container_handle"]["data"]
        container_module = importlib.import_module(container_module_name)
        container_klass: Type[ContainerHandle] = getattr(container_module, container_class_name)
        obj["container_handle"] = await container_klass.load(container_data)

        instance = TypeAdapter(cls).validate_python(obj)

        return instance

    @staticmethod
    def heredoc_delimiter(salt):
        return uuid.uuid5(uuid.NAMESPACE_DNS, salt)

    @property
    def repo_directory(self):
        return REPO_DIRECTORY

    @property
    def pre_untracked(self):
        return self._pre_untracked

    @property
    def fail_to_pass(self):
        return self._fail_to_pass

    @property
    def pass_to_pass(self):
        return self._pass_to_pass

    @property
    def other_to_other(self):
        return self._other_to_other

    @property
    def test_spec(self):
        return {
            "instance_id": self.swe_datum.instance_id,
            "FAIL_TO_PASS": self.fail_to_pass,
            "PASS_TO_PASS": self.pass_to_pass,
        }

    @property
    def base_report(self):
        return get_eval_report_from_dict(
            test_spec=self.test_spec, agent_results=self._base_results, include_tests_status=True
        )

    @property
    def gold_report(self):
        return get_eval_report_from_dict(
            test_spec=self.test_spec,
            agent_results=self._gold_code_results,
            include_tests_status=True,
        )

    async def eval_report(self, patch, *, timeout):
        agent_results = await self._xml_eval_results(
            self.run_patched_code_and_gold_tests_script(patch),
            gold_test_patch=True,
            gold_code_patch=False,
            timeout=timeout,
        )
        return get_eval_report_from_dict(
            test_spec=self.test_spec, agent_results=agent_results, include_tests_status=True
        )

    async def eval_test_report(self, test_patch, *, timeout):
        agent_results = await self._xml_eval_results(
            self.run_gold_code_and_patched_tests_script(test_patch),
            gold_test_patch=False,
            gold_code_patch=True,
            timeout=timeout,
        )
        return get_eval_report_from_dict(
            test_spec=self.test_spec,
            agent_results=agent_results,
            include_tests_status=True,
            include_new_tests=True,
        )

    async def git_diff(self, *, include_new_untracked: bool, timeout: int):
        # NB: return value of git diff is 1 if there are differences
        exclude_patterns = shlex.join(
            [f":(exclude){file}" for file in self.pre_untracked if file.strip() != ""]
        )

        base_commit = self.swe_datum.base_commit
        if (
            hasattr(self.container_handle, "git_volume_mounts")
            and self.container_handle.git_volume_mounts
        ):
            # If git_volume_mounts is set, we have to get base commit
            log_with_timestamp(
                "Getting base commit since git_volume_mounts is set...",
                instance_id=self.swe_datum.instance_id,
            )
            _, base_commit = await self.container_handle.exec(
                cmd=["git", "rev-list", "--max-parents=0", "HEAD"],
                workdir=self.repo_directory,
                timeout=timeout,
                return_bytes=False,
            )
            log_with_timestamp(
                f"Base commit is {base_commit}", instance_id=self.swe_datum.instance_id
            )
        base_commit = base_commit.strip()

        _, tracked_diff = await self.container_handle.exec(
            cmd=[
                "bash",
                "-lc",
                f"git diff {base_commit} -- . {exclude_patterns}"
                if exclude_patterns
                else f"git diff {base_commit} -- .",
            ],
            workdir=self.repo_directory,
            timeout=timeout,
            return_bytes=False,
        )
        if not include_new_untracked:
            return tracked_diff

        git_ls_files_output = await success_output(
            handle=self.container_handle,
            cmd=["git", "ls-files", "--exclude-standard", "--others", "-z"],
            workdir=self.repo_directory,
            timeout=timeout,
            return_bytes=False,
        )
        more_diffs = [
            (
                await self.container_handle.exec(
                    cmd=["git", "diff", "--no-index", "/dev/null", shlex.quote(filename)],
                    workdir=self.repo_directory,
                    timeout=timeout,
                    return_bytes=False,
                )
            )[1]
            for filename in git_ls_files_output.split("\0")
            if filename not in self._pre_untracked
        ]

        return "\n".join([tracked_diff] + more_diffs)

    @property
    def run_tests_command(self):
        return [
            "set +e",
            *[
                line
                for line in (self.swe_datum.test_script or "").splitlines()
                if not line.strip().startswith("set -e")
            ],
            "set -e",
            f"cd {self.repo_directory}",
            "mkdir -p junit_xml_reports",
        ]

    """
    NB: there's some funny business here.
        we protect the newly contributed tests from being modified (reset_tests_command).
        but we don't protect other tests from being modified.
        it's unclear how to do this in general b/c we don't know which files are tests.
    """

    @property
    def run_gold_tests_script(self):
        return self.run_tests_script(self.swe_datum.test_patch)

    def run_tests_script(self, test_patch=None):
        if test_patch is not None:
            import re

            DIFF_MODIFIED_FILE_REGEX = r"^--- a/(.+)"
            modified_test_files = set(
                re.findall(DIFF_MODIFIED_FILE_REGEX, test_patch, re.MULTILINE)
            )

            NEW_FILE_REGEX = r"^--- /dev/null\s*\n\+\+\+ b/(.+)"
            new_test_files = set(re.findall(NEW_FILE_REGEX, test_patch, re.MULTILINE))

            if modified_test_files:
                quoted_files = " ".join(shlex.quote(f) for f in modified_test_files)
                reset_tests_command = f"git checkout --force {shlex.quote(self.swe_datum.base_commit)} -- {quoted_files}"
            else:
                reset_tests_command = ""

            if new_test_files:
                quoted_files = " ".join(shlex.quote(f) for f in new_test_files)
                remove_tests_command = f"rm -f -- {quoted_files}"
            else:
                remove_tests_command = ""

            delim = Example.heredoc_delimiter("run_gold_tests_script")
            apply_test_patch_command = (
                f"git apply --reject --allow-empty -v - <<'{delim}'\n{test_patch}\n{delim}"
            )

            return [
                "set -e",
                f"cd {self.repo_directory}",
                reset_tests_command,
                remove_tests_command,
                "set +e",
                apply_test_patch_command,
                "set -e",
                *self.run_tests_command,
                f"cd {self.repo_directory}",
                remove_tests_command,
                reset_tests_command,
            ]
        else:
            return self.run_tests_command

    def run_patched_code_and_gold_tests_script(self, patch=None):
        if patch is not None:
            seeded_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, self.swe_datum.base_commit)
            # TODO: upload patch as file instead of here doc
            code_restore_patch = f"/restore_{seeded_uuid.hex}.patch"

            save_current_code_state_command = (
                f"git diff {self.swe_datum.base_commit} > {code_restore_patch}"
            )
            reset_code_command = f"git reset --hard {self.swe_datum.base_commit}"
            delim = Example.heredoc_delimiter("run_gold_code_and_tests_script")
            apply_code_patch_command = (
                f"git apply --reject --allow-empty -v - <<'{delim}'\n{patch}\n{delim}"
            )
            restore_current_code_state_command = (
                f"git apply --reject --allow-empty -v {code_restore_patch}"
            )

            return [
                "set -e",
                f"cd {self.repo_directory}",
                save_current_code_state_command,
                reset_code_command,
                "set +e",
                apply_code_patch_command,
                "set -e",
                *self.run_gold_tests_script,
                f"cd {self.repo_directory}",
                reset_code_command,
                restore_current_code_state_command,
                f"rm -f {code_restore_patch}",
            ]
        else:
            return self.run_gold_tests_script

    def run_gold_code_and_patched_tests_script(self, test_patch=None):
        if test_patch is not None:
            seeded_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, self.swe_datum.base_commit)
            # TODO: upload patch as file instead of here doc
            code_restore_patch = f"/restore_{seeded_uuid.hex}.patch"

            save_current_code_state_command = (
                f"git diff {self.swe_datum.base_commit} > {code_restore_patch}"
            )
            reset_code_command = f"git reset --hard {self.swe_datum.base_commit}"
            delim = Example.heredoc_delimiter("run_gold_code_and_tests_script")
            apply_code_patch_command = f"git apply --reject --allow-empty -v - <<'{delim}'\n{self.swe_datum.patch}\n{delim}"
            restore_current_code_state_command = (
                f"git apply --reject --allow-empty -v {code_restore_patch}"
            )

            return [
                "set -e",
                f"cd {self.repo_directory}",
                save_current_code_state_command,
                reset_code_command,
                "set +e",
                apply_code_patch_command,
                "set -e",
                *self.run_tests_script(test_patch),
                f"cd {self.repo_directory}",
                reset_code_command,
                restore_current_code_state_command,
                f"rm -f {code_restore_patch}",
            ]
        else:
            return self.run_gold_tests_script

    def _extract_xml_chunks(self, data):
        import base64
        import io
        import zipfile

        zip_bytes = base64.b64decode(data)

        try:
            zip_file = zipfile.ZipFile(io.BytesIO(zip_bytes), "r")
        except zipfile.BadZipFile:
            return {}

        chunks = {}
        for filename in zip_file.namelist():
            if filename.endswith(".xml"):
                with zip_file.open(filename) as f:
                    chunks[filename] = f.read()

        return chunks

    async def _xml_eval_results(self, commands, *, gold_test_patch, gold_code_patch, timeout):
        try:
            await self.container_handle.upload_file(
                f"{self.repo_directory}/eval.sh", "\n".join(commands).encode()
            )
            await success_output(
                handle=self.container_handle,
                cmd=["bash", f"{self.repo_directory}/eval.sh"],
                timeout=timeout,
                workdir=self.repo_directory,
            )
            collect_cmd = [
                "sh",
                "-c",
                "find junit_xml_reports -type f -print0 | xargs -0 -r -x zip -q - | base64 -w 0",
            ]
            collect_out = await success_output(
                handle=self.container_handle,
                cmd=collect_cmd,
                timeout=timeout,
                workdir=self.repo_directory,
                return_bytes=False,
            )
            cleanup_cmd = [
                "rm",
                "-rf",
                f"{self.repo_directory}/eval.sh",
                f"{self.repo_directory}/junit_xml_reports",
            ]
            await success_output(
                handle=self.container_handle,
                cmd=cleanup_cmd,
                timeout=timeout,
                workdir=self.repo_directory,
            )
        except RemoteError as e:
            raise TestError(
                instance_id=self.swe_datum.instance_id,
                gold_test_patch=gold_test_patch,
                gold_code_patch=gold_code_patch,
                returncode=e.rv,
                cmd=e.cmd,
                output=e.out,
            )

        return chunks_to_flat_result(self._extract_xml_chunks(collect_out))

    async def _compute_fail_to_pass(self, *, timeout, run_tests):
        if run_tests:
            self._base_results = await self._xml_eval_results(
                self.run_tests_command,
                gold_test_patch=False,
                gold_code_patch=False,
                timeout=timeout,
            )
            self._gold_code_results = await self._xml_eval_results(
                self.run_patched_code_and_gold_tests_script(self.swe_datum.patch),
                gold_test_patch=True,
                gold_code_patch=True,
                timeout=timeout,
            )
            self._fail_to_pass, self._pass_to_pass, self._other_to_other = compute_test_lists(
                self._base_results, self._gold_code_results
            )
        else:
            self._base_results = None
            self._gold_code_results = None
            self._fail_to_pass, self._pass_to_pass, self._other_to_other = [None] * 3

    async def _do_setup(self, *, timeout):
        git_volume_mounts = (
            self.container_handle.git_volume_mounts
            if hasattr(self.container_handle, "git_volume_mounts")
            else []
        )
        print(f"git_volume_mounts: {git_volume_mounts}")
        log_with_timestamp(
            f"git_volume_mounts: {git_volume_mounts}",
            instance_id=self.swe_datum.instance_id,
        )
        git_volume_mount_path = None
        if len(git_volume_mounts) > 0:
            git_volume_mount_path = git_volume_mounts[0].container
        print(f"git_volume_mount_path: {git_volume_mount_path}")
        log_with_timestamp(
            f"git_volume_mount_path: {git_volume_mount_path}",
            instance_id=self.swe_datum.instance_id,
        )
        await initialize_resource(
            handle=self.container_handle,
            repo=self.swe_datum.repo,
            workdir=self.repo_directory,
            commit=self.swe_datum.base_commit,
            instance_id=self.swe_datum.instance_id,
            initial_gitdiff=self.swe_datum.initial_gitdiff,
            setup_script=self.swe_datum.setup_script,
            timeout=timeout,
            do_setup=True,
            git_volume_mount_path=git_volume_mount_path,
        )

    async def _compute_pre_untracked(self, *, timeout):
        try:
            git_ls_files_output = await success_output(
                handle=self.container_handle,
                cmd=["git", "ls-files", "--exclude-standard", "--others", "-z"],
                workdir=self.repo_directory,
                timeout=timeout,
                return_bytes=False,
            )
        except RemoteError as e:
            raise SetupError(
                instance_id=self.swe_datum.instance_id,
                returncode=e.rv,
                cmd=e.cmd,
                output=traceback.format_exc(),
            )

        self._pre_untracked = {filename for filename in git_ls_files_output.split("\0")}

    def __post_init__(self):
        assert (
            not self._dont_call_the_constructor_directly_use_async_def_create
        ), "don't call the constructor directly use async def create"

    async def __async_post_init__(self, *, timeout, run_tests, run_setup):
        if run_setup:
            await self._do_setup(timeout=timeout)
        # TODO: check if already defined in swe_datum
        await self._compute_fail_to_pass(timeout=timeout, run_tests=run_tests)
        await self._compute_pre_untracked(timeout=timeout)

    @classmethod
    async def create(cls, *, swe_datum, container_handle, timeout, run_tests=None, run_setup=None):
        if run_setup is None:
            run_setup = container_handle.image_name != swe_datum.image_name
        if run_tests is None:
            run_tests = (
                swe_datum.fail_to_pass is None
                or swe_datum.pass_to_pass is None
                or swe_datum.other_to_other is None
            )
        if swe_datum.test_script is None:
            run_tests = False

        instance = cls(
            swe_datum=swe_datum,
            container_handle=container_handle,
            _dont_call_the_constructor_directly_use_async_def_create=False,
        )
        await instance.__async_post_init__(
            timeout=timeout, run_tests=run_tests, run_setup=run_setup
        )
        if (
            instance._fail_to_pass is None
            and instance._pass_to_pass is None
            and instance._other_to_other is None
            and swe_datum.fail_to_pass is not None
            and swe_datum.pass_to_pass is not None
            and swe_datum.other_to_other is not None
        ):
            instance._base_results = swe_datum.tests_results
            instance._gold_code_results = swe_datum.tests_results_patched
            instance._fail_to_pass, instance._pass_to_pass, instance._other_to_other = (
                swe_datum.fail_to_pass,
                swe_datum.pass_to_pass,
                swe_datum.other_to_other,
            )
        return instance

    # TODO: allow user to run subset of tests
    # def run_specific_tests(...)

    async def get_evaluation_report(self) -> dict:
        pass
