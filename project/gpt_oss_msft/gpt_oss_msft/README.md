# `deep_swe_msft`

SWE project @ Microsoft

For more details, check [SWE Agent deep_swe_msft project in Glass](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/6327/SWE-Agent-deep_swe_msft-project-in-Glass)

1. Install Project
   ```bash
   oaipkg installoai deep_swe_msft
   ```

2. Setup Task Repo 
   ```python
   ENVIRONMENT.update(
      {
         "SWE_TASK_REPO" : "az://orngcresco/data/swang/swe/upload20250320/wave1_1/repo_tasks",
      }
   )
   ```

3. Setup Upload Repo
   ```python
   ENVIRONMENT.update(
      {
         "SWE_UPLOAD_REPO" : "https://codemodeldata.blob.core.windows.net/swe-model-training-data/github_repos_v2_training/wave1_1/merged_upload_repos",
         "STRAWBERRYACE_CENTRALUS_TOKEN": "SAS_TOKEN",
      }
   )
   ```
   
4. Get GPUs
   ```bash
   python -m qstar.peashooter.run_peashooter \
   ...rapid_id=swe-derisk-128 \
   rapid.cluster.name=prod-uksouth-7 \
   rapid.cluster.priority=team-critical \
   rapid.pull_git_on_restart=False \
   num_controller_nodes=1 \
   n_gpus_per_sampler=8 \
   sampling_jobs=8 \
   n_train_gpus=56 \
   cpu_controller=False \
   security_profile=msft \
   strict=False
   ```   
   
5. Baseline Run

   ```bash
    python -m qstar.run_experiment nostrict \
    name=nv4-swe-baseline \
    :berry_models.scallion_lpe:d36_80g_mbg16_bf16 \
    :deep_swe_msft.sweberry_v2.presets:train_baseline \
    optimizer.hparam_scaler.lr_per_instance_d16=1e-5 \
    policy.n_gpus=56 \
    policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True' \
    policy.ml_config='ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22' \
    policy.initial_checkpoint=az://orngcresco/models/snapshots/models.tc/nv4-strawberry-step-320-transferred-20250122-decrypted \
    policy.is_multimodal=True \
    peashooter.timeout_seconds.stalled_datapoint=3600 \
    timeout.default=None \
    ...target_samples_per_instance=32 \
    ...instances_per_batch=32 \
    ...batch_size_schedule=StepBasedLinearWarmup \
    ...batch_size_schedule.initial_instances_per_batch=8 \
    ...batch_size_schedule.num_iterations=4 \
    ...num_initial_samples=32 \
    ...n_batches_in_flight=8 \
    ...n_ctx=131072 \
    ...save_every=5 \
    ...inverse_token_cost=4096 \
    ...tool_penalty_multiplier=0 \
    ...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe \
    ...harmony_constrained_sampling=True \
    skip_validate_config=True \
    security_profile=msft-orng \
    github_upload=False \
    wandb_enable=True \
    kafka_enable=False \
    2>&1 | tee -a swe-d36.log
   ```
6 Eval & Visualize 

[swe_bench_eval_msft/README.md](../swe_bench/swe_bench_eval_msft/README.md) 

[swe_visualizer_msft/README.md](../swe_bench/swe_visualizer_msft/README.md) 
