This document explains berry configs from scratch. If you already know about `chz` and presets, skip the intro and check
what tools we’ve built to understand and validate your configs:

[🔦 Use tools to inspect configs](https://www.notion.so/Use-tools-to-inspect-configs-1df8e50b62b080089953d265bb537eea?pvs=21)

# 🌐 General

berry config is based on [chz](https://github.com/openai/openai/tree/master/lib/chz). What chz essentially does is allow
us to specify a large, complex and deeply nested Python object via command line. The Python object can have collections
such as `dict` and `tuple`.

For instance, a `chz` class can be like that:

```python
@chz.chz
class ComponentA:
    a: int
    b: int

@chz.chz
class ComponentB:
    c: int
    d: int

@chz.chz
class Args:
    a_comp: tuple[ComponentA, ...]
    b_comp: dict[str, ComponentB]
```

And we can specify its value via command-line like that:

```bash
python ~/play_chz.py \
    a_comp.0.a=1 \
    a_comp.0.b=2 \
    a_comp.1.a=3 \
    a_comp.1.b=4 \
    b_comp.abc.c=5 \
    b_comp.abc.d=6 \
    b_comp.def.c=7 \
    b_comp.def.d=8
```

`chz` objects are immutable. `chz.replace(...)` will return a new copy of the object with modified fields. This is a
great feature 🎉 as we can trust what we see and never need to reason whether the `chz` object can be mutated along the
way.

# ✨ `chz` object in qstar

The chz `Args` object in qstar runs is defined in
this [file](https://github.com/openai/openai/blob/master/project/qstar/qstar/args.py). The overall structure of the
`Args` is like that:

```
<root-level configs>
defaults.
  ...
batcher.curriculum.training_datasets.
  0.
    <configs for dataset 0>
  1.
    <configs for dataset 1>
  2.
    <configs for dataset 2>
  ...
```

In terms of pure config size, the majority of `Args` are dataset configs, especially for big runs, although the major
part of dataset configs are just repetitive contents. As an example, the big run `strawberrybr-tbv3p2-hailmary-v6`,
`8.9 MB` of `10.2 MB` total configs are dataset configs (~87%).

You can visualize how launch commands expand for each run in 🍋 [go/lemon](https://go/lemon).
e.g.: [strawberrybr-tbv3p2-hailmary-v6](https://lemon-br.pom.whale.sci.openai.org/configs?experiment_id=strawberrybr-tbv3p2-hailmary-v6).
Clicking the per-dataset link you will see the expanded chz object for that dataset:

![image.png](attachment:842062f6-43a4-45ec-a699-4f6ece17c4fc:image.png)

See more
in [🔦 Use tools to inspect configs](https://www.notion.so/Berry-Config-101-go-sb-config101-1df8e50b62b080b699abf2e479b0006d?pvs=21)
section.

The configs for a single dataset consist of 2 parts:

1. Information about the dataset: e.g.: *where can we get the dataset problems.*
2. RL configs about the dataset: e.g.: *ITC*, *reinforcement scale, passrate filter, graders, etc.*

2. is the major part, though lots of datasets share the same RL configs in a typical run.

# 🈯 Presets

The qstar `Args` object is extremely complex, so the command will be outrageously long if we specify everything there.
Presets are what we introduced to mitigate this. In a nutshell, a preset is just a function (or more precisely, anything
that conforms to [
`Callable[[str, chz.Blueprint[_T]], chz.Blueprint[_T]]`](https://github.com/openai/openai/blob/64d43a827251af9786afa560b3a7f8ae5cc02b3e/lib/chz_presets/chz_presets/core.py#L11))
defined in our codebase to specify what commands to apply. Here is an example of a simple preset:

```python
@chz.chz
class Args:
    name: str

def test_preset(layer_name: str, blueprint: chz.Blueprint[Args]) -> chz.Blueprint[Args]:
    return blueprint.apply({"name": "test"}, layer_name=layer_name)
    
# used like `python -m qstar.example :path.to.module:test_preset`
```

Here `layer_name` is just for debugging purpose.

In qstar, presets can be composed of other presets thus can be
nested ([example](https://github.com/openai/openai/blob/ce3d54c11b16bc158d4abe021a84dc292b502efc/project/qstar/qstar/presets/scirl.py#L136)).
One of presets’ major use cases in qstar is to compose dataset-specific config. For instance, this is something we used
for big
run ([code here](https://github.com/openai/openai/blob/a729a0df38852a8de79960dd1d998f97d0540237/project/qstar/qstar/presets/tbv3p1/derisk_1216_w_delib.py#L12)):

```python
tr_kaggle = berry.preset_utils.training_dataset_preset(
    [
        "variant_producer.variant_producers.0.reward_distribution_power=1.0",
        "variant_producer.variant_producers.0.num_reward_multipliers=4",
        "variant_producer.variant_producers.0.max_reward_multiplier=1024",
        "variant_producer.variant_producers.0.min_reward_multiplier=16",
        "variant_producer.variant_producers.0=VarDiscountingVariantProducer",
        "resource_configs.0.memory_limit=20",
        "resource_configs.0.cpu_limit=10",
        "tool_configs.0.tool_timeout=1200",
        "grader.use_fixed_threshold=True",
        "tool_configs.0.timeout=600",
        "resource_configs.0=qstar.common.tools.python.python_tool:JupyterKernelResourceConfig",
        "dataset_container=oaiwhalesong/strawberry-processing",
        "variant_producer=CompositeVariantProducer",
        "datapoint_type=HarmonyCompletionDatapoint",
        "tool_configs.0=qstar.common.tools.python.python_tool:PythonToolConfig",
        "max_num_yields=40",
        "dataset_id=reasoning.kaggle.v3p1_p90.train",
        "grader=qstar.graders.kaggle_grader:KaggleGrader",
        "=HarmonyCompletionDatasetConfig",
        "variant_producer.variant_producers.1=qstar.common.tools.delegate.deliberate_tool:DeliberateVariantProducer",
        "variant_producer.variant_producers.1.distribution_power=1.0",
        "variant_producer.variant_producers.1.reward_cutoff_ratio_per_depth.0=1.0",
        "variant_producer.variant_producers.1.reward_cutoff_ratio_per_depth.1=0.75",
        "variant_producer.variant_producers.1.reward_cutoff_ratio_per_depth.2=0.375",
    ]
)
```

The function `berry.preset_utils.training_dataset_preset` is responsible for generating commands like:

```bash
batcher.curriculum.training_datasets.<index>.dataset_id=reasoning.kaggle.v3p1_p90.train
```

It has the magic to ensure `<index>` is generated sequentially.

# 🧪 Case study for a real qstar run

Now, let’s study a simple command for a qstar run!

```bash
python -m qstar.run_experiment \
	:berry_models.scallion:d32_80g \
	:qstar.presets.scirl:d32_shrub \
	name="token-cost-test5-obj"
```

`:berry_models.scallion:d32_80g` - this is how we specify a preset (starting with prefix `:`) in qstar runs. Basically
`berry_models.scallion:d32_80g` means to find the `d32_80g` attribute in module `berry_models.scallion` (which is in
`lib/berry_models/berry_models/scallion.py`). `d32_80g`
is [defined](https://github.com/openai/openai/blob/6f8d591c7b0608318500d6d14247748df7b9a328/lib/berry_models/berry_models/scallion.py#L59)
like that, which specifies the config parameters for the model:

```python
def make_d16_80g(model_name: str) -> berry.preset_utils.Preset:
    model_path = berry.preset_utils.model_path(model_name)
    return berry.preset_utils.compose_presets(
        berry.preset_utils.args_preset(
            [
                f"{model_path}=berry_tw.model_config:ModelConfig",
                f"{model_path}.set_by_preset=True",
                f"{model_path}.model_config_name=falcon.orion.d16-s32-k4-fp16rgs-scallion-trimodal",
                f"{model_path}.initial_checkpoint=az://oaidsm2/oaistrawberry2/twapi/mini/e/dm-scallion-d16v1-miniseries-mulch1ep-td2e-1-se20x2k4-1ep-lr3.73e-5-ema1e-1-v0401rc1/20876365-12c9-41d5-8bc8-5569c489c7f0/checkpoint/model1/000000000038?ema=True",
                f"{model_path}.layout=finetune-80g",
                f"{model_path}.encoding_name=orion_200k",
            ]
        ),
        numerics.make_fp4_scallion(model_name),
    )

d16_80g = make_d16_80g("policy")
```

`:qstar.presets.scirl:d32_shrub` - this is a composite
preset [defined](https://github.com/openai/openai/blob/5d43408174d961f87c7d866c8071baf6aa782fa9/project/qstar/qstar/presets/scirl.py#L136)
like that:

```python
d32_shrub = berry.preset_utils.compose_presets(
    mathgen.tr_mathd_aops_dedup_v2,
    default_harmony,
    default_hogwild,
    default_batch_schedule,
    default_single_dataset_vardisc,
    default_passrate_filter,
    default_staleness,
    default_peashooter,
    scirl_models.chiveh2,
    # Setting root_config may not be necessary. It's largely in case the twapi
    # driver rpc's timeout (default is 60s). Since we're specifying the timeout
    # also need to specify the config, which mini.root.dev
    berry.preset_utils.args_preset(["root_config=mini.root.dev driver_rpc_timeout=3840"]),
    berry.preset_utils.args_preset(
        [
            "defaults.instance_completer=qstar.instance_completers:VariantsInstanceCompleter",
            "seed=1729",
            "defaults.target_samples_per_instance=64",
            "defaults.instances_per_batch=64",
        ]
    ),
)
```

It defines lots of default parameters, and defines the dataset via `mathgen.tr_mathd_aops_dedup_v2`, which further
expands
to [this](https://github.com/openai/openai/blob/f5366041553132dbdee0afdc320600b11c1ed020/project/qstar/qstar/presets/mathgen.py#L293):

```python
tr_mathd_aops_dedup_v2 = berry.preset_utils.deprecated_preset(
    berry.preset_utils.compose_presets(
        berry.preset_utils.training_dataset_preset(
            [
                "dataset_id=mathgen-rw.x.vineet.mathd-aops-psa.debug.train",
                "grader=qstar.graders.mathgen_grader:MathgenGrader",
            ]
        ),
    ),
    name="mathd_aops_dedup_v2",
    msg="The use of MATH dataset (part of shrub) is prohibited in the big runs. The dataset will be soon deleted from "
    "disk soon.",
    warn_only=True,
)
```

Finding the attribute definition from the corresponding module like what we did above is the typical way to find the
preset from our codebase. However, beware of all sorts of crazy things we might be doing in our codebase (
like [this one](https://github.com/openai/openai/blob/c9a0ba4eace898d7257e91826b729c7eb08d8d92/project/qstar/qstar/presets/o4/datasets.py#L2670)).

<aside>
😓

Our preset system is probably a bit too expressive, and leads to some footguns:
It is sometimes difficult to figure out what a preset resolves to.

</aside>

# 🔦 Use tools to inspect configs

The config tree is complicated and sometimes command line args and presets can expand in confusing ways. We built lots
of tools to help you inspect the content of the configs for qstar experiments.

## 🔧 **`inspect_presets.py`**

When you’re puzzled by some specific presets, you can consider
this [small tool](https://github.com/openai/openai/blob/master/personal/xintao/utils/inspect_presets.py) to help you
inspect / debug one single preset (or multiple presets) (probably needs to run in devbox).

## 🍋 [go/lemon](https://go/lemon)

From go/lemon, you can check the launch command, see how each preset expands to commands, and inspect the entire `chz`
object tree for each dataset config (that includes fields not specified in configs but predefined as defaults). Here is
a screencast of using lemon to inspect configs:

[screen_recording_2025-03-27_at_4.58.13___pm.mov](attachment:0fd680c4-1b23-4068-8b7a-4570c88cd1db:screen_recording_2025-03-27_at_4.58.13___pm.mov)

## 📈 [go/sb-config](https://go/sb-config)

We have a `Config Diff` page in go/sb-metrics where you can visualize the entire `chz` object tree in a flattened
tabular form. This also includes fields not specified in configs but predefined as defaults. You can also compare the
config of one experiment with another experiment to see the differences.

Here is a screenshot of inspecting configs with go/sb-config:

[Screen Recording 2025-04-24 at 8.03.10 AM.mov](attachment:cf3b3ead-2a56-4ec8-8354-801059e73b41:Screen_Recording_2025-04-24_at_8.03.10_AM.mov)

## 👀 `preview_config=True`: Preview config without launching experiment

You can append any launch command **📜** with `preview_config=True`. This way, upon running the launch command, you will
receive a link from @training-bot via Slack that lands you to the go/sb-config page to visualize the config. The
experiment won’t be launched this way but you can inspect the whole config had it launched.

As always, you can inspect every single key of the config in the tabular form of `chz` object tree, and compare it with
an existing experiment.

<aside>
💡

You can preview config from your laptop as well. Just run:

```bash
python -m qstar.preview_qstar_args \
  <args of your experiments>
```

As above, you will receive a Slack message sent by @training-bot with a link that lands you to the go/sb-config page.

</aside>

# 💣 Config footguns

`chz` is not a perfect config system and it has some inherent design flaws. As a result, various footguns exist. Thus as
a general rule of thumb, **it’s always a good idea to
** [🔦 inspect](https://www.notion.so/Berry-Config-101-go-sb-config101-1df8e50b62b080b699abf2e479b0006d?pvs=21) **the
actual config that is being used for every experiment you launched, to double confirm the actual config aligns with what
you intended it to be.**

One big category of config footguns is about various templating solutions introduced to avoid repetition, specifically,
wildcards and `defaults.`

## 🌀 Error-prone wildcards `...`

Wildcards are introduced in `chz` to allow you specify multiple common fields in a single command line. For instance,
this command-line (`...` means `*` in `chz`):

```bash
...inverse_token_cost=256
```

means set all leaf-level fields whose name is `inverse_token_cost` to `256` (note that wildcard match in `chz` works at
field-level, thus `...inverse_token_cost` won’t match fields who have `inverse_token_cost` as suffixes).

There are a few problems with wildcards to make it error-prone:

- Wildcards are order-dependent.
  qstar [parses](https://github.com/openai/openai/blob/b5529caac4020da3c04968a24e792aa396d680cc/lib/chz_presets/chz_presets/core.py#L107)
  command lines in a line-by-line order. Thus configs specified by a single wildcard line can be overridden by a command
  line that appears later (possibly by another wildcard line). This makes it challenging to reason the actual effect of
  a single wildcard line.
- Wildcard matching is weak-typed string-based matching. This means a wildcard can match fewer or more fields than you
  intended (e.g.: by a mis-spelling), and `chz` will be silent about the errors you made (except `chz` will report an
  error if a wildcard line doesn’t match any field).
- (TODO: add more wildcard issues)

## 🧩 `defaults.`

`defaults` field is introduced for the similar purpose of specifying multiple common fields in a single command line.
It’s introduced with the hope to avoid problems with wildcards. However, `defaults` field has its own problems as well.

`defaults` is
a [field](https://github.com/openai/openai/blob/c9a0ba4eace898d7257e91826b729c7eb08d8d92/project/qstar/qstar/args.py#L38)
in qstar `Args` object. Its type [
`Defaults`](https://github.com/openai/openai/blob/c9a0ba4eace898d7257e91826b729c7eb08d8d92/project/qstar/qstar/common/defaults.py#L32)
defines a few common fields for the experiment. These are usually for you to specify values shared by all datasets.
Under the hood, we hiddenly insert wildcard lines for things specified under `defaults.` (expanded from preset [
`qstar.presets.common:preamble`](https://github.com/openai/openai/blob/c9a0ba4eace898d7257e91826b729c7eb08d8d92/project/qstar/qstar/presets/common.py#L5)
which is prepended to the launch command implicitly):

![image.png](attachment:8b298635-4e16-46d1-a37c-a8db786f856c:image.png)

Because `Defaults` is just an ordinary `chz` object, you have the strong-typed checking for all its fields, avoiding the
mis-spelling problems in wildcards. And also because the wildcard lines expanded from the implicit preset
`qstar.presets.common:preamble` are guaranteed to be in the very beginning of the launch command, we have the clarity
and consistency on config parsing order.

However, `defaults` field has its own, often more tricky footguns. One of the issues is well documented
here: [Story of a config bug in a progcomps preset](https://www.notion.so/Story-of-a-config-bug-in-a-progcomps-preset-1c78e50b62b080a1ab50cdf004215c13?pvs=21) .
Specifically, if we specify a deeply nested field under `defaults.` (e.g.:
`defaults.instance_completer.instance_objective.main_objective.scale=2.0`), we would normally expect it applies to the
field for all datasets (unless we override the specific field in the dataset-specific config). However, this is not the
case as long as we have things like (oftentimes this is expanded from a preset rather than directly from the launch
command, which makes the issue more obscure):

```bash
batcher.curriculum.training_datasets.0.dataset.instance_completer=qstar.instance_completers:VariantsInstanceCompleter
```

The reason is the line above overrides the wildcard line from `qstar.presets.common:preamble` for the dataset:

```bash
...instance_completer@=defaults.instance_completer
```

Thus for that dataset, any values from `defaults.instance_completer` are not being used at all!

One mitigation that we can naturally think of is to ban things like:

```python
batcher.curriculum.training_datasets.0.dataset.instance_completer=qstar.instance_completers:VariantsInstanceCompleter
```

in our dataset-specific command lines, especially this line seems redundant as the instance complete in `defaults` is
`VariantsInstanceCompleter` anyway. However, we might not be able to do that due to `chz`'s technical limit: If we want
to specify a field under `VariantsInstanceCompleter` that is not defined in the `InstanceCompleter` (e.g.:
`instance_completer.pass_rate_minimum`), we’re not able to do this:

```python
batcher.curriculum.training_datasets.0.dataset.instance_completer.pass_rate_minimum=0.01
```

unless the command line above is also in place.

Thus, we’re in a dilemma and this is the major flaw of the current `defaults.` mechanism - **there isn’t a good way to
define a template for all datasets and allow each dataset to do fine-grained overrides.** For each dataset, they either
takes the entire `instance_completer` as a whole, or completely specify every single `instance_completer` field on their
own.

<aside>
🧵

The actually situation is even more nuanced, specifically:

```python
python -m qstar.run_experiment \
    name=mathd-scallion_d16 \
    :berry_models.scallion:d16_80g \
    :qstar.presets.mathgen:tr_mathd_v2 \
    batcher.curriculum.training_datasets.0.dataset.instance_completer=qstar.instance_completers:VariantsInstanceCompleter \
    defaults.n_ctx=4096 \
    defaults.inverse_token_cost=256 \
    preview_config=True
```

will propagate `inverse_token_cost` to the dataset, but:

```python
python -m qstar.run_experiment \
    name=mathd-scallion_d16-160-preview3 \
    :berry_models.scallion:d16_80g \
    :qstar.presets.mathgen:tr_mathd_v2 \
    batcher.curriculum.training_datasets.0.dataset.instance_completer=qstar.instance_completers:VariantsInstanceCompleter \
    defaults.n_ctx=4096 \
    defaults.instance_completer.instance_objective.token_cost_objective.inverse_token_cost=256 \
    preview_config=True
```

will not. This is because `defaults.inverse_token_cost=256` is equivalent to `...inverse_token_cost=256` which is
applicable even when we have
`batcher.curriculum.training_datasets.0.dataset.instance_completer=qstar.instance_completers:VariantsInstanceCompleter`.
But `defaults.instance_completer.instance_objective.token_cost_objective.inverse_token_cost=256` does nothing except
sets the field in `defaults.instance_completer`, where
`batcher.curriculum.training_datasets.0.dataset.instance_completer=qstar.instance_completers:VariantsInstanceCompleter`
invalidates `...instance_completer@=defaults.instance_completer` meaning nothing we set in
`defaults.instance_completer`.

**Thus, the whole situation is rather confusing and error-prone, so please do double confirm the config is what you
intended it to be.**

</aside>

# 🎓 Best practices

## 🔦 Inspect, inspect, and inspect, …

**It’s always a good idea to inspect the config, check the config, and double confirm the config is doing what you
intended.** See
the [🔦 inspect](https://www.notion.so/Berry-Config-101-go-sb-config101-1df8e50b62b080b699abf2e479b0006d?pvs=21) section
above.

## 🐳 `config_ci`

`config_ci` allows you to specify the launch command in a `.chz.py` file, and there will be CI tests to ensure the
launch command can always be parsed, and pass the config validation. It’s recommended to put the launch commands of
important runs under `config_ci` to prevent launch commands broken by qstar refactors (at least the launch commands can
be parsed and pass the validation).

For more information, refer to the [README.md file](https://github.com//openai/openai/tree/master/project/config_ci/).

## 🛡️ Config validation

Config validation allows us detect problematic / footgun-ish settings before a run is even started. Launch commands that
fail the validation won’t be started at all (unless launched with `skip_validate_config`, which we highly discourage).
And any PR that breaks the validation will break `config_ci`, thus can’t be merged.

**Please heavily lean on putting more config validations. Anything that doesn’t makes sense, or anything with a
significant possibility to be a footgun needs to be a validation error. An experiment that failed to launch is 100X
better than an experiment launched with unexpected configs, leading to wrong conclusion and/or bad training.**

qstar supports config validation in many forms:

- Validate the raw command
  lines. [Example](https://github.com/openai/openai/blob/b5529caac4020da3c04968a24e792aa396d680cc/project/qstar/qstar/config_validator.py#L91):
  check if a deprecated feature is still being used.
- Validate `chz`
  blueprint. [Example](https://github.com/openai/openai/blob/b5529caac4020da3c04968a24e792aa396d680cc/project/qstar/qstar/config_validator.py#L145):
  check if wildcards are being used in a footgun-ish way.
- Validate the qstar `Args`
  object. [Example](https://github.com/openai/openai/blob/b5529caac4020da3c04968a24e792aa396d680cc/project/qstar/qstar/config_validator.py#L666):
  good validation during the instance objective migration to prevent us from double counting the reinforcement by
  enabling both legacy and new system, or 0 reinforcement where both legacy and new system. This is quite likely to
  happen as presets, launch command and default values in `chz` classes are very likely to not play well with each
  other.
- `chz` allows you to add localized validation for a specific `chz` class. This way the validation can be close to the
  code, and will be enforced whenever the `chz` class is being used inside qstar
  `Args`. [Example](https://github.com/openai/openai/blob/c9a0ba4eace898d7257e91826b729c7eb08d8d92/project/qstar/qstar/objectives/token_cost_objective.py#L25):
  prevent the footgun where reinforcement scale of token cost objective messes up with the intended ITC.

## 📦 Good defaults

We believe a good config is no config at all. Thus please define the default value that makes the most sense so that
most experiments won’t need to specify the value at all. Or, better yet, consider using a constant if your value should
never need to be configured in the first place!

Here are some `chz` features that allow you to define defaults in a flexible way:

📌**You can define a default value in the base class, and then define an alternative default value in its derived class.
** For instance, we can have:

```python
@chz.chz
class Objective(ABC):
    scale: float = chz.field(default=1.0)
```

and

```python
@chz.chz
class LinearObjective(GradeBasedObjective):
    scale: float = chz.field(default=3.0)
```

This means by default using `scale` `3.0` for `LinearObjective` and using `scale` `1.0` for other objectives (assuming
other objective subclasses don’t override the default of `scale`).

📌**As a corollary, you can define a `chz` subclass just for providing a collection of default values,
like [this one](https://github.com/openai/openai/blob/master/project/qstar/qstar/objectives/default_qstar_instance_objective.py):
**

```python
@chz.chz
class DefaultQstarInstanceObjective(InstanceObjective):
    main_objective: Objective = chz.field(
        doc="Main objective to optimize.",
        blueprint_unspecified=BetaObjective,
        default_factory=BetaObjective,
    )

    token_cost_objective: Objective = chz.field(
        doc="Objective used for token cost.",
        blueprint_unspecified=TokenCostObjective,
        default_factory=TokenCostObjective,
    )
```

then command line like `instance_objective=DefaultQstarInstanceObjective` will mean to use all the default values
defined in `DefaultQstarInstanceObjective`.

## :froge-offers-python: `chz` class design

There are special considerations for designing a `chz` class. Specifically, we believe it might not be a good idea to
have collection fields, like `tuple` or `dict`, especially `tuple` to hold other complex `chz` objects. These are the
main issues with collection fields:

📌**`tuple` fields make command lines less readable and less discoverable**

Command lines like this:

```bash
batcher.curriculum.training_datasets.0.dataset.variant_producer.variant_producers.1.reward_cutoff_ratio_per_depth.2=0.375
```

are really hard to reason (what dataset is it about? what variant producer is it about?) and also hard to discover (how
to find all relevant parameters for `VarDiscountingVariantProducer`?).

In go/sb-config, we’re using a
specialized [config parser](https://github.com/openai/openai/blob/master/project/qstar/qstar/config_parser/parser.py) to
hardcode the way to extract the dataset and grader names (assuming these are the most important things we want to
discover), but for lots of other `tuple` fields, discoverability is a big challenge, if not impossible.

📌**`tuple` fields are wildcard-unfriendly**

Similar to the point above, it would be hard or footgun-ish, if not impossible, to target a `chz` object field stored in
a `tuple`. Think about: how to set `initial_max_reward_multiplier` for `VarDiscountingVariantProducer` across all
datasets? Having wildcards like `...initial_max_reward_multiplier=2.0` might heuristically solve the problem, but it
will become a footgun 💣 if other `chz` classes happen to have a field with the same name. In contrast, if our design
didn’t make variant producers stored in a `tuple` field, we could have more specific wildcard like
`...var_discounting_variant_producer.initial_max_reward_multiplier=2.0` to make it vardisc-specific. We can even specify
the fully-qualified path (thus no wildcard at all!).

📌**You can’t set good default values for `tuple` and `dict` field**

This is due to `chz`'s [own limitation](https://openai.slack.com/archives/C05LXG3EE14/p1743788276216869). We’re not able
to provide the default value for a `tuple` or `dict` field if the value is a `chz` object whose fields need to be
further specified by more command lines. As a result, in many cases the only viable default value for a `tuple` or
`dict` field is an empty one, which prevents us to
provide [📦 good defaults](https://www.notion.so/Berry-Config-101-go-sb-config101-1df8e50b62b080b699abf2e479b0006d?pvs=21)
for these fields, forcing users to specify these fields in either command lines or presets, which leads to more
confusion and footguns.

💡**As an alternative, consider to design `chz` class with flattened fields**

The [
`InstanceObjective`](https://github.com/openai/openai/blob/5719b10aacb6c3df345594bbe29a071fbcf08978/lib/berry/berry/instance_objective.py#L66)
class defined
for [instance objective framework](https://www.notion.so/Instance-Objectives-1498e50b62b080599beffb490cbd5b60?pvs=21) is
a good example of this design:

```python
@chz.chz
class InstanceObjective:
    main_objective: Objective = chz.field(
        doc="Main objective (e.g. pass rate).",
        blueprint_unspecified=UnusedObjective,
        default=UnusedObjective(),
    )
    
    token_cost_objective: Objective = chz.field(
        doc="Objective that specifies token costs.",
        blueprint_unspecified=UnusedObjective,
        default=UnusedObjective(),
    )

    confidence_objective: Objective = chz.field(
        doc="Objective that specifies confidence costs.",
        blueprint_unspecified=UnusedObjective,
        default=UnusedObjective(),
    )
    
    # Auxiliary objectives that you want to use in your experiment.
    aux_objective_0: Objective = chz.field(
        doc="Auxiliary objective that you want to use in your experiment (index 0).",
        blueprint_unspecified=UnusedObjective,
        default=UnusedObjective(),
    )
    # You can add additional `aux_objective_<idx>`s as needed.
```

In this design, we just exhaust all the objectives that can be possibly enabled during a run, which can be individually
turned on / off by setting to a concrete subclass or the dummy `UnusedObjective`. We can implement a helper property to
simulate the effect of iterating through a collection field:

```python
    @property
    def _all_objectives(self) -> Iterable[Objective]:
        fields = chz.chz_fields(self)
        for key in fields.keys():
            attr = getattr(self, key)
            if isinstance(attr, Objective) and not isinstance(attr, UnusedObjective):
                yield attr
```

With this design, it will be much easier to provide
a [default template](https://github.com/openai/openai/blob/master/project/qstar/qstar/objectives/default_qstar_instance_objective.py)
for qstar (we can even define more than 1 default templates to allow users to choose from), and allow granular overrides
via follow-up command lines. This is something that can’t be achieved with [wildcards or
`defaults` field](https://www.notion.so/Berry-Config-101-go-sb-config101-1df8e50b62b080b699abf2e479b0006d?pvs=21).

Note that even for fields like `aux_objective_0`, where we’re simulating the effect of a `tuple`, it’s still much better
than using a `tuple` field, because of ease of specifying good defaults and allowing further per-field overrides (though
it suffers the same problem of readability, discoverability and wildcard-friendly as `tuple` field).