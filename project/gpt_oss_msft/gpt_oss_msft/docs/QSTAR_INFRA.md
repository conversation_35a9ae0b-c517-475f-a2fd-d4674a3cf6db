# Q∗ RL Infrastructure Overview: <PERSON><PERSON><PERSON><PERSON>, Qstar, and <PERSON> at OpenAI

The code I work on is mostly in peashooter and qstar.

I am a researcher working on the strawberry (or berry) team at OpenAI. We are using RL to train
large language models.
We use Kubernetes to manage our experiments, but we have some custom tools like brix and rcall.
We use a system called Beam that helps with parallelism.

Main repo: Monorepo (`~/code/openai`)

Important note: Monorepo is a gigantic repo, so try not to search over the entire repo, with lots of
unrelated stuff from OpenAI. Try to stay only within the most relevant directories to not spend too
much context on irrelevant stuff.

The most important directories in `~/code/openai` are:

- `project/qstar` (has the main code)
- `project/peashooter` (has lots of infra code)
- `lib/berry` (contains general abstractions, sometimes used in qstar and peashooter)
- `lib/berry_tw` (contains abstractions for actually training using torchflow, used in qstar)
- `project/deep_swe`: has some experiments and code related to the automated SWE project, where we
  teach the model about software engineering.
- `project/caas_tasks`: has some information about the cluster infra. CaaS is Container as a Service;
  it allows us to run model-generated code in containers. This way the model can run code and learn
  from it when solving programming completion tasks, software engineering tasks, math tasks, or
  even just to answer questions.

- `lib/berry_rfs`: utilities for Repo Function Synthesis tasks; it sets up repositories in CaaS containers
  and runs their tests.

## Peashooter

Here is context on how peashooter works. We care about peashooter under the project/peashooter.
The peashooter system is the piece of infrastructure that orchestrates sampling and training for
Berry experiments.

Pools:

- **Train pool**: the main pool where training happens. It has several `train` pods `train-w0-0`,
  `train-w0-1`, ..., and a single `train-w0` pool (or box). The pods run `InstanceWorker` processes.
  The main pod `train-w0-0` runs the driver process and a `TrainWorker` process.
- **Sampling pool**: the pool where sampling happens. It has several `rollout-worker` pools
  `rollout-worker-w0`, `rollout-worker-w1`, ..., sometimes we might have more than one pod (e.g. 2
  pods) per rollout-worker pool. Each pod runs several `peashooter_rollout` processes and each pool
  runs one `peashooter_engine` (usually at pod `-0`).
- **Controller pool**: the pool that runs the Redis coordination services. It has several pods `controller-w0-0`,
  `controller-w0-1`, ..., and one pool `controller-w0`.

At a high level, the peashooter system consists of several cooperating processes:

- A *driver* process, started by `qstar/run_experiment.py`, which iterates through datapoints and
  batches. This runs in the *-train-w0* box. It uses a `StateRedis` backed by the Controller Redis and a machine-local
  Redis to
  maintain the experiment’s state at pod-0, including staged datapoints, batched samples, and
  training batch metadata.
- A *rollout worker* process, launched in the sampling pools, which pulls work off the sample queue,
  runs the model and any associated tooling to produce "samples" and pushes results back on the main
  queue. This runs in the *-rollout-worker-w[0-N]* pools. There is concurrency and processes here,
  so each rollout worker machine has a number of rollout worker CPU processes and threads.
- An *engine* process, also running inside each rollout worker pool, usually at pod-0, responsible
  for loading weights (via snapshots pulled from the train box), initializing the model, and
  servicing LLM completion requests. There is only one engine per rollout worker (which itself might
  encompass multiple machines to provide enough GPUs). Inference snapshots are distributed to
  rollout workers peer-to-peer from the train pool or fetched from controller storage.
- A *train policy worker*, which polls `StateRedis` for new training batches, updates the model,
  checkpoints state, and updates Redis so the driver can move on. This also runs in the *-train-w0*
  pool and talks to the same machine-local Redis as the driver.

In addition to these processes, Q∗ relies on multiple Redis services for coordination:

- **Controller Redis** stores experiment configuration and run state.
- **Work queue Redis** holds pending sample requests. The queue is step-gated and automatically
  requeues lost or stalled work.
- **Telemetry Redis** collects runtime metrics from all workers.
- **Buffer store** uses a remote tensor service backed by Beam Object Store to persist large sample
  batches. It is shared between the train pool and rollout workers.
- **Peer sample Redis** caches samples shared across datapoints for pairwise grading so that
  distributed workers can compare completions.
- **Engine registry** advertises active engines and their loaded snapshots.

Here is how these pieces fit together.

### Experiment startup

`run_experiment.py` parses CLI arguments and calls
`peashooter.experiment.experiment_runner.run_experiment_on_current_cluster`. This sets up
logging, preloads datapoints, inserts the experiment configuration into Controller Redis, then constructs
both the *state Redis* wrapper and the *sample queue* before launching the driver:

```python
store = get_peashooter_store(experiment_name=experiment_name)
ctrl_redis = ctrl().redis
state_redis = StateRedis(experiment_name, ctrl_redis)
curriculum = args.berry_curriculum.bind(ctrl_redis, experiment_name, launch_id)

# ...

driver = ExperimentDriver(
    experiment_name=experiment_name,
    all_datapoints=all_datapoints,
    curriculum=curriculum,
    batch_completer=args.batch_completer,
    state_redis=state_redis,
    store=store,
    instance_workers=instance_workers,
    # ...
)
driver.start()
```

This driver object encapsulates the plumbed state Redis connection, a batch completer, and the
worker pools.

Besides the long‑running rollout workers on the separate sampling pools, the driver also
manages
a pool of Beam actors (`InstanceWorkers`) running on the training pool. These per‑pod train
workers handle the CPU‑side logic of enqueuing sample requests, grouping samples into
instances,
and assembling finished instances into training batches before handing them off to the train
worker. If an instance worker pod is evicted or crashes, the Beam RPCs used to call
`get_instance_summaries` will fail and propagate back through the batch completer.

### Batch iteration

`experiment_runner.run_experiment` loops over training steps. For each
`batch_step` it does:

- `driver.get_next_batch()` asks the `BatchCompleter` to stage the next set of datapoints via
  the `PeashooterBatchCompleterCtx`. `PeashooterBatchCompleterCtx.enqueue_next_instance` uses
  `state_redis.stage_next_instance` to claim a datapoint, chooses which `InstanceWorker` to use
  and kicks off an `_instance_completer_main` thread on the worker via
  `InstanceWorkers.create_instance`.
- Each `InstanceWorker` runs `_instance_completer_main_inner`. It builds a
  `PeashooterInstanceCompleterCtx` around a `MangoSampleQueue`. It calls into the dataset’s
  instance completer, which invokes `InstanceCompleterCtx.enqueue_samples`. This queues
  `berry.Sample` objects into the `MangoSampleQueue`.

### Datapoint pipeline (variants flow)

Berry and Peashooter cooperate to take a raw task row through sampling and into a training batch.
When running with the variants-based instance completer, the main components are:

- **Dataset configuration & indexing** – Each dataset’s `DatasetConfig` points at its JSONL
  files, includes any `datapoint_converters` and graders, and yields an `IdIndexedDatapoints` so we
  can look up `(dataset_id, idx_in_dataset)` quickly and persistently across reloads.
- **Datapoint converters** – `datapoint_converters` on a dataset convert raw JSON rows into the
  other raw JSON rows (e.g. parsing conversations, adding prompts) before any variant or sampling
  logic runs.
- **Curriculum** – At startup we collect all `(dataset_id, idx_in_dataset)` pairs from the
  index
  and feed them into a configured curriculum that manages order, retry/skip and epoch metadata.
- **Instance completer** – For each datapoint ID popped from the curriculum, the dataset’s
  `InstanceCompleter` is invoked. With `VariantsInstanceCompleter` this explodes a single `Datapoint`
  into one or more variants via `berry.variant_utils.get_variants`, giving each variant its own UID.
- **Variant producers** – A dataset may configure a `VariantProducer` chain to generate and
  optionally subsample variant groups (e.g. rephrase, tool variants) prior to sampling, influencing
  how many variants the instance completer sees.
- **Pass rate estimation** – For a group of variants, queue an initial set of samples across
  them
  to estimate pass rates, drop too‑easy or too‑hard variants and record per‑variant metrics.
- **Main sampling round** – For active variants, enqueue more samples to hit the target samples
  per instance, weighted by initial information content. Sample requests go out via `MangoSampleQueue`
  and return `VirtualSample`s carrying a `variant_id`.
- **Batch assembly** – Each `InstanceWorker` sends sample requests and collects results independently. The
  `BatchCompleter` merges the `InstanceSamples` that finish first into a training `Batch` and removes those instances
  from its tracking set. Slower instances keep running in the background, so sampling and training proceed hog‑wild
  without blocking on any one datapoint.

### Enqueuing to the sample queue in chunks

`PeashooterInstanceCompleterCtx.enqueue_samples` groups samples by datapoint and slices them into
chunks of size `sample_queue_granularity`:

```python
    for _, samples_by_dp in itertools.groupby(new_samples, key=lambda s: s.gt_datapoint.uid):
        for chunk in chunked(samples_by_dp, self._sample_queue_granularity):
            request_id, _ = self._sample_queue.mango__enqueue_samples(
                samples=chunk,
                base_priority=self._staged_instance.base_priority,
                step=self._staged_instance.min_optim_step_to_sample_from,
            )
```

`MangoSampleQueue.mango__enqueue_samples` creates a `SampleRequestBatch`, derives a unique
request id, and feeds it to its `SampleQueue._submit_samples_async`. That class handles
serialization, pushes a request record into Redis (organized as a sorted set and hash for
submission, plus a result list) and starts a reader thread to monitor completions.

### Rollout worker consumption and processing

On the sampling pools,
`peashooter/sampling/rollout_worker_entrypoint.py` brings up an engine process and launches
`qstar.peashooter.sampling.rollout_main`, which loops over pending requests in the `SampleQueue`.
A background thread (`QueueProducer._read_samples_worker`) in the queue producer also pulls
requests from a Redis list as they become available. The worker code parses each
`PeashooterSampleRequest`, uses the loaded model(s) and tool pool to generate completions and
grades through `sample_completer.SampleExecutionContext`, and builds a
`PeashooterSampleResultBatch`. It serializes this and stores it in Redis, then pushes the request
id onto the result list.

### Receiving results and completion

Back in the driver process, the `MangoSampleQueue`'s reader
threads see the request id in the result list, fetch the serialized bytes from Redis, deserialize
them back into `(VirtualSample, metrics)` tuples and complete the corresponding Python `Future`
(`_populate_request_future` in `_queue.py`). The `PeashooterInstanceCompleterCtx` attached a
callback to each request future that unpacks each `VirtualSample` and updates metrics. When all
samples for the instance have arrived, `_instance_completer_main_inner` returns a
`berry.InstanceExamples` object and collected metrics.

The driver’s `instance_workers.use_instances_for_batch` waits for each
`InstanceWorker.use_instance_for_batch` future to complete, then extracts `variant_examples` and
aggregates metrics for all examples in the batch.
Internally these `InstanceWorker` objects are implemented as Beam actors running on the rollout
cluster. `instance_workers.get_instance_summaries()` uses `beam.api.rpcs` to broadcast a call into
each worker to check on progress. If a rollout pod dies or loses network connectivity, this RPC will
raise (e.g. “transport error – connection reset”), causing the batch completer thread to
terminate
and propagating up through `driver.get_next_batch()`. The driver process will then log the exception
and abort the run.

### Writing the training batch

With the collected `variant_examples`, the driver builds a
`SuspendedTrainStep` (a `berry_tw.optimizer` wrapper that includes the examples, per–sample
metadata and a random seed) and writes the training batch out to Redis:
```
driver.state_redis.incr_training_batch(
    batch_step=batch_step,
    batch_step_timestamp=time.time(),
    batch=batch,
    training_batch=TrainingBatch(suspended_train_step=suspended_train_step, …),
    buffer_store_tags=buffer_store_tags,
    birder_metrics=metrics.serialize_state(),
    curriculum=driver.curriculum,
    store=PeashooterBufferStore(driver.experiment_name),
)
```
Because the training batch can be very large, `StateRedis.incr_training_batch` actually writes
the full examples out to blob storage and only sets small pointers (keys/tags) and metrics in
Redis. It then increments the `next_batch_step` counter. This write goes to the Controller Redis cluster.

Independently, a training worker running `peashooter/train_worker/main.py` connects
to the same `StateRedis` on the Controller Redis cluster. It polls `get_next_batch_step`, then
calls `get_training_batch(step)` to block until the batch appears. It uses the blobstore pointers
in Redis to retrieve the virtualized training data, deserializes it into the `TrainingBatch`
(which wraps the `SuspendedTrainStep` and flags), and passes it to its model.

The `SuspendedTrainStep` carries a reference to a `berry_tw.optimizer.BerryOptimizer` instance
and the `variant_examples` to train on. The optimizer’s `learn` and `learn_sharded` methods
flatten and reorder the examples, compute per–variant weights and global hyperparameters (KL
penalty, Adam betas, etc.), and pack them into `VirtualTwBatch` contexts sized to the Tw
model’s context length. It then loops over substeps (for “superstep” training across many replicas)
and across sharded models, constructing for each a `TrainStepRpcV2` via
`berry_tw.optimizer.train_step_rpc_builder.build_train_step_rpc`. That builder wraps the virtual
batch, a constructed loss function, the optimizer hparams and seed into a `TrainStepRpcV2` RPC
object that the Tw training worker can execute to perform a weight update on the model.

After performing the train RPC(s) and checkpointing, the train worker calls
`state_redis.set_train_worker_state` to bump the `last_checkpointed_optim_step` on machine
Redis. The experiment runner loop in the driver watches this field to know when training has
caught up to the current batch.

In this way, “enqueuing a batch” in the driver fans out into chunks of sampling requests pushed
to
the Redis sample queue, progressed to completion by rollout workers, and reassembled into a training
batch for consumption by the train worker. This dance is monitored and synchronized via Redis keys
so that driver, workers and sampler can recover from crashes and cooperate correctly.

#### Sample preprocessors

After grading, the per‑variant `SamplePreprocessor` attached to the optimizer gets a chance to
modify each `VirtualSampleWithGrade` before it is turned into training tensors. Sample preprocessors
like `TokenCostPreprocessor` or `ConfidencePreprocessor` can add auxiliary losses/reinforcements
based on token counts or confidence channels. They run over the batch of variant samples just before
`VariantExamples` are built for training.

This mechanism is **no longer the preferred way** to add token cost. Instead, configure
`instance_objective.token_cost_objective=TokenCostObjective` and leave `sample_preprocessor` unset.
`TokenCostObjective` computes the cost, logs a scalar objective, and supplies the per-token
reinforcement in one place. The config-validator forbids enabling **both** `TokenCostObjective` and
`TokenCostPreprocessor` for the same dataset; keep the pre-processor only for legacy setups.

#### InstanceObjective

- **Purpose:** Specifies the objective to optimize (e.g., pass rate, token cost).
- **Input:** All sampled trajectories for an instance.
- **Output:** Reinforcement (weight for cloning each trajectory).
- **Implementation:**
    - `get_reward` function returns a scalar reward per rollout.
    - `compute_reinforcements` aggregates rewards into reinforcements (can be average, beta, percentile filtering,
      etc.).
    - Objectives can be combined with linear scales using an `InstanceObjective` container.
- **Observability:** Reports objective value estimates for monitoring run health.

#### SampleSelector

- **Purpose:** Subsamples trajectories for training to minimize variance, given a training budget.
- **Variance Reduction:**
    - Training is more expensive than sampling; may not train on all samples.
    - Shifts reinforcements by a constant (does not affect expected gradient).
    - Uses importance weighting (if kept with probability \( p \), upweight by \( 1/p \)).
- **Implementation:**
    - Input: \( n \) trajectories with reinforcements, target train SPI.
    - Output: Expected \( m \) trajectories to train on, reinforcement shift.
    - **JALOC:** Solves a joint convex optimization to minimize variance over selection probabilities and baseline
      constant. `JallocSampleSelector` only keeps the probabilities from `solve_cq`;
      `SampleSelector.select_instance_rollouts` recomputes a fresh baseline using `_compute_baseline` before applying
      it.

#### SampleAllocator

- **Purpose:** Allocates compute between instances—decides how much to sample and how many samples to train on.
- **Implementation:**
    - Input: Trajectories, reinforcements, target SPI (now an abstract compute scale parameter).
    - Called twice: once for sampling, once for training.
    - **UniversalSampleAllocator:** Simulates allocations to minimize variance, factoring in train-to-sample cost ratio.
      **This allocator requires per-sample reinforcements to be provided; if they are missing, it will error.**
    - **AdaptiveSampleAllocator:** In contrast, this allocator ignores the reinforcement values entirely and operates
      only on pass-rate statistics (e.g., fraction of samples passing a threshold). Reinforcement values passed to it
      are ignored.

  Thus, whether per-sample reinforcements influence the allocation decision depends on which allocator is configured:
  UniversalSampleAllocator uses them, while AdaptiveSampleAllocator and similar allocators do not.

#### Step-by-Step

- Initial sampling and reinforcement calculation.
- Call sample allocator to determine additional samples needed.
- Run more rollouts as needed.
- Call sample allocator again to determine number of samples to train on.
- Use sample selector to pick which samples to train on and baseline reinforcements.
- Send selected samples and reinforcements as a training batch.

#### Nuances

- **Variance Producer:** Generates variants (e.g., different tools, rephrasings) per problem.
- **Instance Definition:** "Instance" may refer to dataset problem or optimization variant.
- **Pass Rate Filtering:** Filter out too easy/hard problems (default thresholds: 5% and 95% pass rate, overridable per
  dataset).
- **Per-Token Reinforcement:** Uniformly applied, with exceptions (e.g., prefill tokens get zero reinforcement).
- **Async RL (Hog-wild):** Training batches sent as soon as ready; batch starts when enough data is collected.
- **Error Handling:** Model errors treated as incorrect; system errors treated as non-existent (rewarded with baseline).

#### Jalloc

Jalloc is a sample selection algorithm that chooses a constant baseline `c` and keep probabilities
`q` to minimize gradient variance. The solver's `solve_cq` function returns both values, but
`JallocSampleSelector` only uses the keep probabilities. After selection,
`SampleSelector.select_instance_rollouts` recomputes a baseline using `_compute_baseline` and applies
it to the kept samples. Thus the baseline from `solve_cq` is discarded and recalculated later.

### Autoregressive loss, reinforcements and Berry optimizer

The core of Q∗’s RL training loop lives in `lib/berry_tw/optimizer/BerryOptimizer`. When the
peashooter driver assembles a batch, `BerryOptimizer` flattens multi‑turn conversations into
individual `VirtualExample`s carrying:

* model log‑probabilities (`new_logp`) for each target token,
* an `on_policy_mask` distinguishing model‑generated tokens from prefill/prompt/tool tokens,
* a per‑token reinforcement tensor encoding rewards and shaping terms.

Each `VirtualSampleWithReinforcement` produced by a sampler/grader combines several signals into
this tensor:

* **Primary reward**: a scalar per‑sequence reward from the grader (`linear_reward`/`log_reward`)
  that is broadcast across the sampled tokens.
* **Auxiliary scalars**: `aux_reinforcements` apply shaping terms uniformly;
  `aux_on_policy_reinforcements` apply only to tokens flagged by `on_policy_mask`.
* **Per‑token costs**: `token_reinforcements` hold explicit per‑position adjustments inserted by
  sample preprocessors like `TokenCostPreprocessor` or `ConfidencePreprocessor`.

In `berry_tw/loss.unified_loss` the negative log‑probabilities are multiplied by this
reinforcement
tensor to implement a REINFORCE‑style weighted cross‑entropy. Additional loss terms for KL
divergence, entropy regularization and energy‑based heads can be attached and are scaled
per‑example
by an `AuxLossScaler` (computed from sequence length and sample count) to normalize their magnitude.
Finally, in `BerryOptimizer` the sum of all components is multiplied by a hard‑coded
`_GLOBAL_REINFORCEMENT_SCALE` (100.0) before backpropagation. This global factor, also logged to
metrics as `optimizer/global_reinforcement_scale`, ensures that small reward values still lead to
gradient updates with a reasonable scale.

The `alternative_loss` term is computed within `unified_loss` in `lib/berry_tw/loss.py`. This term
implements Jakub's Policy Correction (JPC), which is distinct from LVEM or confidence-based signals.
JPC operates by considering a set of alternative tokens at each timestep, calculating an importance
ratio between the current policy's log probabilities and those of the original sampling policy. It
then averages these weighted log probabilities (`\log \pi_\theta`) across the alternatives,
resulting in a lower-variance policy-gradient update.

Additionally, `unified_loss` includes a separate KL divergence penalty term, defined as
`\alpha_{\mathrm{KL}}(\log \pi_{\mathrm{API}} - \log \pi_\theta)`. This penalty can optionally be
computed over the same set of alternative tokens to measure and control divergence from the sampling
policy.

Together, the primary policy-gradient loss on the sampled tokens, the JPC alternative loss, and the
KL divergence penalty form the complete RL loss used during training.

Per-sample reinforcement is computed via an `InstanceObjective`, which bundles
one or more `Objective` subclasses (such as `BetaObjective`, `TokenCostObjective`). Most
experiments simply use the `DefaultQstarInstanceObjective`, which wires together the
standard token cost and beta objectives under one umbrella.
`InstanceObjective.evaluate()` returns both the scalar objective value and the per-sample
reinforcements; any field left as `berry:UnusedObjective` is ignored.

### How the sample queue handles heartbeats and requeues

The `SampleQueue` logic in `peashooter/queue/_sample_queue.py` builds atop a generic Redis queue
implementation (`_queue.py`) that tracks the state of each sampling request via several Redis keys
(sorted sets and hashes). Rollout workers call `get_work` to pop the next item from the submit queue
(a ZSET keyed by step/priority), and then periodically send heartbeats to the queue with
`refresh_pending`. This updates a sorted set of “in–progress” requests with current
timestamps. If a
worker crashes or hangs and fails to heartbeat, the background reader loop in
`QueueProducer._read_samples_worker` (called on the driver side, plus
`MangoSampleQueue._read_samples_work` in our wrapper) will detect lost or stalled requests using
`TimeoutConfig.lost_datapoint` and `TimeoutConfig.stalled_datapoint`. If an in–progress
request’s
timestamp is too old (lost) or the processing never started (stalled), the queue uses a Lua script
(`scripts/requeue_lost.lua`) to remove the entry from the in–progress/start sets and re–insert
the
request id back into its submit queue with its original priority. This allows a different worker to
pick up and process the request. Mango’s `_maybe_populate_futures` also handles duplicate results
arriving for a request that may have been requeued.
In addition to requeuing, there are methods to “drop” requests that have not yet been taken by a
worker. `MangoSampleQueue.mango__drop_requests` removes given request ids from the submit queue and
resets control set membership. This is used in eval scripts (e.g. `show_incomplete_requests.py`) to
skip staging requests that have sat in the queue too long. Both producers and consumers should call
`release`/`close` appropriately to clean up their local future maps and avoid leaks.

### Sampling logic

The `Sampler.sample_batch` implementation in `qstar/samplers/sampler.py` is where formatted prompts
are constructed and passed to the engine. Rollout workers normally call it with a single datapoint
per invocation. When a backend failure occurs during sampling (for example, the engine returns a
"sampled -1" error), `_sample_from_model` catches the `ValueError`, logs a warning, and populates
`errors_blamed_on_system` with `CommonErrors.SAMPLING_ERROR`. This prevents the exception from
killing the rollout worker.

A `SampleCompleter` under `qstar/sample_completers` handles assembling multi-turn conversations. The
basic pattern looks like:

```python
while get_next_action(sample) in ["sample", "tool"]:
    if get_next_action(sample) == "sample":
        sample = context.extend_sample_local(sample)
    else:
        sample = context.run_tool(sample)
```

The helper `get_next_action` returns "process_invalid" as soon as `errors_blamed_on_model` or
`errors_blamed_on_system` are set, so any error raised or added by the sampler causes the completer
loop to bail out early. Higher-level code then grades the invalid sample or drops it without letting
the exception propagate.

### ml_config and sampling_ml_config

ml_config is a space‑separated string of flags passed to the model to tweak its behavior at load
time. Training and sampling runs attach different flags: training ml_config strings include things
like enable_rollbackv2=True; sampling strings disable optimizers, set twppo.sampling_only, turn off
certain slow features, and ensure the checkpoint directory points somewhere valid. In the code you
can see mini/internal/auto_sampling_config/make_config.py building up a standard sampling
configuration.

sampling_ml_config is a secondary override layer used when sampling from a trained model; it's
appended to or replaces parts of the base ml_config for inference‑only runs. For example, scripts
may set policy.sampling_ml_config='ixf_use_cuda_graph=True ixf_use_fp8_kv_cache=False' to further
tweak performance without touching the training config.

### Graders

Sampling isn't enough; for RL (or RLHF) training we need to assign a reward to each sampled
completion. Q∗'s grading infrastructure under `qstar/graders` implements a variety of evaluators
for
different domains:

- **BerryGrader**: our generalized autoregressive
  grader that builds a prompt (single–answer or multi–answer) and asks another model to grade
  the
  sampled answer(s). Many of our "RLHF" tasks like TBv3 utility use a `BerryGrader` subclass with
  `add_peer_samples=True` to produce pairwise utility scores. BerryGrader delegates prompt building
  to `OptionalSideInfoPromptConstructor`, which falls back to single–answer prompting if no peer
  samples are available.
- **String–matching graders**: e.g. `contains_answer_str_grader` and `fixed_answer_grader` perform
  exact or normalized string comparisons against the ground truth answer.
- **Multiple–choice graders**: `mc_grader` handles selection tasks, including computing cross
  entropy losses and match rewards.
- **Progcomp/CaaS graders**: under `progcomp_caas`, graders for code generation tasks can spin up
  sandboxes, compile and execute model code against hidden testcases to award correctness.
- **Repo Function Synthesis (RFS) graders**: the repo/file system graders in
  `qstar/graders/repo_graders_v*` evaluate diff‑style tasks using specialized code analysis.
- **Tool–using and multi–stage graders**: `tool_using_grader`, `multi_stage_grader` and others
  can
  grade sequences where the model calls tools, or combine multiple simpler graders in stages.

Some datasets wire in a specific grader via presets; e.g. TBv3 long answers uses a MultiStageGrader
combining a TBv3BerryUtilityGrader with style and hack detection.

### Presets

Q∗ experiments typically use presets—predefined bundles of command-line arguments that set up
common
model configurations and dataset combinations. These presets simplify run scripts by eliminating the
need to manually specify long lists of flags.

Presets reside in `project/qstar/qstar/presets/` and are built on the `lib/chz_presets` library. In
essence, a preset is a function that takes a preset name and a `chz.Blueprint` and then adjusts the
blueprint by appending or overriding configuration options.

Key utilities in `qstar.presets.preset_utils` include:
•`args_preset([...])`: Returns a preset that adds a list of `section.option=value` strings.
•`compose_presets(p1, p2, …)`: Chains multiple presets into one.
•`training_dataset_preset([...])` and `eval_dataset_preset([...])`: Append a dataset to the
training or evaluation configuration.
•`append_ml_config("flags")` / `append_sampling_ml_config(...)`: Append tokens to
`policy.ml_config` or `policy.sampling_ml_config`.

On the command line, presets are indicated by a leading colon. For example:

```bash
python -m qstar.run_experiment \
    tbv3-long-answers-utility \
    :qstar.presets.scallion:d32_80g \
    :qstar.presets.numerics:fp4_scallion \
    :qstar.presets.tbv3:formalgen_training
```

This command tells Q∗ to use the `d32_80g` scallion snapshot, the `fp4_scallion` precision
settings, and the `formalgen_training` dataset. Presets can also be parameterized (e.g.,
`:my_precision(n_units=4)`), and using fully qualified names like `:qstar.presets.numerics:fp4`
removes any ambiguity.

Internally, `chz_presets.parse_args` scans the command-line arguments for entries starting with a
colon, retrieves the corresponding preset function, and applies it to modify the `Blueprint`
configuration. The updated blueprint is then converted back into argument form and passed to the
experiment’s entrypoint. This mechanism promotes reusability and helps keep run scripts concise.

## Other useful concepts

We also have other projects like brix, rcall, beam, and most importantly, the code for training and
sampling from models. I'm not very familiar with the details of torchflow and IXF (used via engine
v2 or v3), but I know they're controlled by flags in ml_config or sampling_ml_confign. We often
cargo-cult these flags; I'll mention explicitly if they're important for a specific task.

### External services

Within Q∗, tool‑using datasets interact with CaaS via `qstar/common/tools/caas_container`.
There’s a `CaasContainerResourceConfig` type that uses the `CaasContainer` class from
`lib/harmony_tools/caas_tool` to allocate or resume containers. When a rollout worker constructs a
`HarmonyToolbox` for a datapoint that uses CaaS, it starts a background heartbeat loop that
periodically calls `CaasContainer.send_heartbeat()`. That method issues a
`TerminalSession.keepalive()` to the remote CaaS server; running this every 60 s prevents the
container from being GC’d while other datapoints are processed.
Besides CaaS, the RL stack interacts with several other services:

- Search API/browse lets tasks retrieve web content during sampling.
- Graders run remotely to score completions for reward signals.
- Bus-based messaging connects rollout workers and engines when direct network access is blocked.
- Telemetry dashboards help track stuck or requeued requests so we can debug issues.

We work with the CaaS, Search, and infrastructure teams to keep these integrations smooth.

### torchflow

Torchflow is OpenAI's distributed training stack built on PyTorch. It splits large transformer
models across multiple GPUs, schedules microbatches through pipelines, shards weights or
Mixture-of-Experts layers, and efficiently manages forward and backward passes.

Torchflow abstracts distributed execution (previously using Ray, now Beam), allowing multi-GPU
training without manual process management. Users define a `FalconModelConfig` and `RunConfig`,
then use a `ModelContext` to specify local and global layouts (`LocalLayout`/`GlobalLayout`) and
apply transforms for pipelining, operation sharding, expert sharding, and data parallelism.
Internally, the `SyncEngine` manages microbatch slicing, pipeline streaming, recomputation, and
overlapping communication. The `parameter.py` module handles tensor sharding (`ParameterConfig`,
`ShardedParameter`), gradient buffer allocation, and non-default data types (FP8, FP4).

Torchflow checkpoints ("snapshots") are stored in Azure blob storage (e.g.,
`az://…/torchflow-v1/scallion/...`). A minimal version of torchflow for unit tests and scripts is
available in `mini` and `mini/lib`.

Distributed execution in torchflow relies on **actors**, Python processes launched via Beam, each
owning a model shard. The scheduler (`torchflow/trainflow/scheduler.py`) creates actors for each
pipeline stage, replica, and shard. Actors expose a simple RPC API (`init`, `forward`, `backward`,
`save_checkpoint`) and are coordinated by a leader actor that manages microbatch streaming. If an
actor fails (e.g., due to OOM), the scheduler logs a `FailedActors` error and either restarts the
actor or aborts training, depending on the `recover_from_worker_failure` setting.

### Engine

All model sampling in Q∗ uses an *engine*: a live process hosting a trained model and providing
an API for generating completions. Key engine components include:

* **Engine v2**: The second-generation Python engine server run out of Twapi and can be configured
  to use “IXF”. Rollout workers launch an engine process per sampling pod (see
  `peashooter.sampling.rollout_worker_entrypoint`), loading a torchflow snapshot based on the
  provided `ml_config` and serving sampling requests via HTTP. Newer code uses an `EngineInterface`
  abstraction to interact with local or remote engines. Because it sits on top of Twapi and Redis,
  throughput can be limited by CPU and Redis overhead.

* **Engine v3**:
  Engine v3 lives under `project/platform_inference/platform_enginev3`. It’s a next‑generation
  inference server and load balancer (pipereplica and friends) using gRPC rather than HTTP. EV3 is
  built to remove the Python and Redis bottlenecks in EV2, and can unify the inference and training
  code paths. Under the hood, EV3 only supports the “chicken” and “zen” model stacks—IXF
  isn’t supported. The client lives in `platform_enginev3/client`, or at a higher level under
  `lib/completer/ev3_token_completer`. It could use the same kernels as training, but we often use
  lower precision for sampling. EV3 itself is in Rust.

* **TWAPI** (Torchflow Web API): A server typically running on port 7000 within the pod, using
  Redis queues to handle model queries.

### Bus

Bus is a small library that turns Redis into a cross-cluster message queue so
clients and engines can communicate without direct network access. Requests are
enqueued to Redis and engines pull them asynchronously, enabling optional rate
limiting as well as basic load balancing and fairness.

### Layout

Layouts define how models are partitioned across GPUs, specifying operation shards (`n_op_shards`),
expert shards (`n_expert_shards`), pipeline depth, and replica count. Named layout presets (e.g.,
`scallion_layouts`) in `layout_store.methods` determine GPU requirements. The
`PeashooterChunkLayout` class computes training and sampling layouts from a snapshot's
`model_config`, calculates pipeline depth and replicas, and manages model chunking for
saving/loading.

### chz

It's a small but powerful library for declaring immutable configuration objects. Annotating a class
with `@chz.chz` turns it into a dataclass–like type whose fields are keyword–only,
type–checked, and immutable by default. `chz` additionally provides a `Blueprint` abstraction
that can parse command–line arguments and supply partial configurations; utilities like
`chz_presets` build on this to support reusable `:preset` flags in CLI scripts. Many of the run
scripts end with `.chz.py` and use these facilities to assemble experiment configs from a
combination of base classes and presets.

### IXF and Chicken

In many parts of the codebase you'll see references to **IXF** and **Chicken** as alternative
inference paths for sampling/evaluation.

*IXF* (Inference Transformers)**: An optimized inference mode in torchflow for Falcon checkpoints.
IXF significantly improves sampling throughput by using specialized kernels for attention and KV
cache management. It is enabled via `policy.ml_config` flags such as `ixf_use_cuda_graph` and
`ixf_implementation`. Common configuration options include:

- `ixf_use_cuda_graph`
- `ixf_max_batch_size`
- `ixf_max_logprobs_per_batch`
- `ixf_implementation`: can be `"ixf"`, `"dust"`, or `"chicken_inference"`

Presets like `scallion` typically enable IXF with additional flags in `policy.sampling_ml_config`,
e.g., `ixf_use_ctx_sharding=8`, `ixf_use_dust_bulk_kv_layer=True`. Environment variables
(`OPENAI_FEATURE_IXF_*`) further control low-level kernel selection and KV cache quantization
behaviors.

*Chicken* is an alternative to Falcon in torchflow that could reuse the training forward pass for
inference. It is implemented in the parallel `torchflow/chicken` module, which defines
`ChickenModelConfig`, `ChickenRunConfig`, and optimized inference blocks mirroring Falcon. When
enabled (`use_chicken=True`) and provided with a `chicken_path` (e.g., `"chicken.orion"`),
torchflow's `falcon.transformer` switches to using `chicken.AttentionMLPBlock`. Chicken mode could
ensure bitwise identical outputs between training and sampling, and is required by Engine v3. To
activate Chicken mode in presets, set `use_chicken=True` and specify the appropriate `chicken_path`
in `policy.ml_config`.

### Gradient accumulation in Torchflow

Torchflow has two gradient‑accumulation modes and they are mutually exclusive:

• Microbatch Grouping → within a single step. Enable by setting `max_n_microbatches_per_group`
(requires fetch‑params‑inline). Multiple microbatches are run before `opt.step()`.

• Super Step → across steps (a‑k‑a “sub‑step”). Enable with `use_sub_step=True` + a
`SuperStepConfig`. A logical step is split into N sub‑steps; `opt.step()` happens only on the
final one. This lets Peashooter start training as soon as the first fraction of the batch is ready,
reduces model staleness, and allows mid‑step recovery (restart from the failed sub‑step instead
of rerunning the entire batch).

Rule: if `use_sub_step=True` then microbatch grouping must be off. Torchflow enforces this with an
assertion, and the helper `auto_infer_super_step_config` will automatically flip you to Super Step
and clear `max_n_microbatches_per_group` if you set both.

## GraderService and TokenCompleter

The autograding infrastructure under `qstar.graders.taskgen_utils` introduces a `GraderService`
abstraction for any model-backed service used to grade samples. A `GraderService` subclass like
`TokenCompleterGraderService` holds a `TokenCompleter.Config` (e.g., a REST token completer) and
uses it to sample from a model when constructing grading prompts. Internally, `GraderService` uses
a keyed singleton pattern so the same backend service is reused across graders that share
configuration, and can sync its config to Redis for remote workers. The underlying
`token_completer` package provides a base `TokenCompleter` interface that hides whether you're
talking to a local engine, Bus or REST API and exposes a uniform set of completion parameters like
`max_tokens`, stop sequences and special token allowlists.

### Single Problem Optimization (SPO)

SPO is an approach where we try to squeeze extra performance for a *single* task or
datapoint. Several research side projects (AtCoder Heuristics, NanoGPT,
Chipberry hardware synthesis, some CUDA kernel searches, and others) implement
benchmarks that run many rounds of sampling on a single problem and only keep
the best sample for scoring. The common "SPO benchmark" measures how much the
model can improve a score by repeatedly proposing better answers.

To support this we store `prev_score` on each `gt_datapoint` or sample's
metadata. `PrevScoreGraderWrapper` is a small wrapper around a real grader that
compares the new score against `prev_score` to decide if an attempt improved the
state of that problem. In some sweeps it is extremely important to carry forward
these scores, so we make sure `prev_score` is propagated anywhere the sample or
datapoint gets serialized.

SPO exploration often relies on a ``StrategiesGrader`` (under
`qstar.graders.strategies_grader`) to classify an answer into a small set of
predefined strategies. Coupled with `StrategiesAppendedVariantProducer` or other
variant producer chains, this lets us hint or force the model to try different
approaches when looking for a better score. These variant producers are also
used outside of SPO, but they showed up first in these small optimization
projects.

Some projects define custom `InstanceObjective`s or objectives such as a
`PercentileFilterObjective` to only keep the top percentile of samples or adjust
pass thresholds. Others have dataset-specific instance completers, e.g. the
`AtcoderHeuristicInstanceCompleter` which generates parent/child prompts to keep
improving an AtCoder solution. All these pieces are fairly ad hoc and are not
guaranteed to work in every domain; they are just the current experimental tools
people try out.

### Lite Patch Encoder (LPE)

LPE is a lightweight convolutional encoder for vision (and audio) inputs. Its
structure is configured by `LitePatchEncoderConfig` in
`torchflow/falcon/model_config.py`. A typical configuration consists of ten
`(kernel, stride, multiplier)` tuples and an `input_resolution` of `(32, 32)`,
using `image_encoder_type="lite_patch_encoder_v3"`. Each `LPEBlock` stacks a
`Conv2d` layer, `LayerNorm` and an activation function.

Images are tokenized into `32×32` patches with `ImagePatchTokenizer`. The
tokenizer resizes images to fit the patch budget and yields patch tokens plus a
sentinel token (`200053`) handled specially by the RKLD utilities. Training
configs like `scallion_lpe.py` set `vision_config=LitePatchEncoderConfig(...)`
to swap heavier CLIP encoders for LPE. This provides a lightweight alternative
for embedding vision inputs before RL training.

### Beam

Beam is OpenAI's internal actor system. Rather than running Python multiprocessing
or Ray, we spin up Beam actors for most distributed tasks. A small helper package
(`beam_service_scaffold`) provides a base service and client for writing Beam
services. You can manage services with the `beam-services` CLI and inspect them
via `beam-services --help`.

Beam also includes an object store. Basic operations are exposed via the
`bstore-cli` which can summarize, list, put and get objects. By default object
store events are logged to `/var/log/export/beam_object_store_events`.

Peashooter's `InstanceWorkers` are themselves Beam actors launched on the train
pool. The driver creates them using `beam.api.launch_actors` and communicates via
Beam RPCs to fetch instance summaries or enqueue work. If one of these workers
dies, the RPC will fail and the batch completer aborts.
