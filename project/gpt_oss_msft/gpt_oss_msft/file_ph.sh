
# RL
beam python --use-cwd -m qstar.run_experiment \
nostrict \
:berry_models.osmini:d36_qatmoe_fp8kv \
...instance_completer.instance_optimizer.aux_loss_scale_sample_length_power=0 \
...instance_completer.instance_optimizer.aux_loss_scale_sample_length_clip_min=1 \
skip_validate_config_serdes=True \
skip_validate_config=True \
disable_lemon_chz_serialization=False \
log_min_mini_experiment_config=False \
name=0607-osmini-r16-candidate-cs0.1-ec2-codeberry-nctx102400-yap32k-fft0.005-lr2e-06-klpenalty0-flight8-itc256-SPI64-IPB128-baseckpt-osmini_0531_rkld_step_800_m0.8-250608-0747 \
...layout=finetune-80g-os8-128k \
github_upload=False \
timeout.teach=20000 \
timeout.rollout=10000 \
root_config='mini.root.dev init_actors_rpc_timeout=600 driver_rpc_timeout=600' \
policy.initial_checkpoint=az://oaiwhalesong/strawberry-processing/x/xinw/osmini/ckpts/osmini_surgery/osmini_rkld_s800/multi_0.8/ \
security_profile=strawberry2 \
save_every=2 \
...enforce_unique_variant_types=False \
peashooter.kv_spilling=False \
peashooter.sampling_concurrency=64 \
peashooter.num_sampling_processes=64 \
peashooter.timeout_seconds.stalled_datapoint=7200 \
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600 \
peashooter.queue_granularity=2 \
peashooter.num_instance_workers=128 \
peashooter.distributed_sampling_config=peashooter.distributed_sampling.distributed_sampling_config:DistributedSamplingConfig \
peashooter.distributed_sampling_config.load_balancing.max_kv_util=90.0 \
peashooter.buffer_store_config_internal=beam_object_store \
batch_completer.n_batches_in_flight=8 \
batch_completer.instances_per_batch=128 \
batch_completer.batch_size_schedule=StepBasedLinearWarmup \
batch_completer.batch_size_schedule.initial_instances_per_batch=64 \
batch_completer.batch_size_schedule.num_iterations=100 \
...safely_set_max_datapoints=False \
defaults.n_ctx=102400 \
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only \
defaults.sampler.interleave_channels.0=analysis \
defaults.sampler.interleave_channels.1=commentary \
defaults.sampler.max_num_yields=100000 \
defaults.sampler.return_token_alternatives=32 \
defaults.target_samples_per_instance=64 \
...harmony_constrained_sampling=True \
...harmony_constrained_sampling_with_prob=0.1 \
optimizer.kl_penalty_alpha=0 \
...adaptive_overtrain_factor=1.5 \
...lr_per_instance_d16=2e-06 \
seed=47 \
policy.is_multimodal=False \
...multi_stage_grader_exits_early=True \
...dataset.deprecated_knowledge_cutoff=2024-06-01 \
...dataset.conversation_start_date=2025-03-08 \
...dataset.is_multimodal=False \
...override_reward_multiplier=8,64,512 \
...dataset.vary_reasoning_message=1 \
...instance_completer.instance_optimizer.sample_preprocessor=qstar.optimizers.sample_preprocessors.token_costs:TokenCostPreprocessor \
...instance_completer.instance_objective.token_cost_objective=berry:UnusedObjective \
...sample_preprocessor.sample_preprocessors.0.per_channel_inverse_token_costs.0.fixed_inverse_token_cost=32768 \
...min_initial_samples_per_variant=4 \
optimizer.rkld_alpha=0.001 \
defaults.inverse_token_cost=256 \
...qos_type=ROUND_ROBIN_BY_POD \
...deliberate_tool_instructions= \
...deliberation_enabled=False \
...dataset.mode=osmini \
...include_channel_error_in_format_error_reinforcement=True \
...format_error_cost=0.005 \
ensure_sample_allocators_consistent=False \
...caas_endpoint=https://caas-prod.ace-research.openai.org \
...caas_blobstore_container_name=oaistrawberryacecus

# RKLD
beam python --use-cwd -m qstar.run_experiment \
...override_target_samples_per_instance@=defaults.override_target_samples_per_instance \
...instances_per_batch@=defaults.instances_per_batch \
...inverse_token_cost@=defaults.inverse_token_cost \
...sample_completer@=defaults.sample_completer \
...variant_producer@=defaults.variant_producer \
...channel_config@=defaults.channel_config \
...max_tokens@=defaults.max_tokens \
...sampler@=defaults.sampler \
...n_ctx@=defaults.n_ctx \
name=0531-osmini-v5-moemxfp4-all_data_v4-cont215-cont420-ctx131072-LR3e-6-spi32-iIPB64IPB128-itc4096-rkl0.2-kl0.03-d8_64_512-f16-CS0.1-myasu-rkld-ps-train-alu720-train-w0-r1 \
peashooter.sampling_use_ev3=True \
policy.sampling_ml_config='osmini_bf16_chicken_sampling_nown_moe_mxfp4_kv_fp8' \
policy.model_config_name=falcon.leek.d36-s32-k4-bf16-v0_sesame_final-textonly-nownsimwn \
policy.set_by_preset=True \
policy.encoding_name=orion_200k \
policy.ml_config='osmini_bf16_falcon_training_nown_qat_only_moe' \
policy=berry_tw.model_config:ModelConfig \
policy.precision.moe=SetThroughMLConfig \
...layout=finetune-80g-os8-128k \
peashooter.sampling_concurrency=32 \
peashooter.num_sampling_processes=64 \
peashooter.queue_granularity=2 \
...override_reward_multiplier=8,64,512 \
...dataset.vary_reasoning_message=1 \
...dataset=HarmonyCompletionDatasetConfig \
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only \
...harmony_constrained_sampling=True \
...harmony_constrained_sampling_with_prob=0.1 \
batch_completer.batch_size_schedule=StepBasedLinearWarmup \
batch_completer.batch_size_schedule.initial_instances_per_batch=64 \
batch_completer.batch_size_schedule.num_iterations=200 \
...dataset.mode=osmini \
...dataset.is_multimodal=False \
defaults.sampler.interleave_channels.1=commentary \
defaults.sampler.interleave_channels.0=analysis \
defaults.n_ctx=131072 \
defaults.instances_per_batch=128 \
...target_samples_per_instance=32 \
optimizer.hparam_scaler.lr_per_instance_d16=2e-6 \
optimizer.rkld_alpha=0.2 \
defaults.inverse_token_cost=4096 \
defaults.sampler.return_token_alternatives=32 \
optimizer.kl_penalty_alpha=0.03 \
batch_completer.n_batches_in_flight=16 \
github_upload=False \
skip_validate_config=True \
...instance_completer.instance_optimizer.sample_preprocessor=qstar.optimizers.sample_preprocessors.token_costs:TokenCostPreprocessor \
...instance_completer.instance_objective.token_cost_objective=berry:UnusedObjective \
...instance_completer=qstar.instance_completers:SimpleInstanceCompleter \
...instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer \
...instance_completer.instance_optimizer.reinforce_strategy.sample_selector=qstar.sample_selectors:SimpleSampleSelector \
...instance_completer.instance_optimizer.reinforce_strategy.reward_reinforcer=qstar.optimizers.strategies.reward_reinforcers:ZeroReinforcer \
...enforce_unique_variant_types=False \
...max_num_yields=1000 \
...safely_set_max_datapoints=False \
save_every=5 \
...qos_type=ROUND_ROBIN_BY_POD \
...dataset.rkld_throw_away_if_teacher_max_len=False \
...include_channel_error_in_format_error_reinforcement=True \
...format_error_cost=20 \
...instance_completer.instance_optimizer.aux_loss_scale_sample_length_power=0 \
...instance_completer.instance_optimizer.aux_loss_scale_sample_length_clip_min=1 \
...deliberation_enabled=False \
peashooter.distributed_sampling_config=peashooter.distributed_sampling.distributed_sampling_config:DistributedSamplingConfig \
peashooter.distributed_sampling_config.load_balancing.max_kv_util=90.0 \
...initial_checkpoint=az://oairic1/oaistrawberry2/twapi/mini/e/myasu-0531-osmini-v5-moemxfp4-all_data_v4-cont215-ctx131072-LR3e-6-spi32-iIPB64IPB128-itc4096-rkl0.2-kl0.03-d8_64_512-f16-CS0.5-myasu-rkld-ps-train-alu720-train-w0-r1/policy/step_000420/250531114326W3HVZBLS-0/ \
...initial_checkpoint_mode=resume


beam python --use-cwd -m qstar.run_experiment \
nostrict \
name=math-oss-july6 \
seed=20250616 \
:qstar.presets.mathgen:tr_mathd_v2 \
batcher.curriculum.training_datasets.0.dataset.dataset_container=orngscuscresco \
batcher.curriculum.training_datasets.0.dataset.dataset_id=data.mathd-msft.train \
peashooter.sampling_use_ev3=True \
policy.sampling_ml_config='osmini_bf16_chicken_sampling_nown_moe_mxfp4_kv_fp8' \
policy.model_config_name=falcon.leek.d36-s32-k4-bf16-v0_sesame_final-textonly-nownsimwn \
policy.encoding_name=orion_200k \
policy.ml_config='osmini_bf16_falcon_training_nown_qat_only_moe' \
policy=berry_tw.model_config:ModelConfig \
policy.precision.moe=SetThroughMLConfig \
...layout=finetune-80g-os8-128k \
...dataset.is_multimodal=False \
defaults.n_ctx=131072 \
defaults.inverse_token_cost=4096 \
enable_slackbot=False \
security_profile=msft-orng \
github_upload=False \
wandb_enable=True \
kafka_enable=False \
max_steps=20 \
...save_every=2 \
policy.initial_checkpoint=az://orngtransfer/ameinbound/snapshots/osmini-20250623-r16s110-safetys15-step-15-tc