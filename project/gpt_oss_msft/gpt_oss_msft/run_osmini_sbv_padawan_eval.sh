#! /bin/bash

EVAL_INITIAL_POLICY=True
EVAL_EVERY=1

dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="oss-mini-sbv-$dt-evals-peaval"


INITIAL_CHECKPOINT="az://orngtransfer/ameinbound/snapshots/osmini-20250623-r16s110-safetys15-step-15-tc"
RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only"

CMD=(
# oaipkg run qstar.run_eval nostrict
beam python --start-daemon --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}

:berry_models.osmini:d36_qatmoe_fp8kv
policy.initial_checkpoint=${INITIAL_CHECKPOINT}
policy.is_multimodal=False

root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='osmini_bf16_falcon_training_nown_qat_only_moe'

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=32
peashooter.num_sampling_processes=16
peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
peashooter.sampling_use_ev3=True


eval_settings.eval_every=${EVAL_EVERY}
eval_settings.exit_on_no_new_checkpoint=True
eval_settings.eval_initial_policy=${EVAL_INITIAL_POLICY}
eval_settings.checkpoint_dir=${INITIAL_CHECKPOINT}

# Inherit model params from training experiment!
auto_inherit_training_args=False
seed=47

metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

# sbv data new
':deep_swe_eval_msft.swe_bench.peaval.padawan.presets:ev_sm_bench_juice_padawan(juice=128 override_target_samples_per_instance=1 max_num_yields=256)'

policy.n_ctx=131072 
defaults.n_ctx=131072 
defaults.sampler.max_num_yields=100
defaults.sampler.harmony_constrained_sampling=True
...harmony_renderer_name=${RENDERER_NAME}

# Set to True if you want to evaluate step0 (your prior)
eval_settings.eval_initial_policy=True

# msft special
load.restore_from_all_clusters=False

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False

)
GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval-ossmini-sbv-peaval.log