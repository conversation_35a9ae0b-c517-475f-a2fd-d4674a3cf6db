import berry.preset_utils
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe.datasets.config_utils import chz_path

import gpt_oss_msft.swe_tasks.rfs_rcs_bb_ml.dataset_config as rfs_rcs_bb_ml_configs
import gpt_oss_msft.swe_tasks.sbh.dataset_config as sbh_configs
import gpt_oss_msft.swe_tasks.sbt.dataset_config as sbt_configs
import gpt_oss_msft.swe_tasks.webdev.dataset_config as webdev_configs

format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)

sbv_mix1 = [
    # (rfs_rcs_bb_ml_configs.RFSRCSBBPythonDatasetConfig, []),
    (sbt_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.chenliang1.swe_train.data_mix_v1.swb_train_rewritten_4x_2rp"]),
    (sbh_configs.SBHRepairTrainDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
]

hint_mix1 = [
    (sbt_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.chenliang1.swe_train.data_mix_v1.swb_train_rewritten_4x.teacher_v4"]), # 2400
    (sbh_configs.SBHRepairTrainDatasetConfig, ["dataset_id=data.chenliang1.swe.upload05202025.rcr_1790.train_hq.teacher_v4"]), # 1700
]

sbv_mix2 = [
    (sbt_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.chenliang1.swe_train.data_mix_v1.swb_train_rewritten_4x.teacher_v4"]), # 2400
    (sbh_configs.SBHRepairTrainDatasetConfig, ["dataset_id=data.chenliang1.swe.upload05202025.rcr_1790.train_hq.teacher_v4"]), # 1700
    (sbh_configs.SBHV2RepairTrainDatasetConfig, ["dataset_id=data.jadhuang.swe.upload08182025.sbhv2.vsc.train"]), # 4500
    (rfs_rcs_bb_ml_configs.RFSRCSBBPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_py_diverse_repo"]), # 4400
]

rkld_mix1 = [
    (sbt_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.chenliang1.swe_train.data_mix_v1.swb_train_rewritten_4x.teacher_v4"]), # 2400
    (sbh_configs.SBHRepairTrainDatasetConfig, ["dataset_id=data.chenliang1.swe.upload05202025.rcr_1790.train_hq.teacher_v4"]), # 1700
    (sbh_configs.SBHV2RepairTrainDatasetConfig, ["dataset_id=data.jadhuang.swe.upload08182025.sbhv2.vsc.train"]), # 4500
    (webdev_configs.WebDevArenaSingleTurnFilteredDatasetConfig, []),
    (webdev_configs.WebDevArenaMultiTurnFilteredDatasetConfig, []),
    (rfs_rcs_bb_ml_configs.RFSRCSBBPythonDatasetConfig, []),
]

train_sbv_mix1 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in hint_mix1
    ],
    format,
)

train_sbv_mix2 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in sbv_mix2
    ],
    format,
)

train_rkld_mix1 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in rkld_mix1
    ],
    format,
)

debug_run = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in [(sbh_configs.SBHRepairTrainDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"])]
    ],
    format,
)

webdev_derisk = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in [
            (webdev_configs.WebDevArenaSingleTurnFilteredDatasetConfig, []),
            (webdev_configs.WebDevArenaMultiTurnFilteredDatasetConfig, []),
        ]
    ],
    format,
)


