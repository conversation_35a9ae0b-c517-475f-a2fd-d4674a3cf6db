import copy
import random
from functools import cache
from threading import Lock
from typing import Any, Sequence, cast

import structlog
from gpt_oss_msft.data.prompts import STEPS_TO_FOLLOW


logger = structlog.stdlib.get_logger(component=__name__)

ENFORCE_COMMANDS_FORBIDDEN = "enforce_commands_forbidden"
ENFORCE_COMMANDS_REQUIRED = "enforce_commands_required"
EXTRA_INSTRUCTIONS_METADATA_KEY = (
    "deep_swe_append_extra_instructions"  # or "deep_swe_prepend_extra_instructions"
)

BAN_COMMANDS_PREFIXES = [
    "The following commands are forbidden:",
    "Do not use any of the following commands:",
    "Never use any of these commands:",
]

ENCOURAGED_COMMANDS = [
    # NOT USED
]

DISCOURAGED_COMMANDS = [
    "oai",
]

def datapoint_rng(dp: dict[str, Any], seed: int = 1337) -> random.Random:
    return random.Random(str(seed) + str(dp.get("unique_id") or dp.get("task_id") or dp))

def enforce_commands(
    dp: dict[str, Any],
    seed: int = 1337,
    probability: float = 0.25,
    extra_good_commands: tuple[str, ...] = (),
    extra_bad_commands: tuple[str, ...] = (),
) -> Sequence[dict[str, Any]]:
    r"""
    Augment the datapoint to encourage/discourage arguments whose functionalities are covered by oai-coreutils.
    """
    rng = datapoint_rng(dp, seed)
    if rng.random() > probability:
        return [dp]

    good_k = rng.randint(0, 3)
    bad_k = rng.randint(0, 3)
    if not good_k and not bad_k:
        return [dp]

    # good_commands = rng.sample(ENCOURAGED_COMMANDS + list(extra_good_commands), k=good_k)
    # bad_commands = rng.sample(DISCOURAGED_COMMANDS + list(extra_bad_commands), k=bad_k)
    bad_commands = ['oai']

    dp = copy.deepcopy(dp)
    # dp["metadata"][ENFORCE_COMMANDS_REQUIRED] = good_commands
    dp["metadata"][ENFORCE_COMMANDS_FORBIDDEN] = bad_commands

    instructions = ""
    # if good_commands := good_commands:
    #     instructions = rng.choice(REQUIRE_COMMANDS_PREFIXES)
    #     for command in good_commands:
    #         instructions += f"\n- `{command}`"
    if bad_commands := bad_commands:
        if instructions:
            instructions += "\n\n"
        instructions += rng.choice(BAN_COMMANDS_PREFIXES)
        for command in bad_commands:
            instructions += f"\n- `{command}`"
    cast(dict[str, Any], dp["metadata"]).setdefault(EXTRA_INSTRUCTIONS_METADATA_KEY, [])
    dp["metadata"][EXTRA_INSTRUCTIONS_METADATA_KEY].append(instructions)

    return [dp]


def append_step_to_follow(dp: dict[str, Any], probability: float = 0.7) -> list[dict[str, Any]]:
    cast(dict[str, Any], dp["metadata"]).setdefault(EXTRA_INSTRUCTIONS_METADATA_KEY, [])

    if random.random() < probability:
        instruction = STEPS_TO_FOLLOW[random.choice(list(STEPS_TO_FOLLOW.keys()))]
        dp["metadata"][EXTRA_INSTRUCTIONS_METADATA_KEY].append(f'\n\n{instruction}')
    return [dp]