import copy
import dataclasses
import uuid
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Any, Sequence, TypeVar, no_type_check
from qstar.common.datapoint import HarmonyCompletionDatapoint
from gpt_oss_msft.data.prompts import GPT5_SYSTEM_PROMPT

from chat import chat

PromptT = TypeVar("PromptT", bound=chat.Conversation | str)


@dataclasses.dataclass
class GpTossHarmonyCompletionDatapoint(HarmonyCompletionDatapoint):
    """Datapoint for osmini Harmony completions.
    This is a subclass of HarmonyCompletionDatapoint that removes yap and juice levels from the prompt
    and instead adds reasoning effort level based on the juice value.

    This is kind of a hack! message @jmcgrath if this is causing you trouble in refactors, etc
    and he will move this into a custom renderer.
    """

    def _map_juice_to_effort(self) -> str:
        """Map juice to effort level and length.
        # below 32 -> low | 32 < juice <= 128 -> medium | juice > 128 -> high
        """
        reasoning_level = "low"
        juice = self.reward_multiplier
        if isinstance(juice, int):
            if 32 < juice <= 128:
                reasoning_level = "medium"
            elif juice > 128:
                reasoning_level = "high"
        return reasoning_level

    @no_type_check
    def osmini_make_student_prompt(self, prompt: chat.Conversation) -> chat.Conversation:
        """Make the student prompt for osmini."""
        from copy import deepcopy

        reasoning_level = self._map_juice_to_effort()
        reasoning_msg = f"Reasoning: {reasoning_level}"
        p = deepcopy(prompt)
        # remove the juice sentence
        p.messages[0].content.metadata.reward_multiplier = None
        # drop the Yap sentence and instead mention reasoning effort
        instructions = [
            inst
            for inst in p.messages[0].content.instructions
            if "yap" not in inst.lower() and "oververbosity" not in inst.lower()
        ]
        instructions += [reasoning_msg]
        p.messages[0].content.instructions = instructions
        with open("/var/log/supervisor/rkld_student_prompt.log", "a") as f:
            f.write(f"{p.messages[0]}\n")
        return p
    
    @no_type_check
    def osmini_make_teacher_prompt(self, prompt: chat.Conversation) -> chat.Conversation:
        """Make the teacher prompt for osmini."""
        from copy import deepcopy

        p = deepcopy(prompt)
        # drop the Yap sentence and instead mention reasoning effort
        instructions = p.messages[0].content.instructions
        instructions += [GPT5_SYSTEM_PROMPT]
        p.messages[0].content.instructions = instructions
        with open("/var/log/supervisor/rkld_teacher_prompt.log", "a") as f:
            f.write(f"{p.messages[0]}\n")
        return p
    
    @property
    def prompt(self) -> PromptT:
        if self._prompt is None:
            base_prompt = super()._get_prompt()

            p_teacher = self.osmini_make_teacher_prompt(base_prompt)
            p_student = self.osmini_make_student_prompt(base_prompt)

            for prompt_postprocessor in self.dataset_config.prompt_postprocessors:
                p_teacher = prompt_postprocessor.postprocess_prompt(self, p_teacher)
                p_student = prompt_postprocessor.postprocess_prompt(self, p_student)

            self._prompt = p_student
            self.metadata["prompt_teacher"] = p_teacher.model_dump_json()
            self.metadata["prompt_student"] = p_student.model_dump_json()

        return self._prompt

    # def _get_prompt(self) -> chat.Conversation:
    #     prompt_conversation = super()._get_prompt()
    #     # Make the student prompt for osmini
    #     prompt_conversation = self.osmini_make_student_prompt(prompt_conversation)
    #     return prompt_conversation