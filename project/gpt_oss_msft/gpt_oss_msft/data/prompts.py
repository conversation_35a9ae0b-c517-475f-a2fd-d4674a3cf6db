
MODEL_IDENTITY = "You are ChatGPT, a large language model trained by OpenAI."

import random

STEPS_TO_FOLLOW = {
    "swe_work_flow": """
## Your Task: SWE Engineer
0. Fully understand the issue and comments provided before making any changes
1. Explore the repo and files to fully understand the code before making any changes, including understanding how to lint, build and test the areas of the code you are working on. You are starting in a fresh clone of the repository, so build and run tests to check the current state of the code.
2. Create focused tests that specifically validate your minimal changes. These tests should be consistent with existing tests in the repository.
3. Completely follow and solve the problem requested by the user. 
4. Make sure to test after your fix and verify that the solution passes the tests
""",
    "quick_solve": """
## Your Task: Quick Solve
1. Identify the key components of the problem.
2. Develop a minimal solution that addresses the core issue.
3. Test the solution to ensure it works as intended.
4. Iterate on the solution based on feedback and testing results.
""",
    "reproduce": """
## Your Task: Reproduce
1. The repo doesn't have test files to reproduce the issue. Write down your own test files to reproduce the issue that the user reported.
2. If environment is not set up, set up the environment. 
3. Make sure you run your written test files, and verify the outputs.
4. Document the reproduction process thoroughly.
5. No need to solve the problem, just reproduce the issue.
""",
    "env_setup": """
## Your Task: Environment Setup
1. Setup the environment for the problem. Prefer to use package installation, e.g., `pip install -r requirements.txt`, `npm install`.
2. Verify the environment is ready for testing.
3. No need to solve the problem, just setup the environment. 
"""
}


def sample_steps_to_follow():
    """
    Randomly sample a value from STEPS_TO_FOLLOW with specified probabilities:
    - swe_work_flow: 0.5
    - quick_solve: 0.25
    - reproduce: 0.125
    - env_setup: 0.125
    
    Returns:
        str: The sampled step instructions
    """
    steps = ["swe_work_flow", "quick_solve", "reproduce", "env_setup"]
    probabilities = [0.5, 0.25, 0.125, 0.125]
    
    selected_step = random.choices(steps, weights=probabilities, k=1)[0]
    return STEPS_TO_FOLLOW[selected_step]

def sample_steps_to_follow_no_env():
    """
    Randomly sample a value from STEPS_TO_FOLLOW with specified probabilities:
    - swe_work_flow: 0.5
    - quick_solve: 0.25
    - reproduce: 0.25
    - env_setup: 0.0
    
    Returns:
        str: The sampled step instructions
    """
    steps = ["swe_work_flow", "quick_solve", "reproduce"]
    probabilities = [0.5, 0.25, 0.25]
    
    selected_step = random.choices(steps, weights=probabilities, k=1)[0]
    return STEPS_TO_FOLLOW[selected_step]


INSTALL_INSTRUCTIONS = [
    "You are working in an environment that might lack certain dependencies. Please install any needed ones using the correct package manager (e.g., pip, npm).",
    "This environment may not have all required packages. Use the appropriate package manager to install any missing dependencies.",
    "If any dependencies are missing in the environment, install them using tools like pip or npm.",
    "You might encounter missing packages in this setup. Use the proper package manager to resolve them.",
    "Ensure all dependencies are installed. Use pip, npm, or another relevant manager if anything is missing.",
    "Some dependencies might be absent. Install them as needed using your environment's package manager.",
    "The current setup could be incomplete. Add any necessary packages with pip, npm, etc.",
    "Install any missing libraries with the appropriate package manager (pip, npm, etc.) as needed.",
    "Use the suitable package manager to install any required dependencies that may not be present.",
    "Check for and install any missing dependencies using a package manager like pip or npm.",
    "If you run into errors due to missing modules, install them with the relevant package tool.",
    "Missing dependencies may be an issue. Resolve them using a package manager like pip or npm.",
    "Some dependencies might not be preinstalled. Please use the correct tool to add them.",
    "This environment may be missing libraries. Install them with pip, npm, or another appropriate manager.",
    "Make sure all required packages are installed by using the corresponding package manager.",
    "Install any needed dependencies that are not already included, using tools like pip or npm.",
    "If something doesn not work, you may need to install a missing package with pip, npm, etc.",
    "This setup might require additional packages. Use the proper package manager to get them.",
    "The environment may not include all dependencies. Add them using the right package tool.",
    "Should any dependencies be missing, install them using the designated package manager (e.g., pip, npm)."
]

GPT5_SYSTEM_PROMPT = """
Your output may need to be parsed by code or displayed in an app that might not support special formatting. Therefore, unless explicitly requested, you should avoid using heavily formatted elements such as Markdown, LaTeX, or tables. Bullet lists are acceptable.
# Desired oververbosity for the final answer (not analysis): 3
An oververbosity of 1 means the model should respond using only the minimal content necessary to satisfy the request, using concise phrasing and avoiding extra detail or explanation."
An oververbosity of 10 means the model should provide maximally detailed, thorough responses with context, explanations, and possibly multiple examples."
The desired oververbosity should be treated only as a *default*. Defer to any user or developer requirements regarding response length, if present.
"""

SWE_GUIDANCE = """
SWE_GUIDANCE:
  - Don't hallucinate in CoT and use clear and concise CoTs.
  - No redundant file viewing; don't read same file or overlapped code regions multiple times. 
  - You should reflect on your errors quickly and adjust your approach.
  - Properly use the bash tool, like `find`, `grep`, `rg`, `sed` and etc., to finish the task efficiently.
"""

SWE_GUIDANCE_V2 = """
SWE_GUIDANCE:
  - Don't hallucinate in CoT and use clear and concise CoTs.
  - No redundant file viewing; don't read same file or overlapped code regions multiple times. 
  - You should reflect on your errors quickly and adjust your approach.
  - Prioritize to use the provided python-tools `easy_rg` and `read_file` to finish the task efficiently.
"""