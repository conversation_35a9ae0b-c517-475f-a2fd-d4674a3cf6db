import random
from functools import lru_cache
from typing import Any, Literal, Mapping, Sequence, TypeVar, cast

import numpy as np
import numpy.typing as npt
import structlog

import berry.sampler
import chat
import chz
import qstar.common.next_action
import tiktoken
from berry import birder_utils
from berry.virtual_factory import VirtualFactory
from chat import <PERSON><PERSON><PERSON><PERSON>
from chat.render.common import render_content
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from chat.render.v4.harmony_v4 import <PERSON><PERSON><PERSON>erV4
from mini.model import EXTRA_FIELD_MOE_EXPERTS
from multimodal_token.toklist import TokList
from qstar.common import datapoint, encoder_utils, prompt_encoding, types, utils
from qstar.common.next_action import get_next_action
from qstar.common.tools import constraint_machine_spec, renderer_worker
from qstar.common.tools.constraint_machine_spec import ConstraintMachineMaxTokensSpec
from qstar.common.tools.developer_functions.developer_functions import DeveloperFunctionsToolConfig
from qstar.common.tools.memento.memento_tool import MementoToolConfig
from qstar.model_samplers import Model<PERSON>ampler, SampleArgs
from qstar.samplers.postprocessors import postprocessor as postprocessor_module
from twapi_engine_api_services.api_services import API_MAX_LOGIT_BIAS

logger = structlog.stdlib.get_logger(component=__name__)

from copy import deepcopy
from qstar.samplers.sampler import (
    BaseSampler, apply_harmony_errors, _special_token_denylist_bias,
    _encode_special_token, prepare_constrained_sampling_extension
)

@chz.chz(typecheck=True)
class OsminiSampler(BaseSampler):
    
    harmony_constrained_sampling_with_prob: float = chz.field(
        doc="Probability to actually use the harmony constrain sampling extension.", default=1.0
    )

    def initialize_prompts(
        self,
        model_sampler: ModelSampler,
        samples: Sequence[types.Sample[types.DatapointT]],
        profile_label: str | None,
        sample_kwargs: dict[str, Any] | None,
        virtual_factory: VirtualFactory | None,
    ) -> list[types.SampleWithCompletion[types.DatapointT]]:
        is_harmony = isinstance(samples[0].gt_datapoint, datapoint.HarmonyDatapoint)
        encoding_name = model_sampler.encoding.name
        is_multimodal = model_sampler.spec.is_multimodal

        if is_harmony:
            assert self.harmony_renderer_name
            renderer = renderer_worker.get_renderer_cache(self.harmony_renderer_name)
            # sanity check renderer
            assert encoding_name in renderer.encoding_name, (
                f"Renderer and model encoding do not match!, {renderer.encoding.name=} != {encoding_name=}, {self.harmony_renderer_name=}"
            )
            assert renderer.is_multimodal == is_multimodal, (
                f"Renderer and model multimodal settings do not match!, {renderer.is_multimodal=} != {is_multimodal=}"
            )
        else:
            renderer = None

        samples_with_completion = list[types.SampleWithCompletion]()
        timer_prefix = f"{profile_label or 'sample'}"
        with birder_utils.simple_timer(f"{timer_prefix}.encode", extra_info=str(len(samples))):
            for sample in samples:
                assert not isinstance(sample, types.SampleWithCompletion)
                prompt_tokens = self._compute_prompt_tokens(
                    sample=sample,
                    renderer=renderer,
                    model_sampler=model_sampler,
                    virtual_factory=virtual_factory,
                )
                errors_blamed_on_system: set[str] = set()
                if self.n_ctx - len(prompt_tokens) - self.header_buffer_tokens <= 0:
                    # If there are no tokens left after counting the prompt and the # of header
                    # tokens for the first round of sampling, mark it as invalid immediately.
                    logger.warning(
                        f"Marking sample as invalid because {len(prompt_tokens)=} was too long for {self.n_ctx=}."
                    )
                    max_length = 0
                    errors_blamed_on_system.add(types.CommonErrors.PROMPT_TOO_LONG)
                else:
                    max_length = berry.sampler.compute_max_length_for_new_sample(
                        n_ctx=self.n_ctx,
                        gt_datapoint_max_tokens=sample.gt_datapoint.max_tokens,
                        dataset_config_max_tokens=sample.gt_datapoint.dataset_config.max_tokens,
                        prompt_len=len(prompt_tokens),
                    )
                prompt_tokens = self._get_prompt_for_sample_init(prompt_tokens)
                sample_with_completion = sample.init_empty_completion(
                    prompt_tokens=prompt_tokens,
                    raw_ebm_infos=self.raw_ebm_infos,
                    return_token_alternatives=self.return_token_alternatives,
                    return_top_tokens=self.return_top_tokens,
                    return_snapshot_hashes=self.return_snapshot_hashes,
                    max_length=max_length,
                    max_num_yields=self.compute_max_num_yields(sample),
                    errors_blamed_on_system=errors_blamed_on_system,
                )

                sample_teacher = deepcopy(sample)
                sample_teacher.gt_datapoint._uid += "_teacher"
                sample_teacher.gt_datapoint._prompt = sample_teacher.gt_datapoint.metadata[
                    "prompt_teacher"
                ]

                prompt_tokens_teacher = self._compute_prompt_tokens(
                    sample=sample_teacher,
                    renderer=renderer,
                    model_sampler=model_sampler,
                    virtual_factory=virtual_factory,
                )

                sample_with_completion.ephemeral_metadata["prompt_tokens_teacher"] = (
                    prompt_tokens_teacher
                )

                samples_with_completion.append(sample_with_completion)
        return samples_with_completion


    def _sample_from_model(
        self,
        model_sampler: ModelSampler,
        samples_with_completion: list[types.SampleWithCompletion],
        profile_label: str | None,
        sample_kwargs: dict[str, Any] | None,
        virtual_factory: VirtualFactory | None,
        interleave_channels: tuple[BerryChannel, ...] | None = None,
        forced_recipient: str | None = None,
    ) -> list[types.SampleWithCompletion[types.DatapointT]]:
        is_harmony = isinstance(samples_with_completion[0].gt_datapoint, datapoint.HarmonyDatapoint)
        encoding_name = model_sampler.encoding.name
        is_multimodal = model_sampler.spec.is_multimodal
        # If the batch consists of samples that have not been sampled before, we
        # set the priority to None (low priority), otherwise we set it to 0 (high priority).
        # This is to ensure multi-step rollouts aren't starved by single-step rollouts.
        research_batcher_args: dict[str, object] = {
            "priority": 0 if samples_with_completion[0].num_yields != 0 else None,
            "contextvars": structlog.contextvars.get_contextvars(),
            "num_yields": samples_with_completion[0].num_yields,
        }
        if is_harmony:
            renderer = renderer_worker.get_renderer_cache(self.harmony_renderer_name)
            # sanity check renderer
            assert encoding_name in renderer.encoding_name, (
                f"Renderer and model encoding do not match!, {renderer.encoding.name=} != {encoding_name=}, {self.harmony_renderer_name=}"
            )
            assert renderer.is_multimodal == is_multimodal, (
                f"Renderer and model multimodal settings do not match!, {renderer.is_multimodal=} != {is_multimodal=}, {self.harmony_renderer_name=}"
            )
            research_batcher_args["end_of_rollout_stop_token"] = (
                self.harmony_stop_tokens_utils.end_of_rollout_stop_token()
            )
        else:
            renderer = None
            research_batcher_args["end_of_rollout_stop_token"] = _encode_special_token(
                encoding_name, "<|endoftext|>"
            )

        timer_prefix = f"{profile_label or 'sample'}"
        with birder_utils.simple_timer(
            f"{timer_prefix}.encode", extra_info=str(len(samples_with_completion))
        ):
            max_tokens_per_context: list[int] | None = cast(list[int], [])
            assert max_tokens_per_context is not None
            prompts: list[TokList] = []
            for i, sample in enumerate(samples_with_completion):
                logger.debug(
                    "Sample with completion",
                    sample_id=sample.sample_id,
                    max_length=sample.max_length,
                    token_length=len(sample.tokens),
                    next_action=get_next_action(sample),
                )
                if isinstance(sample.gt_datapoint.prompt, chat.Conversation):
                    # For Harmony, extend the prompt with Harmony header tokens.
                    # Normally this is done by `chat.BaseRenderer.render_for_completion`,
                    # which we (currently) don't use in `qstar`.
                    assert is_harmony
                    sample = self._add_harmony_header(sample, recipient=forced_recipient)
                    samples_with_completion[i] = sample  # Janky footgun.
                prompts.append(
                    # Use type(sample.tokens).concatenate to return same type as type(sample.tokens) (e.g., TrainTokList vs. vanilla TokList)
                    type(sample.tokens).concatenate(
                        [utils.not_none(sample.prompt_tokens), sample.tokens]
                    )
                )
                assert utils.not_none(sample.max_length) > len(sample.tokens), (
                    f"{sample.max_length=}, {len(sample.tokens)=}, {sample.sample_id=}"
                )
                max_tokens_per_context.append(
                    utils.not_none(sample.max_length) - len(sample.tokens)
                )

        if sample_kwargs is None:
            sample_kwargs = {}

        if (
            self.return_token_alternatives is not None
            and "return_token_alternatives" not in sample_kwargs
        ):
            sample_kwargs["return_token_alternatives"] = self.return_token_alternatives
        if self.return_top_tokens is not None and "logprobs" not in sample_kwargs:
            # This potentially overwrites the default of logprobs=0 (return
            # logprobs of sampled tokens), but this is fine since whatever
            # non-null return_top_tokens value is set to gives a superset of the
            # behavior of logprobs=0.
            sample_kwargs["logprobs"] = self.return_top_tokens
        if self.temperature is not None and "temperature" not in sample_kwargs:
            sample_kwargs["temperature"] = self.temperature
        if (
            self.first_n_token_alternatives_no_top_p is not None
            and "n_token_alternatives_no_top_p" not in sample_kwargs
        ):
            sample_kwargs["n_token_alternatives_no_top_p"] = (
                self.first_n_token_alternatives_no_top_p
            )
        if self.return_snapshot_hashes and "return_snapshot_hashes" not in sample_kwargs:
            sample_kwargs["return_snapshot_hashes"] = True

        if "logit_bias" not in sample_kwargs:
            sample_kwargs["logit_bias"] = {}
        sample_kwargs["logit_bias"] |= _special_token_denylist_bias(
            model_sampler.encoding, self.special_token_allowlist
        )

        assert all(max_tokens > 0 for max_tokens in max_tokens_per_context), max_tokens_per_context
        if is_harmony:
            stop_sequences = [
                list(sequence)
                for sequence in self.harmony_stop_tokens_utils.stop_token_sequences_for_sample()
            ]
            if self.stop_tokens is not None:
                logger.warning(
                    "Appending user-specified stop_tokens to stop_tokens used for harmony"
                )
                # We allow for multi-token stop tokens
                stop_sequences.extend(
                    list(model_sampler.encoding.encode(stop_token, allowed_special="all"))
                    for stop_token in self.stop_tokens
                )
        elif self.stop_tokens is not None:
            stop_sequences = [
                [_encode_special_token(model_sampler.encoding.name, stop_token)]
                for stop_token in self.stop_tokens
            ]
        else:
            stop_sequences = [[_encode_special_token(model_sampler.encoding.name, "<|endoftext|>")]]

        for prompt, sample in zip(prompts, samples_with_completion, strict=True):
            seen_tokens: int = sample.metadata.get("seen_tokens", 0)
            logger.info(
                "Sending sampling request to engine",
                num_prompt_tokens=len(prompt),
                num_seen_tokens=seen_tokens,
                num_new_tokens=len(prompt) - seen_tokens,
            )

        if "extensions" not in sample_kwargs:
            sample_kwargs["extensions"] = []

        if self.harmony_constrained_sampling and random.random() < self.harmony_constrained_sampling_with_prob:
            assert len(samples_with_completion) == 1, (
                "Constrained sampling only supports single datapoint sampling"
            )
            assert renderer is not None
            extension = prepare_constrained_sampling_extension(
                dp=samples_with_completion[0],
                forced_recipient=forced_recipient,
                harmony_constrained_sampling_enable_json=self.harmony_constrained_sampling_enable_json,
                renderer=renderer,
                harmony_stop_tokens_utils=self.harmony_stop_tokens_utils,
                cot_msg_max_length=self.cot_msg_max_length,
            )
            sample_kwargs["extensions"].append(extension)

        if self.banned_tokens:
            sample_kwargs["extensions"].append(
                constraint_machine_spec.banned_tokens_extension(self.banned_tokens)
            )

        batch_tokens: list[TokList] = []
        for prompt, sample in zip(prompts, samples_with_completion, strict=True):
            try:
                batch_tokens.append(
                    self._get_prompt_for_batch(prompt, sample, model_sampler, virtual_factory)
                )
            except TypeError as e:
                logger.error(
                    f"Error while processing datapoint {sample.gt_datapoint.dataset_config.dataset_id} {e}"
                )
                raise

        samples = model_sampler.sample(
            args=SampleArgs(
                batch=batch_tokens,
                stop=stop_sequences,
                echo=False,
                seed=[[s.seed_as_int] for s in samples_with_completion],  # per request seed
                max_tokens=None,
                max_tokens_per_context=max_tokens_per_context,
                return_finish_reason=True,
                research_batcher_args=research_batcher_args,
                hacky_top_p=self.hacky_top_p,
                **sample_kwargs,
            )
        )

        with birder_utils.simple_timer(f"{timer_prefix}.decode", extra_info=str(len(samples))):
            # We make a copy here to avoid accidentally modifying the samples in place.
            samples_tokens, samples_int_tokens, samples_train_masks = (
                self._get_samples_tokens_and_masks(samples)
            )
            samples_logprobs = [cast(list[float], s["logprobs"].copy()) for s in samples]
            samples_special_scalars = [
                cast(list[list[float | None]] | None, s.get("special_scalars", []).copy())
                for s in samples
            ]
            sample_tensor_metrics = [s.get("tensor_metrics", []) for s in samples]
            samples_reached_max_tokens = [
                s.get("finish_reason", "") == "max_tokens" for s in samples
            ]
            samples_experts = [
                (
                    np.array(s[EXTRA_FIELD_MOE_EXPERTS], dtype=np.int16)
                    if EXTRA_FIELD_MOE_EXPERTS in s
                    else None
                )
                for s in samples
            ]

            samples_seen_tokens = [
                len(prompt) + len(tokens)
                for prompt, tokens in zip(prompts, samples_tokens, strict=True)
            ]

            for prompt, token, expert in zip(prompts, samples_tokens, samples_experts):
                if expert is not None:
                    assert len(token) + len(prompt) - 1 == len(expert), (
                        len(token) + len(prompt) - 1,
                        len(expert),
                    )

            samples_ebm_token_negenergies = [
                (
                    {
                        utils.not_none(ebm.key_name): ebm.compute_token_negenergies(
                            special_scalars=special_scalars,
                            num_tokens_expected=len(tokens),
                        )
                        for ebm in self.raw_ebms
                    }
                    if special_scalars
                    else None
                )
                for special_scalars, tokens in zip(
                    samples_special_scalars,
                    samples_tokens,
                    strict=True,
                )
            ]

            samples_alternative_tokens: list[npt.NDArray[np.int32] | None]
            samples_alternative_logprobs: list[npt.NDArray[np.float32] | None]

            if self.return_token_alternatives is not None:
                samples_alternative_tokens = []
                samples_alternative_logprobs = []
                for s in samples:
                    assert len(s["token_alternatives"]) == len(s["tokens"]), (
                        f"token_alternatives len: {len(s['token_alternatives'])}, tokens len: {len(s['tokens'])}"
                    )
                    sample_alt_tokens_raw = [
                        alt_tokens for alt_tokens, _ in s["token_alternatives"]
                    ]
                    sample_alt_tokens = np.array(sample_alt_tokens_raw, dtype=np.int32)
                    sample_alt_logprobs_raw = [
                        alt_logprobs for _, alt_logprobs in s["token_alternatives"]
                    ]
                    sample_alt_logprobs = np.array(sample_alt_logprobs_raw, dtype=np.float32)

                    samples_alternative_tokens.append(sample_alt_tokens)
                    samples_alternative_logprobs.append(sample_alt_logprobs)
                    # Count the number of tokens we have API logprobs for.
                    # Useful for global weight calculation during optimization.
            else:
                samples_alternative_tokens = [None] * len(samples)
                samples_alternative_logprobs = [None] * len(samples)

            samples_top_tokens: list[npt.NDArray[np.int32] | None]
            samples_top_logprobs: list[npt.NDArray[np.float32] | None]

            if self.return_top_tokens is not None:
                samples_top_tokens = []
                samples_top_logprobs = []
                for s in samples:
                    raw_top_logprobs: list[list[tuple[int, float]]] = s["top_logprobs"]
                    assert len(raw_top_logprobs) == len(s["tokens"])
                    sample_top_tokens = np.array(
                        [[t for t, _ in options_list] for options_list in raw_top_logprobs],
                        dtype=np.int32,
                    )
                    sample_top_logprobs = np.array(
                        [[l for _, l in options_list] for options_list in raw_top_logprobs],
                        dtype=np.float32,
                    )
                    samples_top_tokens.append(sample_top_tokens)
                    samples_top_logprobs.append(sample_top_logprobs)
            else:
                samples_top_tokens = [None] * len(samples)
                samples_top_logprobs = [None] * len(samples)

            samples_token_offsets: list[list[int] | None]

            if self.include_token_offsets:
                out = encoder_utils.batched_decode_with_offsets(
                    model_sampler.encoding, samples_int_tokens, progress_msg=None
                )
                samples_text = [text for text, _ in out]
                samples_token_offsets = [offsets for _, offsets in out]
            else:
                samples_text = model_sampler.encoding.decode_batch(
                    samples_int_tokens, num_threads=self.encoding_num_threads
                )
                samples_token_offsets = [None] * len(samples)

            samples_snapshot_hashes: list[npt.NDArray[np.uint64] | None]
            if self.return_snapshot_hashes:
                samples_snapshot_hashes = [
                    np.array(s.get("snapshot_hashes", []), dtype=np.uint64) for s in samples
                ]
            else:
                samples_snapshot_hashes = [None] * len(samples)

        sample_conversations: list[chat.Conversation | None] = []
        sample_errors_blamed_on_model: list[set[str]] = []
        for s, dp, reached_max_tokens in zip(
            samples, samples_with_completion, samples_reached_max_tokens
        ):
            convo, errors_blamed_on_model = self._construct_convo_and_harmony_errors(
                s,
                dp,
                forced_recipient,
                renderer,
                reached_max_tokens,
                self.harmony_stop_tokens_utils if is_harmony else None,
            )
            sample_conversations.append(convo)
            sample_errors_blamed_on_model.append(errors_blamed_on_model)

        samples_with_completion = [
            (
                sample.extend_completion(
                    text=text,
                    tokens=tokens,
                    logprobs=logprobs,
                    tensor_metrics=tensor_metrics,
                    token_offsets=token_offsets,
                    experts=experts,
                    ebm_token_negenergies=ebm_token_negenergies,
                    alternative_tokens=alternative_tokens,
                    alternative_logprobs=alternative_logprobs,
                    top_tokens=top_tokens,
                    top_logprobs=top_logprobs,
                    snapshot_hashes=snapshot_hashes,
                    conversation=conversation,
                    errors_blamed_on_model=errors_blamed_on_model,
                    new_metadata={
                        "seen_tokens": seen_tokens,
                    },
                    train_mask=train_mask,
                )
            )
            for (
                (sample, conversation),
                (text, tokens, logprobs, tensor_metrics, token_offsets, seen_tokens, train_mask),
                (
                    experts,
                    ebm_token_negenergies,
                    alternative_tokens,
                    alternative_logprobs,
                    top_tokens,
                    top_logprobs,
                    snapshot_hashes,
                    errors_blamed_on_model,
                ),
            ) in zip(
                # Note: must use nested zip statements, as `mypy` zip typing does not
                # have full variadic support, and so long zip instances become typed as `Any`.
                zip(samples_with_completion, sample_conversations, strict=True),
                zip(
                    samples_text,
                    samples_tokens,
                    samples_logprobs,
                    sample_tensor_metrics,
                    samples_token_offsets,
                    samples_seen_tokens,
                    samples_train_masks,
                    strict=True,
                ),
                zip(
                    samples_experts,
                    samples_ebm_token_negenergies,
                    samples_alternative_tokens,
                    samples_alternative_logprobs,
                    samples_top_tokens,
                    samples_top_logprobs,
                    samples_snapshot_hashes,
                    sample_errors_blamed_on_model,
                    strict=True,
                ),
                strict=True,
            )
        ]

        for idx, sample in enumerate(samples_with_completion):
            for postprocessor in self.postprocessors:
                samples_with_completion[idx] = postprocessor.postprocess_sample(sample)

        # We introduce this *after* post-processing in order to allow post-processing to intercept
        # and modify the samples before we apply harmony errors.
        # TODO: we should eventually refactor this to also be a postprocessor
        if is_harmony:
            for sample in samples_with_completion:
                apply_harmony_errors(
                    sample,
                    interleave_channels=interleave_channels,
                    max_num_yields=self.compute_max_num_yields(sample),
                    header_buffer_tokens=self.header_buffer_tokens,
                    validate_commentary_to_all=self.validate_commentary_to_all,
                    validate_all_channels_present=self.validate_all_channels_present,
                )
        return samples_with_completion