export TEACHER_CKPT_0="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
export USER_0="gpt-oss-rkld"
# export TEACHER_CKPT_1=az://orngscuscresco/models/snapshots/4o-tt-v5-transferred-2025-04-09-decrypted
# export USER_1=codechat
export STUDENT=az://orngtransfer/ameinbound/snapshots/osmini-20250623-r16s110-safetys15-step-15-tc
export WANDB_ENTITY=genaicore
export WANDB_PROJECT_NAME=gpt_oss_msft

LR=2e-6
N_CTX=131072 # 272144
TRAIN_GPUS=80
BZ=512
N_SAMPLE=1

oaipkg run --beam qstar.run_experiment nostrict \
    :qstar.presets.rkld:base_preset \
    name=fkld-rkld-mix8-flight-5-o30402-${BZ}x${N_SAMPLE}-$(date +%m%d-%H%M) \
    batcher.curriculum.training_datasets.0.dataset.dataset_id=data.haoranxu.rkld.mix.mix8 \
    batcher.curriculum.training_datasets.0.dataset.dataset_container=orngscuscresco \
    batcher.curriculum.training_datasets.0.dataset.grader=gpt_oss_msft.graders.rkld_grader:RkldGrader \
    batcher.curriculum.training_datasets.0.dataset.grader.topic.0=$TEACHER_CKPT_0 \
    batcher.curriculum.training_datasets.0.dataset.grader.line=bus \
    batcher.curriculum.training_datasets.0.dataset.grader.topic_mode_or_user.0=${USER_0} \
    \
    ...dataset=HarmonyCompletionDatasetConfig \
    ...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only \
    ...harmony_constrained_sampling=True \
    defaults.sampler.interleave_channels.1=commentary \
    defaults.sampler.interleave_channels.0=analysis \
    defaults.sampler.n_ctx=$N_CTX \
    defaults.sampler.return_token_alternatives=None \
    \
    skip_validate_config_serdes=True \
    skip_validate_config=True \
    \
    defaults.inverse_token_cost=4096 \
    ...instance_completer.instance_optimizer.sample_preprocessor=qstar.optimizers.sample_preprocessors.token_costs:TokenCostPreprocessor \
    ...instance_completer.instance_objective.token_cost_objective=berry:UnusedObjective \
    ...instance_completer.instance_objective.main_objective=qstar.objectives.zero_objective:ZeroObjective \
    ...instance_completer=qstar.instance_completers:SimpleInstanceCompleter \
    ...instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer \
    ...instance_completer.instance_optimizer.reinforce_strategy.sample_selector=qstar.sample_selectors:SimpleSampleSelector \
    ...instance_completer.instance_optimizer.aux_loss_scale_sample_length_power=0 \
    ...instance_completer.instance_optimizer.aux_loss_scale_sample_length_clip_min=1 \
    ...max_num_yields=1000 \
    \
    defaults.target_samples_per_instance=${N_SAMPLE} \
    optimizer.rkld_alpha=1.0 \
    optimizer.kl_penalty_alpha=0.02 \
    optimizer.hparam_scaler.lr_per_instance_d16=$LR \
    \
    :berry_models.osmini:d36_qatmoe_fp8kv \
    policy.n_gpus=${TRAIN_GPUS} \
    policy.initial_checkpoint=${STUDENT} \
    policy.n_ctx=$N_CTX \
    defaults.max_tokens=$N_CTX \
    \
    root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False' \
    policy.sampling_ml_config='osmini_bf16_chicken_sampling_nown_moe_mxfp4_kv_fp8 ixf_cpu_kv_cache_ratio=0.0' \
    policy.model_config_name=falcon.leek.d36-s32-k4-bf16-v0_sesame_final-textonly-nownsimwn \
    policy.ml_config='osmini_bf16_falcon_training_nown_qat_only_moe' \
    policy=berry_tw.model_config:ModelConfig \
    policy.precision.moe=SetThroughMLConfig \
    ...layout=finetune-80g-os8-128k \
    \
    defaults.instances_per_batch=${BZ} \
    batch_completer.n_batches_in_flight=5 \
    peashooter.sampling_use_ev3=True \
    peashooter.kv_spilling=False \
    peashooter.sampling_concurrency=32 \
    peashooter.num_sampling_processes=16 \
    peashooter.timeout_seconds.stalled_datapoint=7200 \
    peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600 \
    peashooter.queue_granularity=2 \
    peashooter.num_instance_workers=64 \
    peashooter.distributed_sampling_config=peashooter.distributed_sampling.distributed_sampling_config:DistributedSamplingConfig \
    peashooter.distributed_sampling_config.load_balancing.max_kv_util=90.0 \
    peashooter.buffer_store_config_internal=beam_object_store \
    \
    security_profile=msft-orng \
    github_upload=False \
    wandb_enable=True \
    wandb.wandb_project=${WANDB_PROJECT} \
    kafka_enable=False \
    enable_slackbot=False \
    ...save_every=50
   