python ~/code/glass/personal/bolian/cloud-bus-shooter.py \
    --quota-team "team-moonfire-genaicore" \
    --engine "start-bus-gpt5-mini.sh" \
    --user "swe-main-run" \
    --snapshot_path "az://orngscuscresco/models/snapshots/zen-os4-nv4-cbv6-hh-rkld-4jul-orca-2-2025-07-10-00-04-decrypted" \
    --base_job_name "gpt5-mini-hpe3-rm" \
    --start_num 10 --end_num 30 \
    --cluster "prod-southcentralus-hpe-3" \
    --priority_class "team-critical" \
    --num_pods 1 \
    --num_gpu 8 \
    --nowait