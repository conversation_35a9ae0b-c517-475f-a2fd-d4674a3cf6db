python ~/code/glass/personal/bolian/cloud-bus-shooter.py \
    --quota-team "team-moonfire-genaicore" \
    --engine "./start-bus-gpt5.sh" \
    --user "gpt-oss-rkld" \
    --snapshot_path "az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas" \
    --base_job_name "gpt-oss-rkld-gpt5-rm" \
    --start_num 7 --end_num 8 \
    --cluster "prod-southcentralus-hpe-5" \
    --priority_class "team-critical" \
    --num_pods 2 \
    --num_gpu 8 \
    --nowait

# python ~/code/glass/personal/bolian/cloud-bus-shooter.py \
#     --quota-team "team-moonfire-genaicore" \
#     --engine "./start-bus-gpt5-mini.sh" \
#     --user "swe-main-run" \
#     --snapshot_path "az://orngscuscresco/models/snapshots/zen-os4-nv4-cbv6-hh-rkld-4jul-orca-2-2025-07-10-00-04-decrypted" \
#     --base_job_name "swe-sft-data" \
#     --start_num 1 --end_num 16 \
#     --cluster "prod-southcentralus-hpe-3" \
#     --priority_class "team-critical" \
#     --num_pods 1 \
#     --num_gpu 8 \
#     --nowait

# az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted/

# python cloud-bus-shooter.py \
#     --quota-team "team-moonfire-genaicore" \
#     --engine "start-bus-o3.sh" \
#     --user "swe-main-run" \
#     --snapshot_path "az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted" \
#     --base_job_name "o3-hpe2-rm" \
#     --start_num 1 --end_num 4 \
#     --cluster "prod-southcentralus-hpe-2" \
#     --priority_class "team-critical"

# python cloud-bus-shooter.py \
#     --quota-team "team-moonfire-genaicore" \
#     --engine "start-bus-o3.sh" \
#     --user "swe-main-run" \
#     --snapshot_path "az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted" \
#     --base_job_name "o3-hpe3-rm" \
#     --start_num 1 --end_num 8 \
#     --cluster "prod-southcentralus-hpe-3" \
#     --priority_class "team-critical"

# python bus_shooter.py \
#     --user "swe-main-run" \
#     --snapshot_path "az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted" \
#     --base_job_name "o3-hpe4-rm" \
#     --start_num 1 --end_num 20 \
#     --cluster "prod-southcentralus-hpe-4" \
#     --priority_class "team-mild"

# python cloud-bus-shooter.py \
#     --quota-team "team-moonfire-genaicore" \
#     --engine "start-bus-o3.sh" \
#     --user "swe-o3-grader-pdw" \
#     --snapshot_path "az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted" \
#     --base_job_name "o3-hpe2-rm" \
#     --start_num 5 --end_num 8 \
#     --cluster "prod-southcentralus-hpe-2" \
#     --priority_class "team-critical"

