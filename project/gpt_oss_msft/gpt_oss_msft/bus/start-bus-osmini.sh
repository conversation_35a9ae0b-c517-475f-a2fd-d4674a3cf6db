RUST_BACKTRACE=1 beam python -m chicken_inference_scripts.start_chicken_engine \
    snapshot_family=osmini \
    snapshot_name=d36_chicken_sampling \
    layout.n_expert_shards=1 layout.n_replicas=1 layout.n_op_shards=8 layout.pipe_depth=1 \
    snapshot_path="az://orngtransfer/ameinbound/snapshots/osmini-20250623-r16s110-safetys15-step-15-tc" \
    config_overrides=" simple_attn_sink=False soap=False combo_scale_all_timeouts=10 use_attn_global_scale=False use_moe_global_scale=False experts_output_scale=1.0 force_embedding_rmsnorm=False enable_snowflake_hook=False " \
    name=osmini-engine \
    cluster=local  2>&1 | tee ~/osmini_posttrained.log