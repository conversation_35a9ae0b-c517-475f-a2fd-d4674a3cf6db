#! /bin/bash
WANDB_PROJECT_NAME=gpt_oss_msft
JUICE=64
YIELDS=1000
# MAX_TOKENS=131072
MAX_TOKENS=262144

EVAL_INITIAL_POLICY=True
EVAL_EVERY=10

dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="oss-mini-rkld-sbv-ctx${MAX_TOKENS}-juice${JUICE}-yields${YIELDS}-$dt-evals-peaval"


# INITIAL_CHECKPOINT="az://orngtransfer/ameinbound/snapshots/osmini-20250623-r16s110-safetys15-step-15-tc"
INITIAL_CHECKPOINT="az://orngscuscresco/models/snapshots/osmini-unrl3-safetify-tc-20250815"
CHECKPOINT_DIR="az://orngscuscresco/twapi/mini/e/zhendongwang-gpt-oss-rkld-384x3-mix1-0828-0053/policy"
RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only"

CMD=(
# oaipkg run qstar.run_eval nostrict
beam python --start-daemon --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}

:berry_models.osmini:d36_qatmoe_fp8kv
policy.initial_checkpoint=${INITIAL_CHECKPOINT}
policy.is_multimodal=False
policy.layout=finetune-80g-os8-128k

root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=1
peashooter.num_sampling_processes=200
peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
peashooter.sampling_use_ev3=True
peashooter.buffer_store_config_internal=beam_object_store
peashooter.distributed_sampling_config=peashooter.distributed_sampling.distributed_sampling_config:DistributedSamplingConfig
peashooter.distributed_sampling_config.load_balancing.max_kv_util=90.0

# eval_settings.min_step=10
eval_settings.eval_every=${EVAL_EVERY}
eval_settings.exit_on_no_new_checkpoint=False
eval_settings.eval_initial_policy=${EVAL_INITIAL_POLICY}
eval_settings.checkpoint_dir=${CHECKPOINT_DIR}

# Inherit model params from training experiment!
auto_inherit_training_args=False
seed=47

metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

# sbv data
':deep_swe_eval_msft.swe_bench.peaval.oai.presets:ev_sm_bench_juice_oai(juice=64 override_target_samples_per_instance=1 max_num_yields=1000)'
...variant_producer.override_reward_multiplier=${JUICE}
...datapoint_type=gpt_oss_msft.data.datapoint:GpTossHarmonyCompletionDatapoint
...dataset.deprecated_knowledge_cutoff=2024-06-01
...dataset.conversation_start_date=2025-03-08

# avoid tool penalty errors
...instance_objective.aux_objective_0=caas_autograding.tool_penalty_objective:ToolPenaltyObjective

policy.n_ctx=${MAX_TOKENS}
defaults.n_ctx=${MAX_TOKENS}
...max_num_yields=${YIELDS}
defaults.sampler.harmony_constrained_sampling=True
defaults.channel_config.channels=analysis,final
defaults.sampler.interleave_channels.0=analysis
defaults.sampler.interleave_channels.1=commentary
...harmony_renderer_name=${RENDERER_NAME}
...encoding_name=orion_200k

# msft special
load.restore_from_all_clusters=False

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=${WANDB_PROJECT_NAME}
kafka_enable=False
enable_slackbot=False

)
GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval-ossmini-sbv-peaval.log