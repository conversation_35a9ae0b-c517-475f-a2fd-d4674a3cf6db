export WANDB_PROJECT_NAME=gpt_oss_msft
# export INIT_CKPT=az://orngtransfer/ameinbound/snapshots/osmini-20250623-r16s110-safetys15-step-15-tc
# export INIT_CKPT=az://orngscuscresco/models/snapshots/osmini-unrl3-safetify-tc-20250815
# export INIT_CKPT=az://orngscuscresco/twapi/mini/e/zhendongwang-gpt-oss-rkld-128x4-mix1-0822-1805/policy/step_000020/
# export INIT_CKPT=az://orngscuscresco/twapi/mini/e/zhendongwang-gpt-oss-rkld-384x3-mix1-0826-0611/policy/step_000035/
# export INIT_CKPT=az://orngscuscresco/twapi/mini/e/zhendongwang-gpt-oss-rkld-384x3-mix1-0828-0053/policy/step_000030/
export INIT_CKPT=az://orngscuscresco/twapi/mini/e/chenliang1-gpt-oss-rkld-teacherv4-256x2-0829-1900/policy/step_000015/
# export INIT_CKPT=az://orngscuscresco/twapi/mini/e/zhendongwang-gpt-oss-rkld-train_rkld_mix1-384x3-0901-0237/policy/step_000050/


LR=2e-6
N_CTX=131072 # 272144
TRAIN_GPUS=128
IPB=32
SPI=32
ITC=1536

PRESET=train_sbv_mix1

CMD=(
beam python --use-cwd -m qstar.run_experiment nostrict
name=gpt-oss-rl-rkld-${PRESET}-ITC${ITC}-${IPB}x${SPI}-$(date +%m%d-%H%M)
seed=20250501

skip_validate_config_serdes=True
skip_validate_config=True

:berry_models.osmini:d36_qatmoe_fp8kv
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
# policy.sampling_ml_config='osmini_bf16_chicken_sampling_nown_moe_mxfp4_kv_fp8'
# policy.model_config_name=falcon.leek.d36-s32-k4-bf16-v0_sesame_final-textonly-nownsimwn
# policy.ml_config='osmini_bf16_falcon_training_nown_qat_only_moe'
# policy=berry_tw.model_config:ModelConfig
# policy.precision.moe=SetThroughMLConfig
# ...layout=finetune-80g-os8-128k

policy.n_gpus=${TRAIN_GPUS}
policy.initial_checkpoint=${INIT_CKPT}
# policy.n_ctx=$N_CTX
defaults.n_ctx=$N_CTX
berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=5

...dataset=HarmonyCompletionDatasetConfig
...datapoint_type=gpt_oss_msft.data.datapoint:GpTossHarmonyCompletionDatapoint
...sampler=gpt_oss_msft.data.sampler:OsminiSampler
:gpt_oss_msft.presets:${PRESET}
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only
...harmony_constrained_sampling=True
# ...harmony_constrained_sampling_with_prob=0.1
# defaults.channel_config.channels=analysis,commentary,final
defaults.sampler.interleave_channels.1=commentary
defaults.sampler.interleave_channels.0=analysis
# defaults.sampler.n_ctx=$N_CTX
defaults.sampler.return_token_alternatives=32
...average_reward_maximum=0.99
...tool_penalty_multiplier=0.1
...adaptive_overtrain_factor=1.5
# ...rkld_throw_away_if_teacher_max_len=False

...training_mode='rl'
# ...prompt_mode='hint' # hard coded in presets
...dataset.variant_producer=VarDiscountingVariantProducer
...dataset.variant_producer.override_reward_multiplier=8,64,512

defaults.inverse_token_cost=$ITC
defaults.instances_per_batch=${IPB}
defaults.target_samples_per_instance=${SPI}

...instance_completer=qstar.instance_completers:VariantsInstanceCompleter
...instance_completer.num_initial_samples=12
...instance_completer.min_initial_samples_per_variant=3
...instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer
# ...instance_completer.instance_optimizer.sample_preprocessor=qstar.optimizers.sample_preprocessors.token_costs:TokenCostPreprocessor
# ...instance_completer.instance_objective.token_cost_objective=berry:UnusedObjective
# ...instance_completer.instance_optimizer.aux_loss_scale_sample_length_power=0
# ...instance_completer.instance_optimizer.aux_loss_scale_sample_length_clip_min=1
...instance_objective.aux_objective_0=caas_autograding.tool_penalty_objective:ToolPenaltyObjective
# ...instance_completer.instance_objective.aux_objective_1=qstar.objectives.format_objective:FormatObjective
# ...instance_completer.instance_objective.aux_objective_1.format_errors="reached_max_tokens,reached_max_num_yields"
...max_num_yields=256
# ...yield_cost=0.005
...save_every=5


...instance_completer.instance_objective.main_objective.c=0.8
...instance_completer.instance_objective.main_objective.d=0.8
# batcher.curriculum.training_datasets.0.dataset.inverse_token_cost_multiplier=8
# batcher.curriculum.training_datasets.0.dataset.instance_completer.instance_objective.main_objective.c=0
# batcher.curriculum.training_datasets.0.dataset.instance_completer.instance_objective.main_objective.d=1
# batcher.curriculum.training_datasets.0.dataset.inverse_token_cost_multiplier=16
# batcher.curriculum.training_datasets.1.dataset.inverse_token_cost_multiplier=16

# optimizer.rkld_alpha=0.003
# optimizer.kl_penalty_alpha=0
optimizer.hparam_scaler.lr_per_instance_d16=$LR

batch_completer.n_batches_in_flight=16
# batch_completer.batch_size_schedule=StepBasedLinearWarmup
# batch_completer.batch_size_schedule.initial_instances_per_batch=128
# batch_completer.batch_size_schedule.num_iterations=16

# peashooter.sampling_use_ev3=True # already inside the berry_models
peashooter.kv_spilling=False
peashooter.sampling_concurrency=16
peashooter.num_sampling_processes=32
peashooter.timeout_seconds.stalled_datapoint=7200
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
# peashooter.queue_granularity=2
peashooter.num_instance_workers=64
peashooter.distributed_sampling_config=peashooter.distributed_sampling.distributed_sampling_config:DistributedSamplingConfig
peashooter.distributed_sampling_config.load_balancing.max_kv_util=90.0
peashooter.buffer_store_config_internal=beam_object_store

security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=${WANDB_PROJECT_NAME}
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}" 2>&1 | tee -a train.log

