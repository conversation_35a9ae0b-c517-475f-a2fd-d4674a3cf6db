#! /bin/bash
WANDB_PROJECT_NAME=gpt_oss_msft
# JUICE=512
JUICE=200
# YIELDS=512
YIELDS=400
# MAX_TOKENS=131072
# MAX_TOKENS=262144
MAX_TOKENS=400000

EVAL_INITIAL_POLICY=True
EVAL_EVERY=1

dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="oss-gpt5-sbv-ctx${MAX_TOKENS}-juice${JUICE}-yields${YIELDS}-$dt-evals-peaval"

INITIAL_CHECKPOINT="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"
CMD=(
# oaipkg run qstar.run_eval nostrict
beam python --start-daemon --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}

:berry_models.scallion:d64_chicken_80g_bf16
policy.initial_checkpoint=${INITIAL_CHECKPOINT}
policy.is_multimodal=True

root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0}'

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=1
peashooter.num_sampling_processes=100
peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
peashooter.sampling_use_ev3=True

eval_settings.eval_every=${EVAL_EVERY}
eval_settings.exit_on_no_new_checkpoint=True
eval_settings.eval_initial_policy=${EVAL_INITIAL_POLICY}
eval_settings.checkpoint_dir=${INITIAL_CHECKPOINT}

# Inherit model params from training experiment!
auto_inherit_training_args=False
seed=47

metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

#############################
## For GPT5
# use truncation -> max 256 lines, 256 per line
...tool_configs.container_tool_config.exec_output_processor_path=gpt_oss_msft.tools.truncate:TruncateExecPostProcessor
# sbv data, using gpt5 prompt format
':deep_swe_eval_msft.swe_bench.peaval.oai.presets:ev_sm_bench_juice_oai_gpt5(juice=200 override_target_samples_per_instance=1 max_num_yields=400)'
##############################

...variant_producer.override_reward_multiplier=${JUICE}
# ...datapoint_type=gpt_oss_msft.data.datapoint:GpTossHarmonyCompletionDatapoint
...dataset.deprecated_knowledge_cutoff=2024-06-01
...dataset.conversation_start_date=2025-03-08

# avoid tool penalty errors
...instance_objective.aux_objective_0=caas_autograding.tool_penalty_objective:ToolPenaltyObjective

policy.n_ctx=${MAX_TOKENS}
defaults.n_ctx=${MAX_TOKENS}
...max_num_yields=${YIELDS}
defaults.sampler.harmony_constrained_sampling=True
defaults.channel_config.channels=analysis,final
defaults.sampler.interleave_channels.0=analysis
defaults.sampler.interleave_channels.1=commentary
...harmony_renderer_name=${RENDERER_NAME}
...encoding_name=orion_200k

...tools_format_version=v2

# Set to True if you want to evaluate step0 (your prior)
eval_settings.eval_initial_policy=True

# msft special
load.restore_from_all_clusters=False

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=${WANDB_PROJECT_NAME}
kafka_enable=False
enable_slackbot=False

)
GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval-ossmini-sbv-peaval.log