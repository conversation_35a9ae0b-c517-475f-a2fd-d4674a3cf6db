dt=`date '+%m%d'`

# CLUSTER=prod-uksouth-7
# CLUSTER=prod-uksouth-8
# CLUSTER=prod-southcentralus-hpe-2
CLUSTER=prod-southcentralus-hpe-3
# CLUSTER=prod-southcentralus-hpe-4
CLUSTER=prod-southcentralus-hpe-5
# CLUSTER=prod-australiaeast-8

PRIORITY=team-critical
# PRIORITY=team-high
# PRIORITY=team-mild
# PRIORITY=team-low

PS=(
python -m qstar.peashooter.run_peashooter

...rapid_id=gpt-oss-eval-hpe5
rapid.cluster.name=$CLUSTER
rapid.cluster.priority=$PRIORITY
rapid.pull_git_on_restart=False

num_controller_nodes=1
n_gpus_per_sampler=8
sampling_jobs=3
n_clip_pods=0
use_beam_object_store=True
cpu_controller=False
n_train_gpus=0
eval_only=True

security_profile=msft-orng
strict=True
)

BRIX_QUOTA="team-moonfire-genaicore" "${PS[@]}"

