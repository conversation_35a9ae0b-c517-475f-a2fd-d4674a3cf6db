CAAS_CONTAINER_COREUTILS_MANUAL = """
Regular UNIX commands and common utilities (like python) are available.
Other than those, there's an additional `apply_patch` tool.

```
apply_patch <patch_content>             Applies a patch to the filesystem.
    - Only one argument <patch_content> is allowed in this command -- no flags or options. The whole patch must be passed as a single string argument.
    - Patch content must be a string beginning with "*** Begin Patch" and ending with "*** End Patch", e.g:
        Updating a file: {"cmd":["apply_patch","*** Begin Patch\n*** Update File: path/to/file.py\n@@ def example():\n-  pass\n+  return 123\n*** End Patch"]}
        Adding a file: {"cmd":["apply_patch","*** Begin Patch\n*** Add File: file_to_add.py\n+if __name__ == '__main__':\n+  print(1234)\n*** End Patch"]}
        Deleting a file: {"cmd":["apply_patch","*** Begin Patch\n*** Delete File: file_to_delete.py\n*** End Patch"]}
```
""".strip()

CAAS_CONTAINER_COREUTILS_MANUAL_V2 = """
Regular UNIX commands and common utilities (like python) are available.

Additionally, there are three python-based tools: `apply_patch`,`easy_rg` and `read_file`. Prioritize to use the new python-tools, instead of the linux `ls -R`, `grep`, `sed` and `find` commands.
 - easy_rg can help show project structure, find patterns in files quickly. -> Replace `grep`, `ls -R`, `find`
 - read_file can be used to read the contents of a file. -> Replace `cat`, `sed`
""".strip()
