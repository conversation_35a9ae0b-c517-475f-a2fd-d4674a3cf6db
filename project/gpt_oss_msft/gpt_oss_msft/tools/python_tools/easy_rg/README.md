# Easy RG - Enhanced Ripgrep Wrapper

A Python wrapper for ripgrep (`rg`) with enhanced formatting, output control, and user-friendly features.

## Features

- 🎯 **Smart truncation**: Preserves search patterns in the center when truncating long lines
- 📁 **Structured output**: Well-formatted project structure with folders, files, and line numbers
- 🔢 **Configurable limits**: Control the number of files per folder and lines per file
- 📋 **Project overview**: Show project structure when called without arguments
- 🎛️ **Flexible configuration**: Customize behavior for different use cases
- 🔍 **All ripgrep features**: Pass through any ripgrep arguments

## Installation

First, ensure you have ripgrep installed:

```bash
# macOS
brew install ripgrep

# Ubuntu/Debian
sudo apt install ripgrep

# Other systems: see https://github.com/BurntSushi/ripgrep
```

Then use the Python module (no additional installation needed).

## Usage

### Basic Usage

```python
from easy_rg import easy_rg

# Show project structure
result = easy_rg()
print(result)

# Search for a pattern
result = easy_rg("function")
print(result)

# Search in specific directory
result = easy_rg("TODO", "src/")
print(result)
```

### Advanced Usage

```python
from easy_rg import EasyRg

# Custom configuration
rg = EasyRg(
    max_files_per_folder=10,    # Show max 10 files per folder
    max_lines_per_file=15,      # Show max 15 lines per file
    max_line_length=100,        # Truncate lines longer than 100 chars
    project_structure_files_limit=5  # Show max 5 files in structure view
)

# Search with custom config
result = rg.search("pattern", ".")
print(result)

# Pass ripgrep arguments
result = rg.search("Pattern", ".", **{
    "--ignore-case": True,
    "--type": "py",
    "--glob": "!tests/*"
})
print(result)
```

### Command Line Usage

```bash
# Show project structure
python easy_rg_cli.py

# Search for pattern
python easy_rg_cli.py "function"

# Search in specific directory
python easy_rg_cli.py "TODO" src/

# With options
python easy_rg_cli.py --max-files 5 --ignore-case "Pattern"

# Show help
python easy_rg_cli.py -h
```

## Output Format

### Project Structure Mode
```
📁 Project Structure: your-project
==================================

📁 ./
  📄 README.md
  📄 setup.py
  📄 requirements.txt

📁 src/
  📄 main.py
  📄 utils.py
  ... [3 more files] ...

📁 tests/
  📄 test_main.py
  📄 test_utils.py
```

### Search Results Mode
```
📁 src/
────────

  📄 main.py
      15: def main_function():
      23:     helper_function()
      
  📄 utils.py
      8: def helper_function():
      12:     return function_result()
      ... [5 more lines] ...

  ... [3 more files in this directory] ...

📁 tests/
─────────

  📄 test_main.py
      10: def test_main_function():
      15:     assert main_function() is not None
```

## Configuration Options

| Parameter | Default | Description |
|-----------|---------|-------------|
| `max_files_per_folder` | 20 | Maximum files to show per directory |
| `max_lines_per_file` | 20 | Maximum lines to show per file |
| `max_line_length` | 128 | Maximum characters per line before truncation |
| `project_structure_files_limit` | 10 | Files to show in project structure mode |

## Line Truncation

When lines exceed `max_line_length`, they are intelligently truncated:

- **With search pattern**: Pattern is preserved in the center
- **Without pattern**: Center truncation with context on both sides
- **Truncation indicators**: `...` shows where content was removed

Example:
```
Original: "This is a very long line with the search pattern that exceeds the limit"
Truncated: "...long line with the search pattern that exc..."
```

## Ripgrep Integration

All ripgrep arguments are supported. Common examples:

```python
# Case insensitive search
easy_rg("pattern", ".", **{"--ignore-case": True})

# Search specific file types
easy_rg("function", ".", **{"--type": "py"})

# Use glob patterns
easy_rg("TODO", ".", **{"--glob": "*.py", "--glob": "!tests/*"})

# Word boundaries
easy_rg("var", ".", **{"--word-regexp": True})

# Include hidden files
easy_rg("config", ".", **{"--hidden": True})
```

## Testing

Run the test suite:

```bash
python -m pytest tools/tests/test_easy_rg.py -v
```

Or run the demo:

```bash
python tools/easy_rg_demo.py
```

## Examples

### Finding Function Definitions
```python
# Find all Python function definitions
result = easy_rg("def ", ".", **{"--type": "py"})
```

### Project Overview
```python
# Get a quick overview of project structure
overview = easy_rg()  # No arguments = project structure
```

### Case-Insensitive TODO Search
```python
# Find all TODO comments, case insensitive
todos = easy_rg("todo", ".", **{"--ignore-case": True})
```

### Search in Specific File Types
```python
# Search only in Python files
python_matches = easy_rg("import", ".", **{"--type": "py"})

# Search only in markdown files
docs = easy_rg("API", ".", **{"--type": "md"})
```

## Error Handling

The module gracefully handles common errors:

- **Ripgrep not installed**: Clear error message with installation instructions
- **No matches found**: Returns "No matches found." message
- **Invalid paths**: Ripgrep error messages are passed through
- **Permission errors**: Handled by ripgrep's built-in error handling

## Files

- `easy_rg.py` - Main module with EasyRg class and convenience function
- `easy_rg_cli.py` - Command-line interface
- `easy_rg_demo.py` - Demo script showcasing features
- `tests/test_easy_rg.py` - Comprehensive test suite

## Requirements

- Python 3.6+
- ripgrep (`rg`) installed and in PATH
- Standard library only (no additional Python dependencies)

## License

This project is provided as-is for educational and development purposes.