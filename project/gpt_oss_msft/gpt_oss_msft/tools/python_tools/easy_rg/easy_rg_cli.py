#!/usr/bin/env python3
"""
Command-line interface for easy_rg.

Usage:
    easy_rg_cli.py                    # Show project structure
    easy_rg_cli.py "pattern"          # Search for pattern
    easy_rg_cli.py "pattern" path     # Search for pattern in specific path
    easy_rg_cli.py -h                 # Show help
"""

import argparse
import sys
from pathlib import Path

# Add the parent directory to path to import easy_rg
sys.path.insert(0, str(Path(__file__).parent))

from easy_rg import easy_rg, EasyRg


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Easy ripgrep wrapper with enhanced formatting and output control. "
                   "By default, searches treat patterns as literal strings (special regex characters are escaped automatically). "
                   "Use --regex to enable regex pattern matching.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                           # Show project structure (depth=3, max-files=10)
  %(prog)s "function"                # Search for 'function' in current directory
  %(prog)s "TODO" src/               # Search for 'TODO' in src/ directory
  %(prog)s -i "Function"             # Case-insensitive search
  %(prog)s --max-files 10 "pattern" # Limit to 10 files per folder
  %(prog)s --depth 3 "pattern"      # Search only 3 levels deep
  %(prog)s "rg_args['--hidden']"     # Search for literal text (special chars handled automatically)
  %(prog)s --regex "rg_args\\['.*'\\]" # Use regex patterns when needed
        """
    )
    
    parser.add_argument(
        '--pattern',
        nargs='?',
        help='Search pattern (if not provided, shows project structure)'
    )
    
    parser.add_argument(
        '--path',
        type=str,
        default='.',
        help='Path to search in (default: current directory)'
    )
    
    # Configuration options
    parser.add_argument(
        '--max-files',
        type=int,
        default=20,
        help='Maximum files to show per folder (default: 20)'
    )
    
    parser.add_argument(
        '--max-lines',
        type=int,
        default=20,
        help='Maximum lines to show per file (default: 20)'
    )
    
    parser.add_argument(
        '--max-length',
        type=int,
        default=128,
        help='Maximum characters per line before truncation (default: 128)'
    )
    
    parser.add_argument(
        '--project-files',
        type=int,
        default=10,
        help='Files to show in project structure mode (default: 10)'
    )
    
    parser.add_argument(
        '--depth',
        type=int,
        help='Maximum depth for recursive search (default: unlimited for search, 2 for project structure)'
    )
    
    # Ripgrep options
    parser.add_argument(
        '-i', '--ignore-case',
        action='store_true',
        help='Case insensitive search'
    )
    
    parser.add_argument(
        '-w', '--word-regexp',
        action='store_true',
        help='Only show matches surrounded by word boundaries'
    )
    
    parser.add_argument(
        '-v', '--invert-match',
        action='store_true',
        help='Invert matching, show non-matching lines'
    )
    
    parser.add_argument(
        '-t', '--type',
        help='Only search files matching TYPE'
    )
    
    parser.add_argument(
        '-g', '--glob',
        help='Include or exclude files and directories for searching'
    )
    
    parser.add_argument(
        '--hidden',
        action='store_true',
        help='Search hidden files and directories'
    )
    
    parser.add_argument(
        '--regex',
        action='store_true',
        help='Treat pattern as regex (default: literal string search)'
    )
    
    parser.add_argument(
        '--fixed-strings',
        action='store_true',
        help='Treat pattern as literal string (overrides --regex)'
    )
    
    args = parser.parse_args()
    
    try:
        # Handle project structure mode with specific defaults
        # For search mode: use user-specified values
        depth = args.depth
        max_files = args.max_files
        
        # Create EasyRg instance with configuration
        rg = EasyRg(
            max_files_per_folder=max_files,
            max_lines_per_file=args.max_lines,
            max_line_length=args.max_length,
            project_structure_files_limit=args.project_files
        )
        
        # Build ripgrep arguments
        rg_args = {}
        if args.ignore_case:
            rg_args['--ignore-case'] = True
        if args.word_regexp:
            rg_args['--word-regexp'] = True
        if args.invert_match:
            rg_args['--invert-match'] = True
        if args.type:
            rg_args['--type'] = args.type
        if args.glob:
            rg_args['--glob'] = args.glob
        if args.hidden:
            rg_args['--hidden'] = True
        
        # Handle literal vs regex search
        # By default, treat patterns as literal strings unless --regex is specified
        if args.fixed_strings or (not args.regex):
            rg_args['--fixed-strings'] = True
        
        # Add depth restriction if specified
        if depth is not None:
            rg_args['--max-depth'] = depth
        
        # Perform search
        result = rg.search(args.pattern, args.path, **rg_args)
        print(result)
        
    except RuntimeError as e:
        print(f"PYTHON-TOOL ERROR: {e}", file=sys.stderr)
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nInterrupted by user", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"PYTHON-TOOL ERROR: Unexpected error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()