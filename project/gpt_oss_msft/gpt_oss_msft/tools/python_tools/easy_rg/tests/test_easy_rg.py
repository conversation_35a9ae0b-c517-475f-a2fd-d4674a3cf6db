#!/usr/bin/env python3
"""
Test suite for easy_rg module.
"""

import unittest
import tempfile
import os
from pathlib import Path
import sys

# Add the easy_rg directory to the path so we can import easy_rg
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from easy_rg import EasyRg, easy_rg


class TestEasyRg(unittest.TestCase):
    """Test cases for EasyRg class."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # Create test file structure
        self._create_test_files()
        
        # Initialize EasyRg instance
        self.easy_rg = EasyRg(
            max_files_per_folder=3,
            max_lines_per_file=5,
            max_line_length=50,
            project_structure_files_limit=3
        )
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        # Clean up test directory
        import shutil
        shutil.rmtree(self.test_dir)
    
    def _create_test_files(self):
        """Create test files for testing."""
        # Create directory structure
        os.makedirs('src/utils', exist_ok=True)
        os.makedirs('tests', exist_ok=True)
        os.makedirs('docs', exist_ok=True)
        
        # Create test files with content
        test_files = {
            'README.md': """# Test Project
This is a test project for easy_rg functionality.
It contains various patterns to search for.
The word 'function' appears multiple times.
Another function definition here.
""",
            'src/main.py': """#!/usr/bin/env python3
def main():
    print("Hello, world!")
    function_call()
    
def function_call():
    return "This is a function"
    
def another_function():
    # This function does something
    pass
""",
            'src/utils/helpers.py': """def helper_function():
    # A very long line that should be truncated because it exceeds the maximum length limit set in the configuration
    return "helper"
    
def another_helper():
    function = "variable named function"
    return function
""",
            'tests/test_main.py': """import unittest
from src.main import function_call

class TestMain(unittest.TestCase):
    def test_function_call(self):
        result = function_call()
        self.assertEqual(result, "This is a function")
""",
            'docs/api.md': """# API Documentation

## Functions

### function_call()
This function returns a string.

### helper_function()
This function helps with things.
""",
            # Additional files to test truncation
            'file1.txt': 'content1',
            'file2.txt': 'content2',
            'file3.txt': 'content3',
            'file4.txt': 'content4',
            'file5.txt': 'content5',
        }
        
        for file_path, content in test_files.items():
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            file_path.write_text(content)
    
    def test_line_truncation(self):
        """Test line truncation functionality."""
        long_line = "This is a very long line that should be truncated when it exceeds the maximum length"
        pattern = "very long"
        
        truncated = self.easy_rg._truncate_line(long_line, pattern)
        
        # Should preserve the pattern and be within length limit
        self.assertLessEqual(len(truncated), self.easy_rg.max_line_length)
        self.assertIn(pattern, truncated)
        self.assertTrue("..." in truncated)
    
    def test_line_truncation_without_pattern(self):
        """Test line truncation without specific pattern."""
        long_line = "A" * 100  # Long line without specific pattern
        
        truncated = self.easy_rg._truncate_line(long_line)
        
        self.assertLessEqual(len(truncated), self.easy_rg.max_line_length)
        self.assertTrue(truncated.startswith("A"))
        self.assertTrue(truncated.endswith("A"))
        if len(long_line) > self.easy_rg.max_line_length:
            self.assertIn("...", truncated)
    
    def test_short_line_no_truncation(self):
        """Test that short lines are not truncated."""
        short_line = "This is short"
        
        result = self.easy_rg._truncate_line(short_line)
        
        self.assertEqual(result, short_line)
    
    def test_project_structure(self):
        """Test project structure display."""
        result = self.easy_rg._get_project_structure()
        
        # Should contain project structure elements
        self.assertIn("Project Structure", result)
        self.assertIn("📁", result)  # Folder emoji
        self.assertIn("📄", result)  # File emoji
        
        # Should show some of our test files
        self.assertIn("README.md", result)
        self.assertIn("src/", result)
    
    def test_search_functionality(self):
        """Test basic search functionality."""
        # Skip this test if rg is not available
        try:
            result = self.easy_rg.search("function")
            
            # Should find matches
            self.assertNotEqual(result, "No matches found.")
            self.assertIn("📁", result)  # Should have directory structure
            self.assertIn("📄", result)  # Should have file names
            
        except RuntimeError as e:
            if "ripgrep" in str(e):
                self.skipTest("ripgrep not available for testing")
            raise
    
    def test_search_no_matches(self):
        """Test search with no matches."""
        try:
            result = self.easy_rg.search("nonexistentpattern12345")
            self.assertEqual(result, "No matches found.")
        except RuntimeError as e:
            if "ripgrep" in str(e):
                self.skipTest("ripgrep not available for testing")
            raise
    
    def test_convenience_function(self):
        """Test the convenience function."""
        try:
            # Test project structure
            result = easy_rg()
            self.assertIn("Project Structure", result)
            
            # Test search
            result = easy_rg("function")
            if result != "No matches found.":
                self.assertIn("📁", result)
        except RuntimeError as e:
            if "ripgrep" in str(e):
                self.skipTest("ripgrep not available for testing")
            raise
    
    def test_configuration_parameters(self):
        """Test that configuration parameters are properly set."""
        custom_rg = EasyRg(
            max_files_per_folder=5,
            max_lines_per_file=10,
            max_line_length=80,
            project_structure_files_limit=15
        )
        
        self.assertEqual(custom_rg.max_files_per_folder, 5)
        self.assertEqual(custom_rg.max_lines_per_file, 10)
        self.assertEqual(custom_rg.max_line_length, 80)
        self.assertEqual(custom_rg.project_structure_files_limit, 15)


class TestIntegration(unittest.TestCase):
    """Integration tests for the easy_rg functionality."""
    
    def test_command_line_usage(self):
        """Test command line usage scenarios."""
        # This would be tested by actually running the script
        # For now, we'll just test that the module can be imported
        from easy_rg import easy_rg
        self.assertTrue(callable(easy_rg))
    
    def test_with_additional_rg_args(self):
        """Test passing additional ripgrep arguments."""
        try:
            rg = EasyRg()
            # Test with case-insensitive search
            result = rg.search("FUNCTION", ".", **{"--ignore-case": True})
            # If rg is available, this should work
            self.assertIsInstance(result, str)
        except RuntimeError as e:
            if "ripgrep" in str(e):
                self.skipTest("ripgrep not available for testing")
            raise


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)