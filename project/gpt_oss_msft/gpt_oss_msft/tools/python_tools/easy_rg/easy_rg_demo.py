#!/usr/bin/env python3
"""
Demo script for easy_rg functionality.
"""

import sys
from pathlib import Path

# Add the tools directory to path
sys.path.insert(0, str(Path(__file__).parent))

from easy_rg import easy_rg, EasyRg


def demo_basic_usage():
    """Demonstrate basic usage of easy_rg."""
    print("=" * 60)
    print("🔍 Easy RG Demo - Basic Usage")
    print("=" * 60)
    
    print("\n1. Project Structure (no arguments):")
    print("-" * 40)
    try:
        result = easy_rg()
        print(result)
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n2. Search for 'def' (Python function definitions):")
    print("-" * 50)
    try:
        result = easy_rg("def")
        print(result)
    except Exception as e:
        print(f"Error: {e}")


def demo_advanced_features():
    """Demonstrate advanced features."""
    print("\n" + "=" * 60)
    print("🔍 Easy RG Demo - Advanced Features")
    print("=" * 60)
    
    # Custom configuration
    custom_rg = EasyRg(
        max_files_per_folder=5,
        max_lines_per_file=3,
        max_line_length=60,
        project_structure_files_limit=5
    )
    
    print("\n1. Custom Configuration (fewer results):")
    print("-" * 45)
    try:
        result = custom_rg.search("import")
        print(result)
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n2. Case-insensitive search:")
    print("-" * 30)
    try:
        result = custom_rg.search("IMPORT", ".", **{"--ignore-case": True})
        print(result)
    except Exception as e:
        print(f"Error: {e}")


def demo_line_truncation():
    """Demonstrate line truncation feature."""
    print("\n" + "=" * 60)
    print("🔍 Easy RG Demo - Line Truncation")
    print("=" * 60)
    
    rg = EasyRg(max_line_length=50)
    
    # Test line truncation
    test_lines = [
        "This is a short line",
        "This is a very long line that will definitely be truncated because it exceeds the maximum length limit",
        "Another long line with the word 'pattern' in the middle that should be preserved during truncation"
    ]
    
    print("\nOriginal lines and their truncated versions:")
    print("-" * 50)
    
    for i, line in enumerate(test_lines, 1):
        truncated = rg._truncate_line(line, "pattern" if "pattern" in line else None)
        print(f"\n{i}. Original ({len(line)} chars):")
        print(f"   {line}")
        print(f"   Truncated ({len(truncated)} chars):")
        print(f"   {truncated}")


def main():
    """Run the demo."""
    print("🚀 Welcome to Easy RG Demo!")
    print("This demo will showcase the features of the easy_rg wrapper.")
    
    try:
        demo_basic_usage()
        demo_advanced_features()
        demo_line_truncation()
        
        print("\n" + "=" * 60)
        print("✅ Demo completed!")
        print("=" * 60)
        print("\nTo use easy_rg in your code:")
        print("  from easy_rg import easy_rg")
        print("  result = easy_rg('your_pattern')")
        print("\nTo use from command line:")
        print("  python easy_rg_cli.py 'your_pattern'")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("\nNote: This demo requires ripgrep (rg) to be installed.")
        print("Install with: brew install ripgrep (macOS) or your package manager.")


if __name__ == '__main__':
    main()