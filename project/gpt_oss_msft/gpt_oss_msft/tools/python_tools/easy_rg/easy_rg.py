#!/usr/bin/env python3
"""
easy_rg: A Python wrapper for ripgrep (rg) with enhanced formatting and output control.

This module provides an easier-to-use interface for ripgrep with:
- Better length control and output efficiency
- Well-formatted project structure display
- Configurable result limits per folder and file
- Intelligent truncation for long lines and large result sets
"""

import os
import subprocess
import json
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import re


class EasyRg:
    """A Python wrapper for ripgrep with enhanced formatting and output control."""
    
    def __init__(self, max_files_per_folder: int = 20, max_lines_per_file: int = 20, 
                 max_line_length: int = 128, project_structure_files_limit: int = 10):
        """
        Initialize EasyRg with configuration parameters.
        
        Args:
            max_files_per_folder: Maximum files to show per folder (default: 20)
            max_lines_per_file: Maximum lines to show per file (default: 20)
            max_line_length: Maximum characters per line before truncation (default: 128)
            project_structure_files_limit: Files to show when displaying project structure (default: 10)
        """
        self.max_files_per_folder = max_files_per_folder
        self.max_lines_per_file = max_lines_per_file
        self.max_line_length = max_line_length
        self.project_structure_files_limit = project_structure_files_limit
        
        # Check if rg is available
        if not self._check_rg_available():
            raise RuntimeError("ripgrep (rg) is not installed or not in PATH")
    
    def _check_rg_available(self) -> bool:
        """Check if ripgrep is available in the system."""
        try:
            subprocess.run(['rg', '--version'], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _truncate_line(self, line: str, pattern: str = None) -> str:
        """
        Truncate a line to max_line_length, preserving the pattern in the center if possible.
        
        Args:
            line: The line to truncate
            pattern: The search pattern to preserve (optional)
            
        Returns:
            Truncated line with pattern preserved in center if possible
        """
        if len(line) <= self.max_line_length:
            return line
        
        if pattern and pattern in line:
            # Find pattern position and try to center it
            pattern_start = line.find(pattern)
            pattern_end = pattern_start + len(pattern)
            
            # Calculate how much space we have for context around the pattern
            available_space = self.max_line_length - len(pattern) - 6  # 6 for "..." on both sides
            left_context = available_space // 2
            right_context = available_space - left_context
            
            # Calculate start and end positions
            start = max(0, pattern_start - left_context)
            end = min(len(line), pattern_end + right_context)
            
            # Build truncated line
            result = ""
            if start > 0:
                result += "..."
            result += line[start:end]
            if end < len(line):
                result += "..."
                
            return result
        else:
            # Simple center truncation
            if len(line) <= self.max_line_length:
                return line
            
            keep_length = self.max_line_length - 6  # 6 for "..." on both sides
            start_length = keep_length // 2
            end_length = keep_length - start_length
            
            return line[:start_length] + "..." + line[-end_length:]
    
    def _format_search_results(self, results: List[Dict], pattern: str = None) -> str:
        """
        Format search results into a well-structured output.
        
        Args:
            results: List of search result dictionaries
            pattern: The search pattern used
            
        Returns:
            Formatted string output
        """
        if not results:
            return "No matches found."
        
        # Group results by directory
        dir_groups = {}
        for result in results:
            file_path = result['data']['path']['text']
            dir_path = str(Path(file_path).parent)
            
            if dir_path not in dir_groups:
                dir_groups[dir_path] = {}
            
            if file_path not in dir_groups[dir_path]:
                dir_groups[dir_path][file_path] = []
            
            dir_groups[dir_path][file_path].append(result)
        
        output = []
        total_dirs = len(dir_groups)
        
        for dir_path in sorted(dir_groups.keys()):
            files_in_dir = dir_groups[dir_path]
            
            # Directory header
            output.append(f"\n📁 {dir_path}/")
            output.append("─" * (len(dir_path) + 4))
            
            # Limit files per directory
            file_paths = sorted(files_in_dir.keys())
            if len(file_paths) > self.max_files_per_folder:
                displayed_files = file_paths[:self.max_files_per_folder]
                truncated_count = len(file_paths) - self.max_files_per_folder
                file_paths = displayed_files
                show_truncation_msg = True
            else:
                show_truncation_msg = False
            
            for file_path in file_paths:
                file_results = files_in_dir[file_path]
                file_name = Path(file_path).name
                
                # File header
                output.append(f"  📄 {file_name}")
                
                # Limit lines per file
                if len(file_results) > self.max_lines_per_file:
                    # Show first half and last half
                    half = self.max_lines_per_file // 2
                    displayed_results = file_results[:half] + file_results[-half:]
                    show_line_truncation = True
                    truncated_lines = len(file_results) - self.max_lines_per_file
                else:
                    displayed_results = file_results
                    show_line_truncation = False
                    truncated_lines = 0
                
                for i, result in enumerate(displayed_results):
                    if show_line_truncation and i == half:
                        output.append(f"      ... [{truncated_lines} more lines] ...")
                    
                    line_data = result['data']
                    line_number = line_data['line_number']
                    line_text = line_data['lines']['text'].strip()
                    
                    # Truncate line if needed
                    truncated_line = self._truncate_line(line_text, pattern)
                    
                    output.append(f"      {line_number:4d}: {truncated_line}")
            
            if show_truncation_msg:
                output.append(f"  ... [{truncated_count} more files in this directory] ...")
        
        return "\n".join(output)
    
    def _get_project_structure(self, path: str = ".", **rg_args) -> str:
        """
        Get the project structure of the given path.
        
        Args:
            path: Path to analyze (default: current directory)
            **rg_args: Additional arguments to pass to ripgrep (e.g., --max-depth)
            
        Returns:
            Formatted project structure
        """
        output = []
        root_path = Path(path).resolve()
        output.append(f"📁 Project Structure: {root_path.name}")
        output.append("=" * (len(str(root_path.name)) + 20))
        
        try:
            # Use rg to list files, excluding common ignore patterns
            cmd = ['rg', '--files', '--sort', 'path', str(root_path)]
            
            # Add any additional ripgrep arguments (like --max-depth)
            for key, value in rg_args.items():
                if key.startswith('--'):
                    if value is True:
                        cmd.append(key)
                    elif value is not False:
                        cmd.extend([key, str(value)])
                else:
                    cmd.append(f'--{key}')
                    if value is not True:
                        cmd.append(str(value))
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            all_files = result.stdout.strip().split('\n') if result.stdout.strip() else []
            
            # Group files by directory
            dir_structure = {}
            special_folders_content = {}  # Track content for special folders like docs, tests, etc.
            
            for file_path in all_files:
                if not file_path:
                    continue
                    
                rel_path = Path(file_path).relative_to(root_path)
                dir_path = str(rel_path.parent) if rel_path.parent != Path('.') else '.'
                
                # Check if this file is in a special folder (docs, tests, etc.)
                path_parts = rel_path.parts
                if len(path_parts) > 1:
                    top_folder = path_parts[0].lower()
                    if top_folder in ['docs', 'tests', 'test', 'examples', 'samples', 'demos']:
                        # For special folders, only show first layer items
                        if len(path_parts) == 2:  # Direct file in special folder
                            special_key = path_parts[0]
                            if special_key not in special_folders_content:
                                special_folders_content[special_key] = []
                            special_folders_content[special_key].append(rel_path.name)
                        elif len(path_parts) > 2:  # File in subfolder of special folder
                            special_key = path_parts[0]
                            subfolder_name = path_parts[1] + '/'
                            if special_key not in special_folders_content:
                                special_folders_content[special_key] = []
                            if subfolder_name not in special_folders_content[special_key]:
                                special_folders_content[special_key].append(subfolder_name)
                        continue  # Skip adding to regular dir_structure
                
                # Regular processing for non-special folders
                if dir_path not in dir_structure:
                    dir_structure[dir_path] = []
                
                dir_structure[dir_path].append(rel_path.name)
            
            # Format output - first handle regular directories
            for dir_path in sorted(dir_structure.keys()):
                files = sorted(dir_structure[dir_path])
                
                if dir_path == '.':
                    output.append(f"\n📁 ./")
                else:
                    output.append(f"\n📁 {dir_path}/")
                
                # Use regular limits for non-special folders
                files_limit = self.project_structure_files_limit
                
                # Limit files shown per directory
                if len(files) > files_limit:
                    displayed_files = files[:files_limit]
                    truncated_count = len(files) - files_limit
                else:
                    displayed_files = files
                    truncated_count = 0
                
                for file_name in displayed_files:
                    output.append(f"  📄 {file_name}")
                
                if truncated_count > 0:
                    output.append(f"  ... [{truncated_count} more files] ...")
            
            # Then handle special folders with limited first-layer content
            for special_folder in sorted(special_folders_content.keys()):
                output.append(f"\n📁 {special_folder}/")
                
                items = sorted(special_folders_content[special_folder])
                # Limit to 5 items for special folders
                if len(items) > 5:
                    displayed_items = items[:5]
                    truncated_count = len(items) - 5
                else:
                    displayed_items = items
                    truncated_count = 0
                
                for item in displayed_items:
                    if item.endswith('/'):
                        output.append(f"  � {item}")
                    else:
                        output.append(f"  📄 {item}")
                
                if truncated_count > 0:
                    output.append(f"  ... [{truncated_count} more items] ...")
        
        except subprocess.CalledProcessError as e:
            output.append(f"PYTHON-TOOL ERROR: Error reading directory structure: {e}")
        
        return "\n".join(output)
    
    def search(self, pattern: str = None, path: str = ".", **rg_args) -> str:
        """
        Search for a pattern using ripgrep with enhanced formatting.
        
        Args:
            pattern: Search pattern (if None, shows project structure)
            path: Path to search in (default: current directory)
            **rg_args: Additional arguments to pass to ripgrep
            
        Returns:
            Formatted search results or project structure
        """
        if pattern is None:
            return self._get_project_structure(path, **rg_args)
        
        try:
            # Build ripgrep command
            cmd = ['rg', '--json', '--line-number', pattern, path]
            
            # Add any additional ripgrep arguments
            for key, value in rg_args.items():
                if key.startswith('--'):
                    if value is True:
                        cmd.append(key)
                    elif value is not False:
                        cmd.extend([key, str(value)])
                else:
                    cmd.append(f'--{key}')
                    if value is not True:
                        cmd.append(str(value))
            
            # Run ripgrep
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                if result.returncode == 1:  # No matches found
                    return "No matches found."
                else:
                    return f"PYTHON-TOOL ERROR: Error running ripgrep: {result.stderr}"
            
            # Parse JSON output
            results = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        data = json.loads(line)
                        if data['type'] == 'match':
                            results.append(data)
                    except json.JSONDecodeError:
                        continue
            
            return self._format_search_results(results, pattern)
        
        except Exception as e:
            return f"PYTHON-TOOL ERROR: {str(e)}"


# Convenience function for easy usage
def easy_rg(pattern: str = None, path: str = ".", **kwargs) -> str:
    """
    Convenience function to search with easy_rg.
    
    Args:
        pattern: Search pattern (if None, shows project structure)
        path: Path to search in (default: current directory)
        **kwargs: Additional configuration or ripgrep arguments
        
    Returns:
        Formatted search results or project structure
    """
    # Extract EasyRg configuration from kwargs
    config_keys = ['max_files_per_folder', 'max_lines_per_file', 'max_line_length', 'project_structure_files_limit']
    config = {k: kwargs.pop(k) for k in config_keys if k in kwargs}
    
    rg = EasyRg(**config)
    return rg.search(pattern, path, **kwargs)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) == 1:
        # No arguments - show project structure
        print(easy_rg())
    else:
        # Search for pattern
        pattern = sys.argv[1]
        path = sys.argv[2] if len(sys.argv) > 2 else "."
        print(easy_rg(pattern, path))