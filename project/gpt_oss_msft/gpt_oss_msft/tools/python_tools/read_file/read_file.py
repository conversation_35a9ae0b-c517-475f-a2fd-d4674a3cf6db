#!/usr/bin/env python3
"""
read_file: A tool for reading and displaying file contents with line range support.

This module provides a Python-based file reader that:
- Reads and displays file contents with line numbers
- Supports reading specific line ranges (e.g., 40:60)
- Handles large files by truncating output when needed
- Provides clear error messages for invalid files or ranges
"""

import os
from typing import Op<PERSON>, Tuple, Union
from pathlib import Path


class FileReader:
    """A tool for reading file contents with line range support."""
    
    def __init__(self, max_lines: int = 200, show_line_numbers: bool = True):
        """
        Initialize FileReader with configuration parameters.
        
        Args:
            max_lines: Maximum number of lines to display before truncation (default: 200)
            show_line_numbers: Whether to show line numbers (default: True)
        """
        self.max_lines = max_lines
        self.show_line_numbers = show_line_numbers
    
    def _parse_range(self, range_str: str) -> Tuple[int, int]:
        """
        Parse a range string like "40:60" into start and end line numbers.
        
        Args:
            range_str: Range string in format "start:end" or just "line_number"
            
        Returns:
            Tuple of (start_line, end_line) - 1-indexed
            
        Raises:
            ValueError: If range format is invalid
        """
        range_str = range_str.strip()
        
        if ':' in range_str:
            try:
                start_str, end_str = range_str.split(':', 1)
                start = int(start_str.strip()) if start_str.strip() else 1
                end = int(end_str.strip()) if end_str.strip() else None
                
                if start < 1:
                    raise ValueError("Line numbers must start from 1")
                
                if end is not None and end < start:
                    raise ValueError("End line must be greater than or equal to start line")
                
                return start, end
            except ValueError as e:
                if "invalid literal" in str(e):
                    raise ValueError("Invalid line range format. Use 'start:end' or 'line_number'")
                raise
        else:
            try:
                line_num = int(range_str)
                if line_num < 1:
                    raise ValueError("Line numbers must start from 1")
                return line_num, line_num
            except ValueError:
                raise ValueError("Invalid line number format")
    
    def _format_line(self, line_content: str, line_number: int) -> str:
        """Format a line with optional line number."""
        if self.show_line_numbers:
            return f"{line_number:4d}: {line_content.rstrip()}"
        else:
            return line_content.rstrip()
    
    def read_file(self, file_path: str, line_range: str = None) -> str:
        """
        Read file contents with optional line range.
        
        Args:
            file_path: Path to the file to read
            line_range: Optional line range (e.g., "40:60", "42", ":50", "30:")
            
        Returns:
            Formatted file contents
        """
        try:
            # Validate file path
            path = Path(file_path)
            if not path.exists():
                return f"PYTHON-TOOL ERROR: File '{file_path}' does not exist"
            
            if not path.is_file():
                return f"PYTHON-TOOL ERROR: '{file_path}' is not a file"
            
            # Check if file is readable
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            except UnicodeDecodeError:
                try:
                    # Try with different encoding
                    with open(path, 'r', encoding='latin-1') as f:
                        lines = f.readlines()
                except Exception as e:
                    return f"PYTHON-TOOL ERROR: Cannot read file '{file_path}': {str(e)}"
            except PermissionError:
                return f"PYTHON-TOOL ERROR: Permission denied reading '{file_path}'"
            except Exception as e:
                return f"PYTHON-TOOL ERROR: Error reading file '{file_path}': {str(e)}"
            
            total_lines = len(lines)
            
            # Parse line range if provided
            if line_range:
                try:
                    start_line, end_line = self._parse_range(line_range)
                except ValueError as e:
                    return f"PYTHON-TOOL ERROR: {str(e)}"
                
                # Validate range against file length
                if start_line > total_lines:
                    return f"PYTHON-TOOL ERROR: Start line {start_line} exceeds file length ({total_lines} lines)"
                
                # Set end_line if not specified
                if end_line is None:
                    end_line = total_lines
                else:
                    end_line = min(end_line, total_lines)
                
                # Extract requested lines (convert to 0-indexed)
                selected_lines = lines[start_line-1:end_line]
                start_line_num = start_line
                
            else:
                # No range specified, read entire file
                selected_lines = lines
                start_line_num = 1
                end_line = total_lines
            
            # Check if we need to truncate
            if len(selected_lines) > self.max_lines:
                # Truncate and show notification
                half = self.max_lines // 2
                truncated_lines = selected_lines[:half] + selected_lines[-half:]
                truncated_count = len(selected_lines) - self.max_lines
                
                output = []
                
                # Add file header
                if line_range:
                    output.append(f"File: {file_path} (lines {start_line_num}-{end_line}, showing truncated)")
                else:
                    output.append(f"File: {file_path} (total {total_lines} lines, showing truncated)")
                output.append("─" * min(60, len(output[0])))
                
                # Add first half
                for i, line in enumerate(truncated_lines[:half]):
                    line_num = start_line_num + i
                    output.append(self._format_line(line, line_num))
                
                # Add truncation notice
                output.append(f"... [{truncated_count} lines truncated] ...")
                
                # Add second half
                for i, line in enumerate(truncated_lines[half:]):
                    line_num = end_line - half + i + 1
                    output.append(self._format_line(line, line_num))
                
                return "\n".join(output)
            
            else:
                # No truncation needed
                output = []
                
                # Add file header
                if line_range:
                    output.append(f"File: {file_path} (lines {start_line_num}-{end_line})")
                else:
                    output.append(f"File: {file_path} ({total_lines} lines)")
                output.append("─" * min(60, len(output[0])))
                
                # Add all lines
                for i, line in enumerate(selected_lines):
                    line_num = start_line_num + i
                    output.append(self._format_line(line, line_num))
                
                return "\n".join(output)
        
        except Exception as e:
            return f"PYTHON-TOOL ERROR: Unexpected error reading '{file_path}': {str(e)}"


# Convenience function for easy usage
def read_file(file_path: str, line_range: str = None, **kwargs) -> str:
    """
    Convenience function to read file contents.
    
    Args:
        file_path: Path to the file to read
        line_range: Optional line range (e.g., "40:60", "42")
        **kwargs: Additional configuration options
        
    Returns:
        Formatted file contents
    """
    # Extract FileReader configuration from kwargs
    config_keys = ['max_lines', 'show_line_numbers']
    config = {k: kwargs.pop(k) for k in config_keys if k in kwargs}
    
    reader = FileReader(**config)
    return reader.read_file(file_path, line_range)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: read_file.py <file_path> [line_range]")
        print("Examples:")
        print("  read_file.py file.txt")
        print("  read_file.py file.txt 40:60")
        print("  read_file.py file.txt 42")
        sys.exit(1)
    
    file_path = sys.argv[1]
    line_range = sys.argv[2] if len(sys.argv) > 2 else None
    
    print(read_file(file_path, line_range))