# Read File - File Content Reader with Line Range Support

A Python-based file reader that supports reading specific line ranges with intelligent truncation for large files.

## Features

- 📖 **Line Range Support**: Read specific line ranges (e.g., 40:60, 42, :50, 30:)
- 📏 **Smart Truncation**: Automatically truncates large outputs while preserving context
- 🔢 **Line Numbers**: Optional line number display for easy reference
- 🎯 **Flexible Ranges**: Support for various range formats
- 📄 **File Info**: Shows file path and line information in headers
- 🛡️ **Error Handling**: Graceful handling of missing files, permissions, encoding issues

## Usage

### Basic Usage

```python
from read_file import read_file

# Read entire file
result = read_file("file.txt")
print(result)

# Read specific line range
result = read_file("file.py", "40:60")
print(result)

# Read single line
result = read_file("file.py", "42")
print(result)
```

### Advanced Usage

```python
from read_file import FileReader

# Custom configuration
reader = FileReader(
    max_lines=50,           # Limit output to 50 lines
    show_line_numbers=False # Don't show line numbers
)

# Read with custom config
result = reader.read_file("large_file.txt", "100:200")
print(result)
```

### Command Line Usage

```bash
# Read entire file
python read_file_cli.py file.txt

# Read specific line range
python read_file_cli.py file.txt 40:60

# Read single line
python read_file_cli.py file.txt 42

# Read first 50 lines
python read_file_cli.py file.txt :50

# Read from line 30 to end
python read_file_cli.py file.txt 30:

# Custom options
python read_file_cli.py --max-lines 100 --no-line-numbers file.txt
```

## Line Range Formats

The tool supports various line range formats:

| Format | Description | Example |
|--------|-------------|---------|
| `40:60` | Lines 40 to 60 | Read lines 40-60 inclusive |
| `42` | Single line | Read only line 42 |
| `:50` | From start to line 50 | Read first 50 lines |
| `30:` | From line 30 to end | Read from line 30 to EOF |
| `10:10` | Same as single line | Read only line 10 |

## Output Format

### Regular Output
```
File: example.py (lines 40-60)
────────────────────────────────
  40: def example_function():
  41:     """This is an example function."""
  42:     result = calculate_something()
  43:     return result
  ...
```

### Truncated Output (for large ranges)
```
File: large_file.py (total 1000 lines, showing truncated)
─────────────────────────────────────────────────────────
   1: #!/usr/bin/env python3
   2: """Large file example."""
   3: import os
   ...
... [950 lines truncated] ...
 998: if __name__ == "__main__":
 999:     main()
1000: # End of file
```

## Configuration Options

| Parameter | Default | Description |
|-----------|---------|-------------|
| `max_lines` | 200 | Maximum lines to display before truncation |
| `show_line_numbers` | True | Whether to show line numbers in output |

## Truncation Behavior

When the requested range exceeds `max_lines`:
- Shows first half of the range
- Displays truncation notice with count
- Shows last half of the range
- Preserves line numbers for context

This ensures you see both the beginning and end of large ranges while keeping output manageable.

## Error Handling

The tool provides clear error messages for:

### File Errors
- **File not found**: `Error: File 'missing.txt' does not exist`
- **Not a file**: `Error: 'directory/' is not a file`
- **Permission denied**: `Error: Permission denied reading 'protected.txt'`

### Range Errors
- **Invalid format**: `Error: Invalid line range format. Use 'start:end' or 'line_number'`
- **Invalid numbers**: `Error: Line numbers must start from 1`
- **Range too large**: `Error: Start line 100 exceeds file length (50 lines)`

### Encoding Handling
- Tries UTF-8 first, falls back to Latin-1
- Provides clear error if file cannot be decoded

## Examples

### Reading Code Sections
```python
# Read a specific function
result = read_file("module.py", "45:67")

# Read just the imports
result = read_file("script.py", ":20")

# Read the main section
result = read_file("app.py", "100:")
```

### Debugging with Line Numbers
```python
# Find error around line 142
result = read_file("buggy_script.py", "135:150")
```

### Reading Configuration Files
```python
# Read specific config section
result = read_file("config.ini", "25:40")
```

### Large File Handling
```python
# Read large log file (will be truncated automatically)
result = read_file("large_log.txt", "1000:2000")
```

## Files

- `read_file.py` - Main module with FileReader class and convenience function
- `read_file_cli.py` - Command-line interface
- `tests/test_read_file.py` - Comprehensive test suite

## Requirements

- Python 3.6+
- Standard library only (no external dependencies)

## Performance

The tool is optimized for performance:
- Reads only the required lines when possible
- Efficient line counting and seeking
- Memory-efficient for large files
- Smart encoding detection

For very large files, the tool loads only the necessary portions into memory.