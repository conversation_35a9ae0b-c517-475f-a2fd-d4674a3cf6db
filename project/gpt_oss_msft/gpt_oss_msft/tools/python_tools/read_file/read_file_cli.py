#!/usr/bin/env python3
"""
Command-line interface for read_file.

Usage:
    read_file_cli.py <file_path>                # Read entire file
    read_file_cli.py <file_path> <line_range>   # Read specific line range
    read_file_cli.py -h                         # Show help
"""

import argparse
import sys
from pathlib import Path

# Add the parent directory to path to import read_file
sys.path.insert(0, str(Path(__file__).parent))

from read_file import read_file, FileReader


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Read file contents with line range support",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s file.txt                  # Read entire file
  %(prog)s file.txt 40:60            # Read lines 40-60
  %(prog)s file.txt 42               # Read line 42 only
  %(prog)s file.txt :50              # Read first 50 lines
  %(prog)s file.txt 30:              # Read from line 30 to end
  %(prog)s --max-lines 50 file.txt   # Limit output to 50 lines max
  %(prog)s --no-line-numbers file.txt # Don't show line numbers
        """
    )
    
    parser.add_argument(
        '--file_path',
        help='Path to the file to read'
    )
    
    parser.add_argument(
        '--line_range',
        nargs='?',
        help='Line range to read (e.g., "40:60", "42", ":50", "30:")'
    )
    
    # Configuration options
    parser.add_argument(
        '--max-lines',
        type=int,
        default=200,
        help='Maximum number of lines to display before truncation (default: 200)'
    )
    
    parser.add_argument(
        '--no-line-numbers',
        action='store_true',
        help='Don\'t show line numbers'
    )
    
    args = parser.parse_args()
    
    try:
        # Create FileReader instance with configuration
        reader = FileReader(
            max_lines=args.max_lines,
            show_line_numbers=not args.no_line_numbers
        )
        
        # Read file
        result = reader.read_file(args.file_path, args.line_range)
        print(result)
        
    except KeyboardInterrupt:
        print("\nInterrupted by user", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"PYTHON-TOOL ERROR: Unexpected error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()