#!/usr/bin/env python3
"""
Test suite for read_file module.
"""

import unittest
import tempfile
import os
from pathlib import Path
import sys

# Add the parent directory to the path so we can import read_file
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from read_file import FileReader, read_file


class TestReadFile(unittest.TestCase):
    """Test cases for FileReader class."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # Create test files
        self._create_test_files()
        
        # Initialize FileReader instance
        self.file_reader = FileReader(max_lines=10, show_line_numbers=True)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        # Clean up test directory
        import shutil
        shutil.rmtree(self.test_dir)
    
    def _create_test_files(self):
        """Create test files for testing."""
        # Create a test file with numbered lines
        lines = [f"Line {i}: This is line number {i}\n" for i in range(1, 101)]
        with open('test_file.txt', 'w') as f:
            f.writelines(lines)
        
        # Create a small file
        with open('small_file.txt', 'w') as f:
            f.write("Line 1\nLine 2\nLine 3\n")
        
        # Create an empty file
        Path('empty_file.txt').touch()
        
        # Create a binary-like file
        with open('binary_file.bin', 'wb') as f:
            f.write(b'\x00\x01\x02\x03')
    
    def test_read_entire_file(self):
        """Test reading entire small file."""
        result = self.file_reader.read_file('small_file.txt')
        
        self.assertIn("Line 1", result)
        self.assertIn("Line 2", result)
        self.assertIn("Line 3", result)
        self.assertIn("1:", result)  # Line numbers
        self.assertIn("(3 lines)", result)  # File info
    
    def test_read_line_range(self):
        """Test reading specific line range."""
        result = self.file_reader.read_file('test_file.txt', '5:8')
        
        self.assertIn("5: Line 5", result)
        self.assertIn("6: Line 6", result)
        self.assertIn("7: Line 7", result)
        self.assertIn("8: Line 8", result)
        self.assertNotIn("4: Line 4", result)
        self.assertNotIn("9: Line 9", result)
        self.assertIn("(lines 5-8)", result)
    
    def test_read_single_line(self):
        """Test reading single line."""
        result = self.file_reader.read_file('test_file.txt', '42')
        
        self.assertIn("42: Line 42", result)
        self.assertNotIn("41: Line 41", result)
        self.assertNotIn("43: Line 43", result)
    
    def test_read_from_start(self):
        """Test reading from start to specific line."""
        result = self.file_reader.read_file('test_file.txt', ':3')
        
        self.assertIn("1: Line 1", result)
        self.assertIn("2: Line 2", result)
        self.assertIn("3: Line 3", result)
        self.assertNotIn("4: Line 4", result)
    
    def test_read_to_end(self):
        """Test reading from specific line to end."""
        # Use small range to test with our max_lines limit
        result = self.file_reader.read_file('small_file.txt', '2:')
        
        self.assertNotIn("1: Line 1", result)
        self.assertIn("2: Line 2", result)
        self.assertIn("3: Line 3", result)
    
    def test_truncation_with_large_file(self):
        """Test truncation when file exceeds max_lines."""
        # max_lines is set to 10 in setUp
        result = self.file_reader.read_file('test_file.txt')
        
        self.assertIn("truncated", result)
        self.assertIn("1: Line 1", result)  # Should have first lines
        self.assertIn("100: Line 100", result)  # Should have last lines
    
    def test_line_range_truncation(self):
        """Test truncation when line range exceeds max_lines."""
        result = self.file_reader.read_file('test_file.txt', '10:30')
        
        self.assertIn("truncated", result)
        self.assertIn("10: Line 10", result)  # First few lines
        self.assertIn("30: Line 30", result)  # Last few lines
    
    def test_invalid_file(self):
        """Test behavior with non-existent file."""
        result = self.file_reader.read_file('nonexistent.txt')
        
        self.assertIn("Error", result)
        self.assertIn("does not exist", result)
    
    def test_invalid_range_format(self):
        """Test behavior with invalid range format."""
        result = self.file_reader.read_file('test_file.txt', 'invalid')
        
        self.assertIn("Error", result)
        self.assertIn("Invalid", result)
    
    def test_range_exceeds_file_length(self):
        """Test behavior when range exceeds file length."""
        result = self.file_reader.read_file('small_file.txt', '10:20')
        
        self.assertIn("Error", result)
        self.assertIn("exceeds file length", result)
    
    def test_negative_line_numbers(self):
        """Test behavior with negative line numbers."""
        result = self.file_reader.read_file('test_file.txt', '-5:10')
        
        self.assertIn("Error", result)
        self.assertIn("must start from 1", result)
    
    def test_empty_file(self):
        """Test reading empty file."""
        result = self.file_reader.read_file('empty_file.txt')
        
        self.assertIn("(0 lines)", result)
    
    def test_no_line_numbers(self):
        """Test reading without line numbers."""
        reader = FileReader(show_line_numbers=False)
        result = reader.read_file('small_file.txt')
        
        self.assertIn("Line 1", result)
        self.assertNotIn("1:", result)  # No line numbers
    
    def test_convenience_function(self):
        """Test the convenience function."""
        result = read_file('small_file.txt')
        
        self.assertIsInstance(result, str)
        self.assertIn("Line 1", result)
    
    def test_convenience_function_with_range(self):
        """Test convenience function with range."""
        result = read_file('test_file.txt', '5:7')
        
        self.assertIn("5: Line 5", result)
        self.assertIn("7: Line 7", result)
        self.assertNotIn("4: Line 4", result)


if __name__ == '__main__':
    unittest.main(verbosity=2)