import shlex
import os
from functools import cache
from typing import Any

import blobfile as bf
import structlog
from aswe_coreutils.coreutils_version import CoreutilsVersion
from caas.api import CaasSession
from caas.commands import Exec, UploadFile
from sciclone_utils.operations import sync_from_canonical_source

logger = structlog.stdlib.get_logger(component=__name__)

COREUTILS_VERSION = CoreutilsVersion.V_0_0_17


@cache
def get_coreutils_bytes(version: str) -> bytes:
    if version == "apply_patch_only":
        uri = "az://orngscuscresco/data/zhendongw/gpt_oss_msft/oai_tools/oai_coreutils_linux-x64_0.1.0_apply_patch_only.tar.gz"
        return bf.read_bytes(sync_from_canonical_source(uri))
    try:
        uri = "az://orngscuscresco/data/damajercak/teammate_coreurils/teammate_coreutils_linux-x64_0.1.0.tar.gz"
        return bf.read_bytes(sync_from_canonical_source(uri))
    except:
        uri = "az://orngcresco/data/damajercak/teammate_coreurils/teammate_coreutils_linux-x64_0.1.0.tar.gz"
        return bf.read_bytes(sync_from_canonical_source(uri))


@cache
def get_python_tools_bytes() -> bytes:
    """Get the python_tools tar.gz file from storage."""
    uri = "az://orngscuscresco/data/zhendongw/gpt_oss_msft/oai_tools/python_tools_0827.tar.gz"
    return bf.read_bytes(sync_from_canonical_source(uri))
    
    
async def _setup_python_tools(session: CaasSession) -> None:
    """Setup Python tools by extracting the tar.gz file to the correct location."""
    try:
        # Upload the python_tools tar.gz file
        tar_path = "/tmp/python_tools.tar.gz"
        await session.run(UploadFile(path=tar_path, data=get_python_tools_bytes()))
        
        # Create the target directory
        await session.run(Exec(['mkdir', '-p', '/python_tools'], timeout=30))
        
        # Extract the tar file directly to /python_tools
        # Use --strip-components=1 to remove the top-level python_tools/ directory from the tar
        await session.run(Exec(['tar', '-C', '/python_tools', '--strip-components=1', '-xzf', tar_path], timeout=90))
        
        # Make all Python CLI scripts executable
        await session.run(Exec(['find', '/python_tools', '-name', '*_cli.py', '-exec', 'chmod', '+x', '{}', ';'], timeout=30))
        
        # Clean up the tar file
        await session.run(Exec(['rm', tar_path], timeout=30))
        
        logger.info("Successfully set up Python tools from tar.gz")
        
    except Exception as e:
        logger.warning(f"Failed to setup Python tools: {e}")
        # Don't raise the exception to avoid breaking the entire setup process


def _coreutils_bytes(version: str) -> bytes:
    """
    Copied from openai's project/caas_tasks/caas_utils/caas_utils/utils.py
    """
    return get_coreutils_bytes(version)


# oai-coreutils is built from monorepo: openai/project/teammate/teammate_coreutils
async def setup_coreutils(
    session: CaasSession,
    datapoint: dict[str, Any],
    repo_root: str | None,
    login: bool = False,
    version: str = "oai",
    setup_python_tools: bool = False
) -> None:
    """
    Copied from openai's project/caas_tasks/caas_utils/caas_utils/utils.py
    """
    # `login`: whether the commands we run in `session` should be via `bash -l`.
    path = "/tmp/core_utils.tar.gz"
    await session.run(UploadFile(path=path, data=_coreutils_bytes(version)))

    # NOTE: --skip-old-files preserves any existing versions of the binaries, which may or may not be ideal
    #       -k does a similar thing, but treats them as errors, which we don't want
    try:
        cmd = ["tar", "-C", "/usr/local/bin", "--skip-old-files", "-xzf", path]
        if login:
            cmd = ["bash", "-lc", shlex.join(cmd)]
        await session.run(Exec(cmd, timeout=90))
        
        if setup_python_tools:
            await _setup_python_tools(session)
    except Exception as e:
        raise RuntimeError(
            f"Failed to install coreutils. Error: {e}."
        ) from e


async def setup_fn_coreutils(
    *, datapoint: dict[str, Any], session: CaasSession, workdir: str | None
) -> None:
    """
    Copied from deep_swe/datasets/setup.py
    """
    await setup_coreutils(session, datapoint, None)
