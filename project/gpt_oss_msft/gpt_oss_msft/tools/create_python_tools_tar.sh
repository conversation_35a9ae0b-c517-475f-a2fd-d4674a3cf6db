#!# Script to create python_tools.tar.gz for deployment
# This creates a tar file that can be extracted with --strip-components=1 to /python_tools in the containerin/bash

# Script to create python_tools.tar.gz for deployment
# This creates a tar file that can be extracted directly to /swe_python_tools in the container

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TOOLS_DIR="$SCRIPT_DIR"

# Output file
OUTPUT_FILE="$SCRIPT_DIR/python_tools.tar.gz"

# Change to the tools directory
cd "$TOOLS_DIR"

# Create the tar file with the python_tools directory structure
# This will create a tar that when extracted creates easy_rg/, file_search/, read_file/ directories
echo "Creating python_tools.tar.gz..."
tar -czf "$OUTPUT_FILE" \
    --exclude="*.pyc" \
    --exclude="__pycache__" \
    --exclude="*.git*" \
    --exclude="create_python_tools_tar.sh" \
    python_tools/

echo "Created $OUTPUT_FILE"
echo "File size: $(du -h "$OUTPUT_FILE" | cut -f1)"

# List contents to verify structure
echo -e "\nTar file contents:"
tar -tzf "$OUTPUT_FILE" | head -20

echo -e "\nTo upload this file, you can now copy it to your storage location."
echo "The container will extract this to /python_tools/ creating the following structure:"
echo "  /python_tools/easy_rg/"
echo "  /python_tools/file_search/"
echo "  /python_tools/read_file/"