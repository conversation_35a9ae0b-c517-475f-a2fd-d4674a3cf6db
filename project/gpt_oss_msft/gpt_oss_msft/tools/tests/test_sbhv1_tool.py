import os
import chat
import async<PERSON>
import json
import shlex
import time
import structlog
from caas.api import CaasSession
from caas import caas_api
from caas.protocol import VolumeMount
from caas.commands import RawExec, UploadFile, BashScript, Exec, HttpGet
from caas_tool.caas_container import CaasContainer

from deep_swe_msft.tools.utils import CRESCO_STORAGE_NAME

from gpt_oss_msft.tools.oai_coreutils_setup import setup_coreutils
from gpt_oss_msft.tools.caas_container_tool import DeepSWECaasContainerTool
from gpt_oss_msft.tools.utils import NODE_CERT_MNT, CAAS_ENVS
from deep_swe_tasks.task_metadata import DeepSWETaskMetadata
from gpt_oss_msft.swe_tasks.sbh.setup import sbhv1_setup_fn


logger = structlog.stdlib.get_logger(component=__name__)

def load_jsonl(file_path):
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            item = json.loads(line.strip())
            data.append(item)
    return data

NODEJS_VERSION='22.14.0'

TMP_DRI="/usr/local/nodejsinstall"
CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
CAAS_ARTIFACTORY_NPM_REGISTRY = "https://npm.pkg.privatelink.caas.azure.com"

async def main(metadata):
    blob_name = f"node-v{NODEJS_VERSION}-linux-x64.tar.gz"
    # caas = caas_api(endpoint=CAAS_ENDPOINT)
    caas_session = None
    CAAS_IMAGE = "acrcommitcaaseastus2ame.azurecr.io/swe-bench-hard-taozhi8833998__node-sql-parser-1287:v3"
    
    print(f"Using CAAS_IMAGE: {CAAS_IMAGE}")
    try:
        container = await CaasContainer.new(
            caas_endpoint=CAAS_ENDPOINT, 
            image_name=CAAS_IMAGE, 
            idle_ttl=1200, 
            memory_limit="4g", 
            cpu_limit="4", 
            volume_mounts=NODE_CERT_MNT,
            env=CAAS_ENVS,
        )
        
        caas_session = container.terminal_session.session

        print("="*100)
        print("setup_coreutils")
        out = await sbhv1_setup_fn(datapoint={}, session=caas_session, workdir='/testbed')
        print(out)
        print("="*100+'\n')

        output = await caas_session.run(RawExec(['bash', '-c', "ls /testbed"], timeout=60, workdir='/'))
        print(f"Test apply-patch: \nExit Code: {output[0]} Output: {output[1].decode()}")

        output = await caas_session.run(RawExec(['bash', '-lc', "ls"], timeout=60, workdir='/'))
        print(f"Test apply-patch: \nExit Code: {output[0]} Output: {output[1].decode()}")

        output = await caas_session.run(RawExec(['bash', '-c', """apply_patch "*** Begin Patch\n*** Add File: file_to_add.py\n+if __name__ == '__main__':\n+  print(1234)\n*** End Patch" """], timeout=60, workdir='/'))
        print(f"Test apply-patch: \nExit Code: {output[0]} Output: {output[1].decode()}")
        
        # Tool 1: create a file
        tool = DeepSWECaasContainerTool(
            container=container,
            terminal_emulator_path="teammate_service.termites.lean_terminal:LeanTerminal",
        )
        # print(tool.get_function_calling_namespace())
        call_arguments = {
            "cmd": ["apply_patch", "*** Begin Patch\n*** Add File: file_to_add.py\n+if __name__ == '__main__':\n+  print(1234)\n*** End Patch"],
            "workdir": '/'
        }
        message = chat.Message.assistant(
            recipient=f"{tool.get_tool_name()}.exec",
            content=json.dumps(call_arguments)
        )
        async for response in tool.process(message):
            print(response)

        # Tool 2: read the file
        call_arguments = {
            "cmd": ["cat", "file_to_add.py"],
            "workdir": '/'
        }
        message = chat.Message.assistant(
            recipient=f"{tool.get_tool_name()}.exec",
            content=json.dumps(call_arguments)
        )
        async for response in tool.process(message):
            print(response)

    except Exception as e:
        print("Error: ", e)
    finally:
        if caas_session:
            await caas_session.close()
            print("caas_session closed")


if __name__ == "__main__":
    import subprocess
    dataset_name_or_path = "swe_bench_train_updated.jsonl"
    if not os.path.exists(dataset_name_or_path):
        subprocess.run(["bbb", "cp", f"az://{CRESCO_STORAGE_NAME}/data/luw/swe-bench-train-vsc/test_train/swe_bench_train_updated.jsonl", "./"])
    data = load_jsonl("swe_bench_train_updated.jsonl")
    metadata = data[4]['metadata']
    metadata = DeepSWETaskMetadata.model_validate(metadata)
    asyncio.run(main(metadata))