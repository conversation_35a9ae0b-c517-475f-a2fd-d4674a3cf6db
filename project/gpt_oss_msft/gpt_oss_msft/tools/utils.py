# Generate a SAS token for the blob
import os
from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobServiceClient, generate_container_sas, ContainerSasPermissions
from datetime import datetime, timedelta

# CAAS_ENDPOINT = "https://eastus2.caas.azure.com"
CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
CAAS_IDLE_TTL = 1200

def get_strawberry_ace_token():

    # Set up the credentials and BlobServiceClient
    credential = DefaultAzureCredential()
    blob_service_client = BlobServiceClient(account_url="https://orngcaas.blob.core.windows.net", credential=credential)

    # Set the expiry time for the SAS token
    expiry_time = datetime.now() + timedelta(hours=1)  # Token valid for 1 hour
    # Generate the user delegation SAS token
    user_delegation_key = blob_service_client.get_user_delegation_key(datetime.now(), expiry_time)
    sas_token = generate_container_sas(
        account_name="orngcaas",
        container_name="data",
        user_delegation_key=user_delegation_key,
        permission=ContainerSasPermissions(read=True),  # Set permissions as needed
        expiry=expiry_time
    )

    return sas_token

# For installing nodejs
from caas.protocol import VolumeMount

CRESCO_STORAGE_NAME = "orngscuscresco"

NODE_CERT_MNT = [VolumeMount(host=f"/mnt/azure_blob/certs/mitmproxy-ca-cert.crt", container=f"/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt", deprecated_use_blobfuse=True)]
CAAS_ENVS = {
    "NODE_EXTRA_CA_CERTS": "/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt"
}

ENABLE_NETWORK = True

# Base Image Name
RFS_LANG_TO_IMAGE_NAME = {
    "py": "aio",
    "typescript": "aio",
    "cs": "aio",
    "java": "aio",
    "javascript": "aio",
}
