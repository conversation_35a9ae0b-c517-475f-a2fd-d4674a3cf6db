import functools
import json
import shlex
from functools import cached_property
from functions import Function
import timeit
import logging
from typing import Any, AsyncIterator, Collection, Literal

import structlog
import orjson
import chat
import chz
from caas_tool.caas_container import CaasContainer
from chat.render.common import render_content
from chat.render.v4.experimental.strawberry.formatter import Berry<PERSON>hannel
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from berry_harmony.tools.berry_tool_interface import BerryTool
from berry_caas_container_tool.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from berry_caas_container_tool.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
    _caas_container_resource_name
)
try:
    from berry_caas_container_tool.caas_container_tool import (
        ToolPluginConfig as CaasToolPluginConfig,
        create_tool_plugins as create_caas_tool_plugins,
    )
except ImportError:
    # Version prior to July FI:
    from berry_caas_container_tool.caas_container_tool import (
        CaasToolPluginConfig,
        create_caas_tool_plugins,
    )

from abc import abstractmethod
from typing import Annotated, AsyncIterator, ClassVar, Iterator, Sequence, Literal, Callable
from function_calling_tool import FunctionCallingTool, function_the_model_can_call

from gpt_oss_msft.tools.coreutils_manual import CAAS_CONTAINER_COREUTILS_MANUAL
from gpt_oss_msft.data.datapoint_converters import ENFORCE_COMMANDS_FORBIDDEN
from deep_swe.datasets.caas_container_tool.tool_config import BaseCaasContainerTool

logger = structlog.stdlib.get_logger(component=__name__)

# Tool penalty configuration
FORBID_ALL_EXCEPT_EXEC_PREFIXES_METADATA_KEY = "forbid_all_but_exec_prefixes"

TOOL_PENALTY_ANNEAL_STEPS = 20
TOOL_PENALTY_BASE = 0.05
def calculate_tool_penalty(message: chat.Message) -> float:
    """
    Computes an aux reward penalty for the **response** to a container.exec message.
    `exec_cmd`, `exec_status_code`, `exec_duration_seconds` are populated by CaasContainerTool.
    """
    
    unwanted_cmds_in_bash = ["oai", "ls -R"]
    # container.exec tool penalty
    if message.recipient == "container.exec":
        try:
            args = json.loads(str(message.content), object_pairs_hook=_reject_format)
            # Different bash variants use different parameter names for the command
            args_cmd = args.get("cmd")
            if args_cmd and any(c in args_cmd for c in unwanted_cmds_in_bash):
                return TOOL_PENALTY_BASE
        except Exception:
            return TOOL_PENALTY_BASE

    # container.apply_patch tool penalty
    if exec_cmd := message.metadata.get("exec_cmd"):
        match exec_cmd:
            case ["apply_patch", *_]:
                # v0.0.4 does not respect exit codes but outputs "Done!"
                output = render_content(message).strip()
                if not output.startswith(("Done!", "Success")):
                    tool_penalty = TOOL_PENALTY_BASE
    
    tool_penalty: float = 0.0
    if (code := message.metadata.get("exec_status_code")) and code >= 124:
        # Timeout, not found, etc. usually indicate command misuse
        tool_penalty = TOOL_PENALTY_BASE
    if (duration := message.metadata.get("exec_duration_seconds")) and duration > 30:
        # Long commands come with a cost
        tool_penalty = TOOL_PENALTY_BASE

    return tool_penalty


class DeepSWECaasContainerTool(BaseCaasContainerTool):
    """
    Applies tool penalties and (forbidden) command enforcement!

    NOTE: Required command enforcement is done in IFEnforcementGrader;
    however failing forbidden commands immediately is more efficient and is a sort of weak credit assignment.
    """
    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        default_exec_timeout: int | None = 10_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        inverse_tool_duration_penalty: float | None = None,
        inverse_tool_yield_penalty: float | None = None,
        tool_penalty_anneal_steps: int = TOOL_PENALTY_ANNEAL_STEPS,
        exec_error_includes_session_id: bool = False,
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            exec_error_includes_session_id=exec_error_includes_session_id,
        )
        # custom the default_exec_timeout, the oai by default uses 10 seconds
        self._default_exec_timeout = default_exec_timeout

        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_penalty_anneal_steps = tool_penalty_anneal_steps
        self.inverse_tool_duration_penalty = inverse_tool_duration_penalty
        self.inverse_tool_yield_penalty = inverse_tool_yield_penalty

    # Remove feed_chars from the tool instructions
    @classmethod
    @functools.cache
    def get_names_of_functions_the_model_can_call(cls) -> Collection[str]:
        result = set(super().get_names_of_functions_the_model_can_call())
        result.discard("feed_chars")
        result.discard("new_session")
        return result
    # This class is by default using OAI container.exec tool.
    
    @classmethod
    @functools.cache
    def get_function_calling_function_schemas(cls) -> list[Function]:
        return [
            cls.get_function_calling_function_schema(name)
            for name in cls.get_names_of_functions_the_model_can_call() if name not in ["feed_chars", "new_session"]
        ]

    @classmethod
    def get_tool_name(cls) -> str:
        return "container"
    
    @classmethod
    def get_description(cls) -> str | None:
        return (
            'Microsoft Coding Tool for interacting with containers \n\n'
            "Use `container.exec` to execute commands in a terminal if needed.\n\n"
            + CAAS_CONTAINER_COREUTILS_MANUAL
        )
    
    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        assert sample.conversation
        toolcall_message = sample.conversation.messages[-1]

        # check some forbidden calls
        forbid_all_except_exec_prefixes: Sequence[Sequence[str]] | None = (
            sample.gt_datapoint.metadata.get(FORBID_ALL_EXCEPT_EXEC_PREFIXES_METADATA_KEY, None)
        )
        forbidden_commands = sample.gt_datapoint.metadata.get(ENFORCE_COMMANDS_FORBIDDEN)

        if forbid_all_except_exec_prefixes is not None and toolcall_message.recipient not in {
            "container.exec",
            chat.DEFAULT_RECIPIENT,
        }:
            raise errors.ModelError(
                label=f"used_resricted_forbidden_{toolcall_message.recipient}",
                description=f"Attempted to call a forbidden tool: {toolcall_message.recipient} with {forbid_all_except_exec_prefixes=}",
                log_level=logging.WARNING,
                sample=sample,
            )

        if forbidden_commands or (forbid_all_except_exec_prefixes is not None):
            raw_args_cmd: list[str] | None = None
            args_cmd = ""
            try:
                if toolcall_message.recipient == "container.exec":
                    args = json.loads(str(toolcall_message.content))
                    raw_args_cmd = args_cmd = args.get("cmd")
                    if args_cmd[0] in ("bash", "sh", "/bin/bash", "/bin/sh"):
                        args_cmd = args_cmd[-1]
                    else:
                        args_cmd = shlex.join(args_cmd)
                assert isinstance(args_cmd, str)
            except Exception:
                args_cmd = ""

            if raw_args_cmd is not None and forbid_all_except_exec_prefixes is not None:
                for allowed_prefix in forbid_all_except_exec_prefixes:
                    if tuple(raw_args_cmd[: len(allowed_prefix)]) == tuple(allowed_prefix):
                        break
                else:
                    raise errors.ModelError(
                        label="used_resricted_forbidden_command",
                        description=f"Attempted to call a forbidden tool command: {raw_args_cmd} with {forbid_all_except_exec_prefixes=}",
                        log_level=logging.WARNING,
                        sample=sample,
                    )

            if args_cmd and forbidden_commands:
                for forbidden_command in forbidden_commands:
                    if args_cmd.startswith(forbidden_command):
                        # The sample attempted to execute a forbidden command.
                        # Raise a ModelError to abort further processing.
                        raise errors.ModelError(
                            label="used_enforced_forbidden_command",
                            description="Attempted to call a forbidden tool command",
                            log_level=logging.WARNING,
                            sample=sample,
                        )

        sample.metrics.setdefault("tool_penalty", 0.0)  # For correct wandb average
        sample.metrics.setdefault("tool_duration", 0.0)  # For correct wandb average
        policy_step = sample.metadata.get("sampling/policy_step_initial")
        t0_message = t0 = timeit.default_timer()
        async for message in super()._process_sample_inner(sample):
            duration_message = timeit.default_timer() - t0_message
            yield message
            message.metadata["exec_duration_seconds_process_sample_inner"] = duration_message
            t0_message = timeit.default_timer()

            if self.tool_penalty_multiplier:
                # for exec
                try:
                    if policy_step and ("exec_cmd" in message.metadata):
                        # This message is the first **response** tool message
                        sample.metrics["tool_penalty"] -= (
                            self.tool_penalty_multiplier * calculate_tool_penalty(message)
                        )
                except Exception:
                    logger.exception("Error applying tool penalty", exc_info=True)

        duration = timeit.default_timer() - t0
        if self.inverse_tool_duration_penalty is not None:
            assert (juice := sample.gt_datapoint.reward_multiplier) is not None
            sample.metrics["tool_penalty"] -= duration / (
                juice * self.inverse_tool_duration_penalty
            )
        if self.inverse_tool_yield_penalty is not None:
            assert (juice := sample.gt_datapoint.reward_multiplier) is not None
            sample.metrics["tool_penalty"] -= 1 / (juice * self.inverse_tool_yield_penalty)
        sample.metrics["tool_duration"] += duration
        if policy_step and self.tool_penalty_anneal_steps > 0:
            # Only apply tool penalties starting from step 10 onwards
            sample.metrics["tool_penalty"] *= min(1.0, policy_step / self.tool_penalty_anneal_steps)
        if (
            self.max_tool_penalty is not None
            and sample.metrics["tool_penalty"] < -self.max_tool_penalty
        ):
            sample.metrics["tool_penalty"] = -self.max_tool_penalty

    def parse_function_call(self, message: chat.Message) -> tuple[str, Callable, dict[str, Any]]:
        """
        Parse this message as a call to one of the functions in this tool.

        Raises `chat.ToolInputError` if the message does not parse
        successfully, if the message has an unrecognized recipient, etc.
        """

        # Figure out which function the model is calling.
        recipient = message.recipient
        if "." not in recipient:
            raise chat.ToolInputError(
                f"Invalid {recipient=}, expected '{self.get_tool_name()}.<function_name>'"
            )
        tool_name, function_name = recipient.split(".", 1)
        if tool_name != self.get_tool_name():
            raise chat.ToolInputError(
                f"Wrong {tool_name=} (with {function_name=}), expected '{self.get_tool_name()}'"
            )
        fn = getattr(self.__class__, function_name, None)
        if fn is None:
            raise chat.ToolInputError(f"Unrecognized function name '{function_name}'")
        if not hasattr(fn, "__fn_calling_tool_fn_type__"):
            raise chat.ToolInputError(f"Model cannot call {function_name=}")
        json_string = chat.render.common.render_content(message, msg_idx=0)
        try:
            json.loads(json_string, object_pairs_hook=_reject_format)
        except ValueError as e:
            raise chat.ToolInputError(f"Could not parse args as JSON: {e}") from e
        try:
            kwargs = orjson.loads(json_string)
        except orjson.JSONDecodeError as e:
            raise chat.ToolInputError(f"Could not parse args as JSON: {e}") from e
        function_schema = self.get_function_calling_function_schema(function_name)
        assert function_schema is not None
        if not function_schema.validate_json(kwargs):
            raise chat.ToolInputError(
                f"Invalid {function_name=} call: {kwargs=}. Expected: {function_schema.to_typescript()}"
            )
        return function_name, fn, kwargs

# fix grep in command: 
# Hook that spots format checks inside each JSON object
def _reject_format(pairs):
    seen = {}
    for k, v in pairs:
        if k in seen:                       # ← duplicate!
            raise ValueError(f"duplicate key {k!r}")
        seen[k] = v
    return seen


@chz.chz(typecheck=True)
class DeepSWECaasContainerToolConfig(OriginalCaasContainerToolConfig):
    tool_timeout: int = chz.field(
        doc="Maximum time to wait by the system for each tool call",
        default=1180,  # 20 minutes
    )
    exec_output_processor_path: str | None = (
        "gpt_oss_msft.tools.truncate:TruncateExecPostProcessor" # truncate -> max 128 lines, 128 per line
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown", "enabled"] = "no_access"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_timeout: int = 1_980
    default_exec_timeout: int = 10_000

    # tool_channel: BerryChannel | None = chz.field(
    #     doc="What channel the tool should go in", default=BerryChannel.CHAIN_OF_THOUGHT
    # )

    # def get_channel_for_tool_call(self) -> BerryChannel | None:
    #     return self.tool_channel

    def get_tool_name(self) -> str:
        return DeepSWECaasContainerTool.get_tool_name()

    def unique_descriptor_for_variants(self) -> str:
        return DeepSWECaasContainerTool.get_tool_name()

    @cached_property
    def _tool_for_tool_info(self) -> DeepSWECaasContainerTool:
        """Override to use DeepSWECaasContainerTool instead of base BerryCaasContainerTool for tools_format_version=v2 support."""
        tool = DeepSWECaasContainerTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = DeepSWECaasContainerTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        return tool.instruction()

    def get_names_of_functions_the_model_can_call(self, datapoint: HarmonyDatapoint | None = None) -> Collection[str]:
        return DeepSWECaasContainerTool.get_names_of_functions_the_model_can_call()

    @property
    def _caas_resource_name(self) -> str:
        return _caas_container_resource_name(
            caas_network=self.caas_network,
            enable_network_after_setup=self.internet_policy == "enabled",
        )

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = DeepSWECaasContainerTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        return tool
