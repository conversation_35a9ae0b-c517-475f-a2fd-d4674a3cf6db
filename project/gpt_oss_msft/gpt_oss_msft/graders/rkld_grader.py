from typing import Sequence

from typing import Sequence

import numpy as np
import numpy.typing as npt
import structlog
import chz
import qstar.graders.rkld_utils as rkld_utils
from berry import not_none
from berry.grader import GraderOutput
from bus.registry import DEFAULT_BUS_LINE
from bus_token_completer.bus_token_completer import Bus<PERSON><PERSON>Completer
from multimodal_token.token_schema import Modality
from multimodal_token.toklist.list import TokList
from oaicommon.oai_types import none_throws
from qstar.common import types
from qstar.graders import grader
from qstar.sample_completers import sample_completer

from berry.sample import GraderError
from qstar.graders.rkld_grader import RkldGrader as RkldBaseGrader

logger = structlog.stdlib.get_logger(component=__name__)

@chz.chz(typecheck=True)
class RkldGrader(RkldBaseGrader):

    teacher_top_k: int = chz.field(
        doc="The number of top tokens to query from the teacher engine for forward KL distillation. Set to 0 for reverse KL distillation.",
        default=0,
    )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade]:
        """
        Main entry point for grading a batch of samples.
        Fetches logprobs from the teacher and aligns/pads them to each sample's completion.
        """
        if self._any_sample_is_multimodal(samples):
            assert self.renderer_name is not None, "renderer_name must be set for multimodal RKLD."
        graded_samples = []
        for sample in samples:
            try:
                assert sample.prompt_tokens is not None, "Prompt tokens must be set."
                prompt_tokens_teacher = sample.ephemeral_metadata["prompt_tokens_teacher"]
                prompt_tokens_student = sample.prompt_tokens
                tokens = TokList.concatenate([prompt_tokens_teacher, sample.tokens])

                MAX_LEN = 131072
                if len(tokens) > MAX_LEN:
                    if sample.gt_datapoint.dataset_config.rkld_throw_away_if_teacher_max_len:
                        assert False, f"len(tokens) > {MAX_LEN}, so we will not send to teacher"
                    trim = len(tokens) - MAX_LEN
                    if self.teacher_top_k == 0:
                        teacher_logp = self._fetch_logprobs(tokens.truncate_by(trim))
                        teacher_logp = np.concatenate(
                            [teacher_logp, np.full(trim, -np.inf)]
                        )
                        teacher_top_tokens = teacher_top_logprobs = None
                    else:
                        teacher_logp, teacher_top_tokens, teacher_top_logprobs = (
                            self._fetch_logprobs_fkld(tokens.truncate_by(trim))
                        )
                        teacher_logp = np.concatenate(
                            [teacher_logp, np.full(trim, -np.inf)]
                        )
                        teacher_top_tokens = np.concatenate(
                            [teacher_top_tokens, np.full((trim, self.teacher_top_k), 0)]
                        )
                        teacher_top_logprobs = np.concatenate(
                            [teacher_top_logprobs, np.full((trim, self.teacher_top_k), -np.inf)]
                        )
                else:
                    if self.teacher_top_k == 0:
                        teacher_logp = self._fetch_logprobs(tokens)
                        teacher_top_tokens = teacher_top_logprobs = None
                    else:
                        teacher_logp, teacher_top_tokens, teacher_top_logprobs = (
                            self._fetch_logprobs_fkld(tokens)
                        )

                # Pad teacher_logp if needed (for osmini)
                prompt_teacher_len = len(prompt_tokens_teacher)
                prompt_student_len = len(prompt_tokens_student)
                diff = prompt_teacher_len - prompt_student_len
                assert diff >= 0, (prompt_teacher_len, prompt_student_len)
                teacher_logp = teacher_logp[diff:]
                if teacher_top_tokens is not None:
                    teacher_top_tokens = teacher_top_tokens[diff:]
                    teacher_top_logprobs = teacher_top_logprobs[diff:]

                graded_samples.append(
                    sample.with_grade(
                        log_rewards={"neg_rkld": 0},
                        is_correct=self._has_no_model_errors(sample),
                        teacher_logp=teacher_logp,
                        teacher_top_tokens=teacher_top_tokens,
                        teacher_top_logprobs=teacher_top_logprobs,
                    )
                )
            except Exception as e:
                grader_error = GraderError.from_exception(e)
                graded_samples.append(
                    sample.without_reward(errors_blamed_on_system=[grader_error])
                )
                logger.error(f"Exception while grading sample in RKLD Grader: {e}")
        return graded_samples

    def _fetch_logprobs(self, tokens: TokList) -> npt.NDArray[np.float32]:
        response = self.bus_completer.completion(
            prompt=[tokens], logprobs=0, echo=True, max_tokens=0
        )
        logprobs = none_throws(response.choices[0].logprobs)
        token_logprobs = none_throws(logprobs.token_logprobs)[1:]  # first token is always nan
        return np.array(token_logprobs, dtype=np.float32)

    def _fetch_logprobs_fkld(
        self, tokens: TokList
    ) -> tuple[npt.NDArray[np.float32], npt.NDArray[np.int32], npt.NDArray[np.float32]]:
        response = self.bus_completer.completion(
            prompt=[tokens], logprobs=self.teacher_top_k, echo=True, max_tokens=0
        )
        logprobs = none_throws(response.choices[0].logprobs)
        token_logprobs = none_throws(logprobs.token_logprobs)[1:]  # first token is always nan
        teacher_top_tokens = np.full((len(tokens) - 1, self.teacher_top_k), 0, dtype=np.int32)
        teacher_top_logprobs = np.full((len(tokens) - 1, self.teacher_top_k), float("-inf"), dtype=np.float32)

        if logprobs.top_logprobs is not None:
            for i, top in enumerate(logprobs.top_logprobs[1:]):
                for j, (_tok, _logp) in enumerate(top[: self.teacher_top_k]):
                    teacher_top_tokens[i, j] = _tok
                    teacher_top_logprobs[i, j] = _logp
        # BUG: the first column of these arrays corresponds to the sampled token.
        # The sampled token of the student is almost always in the top k of the teacher given a reasonably large k.
        return (
            np.array(token_logprobs, dtype=np.float32),
            teacher_top_tokens,
            teacher_top_logprobs,
        )


# @chz.chz(typecheck=True)
# class RkldGrader(grader.Grader[datapoint.HarmonyCompletionDatapoint]):
#     """
#     A simple grader that follows the structure of MathgenGrader but instead of
#     grading correctness, it queries multiple sampling engines for logprobs and returns their average.
#     """

#     topic: str = chz.field(
#         doc="The teacher model snapshot, used for routing to the correct bus engine.",
#     )

#     topic_mode_or_user: str = chz.field(
#         doc="The topic mode or user used for creating the engine.",
#     )

#     line: str = chz.field(
#         doc="The bus line to use.",
#         default=DEFAULT_BUS_LINE,
#     )

#     @chz.init_property
#     def bus_completer(self) -> BusTokenCompleter:
#         """Create a single bus token completer."""
#         return BusTokenCompleter(
#             topic_or_snapshot=self.topic,
#             topic_mode_or_user=self.topic_mode_or_user,
#             bus_line=self.line,
#             qos_type=QoSType.ROUND_ROBIN_BY_POD,
#         )

#     @property
#     def accepts_invalid(self) -> bool:
#         """Whether invalid samples should be sent to `grade_batch()`.
#         Most graders should ignore invalid samples.
#         """
#         return True

#     def _grade_batch_inner(
#         self,
#         samples: Sequence[types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint]],
#         sample_execution_context: sample_completer.SampleExecutionContext | None = None,
#         profile_label: str | None = None,
#     ) -> list[types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]]:
#         """
#         Main entry point for grading a batch of samples. We'll fetch logprobs from the
#         bus endpoint and attach them to each sample's additional_metadata.
#         """
#         if self._any_sample_is_multimodal(samples):
#             raise ValueError("Multimodal samples have not been tested for RKLD grader.")
#         graded_samples = []
#         for sample in samples:
#             assert sample.prompt_tokens is not None, "Prompt tokens must be set."
#             tokens = TokList.concatenate([sample.prompt_tokens, sample.tokens])
#             teacher_logp = self._fetch_logprobs(tokens)

#             graded_samples.append(
#                 sample.with_grade(
#                     log_rewards={"neg_rkld": 0},
#                     is_correct=self._has_no_model_errors(sample),
#                     teacher_logp=teacher_logp,
#                 )
#             )
#         return graded_samples

#     def _any_sample_is_multimodal(
#         self,
#         samples: Sequence[types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint]],
#     ) -> bool:
#         return any(s.gt_datapoint.dataset_config.is_multimodal for s in samples)

#     def _has_no_model_errors(
#         self, sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint]
#     ) -> bool:
#         return len(sample.errors_blamed_on_model) == 0

#     def _fetch_logprobs(
#         self, tokens: TokList, mask: TokList | None = None
#     ) -> npt.NDArray[np.float32]:
#         """
#         Fetch logprobs from the teacher model.
#         """
#         response = self.bus_completer.completion(prompt=[tokens], logprobs=0, echo=True, max_tokens=0)
#         logprobs = none_throws(response.choices[0].logprobs)
#         token_logprobs = none_throws(logprobs.token_logprobs)[1:]  # first token is always nan
#         return np.array(token_logprobs, dtype=np.float32)
