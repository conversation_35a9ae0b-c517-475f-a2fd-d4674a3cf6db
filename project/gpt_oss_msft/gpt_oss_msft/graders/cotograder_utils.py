import chz
import berry
from functools import partial
from qstar.presets.chz_utils import IsOverride, override
from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    GraderService,
    TokenCompleterGraderService,
)
from bus.qos_type import QoSType
from bus_token_completer import BusT<PERSON><PERSON><PERSON>pleter
from token_completer import TokenCompleter

# BUS configuration for CotoGrader
COTOGRADER_BUS_USER = "gpt-oss-rkld"
COTOGRADER_BUS_TOPIC = "az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
COTOGRADER_BUS_LINE = "bus"
COTOGRADER_RENDERER = "harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"
COTOGRADER_REDIS_ID = "cotograder-290"  # Makes it easy to swap out the bus user if we need to

GPT5_MINI_BUS_USER = "swe-main-run"
GPT5_MINI_BUS_TOPIC = "az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted"
GPT5_MINI_BUS_LINE = "bus"
GPT5_MINI_RENDERER = "harmony_v4.0.16_berry_v3_128k_orion_mm_no_budget_truncate"
GPT5_MINI_REDIS_ID = "cotograder-290"  # Makes it easy to swap out the bus user if we need to

GPT5MINI_BUS_ARGV = [
    f"grader_service.token_completer.bus_line={GPT5_MINI_BUS_LINE}",
    "grader_service.token_completer.qos_type=bus_token_completer:QoSType.ROUND_ROBIN_BY_POD",
    f"grader_service.token_completer.topic_mode_or_user={GPT5_MINI_BUS_USER}",
    f"grader_service.token_completer.topic_or_snapshot={GPT5_MINI_BUS_TOPIC}",
    "grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
    "grader_service.token_completer.default_timeout=1200",
    f"grader_service.redis_config_id={GPT5_MINI_REDIS_ID}",
    "grader_service=TokenCompleterGraderService",
    f"renderer_name={GPT5_MINI_RENDERER}",
]

@chz.chz(typecheck=True)
class CotoGraderService(TokenCompleterGraderService, IsOverride):
    token_completer: TokenCompleter.Config = override(
        partial(
            BusTokenCompleter.Config,
            topic_or_snapshot=COTOGRADER_BUS_TOPIC,
            topic_mode_or_user=COTOGRADER_BUS_USER,
            bus_line=COTOGRADER_BUS_LINE,
            qos_type=QoSType.ROUND_ROBIN_BY_POD,
        ),
    )
    redis_config_id: str = COTOGRADER_REDIS_ID

@chz.chz(typecheck=True)
class CotoGraderConfig(BerryGraderConfig, IsOverride):
    grader_reward_multiplier: int = 64
    grader_top_p: float = 0.985


# RKLD supports multiple teachers
RKLD_TOPIC = [
    "az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas",
]
RKLD_TOPIC_MODE_OR_USER = [
    "gpt-oss-rkld",
]