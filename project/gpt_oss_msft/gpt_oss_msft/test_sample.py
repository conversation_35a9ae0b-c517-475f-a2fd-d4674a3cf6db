import math

def _jointly_adaptive_sampling_and_training_beta(
    p_i: float, alpha: float, spi_compute_scale: float, c: float = 0, d: float = 1
) -> tuple[float, float]:
    """
    Jointly-Adaptive Sampling & Training determines the number of samples to roll and the number of samples to train
    on as a function of `p_i`, the estimated pass rate, `alpha`, the relative cost of training compute to sampling
    compute, `spi_compute_scale` controls the overall scale of compute for the instance.

    The reward for each instance is p^c * (1-p)^(d-1) where p is the pass rate.
    c = 1, d = 1 is pass rate
    c = 0, d = 1 is MLPR
    c = 0, d = 0 is MLPR - MLER
    so the default corresponds to MLPR.

    Returns a tuple of the form: `(number_of_samples_to_train_on, number_of_samples_to_roll)`.

    See https://www.notion.so/openai/Jointly-Adaptive-Sampling-Training-828aae6070ae412fafc276b73b8f808c
    for more details of the MLPR case,
    https://www.notion.so/openai/MLER-Objectives-PR-Independence-Positive-Negative-Symmetry-9652d638a63148178becaa2e6bfae986
    for the details of the MLPR - beta MLER case,
    https://www.notion.so/openai/Part-III-General-Families-of-Objectives-Pass-Rate-Priors-Instance-Informativity-7a462d0bbbe04c65bb531e1d69f521dc
    for the general Beta objective case.
    """
    p_lower = alpha / (1 + 2 * alpha)  # the lower end of the pass rate where we train on everything
    p_upper = (alpha + 1) / (
        1 + 2 * alpha
    )  # the upper end of the pass rate where we train on everything

    # function that controls the train on all positives/negatives branch
    def _eta(p: float, alpha: float) -> float:
        return p**1.5 * math.sqrt((1 + alpha) / (1 + p * alpha))

    # coefficient from the choice of reward function
    if p_i == 0.0 and (c - 1) < 0 or p_i == 1.0 and (d - 1) < 0:
        dJdp = 0
    else:
        dJdp = p_i ** (c - 1) * (1 - p_i) ** (d - 1)

    T = spi_compute_scale * dJdp

    if p_i <= p_lower:
        # if the instance is hard, we follow this branch
        # where we roll to get a fixed number of positive samples
        # and then train on that number of samples
        # (the specific samples are determined by jalloc)
        positives = T * _eta(p_i, alpha)
        # and how many total samples
        number_of_samples_to_roll = positives / p_i if positives > 0 else 1
        return (positives, number_of_samples_to_roll)
    elif p_i >= p_upper:
        # if the instance is easy, we follow this branch
        # where we roll to get a fixed number of negative samples
        # and then train on that number of samples
        # (the specific samples are determined by jalloc).
        negatives = T * _eta(1 - p_i, alpha)
        # and how many total samples
        number_of_samples_to_roll = negatives / (1 - p_i) if negatives > 0 else 1
        return (negatives, number_of_samples_to_roll)
    else:
        # in this range intermediate range, we directly compute how many total samples
        # we roll, and then we train on ALL of these samples
        # (again if we end up with more or less the specifics will be determined by jalloc)
        number_of_samples_to_roll = T * math.sqrt((1 - p_i) * p_i)
        return (number_of_samples_to_roll, number_of_samples_to_roll)


laplace_smooth_pass_rate_constant = 1.0
sampling_alpha = 3.0
len_samples = 32
num_correct = 31
target_samples_per_instance = 32
c = d = 0.05

laplace_smoothed_pass_rate_estimate = (
    num_correct + laplace_smooth_pass_rate_constant
) / (len_samples + 2 * laplace_smooth_pass_rate_constant)

_, total_samples_needed = _jointly_adaptive_sampling_and_training_beta(
    laplace_smoothed_pass_rate_estimate,
    sampling_alpha,
    target_samples_per_instance,
    c=c,
    d=d,
)

print(total_samples_needed)