from typing import Any, Awaitable, Callable
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.mrfs_setup import (
    clean_up_repo_name,
    get_repo_directory,
    setup_repo_with_gt_metadata
)
from gpt_oss_msft.tools.oai_coreutils_setup import setup_coreutils

from caas.terminal.api import TerminalSession

async def mrfs_setup_fn_coreutils(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    """
    Thin wrapper over regular RFS setup that installs oai coreutils
    """
    repo_name = clean_up_repo_name(datapoint["metadata"].get("repo_name", "workdir"))
    repo_root = get_repo_directory(repo_name)
    await setup_repo_with_gt_metadata(terminal_session, datapoint["metadata"], random_drop_packages=False)
    await setup_coreutils(terminal_session.session, {}, repo_root=repo_root, setup_python_tools=True)
