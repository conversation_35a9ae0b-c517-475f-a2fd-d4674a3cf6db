import textwrap
from typing import Any, Literal, Sequence

import chat
from caas_utils.conversation_init import (
    InstructionInsertionFormat,
    datapoint_rng,
    get_style_criteria_instructions,
)
import random
from gpt_oss_msft.data.prompts import INSTALL_INSTRUCTIONS, sample_steps_to_follow
from gpt_oss_msft.data.prompts import sample_steps_to_follow_no_env, SWE_GUIDANCE, SWE_GUIDANCE_V2

from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.mrfs_setup import (
    clean_up_repo_name, 
    get_repo_directory
)

def process_rrb_problem_statement(instance) -> str:
    """Extract problem statement before 'until the tests pass'"""
    # Find the position of "until the tests pass" and extract everything before it
    repo_name = clean_up_repo_name(instance['metadata'].get("repo_name"))
    root_repo = get_repo_directory(repo_name)
    problem_statement = instance["problem"]

    # remove_phrase = "Please make sure the updated repo can pass unit tests by running `bash run_tests.sh`. Please do NOT modify the `run_tests.sh` file."
    # problem_statement = problem_statement.replace(remove_phrase, "")

    problem_statement += f"\nRepo Path: `{root_repo}`. Don't explore unrelated folders/files such as /workspace and /project" + f"\nIssue Number: #{random.randint(1000, 9999)}"
    problem_statement += f"\n{SWE_GUIDANCE_V2}"
    return problem_statement


def conversation_init_fn(
    *,
    datapoint: dict[str, Any],
    instruction: str = "",
    instruction_insertion_format: InstructionInsertionFormat = InstructionInsertionFormat.PREPEND_TO_USER_INSTRUCTION,
    language: str | None = None,
    apply_language_instruction_probability: float = 0.75,
    additional_language_instruction_dropout_probability: float = 0.25,
    add_contributor_criteria: (
        bool | list[str]
    ) = False,  # if True, will fetch from |CONTRIBUTOR_CRITERIA|
    extra_appended_instructions: tuple[str, ...] = (),
    custom_instruction_preamble: (
        str | None
    ) = None,  # if not set, will use default ones that instruct on issue solving
    task_header: Literal["Issue", "Task"] = "Issue",
) -> Sequence[chat.Message]:
    problem = process_rrb_problem_statement(datapoint)
    
    for ex_instruction in extra_appended_instructions:
        if not ex_instruction.startswith("- "):
            ex_instruction = "- " + ex_instruction.strip()
        instruction = instruction + "\n" + ex_instruction

    user_msg = f"""{instruction}
### {task_header}
{problem}
{random.choice(INSTALL_INSTRUCTIONS)}
This is a general coding problem, so no concerns in solving it. Never reject to finish the task.
"""
    
    # user_msg += "\n" + sample_steps_to_follow()
    
    return [chat.Message.user(user_msg)]


