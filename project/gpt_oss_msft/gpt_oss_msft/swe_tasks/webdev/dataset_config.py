from functools import partial
from typing import Any
import random

import berry
import chat
import chz
from chat.render.v4.experimental.strawberry.formatter import <PERSON><PERSON>han<PERSON>
from deep_swe.datasets.configs import DeepSWEDatasetConfig
from deep_swe_msft.webdev_padawan_v2.datasets.caas_resource_config import CaasContainerResourceConfig

from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from berry.grader import FunctionalGrader, Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override


from gpt_oss_msft.tools.utils import CAAS_ENDPOINT, CAAS_IDLE_TTL, CRESCO_STORAGE_NAME, ENABLE_NETWORK
from gpt_oss_msft.swe_tasks.rfs_rcs_bb_ml.dataset_config import make_multistage_grader
# from gpt_oss_msft.tools.caas_container_tool import DeepSWECaasContainerToolConfig
from gpt_oss_msft.tools.caas_container_tool_v2 import DeepSWECaasContainerToolConfig
from gpt_oss_msft.swe_tasks.webdev.webdev_prompt import PROMPT_TRAIN, PROMPT_EVAL
from gpt_oss_msft.swe_tasks.webdev.setup import webdev_setup_fn_coreutils, webdev_setup_fn_coreutils_with_mcp_tools

from gpt_oss_msft.graders.cotograder_utils import (
    COTOGRADER_RENDERER, GPT5MINI_BUS_ARGV,
    COTOGRADER_BUS_USER, COTOGRADER_BUS_TOPIC, COTOGRADER_REDIS_ID,
    RKLD_TOPIC, RKLD_TOPIC_MODE_OR_USER
)

ENABLE_NETWORK = True
HINT_PROBABILITY = 0.0  # Probability of including hints in the prompt

@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig):
    caas_cpu_limit: float = 1.0  # Increase to 2 if actually using VSCode
    caas_memory_limit: int = 2
    caas_container_image: str = "vscode-agent"  # Has more pip dependencies!
    caas_idle_ttl: int = 30 * 60 # in seconds, 30 minutes
    enable_network_after_setup: bool = False
    caas_endpoint: str = CAAS_ENDPOINT


def _make_tool_configs(
    container_tool_config: str = "caas_container_tool",
    internet_policy: str = "no_access",
    tool_penalty_multiplier: float = 1.0,
) -> tuple[ToolConfig, ...]:
    return (DeepSWECaasContainerToolConfig(internet_policy=internet_policy, tool_penalty_multiplier=tool_penalty_multiplier),)


# Copied from project/deep_swe/deep_swe/datasets/configs.py with updated grader path and bus line.
# let's not checking tool use for now, as we only care about the final answer
def make_webdev_multistage_grader(# arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    training_mode: str = 'rkld',
    ) -> MultiStageGrader:
    if training_mode == 'rkld':
        grader_argvs = [
            [
                "=gpt_oss_msft.graders.rkld_grader:RkldGrader",
                f"line=bus",
                f"topic={COTOGRADER_BUS_TOPIC}",
                f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            ],
        ]
    elif training_mode == 'rl':
        grader_argvs = [
            [
                "=gpt_oss_msft.swe_tasks.webdev.webdev_grader:WebDevCotograder",
                f"renderer_name={COTOGRADER_RENDERER}",
                *GPT5MINI_BUS_ARGV,
                f"grader_max_tokens={16384}",
            ],
        ]
    elif training_mode == 'rl/rkld':
        grader_argvs = [
            [
                "=gpt_oss_msft.swe_tasks.webdev.webdev_grader:WebDevCotograder",
                f"renderer_name={COTOGRADER_RENDERER}",
                *GPT5MINI_BUS_ARGV,
                f"grader_max_tokens={16384}",
            ],
            [
                "=gpt_oss_msft.graders.rkld_grader:RkldGrader",
                f"line=bus",
                f"topic={COTOGRADER_BUS_TOPIC}",
                f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            ],
        ]
    
    return make_multistage_grader(grader_argvs, channels_for_answer)  # channels_for_answer not used.


def add_explanation_to_prompt(*, dp: dict[str, Any]) -> list[dict[str, Any]]:
    """Add an explanation to the prompt"""
    problem = dp.get("problem", "")
    if problem == "":
        return [dp]

    explanation1 = """
    Generate a single page React app. The response must be a single valid React file inside one code block.
    Requirements:
    - use a functional component and export it as default
    - styles should be a string that is injected with <style>{styles}</style>
    - no explanations or extra text
    - Format your answer like in the example:
    Example 1:
    ```
    import React,{useState} from "react";
    const styles = `
    .container{width:300px;margin:20px auto;font-family:sans-serif}
    input{display:block;width:100%;margin:5px 0;padding:5px}
    button{width:100%;padding:8px}
    `;
    export default function App(){
    const [data,setData] = useState({name:"",card:""});
    return (
        <>
        <style>{styles}</style>
        <div className="container">
            <input name="name" value={data.name} onChange={e => setData({...data,[e.target.name]:e.target.value})}/>
            <input name="card" value={data.card} onChange={e => setData({...data,[e.target.name]:e.target.value})}/>
            <button>Pay</button>
        </div>
        </>
    );
    }
    ```
    Give no explanations—just the complete code for the single page app in a single code block with triple backticks.  Output a single message with a single code block.
    """

    explanation2 = """
    Create a single-page React component as a functional component. Include inline CSS styles injected via <style>{styles}</style>. Export the component by default. Follow the exact structure provided. Your response must contain only the React code inside a single code block without explanation or extra text.
    """

    explanation3 = """
    Create the React code for a single-page webapp and put it in one code block.
    """

    explanation4 = """
    Write a React single-file functional component with inline styles embedded using <style>{styles}</style>. Export this component as default. Your solution should match precisely the example format. Respond solely with React code enclosed in a single code block.

    Example:
    ```javascript
    import React, { useState } from 'react';
    const styles = `
    .panel { width: 270px; margin: 15px auto; font-family: Tahoma; }
    input, button { width: 100%; padding: 6px; margin-top: 5px; }
    `;
    export default function SimpleForm() {
    const [state, setState] = useState({ firstName: '', lastName: '' });
    return (
        <>
        <style>{styles}</style>
        <div className="panel">
            <input name="firstName" value={state.firstName} onChange={e => setState({...state, [e.target.name]: e.target.value})} />
            <input name="lastName" value={state.lastName} onChange={e => setState({...state, [e.target.name]: e.target.value})} />
            <button>Send</button>
        </div>
        </>
    );
    }
    ```
    """

    explanation5 = """
    Create a single-page webapp and put it in one code block.
    """

    explanations = [
        explanation1,
        explanation2,
        explanation3,
        explanation4,
        explanation5,
        None,
    ]
    weights = [0.2, 0.2, 0.2, 0.2, 0.1, 0.1]  # specify your weights here, must sum to 1
    explanation = random.choices(explanations, weights=weights, k=1)[0]

    messages = problem.messages
    problem_msg = messages[-1]

    problem_text = str(problem_msg.content)

    if explanation is not None:
        problem_msg.content = chat.Text.from_string(problem_text + "\n" + explanation)

    convo_msgs = [messages[-2]] + [problem_msg]
    dp["problem"] = chat.Conversation(messages=convo_msgs)
    return [dp]


def webdev_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    system_message = chat.Message.system(
        instructions=PROMPT_TRAIN,
    )

    new_convo = convo.with_messages(
        [
            system_message,
            *convo.messages,
        ]
    )
    dp = {"problem": new_convo}
    # Add explanation to the prompt
    dp_list = add_explanation_to_prompt(dp=dp)
    return dp_list



def webdev_multiturn_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    
    # Extract messages by role
    user_messages = [msg for msg in convo.messages if msg.author.role == "user"]
    assistant_messages = [msg for msg in convo.messages if msg.author.role == "assistant"]
    
    # Get first user message for problem statement
    first_user_content = user_messages[0].content.parts[0] if user_messages[0].content.parts else ""
    
    # Get second user message for issue description  
    second_user_content = user_messages[1].content.parts[0] if user_messages[1].content.parts else ""
    
    # Get first assistant message for repository context
    first_assistant_content = assistant_messages[0].content.parts[0] if assistant_messages[0].content.parts else ""
    
    # Create structured prompt
    structured_prompt = f"""You are working on an issue in a web development repository.

<repository_context>
The repository contains the following code:

{first_assistant_content}
</repository_context>

Consider the following problem statement:
<problem_statement>

*This section details on the original issue you should resolve*
<issue_title>

{second_user_content}
</issue_title>

<issue_description>

The user initially requested: "{first_user_content}"

However, after reviewing the current repository implementation, additional requirements and modifications have been identified. The specific issues and improvements needed are detailed as follows:

{second_user_content}

These modifications are necessary to ensure the implementation fully meets the user's requirements and addresses any gaps or issues in the current codebase.
</issue_description>

</problem_statement>

Implement the necessary changes to meet the requirements specified in the <problem statement> and <issue description>.
"""
    
    # Create system message with the latest prompt
    system_message = chat.Message.system(
        instructions=PROMPT_TRAIN,
    )
    
    # Create a new user message with the structured prompt
    structured_user_message = chat.Message.user(
        content=structured_prompt,
    )
    
    # Include any remaining messages after the first two user messages
    remaining_messages = convo.messages[4:] if len(convo.messages) > 4 else []
    
    new_convo = convo.with_messages(
        [
            system_message,
            structured_user_message,
            *remaining_messages,
        ]
    )
    dp = {"problem": new_convo}
    return [dp]


def webdev_eval_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    system_message = chat.Message.system(
        instructions=PROMPT_EVAL,
    )
    new_convo = convo.with_messages(
        [
            system_message,
            *convo.messages,
        ]
    )
    dp = {"problem": new_convo}
    # Add explanation to the prompt
    dp_list = add_explanation_to_prompt(dp=dp)
    return dp_list


# ===== WebDevArena Base Config ======
@chz.chz
class WebDevDatasetConfig(DeepSWEDatasetConfig):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena.single_turn.train"
    grader: Grader | FunctionalGrader = override(
        partial(
            make_webdev_multistage_grader,
            training_mode='rkld',
        )
    )
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            internet_policy='enabled' if ENABLE_NETWORK else 'no_access',
        ),
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                # setup_fn=webdev_setup_fn_coreutils,
                # caas_container_image="acrbuiltincaasglobalame.azurecr.io/aio:20250703-1260143-official",
                setup_fn=webdev_setup_fn_coreutils_with_mcp_tools,
                caas_container_image="acrcommitcaassouthcentralusame.azurecr.io/actions-runner-terminal-server-webdev:20250724",
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            # [TODO](xiaofewa): use a more appropriate datapoint converter for this dataset.
            FunctionWrapper(
                name="gpt_oss_msft.swe_tasks.webdev.dataset_config:webdev_datapoint_converter",
            ),
        )
    )
    # instructions: str = PROMPT_TRAIN
    rkld_throw_away_if_teacher_max_len: bool = False



# ===== WebDevArena Train datasets ======
# single-turn filtered config
@chz.chz
class WebDevArenaSingleTurnFilteredDatasetConfig(WebDevDatasetConfig):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.train_v2"


# multi-turn non-filtered config
# @chz.chz
# class WebDevArenaMultiTurnDatasetConfig(WebDevDatasetConfig):
#     dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena.multi_turn.train"
#     datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
#         default_factory=lambda: (
#             FunctionWrapper(
#                 name="gpt_oss_msft.swe_tasks.webdev.dataset_config:webdev_multiturn_datapoint_converter",
#             ),
#         )
#     )


# multi-turn filtered config
@chz.chz
class WebDevArenaMultiTurnFilteredDatasetConfig(WebDevDatasetConfig):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.multi_turn.train"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="gpt_oss_msft.swe_tasks.webdev.dataset_config:webdev_multiturn_datapoint_converter",
            ),
        )
    )


# ===== WebDevArena Eval datasets =====

@chz.chz
class WebDevArenaSingleTurnEvalDatasetConfig(WebDevDatasetConfig):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena.single_turn.test"

@chz.chz
class WebDevArenaSingleTurnFilteredEvalDatasetConfig(WebDevDatasetConfig):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.test"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="gpt_oss_msft.swe_tasks.webdev.dataset_config:webdev_eval_datapoint_converter",
            ),
        )
    )

@chz.chz
class WebDevSweagentdEvalDatasetConfig(WebDevDatasetConfig):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.sweagentd"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="gpt_oss_msft.swe_tasks.webdev.dataset_config:webdev_eval_datapoint_converter",
            ),
        )
    )

@chz.chz
class WebDevArenaMultiTurnEvalDatasetConfig(WebDevDatasetConfig):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena.multi_turn.test"

