PREFIX = """
## General Instructions
Read the following conversation between a user and an AI assistant, and evaluate the web development implementation based on the criteria provided.
""".strip()

WEBDEV_CRITERIA_RUBRIC = """
You are given a model solution for a web development task. Your task is to verify whether the model completed the task as expected according to the rubric. The model messages may be truncated for efficiency and focus on the actions that the model took to complete the task, e.g., function calls.
You should not consider the system prompt as a part of the model solution.

## Criteria

1. criteria1: **User Request Fulfillment**
   - The model MUST implement the core features requested by the user
   - The code MUST work and perform the main requested operations
   - Basic functionality MUST be present and working (forms, buttons, navigation, etc.)
   - The implementation MUST reasonably address the user's requirements
   - Minor edge cases or perfect error handling are not required for this criterion

2. criteria2: **Modern Web Development Practices**
   - The model MUST use appropriate scaffolding tools when creating new projects:
     - React apps: `npx create-next-app@latest` or `npm create vite@latest`
     - Next.js apps: `npx create-next-app@latest`
     - Vue apps: `npm create vue@latest`
   - The model MUST use npm/yarn for package management and build processes
   - The code should follow modern JavaScript/TypeScript patterns (ES6+, hooks for React, composition API for Vue, etc.)
   - The development server should be started during development (`npm start`, `npm run dev`, etc.)

3. criteria3: **Web Application Development**
   - The model should write or modify web application code (HTML, CSS, JavaScript/TypeScript, React, Vue, etc.)
   - The code should be reasonably functional and follow modern web development practices
   - The model should create proper file structures, preferably using scaffolding tools
   - The solution should demonstrate actual web development work with proper component organization
   - The application should build successfully without critical errors

4. criteria4: **Design and User Experience**
   - The model should implement a reasonable UI/UX design that is functional and user-friendly
   - Styling should be applied (CSS, Tailwind, styled-components, or other styling solutions)
   - The layout should be responsive or at least well-structured for the target viewport
   - Interactive elements should have appropriate visual feedback (hover states, active states, etc.)
   - The design should follow basic accessibility principles (semantic HTML, proper contrast, etc.)
   - Perfect pixel-perfect design is not required, but the UI should be presentable and functional

§ Conversation between user and AI assistant:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria4". Do not answer other questions or provide additional commentary - strictly follow the JSON format below.
The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification. 
Example:
{
   "criteria1": {
      "satisfied": true,
      "explanation": "The model implemented all requested features and the code works as expected."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model did not use appropriate scaffolding tools for project creation."
   },
   "criteria3": {
     "satisfied": true,
     "explanation": "The model wrote functional web application code following best practices."
   },
   "criteria4": {
     "satisfied": true,
     "explanation": "The model implemented a clean, responsive design with proper styling and good UX."
   }
}
""".strip()

from pydantic import BaseModel
from tenacity import retry, retry_if_not_exception_type, stop_after_attempt, wait_fixed
from typing import Sequence, Any, Callable
import berry
import hashlib
import blobfile
from functools import partial

import structlog

import chz
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)
from caas_swe_bench_train_v2.cotograders import _JsonCotograder
from deep_swe_msft.padawan_graders.system_prompt_following_grader_hard import _extract_model_actions_with_token_limit

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]
logger = structlog.stdlib.get_logger(component=__name__)

REWARD_NAME = "webdev_cotograder"

from caas.commands import DownloadFileFromContainer, Exec
from caas.terminal.api import TerminalSession
from caas import NotFoundError

class UploadEnvResponse(BaseModel):
    blob_path: str
    env_hash: str

import asyncio
import concurrent.futures
import threading
from typing import Awaitable, TypeVar

_T = TypeVar("_T")
_RUN_CORO = None
_LOCK = threading.Lock()


def run_coro(coro: Awaitable[_T]) -> _T:
    """
    Run a coroutine to run in an asyncio event loop in a separate thread and
    return the result.

    We do this because the qstar code is mostly sync, but we need to run async
    code, for example the CAAS client library code. One worker thread (and
    therefore one asyncio event loop) is created per process.
    """
    global _RUN_CORO
    with _LOCK:
        if _RUN_CORO is None:
            loop: asyncio.AbstractEventLoop | None = None
            loop_ready = threading.Event()

            def _target() -> None:
                nonlocal loop
                assert loop is None
                loop = asyncio.new_event_loop()
                loop_ready.set()
                loop.run_forever()

            thread = threading.Thread(target=_target, daemon=True)
            thread.start()
            loop_ready.wait()
            assert loop is not None

            def _run_coro(coro: Awaitable[_T]) -> concurrent.futures._base.Future[_T]:
                assert loop is not None
                return asyncio.run_coroutine_threadsafe(coro, loop)

            _RUN_CORO = _run_coro
    return _RUN_CORO(coro).result()


@chz.chz(typecheck=True)
class WebDevCotograder(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=64)
    reward_name: str = "web_dev_cotograder"

    def construct_blob_path(self, sample_id: str, hash_string: str = "") -> str:
        # Construct the blob path for the screenshots tarball
        # blob path template is like az://orngscuscresco/twapi/mini/e/keli-<experiment_id>/peashooter/image_asset_pointers/<sample_id>/screenshots_<hash>.tar.gz
        from peashooter.global_state import global_state
        
        if hash_string:
            filename = f"screenshots_{hash_string}.tar.gz"
            blob_path = blobfile.join(global_state().blobstore_experiment_root, "image_asset_pointers", sample_id, filename)
        else:
            # For backward compatibility, if no hash_string provided, just use sample_id
            filename = f"screenshots.tar.gz"
            blob_path = blobfile.join(global_state().blobstore_experiment_root, "image_asset_pointers", sample_id, filename)

        return blob_path

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_fixed(30),
        reraise=True,
        retry=retry_if_not_exception_type(NotFoundError),
    )
    async def download_then_upload_screenshots(
        self,
        terminal_session: TerminalSession,
        blob_path_fn: Callable[[str], str],
        logging_metadata: dict | None = None,
    ) -> UploadEnvResponse:
        """Download screenshot files from /root/screenshots/* of the container, and then upload to blob storage"""
        logging_metadata = logging_metadata or {}
        screenshots_dir = "/root/screenshots"
        tar_path = "/root/screenshots.tar.gz"
        
        # Check if screenshots directory exists
        try:
            await terminal_session.session.run(
                Exec(
                    cmd=["ls", screenshots_dir],
                    workdir="/root",
                    timeout=10,
                )
            )
            logger.info(f"Screenshots directory exists: {screenshots_dir}", **logging_metadata)
            with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
                f.write(f"Screenshots directory exists: {screenshots_dir}\n")
        except Exception as e:
            logger.warning(f"Screenshots directory not found: {screenshots_dir}", **logging_metadata)
            with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
                f.write(f"Screenshots directory not found: {screenshots_dir}\n")
            return UploadEnvResponse(blob_path="", env_hash="")

        # Create tarball of screenshots directory
        logger.info(f"Creating tarball of screenshots from {screenshots_dir}", **logging_metadata)
        with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
            f.write(f"Creating tarball of screenshots from {screenshots_dir}\n")
        await terminal_session.session.run(
            Exec(
                cmd=["tar", "-czf", tar_path, "-C", "/root", "screenshots"],
                workdir="/root",
                timeout=120,
            )
        )
        logger.info(f"Successfully created screenshots tarball", **logging_metadata)
        with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
            f.write(f"Successfully created screenshots tarball\n")

        # Download the tarball from the container
        tar_bytes = await terminal_session.session.run(DownloadFileFromContainer(tar_path))
        
        # Ensure tar_bytes is actually bytes
        if not isinstance(tar_bytes, bytes):
            with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
                f.write(f"Error: Downloaded data is not bytes, got {type(tar_bytes)}\n")
            return UploadEnvResponse(blob_path="", env_hash="")
        
        if len(tar_bytes) == 0:
            with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
                f.write(f"Error: Downloaded tarball is empty\n")
            return UploadEnvResponse(blob_path="", env_hash="")
            
        # create a unique blob path based on experiment_id and the tarball content
        unique_hash = hashlib.sha256(tar_bytes).hexdigest()
        blob_path = blob_path_fn(unique_hash)

        # Upload to blob storage
        logger.info(f"Uploading screenshots tarball to {blob_path}", **logging_metadata)
        with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
            f.write(f"Uploading screenshots tarball to {blob_path}, size: {len(tar_bytes)} bytes\n")
        
        try:
            with blobfile.BlobFile(blob_path, "wb") as f:
                f.write(tar_bytes)
            with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
                f.write(f"Successfully uploaded tarball to blob storage\n")
        except Exception as e:
            with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
                f.write(f"Error uploading to blob storage: {e}\n")
            return UploadEnvResponse(blob_path="", env_hash="")
        
        return UploadEnvResponse(blob_path=blob_path, env_hash=unique_hash)

    async def setup_caas_then_save_screenshots(self, sample: SampleWithCompletion): 
        from caas_autograding.grader import maybe_get_caas_state
        from caas_tool.caas_container import CaasContainer  
        caas_state = maybe_get_caas_state(sample)
        caas_container = CaasContainer.load_sync(caas_state)
        caas_endpoint = caas_container.caas_endpoint
        terminal_session = caas_container.terminal_session
        
        # Create a proper blob_path function that takes a hash and uses construct_blob_path
        def blob_path_fn(hash_str: str) -> str:
            return self.construct_blob_path(sample.sample_id, hash_str)
            
        response = await self.download_then_upload_screenshots(
            terminal_session,
            blob_path_fn,
            logging_metadata={"sample_id": sample.sample_id},
        )
        return response
    
    async def screenshot_saving(self, sample: SampleWithCompletion): 
        try:
            screenshot_response = await self.setup_caas_then_save_screenshots(sample)
            if screenshot_response and screenshot_response.blob_path:
                with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
                    f.write(f"Screenshots uploaded successfully - blob_path: {screenshot_response.blob_path}\n")
            else:
                with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
                    f.write(f"No screenshots found to upload\n")
        except Exception as e:
            with open("/var/log/supervisor/keli_screenshot_saving.log", "a") as f:
                f.write(f"Failed to upload screenshots: {e}\n")
        finally:
            pass

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        # keli: run the screenshot saving coroutine before generating the prompt
        run_coro(self.screenshot_saving(sample))

        conversation_messages = _extract_model_actions_with_token_limit(
            conversation, self.renderer, token_limit=512, tool_truncation_rate=0.25
        )

        webdev_prompt = WEBDEV_CRITERIA_RUBRIC.replace("{conversation_messages}", conversation_messages)
        
        with open("/var/log/supervisor/xiaofewa_rubic_grader.log", "a") as f:
            f.write(f"Grader Prompt: {webdev_prompt}\n")

        return webdev_prompt

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        # Check that all 4 criteria are satisfied
        all_satisfied = all(j[f"criteria{i + 1}"]["satisfied"] is True for i in range(4))
        with open("/var/log/supervisor/xiaofewa_rubic_grader_score.log", "a") as f:
            f.write(f"Grader Result: {all_satisfied}\n")
            for i in range(4):
                criterion = j[f"criteria{i + 1}"]
                f.write(f"  criteria{i + 1}: {criterion['satisfied']} - {criterion['explanation']}\n")
        return GradeFnOutput(
            correct=all_satisfied,
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]
