import os
import shlex
import random
from typing import Any

import structlog

import caas
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas.api import CaasSession
from caas_utils.utils import run_with_retries
from deep_swe_tasks import DeepSWETaskMetadata, SWEBenchV2Task

from gpt_oss_msft.tools.oai_coreutils_setup import setup_coreutils
from gpt_oss_msft.tools.utils import get_strawberry_ace_token
from berry_rfs.mrfs_setup import try_run_command


logger = structlog.get_logger(component=__name__)

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def sbh_setup_fn(
    *,
    datapoint: dict[str, Any],
    session: CaasSession,
    workdir: str | None
) -> None:
    post_setup = [
        f"echo 'cd {workdir}' >> /root/.bashrc",
        f"chmod -R 777 {workdir}",
        f"cd {workdir}",
    ]
    await session.run(BashScript("\n".join(post_setup), login=True, timeout=900))
    await setup_coreutils(session, {}, repo_root=workdir, setup_python_tools=True)
