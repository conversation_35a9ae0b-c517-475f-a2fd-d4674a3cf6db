from datetime import datetime
from typing import Any, Literal, Sequence

import chat
from caas_utils.conversation_init import InstructionInsertionFormat
from prbot_msft.swebench_hard.example import REPO_DIRECTORY

from gpt_oss_msft.data.prompts import sample_steps_to_follow_no_env, SWE_GUIDANCE, SWE_GUIDANCE_V2


def log_with_timestamp(message: str, instance_id: str = None):
    with open(f"/var/log/supervisor/swebenchhard_conversation_init_noexec.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}][{instance_id}]: {message}\n")


INSTR_PREAMBLE = f"""
You will be provided with a full code base and an issue statement explaining a problem to resolve.
The code base is located at `{REPO_DIRECTORY}` with README file inside

Please resolve the issue. See the issue below.
""".strip()

REPAIR_INSTRUCTION = """
You will be provided with a full code base and an issue statement explaining a problem to resolve.
The code base `{repo_simple_name}` is already unzipped and located at `{repo_directory}` with README file inside.

- Keep changes consistent with the style of the existing codebase. Changes should be minimal and focused on the issue.
- Prefer fixing the issue at the root cause rather than applying surface-level patches, when possible.
- Ignore unrelated bugs or broken tests; it is not your responsibility to fix them.
- If the environment is not set up, set up the environment. You have the access to the internet for environment setup.
- Completely solve the user request without exploring new things. 
- Update or add a regression test for your changes. Without your fix, this test should fail.
    - The repo doesn't have the necessary tests in place. You must implement. 
    - Test your solution thoroughly to ensure it works as intended.
- Your final answer must be a well-formatted pull request, following the repo's PULL_REQUEST_TEMPLATE.md (if one exists).

## Issue.
<issue>
{issue_description}
</issue>
""" + '\n' + SWE_GUIDANCE_V2

REPAIR_SUFFIX = """
Please summarize all the changes you made in the end.
""".strip()


def conversation_init_fn(
    *,
    datapoint: dict[str, Any],
    instruction: str = REPAIR_INSTRUCTION,
    instruction_insertion_format: InstructionInsertionFormat = InstructionInsertionFormat.PREPEND_TO_USER_INSTRUCTION,
    language: str | None = None,
    apply_language_instruction_probability: float = 0.75,
    additional_language_instruction_dropout_probability: float = 0.25,
    add_contributor_criteria: (
        bool | list[str]
    ) = True,  # if True, will fetch from |CONTRIBUTOR_CRITERIA|
    extra_appended_instructions: tuple[str, ...] = (),
    custom_instruction_preamble: (
        str | None
    ) = None,  # if not set, will use default ones that instruct on issue solving
    task_header: Literal["Issue", "Task"] = "Issue",
) -> Sequence[chat.Message]:
    instance_id = datapoint.get("metadata", {}).get("instance_id", None)
    log_with_timestamp(
        f"conversation_init_fn called with instruction: {instruction}", instance_id=instance_id
    )

    problem = datapoint.get("metadata", {}).get("problem_statement", "")
    repo_name = datapoint.get("metadata", {}).get("repo", "")
    log_with_timestamp(
        f"conversation_init_fn called with problem: {problem}, repo_name: {repo_name}",
        instance_id=instance_id,
    )

    user_msg = instruction.format(
        repo_simple_name=repo_name,
        repo_directory="/testbed",
        issue_description=problem,
    )
    # user_msg = user_msg + "\n" + sample_steps_to_follow_no_env()
    user_msg = user_msg + "\n" + REPAIR_SUFFIX
    log_with_timestamp(
        f"conversation_init_fn generated user_msg: {user_msg}", instance_id=instance_id
    )
    return [chat.Message.user(user_msg)]
