import chz
from qstar.presets.chz_utils import IsO<PERSON>ride, override
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from berry.grader import FunctionalGrader, Grader
from chat.render.v4.experimental.strawberry.formatter import Berry<PERSON>hannel
from deep_swe.datasets.configs import DeepSWEVardiscProducer, conversation_converter

from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from qstar.curriculums.variant_producer import VariantProducer
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.multi_stage_grader import MultiStageGrader

from functools import partial
from typing import Any, Literal, cast

from qstar.curriculums.variant_producer import (
    VarDiscountingVariantProducer,
    VariantProducer,
)

from gpt_oss_msft.graders.cotograder_utils import (
    COTOGRADER_BUS_USER, COTOGRADER_BUS_TOPIC, COTOGRADER_RENDERER, COTOGRADER_REDIS_ID,
    R<PERSON><PERSON>_TOPIC, RKLD_TOPIC_MODE_OR_USER, GPT5MINI_BUS_ARGV
)
from gpt_oss_msft.tools.utils import CAAS_ENDPOINT, CRESCO_STORAGE_NAME, ENABLE_NETWORK, RFS_LANG_TO_IMAGE_NAME
from gpt_oss_msft.tools.caas_container_tool_v2 import DeepSWECaasContainerToolConfig
from gpt_oss_msft.swe_tasks.sbh.setup import sbh_setup_fn
from gpt_oss_msft.swe_tasks.rfs_rcs_bb_ml.dataset_config import make_multistage_grader, enforce_commands
from gpt_oss_msft.swe_tasks.sbt.conversation_init import conversation_init_fn as sbt_conversation_init_fn

from prbot_msft.configs.swebench_hard import (
    make_swebenchhard_multistage_grader,
    get_sbh_criteria_grader_chz_argv
)
from prbot_msft.configs.caas_container_tool import SweBenchHardCaasContainerResourceConfig, SweBenchHardSetupFn

def _make_tool_configs(
    container_tool_config: str = "caas_container_tool",
    internet_policy: str = "no_access",
    tool_penalty_multiplier: float = 1.0,
) -> tuple[ToolConfig, ...]:
    return (DeepSWECaasContainerToolConfig(internet_policy=internet_policy, tool_penalty_multiplier=tool_penalty_multiplier),)

def sbh_conversation_init_fn_wrapper(**kwargs):
    return sbt_conversation_init_fn(
        **kwargs,
        add_contributor_criteria=False,
        get_problem=lambda dp: dp["metadata"]["problem_statement"],
        get_repo=lambda dp: dp["metadata"]["repo"],
        get_issue_numbers=lambda dp: [dp["metadata"]["instance_id"].split("-")[-1]],
    )

def make_sbh_multistage_grader(
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    training_mode: str = 'rkld',
    prompt_mode: str = 'gold',
    sbhv2: bool = False,
) -> MultiStageGrader:
    if training_mode == 'rkld':
        grader_argvs = [
            # [
            #     "=gpt_oss_msft.graders.swe_coto_grader:SWECotograder",
            #     *GPT5MINI_BUS_ARGV,
            # ],
            # [
            #     "=gpt_oss_msft.graders.rkld_grader:RkldGrader",
            #     f"line=bus",
            #     f"topic={COTOGRADER_BUS_TOPIC}",
            #     f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            # ],
            [
                "=gpt_oss_msft.swe_tasks.sbh.sbh_rkld_grader:SBHGTRkldGrader",
                f"line=bus",
                f"topic={COTOGRADER_BUS_TOPIC}",
                f"topic_mode_or_user={COTOGRADER_BUS_USER}",
                f"prompt_mode={prompt_mode}",
            ],
        ]
    elif training_mode == 'rl':
        grader_argvs = []
        if sbhv2:
            grader_argvs.append(
                [
                    "=prbot_msft.configs.swebench_hard:SWEBenchRepairComparisonCotGrader",
                    *GPT5MINI_BUS_ARGV
                ]
            )
        else:
            grader_argvs.append(
                ["=prbot_msft.graders.swebenchhard_repair_grader:SWEBenchHardRepairGrader"], # unitest grader for sbhv1 only
            )
            # grader_argvs.append(
            #     [
            #         "=prbot_msft.configs.swebench_hard:SWEBenchRepairCriteriaCotGrader",
            #         *GPT5MINI_BUS_ARGV
            #     ]
            # )
            # grader_argvs.append(
            #     [
            #         "=prbot_msft.configs.swebench_hard:SWEBenchRepairComparisonCotGrader",
            #         *GPT5MINI_BUS_ARGV
            #     ]
            # )
        
        grader_argvs.append(
            [
                "=gpt_oss_msft.graders.swe_coto_grader:SWECotograder",
                *GPT5MINI_BUS_ARGV,
            ],
        )
    elif training_mode == 'rl/rkld':
        grader_argvs = []
        if sbhv2:
            grader_argvs.append(
                [
                    "=prbot_msft.configs.swebench_hard:SWEBenchRepairComparisonCotGrader",
                    *GPT5MINI_BUS_ARGV
                ]
            )
        else:
            grader_argvs.append(
                ["=prbot_msft.graders.swebenchhard_repair_grader:SWEBenchHardRepairGrader"], # unitest grader for sbhv1 only
            )
            # grader_argvs.append(
            #     [
            #         "=prbot_msft.configs.swebench_hard:SWEBenchRepairCriteriaCotGrader",
            #         *GPT5MINI_BUS_ARGV
            #     ]
            # )
            # grader_argvs.append(
            #     [
            #         "=prbot_msft.configs.swebench_hard:SWEBenchRepairComparisonCotGrader",
            #         *GPT5MINI_BUS_ARGV
            #     ]
            # )

        grader_argvs.extend([
            [
                "=gpt_oss_msft.graders.swe_coto_grader:SWECotograder",
                *GPT5MINI_BUS_ARGV,
            ],
            [
                "=gpt_oss_msft.swe_tasks.sbh.sbh_rkld_grader:SBHGTRkldGrader",
                f"line=bus",
                f"topic={COTOGRADER_BUS_TOPIC}",
                f"topic_mode_or_user={COTOGRADER_BUS_USER}",
                f"prompt_mode={prompt_mode}",
            ],
            # [
            #     "=gpt_oss_msft.graders.rkld_grader:RkldGrader",
            #     f"line=bus",
            #     f"topic={COTOGRADER_BUS_TOPIC}",
            #     f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            # ]
        ])

    return make_multistage_grader(grader_argvs, channels_for_answer)


@chz.chz
class SBHRepairTrainDatasetConfig(HarmonyCompletionDatasetConfig):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = "data.damajercak.swe.upload05202025.rcr_12878.train_hq"
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            internet_policy='enabled' if ENABLE_NETWORK else 'no_access',
        ),
    )
    grader: Grader | FunctionalGrader = override(
        partial(make_sbh_multistage_grader, training_mode='rkld', prompt_mode='hint')
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            SweBenchHardCaasContainerResourceConfig(
                setup_fn=cast(SweBenchHardSetupFn, sbh_setup_fn),
                caas_endpoint=CAAS_ENDPOINT, # sbhv1 rewrite this to eastus2 in prbot_msft
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    max_num_yields: int = 256
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            enforce_commands(probability=0.9),
            conversation_converter("gpt_oss_msft.swe_tasks.sbh.conversation_init:conversation_init_fn"),
            # conversation_converter(func_name="gpt_oss_msft.swe_tasks.sbh.dataset_config:sbh_conversation_init_fn_wrapper"),
        )
    )
    
    rkld_throw_away_if_teacher_max_len: bool = False


@chz.chz
class SBHV2RepairTrainDatasetConfig(SBHRepairTrainDatasetConfig):
    dataset_id: str = "data.jadhuang.swe.upload08182025.sbhv2.vsc.train"
    grader: Grader | FunctionalGrader = override(
        partial(make_sbh_multistage_grader, training_mode='rkld', sbhv2=True, prompt_mode='none')
    )