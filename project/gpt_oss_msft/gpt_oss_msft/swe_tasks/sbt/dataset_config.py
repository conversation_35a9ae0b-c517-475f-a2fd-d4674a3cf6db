from functools import partial
from typing import Any

import structlog

import caas_autograding.grader as caas_graders
import chz
import berry
from berry.function_wrapper import FunctionWrapper

from chat.render.v4.experimental.strawberry.formatter import BerryChannel

from deep_swe.graders.thoroughness_grader import ThoroughnessGrader
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig  # type: ignore
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from qstar.curriculums.variant_producer import (
    VarDiscountingVariantProducer,
    VariantProducer,
)
from berry.grader import FunctionalGrader, Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    TokenCompleterGraderService,
)
from qstar.presets.chz_utils import IsOverride, override
from token_completer import TokenCompleter
from qstar.samplers import BaseSampler

from gpt_oss_msft.graders.cotograder_utils import (
    COTOGRADER_RENDERER, GPT5MINI_BUS_ARGV,
    COTOGRADER_BUS_USER, COTOGRADER_BUS_TOPIC, COTOGRADER_REDIS_ID,
    RKLD_TOPIC, RKLD_TOPIC_MODE_OR_USER
)
from gpt_oss_msft.tools.utils import CAAS_ENDPOINT, CAAS_IDLE_TTL, CRESCO_STORAGE_NAME, ENABLE_NETWORK
from gpt_oss_msft.tools.caas_container_tool_v2 import DeepSWECaasContainerToolConfig
from gpt_oss_msft.swe_tasks.sbt.setup import swe_bench_v2_setup_fn
from gpt_oss_msft.swe_tasks.sbt.caas_resource_config import DeepSWECaasContainerResourceConfig

logger = structlog.stdlib.get_logger(component=__name__)

def _make_tool_configs(
    container_tool_config: str = "caas_container_tool",
    internet_policy: str = "no_access",
    tool_penalty_multiplier: float = 1.0,
) -> tuple[ToolConfig, ...]:
    return (DeepSWECaasContainerToolConfig(internet_policy=internet_policy, tool_penalty_multiplier=tool_penalty_multiplier),)


def conversation_converter(func_name: str, **kwargs: dict[str, Any]) -> FunctionWrapper:
    return FunctionWrapper(
        name="caas_converters:conversation_init_fn",
        kwargs={"func": func_name, **kwargs},
    )


# IMPORTANT: must be paired with IFEnforcementGrader if you have `extra_good_commands`
DEFAULT_ENFORCE_PROBABILITY = 0.5
def enforce_commands(
    probability: float = DEFAULT_ENFORCE_PROBABILITY,
    extra_good_commands: tuple[str, ...] = (),
    extra_bad_commands: tuple[str, ...] = (),
) -> FunctionWrapper:
    return FunctionWrapper(
        name="gpt_oss_msft.data.datapoint_converters:enforce_commands",
        kwargs={
            "probability": probability,
            "extra_good_commands": extra_good_commands,
            "extra_bad_commands": extra_bad_commands,
        },
    )


def make_multistage_grader(
    grader_argvs: list[list[str]] | None = None,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
) -> caas_graders.MultiStageGraderWithAuxReinforcements:
    # NB: we can freely set |channels_for_answer| here. See NOTE [ DeepSWEDatasetConfig.channel_format ]
    if grader_argvs is None:
        grader_argvs = []
    grader_argvs = list(grader_argvs)
    
    blueprint = chz.Blueprint(caas_graders.MultiStageGraderWithAuxReinforcements)
    for ii, grader_argv in enumerate(grader_argvs):
        argv: list[str] = []
        for arg in grader_argv:
            if not arg.startswith(("=", ".")):  # allow wildcards
                arg = "." + arg
            argv.append(f"graders.{ii}{arg}")
        blueprint = blueprint.apply_from_argv(argv)
    if channels_for_answer:
        channels_for_answer_arg = "channels_for_answer=" + ",".join(
            str(c) for c in channels_for_answer
        )
        blueprint = blueprint.apply_from_argv(["..." + channels_for_answer_arg])
    return blueprint.make()


def make_sbt_multistage_grader(
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    training_mode: str = 'rkld',
    prompt_mode: str = 'gold',
) -> MultiStageGrader:
    if training_mode == 'rkld':
        grader_argvs = [
            # [
            #     "=gpt_oss_msft.graders.swe_coto_grader:SWECotograder",
            #     *GPT5MINI_BUS_ARGV,
            # ],
            # [
            #     "=gpt_oss_msft.graders.rkld_grader:RkldGrader",
            #     f"line=bus",
            #     f"topic={COTOGRADER_BUS_TOPIC}",
            #     f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            # ],
            [
                "=gpt_oss_msft.swe_tasks.sbt.sbt_rkld_grader:SBTGTRkldGrader",
                f"line=bus",
                f"topic={COTOGRADER_BUS_TOPIC}",
                f"topic_mode_or_user={COTOGRADER_BUS_USER}",
                f"prompt_mode={prompt_mode}",
            ]
        ]
    elif training_mode == 'rl':
        grader_argvs = [
            [
                "=caas_autograding.grader:TermberryGrader",
                f"grade_fn=deep_swe_msft.swe_bench_train_v2_padawan_v2.graders.unitest_grader:grade_fn_v2",
            ],
            [
                "=gpt_oss_msft.graders.swe_coto_grader:SWECotograder",
                *GPT5MINI_BUS_ARGV,
            ],
        ]
    elif training_mode == 'rl/rkld':
        grader_argvs = [
            [
                "=caas_autograding.grader:TermberryGrader",
                f"grade_fn=deep_swe_msft.swe_bench_train_v2_padawan_v2.graders.unitest_grader:grade_fn_v2",
            ],
            [
                "=gpt_oss_msft.graders.swe_coto_grader:SWECotograder",
                *GPT5MINI_BUS_ARGV,
            ],
            [
                "=gpt_oss_msft.swe_tasks.sbt.sbt_rkld_grader:SBTGTRkldGrader",
                f"line=bus",
                f"topic={COTOGRADER_BUS_TOPIC}",
                f"topic_mode_or_user={COTOGRADER_BUS_USER}",
                f"prompt_mode={prompt_mode}",
            ]
        ]

    return make_multistage_grader(grader_argvs, channels_for_answer)


@chz.chz(typecheck=True)
class SWEBenchTrainV2DatasetConfig(HarmonyCompletionDatasetConfig):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = (
        "data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"
    )
    grader: Grader | FunctionalGrader = override(
        partial(
            make_sbt_multistage_grader,
            training_mode='rkld',
            prompt_mode='hint',
        )
    )
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            internet_policy='enabled' if ENABLE_NETWORK else 'no_access',
        ),
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfig(setup_fn=swe_bench_v2_setup_fn,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL,
                                               enable_network_after_setup=ENABLE_NETWORK),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            enforce_commands(probability=0.9),
            conversation_converter(
                "gpt_oss_msft.swe_tasks.sbt.conversation_init:conversation_init_fn",
            ),
        )
    )

    rkld_throw_away_if_teacher_max_len: bool = False