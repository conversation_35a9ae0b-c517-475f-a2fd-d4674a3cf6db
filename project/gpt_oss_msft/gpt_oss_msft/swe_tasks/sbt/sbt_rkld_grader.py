from typing import Sequence

import chz
import structlog
import numpy as np
import numpy.typing as npt
import tiktoken
from bus.registry import DEFAULT_BUS_LINE
from bus_token_completer.bus_token_completer import BusTokenCompleter
from multimodal_token.toklist import IntTok<PERSON><PERSON>, TokList
from oaicommon.oai_types import none_throws
from prbot_msft.graders.swebenchhard_grader_base import log_to_file_sync
from qstar.common import datapoint, types
from qstar.graders import grader as grader_module
from qstar.sample_completers import sample_completer

from deep_swe_tasks import DeepSWETaskMetadata
from berry.sample import GraderError
from qstar.graders.rkld_grader import RkldGrader as RkldBaseGrader

logger = structlog.stdlib.get_logger(component=__name__)


LOG_FILENAME = "/var/log/supervisor/swebenchtrain_rkld_grader_results.log"

def log_wrapper(message, instance_id):
    return log_to_file_sync(message, instance_id=instance_id, log_file=LOG_FILENAME)


@chz.chz(typecheck=True)
class SBTGTRkldGrader(RkldBaseGrader):
    """
    A simple grader that follows the structure of MathgenGrader but instead of
    grading correctness, it queries a sampling engine for logprobs and returns them.
    """

    topic: str = chz.field(
        doc="The teacher model snapshot, used for routing to the correct bus engine.",
    )

    topic_mode_or_user: str = chz.field(
        doc="The topic mode or user used for creating the engines.",
    )

    line: str = chz.field(
        doc="The bus line to use.",
        default=DEFAULT_BUS_LINE,
    )

    prompt_mode: str = chz.field(
        doc="The customization mode for the teacher prompt. choose from none, gold, hint.",
        default="gold",
    )

    @chz.init_property
    def bus_completer(self) -> BusTokenCompleter:
        completer = BusTokenCompleter(
            topic_or_snapshot=self.topic,
            topic_mode_or_user=self.topic_mode_or_user,
            bus_line=self.line,
        )
        return completer

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:
        """
        Main entry point for grading a batch of samples. We’ll fetch logprobs from the
        bus endpoint and attach them to each sample’s additional_metadata.
        """
        try:
            if self._any_sample_is_multimodal(samples):
                raise ValueError("Multimodal samples have not been tested for RKLD grader.")
        except Exception as e:
            log_wrapper(f"Error checking multimodal samples: {e}", instance_id=None)

        graded_samples = []
        MAX_LEN = 131072
        for sample in samples:
            try:
                metadata = DeepSWETaskMetadata.model_validate(sample.gt_datapoint.metadata)
                assert sample.prompt_tokens is not None, "Prompt tokens must be set."
                prompt_tokens_teacher = sample.ephemeral_metadata["prompt_tokens_teacher"]
                prompt_tokens_student = sample.prompt_tokens
                encoding_name = 'orion_200k'
                encoding = tiktoken.get_encoding(encoding_name)

                if self.prompt_mode == "gold":
                    gold_patch = metadata.task.patch
                    assert gold_patch is not None, "Gold patch must be set"
                    hint_text = (
                        "Hint: The following is a superset of the solution\n" + gold_patch + "\n"
                    )
                    hintonkens = TokList(spans=[IntTokSpan.from_ints(encoding.encode(hint_text))])
                elif self.prompt_mode == "hint":
                    hint_text = sample.gt_datapoint.metadata.get("hint_text", None)
                    assert hint_text is not None, "Hints must be set"
                    with open("/var/log/supervisor/chenliang1_teacher_prompt.txt", "w") as f:
                        f.write(hint_text + "\n")
                    hintonkens = TokList(spans=[IntTokSpan.from_ints(encoding.encode(hint_text))])
                else:
                    hintonkens = TokList()

                if len(hintonkens) >= MAX_LEN // 4:
                    # remove hint tokens if too long
                    hintonkens = TokList()

                # Use teacher prompt tokens + hints + student solution tokens here as the full rollout
                tokens = TokList.concatenate([prompt_tokens_teacher, hintonkens, sample.tokens])
                if len(tokens) > MAX_LEN:
                    if sample.gt_datapoint.dataset_config.rkld_throw_away_if_teacher_max_len:
                        assert False, f"len(tokens) > {MAX_LEN}, so we will not send to teacher"
                    trim = len(tokens) - MAX_LEN

                    teacher_logp = self._fetch_logprobs(
                        tokens.truncate_by(trim),
                        discard=slice(
                            len(prompt_tokens_teacher), len(prompt_tokens_teacher) + len(hintonkens)
                        ),
                    )
                    # pad trimmed token back with mask
                    teacher_logp = np.concatenate([teacher_logp, np.full(trim, -np.inf)])
                else:
                    teacher_logp = self._fetch_logprobs(
                        tokens,
                        discard=slice(
                            len(prompt_tokens_teacher), len(prompt_tokens_teacher) + len(hintonkens)
                        ),
                    )

                # truncate teacher logp to match student logp
                prompt_teacher_len = len(prompt_tokens_teacher)
                prompt_student_len = len(prompt_tokens_student)
                diff = prompt_teacher_len - prompt_student_len
                assert diff >= 0, (prompt_teacher_len, prompt_student_len)
                teacher_logp = teacher_logp[diff:]

                graded_samples.append(
                    sample.with_grade(
                        log_rewards={"neg_rkld": 0},
                        is_correct=self._has_no_model_errors(sample),
                        teacher_logp=teacher_logp,
                    )
                )
            except Exception as e:
                grader_error = GraderError.from_exception(e)
                graded_samples.append(
                    sample.without_reward(errors_blamed_on_system=[grader_error])
                )
                logger.error(f"Exception while grading sample in RKLD Grader: {e}")
        return graded_samples

    def _fetch_logprobs(self, tokens: TokList, discard: slice) -> npt.NDArray[np.float32]:
        response = self.bus_completer.completion(
            prompt=[tokens], logprobs=0, echo=True, max_tokens=0
        )
        logprobs = none_throws(response.choices[0].logprobs)
        token_logprobs = none_throws(logprobs.token_logprobs)
        all_logprobs = np.array(token_logprobs, dtype=np.float32)
        mask = np.ones(all_logprobs.shape, dtype=bool)
        mask[discard] = False  # discard the hint tokens
        mask[0] = False  # first token is always nan and is discarded in all paths
        return np.array(all_logprobs[mask], dtype=np.float32)
    
