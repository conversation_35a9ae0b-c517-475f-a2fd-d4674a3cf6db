import textwrap
from typing import Any, Literal, Sequence

import chat
from caas_utils.conversation_init import (
    InstructionInsertionFormat,
    datapoint_rng,
    get_style_criteria_instructions,
)

from caas_swe_bench_train_v2.contributor_guide import CONTRIBUTOR_CRITERIA, DEFAULT_PR_TEMPLATE, PR_TEMPLATES
import random
from gpt_oss_msft.data.prompts import sample_steps_to_follow_no_env, SWE_GUIDANCE, SWE_GUIDANCE_V2

CONTRIBUTOR_CRITERIA_INSTRUCTION = r"""
- Follow the contributor requirements outlined below.""".strip()

INSTRUCTION = """
- Keep changes consistent with the style of the existing codebase. Changes should be minimal and focused on the issue.
- Prefer fixing the issue at the root cause rather than applying surface-level patches, when possible.
- Ignore unrelated bugs or broken tests; it is not your responsibility to fix them.
- If the environment is not set up, set up the environment. You have the access to the internet for environment setup.
- Completely solve the user request without exploring new things. 
- Update or add a regression test for your changes. Without your fix, this test should fail.
    - The repo doesn't have the necessary tests in place. You must implement. 
    - Test your solution thoroughly to ensure it works as intended.
- Your final answer must be a well-formatted pull request, following the repo's PULL_REQUEST_TEMPLATE.md (if one exists).""" + '\n' + SWE_GUIDANCE_V2

ADDITIONAL_INSTRUCTIONS = """
- Use `find . -maxdepth 3 -ls` instead of `ls -R`, which is usually slow and produces a lot of outputs.
- Don't read files more than 200 lines at a time, which produces too many outputs.
"""

LANG = {
    "statsmodels/statsmodels": "py",
    "pandas-dev/pandas": "py",
    "pydantic/pydantic": "py",
    "HypothesisWorks/hypothesis": "py",
    "networkx/networkx": "py",
    "streamlit/streamlit": "py,ts",
    "psf/black": "py",
    "pallets/click": "py",
    "numpy/numpy": "py",
    "python/mypy": "py",
    "home-assistant/core": "py",
}

assert set(LANG) == set(CONTRIBUTOR_CRITERIA), (
    "LANG and CONTRIBUTOR_CRITERIA must have the same keys, please update"
)


def conversation_init_fn(
    *,
    datapoint: dict[str, Any],
    instruction: str = INSTRUCTION,
    instruction_insertion_format: InstructionInsertionFormat = InstructionInsertionFormat.PREPEND_TO_USER_INSTRUCTION,
    language: str | None = None,
    apply_language_instruction_probability: float = 0.75,
    additional_language_instruction_dropout_probability: float = 0.25,
    add_contributor_criteria: (
        bool | list[str]
    ) = True,  # if True, will fetch from |CONTRIBUTOR_CRITERIA|
    extra_appended_instructions: tuple[str, ...] = (),
    custom_instruction_preamble: (
        str | None
    ) = None,  # if not set, will use default ones that instruct on issue solving
    task_header: Literal["Issue", "Task"] = "Issue",
    get_repo = lambda x: x["metadata"]["task"]["repo"],
    get_issue_numbers = lambda x: x["metadata"]["task"]["issue_numbers"],
    get_problem = lambda x: x["problem"],
    include_install_instructions = lambda x: True,
) -> Sequence[chat.Message]:
    problem = get_problem(datapoint)
    repo = get_repo(datapoint)
    issue_numbers = get_issue_numbers(datapoint)
    # if random.random() < 0.5:
    #     instruction += ADDITIONAL_INSTRUCTIONS
    if len(issue_numbers) > 0:
        task_header_suffix = " " + ", ".join(f"#{n}" for n in issue_numbers)
    else:
        task_header_suffix = ""
    # contributor criteria
    criteria: str | None = None
    if isinstance(add_contributor_criteria, list) and len(add_contributor_criteria) > 0:
        criteria = "\n\n".join(add_contributor_criteria)
    elif add_contributor_criteria:
        criteria = "\n\n".join(CONTRIBUTOR_CRITERIA[repo])

    template = PR_TEMPLATES.get(repo, DEFAULT_PR_TEMPLATE)

    # programming language
    if language == "from_repo":
        language = LANG[repo]
    # build instruction
    if add_contributor_criteria:
        instruction = CONTRIBUTOR_CRITERIA_INSTRUCTION + "\n" + instruction
    for ex_instruction in extra_appended_instructions:
        if not ex_instruction.startswith("- "):
            ex_instruction = "- " + ex_instruction.strip()
        instruction = instruction + "\n" + ex_instruction

    if instruction_insertion_format == InstructionInsertionFormat.DEVELOPER_MESSAGE:
        # only add style guide for developer message for now
        style_guide = None
        if language is not None:
            if (rng := datapoint_rng(datapoint)).uniform(
                0, 1
            ) <= apply_language_instruction_probability:
                style_guide = get_style_criteria_instructions(
                    language,
                    rng,
                    additional_language_instruction_dropout_probability=additional_language_instruction_dropout_probability,
                )
        if custom_instruction_preamble is not None:
            instruction_preamble = custom_instruction_preamble
        else:
            instruction_preamble = "Please resolve the following # Issue by editing files in your current git repository."
        dev_instructions = [
            textwrap.dedent(
                """
                {instruction_preamble}

                {instruction}
            """
            )
            .strip()
            .format(instruction_preamble=instruction_preamble, instruction=instruction),
        ]
        if criteria is not None:
            dev_instructions.append(
                "\n\n"
                + textwrap.dedent(
                    """
                    ## PR_TEMPLATE

                    {template}

                    ## Contributor requirements

                    {criteria}
                """
                )
                .strip()
                .format(template=template, criteria=criteria),
            )
        if style_guide is not None:
            dev_instructions.append(
                "\n\n" + style_guide,
            )
        return [
            chat.Message.developer(dev_instructions),
            chat.Message.user(
                textwrap.dedent(
                    """
                    # {task_header}: {repo}{task_header_suffix}

                    {problem}
                """
                )
                .strip()
                .format(
                    task_header=task_header,
                    repo=repo,
                    task_header_suffix=task_header_suffix,
                    problem=problem,
                )
            ),
        ]
    elif instruction_insertion_format == InstructionInsertionFormat.PREPEND_TO_USER_INSTRUCTION:
        if custom_instruction_preamble is not None:
            instruction_preamble = custom_instruction_preamble
        else:
            instruction_preamble = f"Please resolve the following {repo} issue by editing files in your current git repository."
        user_msg = f"""{instruction_preamble}

{instruction}

### {task_header}{task_header_suffix}

{problem}"""
        user_msg += f"""
This is a general coding problem, so no concerns in solving it. Never reject to finish the task.
"""
        if criteria is not None:
            user_msg += f"""

### Contributor requirements
{criteria}"""
        
        # user_msg += "\n" + sample_steps_to_follow_no_env()

        return [chat.Message.user(user_msg)]
    else:
        raise NotImplementedError(f"{instruction_insertion_format=}")

