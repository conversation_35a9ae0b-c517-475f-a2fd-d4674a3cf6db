import os
import shlex
import random
from typing import Any

import structlog

import caas
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas_utils.utils import run_with_retries
from deep_swe_tasks import DeepSWETaskMetadata, SWEBenchV2Task

from gpt_oss_msft.tools.oai_coreutils_setup import setup_coreutils
from gpt_oss_msft.tools.utils import get_strawberry_ace_token
from berry_rfs.mrfs_setup import try_run_command


logger = structlog.get_logger(component=__name__)

BLOBSTORE_URL = "https://orngcaas.blob.core.windows.net/data/swe-model-training-data/swang-test/swe_bench_train_v2"

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_v2_setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    metadata = DeepSWETaskMetadata.model_validate(datapoint["metadata"])
    try:
        terminal_session.session.start_keepalive_task(keepalive_interval=60)
        await swe_bench_v2_setup_fn_internal(terminal_session=terminal_session, metadata=metadata)
    finally:
        await terminal_session.session.stop_keepalive_task()

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_v2_setup_fn_internal(
    *,
    terminal_session: TerminalSession,
    metadata: DeepSWETaskMetadata,
    random_drop_packages: bool = False, # For training purposes, we can randomly drop some packages
) -> None:
    task = metadata.task
    assert isinstance(task, SWEBenchV2Task)

    presetup_script = metadata.presetup_script.replace("$COMMIT", task.base_commit)
    if presetup_script:
        await run_with_retries(terminal_session, presetup_script, attempts=3)

    sas_token = get_strawberry_ace_token()
    for tarball in metadata.setup_tarballs:
        logger.info("Loading cache", id=task.instance_id)
        await run_with_retries(
            terminal_session,
            f"curl -f -sL '{BLOBSTORE_URL}/{tarball}?{sas_token}' | tar -xz -C {metadata.cwd}",
            seconds=1200,
            attempts=3,
        )

    # TODO(hanson): pydantic pre-commits require a non-master branch
    if task.repo == "pydantic/pydantic" and "git checkout -b fix" not in metadata.setup_script:
        await run_with_retries(terminal_session, "git checkout -b fix", attempts=3)

    setup_script = metadata.setup_script.replace("$COMMIT", task.base_commit)
    try:
        await terminal_session.session.run(BashScript(setup_script, login=True, timeout=900))
    except caas.ExecError as e:
        logger.error(
            "Setup failed",
            output=e.output.decode(errors="ignore"),
            instance_id=task.instance_id,
        )
        raise

    post_setup = [
        f"echo 'cd {metadata.cwd}' >> /root/.bashrc",
        "git config --global user.name User",
        "git config --global user.email user@localhost",
        "pre-commit install || true",
    ]
    # Mark these with SKIP so the agent ignores these by default
    if task.PRECOMMIT_FAIL:
        skip = shlex.quote(",".join(task.PRECOMMIT_FAIL))
        post_setup.append(f"echo export SKIP={skip} >> /root/.bashrc")

    await terminal_session.session.run(BashScript("\n".join(post_setup), login=True, timeout=900))
    await setup_coreutils(terminal_session.session, {}, repo_root=metadata.cwd, setup_python_tools=True)

    if random_drop_packages:
        # add a random removal of some packages dependency and aks model to fix
        default_attempts = 1
        install_seconds = 60
        if random.random() < 0.2:
            n_remove = random.randint(1, 2)
            _, d = await try_run_command(
                terminal_session, f"pip freeze | shuf -n {n_remove} | xargs pip uninstall -y", install_seconds,
                attempts=default_attempts,
            )
        if random.random() < 0.2:
            _, d = await try_run_command(
                terminal_session, f"pip uninstall pytest -y", install_seconds,
                attempts=default_attempts,
            )
