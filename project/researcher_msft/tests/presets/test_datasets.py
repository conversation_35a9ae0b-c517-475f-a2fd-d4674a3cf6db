from researcher_msft.presets.datasets import (
    SYSTEM_IDENTITY,
    TRAINING_DATASET_PRESETS,
    eval_uksouth_7_ProjectsTiny_DummyTools_RougeGrader,
    eval_wus2_19_ProjectsSmall_DummyTools_RougeGrader,
    tr_uksouth_7_ProjectsTiny_DummyTools_RougeGrader,
    tr_uksouth_7_ProjectsTiny_EnterpriseBrowserTools_RougeGrader,
    tr_uksouth_7_VertexEdgeLabsV1_EnterpriseBrowserTools_RougeGrader,
    tr_wus2_19_ProjectsSmall_DummyTools_RougeGrader,
)


def test_train_preset_for_uksouth_7_projects_dataset() -> None:
    assert tr_uksouth_7_ProjectsTiny_DummyTools_RougeGrader.dataset_args == (
        f"model_identity_str={SYSTEM_IDENTITY}",
        "dataset_container=orngcresco",
        "dataset_id=data.researcher.projects.train_tiny_20250702",
        "tool_configs.0=researcher_msft.common.tools.dummy.dummy_search_web_tool:DummySearchWebToolConfig",
        "grader=researcher_msft.graders.rouge_grader:RougeGrader",
        "grader.score_threshold=0.25",
    )


def test_train_preset_for_wus2_19_projects_dataset() -> None:
    assert tr_wus2_19_ProjectsSmall_DummyTools_RougeGrader.dataset_args == (
        f"model_identity_str={SYSTEM_IDENTITY}",
        "dataset_container=orngwus2cresco",
        "dataset_id=data.zhongfu.researcher.train_20250629_small",
        "tool_configs.0=researcher_msft.common.tools.dummy.dummy_search_web_tool:DummySearchWebToolConfig",
        "grader=researcher_msft.graders.rouge_grader:RougeGrader",
        "grader.score_threshold=0.25",
    )


def test_eval_preset_for_uksouth_7_projects_dataset() -> None:
    assert eval_uksouth_7_ProjectsTiny_DummyTools_RougeGrader.dataset_args == (
        f"dataset.model_identity_str={SYSTEM_IDENTITY}",
        "dataset.dataset_container=orngcresco",
        "dataset.dataset_id=data.researcher.projects.train_tiny_20250702",
        "dataset.override_target_samples_per_instance=4",
        "dataset.tool_configs.0=researcher_msft.common.tools.dummy.dummy_search_web_tool:DummySearchWebToolConfig",
        "dataset.grader=researcher_msft.graders.rouge_grader:RougeGrader",
        "dataset.grader.score_threshold=0.25",
    )


def test_eval_preset_for_wus2_19_projects_dataset() -> None:
    assert eval_wus2_19_ProjectsSmall_DummyTools_RougeGrader.dataset_args == (
        f"dataset.model_identity_str={SYSTEM_IDENTITY}",
        "dataset.dataset_container=orngwus2cresco",
        "dataset.dataset_id=data.zhongfu.researcher.train_20250629_small",
        "dataset.override_target_samples_per_instance=4",
        "dataset.tool_configs.0=researcher_msft.common.tools.dummy.dummy_search_web_tool:DummySearchWebToolConfig",
        "dataset.grader=researcher_msft.graders.rouge_grader:RougeGrader",
        "dataset.grader.score_threshold=0.25",
    )


def test_train_preset_for_uksouth_7_projects_dataset_enterprise_browser_tools() -> None:
    assert tr_uksouth_7_ProjectsTiny_EnterpriseBrowserTools_RougeGrader.dataset_args == (
        f"model_identity_str={SYSTEM_IDENTITY}",
        "dataset_container=orngcresco",
        "dataset_id=data.researcher.projects.train_tiny_20250702",
        "tool_configs.0=researcher_msft.common.tools.enterprise.enterprise_browser_tool:EnterpriseBrowserToolConfig",
        "tool_configs.0.sentence_transformer.model_path=az://orngcresco/data/joclausm/models--sentence-transformers--all-MiniLM-L6-v2",
        "grader=researcher_msft.graders.rouge_grader:RougeGrader",
        "grader.score_threshold=0.25",
    )


def test_train_preset_for_uksouth_7_vertex_dataset_enterprise_browser_tools() -> None:
    assert tr_uksouth_7_VertexEdgeLabsV1_EnterpriseBrowserTools_RougeGrader.dataset_args == (
        f"model_identity_str={SYSTEM_IDENTITY}",
        "dataset_container=orngcresco",
        "dataset_id=data.joclausm.researcher.train_vertexedgelabs_v1",
        "tool_configs.0=researcher_msft.common.tools.enterprise.enterprise_browser_tool:EnterpriseBrowserToolConfig",
        "tool_configs.0.sentence_transformer.model_path=az://orngcresco/data/joclausm/models--sentence-transformers--all-MiniLM-L6-v2",
        "tool_configs.0.entity_file_uri=az://orngcresco/data/joclausm/researcher/entities_vertexedgelabs_v1/VertexEdgeLabs_entities.json",
        "grader=researcher_msft.graders.rouge_grader:RougeGrader",
        "grader.score_threshold=0.25",
    )


def test_full_training_presets_list() -> None:
    """
    Checks that the full list of dataset presets is as expected.
    """
    expected_keys = [
        "tr_uksouth_7_ProjectsTiny_DummyTools_RougeGrader",
        "tr_uksouth_7_ProjectsTiny_DummyTools_RougeGrader_WithDataVariants",
        "tr_uksouth_7_ProjectsTiny_DummyTools_DeepGroundLeoGrader",
        "tr_uksouth_7_ProjectsTiny_PrototypeTools_RougeGrader",
        "tr_uksouth_7_ProjectsTiny_PrototypeTools_DeepGroundLeoGrader",
        "tr_uksouth_7_ProjectsTiny_EnterpriseBrowserTools_RougeGrader",
        "tr_uksouth_7_ProjectsTiny_EnterpriseBrowserTools_DeepGroundLeoGrader",
        "tr_uksouth_7_ProjectsSmall_DummyTools_RougeGrader",
        "tr_uksouth_7_ProjectsSmall_DummyTools_DeepGroundLeoGrader",
        "tr_uksouth_7_ProjectsSmall_PrototypeTools_RougeGrader",
        "tr_uksouth_7_ProjectsSmall_PrototypeTools_DeepGroundLeoGrader",
        "tr_uksouth_7_ProjectsSmall_EnterpriseBrowserTools_RougeGrader",
        "tr_uksouth_7_ProjectsSmall_EnterpriseBrowserTools_DeepGroundLeoGrader",
        "tr_wus2_19_ProjectsTiny_DummyTools_RougeGrader",
        "tr_wus2_19_ProjectsTiny_DummyTools_DeepGroundLeoGrader",
        "tr_wus2_19_ProjectsTiny_PrototypeTools_RougeGrader",
        "tr_wus2_19_ProjectsTiny_PrototypeTools_DeepGroundLeoGrader",
        "tr_wus2_19_ProjectsTiny_EnterpriseBrowserTools_RougeGrader",
        "tr_wus2_19_ProjectsTiny_EnterpriseBrowserTools_DeepGroundLeoGrader",
        "tr_wus2_19_ProjectsSmall_DummyTools_RougeGrader",
        "tr_wus2_19_ProjectsSmall_DummyTools_DeepGroundLeoGrader",
        "tr_wus2_19_ProjectsSmall_PrototypeTools_RougeGrader",
        "tr_wus2_19_ProjectsSmall_PrototypeTools_DeepGroundLeoGrader",
        "tr_wus2_19_ProjectsSmall_EnterpriseBrowserTools_RougeGrader",
        "tr_wus2_19_ProjectsSmall_EnterpriseBrowserTools_DeepGroundLeoGrader",
    ]
    for key in expected_keys:
        assert key in TRAINING_DATASET_PRESETS.keys()
