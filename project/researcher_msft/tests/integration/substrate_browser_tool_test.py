from typing import Any, Dict, Optional, cast
import json
import os
import tempfile
import chz
import pytest
from boostedblob import cli as bbb
from chat import chat
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.common.tools.browser.browser_tool import Berry<PERSON><PERSON>erTool
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from researcher_msft.common.tools.enterprise.enterprise_browser_tool import (
    EnterpriseBrowserTool,
    EnterpriseBrowserToolConfig,
)
from researcher_msft.common.tools.enterprise.substrate.substrate_backend import SubstrateBackend

def _make_datapoint() -> HarmonyCompletionDatapoint:
    """Makes a dummy datapoint for tool initialization."""
    ds_config = chz.Blueprint(HarmonyCompletionDatasetConfig).make_from_argv(
        [
            "dataset_id=dummy",
            "...grader=qstar.graders.mathgen_grader:MathgenGrader",
        ]
    )
    datapoint = HarmonyCompletionDatapoint(
        dataset_config=ds_config,
        problem="foo?",
    )
    return datapoint


def _make_browser_tool(args: Optional[list[str]]=None) -> BerryBrowserTool:
    
    args=['use_substrate_backend=true', 'substrate_test_tenant_credentials_file=/root/.test_tenant.env'] if not args else args
    conf = chz.Blueprint(EnterpriseBrowserToolConfig).make_from_argv(list(args))
    tool = conf.initialize_tool(datapoint=_make_datapoint())
    tool = cast(BerryBrowserTool, tool)
    return tool

async def call_tool_helper(
    tool: BerryBrowserTool,
    function_name: str,
    **functions_kwargs,
) -> str:
    tool_name = EnterpriseBrowserTool.get_tool_name()
    message = chat.Message.assistant(recipient=f"{tool_name}.{function_name}", content=json.dumps(functions_kwargs))
    results = []
    async for response in tool.process(message):
        results.append(response)
    assert len(results) == 1, f"Expected one result for the {function_name} command"
    assert results[0].recipient == "all", "Expected the recipient to be 'all'"
    assert isinstance(
        results[0].content, chat.TetherBrowsingDisplay
    ), "Expected content to be a TetherBrowsingDisplay"
    assert results[0].content.result is not None, "Expected content result to be not None"
    return results[0].content.result

@pytest.mark.asyncio
async def test_enterprise_backend_init() -> None:
    tool = _make_browser_tool()
    assert isinstance(tool.backend, SubstrateBackend)
    token = await tool.backend.credential.get_token("https://substrate.office.com/search/.default")
    assert token is not None, "Expected a valid token to be returned"

def test_enterprise_browser_tool_names() -> None:
    assert EnterpriseBrowserTool.get_tool_name() == "enterprise", "Expected the tool name to be 'enterprise'"
    assert "search_files" in EnterpriseBrowserTool.get_names_of_functions_the_model_can_call(), "Expected 'search_file' to be in the function names"
    assert "search" not in EnterpriseBrowserTool.get_names_of_functions_the_model_can_call(), "Expected 'search' to not be in the function names"
    assert "open" in EnterpriseBrowserTool.get_names_of_functions_the_model_can_call(), "Expected 'open' to be in the function names"

@pytest.mark.asyncio
async def test_enterprise_backend_search() -> None:
    tool = _make_browser_tool()
    result = await call_tool_helper(tool, "search", query="Secure Boot workshop", topn=10)
    assert "L0:" in result, "Expected the result to contain line numbers"
    assert "Secure Boot" in result, "Expected the result to contain 'Secure Boot'"

    result = await call_tool_helper(tool, "open", cursor=-1, id=0)
    lines = result.split("\n")
    assert len(lines) > 0, "Expected the body to have content"

@pytest.mark.asyncio
async def test_enterprise_backend_search_files() -> None:
    tool = _make_browser_tool()
    result = await call_tool_helper(tool, "search_files", query="Secure Boot workshop", topn=10)
    assert "Secure Boot" in result, "Expected the result to contain 'Secure Boot'"
    assert "Secure_Boot_Workshop_Details" in result, "Expected the result to contain 'Secure_Boot_Workshop_Details'"
