import logging
import math
from dataclasses import dataclass, field
from typing import Any, List
from unittest import mock

import chz
from berry.grades import StandardGrade, StandardGradeType
from berry.sample import Sample
from chat import chat
from chat.render.v4.experimental.strawberry.formatter import Berry<PERSON>hannel
from qstar.common import datapoint, types
from qstar.common.tools import renderer_worker
from researcher_msft.graders.tool_success_grader import ToolSuccessGrader


def create_mock_sample_with_messages(messages: List[chat.Message]) -> types.SampleWithGrade:
    renderer_name = "harmony_v4.0.15_berry_v3_1mil_orion"
    dp = mock.Mock()
    dp.dataset_config.harmony_renderer_name = renderer_name
    dp.dataset_config.header_buffer_tokens = 0
    conversation = chat.Conversation(
        messages=messages,
    )
    tokens = renderer_worker.encode_messages(conversation.messages, renderer_name)
    return Sample(
        gt_datapoint=dp,
        manual_seed=(0,),
        metadata={},
        _metrics={},
        delegation_parent_sample_id=None,
        delegation_depth=0,
        num_yields=999,
        model_name="policy",
    ).with_completion(
        text="Completed answer for testing.",
        max_length=5000,
        conversation=conversation,
        tokens=tokens,
        logprobs=[float("nan")] * len(tokens),
    )


def test_tool_success_grader_all_worked() -> None:
    output = ToolSuccessGrader().grade_batch(
        samples=[
            create_mock_sample_with_messages(
                messages=[
                    chat.Message.assistant(
                        recipient="enterprise.open",
                        content=chat.Text.from_string(
                            """<|im_start|>assistant<|meta_sep|>analysis<|im_sep|>I think [13] shows what we have: "Operating Statistics (Estimated)".
Let's scroll [9] or [12].<|im_end|><|im_start|>assistant to=enterprise.open<|meta_sep|>analysis code<|im_sep|>{"cursor": 9, "loc": 447}<|ghissue|>
"""
                        ),
                        channel=BerryChannel.CHAIN_OF_THOUGHT,
                    ),
                    chat.Message.tool(
                        author_name="enterprise.open",
                        content=chat.TetherQuote(
                            tether_id=0,
                            domain="File",
                            url="",
                            text="""[14] File file_task_2836_source_2 (file)
**viewing lines [447 - 502] of 669**
L447: OPERATING STATISTICS (Estimated)
L448: In the second quarter of 2025, velocity decreased by 4% and dwell increased by 
L449: 2% versus prior year. Carload trip
""",
                            title="file_task_2836_source_2",
                        ),
                    ),
                    chat.Message.assistant(
                        recipient="enterprise.search_files",
                        content=chat.Text.from_string(
                            """
Let's search files:<|im_end|><|im_start|>assistant to=enterprise.search_files<|meta_sep|>analysis code<|im_sep|>{"query": "EdgeDriver Performance Analysis Optimization", "topn": 5}<|ghissue|>
"""
                        ),
                        channel=BerryChannel.CHAIN_OF_THOUGHT,
                    ),
                    chat.Message.tool(
                        author_name="enterprise.search_files",
                        content=chat.TetherQuote(
                            tether_id=1,
                            domain="File",
                            url="",
                            text="""<|im_start|>enterprise.search<|meta_sep|>analysis tether_browsing_display<|im_sep|>[4] Search results for query `EdgeDriver Performance Analysis Optimization`
**viewing lines [0 - 2] of 2**

L0: # 【0†EdgeAI_v1.3.2_Autoscale_Analysis†file】
L1: Workbook: EdgeAI v1....
L2:
""",
                            title="file_task_2836_source_2",
                        ),
                    ),
                    chat.Message.assistant(
                        f"# Answer\n\nFinal dummy answer to trigger grader.",
                        channel=BerryChannel.FINAL_ANSWER,
                    ),
                ]
            )
        ]
    )
    assert output[0].grades[0] == StandardGrade(
        grade_key="tool_success_reward",
        is_correct_for_analysis=True,
        grade_type=StandardGradeType.MULTIPLICATIVE_LOG,
        value=math.log(1.0),
    )


def test_tool_success_grader_half_worked() -> None:
    output = ToolSuccessGrader().grade_batch(
        samples=[
            create_mock_sample_with_messages(
                messages=[
                    chat.Message.assistant(
                        recipient="enterprise.open",
                        content=chat.Text.from_string(
                            """<|im_start|>assistant<|meta_sep|>analysis<|im_sep|>I think [13] shows what we have: "Operating Statistics (Estimated)".
Let's scroll [9] or [12].<|im_end|><|im_start|>assistant to=enterprise.open<|meta_sep|>analysis code<|im_sep|>{"cursor": 9, "loc": 447}<|ghissue|>
"""
                        ),
                        channel=BerryChannel.CHAIN_OF_THOUGHT,
                    ),
                    chat.Message.tool(
                        author_name="enterprise.open",
                        content=chat.TetherQuote(
                            tether_id=0,
                            domain="File",
                            url="",
                            text="""[14] File file_task_2836_source_2 (file)
**viewing lines [447 - 502] of 669**
L447: OPERATING STATISTICS (Estimated)
L448: In the second quarter of 2025, velocity decreased by 4% and dwell increased by 
L449: 2% versus prior year. Carload trip
""",
                            title="file_task_2836_source_2",
                        ),
                    ),
                    chat.Message.assistant(
                        recipient="enterprise.search_files",
                        content=chat.Text.from_string(
                            """
<|im_start|>Let's search in our enterprise data for "Q3 2024 10-Q".<|im_end|>
<|im_start|>assistant to=enterprise.search_files<|meta_sep|>analysis code<|im_sep|>{"query": "\"Q3 2024 10-Q\" ", "topn": 5, "recency_days": null}<|ghissue|>
"""
                        ),
                        channel=BerryChannel.CHAIN_OF_THOUGHT,
                    ),
                    chat.Message.tool(
                        author_name="enterprise.search_files",
                        content="Error parsing function call: Invalid function_name='search_files' call: "
                        "kwargs={'query': 'Q3 2024 10-Q', 'topn': 5, 'recency_days': None}. Expected: // Searches Sharepoint",
                    ),
                    chat.Message.assistant(
                        f"# Answer\n\nFinal dummy answer to trigger grader.",
                        channel=BerryChannel.FINAL_ANSWER,
                    ),
                ],
            )
        ]
    )
    assert output[0].grades[0] == StandardGrade(
        grade_key="tool_success_reward",
        is_correct_for_analysis=True,
        grade_type=StandardGradeType.MULTIPLICATIVE_LOG,
        value=math.log(0.5),
    )


def test_tool_success_grader_none_worked() -> None:
    output = ToolSuccessGrader().grade_batch(
        samples=[
            create_mock_sample_with_messages(
                messages=[
                    chat.Message.assistant(
                        """<|im_start|>assistant<|meta_sep|>analysis<|im_sep|>
So far "EdgeDriver_Performance_Analysis_and_Optimization_Paper.pdf" not obvious. Could try transcripts search for meeting: "EdgeDriver performance analysis optimization".<|im_end|>
<|im_start|>assistant<|meta_sep|>analysis to=enterprise.search_transcripts code<|im_sep|>{"query": "EdgeDriver performance optimization", "cursor": 8}<|ghissue|>
""",
                        channel=BerryChannel.CHAIN_OF_THOUGHT,
                    ),
                    chat.Message.tool(
                        """<|im_start|>enterprise<|meta_sep|>analysis<|im_sep|>Error parsing function call: Invalid function_name='search_transcripts' call: kwargs={'query': 'EdgeDriver performance optimization', 'cursor': 8}. 
Expected: // Search within the user's meeting transcripts and returns matching transcript snippets.
// Args:
""",
                        author_name="enterprise.search_transcripts",
                    ),
                    chat.Message.assistant(
                        """
Let's search in our enterprise data for "Starbucks Q3 2024 10-Q".<|im_end|><|im_start|>assistant to=enterprise.search_files<|meta_sep|>analysis code<|im_sep|>{"query": "\"Q3 2024 10-Q\" Starbucks", "topn": 5, "recency_days": null}<|ghissue|>
""",
                        channel=BerryChannel.CHAIN_OF_THOUGHT,
                    ),
                    chat.Message.tool(
                        """
<|im_start|>enterprise<|meta_sep|>analysis<|im_sep|>Error parsing function call: Invalid function_name='search_files' call: kwargs={'query': '"Q3 2024 10-Q" Starbucks', 'topn': 5, 'recency_days': None}. Expected: // Searches Sharepoint, Onedrive, and various Microsoft Graph Connectors and returns matching files, documents, wiki pages, technical and learning resources, operational and organizational support, websites, links, and etc. that the user has access to.
""",
                        author_name="enterprise.search_files",
                    ),
                    chat.Message.assistant(
                        f"# Answer\n\nFinal dummy answer to trigger grader.",
                        channel=BerryChannel.FINAL_ANSWER,
                    ),
                ],
            )
        ]
    )
    assert output[0].grades[0] == StandardGrade(
        grade_key="tool_success_reward",
        is_correct_for_analysis=True,
        grade_type=StandardGradeType.MULTIPLICATIVE_LOG,
        value=float("-inf"),
    )
