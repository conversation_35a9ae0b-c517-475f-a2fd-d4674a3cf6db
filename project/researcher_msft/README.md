# 🫐 `researcher_msft`

> "We can only see a short distance ahead, but we can see plenty there that needs to be done."

---

This guide outlines how to schedule clusters, run training, evaluation and common tips to post-train models for <PERSON><PERSON>.

# Setup Glass

1. (skip if already done) Follow the [WSL Setup](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9025/WSL-Setup) steps to set up your machine.  
2. (skip if already done) Follow the [Bootstrap Environment](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9026/2.-Bootstrap-Environment) steps to set up your environment and verify your setup by running a [sample run](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/9028/3.-Run-sample-training-flows).
3. To sync the `glass` repository with your devbox, follow the steps in [README.md](https://dev.azure.com/project-argos/Mimco/_git/glass?anchor=setup).


**Note**: whenever you onboard to a new cluster, you will need to re-run the steps 2 and 3.

# Running via Autostart
The easiest way to submit jobs is with the [autostart scripts](./researcher_msft/autostart/). They will submit a request to create a cluster, run a command on that cluster, and then delete the cluster. The job will stay queued until nodes are available on the target cluster. This allows you to "fire and forget" a training job and do other things while you wait for it to be scheduled.

## Pre-requisites

1. Make sure `pychz` is installed on your laptop:
```
oaipkg install pychz
```

2. Make sure the environment variable `OAIPKG_POLYREPO_SIBLINGS=glass` is set:
```
echo "export OAIPKG_POLYREPO_SIBLINGS=glass" >> ~/.zprofile
source ~/.zprofile
```

## Running Training vs Autostart
```bash
pychz researcher_msft/autostart/researcher_train.chz.py -e launch_cluster
```

The `launch_cluster` method has defaults for the most common arguments for creating a cluster and running training. The default arguments will produce a training run which should be reasonably fast on our primary dataset with the default tools, but with a fast reward function. It will not produce a useful checkpoint, but it can be useful as a sanity check that a full training run works. 

You customize the arguments to `launch_cluster` by passing them on the command line like so

```bash
pychz researcher_msft/autostart/researcher_train.chz.py -e launch_cluster cluster=prod-southcentralus-hpe-4 grader=DeepGroundLeoGrader
```

Similarly, you can change the cluster by providing the `cluster=...` argument, although generally this should only be done for low priority jobs unless you have explicit approval.

Additional arguments can be passed to run_experiment with the `experiment_overrides` argument.
For example, you can sanity check a setup by limiting the number of steps.

```bash
pychz researcher_msft/autostart/researcher_train.chz.py -e launch_cluster experiment_overrides="max_steps=1"
```

## Run Priority

In general, runs can always be safely submitted using the default priority (low).
If our team is currently under their quote of allocated nodes (650 at time of writing), then you can include `priority=team-high` to submit a higher priority job.
Only submit `team-critical` jobs with explicit approval, usually from the team (usually Richard Speyer signs off).

As will all orange jobs, you also can change the priority after a run, usually to set it to high if we are under our allocation and your job isn't getting scheduled. e.g.

```
b change-priority --priority-class team-high 'drmini-20250825132711-*'
```

## Monitoring Autostart Jobs

While the run automatically starts, it is really just automating the [manual steps below](#running-manually). This means any of the advice or techniques for debugging jobs still applies, with the small quirks that when the main command (e.g. run_experiment) finishes the nodes will automatically be deleted. See further down in this readme for good tips on debugging and monitoring runs.

Logs and metrics should still go into lemon/wandb/snowflake if you have those setup. Check the wiki and Teams for the latest details about seeing logs in Kusto. 

You can aso attach the the tmux session the command is running in using `b` with a command like so

```bash
b tmux -s 0 'drmini-*-train-w0'
```

# Running Manually

## Provision DevBox
Based on the available cluster and the initial model you select, execute one of the commands below in your WSL session to provision the required devboxes:

<table>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (westus-19)</div>
      <pre style="padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, monospace; font-size:13px; line-height:1.4;">
python -m qstar.peashooter.run_peashooter \
...rapid_id=drmini18 \
rapid.cluster.name=prod-westus2-19 \
rapid.cluster.priority=team-high \
rapid.pull_git_on_restart=False \
num_controller_nodes=1 \
n_gpus_per_sampler=8 \
sampling_jobs=8 \
n_train_gpus=64 \
cpu_controller=False \
security_profile=msft-orng
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (uksouth-7)</div>
      <pre style="padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, monospace; font-size:13px; line-height:1.4;">
python -m qstar.peashooter.run_peashooter \
...rapid_id=drmini18 \
rapid.cluster.name=prod-uksouth-7 \
rapid.cluster.priority=team-high \
rapid.pull_git_on_restart=False \
num_controller_nodes=1 \
n_gpus_per_sampler=8 \
sampling_jobs=8 \
n_train_gpus=64 \
cpu_controller=False \
security_profile=msft-orng
      </pre>
    </td>
  </tr>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr (westus2-19)</div>
      <pre style="padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, monospace; font-size:13px; line-height:1.4;">
python -m qstar.peashooter.run_peashooter \
...rapid_id=dr1 \
rapid.cluster.name=prod-westus2-19 \
rapid.cluster.priority=team-high \
rapid.pull_git_on_restart=False \
num_controller_nodes=1 \
n_gpus_per_sampler=16 \
sampling_jobs=8 \
n_train_gpus=144 \
cpu_controller=False \
security_profile=msft-orng
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr (uksouth-7)</div>
      <pre style="padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, monospace; font-size:13px; line-height:1.4;">
python -m qstar.peashooter.run_peashooter \
...rapid_id=dr1 \
rapid.cluster.name=prod-uksouth-7 \
rapid.cluster.priority=team-high \
rapid.pull_git_on_restart=False \
num_controller_nodes=1 \
n_gpus_per_sampler=16 \
sampling_jobs=8 \
n_train_gpus=144 \
cpu_controller=False \
security_profile=msft-orng
      </pre>
    </td>
  </tr>
</table>


## 🚀 Start Training 🎯

## b ls
Check the status of the active devboxes: `b ls`.  
If you started devboxes in the [Create Cluster](#create-cluster) step, you should see an output similar to the following when your devbox setup is ready:
```bash
NAME                       CLUSTER          SIZE     WORKERS   AGE   STATUS      PRIORITY   TEAM                 RESTARTS
devbox-controller-w0       prod-westus2-19   1        1/1/1     1d    Scheduled   high       team-moonfire-m365
devbox-rollout-worker-w0   prod-westus2-19   1        1/1/1     1d    Scheduled   high       team-moonfire-m365   0
devbox-train-w0            prod-westus2-19   1 (+7)   8/8/8     1d    Scheduled   high       team-moonfire-m365   0
```

### Troubleshooting
Sometimes your devbox won't be scheduled immediately, or has errors. Use the following commands to troubleshoot and make sure kubectl has the right context set to your cluster (`kubectl config use-context prod-westus2-19`)
* Checking quota: `orange_get_quota -c prod-westus2-19`
* Check the status of your devbox in the queue: `orange_get_pendingjob`  
* Check the logs of your devbox: `b tail devbox-train-w0 --clusters=prod-westus2-19`

## (optional) Sync your local code with devbox
To sync committed local changes to the devbox, use the following command:
```bash
b sync devbox-train-w0
```

## (optional) Automatically install researcher_msft on nodes 

If you want to automatically install the researcher_msft on nodes you launch, which is usually a good idea, update your `~/.rcall_config.py` to run oaipkg install on startup like so:

```python
EXTRA_SETUP = """
    oaipkg install researcher_msft

```

Keep in mind this file is clobbered every time you rerun setup_orange_laptop.sh, so to ease the upgrade burden you may want to keep a backup copy of it `cp ~/.rcall_config.py ~/.rcall_config.py.bak` so its easier to move over past changes if you need to setup again.


## SSH to training job
To start training you need to ssh into your training devbox:
```bash
b ssh devbox-train-w0
```
## Run training
Once you've connected to your training devbox via SSH, you can initiate training. Below are commands using **dummy tools** and a **tiny corpus** to quickly validate your setup.

<table>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (westus-19)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-mini-check-setup
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
:researcher_msft.presets.datasets:tr_wus2_19_VertexEdgeLabsV1_DummyTools_RougeGrader
:researcher_msft.presets.models:wus2_19_dr_mini
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (uksouth-7)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-mini-check-setup
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
:researcher_msft.presets.datasets:tr_uksouth_7_VertexEdgeLabsV1_DummyTools_RougeGrader
:researcher_msft.presets.models:uksouth_7_dr_mini
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (scus-hpe-4)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-mini-check-setup
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
:researcher_msft.presets.datasets:tr_scus_hpe_4_VertexEdgeLabsV1_DummyTools_RougeGrader
:researcher_msft.presets.models:scus_hpe_4_dr_mini
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
  </tr>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr (westus-19)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-full-check-setup
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion:d64_80g
:researcher_msft.presets.datasets:tr_wus2_19_VertexEdgeLabsV1_DummyTools_RougeGrader
:researcher_msft.presets.models:wus2_19_dr_full
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr (uksouth-7)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-full-check-setup
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion:d64_80g
:researcher_msft.presets.datasets:tr_uksouth_7_VertexEdgeLabsV1_DummyTools_RougeGrader
:researcher_msft.presets.models:uksouth_7_dr_full
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr (scus-hpe-4)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-full-check-setup
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion:d64_80g
:researcher_msft.presets.datasets:tr_scus_hpe_4_VertexEdgeLabsV1_DummyTools_RougeGrader
:researcher_msft.presets.models:scus_hpe_4_dr_full
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
  </tr>
</table>

### Parameters
You can also experiment with selection of different `researcher_msft.presets.datasets` preset values from those listed in `presets/datasets.py`. Or use different `harmony_renderer_name` values, such as `harmony_v4.0.15_berry_v3_1mil_orion_mm_no_budget_no_mmgen` or `harmony_v4.0.16_16k_orion_mmgen_no_asr_2k_action`.

## b delete <devbox-name>
When you are done with your experiment, make sure to release the resources:
`b delete <name-of-your-devbox>`

## Monitor Training
**[Lemon Dashboard Guide](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/6866/4.3-Lemon-Dashboard)**  
**[Lemon Guide](https://microsoft.sharepoint.com/teams/argos_mimco/Shared%20Documents/Forms/AllItems.aspx?id=%2Fteams%2Fargos%5Fmimco%2FShared%20Documents%2FTransferred%20Docs%2Fberry%2Flemon%2FLemonGuide%2Epdf&parent=%2Fteams%2Fargos%5Fmimco%2FShared%20Documents%2FTransferred%20Docs%2Fberry%2Flemon)**

You can monitor your job's progress using the unified `Lemon` portal: **[Lemon Portal](https://aka.ms/lemon/)**

## Monitor Logs
**[Debugging advice, relevant logs](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/3859/Debugging-advice-relevant-logs)**

### Babysit Training Steps
If you've just started a training run, you can monitor the progress via the command below, which largely follows the logs in [steps.py](https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=%2Fproject%2Fpeashooter%2Fpeashooter%2Ftrain_worker%2Fstep.py&_a=contents&version=GBorange%2Fmain):
```bash
b ssh devbox-train-w0
supervisorctl tail -f train_worker_policy:0
```

### Navigate the log files
During training or post-training, the most useful logs could mainly be found in `training` and the `rollout-worker` devbox, since they both are involved in the model training ([overview of components](https://microsoft.sharepoint.com/teams/argos_mimco/Shared%20Documents/Forms/AllItems.aspx?viewid=7c58075d%2Dc8f0%2D4332%2D912b%2D402132552369&csf=1&web=1&e=8G2Nv7&CID=6b030a26%2D2054%2D4951%2Db74d%2Ddeaad74e4b9e&FolderCTID=0x0120009A95E4BA13E8E9458507E79AC7613BC5&id=%2Fteams%2Fargos%5Fmimco%2FShared%20Documents%2FTransferred%20Docs%2Fberry%2Fqstar%5Fintro%2FStrawberry%20components%20overview%2Epdf&parent=%2Fteams%2Fargos%5Fmimco%2FShared%20Documents%2FTransferred%20Docs%2Fberry%2Fqstar%5Fintro)).

Use the following commands to see logs around GPU usage and training progress:
```bash
b ssh devbox-train-w0 # (or your devbox-rollout-worker-w0)
vim /var/log/torchflow.log
```

### Stream and Search Logs
While connected via SSH, use the following command to stream logs in real-time:
```bash
b ssh devbox-rollout-worker-w0 # (or your devbox-train-w0)
kubectl logs devbox-rollout-worker-w0-0 
```
You can also search for specific error via:
```bash
kubectl logs devbox-rollout-worker-w0-0 --all-containers=true | grep your_beautiful_error_or_any_text
```
### Detailed Logs
Alternatively, use 
```bash
b tail --n 10000 devbox-rollout-worker-w0
``` 
for detailed logs, but you might have to skip a lot of repetitive GPU shard download messages.

# 🚀 Training with best setup 🏆
Follow the steps here to run training with the latest tool and grader setup.  

## (if not done already) Tools and Bus Dependencies Setup
1. Optionally, setup [Enterprise Browser Tools](#enterprise-browser-tools) as needed.
2. Ensure the "researcher" bus devbox is running - [Monitor BUS nodes](#monitor-bus-nodes). If it's not running, or you need to add more nodes, refer to the [Start BUS Nodes](#start-bus-nodes) section.

## Run training
Ssh into your training devbox and run the following command with the latest verified tool and grader setup:

<table>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (westus-19)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-mini-champion-run-1
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
:researcher_msft.presets.datasets:tr_wus2_19_VertexEdgeLabsV1_EnterpriseBrowserTools_GPUMultiStageGrader
:researcher_msft.presets.models:wus2_19_dr_mini
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (uksouth-7)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-mini-champion-run-1
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
:researcher_msft.presets.datasets:tr_uksouth_7_VertexEdgeLabsV1_EnterpriseBrowserTools_GPUMultiStageGrader
:researcher_msft.presets.models:uksouth_7_dr_mini
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (scus-hpe-4)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-mini-champion-run-1
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
:researcher_msft.presets.datasets:tr_scus_hpe_4_VertexEdgeLabsV1_EnterpriseBrowserTools_GPUMultiStageGrader
:researcher_msft.presets.models:scus_hpe_4_dr_mini
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
  </tr>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr (westus-19)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-full-champion-run-1
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion:d64_80g
:researcher_msft.presets.datasets:tr_wus2_19_VertexEdgeLabsV1_EnterpriseBrowserTools_GPUMultiStageGrader
:researcher_msft.presets.models:wus2_19_dr_full
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr (uksouth-7)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-full-champion-run-1
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion:d64_80g
:researcher_msft.presets.datasets:tr_uksouth_7_VertexEdgeLabsV1_EnterpriseBrowserTools_GPUMultiStageGrader
:researcher_msft.presets.models:uksouth_7_dr_full
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr (scus-hpe-4)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=( oaipkg run --beam qstar.run_experiment 
nostrict
name=researcher-dr-mini-champion-run-1
wandb.wandb_project="Researcher FT"
seed=20250306
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
:researcher_msft.presets.datasets:tr_scus_hpe_4_VertexEdgeLabsV1_EnterpriseBrowserTools_GPUMultiStageGrader
:researcher_msft.presets.models:scus_hpe_4_dr_mini
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
  </tr>
</table>


# 📊 Evaluating the trained checkpoints 🔍
You can evaluate the trained checkpoints by selecting a test dataset, configuring the tools, and choosing an appropriate grader for the evaluation run.

## Create eval cluster
We modify our devbox provisioning command to exclude training nodes and enforce the `eval_only` setting.
<table>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (westus-19)</div>
      <pre style="overflow-x:auto; padding:12px 12px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
python -m qstar.peashooter.run_peashooter \
...rapid_id=researcher-dr-mini-eval \
rapid.cluster.name=prod-westus2-19 \
rapid.cluster.priority=team-high \
rapid.pull_git_on_restart=False \
cpu_controller=False \
n_train_gpus=0 \
n_gpus_per_sampler=8 \
sampling_jobs=8 \
use_beam=True \
eval_only=True \
security_profile=msft-orng \
strict=True
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8x; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (uksouth-7)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
python -m qstar.peashooter.run_peashooter \
...rapid_id=researcher-dr-mini-eval \
rapid.cluster.name=prod-uksouth-7 \
rapid.cluster.priority=team-high \
rapid.pull_git_on_restart=False \
cpu_controller=False \
n_train_gpus=0 \
n_gpus_per_sampler=8 \
sampling_jobs=8 \
use_beam=True \
eval_only=True \
security_profile=msft-orng \
strict=True
      </pre>
    </td>
  </tr>
</table>

## Start eval
Find the folder of your checkpoint in `az://orngwus2cresco/twapi/mini/e/` folder.
For example, `az://orngwus2cresco/twapi/mini/e/beautifulperson-researcher-dr-mini-champion-run-1/policy/`.  
Now you should be able to run the evaluation command for the checkpoints in that folder:

<table>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (westus-19)</div>
      <pre style="overflow-x:auto; padding:12px 12px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=(
    beam python --use-cwd -m qstar.run_eval 
    nostrict 
    name=evaluate-researcher-dr-mini-champion-run-peaval
    auto_inherit_training_args=False 
    eval_settings.checkpoint_dir=az://orngwus2cresco/twapi/mini/e/beautifulperson-researcher-dr-mini-champion-run-1/policy/
    policy.initial_checkpoint=az://orngwus2cresco/models/snapshots/deep-research-mini-vis-2025-03-18.transfer-2.tc-decrypted/
    load.restore_from_all_clusters=False 
    :berry_models.scallion_lpe:d36_80g_mbg16_bf16 
    root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False' 
    policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22' 
    policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True' 
    policy.is_multimodal=True 
    seed=20250306
    ...dataset=HarmonyCompletionDatasetConfig
    ...encoding_name=orion_200k 
    ...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe 
    peashooter.timeout_seconds.stalled_datapoint=3600 
    peashooter.timeout_seconds.lost_datapoint=1800 
    peashooter.use_stale_kv_cache=False 
    load.restore_from_all_clusters=False 
    max_workers_per_step=1 
    policy.n_ctx=262144 
    defaults.n_ctx=262144 
    ...max_num_yields=256 
    defaults.sampler.max_num_yields=256 
    policy.encoding_name=orion_200k 
    defaults.sampler.harmony_constrained_sampling=True 
    eval_settings.eval_initial_policy=True
    eval_settings.eval_every=2
    :researcher_msft.presets.datasets:eval_wus2_19_ProjectsTiny_PrototypeTools_DeepGroundLeoGrader
    security_profile=msft-orng 
    github_upload=False 
    wandb_enable=True 
    wandb.wandb_project="Researcher FT"
    kafka_enable=False 
    enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8x; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>dr-mini (uksouth-7)</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
CMD=(
    beam python --use-cwd -m qstar.run_eval 
    nostrict 
    name=evaluate-researcher-dr-mini-champion-run-peaval
    auto_inherit_training_args=False 
    eval_settings.checkpoint_dir=az://orngcresco/twapi/mini/e/beautifulperson-researcher-dr-mini-champion-run-1/policy/
    policy.initial_checkpoint=az://orngcresco/models/snapshots/deep-research-mini-vis-2025-03-18.transfer-2.tc-decrypted/
    load.restore_from_all_clusters=False 
    :berry_models.scallion_lpe:d36_80g_mbg16_bf16 
    root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False' 
    policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22' 
    policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True' 
    policy.is_multimodal=True 
    seed=20250306
    ...dataset=HarmonyCompletionDatasetConfig
    ...encoding_name=orion_200k 
    ...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe 
    peashooter.timeout_seconds.stalled_datapoint=3600 
    peashooter.timeout_seconds.lost_datapoint=1800 
    peashooter.use_stale_kv_cache=False 
    load.restore_from_all_clusters=False 
    max_workers_per_step=1 
    policy.n_ctx=262144 
    defaults.n_ctx=262144 
    ...max_num_yields=256 
    defaults.sampler.max_num_yields=256 
    policy.encoding_name=orion_200k 
    defaults.sampler.harmony_constrained_sampling=True 
    eval_settings.eval_initial_policy=True
    eval_settings.eval_every=5
    :researcher_msft.presets.datasets:eval_uksouth_7_VertexEdgeLabsV1_EnterpriseBrowserTools_GPUMultiStageGrader
    security_profile=msft-orng 
    github_upload=False 
    wandb_enable=True 
    wandb.wandb_project="Researcher FT"
    kafka_enable=False 
    enable_slackbot=False
)
beam start
"${CMD[@]}"
      </pre>
    </td>
  </tr>
</table>

# (Optional) Wandb 
Wandb is useful for tracking key metrics during training and evaluation, such as mean reward, pass rate, tool usage, solution length, and inference time.

**[Setup Wandb for logging training metrics](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/4946/4.1-Setup-Wandb-for-logging-training-metrics)**

Make sure to add `wandb_enable=True` and `wandb.wandb_project="Researcher FT"` arguments to your training commands for metrics to propagate to wandb version of our project.

# (Optional) Snowflake 
Snowflake is useful for everything else that Wandb does not have, since it enables us to download and analyze rollouts ourselves.

**[Setup Snowflake for Analyzing Training or Evaluation Logs](https://dev.azure.com/project-argos/Penny/_wiki/wikis/Penny.wiki/6826/4.2-Setup-Snowflake-for-Analyzing-Training-Logs)**

Follow the steps in [Snowflake Download Samples](https://microsoft.sharepoint-df.com/:fl:/r/contentstorage/CSP_3c0b5b21-10e3-4ea9-b75c-0a29bedb9068/Document%20Library/LoopAppData/Snowflake%20download%20samples.loop?d=wa36cd27879cc4c369ee70f58dd38df07&csf=1&web=1&e=Iwqsjz&nav=cz0lMkZjb250ZW50c3RvcmFnZSUyRkNTUF8zYzBiNWIyMS0xMGUzLTRlYTktYjc1Yy0wYTI5YmVkYjkwNjgmZD1iJTIxSVZzTFBPTVFxVTYzWEFvcHZ0dVFhQ3gtRGE5b3RsZE5zT2ZpeDRVYUdqcXhrTDhCSHlEZVJvb2RmczBUbzZqWCZmPTAxQVRVR0M0M1kySldLSFREWkdaR0o1WllQTERPVFJYWUgmYz0lMkYmYT1Mb29wQXBwJnA9JTQwZmx1aWR4JTJGbG9vcC1wYWdlLWNvbnRhaW5lciZ4PSU3QiUyMnclMjIlM0ElMjJUMFJUVUh4dGFXTnliM052Wm5RdWMyaGhjbVZ3YjJsdWRDMWtaaTVqYjIxOFlpRkpWbk5NVUU5TlVYRlZOak5ZUVc5d2RuUjFVV0ZEZUMxRVlUbHZkR3hrVG5OUFptbDRORlZoUjJweGVHdE1PRUpJZVVSbFVtOXZaR1p6TUZSdk5tcFlmREF4UVZSVlIwTTBORTFJV2tNeU5ETXlWRk5XUVRNMlR6VkhOMU5STkRkQ1JUTSUzRCUyMiUyQyUyMmklMjIlM0ElMjI5MzgxYTU3MC0yZjBjLTQ3ZGQtYWE0OS00MWMzM2E0Mjg2NmUlMjIlN0Q%3D) to download and analyze samples from your evaluation or training run.

# Start BUS Nodes
BUS nodes are needed for the setup where your tools or grader need to make an external LLM call.

Note that bus currently does not support auto-scale, and it suggest to run with only 1 replica, so we have to manually scale it up by creating new nodes and run script of the engine.

## Step 1: Create nodes
```bash
OAIPKG_OVERRIDE_WHEEL=unsafe_skip \
twdev create-ray-devbox \
cluster=prod-westus2-19 \
num_pods=1 \
setup_twapi=True \
num_gpu=8 \
job_name=bus-node-n \
priority_class=low-priority
```

## Step 2: Kick off bus engine
1. ssh to the node created in above
2. create a new tmux session
3. kick off bus engine by below command line. It uses o3-mini checkpoint, but it can be replaced by any other supported checkpoints.
```bash
export OPENAI_API_KEY="dummy_key"
RUST_BACKTRACE=1 python -m harmony_scripts.engines.start_engine \
--name o3-mini-engine \
--mode=optimal \
--snapshot_path="az://orngwus2cresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted" \
--renderer_name=harmony_v4.0.15_berry_v3_16k_orion_text \
--is_multimodal=False \
--gpu_kind=A100_80G \
--extra_config_string="raise_on_load_for_missing_tensor=False tensorcache_v2_load_allow_missing_tensor=True enable_tensorcache_v2=False" \
--cluster=local \
--bus_enable_qos=True \
--bus_rate_limiter=KV_UTIL \
--bus_topic_mode_or_user="researcher" \
--n_replicas=1
```

## Scale up
Repeat step 1~2 to scale up bus capacity. Note that node name need to be changed and unique.

## Monitor BUS nodes
[Bus v2 Dashboard - Singularity Orange - Dashboards - Grafana](https://ai-infra-ephgecb0cucpbkg8.wus2.grafana.azure.com/d/e972695d-bb0e-4049-8bb0-a07397f14dc4/bus-v2-dashboard?orgId=1&refresh=5m&var-Datasource=aeijxz20txtdsa&var-region=All&var-cluster=All&var-pod_user=All&var-topic=researcher&var-brix_pool=All&var-from_user=All&var-bus_line=bus&var-qos_type=All&var-brix_pool_regex=.%2A&var-topic_filter=.%2A&var-group_by=topic&var-unique_pod=All&var-join_alive_right=group%20by%20%28pool,%20user,%20cluster,%20topic%29%20%28default_oai_bus_alive%7Brole%3D&var-common_filter=topic%3D~%22researcher%22,%20pool%3D~%22.%2A%22,%20pool%3D~%22.%2A%22,%20topic%3D~%22%5Ebus:.%2A%22,%20user%3D~%22.%2A%22&var-_alive_signal_grouped_query_string=%28group%20by%20%28pool)

# Start TWAPI
**[Local TWAPI Engine](https://dev.azure.com/project-argos/Mimco/_wiki/wikis/Mackenzie.wiki/5943/Local-Twapi-Engine)**  
This is an alternative to the BUS setup, where you can host a model on your own devbox and call the hosted IP directly from your training job.

See the following command to start twapi on your cluster:

## Start TWAPI cluster

<table>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>westus-19</div>
      <pre style="overflow-x:auto; padding:12px 12px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
job_name="test-twapi"
cluster="prod-westus2-19"
num_pods=1
num_gpu=8
OAIPKG_OVERRIDE_WHEEL=unsafe_skip \
twdev create-ray-devbox cluster=$cluster setup_twapi=True num_pods=$num_pods num_gpu=$num_gpu job_name=$job_name
rcall-brix change-priority $job_name --priority team-high
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8x; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>uksouth-7</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
job_name="test-twapi"
cluster="prod-uksouth-7"
num_pods=1
num_gpu=8
OAIPKG_OVERRIDE_WHEEL=unsafe_skip \
twdev create-ray-devbox cluster=$cluster setup_twapi=True num_pods=$num_pods num_gpu=$num_gpu job_name=$job_name
rcall-brix change-priority $job_name --priority team-high
      </pre>
    </td>
  </tr>
</table>

## Start TWAPI engine
Do the usual ssh to the twapi devbox: `b ssh test-twapi`

Now you can start the twapi engine with the following command:

<table>
  <tr>
    <td style="background:#f7f9fc; padding:8px; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>westus-19</div>
      <pre style="overflow-x:auto; padding:12px 12px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
python -m twapi.bin.start_engine \
    --root_config="twppo.root ray_settings.ld_preload_torch_memplugin=False boot_timeout=12960000 rpc_timeout=12960000 connect_timeout=12960000 setup_profiling_timeout=12960000 create_dataset_timeout=12960000 create_model_comms_timeout=12960000 create_model_timeout=12960000 step_timeout=12960000 initial_step_timeout=12960000 download_checkpoint_timeout=12960000 upload_checkpoint_timeout=12960000"  \
    --model_config="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048"  \
    --skip_tw_ixf_comparison=True  \
    --n_replicas=1  \
    --n_op_shards=2  \
    --pipe_depth=4  \
    --load_path="az://orngwus2cresco/models/snapshots/models.tc/nv4-strawberry-step-320-transferred-20250122-decrypted" \
    --local=True \
    --sequence_parallelism_enabled=False \
    --falcon_sampling=False \
    --is_multimodal=False
      </pre>
    </td>
    <td style="background:#f7f9fc; padding:8x; border:1px solid #e1e4e8; border-radius:8px; vertical-align:top;">
      <div>uksouth-7</div>
      <pre style="overflow-x:auto; padding:12px 14px; margin:0; background:#ffffff; border:1px solid #ddd; border-radius:6px; box-shadow:0 2px 4px rgba(0,0,0,0.08); font-family:Consolas, 'Courier New', monospace; font-size:13px; line-height:1.4;">
python -m twapi.bin.start_engine \
    --root_config="twppo.root ray_settings.ld_preload_torch_memplugin=False boot_timeout=12960000 rpc_timeout=12960000 connect_timeout=12960000 setup_profiling_timeout=12960000 create_dataset_timeout=12960000 create_model_comms_timeout=12960000 create_model_timeout=12960000 step_timeout=12960000 initial_step_timeout=12960000 download_checkpoint_timeout=12960000 upload_checkpoint_timeout=12960000"  \
    --model_config="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048"  \
    --skip_tw_ixf_comparison=True  \
    --n_replicas=1  \
    --n_op_shards=2  \
    --pipe_depth=4  \
    --load_path="az://orngcresco/models/snapshots/models.tc/nv4-strawberry-step-320-transferred-20250122-decrypted" \
    --local=True \
    --sequence_parallelism_enabled=False \
    --falcon_sampling=False \
    --is_multimodal=False
      </pre>
    </td>
  </tr>
</table>

During the execution you should be able to see the IP of your engine under the POD description, something like this:
```bash
POD:    name: test-twapi-0 ip: ****************** hostname: test-twapi-0.rcall.beautifulperson.svc.cluster.local role: worker uptime: 0d0h1m52s
```
**Note:** It may take a while for the TWAPI engine to set up. Similar to the green setup, wait until you see the _CUDA OOMs_ table in the logs with allocated, active, and requested memory details.

### Check if twapi is accessible to training jobs
When the TWAPI engine starts, you see the IP address of the devbox in the logs, something like this:

You can ssh to your training job and, assuming your IP is `**************`, run the following command inside the trainer devbox to verify if TWAPI is running:
```bash
curl http://**************:5122/v1/inference -H "Content-Type: application/json" -H "Authorization: Bearer Fake" -H "OpenAI-Organization: openai" -d '{ "prompt": "Fun fact is fun because?", "max_tokens": 64, "temperature": 0 }'
```

### Enterprise Browser Tools

**NOTE** These tools are under active development and things will change.

There are two implementations for enterprise browser tools which can be used during training. The enterprise browser tools are tools which expose an interface similar to the OAI lean browser tool which is what the deepresearch model was trained on, but serves enterprise data rather than web pages from the internet.

The default tools, `EnterpriseBrowserTools`, use a json file of entities and initializes a local semantic index to mock search and get calls for enterprise data. This requires an entity file be uploaded for the training dataset. The dataset VertexEdgeLabsV1 is setup this way, see the presets files for details.

The alternative tools, `SubstrateEnterpriseBrowserTools`, use the credentials from a test tenant user account to call Substrate APIs during training. These calls aim to be as close as possible to the Substrate calls made by Researcher in production. To use these tools, credentials must be uploaded for the test tenant user to each rollout worker. The default path is /root/.test_tenant. Talk to joclausm for credential access; this will be automated in the future.

