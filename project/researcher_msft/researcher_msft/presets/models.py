from itertools import product

import berry.preset_utils
import structlog
from researcher_msft.presets.datasets import CLUSTER_MAP

logger = structlog.stdlib.get_logger(component=__name__)

MODEL_MAP = {
    "o3_mini": "models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted/",
    "dr_full": "dr/deep-research-vision-tc-20250124-decrypted/",
    "dr_mini": "deep-research-mini-vis-2025-03-18.transfer-2.tc-decrypted/",
}

peashooter_preset = berry.preset_utils.args_preset(
    # Mostly the default Peashooter settings, but with some tweaks for performance we've seen in other jobs.
    [
        # The first two help to manage KV util.
        # Roughly, what matters is num_sampling_processes * sampling_concurrency, as this modulates how many queued samples will simulatenously be processed
        # (Note that "each queued sample" is actually a grouping of 16 or 32 samples)
        # num_sampling_processes should generally be increased before sampling_concurrency, as the latter can be increased on the fly (e.g., through lemon)
        # You may want to increase these if your sampler KV util is low.
        # Recommendations (if overriding): num_sampling_processes <= 32; sampling_concurrency <= 128
        "peashooter.num_sampling_processes=32",
        "peashooter.sampling_concurrency=64",
        "peashooter.kv_spilling=False",  # Disable spilling KVs to CPU. This should lower latency since keeping KVs in GPU memory eliminates the transfer overhead, at a cost of higher memory usage.
        "peashooter.timeout_seconds.stalled_datapoint=3600",  # Timeout for stalled datapoints. (default 14400 seconds)
        "peashooter.num_instance_workers=64",  # Defines number of processes to schedule instance-related work. (default 32)
        "peashooter.timeout_seconds.pause_sampling_after_controller_crash=1800",  # Pause sampling for 30 minutes if controller crashes. (default 3600)
        "peashooter.timeout_seconds.pause_sampling_if_training_n_steps_behind=4",  # Pause sampling if training is more than 4 steps behind.
        "peashooter.delegation_concurrency_multiple=2.0",  # Defines how many sampling processes can be delegated to each instance worker. (default 1.0)
    ]
)

sampling_preset = berry.preset_utils.args_preset(
    [
        # Defines the number of batches to keep rolling out at any given time. (Default 30) Larger values increase sampling throughput at the cost of more memory.
        # I don't think the sampling is the bottleneck in our case, so we can afford to lower this.
        "batch_completer.n_batches_in_flight=16",
        # **IPB** The number of "successful" (information content > 0) instances to aim for to reach information_per_batch in each training batch. (Default 64)
        # During sampling, the batch completer sums the information content of completed instances until it reaches this target. This value controls your effective batch size in terms of
        # datapoints covered. If you set it too low you'll get very small batches; if you set it high the system will hold batches open until enough instances complete to meet the quota.
        "batch_completer.instances_per_batch=64",
        "defaults.instances_per_batch=64",
        # **SPI** Number of positive samples we want to get per instance.
        "...instance_completer.target_samples_per_instance=32",
        "defaults.target_samples_per_instance=32",
    ]
)

policy_preset = berry.preset_utils.args_preset(
    [
        "policy.ml_config=ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22 ",
        "policy.sampling_ml_config=ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=128 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True ixf_sampling_extension_gpu_to_cpu_async=True ",
        # policy.n_ctx should always be multiple of 1024 (for systems reasons), sampling_n_ctx can be whatever
        "policy.n_ctx=131072",
        # default n_ctx also sets the sampling n_ctx, which defined as maximum context length sampler can even see. It sets max tokens to n_ctx - prompt len.
        "defaults.n_ctx=131072",
        # Each datapoint in the underlying dataset will be staged multiple times, as specified by the `num_epochs` parameter.
        "berry_curriculum=berry_curriculums.MultiEpochCurriculum",
        "berry_curriculum.max_epochs=5",
    ]
)

MODEL_PRESETS = {
    f"{cluster_tag}_{model_tag}": berry.preset_utils.compose_presets(
        peashooter_preset,
        berry.preset_utils.args_preset(
            [
                f"policy.initial_checkpoint=az://{CLUSTER_MAP[cluster_tag]}/models/snapshots/{MODEL_MAP[model_tag]}",
                "root_config=mini.root.dev driver_rpc_timeout=600 init_actors_rpc_timeout=600 poll_for_error_timeout=600 dedicated_driver_node=False",
                f"security_profile=msft-orng",
            ]
        ),
        sampling_preset,
        policy_preset,
    )
    for cluster_tag, model_tag in product(
        CLUSTER_MAP.keys(),
        MODEL_MAP.keys(),
    )
}

globals().update(MODEL_PRESETS)
