from asyncio import Lock
import logging
import json
from typing import Any, Optional
import aiohttp
from azure.core.credentials_async import AsyncTokenCredential
from azure.core.credentials import AccessToken
import time
import os

import requests

logger = logging.getLogger("mcp-msgraph.auth")

class TestTenantTokenCredential(AsyncTokenCredential):
    """
    Manages the Sydney token lifecycle.
    """
    sydney_token: AccessToken|None = None
    lock = Lock()

    def __init__(self, username: str, password: str, tenant: str = "7ba44d51-046f-440b-8a8f-772eb62e2eb2", scope: str = "openid", client_id: str = "d3590ed6-52b3-4102-aeff-aad2292ab01c", auth_url: str = "https://login.windows.net/common/oauth2/token") -> None:
        self.tenant = tenant
        self.scope = scope
        self.client_id = client_id
        self.auth_url = auth_url
        self.username = username
        self.password = password
        self.resource_url = "https://substrate.office.com/"


    async def get_api_based_token(self) -> AccessToken:
        logger.info("Attempting to refresh token via API.")
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        payload = {
            'client_id': self.client_id,
            'tenant': self.tenant,
            'scope': self.scope,
            'grant_type': 'password',
            'username': self.username,
            'password': self.password,
            'resource': self.resource_url,
        }

        token_url = self.auth_url.format(tenant=self.tenant)
        async with aiohttp.ClientSession() as session:
            async with session.post(token_url, headers=headers, data=payload) as response:
                if response.status != 200:
                    logger.error(f"Token refresh failed with status {response.status}: {await response.text()}")
                    raise Exception("Token refresh failed.")
                try:
                    token_data = await response.json()
                    expires_on = int(time.time() * 1000) + (int(token_data.get('expires_in', 60)) * 1000)
                    return AccessToken(token=token_data['access_token'], expires_on=expires_on)
                except (KeyError, json.JSONDecodeError) as e:
                    logger.error(f"Error parsing token response: {e}")
                    raise

    async def get_token(
        self,
        *scopes: str,
        claims: Optional[str] = None,
        tenant_id: Optional[str] = None,
        enable_cae: bool = False,
        **kwargs: Any,
    ) -> AccessToken:
        """
        Asynchronously retrieves the Sydney token, refreshing it if necessary.
        """
        if self.sydney_token is None or self.sydney_token.expires_on < int(time.time() * 1000) + 300 * 1000:  # Refresh if token is expired or about to expire
            async with self.lock:
                if self.sydney_token is None or self.sydney_token.expires_on < int(time.time() * 1000) + 300 * 1000:
                    self.sydney_token = await self.get_api_based_token()
        return self.sydney_token