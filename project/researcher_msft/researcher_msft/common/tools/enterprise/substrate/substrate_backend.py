"""
SubstrateBrowser implementation for mcp-msgraph that provides enterprise browser functionality.
"""
import uuid
import httpx
import argparse
import asyncio
import logging
from typing import List, Optional

from typing import List, Optional

from lean_browser import base

from .auth import TestTenantTokenCredential
from .id_decoder import extract_claims_from_jwt, extract_tenant_id_from_token
from .substrate_types import Result, SearchResponse, SourceInfo

from typing import Optional
import aiohttp
import chz
from lean_browser import base
from researcher_msft.common.tools.enterprise.utils import (
    EntityType,
)
from tool_use.browsing import page_contents as pc_lib
from tool_use.browsing.labrador_browse_utils import ScoredPageContents

logger = logging.getLogger("mcp-msgraph.substrate_browser")

@chz.chz(typecheck=True)
class SubstrateBackend(base.Backend):
    source: str = "user_enterprise_data"
    credential: TestTenantTokenCredential

    @chz.init_property
    def available_sources(self) -> list[str]:
        return [self.source, *[e.value for e in EntityType]] 

    @chz.init_property
    def default_source(self) -> str:
        return self.source

    def get_by_source(self, source: str | None) -> base.Backend:
        if (source is None) or (source == self.source):
            return self
        
        return SubstrateBackend(source=source, credential=self.credential)

    async def search(
        self, query: str, topn: int, recency_days: int | None, session: aiohttp.ClientSession
    ) -> list[ScoredPageContents]:
        """
        Internal search implementation that queries substrate API and returns SearchResult objects.
        """
        # Default topn if not specified
        if topn is None:
            topn = 10
            
        # Search across multiple entity types unless a specific source is provided
        entity_request_by_type = {
            EntityType.FILE.value: {
                "entityType": "File",
                "fields": [
                    "URL",
                    "Title",
                    "Author",
                    "filename",
                    "HitHighlightedSummary",
                    "FromTuring",
                    "FileType",
                    "ListID",
                    "UniqueID",
                    "SiteId",
                    "WebId",
                    "DocId",
                    "InformationProtectionLabelId",
                    "FileId",
                    "Summary",
                    "ContentClass",
                    "ModifiedBy",
                    "SharingHistory",
                    "DefaultEncodingURL",
                    "SensitivityLabel",
                    "ContainerSensitivityLabelId",
                    "LastAccessTimebyMailboxOwner",
                    "LastModifyTimebyMailboxOwner",
                    "LastSharedWithMailboxOwnerDateTime",
                    "LabelProtectionType"
                ],
                "from": 0,
                "propertySet": "Optimized",
                "size": min(topn, 5),  # Limit file results
                "query": {
                    "QueryString": query,
                    "QueryTemplate": r"({searchterms})(FileType:pdf OR FileType:docx OR FileType:xlsx OR FileType:doc OR FileType:odt OR FileType:ppt OR FileType:pptx OR FileType:odp OR FileType:rtf OR FileType:txt OR FileType:htm OR FileType:html OR FileType:aspx OR FileType:loop OR FileType:fluid OR FileType:onepart OR FileType:xlsb OR FileType:xlsm OR FileType:xls OR FileType:csv OR FileType:dotx OR FileType:docm OR FileType:ppsx OR FileType:pptm)",
                },
                "resultsMerge": {
                    "BaseContentSource": "None",
                    "Type": "Interleaved"
                },
                "sort": {
                    "Field": "Score",
                    "SortDirection": "Desc"
                },
                "contentSources": ["OneDriveBusiness", "SharePoint"],
                "enableQueryUnderstanding": "false",
                "supportedResultSourceFormats": [
                    "EntityData"
                ],
                "topResultsCount": 0,
                "enableResultAnnotations": "false",
                "includeHiddenContent": "false",
                "annotationsCount": 0,
                "returnMailboxInformation": "false",
            },
            EntityType.EMAIL.value: {
                "entityType": "Conversation",
                "fields": [
                    "DocumentId",
                    "InformationProtectionLabelId",
                    "HasIrm",
                    "HitHighlightedSummary",
                    "RawContent",
                    "DrmRights",
                    "IsExternalSender",
                ],
                "contentSources": ["Exchange", "ExchangeArchive"],
                "filter": {
                    "Or": [
                        {"Term": {"DistinguishedFolderName": "msgfolderroot"}},
                        {"Term": {"DistinguishedFolderName": "deleteditems"}},
                    ]
                },
                "idFormat": "EwsId",
                "from": 0,
                "propertySet": "Optimized",
                "query": {"QueryString": query},
                "size": min(topn, 5),  # Limit message results
                "sort": {
                    "Field": "Score",
                    "SortDirection": "Desc"
                },
                "enableQueryUnderstanding": "false",
                "supportedResultSourceFormats": [
                    "EntityData"
                ],
                "topResultsCount": 0,
                "enableResultAnnotations": "false",
                "includeHiddenContent": "false",
                "annotationsCount": 0,
                "returnMailboxInformation": "false",
            },
            EntityType.MESSAGE.value: {
                "entityType": "Message",
                "contentSources": ["Teams"],
                "fields": [
                    "Extension_SkypeSpaces_ConversationPost_Extension_FromSkypeInternalId_String",
                    "Extension_SkypeSpaces_ConversationPost_Extension_ThreadType_String",
                    "Extension_SkypeSpaces_ConversationPost_Extension_SkypeGroupId_String",
                    "WebLink",
                    "Extension_SkypeSpaces_ConversationPost_Extension_Topic_String",
                ],
                "propertySet": "Optimized",
                "query": {
                    "queryString": query,
                },
                "idFormat": "EwsId",
                "from": 0,
                "size": topn,
                "sort": {
                    "Field": "Score",
                    "SortDirection": "Desc",
                },
                "enableQueryUnderstanding": "false",
                "supportedResultSourceFormats": [
                    "EntityData"
                ],
                "topResultsCount": 0,
                "enableResultAnnotations": "false",
                "includeHiddenContent": "false",
                "annotationsCount": 0,
                "returnMailboxInformation": "false",
            },
            EntityType.MEETING_TRANSCRIPT.value: {
                "entityType": "File",
                "fields": [
                    "URL",
                    "Title",
                    "Author",
                    "filename",
                    "HitHighlightedSummary",
                    "FromTuring",
                    "FileType",
                    "ListID",
                    "UniqueID",
                    "SiteId",
                    "WebId",
                    "DocId",
                    "InformationProtectionLabelId",
                    "FileId",
                    "Summary",
                    "ContentClass",
                    "ModifiedBy",
                    "SharingHistory",
                    "DefaultEncodingURL",
                    "DocumentLink",
                    "SensitivityLabel",
                    "ContainerSensitivityLabelId",
                    "LastAccessTimebyMailboxOwner",
                    "LastModifyTimebyMailboxOwner",
                    "LastSharedWithMailboxOwnerDateTime",
                    "LabelProtectionType",
                    "CollaboratorNames",
                    "CollaboratorAADIDs",
                    "RecordingStartDateTime"
                ],
                "from": 0,
                "propertySet": "Optimized",
                "contentSources": [
                    "OneDriveBusiness",
                    "SharePoint"
                ],
                "query": {
                    "QueryString": query,
                    "QueryTemplate": r"({searchterms})(FileType:loop)",
                },
                "resultsMerge": {
                    "BaseContentSource": "None",
                    "Type": "Interleaved"
                },
                "size": min(topn, 10),  # Limit meeting transcript results
                "sort": {
                    "Field": "Score",
                    "SortDirection": "Desc"
                },
                "enableQueryUnderstanding": "false",
                "idFormat": "EwsId",
                "supportedResultSourceFormats": [
                    "EntityData"
                ],
                "topResultsCount": 0,
                "enableResultAnnotations": "false",
                "includeHiddenContent": "false",
                "annotationsCount": 0,
                "returnMailboxInformation": "false"
            },
            EntityType.MEETING.value: {
                "EntityType": "Event",
                "From": 0,
                "Size": min(topn, 5),  # Limit meeting results
                "ContentSources": [
                    "Exchange"
                ],
                "Query": {
                    "QueryString": query,
                },
                "Sort": {
                    "Field": "Score",
                    "SortDirection": "Desc"
                }
            },
        }

        answer_entity_request_by_type = {
            EntityType.PERSON.value: {
                "EntityTypes": [
                    "People"
                ],
                "propertySet": "Optimized",
                "query": {"QueryString": query},
                "size": min(topn, 5),  # Limit people results
                "enableQueryUnderstanding": "false",
                "idFormat": "EwsId",
                "topResultsCount": 0,
                "returnMailboxInformation": "false"
            }
        }

        query_alterations_by_type = {
            EntityType.MEETING_TRANSCRIPT.value:
            {
                "EnableSuggestion": "true",
                "EnableAlteration": "true",
                "SupportedRecourseDisplayTypes": [
                    "Suggestion",
                    "NoResultModification",
                    "NoResultFolderRefinerModification",
                    "NoRequeryModification",
                    "Modification"
                ],
                "LuClassificationOverride": [
                    {
                        "Name": "Transcript",
                        "Intent": "search"
                    }
                ]
            },
            EntityType.MEETING.value: {
                "EnableSuggestion": "true",
                "EnableAlteration": "true",
                "SupportedRecourseDisplayTypes": [
                    "Suggestion",
                    "NoResultModification",
                    "NoResultFolderRefinerModification",
                    "NoRequeryModification",
                    "Modification"
                ]
            },
        }
        
        entity_requests = []
        source = self.source
        if source == "user_enterprise_data":
            entity_requests = list(entity_request_by_type.values())
        elif source in entity_request_by_type:
            entity_requests = [entity_request_by_type[source]]

        answer_entity_requests = []
        if not source:
            answer_entity_requests = list(answer_entity_request_by_type.values())
        elif source in answer_entity_request_by_type:
            answer_entity_requests = [answer_entity_request_by_type[source]]

        query_alterations = []
        if not source:
            query_alterations = list(query_alterations_by_type.values())
        elif source in query_alterations_by_type:
            query_alterations = [query_alterations_by_type[source]]

        scenario = {}
        if source in (EntityType.MEETING.value, EntityType.PERSON.value, EntityType.MEETING_TRANSCRIPT.value):
            scenario = {
                "Dimensions": [
                    {
                        "DimensionName": "ScenarioDescription",
                        "DimensionValue": "officeweb.fetch.unknownpurpose.nomessageannotationtype"
                    },
                    {
                        "DimensionName": "ScenarioType",
                        "DimensionValue": "AUO"
                    },
                    {
                        "DimensionName": "OriginClientName",
                        "DimensionValue": "officeweb"
                    },
                    {
                        "DimensionName": "QueryType",
                        "DimensionValue": "AllResults"
                    },
                    {
                        "DimensionName": "RequestedSearchPlatform",
                        "DimensionValue": "Gl2X"
                    },
                ],
                "Name": "sydney",
                "TrafficMode": "Normal"
            }

        
        logger.debug(f"Entity requests: {entity_requests}")
        logger.debug(f"Answer entity requests: {answer_entity_requests}")
        logger.debug(f"Query alterations: {query_alterations}")
        logger.debug(f"Scenario: {scenario}")

        try:
            response = await self._query_substrate(entity_requests, answer_entity_requests, query_alterations, scenario)
            return self._convert_response_to_search_results(response)
        except Exception as e:
            logger.error(f"Search with source {source} and query {query} failed", exc_info=True)
            return []

    async def fetch(self, url: str, session: aiohttp.ClientSession) -> pc_lib.PageContents:
        """
        Gets a page by its URL using the substrate API.
        """
        try:
            if url.startswith("SPO_"):
                # Handle vroom ID format
                substrate_url = f"https://substrate.office.com/api/beta/me/Files('{url}')"
            elif url.startswith("https://substrate.office.com/api/"):
                substrate_url = url
            else:
                # Assume it's a regular URL that needs to be opened
                return pc_lib.PageContents(
                    url=url,
                    title=f"External URL: {url}",
                    text=f"This is an external URL that cannot be opened directly: {url}\n\nUse the browser.open functionality to find content within the organization.",
                    urls={},
                    name2idx={},
                )
            
            token = await self.credential.get_token("https://substrate.office.com/.default")
            headers = {
                "Authorization": f"Bearer {token.token}",
                "Content-Type": "application/json",
                "Prefer": 'exchange.behavior="ApplicationData,SubstrateFiles",outlook.data-source="Substrate",substrate.flexibleschema'
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    substrate_url,
                    params={"$select": "*"},
                    headers=headers,
                    follow_redirects=True,
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return self._convert_substrate_item_to_webpage(data, url)
                else:
                    logger.error(f"Failed to fetch {substrate_url}: {response.status_code}")
                    return pc_lib.PageContents(
                        url=url,
                        title="Error loading content",
                        text=f"Failed to load content from {url}. Status: {response.status_code}",
                        urls={},
                        name2idx={},
                    )
                    
        except Exception as e:
            logger.error(f"Error fetching page {url}: {e}")
            return pc_lib.PageContents(
                url=url,
                title="Error loading content",
                text=f"An error occurred while loading the content: {str(e)}",
                urls={},
                name2idx={},
            )

    async def _query_substrate(
        self,
        entity_requests: Optional[List[dict]] = None, 
        answer_entity_requests: Optional[List[dict]] = None, 
        query_alterations: Optional[List[dict]] = None, 
        scenario: Optional[dict] = None
    ) -> SearchResponse:
        """
        Query the substrate with the given entity requests, answer entity requests, query alterations, and scenario.
        """
        if not entity_requests and not answer_entity_requests:
            raise ValueError("At least one of entity_requests or answer_entity_requests must be provided.")
        
        request_body = {
            "Cvid": str(uuid.uuid4()),
            "Scenario": {"Name": "mcp-msgraph"},
            "TimeZone": "UTC",
            "TextDecorations": "Off",
        }
        
        if scenario:
            request_body["Scenario"] = scenario
        if entity_requests:
            request_body["EntityRequests"] = entity_requests
        if answer_entity_requests:
            request_body["AnswerEntityRequests"] = answer_entity_requests
        if query_alterations:
            request_body["QueryAlterations"] = query_alterations

        # Extract anchor mailbox and tenant ID from token
        token = await self.credential.get_token("https://substrate.office.com/search/.default")
        claims = extract_claims_from_jwt(token.token)
        tenant_id = extract_tenant_id_from_token(token.token)
        
        # Get anchor mailbox from UPN claim
        anchor_mailbox = None
        if claims and (upn := claims.get("upn")):
            anchor_mailbox = upn

        headers = {
            "Authorization": f"Bearer {token.token}",
            "Content-Type": "application/json",
            "Prefer": 'outlook.data-source="Substrate", exchange.behavior="SubstrateFiles"',
        }

        # Add optional fields if available
        if anchor_mailbox:
            headers["X-AnchorMailbox"] = anchor_mailbox
        if tenant_id:
            headers["X-TenantId"] = tenant_id

        logger.debug(f"Substrate query request body: {request_body}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://substrate.office.com/search/api/v2/query",
                json=request_body,
                headers=headers,
            )

            logger.debug(f"Substrate query response: {response.json()}")

            if response.status_code != 200:
                logger.error(f"Substrate query failed: {response.status_code} - {response.text}")
                raise httpx.HTTPStatusError(f"Substrate query failed with status {response.status_code}", request=response.request, response=response)
            
            try:
                response_obj = SearchResponse.model_validate_json(response.text, strict=False)
                if tenant_id := extract_tenant_id_from_token(token.token):
                    response_obj.set_default_tenant_id(tenant_id)
                return response_obj
            except Exception as e:
                logger.error(f"Failed to validate response: {response.text}", exc_info=True)
                raise e
    
    def _convert_response_to_search_results(self, response: SearchResponse) -> List[ScoredPageContents]:
        """
        Convert SearchResponse to a list of SearchResult objects.
        """
        results = []
        result_id = 0
        
        # Process entity sets (files, messages, etc.)
        if response.EntitySets:
            for entity_set in response.EntitySets:
                for result_set in entity_set.ResultSets:
                    for result in result_set.Results:                        
                        search_result = self._convert_result_to_search_result(result, result_id)
                        if search_result:
                            results.append(search_result)
                            result_id += 1
        
        # Process answer entity sets (people)
        if response.AnswerEntitySets:
            for answer_entity_set in response.AnswerEntitySets:
                for result_set in answer_entity_set.ResultSets:
                    for result in result_set.Results:
                        search_result = self._convert_result_to_search_result(result, result_id)
                        if search_result:
                            results.append(search_result)
                            result_id += 1
        
        return results
    
    def _convert_result_to_search_result(self, result: Result, result_id: int) -> Optional[ScoredPageContents]:
        """
        Convert a single result to a SearchResult object.
        """
        source = result.Source if hasattr(result, 'Source') else None
        if not source:
            return None
        
        snippet = result.HitHighlightedSummary if hasattr(result, 'HitHighlightedSummary') else None
        if not snippet:
            snippet = self._get_result_snippet(source)
            
        url = self._get_result_url(source)
        title = self._get_result_title(source)
        
        return ScoredPageContents(
            url=url,
            title=title,
            text=snippet,
            name2idx={},
            urls={},
            score=result.Rank or 0.0, # TODO Does Rank need inverted? Can we get the score?
        )
    
    def _get_result_url(self, source: SourceInfo) -> str:
        """
        Generate appropriate URL for the result.
        """
        # Try to get OData URL first
        if hasattr(source, 'ODataId') and source.ODataId:
            return source.ODataId
        
        # For files, try to construct vroom ID
        if hasattr(source, 'VroomId') and source.VroomId:
            return source.VroomId
        
        # For messages, try to get the ID
        if hasattr(source, 'Id') and source.Id:
            return f"message://{source.Id}"
        
        # Fallback
        return f"substrate://item/{uuid.uuid4()}"
    
    def _get_result_title(self, source: SourceInfo) -> str:
        """
        Generate appropriate title for the result.
        """
        # File title
        if hasattr(source, 'Title') and source.Title:
            return source.Title
        
        if hasattr(source, 'Filename') and source.Filename:
            return source.Filename
        
        # Message/Email title
        if hasattr(source, 'Subject') and source.Subject:
            return source.Subject
        elif hasattr(source, 'ConversationTopic') and source.ConversationTopic:
            return source.ConversationTopic
        
        # People title
        if hasattr(source, 'DisplayName') and source.DisplayName:
            return source.DisplayName
        
        # Fallback
        return "Untitled"
    
    def _get_result_snippet(self, source: SourceInfo) -> str:
        """
        Generate appropriate snippet for the result.
        """
        snippets = []
        
        # Add description first if available
        if hasattr(source, 'Description') and source.Description:
            snippets.append(f"Description: {source.Description}")

        # Add preview second if available
        if hasattr(source, 'Preview') and source.Preview:
            snippets.append(f"Preview: {source.Preview}")
        
        # Add metadata based on type
        if hasattr(source, 'FileType') and source.FileType:
            snippets.append(f"Type: {source.FileType}")
        
        if hasattr(source, 'ModifiedBy') and source.ModifiedBy:
            snippets.append(f"Modified by: {source.ModifiedBy}")
        
        if hasattr(source, 'LastModifiedTime') and source.LastModifiedTime:
            snippets.append(f"Modified: {source.LastModifiedTime}")
        
        # For people
        if hasattr(source, 'EmailAddresses') and source.EmailAddresses:
            snippets.append(f"Email: {', '.join(source.EmailAddresses[:2])}")
        
        if hasattr(source, 'JobTitle') and source.JobTitle:
            snippets.append(f"Title: {source.JobTitle}")
        
        if hasattr(source, 'Department') and source.Department:
            snippets.append(f"Department: {source.Department}")
        
        # For messages
        if hasattr(source, 'From') and source.From and hasattr(source.From, 'EmailAddress'):
            if source.From.EmailAddress and hasattr(source.From.EmailAddress, 'Name'):
                snippets.append(f"From: {source.From.EmailAddress.Name}")
        
        if hasattr(source, 'DateTimeReceived') and source.DateTimeReceived:
            snippets.append(f"Received: {source.DateTimeReceived}")

        # If we have content, format it nicely
        if snippets:
            # Check if we have description and/or preview snippets
            description_snippet = None
            preview_snippet = None
            metadata_snippets = []
            
            for snippet in snippets:
                if snippet.startswith("Description: "):
                    description_snippet = snippet
                elif snippet.startswith("Preview: "):
                    preview_snippet = snippet
                else:
                    metadata_snippets.append(snippet)
            
            if metadata_snippets:
                metadata_text = " | ".join(['METADATA'] + metadata_snippets)
                if description_snippet:
                    return f"({metadata_text})\n{description_snippet}"
                elif preview_snippet:
                    return f"({metadata_text})\n{preview_snippet}"
                else:
                    return f"({metadata_text})\n**No preview available, use open to get the full content**"
            elif description_snippet:
                return description_snippet
            else:
                return "**No preview available, use open to get the full content**"
        else:
            return "No description available"
    
    def _convert_substrate_item_to_webpage(self, data: dict, url: str) -> pc_lib.PageContents:
        """
        Convert substrate item data to a WebPage object with human-readable content.
        """
        title = "Substrate Content"
        content_lines = []
        
        # Extract basic information - check both uppercase and lowercase variants
        if 'fileName' in data:
            title = data['fileName']
            content_lines.append(f"File Name: {data['fileName']}")
        elif 'FileName' in data:
            title = data['FileName']
            content_lines.append(f"File Name: {data['FileName']}")
        elif 'displayName' in data:
            title = data['displayName']
            content_lines.append(f"Display Name: {data['displayName']}")
        elif 'DisplayName' in data:
            title = data['DisplayName']
            content_lines.append(f"Display Name: {data['DisplayName']}")
        elif 'subject' in data:
            title = data['subject']
            content_lines.append(f"Subject: {data['subject']}")
        elif 'Subject' in data:
            title = data['Subject']
            content_lines.append(f"Subject: {data['Subject']}")
        
        # Add metadata - check both cases
        if 'lastModifiedDateTime' in data:
            content_lines.append(f"Last Modified: {data['lastModifiedDateTime']}")
        elif 'LastModifiedDateTime' in data:
            content_lines.append(f"Last Modified: {data['LastModifiedDateTime']}")
        
        if 'createdDateTime' in data:
            content_lines.append(f"Created: {data['createdDateTime']}")
        elif 'CreatedDateTime' in data:
            content_lines.append(f"Created: {data['CreatedDateTime']}")
        
        if 'fileSize' in data:
            content_lines.append(f"File Size: {data['fileSize']} bytes")
        elif 'FileSize' in data:
            content_lines.append(f"File Size: {data['FileSize']} bytes")
        
        if 'author' in data:
            content_lines.append(f"Author: {data['author']}")
        elif 'Author' in data:
            content_lines.append(f"Author: {data['Author']}")
        
        # Add content if available - check both cases
        if 'fileContent' in data and data['fileContent']:
            if 'text' in data['fileContent']:
                content_lines.append("\n--- Content ---")
                content_lines.append(data['fileContent']['text'])
            elif 'Text' in data['fileContent']:
                content_lines.append("\n--- Content ---")
                content_lines.append(data['fileContent']['Text'])
        elif 'FileContent' in data and data['FileContent']:
            if 'text' in data['FileContent']:
                content_lines.append("\n--- Content ---")
                content_lines.append(data['FileContent']['text'])
            elif 'Text' in data['FileContent']:
                content_lines.append("\n--- Content ---")
                content_lines.append(data['FileContent']['Text'])
        elif 'body' in data:
            content_lines.append("\n--- Content ---")
            if isinstance(data['body'], dict) and 'content' in data['body']:
                content_lines.append(data['body']['content'])
            elif isinstance(data['body'], dict) and 'Content' in data['body']:
                content_lines.append(data['body']['Content'])
            else:
                content_lines.append(str(data['body']))
        elif 'Body' in data:
            content_lines.append("\n--- Content ---")
            if isinstance(data['Body'], dict) and 'content' in data['Body']:
                content_lines.append(data['Body']['content'])
            elif isinstance(data['Body'], dict) and 'Content' in data['Body']:
                content_lines.append(data['Body']['Content'])
            else:
                content_lines.append(str(data['Body']))
        elif 'content' in data:
            content_lines.append("\n--- Content ---")
            content_lines.append(str(data['content']))
        elif 'Content' in data:
            content_lines.append("\n--- Content ---")
            content_lines.append(str(data['Content']))
        
        # Add other interesting fields
        excluded_keys = ['fileName', 'FileName', 'displayName', 'DisplayName', 'subject', 'Subject', 
                        'lastModifiedDateTime', 'LastModifiedDateTime', 'createdDateTime', 'CreatedDateTime', 
                        'fileSize', 'FileSize', 'author', 'Author', 'fileContent', 'FileContent', 
                        'body', 'Body', 'content', 'Content']
        for key, value in data.items():
            if key not in excluded_keys:
                if isinstance(value, (str, int, float, bool)) and str(value).strip():
                    content_lines.append(f"{key}: {value}")
        
        content = "\n".join(content_lines) if content_lines else "No content available"
        
        return pc_lib.PageContents(
            url=url,
            title=title,
            text=content,
            urls={},
            name2idx={},
        )
