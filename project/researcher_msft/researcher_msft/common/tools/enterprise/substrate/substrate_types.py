from typing import List, Optional, Dict, Any
import uuid
from pydantic import BaseModel, Field, computed_field

from .id_decoder import SpoIdCom<PERSON>, SpoIdEncoder


class Phone(BaseModel):
    """Phone number model."""
    Number: Optional[str] = None
    Type: Optional[str] = None

class EmailAddressInfo(BaseModel):
    """Email address information."""
    Name: Optional[str] = None
    Address: Optional[str] = None


class EmailContact(BaseModel):
    """Email contact information."""
    EmailAddress: Optional[EmailAddressInfo] = None


class ItemIdInfo(BaseModel):
    """Item identifier."""
    Id: Optional[str] = None


class ParentFolderIdInfo(BaseModel):
    """Parent folder identifier."""
    Id: Optional[str] = None


class ConversationIdInfo(BaseModel):
    """Conversation identifier."""
    Id: Optional[str] = None


class MentionsPreviewInfo(BaseModel):
    """Mentions preview information."""
    IsMentioned: Optional[bool] = None

class SourceInfo(BaseModel):
    """Source information for a search result."""
    # People search fields
    Id: Optional[str] = Field(default=None, exclude=True)
    TenantId: Optional[str] = Field(default=None, exclude=True)
    DisplayName: Optional[str] = None
    GivenName: Optional[str] = None
    Surname: Optional[str] = None
    EmailAddresses: Optional[List[str]] = None
    ProxyEmailAddresses: Optional[List[str]] = Field(default=None, exclude=True)
    CompanyName: Optional[str] = None
    Department: Optional[str] = None
    OfficeLocation: Optional[str] = None
    AdditionalOfficeLocation: Optional[str] = Field(default=None, exclude=True)
    Phones: Optional[List[Phone]] = None
    JobTitle: Optional[str] = None
    ImAddress: Optional[str] = Field(default=None, exclude=True)
    PeopleType: Optional[str] = Field(default=None, exclude=True)
    PeopleSubtype: Optional[str] = Field(default=None, exclude=True)
    ADObjectId: Optional[str] = Field(default=None, exclude=True)
    UserPrincipalName: Optional[str] = None
    Confidence: Optional[str] = Field(default=None, exclude=True)
    ExternalDirectoryObjectId: Optional[str] = Field(default=None, exclude=True)
    HighlightSuggestions: Optional[List[str]] = Field(default=None, exclude=True)
    Text: Optional[str] = None
    QueryText: Optional[str] = Field(default=None, exclude=True)
    PropertyHits: Optional[List[str]] = Field(default=None, exclude=True)
    
    # File/Document search fields
    Rank: Optional[int] = None
    Title: Optional[str] = None
    Size: Optional[int] = None
    Description: Optional[str] = None
    FileExtension: Optional[str] = None
    LastModifiedTime: Optional[str] = None
    FileType: Optional[str] = None
    IdentitySiteCollectionId: Optional[str] = Field(default=None, exclude=True)
    IdentityWebId: Optional[str] = Field(default=None, exclude=True)
    IdentityListId: Optional[str] = Field(default=None, exclude=True)
    IdentityListItemId: Optional[str] = Field(default=None, exclude=True)
    Filename: Optional[str] = Field(default=None, alias="filename")
    ModifiedBy: Optional[str] = None
    Author: Optional[str] = None
    ContentTypeId: Optional[str] = Field(default=None, exclude=True)
    ViewsLifeTime: Optional[int] = Field(default=None, exclude=True)
    ViewsRecent: Optional[int] = Field(default=None, exclude=True)

    @computed_field()
    @property
    def VroomId(self) -> Optional[str]:
        if self.IdentityListItemId and self.IdentitySiteCollectionId and self.IdentityWebId and self.IdentityListId:
            spo_components = SpoIdComponents(
                uuid.UUID(self.IdentitySiteCollectionId),
                uuid.UUID(self.IdentityWebId),
                uuid.UUID(self.IdentityListId),
                uuid.UUID(self.IdentityListItemId)
            )
            encoder = SpoIdEncoder()
            return encoder.encode_vroom_id(spo_components)
        return None
    
    @computed_field
    @property
    def ContainerId(self) -> Optional[str]:
        if self.IdentitySiteCollectionId and self.TenantId:
            return f"SPO_{self.IdentitySiteCollectionId}@SPO_{self.TenantId}"
        return None
    
    @computed_field
    @property
    def ODataId(self) -> Optional[str]:
        if self.VroomId and self.ContainerId:
            # OData ID for files
            return f"https://substrate.office.com/api/beta/ItemContainers('{self.ContainerId}')/Files('{self.VroomId}')"
        if self.ConversationId and self.MailboxGuids and self.Id:
            # OData ID for Teams messages and emails
            cleaned_id = self.Id.replace("+hSli+", "_hSli_")
            return f"https://substrate.office.com/api/v2.0/me/Messages('{cleaned_id}')"

        return
    
    # Teams/Message search fields
    ParentFolderHexId: Optional[str] = Field(default=None, exclude=True)
    ParentFolderId: Optional[ParentFolderIdInfo] = Field(default=None, exclude=True)
    ParentFolderRestId: Optional[str] = Field(default=None, exclude=True)
    SortOrderSource: Optional[str] = Field(default=None, exclude=True)
    ItemId: Optional[ItemIdInfo] = Field(default=None, exclude=True)
    Extensions: Optional[Dict[str, Any]] = Field(default=None, exclude=True)
    ImmutableId: Optional[str] = Field(default=None, exclude=True)
    AnnouncementTitle: Optional[str] = None
    ClientConversationId: Optional[str] = Field(default=None, exclude=True)
    ClientThreadId: Optional[str] = Field(default=None, exclude=True)
    ConversationIndex: Optional[str] = Field(default=None, exclude=True)
    DateTimeCreated: Optional[str] = None
    DateTimeLastModified: Optional[str] = None
    DateTimeReceived: Optional[str] = None
    DateTimeSent: Optional[str] = None
    DisplayBcc: Optional[str] = None
    DisplayTo: Optional[str] = None
    HasAttachments: Optional[bool] = None
    IconIndex: Optional[str] = Field(default=None, exclude=True)
    ImmutableEntryId: Optional[str] = Field(default=None, exclude=True)
    Importance: Optional[str] = None
    InferenceClassification: Optional[str] = Field(default=None, exclude=True)
    InternetMessageId: Optional[str] = Field(default=None, exclude=True)
    IsDraft: Optional[bool] = None
    IsRead: Optional[bool] = None
    ItemClass: Optional[str] = Field(default=None, exclude=True)
    ParentFolderDisplayName: Optional[str] = None
    Preview: Optional[str] = None
    ReceivedOrRenewTime: Optional[str] = Field(default=None, exclude=True)
    Sender: Optional[EmailContact] = None
    Sensitivity: Optional[str] = Field(default=None, exclude=True)
    Subject: Optional[str] = None
    ConversationHexId: Optional[str] = Field(default=None, exclude=True)
    ConversationId: Optional[ConversationIdInfo] = Field(default=None, exclude=True)
    ConversationRestId: Optional[str] = Field(default=None, exclude=True)
    ConversationThreadId: Optional[str] = Field(default=None, exclude=True)
    From: Optional[EmailContact] = None
    ItemHexId: Optional[str] = Field(default=None, exclude=True)
    ItemRestId: Optional[str] = Field(default=None, exclude=True)
    MailboxGuids: Optional[List[str]] = Field(default=None, exclude=True)
    MentionsPreview: Optional[MentionsPreviewInfo] = None
    SortKey: Optional[int] = Field(default=None, exclude=True)

    # Email specific fields
    ConversationTopic: Optional[str] = Field(default=None, exclude=True)

class Result(BaseModel):
    """Individual search result."""
    Id: Optional[str] = Field(default=None, exclude=True)
    ContentSource: Optional[str] = Field(default=None, exclude=True)
    ReferenceId: Optional[str] = Field(default=None, exclude=True)
    Rank: Optional[int] = None
    Source: Optional[SourceInfo] = None
    Type: Optional[str] = None
    # File search specific fields
    Provenance: Optional[str] = Field(default=None, exclude=True)
    SearchKey: Optional[str] = Field(default=None, exclude=True)
    SortOrderSource: Optional[str] = Field(default=None, exclude=True)
    ResultSearchType: Optional[str] = Field(default=None, exclude=True)
    # Teams/Message search specific fields
    HitHighlightedSummary: Optional[str] = None


class ResultSet(BaseModel):
    """Set of search results."""
    ContentSources: Optional[List[str]] = Field(default=None, exclude=True)
    Results: List['Result'] = Field(default_factory=list)
    Total: int = -1
    MoreResultsAvailable: Optional[bool] = None
    Rank: Optional[int] = None
    # File search specific fields
    Provenance: Optional[str] = Field(default=None, exclude=True)
    TotalWithoutCollapsing: Optional[int] = Field(default=None, exclude=True)
    QueryId: Optional[str] = Field(default=None, exclude=True)
    QueryModification: Optional[str] = None
    Properties: Optional[Dict[str, Any]] = Field(default=None, exclude=True)


class RankInfo(BaseModel):
    """Ranking information."""
    Confidence: Optional[float] = None


class AnswerEntitySet(BaseModel):
    """Answer entity set containing ranked results."""
    Rank: Optional[RankInfo] = None
    ResultSets: List[ResultSet]
    EntityType: Optional[str] = None


class InstrumentationInfo(BaseModel):
    """Instrumentation information."""
    TraceId: Optional[str] = Field(default=None, exclude=True)


class EntitySet(BaseModel):
    """Entity set for file/document search results."""
    ResultSets: List[ResultSet]
    EntityType: Optional[str] = None
    SearchTerms: Optional[List[str]] = None
    Properties: Optional[Dict[str, Any]] = Field(default=None, exclude=True)


class ExtendedDataInfo(BaseModel):
    """Extended data information."""
    ItemInfo: Optional[Dict[str, Any]] = None


class SearchResponse(BaseModel):
    """Main search response model."""
    ApiVersion: Optional[str] = Field(default=None, exclude=True)
    # People search structure
    AnswerEntitySets: Optional[List[AnswerEntitySet]] = None
    # File/Document search structure
    SearchTerms: Optional[List[str]] = None
    EntitySets: Optional[List[EntitySet]] = None
    ExtendedData: Optional[ExtendedDataInfo] = None
    WasGroupsRestricted: Optional[bool] = Field(default=None, exclude=True)
    # Common fields
    RestrictedSearchMode: Optional[str] = Field(default=None, exclude=True)
    Instrumentation: Optional[InstrumentationInfo] = None

    def set_default_tenant_id(self, tenant_id: str):
        """Set the tenant ID for the response which is important for generating valid substrate URLs"""
        if self.AnswerEntitySets:
            for entity_set in self.AnswerEntitySets:
                for result_set in entity_set.ResultSets:
                    for result in result_set.Results:
                        if result.Source and result.Source.TenantId is None:
                            result.Source.TenantId = tenant_id
        if self.EntitySets:
            for entity_set in self.EntitySets:
                for result_set in entity_set.ResultSets:
                    for result in result_set.Results:
                        if result.Source and result.Source.TenantId is None:
                            result.Source.TenantId = tenant_id

class FileContent(BaseModel):
    """File content information."""
    Text: Optional[str] = None

class ErrorData(BaseModel):
    """Error data model."""
    code: Optional[str] = None
    message: Optional[str] = None
    

class FileResponse(BaseModel):
    # FileName,Id,FileOwner,FileExtension,LastModifiedDateTime,FileContent
    """Response model for file retrieval."""
    Id: Optional[str] = None
    FileName: Optional[str] = None
    LastModifiedDateTime: Optional[str] = None
    fileContent: Optional[FileContent] = Field(default=None, alias="FileContent")
    error: Optional[ErrorData] = None
