"""Python implementation of Graphlib.Identifiers for decoding Substrate API identifiers.

This module provides utilities for decoding various identifier formats used by
Microsoft Graph, Substrate, and SharePoint Online (SPO), based on the C#
Graphlib.Identifiers library.
"""

import base64
import hashlib
import json
import re
import struct
import uuid
from dataclasses import dataclass
from typing import Optional, Tuple
import logging

logger = logging.getLogger("mcp-msgraph.id_decoder")

class Base64UrlEncoder:
    """Implements base64url encoding/decoding (RFC 4648)."""

    @staticmethod
    def encode(data: bytes) -> str:
        """Encode bytes to base64url string."""
        return (
            base64.b64encode(data)
            .decode("ascii")
            .replace("+", "-")
            .replace("/", "_")
            .rstrip("=")
        )

    @staticmethod
    def decode(encoded: str) -> Optional[bytes]:
        """Decode base64url string to bytes."""
        try:
            # Replace URL-safe characters back to standard base64
            encoded = encoded.replace("-", "+").replace("_", "/")

            # Add padding if needed
            padding = 4 - len(encoded) % 4
            if padding != 4:
                encoded += "=" * padding

            return base64.b64decode(encoded)
        except Exception:
            return None


class Base32Encoder:
    """Implements base32 encoding/decoding."""

    BASE32_MAP = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567"

    @staticmethod
    def encode(data: bytes) -> str:
        """Encode bytes to base32 string."""
        result = []
        bits = 0
        need_bits = 5
        bits_left = 8
        index = 0

        while index < len(data):
            if bits_left == 0:
                index += 1
                bits_left = 8
                continue

            current_bits = (data[index] << (8 - bits_left) & 0xFF) >> (8 - bits_left)

            if need_bits <= bits_left:
                bits = (bits << need_bits) + (current_bits >> (bits_left - need_bits))
                bits_left = bits_left - need_bits

                result.append(Base32Encoder.BASE32_MAP[bits])
                bits = 0
                need_bits = 5
            else:
                bits = (bits << bits_left) + current_bits
                need_bits = need_bits - bits_left
                bits_left = 0

        if 0 < need_bits < 5:
            bits = bits << need_bits
            result.append(Base32Encoder.BASE32_MAP[bits])

        return "".join(result)

    @staticmethod
    def decode(data: str, offset: int = 0) -> Optional[bytes]:
        """Decode base32 string to bytes."""
        try:
            # Build decode map
            decode_map = [-1] * 256
            for i, char in enumerate(Base32Encoder.BASE32_MAP):
                decode_map[ord(char)] = i

            data_length = len(data) - offset
            buffer = bytearray(data_length * 5 // 8)
            yielded_bytes = 0

            bits = 0
            need_bits = 8

            for char in data[offset:]:
                char_code = ord(char)
                if char_code >= len(decode_map):
                    return None

                decoded_value = decode_map[char_code]
                if decoded_value == -1:
                    return None

                if need_bits > 5:
                    bits = (bits << 5) + decoded_value
                    need_bits -= 5
                else:
                    bits_left = 5 - need_bits
                    bits = (bits << need_bits) + (decoded_value >> bits_left)

                    if yielded_bytes < len(buffer):
                        buffer[yielded_bytes] = bits & 0xFF
                        yielded_bytes += 1

                    bits = ((decoded_value << (5 - bits_left)) & 0x1F) >> (
                        5 - bits_left
                    )
                    need_bits = 8 - (5 - need_bits)

            return bytes(buffer[:yielded_bytes])
        except Exception:
            return None


@dataclass
class SpoIdComponents:
    """SharePoint Online ID components."""

    site_collection_id: uuid.UUID
    web_id: uuid.UUID
    list_id: uuid.UUID
    unique_id: uuid.UUID
    item_version: Optional[int] = None


@dataclass
class SpoIdComponentsWithTenantId:
    """SharePoint Online ID components with tenant ID."""

    site_collection_id: uuid.UUID
    web_id: uuid.UUID
    list_id: uuid.UUID
    unique_id: uuid.UUID
    tenant_id: uuid.UUID
    item_version: Optional[int] = None


@dataclass
class SubstrateContentIdentifier:
    """Substrate Content Identifier (SCI)."""

    content_domain_id: str
    tenant_id: uuid.UUID
    item_id: str

    @classmethod
    def parse(cls, sci_string: str) -> Optional["SubstrateContentIdentifier"]:
        """Parse a SCI string like 'SPO@tenantId,itemId'."""
        try:
            if "@" not in sci_string or "," not in sci_string:
                return None

            at_index = sci_string.index("@")
            comma_index = sci_string.index(",", at_index)

            content_domain_id = sci_string[:at_index]
            tenant_id_str = sci_string[at_index + 1 : comma_index]
            item_id = sci_string[comma_index + 1 :]

            if not content_domain_id or not item_id:
                return None

            tenant_id = uuid.UUID(tenant_id_str)

            return cls(content_domain_id, tenant_id, item_id)
        except Exception:
            return None

    def __str__(self) -> str:
        return f"{self.content_domain_id}@{self.tenant_id},{self.item_id}"


class VroomIdComponentEncoder:
    """VroomId component encoder/decoder."""

    COMPOSITE_FILE_ID_PREFIX_V1 = "01"
    COMPOSITE_FILE_ID_V1_LENGTH = 34
    COMPOSITE_FILE_ID_PREFIX_V3 = "03"
    COMPOSITE_FILE_ID_V3_LENGTH = 41

    @staticmethod
    def encode_file_id(
        site_id: uuid.UUID,
        web_id: uuid.UUID,
        unique_id: uuid.UUID,
        item_version: Optional[int] = None,
    ) -> str:
        """Encode a file ID."""
        length = 16 + 4 + (4 if item_version is not None else 0)
        file_id_bytes = bytearray(length)

        # Hash of site_id and web_id (first 4 bytes)
        hash_bytes = VroomIdComponentEncoder._compute_hash_from_site_id_web_id(
            site_id, web_id
        )
        file_id_bytes[0:4] = hash_bytes

        # Unique ID guid bytes - use little-endian to match C# implementation
        file_id_bytes[4:20] = unique_id.bytes_le

        # Item version if present
        if item_version is not None:
            file_id_bytes[20:24] = struct.pack("<I", item_version)

        prefix = (
            VroomIdComponentEncoder.COMPOSITE_FILE_ID_PREFIX_V3
            if item_version is not None
            else VroomIdComponentEncoder.COMPOSITE_FILE_ID_PREFIX_V1
        )
        return prefix + Base32Encoder.encode(file_id_bytes)

    @staticmethod
    def decode_file_id(file_id: str) -> Optional[Tuple[uuid.UUID, Optional[int]]]:
        """Decode a file ID to unique_id and optional item_version."""
        if not file_id:
            return None

        is_v1 = file_id.startswith(VroomIdComponentEncoder.COMPOSITE_FILE_ID_PREFIX_V1)
        is_v3 = file_id.startswith(VroomIdComponentEncoder.COMPOSITE_FILE_ID_PREFIX_V3)

        if not is_v1 and not is_v3:
            return None

        expected_length = (
            VroomIdComponentEncoder.COMPOSITE_FILE_ID_V1_LENGTH
            if is_v1
            else VroomIdComponentEncoder.COMPOSITE_FILE_ID_V3_LENGTH
        )
        if len(file_id) != expected_length:
            return None

        # Decode the base32 portion (skip 2-char prefix)
        file_id_bytes = Base32Encoder.decode(file_id, offset=2)
        if not file_id_bytes or len(file_id_bytes) < 20:
            return None

        # Extract unique ID (bytes 4-20) - use little-endian to match C# implementation
        unique_id = uuid.UUID(bytes_le=file_id_bytes[4:20])

        # Extract item version if v3
        item_version = None
        if is_v3 and len(file_id_bytes) >= 24:
            item_version = struct.unpack("<I", file_id_bytes[20:24])[0]

        return unique_id, item_version

    @staticmethod
    def encode_drive_id(
        site_id: uuid.UUID, web_id: uuid.UUID, list_id: uuid.UUID
    ) -> str:
        """Encode a drive ID."""
        drive_id_str = f"{site_id},{web_id},{list_id}"
        drive_id_bytes = drive_id_str.encode("utf-8")
        return Base64UrlEncoder.encode(drive_id_bytes)

    @staticmethod
    def decode_drive_id(
        drive_id: str,
    ) -> Optional[Tuple[uuid.UUID, uuid.UUID, uuid.UUID]]:
        """Decode a drive ID to site_id, web_id, list_id."""
        drive_id_bytes = Base64UrlEncoder.decode(drive_id)
        if not drive_id_bytes:
            return None

        try:
            drive_id_str = drive_id_bytes.decode("utf-8")
            parts = drive_id_str.split(",")
            if len(parts) != 3:
                return None

            site_id = uuid.UUID(parts[0])
            web_id = uuid.UUID(parts[1])
            list_id = uuid.UUID(parts[2])

            return site_id, web_id, list_id
        except Exception:
            return None

    @staticmethod
    def _compute_hash_from_site_id_web_id(
        site_id: uuid.UUID, web_id: uuid.UUID
    ) -> bytes:
        """Compute hash for site ID and web ID GUIDs."""
        # Use little-endian GUID bytes to match C# implementation
        hash_bytes = site_id.bytes_le + web_id.bytes_le
        sha256_hash = hashlib.sha256(hash_bytes).digest()

        # Take first 4 bytes and convert to uint32
        value = 0
        for i in range(4):
            value = (value << 8) | sha256_hash[i]

        return struct.pack("<I", value)


class SpoIdEncoder:
    """SharePoint Online ID encoder/decoder."""

    VROOM_ID_PREFIX = "SPO"
    HISTORICAL_VROOM_ID_PREFIX = "SPV"
    VROOM_ID_SEPARATOR = "_"
    WELL_KNOWN_SPO_CONTENT_DOMAIN = "SPO"

    def encode_vroom_id(self, components: SpoIdComponents) -> str:
        """Encode SPO components to VroomId format."""
        drive_id = VroomIdComponentEncoder.encode_drive_id(
            components.site_collection_id, components.web_id, components.list_id
        )
        file_id = VroomIdComponentEncoder.encode_file_id(
            components.site_collection_id,
            components.web_id,
            components.unique_id,
            components.item_version,
        )

        prefix = (
            self.HISTORICAL_VROOM_ID_PREFIX
            if components.item_version is not None
            else self.VROOM_ID_PREFIX
        )
        return f"{prefix}{self.VROOM_ID_SEPARATOR}{drive_id}{self.VROOM_ID_SEPARATOR}{file_id}"

    def decode_vroom_id(self, vroom_id: str) -> Optional[SpoIdComponents]:
        """Decode VroomId to SPO components."""
        if not vroom_id:
            return None

        parts = vroom_id.split(self.VROOM_ID_SEPARATOR)
        if len(parts) != 3:
            return None

        has_spo_prefix = parts[0] == self.VROOM_ID_PREFIX
        has_spv_prefix = parts[0] == self.HISTORICAL_VROOM_ID_PREFIX

        if not has_spo_prefix and not has_spv_prefix:
            return None

        # Decode drive ID
        drive_result = VroomIdComponentEncoder.decode_drive_id(parts[1])
        if not drive_result:
            return None
        site_id, web_id, list_id = drive_result

        # Decode file ID
        file_result = VroomIdComponentEncoder.decode_file_id(parts[2])
        if not file_result:
            return None
        unique_id, item_version = file_result

        # Validate version consistency
        if (has_spo_prefix and item_version is not None) or (
            has_spv_prefix and item_version is None
        ):
            return None

        return SpoIdComponents(site_id, web_id, list_id, unique_id, item_version)

    def encode_substrate_content_identifier(
        self, components: SpoIdComponents, tenant_id: uuid.UUID
    ) -> SubstrateContentIdentifier:
        """Encode SPO components to SCI format."""
        if components.item_version is not None:
            raise ValueError(
                "SPO IDs with item versions are not supported by SCI encoding"
            )

        # Build item ID (UniqueId, ListId, WebId, SiteId) - use little-endian to match C# implementation
        buffer = bytearray(16 * 4)
        buffer[0:16] = components.unique_id.bytes_le
        buffer[16:32] = components.list_id.bytes_le
        buffer[32:48] = components.web_id.bytes_le
        buffer[48:64] = components.site_collection_id.bytes_le

        item_id = Base64UrlEncoder.encode(buffer)

        return SubstrateContentIdentifier(
            self.WELL_KNOWN_SPO_CONTENT_DOMAIN, tenant_id, item_id
        )

    def decode_substrate_content_identifier(
        self, sci: SubstrateContentIdentifier
    ) -> Optional[SpoIdComponentsWithTenantId]:
        """Decode SCI to SPO components with tenant ID."""
        if sci is None:
            return None
        if sci.content_domain_id.upper() != self.WELL_KNOWN_SPO_CONTENT_DOMAIN:
            return None

        id_bytes = Base64UrlEncoder.decode(sci.item_id)
        if not id_bytes or len(id_bytes) != 16 * 4:
            return None

        # Extract GUIDs in order: UniqueId, ListId, WebId, SiteId - use little-endian to match C# implementation
        unique_id = uuid.UUID(bytes_le=id_bytes[0:16])
        list_id = uuid.UUID(bytes_le=id_bytes[16:32])
        web_id = uuid.UUID(bytes_le=id_bytes[32:48])
        site_id = uuid.UUID(bytes_le=id_bytes[48:64])

        return SpoIdComponentsWithTenantId(
            site_id, web_id, list_id, unique_id, sci.tenant_id
        )


def decode_exchange_identifier(identifier: str) -> Optional[dict]:
    """
    Attempt to decode Exchange/Outlook identifier format.

    Exchange IDs are typically base64url encoded and contain various metadata.
    For EWS/Exchange formats that contain SharePoint data, this will extract
    the tenant ID and other structured information.
    """
    try:
        # Try base64 (not base64url first)
        try:
            decoded_bytes = base64.b64decode(identifier + "==")
        except:
            decoded_bytes = Base64UrlEncoder.decode(identifier)

        if not decoded_bytes or len(decoded_bytes) < 4:
            return None

        result = {
            "format": "Exchange/Outlook Identifier",
            "total_length": len(decoded_bytes),
            "hex_data": decoded_bytes.hex(),
        }

        # Parse Exchange EWS header format
        type_byte = decoded_bytes[0]
        version = decoded_bytes[1]
        size_low = decoded_bytes[2]
        size_high = decoded_bytes[3]
        size = (size_high << 8) | size_low  # Little-endian 16-bit

        result["header"] = {
            "type": f"0x{type_byte:02x}",
            "version": f"0x{version:02x}",
            "size": size,
        }

        # Extract tenant ID string if format matches
        if len(decoded_bytes) >= 4 + size:
            try:
                tenant_string = decoded_bytes[4 : 4 + size].decode("ascii")
                result["tenant_id"] = tenant_string

                # Parse remaining data
                remaining_offset = 4 + size
                remaining_data = decoded_bytes[remaining_offset:]

                if len(remaining_data) >= 2:
                    separator = struct.unpack("<H", remaining_data[0:2])[0]
                    result["separator"] = f"0x{separator:04x}"

                    # The rest contains encoded SharePoint/Exchange data
                    payload_data = remaining_data[2:]
                    result["payload_data"] = {
                        "length": len(payload_data),
                        "hex": payload_data.hex(),
                    }

                    # Look for potential GUID patterns in payload
                    guids = []
                    for i in range(0, len(payload_data) - 15, 1):
                        try:
                            chunk = payload_data[i : i + 16]
                            guid_le = uuid.UUID(bytes_le=chunk)
                            guid_be = uuid.UUID(bytes=chunk)

                            # Only include if not all zeros
                            if not all(b == 0 for b in chunk):
                                guids.append(
                                    {
                                        "offset": remaining_offset + 2 + i,
                                        "le_guid": str(guid_le),
                                        "be_guid": str(guid_be),
                                        "bytes": chunk.hex(),
                                    }
                                )
                        except:
                            continue

                    if guids:
                        result["potential_guids"] = guids

            except UnicodeDecodeError:
                # Fallback to generic parsing
                pass

        # Look for ASCII strings in the whole data
        ascii_strings = []
        current_string = ""
        for byte in decoded_bytes:
            if 32 <= byte <= 126:  # Printable ASCII
                current_string += chr(byte)
            else:
                if len(current_string) >= 4:
                    ascii_strings.append(current_string)
                current_string = ""
        if len(current_string) >= 4:
            ascii_strings.append(current_string)

        if ascii_strings:
            result["ascii_strings"] = ascii_strings

        return result

    except Exception as e:
        return None


def decode_identifier(identifier: str) -> dict:
    """
    Attempt to decode various identifier formats and return information.

    Args:
        identifier: The identifier string to decode

    Returns:
        Dictionary with decoded information and format type
    """
    result = {
        "identifier": identifier,
        "format": "unknown",
        "decoded": False,
        "error": None,
    }

    try:
        # Try SCI format first
        sci = SubstrateContentIdentifier.parse(identifier)
        if sci:
            result["format"] = "SCI (Substrate Content Identifier)"
            result["decoded"] = True
            result["content_domain_id"] = sci.content_domain_id
            result["tenant_id"] = str(sci.tenant_id)
            result["item_id"] = sci.item_id

            # If it's SPO, try to decode further
            if sci.content_domain_id.upper() == "SPO":
                encoder = SpoIdEncoder()
                spo_components = encoder.decode_substrate_content_identifier(sci)
                if spo_components:
                    result["spo_components"] = {
                        "site_collection_id": str(spo_components.site_collection_id),
                        "web_id": str(spo_components.web_id),
                        "list_id": str(spo_components.list_id),
                        "unique_id": str(spo_components.unique_id),
                        "tenant_id": str(spo_components.tenant_id),
                    }
            return result

        # Try VroomId format
        if identifier.startswith(("SPO_", "SPV_")):
            encoder = SpoIdEncoder()
            components = encoder.decode_vroom_id(identifier)
            if components:
                result["format"] = "VroomId"
                result["decoded"] = True
                result["spo_components"] = {
                    "site_collection_id": str(components.site_collection_id),
                    "web_id": str(components.web_id),
                    "list_id": str(components.list_id),
                    "unique_id": str(components.unique_id),
                    "item_version": components.item_version,
                }
                return result

        # Try Exchange identifier format
        exchange_result = decode_exchange_identifier(identifier)
        if exchange_result:
            result.update(exchange_result)
            result["decoded"] = True
            return result

        # Try base64url decoding
        decoded_bytes = Base64UrlEncoder.decode(identifier)
        if decoded_bytes:
            result["format"] = "Base64Url"
            result["decoded"] = True
            result["decoded_bytes_length"] = len(decoded_bytes)
            result["decoded_hex"] = decoded_bytes.hex()

            # Try to interpret as GUIDs if length is multiple of 16
            if len(decoded_bytes) % 16 == 0:
                guid_count = len(decoded_bytes) // 16
                guids = []
                for i in range(guid_count):
                    guid_bytes = decoded_bytes[i * 16 : (i + 1) * 16]
                    guids.append(str(uuid.UUID(bytes=guid_bytes)))
                result["guids"] = guids

            return result

        # If nothing worked, it's unknown
        result["error"] = "Unable to decode identifier in any known format"

    except Exception as e:
        result["error"] = str(e)

    return result

def extract_claims_from_jwt(token: str) -> Optional[dict]:
    try:
        # JWT has 3 parts: header.payload.signature
        token_parts = token.strip().split(".")
        if len(token_parts) != 3:
            return None

        # Decode the payload (middle part)
        payload = token_parts[1]

        # Add padding if needed for base64 decoding
        padding = 4 - len(payload) % 4
        if padding != 4:
            payload += "=" * padding

        decoded_payload = base64.urlsafe_b64decode(payload)
        claims = json.loads(decoded_payload)
        return claims
    except Exception as e:
        logger.debug(f"Failed to extract claims from JWT: {e}")
        return None

def extract_tenant_id_from_token(token: str) -> Optional[str]:
    """Extract tenant ID from the JWT substrate token."""
    try:
        claims = extract_claims_from_jwt(token)
        if not claims:
            return None
    
        # Try common tenant ID claim names
        for claim_name in ["tid", "tenant_id", "tenantid"]:
            if claim_name in claims:
                return claims[claim_name]

        return None
    except Exception as e:
        logger.debug(f"Failed to extract tenant ID from JWT: {e}")
        return None
