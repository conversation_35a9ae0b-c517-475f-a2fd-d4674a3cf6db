"""
Schedules peashooter, automatically starts Q* training  and automatically shuts down the cluster after training.

Based on: https://dev.azure.com/project-argos/Mimco/_git/glass?path=/personal/sebastko/autostart/math_train.chz.py

Usage from the laptop:
```
pychz researcher_train.chz.py -e launch_cluster
```

"""

import datetime
import shlex


def launch_cluster(
    cluster="prod-westus2-19",
    dataset="VertexEdgeLabsV1",
    tools="EnterpriseBrowserTools",
    grader="AcceptsAllWithToolOaiGrader",  # Default to a very fast but not very useful grader
    priority="team-low",
    team=None,
    cpu_controller=True,
    rapid_id=None,
    experiment_name=None,
    prefix="drmini",
    autostart=True,
    seed: int = 42,
    experiment_overrides: str | None = None,
):
    if experiment_name is None:
        experiment_name = f"{prefix}-{datetime.datetime.now():%Y%m%d%H%M%S}"
    if rapid_id is None:
        rapid_id = experiment_name

    chunks = ["python -m qstar.peashooter.run_peashooter"]
    if team:
        chunks.append(f"rapid.cluster.team={team}")
    chunks.append(
        f"""
rapid.cluster.name={cluster}
rapid.cluster.priority={priority}
rapid.rapid_id={rapid_id}
security_profile=msft-orng

n_train_gpus=64
n_sampling_gpus=64

cpu_controller={cpu_controller}
n_cpu_cores_for_controller=8
"""
    )

    if autostart:
        dataset_preset = f"tr_{cluster}_{dataset}_{tools}_{grader}"
        model_preset = f"{cluster}_dr_mini"  # TODO make model configurable

        experiment_overrides_str = (
            f" experiment_overrides={shlex.quote(experiment_overrides)}"
            if experiment_overrides
            else ""
        )

        chunks.append(
            f"""
autostart_file={__file__}
autostart_args="experiment_name={shlex.quote(experiment_name)} seed={seed} dataset_preset={dataset_preset} model_preset={model_preset}{experiment_overrides_str}"
autostart_max_attempts=3

# Local validation doesn't work as of Sept 2025, so skip it.
# See Teams thread in Project Orange (CoreAI only): https://aka.ms/AAxxib4
autostart_skip_validation=True
# Starting from August FI, use the following flag instead:
# autostart_skip_local_test=True
"""
        )

    return "\n".join(chunks) + "\n"


def run_experiment(
    experiment_name, dataset_preset, model_preset, seed=42, experiment_overrides: str | None = None
):
    return f"""
python3 -m qstar.run_experiment
nostrict
name="{experiment_name}"
wandb.wandb_project="ResearcherFT"
seed={seed}
...dataset=HarmonyCompletionDatasetConfig
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
policy.is_multimodal=True
:researcher_msft.presets.datasets:{dataset_preset}
:researcher_msft.presets.models:{model_preset}
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
security_profile=msft-orng
{experiment_overrides if experiment_overrides else ""}
"""


_CMD = run_experiment
