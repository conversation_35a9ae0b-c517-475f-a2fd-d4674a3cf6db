# Metrics for Project Report vertical, used as grader (PRGrader)
# Based on MSRI code/ PR https://dev.azure.com/msasg/WIT%20Engineering/_git/Scripts/pullrequest/5666790?

import json
import math
import os
import re
import traceback
from typing import Any, Dict, Sequence, Set, Union

import blobfile as bf
import chat
import chz
import structlog
from bus_token_completer import BusTokenCompleter, QoSType
from chat import chat
from chat.enums import Role
from chat.render import get_renderer
from harmony_components.actor.data_components.utils import maybe_transform_tether_to_browser_output

# from rouge_score.rouge_scorer import RougeScorer
from qstar.common import types, utils
from qstar.graders import answer_extraction
from qstar.graders import grader as grader_module
from qstar.sample_completers import sample_completer

# import pystache


logger = structlog.stdlib.get_logger(component=__name__, _print=True)

STOP_TOKENS = [[200002]]


def standardize_scores(grader_answer: dict, gt_metrics: dict) -> dict:
    #  compute the inverse z-scores as reward for for the grader answer
    standardized = {}
    try:
        for key, value in grader_answer.items():
            if key in gt_metrics:
                score = (float(value["score"]) - gt_metrics[key]["mean"]) / gt_metrics[key]["stdev"]
                score =  abs(1.0 / score)
                # score takes value 1 t 5. So find the largest value zscore can have w.r.t. to mean to find the normalizing score
                #  find if mean is closer ceil or floor
                ceil_diff = abs(math.ceil(gt_metrics[key]["mean"]) - gt_metrics[key]["mean"])
                floor_diff = abs(math.floor(gt_metrics[key]["mean"]) - gt_metrics[key]["mean"])
                if ceil_diff <= floor_diff:
                    normalizing_factor = abs(math.ceil(gt_metrics[key]["mean"]) - gt_metrics[key]["mean"]) / gt_metrics[key]["stdev"]
                else:
                    normalizing_factor = abs(math.floor(gt_metrics[key]["mean"]) - gt_metrics[key]["mean"]) / gt_metrics[key]["stdev"]

                # normalize standardized score to be less than 1
                standardized[key] = score * normalizing_factor

                if standardized[key] > 1.0:
                    logger.info("Project_Report:standardize_scores:Warning", key=key, original_score=float(value["score"]), mean=gt_metrics[key]["mean"], stdev=gt_metrics[key]["stdev"], standardized_score=standardized[key], score=score, normalizing_factor=normalizing_factor)

    except Exception as e:
        tb = traceback.format_exc()
        logger.info("Project_Report:standardize_scores:Failed", grader_answer=grader_answer, gt_metrics=gt_metrics)
        logger.error("Project_Report:standardize_scores:Error", error=str(e), traceback=tb)

    return standardized


def llm_judgements_to_standardized_reward(grader_answer: dict, gt_metrics: dict) -> dict:
    standardized = standardize_scores(grader_answer, gt_metrics)
    rewards_dict = {}
    # compute rewards as absolute value of inverse of standardized scores
    for key, value in standardized.items():
        rewards_dict[key] = math.log(value)
    return rewards_dict


def sample_is_correct(grader_answer: dict, gt_metrics: dict, threshold: float) -> bool:
    """
    Check if the sample is correct based on log rewards and a threshold.

    Args:
        grader_answer (dict): Raw scores derived from the LLM grader
        threshold (float): This should be a standard deviation multiplier.

    Returns:
        bool: True if the sample zscore is within the threshold of the std dev multiplier
    """
    zscore = []
    llm_scores = {}
    for key, value in grader_answer.items():
        llm_scores[key] = float(value["score"])

    for key, value in grader_answer.items():
        if key in gt_metrics:
            zscore.append(abs((float(value["score"]) - gt_metrics[key]["mean"]) / gt_metrics[key]["stdev"]))
    avg_zscore = sum(zscore) / len(zscore) if zscore else 0.0
    is_correct = avg_zscore <= threshold

    return is_correct


def call_bus(token_completer, messages: list) -> str:
    try:
        # logger.info("Project_Report:call_bus:messages", messages=messages)
        renderer = get_renderer("harmony_v4.0.15_berry_v3_1mil_orion_lpe")
        convo = chat.Conversation(messages=messages)
        tokens = renderer.render_for_completion_multimodal_toklist(convo, role=chat.Role.ASSISTANT)
        results = token_completer.completion(
            [tokens], max_tokens=20480, stop=STOP_TOKENS, temperature=1.0, logprobs=1
        )
        res_tokens = results.choices[0].toklist.spans[0].tokens
        rawText = renderer.decode(res_tokens)
        return rawText
    except Exception as e:
        tb = traceback.format_exc()
        logger.info("Project_Report:Failed_to_call_bus", error=str(e), traceback=tb)
        raise Exception("Project_Report:Failed_to_call_bus")


def extract_json_from_text(in_text: str) -> Dict[str, Any]:
    """
    Extract JSON object from mixed text content.

    This function can handle:
    - JSON wrapped in markdown code blocks (```json ... ```)
    - JSON with special markers like <|im_sep|>, <|fim_suffix|>, etc.
    - Escaped JSON strings
    - JSON mixed with other text content

    Args:
        in_text (str): Mixed text containing JSON data

    Returns:
        Dict[str, Any]: Parsed JSON object, or empty dict if extraction fails

    Example:
        >>> text = '<|im_sep|>analysis<|im_sep|>{"score": 5, "data": "test"}'
        >>> result = extract_json_from_text(text)
        >>> print(result)
        {'score': 5, 'data': 'test'}
    """
    try:
        # Remove special markers first
        cleaned_text = in_text
        markers = [
            "<|im_sep|>analysis<|im_sep|>",
            "<|im_sep|>analysis",
            "<|im_sep|>",
            "<|fim_suffix|>",
            "<|meta_sep|>",
            "<|im_start|>assistant<|meta_sep|>final<|im_sep|>",
            "<|im_sep|>final<|im_sep|>",
        ]

        for marker in markers:
            cleaned_text = cleaned_text.replace(marker, "")

        # Remove markdown code block markers
        cleaned_text = re.sub(r"```json\s*", "", cleaned_text)
        cleaned_text = re.sub(r"\s*```", "", cleaned_text)

        # Remove leading/trailing quotes if present
        cleaned_text = cleaned_text.strip('"')

        # Find the JSON object by counting braces
        start_idx = cleaned_text.find("{")
        if start_idx == -1:
            return {}

        brace_count = 0
        end_idx = start_idx
        in_string = False
        escape_next = False

        # More robust brace counting that handles strings properly
        for i in range(start_idx, len(cleaned_text)):
            char = cleaned_text[i]

            if escape_next:
                escape_next = False
                continue

            if char == "\\":
                escape_next = True
                continue

            if char == '"' and not escape_next:
                in_string = not in_string
                continue

            if not in_string:
                if char == "{":
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i
                        break

        if brace_count != 0:
            # Braces don't match
            return {}

        json_str = cleaned_text[start_idx : end_idx + 1]

        # Clean up whitespace and newlines
        json_str = json_str.strip()

        # Try parsing directly first
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            # If direct parsing fails, try with unicode escape
            try:
                json_str = json_str.encode().decode("unicode_escape")
                return json.loads(json_str)
            except (UnicodeDecodeError, AttributeError, json.JSONDecodeError):
                # If unicode escape fails, try fixing common issues
                # Fix escaped quotes in the middle of strings
                json_str = re.sub(r'\\""', r'\\"', json_str)
                # Fix unescaped quotes in strings
                json_str = re.sub(r'(?<!\\)"(?![,}:\s])', r'\\"', json_str)

                try:
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    pass
        return {}

    except (ValueError, AttributeError) as e:
        logger.info("Project_Report:error_in_extract_json_from_text", in_text=in_text)
        return {}


def build_pr_prompt_from_md(query: str, response: str, promptFilePath: str) -> list:
    try:
        with open(promptFilePath, "r", encoding="utf-8") as file:
            messages = []
            content = file.read()
            content = content.replace("{{query}}", query)
            content = content.replace("{{response}}", response)
            messages.append(chat.Message.system(content))

            messages.append(
                chat.Message.assistant(
                    "# Valid channels: analysis, final. Channel must be included for every message."
                )
            )
            return messages
    except:
        raise Exception("Project_Report:Failed_to_BuildPRPrompt")


def generate_llm_judgements(
    problem: str,
    entity: str,
    answer: str,
    token_completer: BusTokenCompleter,
    pr_grader_prompt: str,
) -> str:
    messages = build_pr_prompt_from_md(problem, answer, pr_grader_prompt)
    grader_answer = call_bus(token_completer, messages)
    grader_answer = extract_json_from_text(grader_answer)
    return grader_answer

# Move this function later to a common utils file
def extract_entity_from_pure_messages(sample: types.SampleWithCompletion) -> str:
    entities = []
    for message in sample.conversation.messages:
        if message.author.role == Role.TOOL:
            message_str = chat.pure_text_message_to_str(message).strip()
            try:
                jmsgs = json.loads(message_str)
                for jmsg in jmsgs:
                    if jmsg not in entities:
                        entities.append(jmsg)
            except:
                raise Exception("Failed to load json: " + chat.pure_text_message_to_str(message))

    return json.dumps(entities, indent=2)


# Move this function later to a common utils file
def extra_entity_from_tether_display_messages(sample: types.SampleWithCompletion) -> str:
    unique_snippets: Set[str] = set()
    for message in sample.conversation.messages:
        if message.author.role == Role.TOOL:
            if message.author.name in [
                "browser_enterprise.search",
                "browser_enterprise.open",
                "browser_enterprise.find",
            ]:
                if isinstance(message.content, chat.TetherBrowsingDisplay):
                    unique_snippets.add(message.content.result)
                elif chat.message_is_pure_text(message):
                    unique_snippets.add(chat.pure_text_message_to_str(message).strip())
    return "\n".join(unique_snippets)


# Move this function later to a common utils file
def extract_entity_from_message(sample: types.SampleWithCompletion) -> str:
    if all(
        chat.message_is_pure_text(message)
        for message in sample.conversation.messages
        if message.author.role == Role.TOOL
    ):
        return extract_entity_from_pure_messages(sample)
    else:
        return extra_entity_from_tether_display_messages(sample)


@chz.chz(typecheck=True)
class ProjectReportGrader(grader_module.Grader):
    """
    Grader that grades according to the Project Report LLM Judgements output
    """

    extractor: answer_extraction.AnswerExtractor[str] = chz.field(
        doc="Answer extractor to use for extracting answers from samples.",
        default_factory=answer_extraction.ChannelExtractor,
    )

    score_threshold: float = chz.field(
        doc="The threshold of grader score. If the score is below this threshold, the response will be considered as incorrect answer",
        default=float("-inf"),
    )

    model: str = chz.field(
        doc="The blobpath of the LLM used by the grader",
        default="az://orngcresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted",
    )

    bus_user: str = chz.field(
        doc="The topic mode or user of the bus",
        default="msft",
    )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> list[types.SampleWithGrade]:

        try:
            answers = self._extract_answers(samples)
            entities: list[str] = [extract_entity_from_message(sample) for sample in samples]
            problems: list[str] = [sample.gt_datapoint.problem for sample in samples]
        except Exception as e:
            tb = traceback.format_exc()
            logger.info(
                "Project_Report:extract_entity_from_message:Failed", error=str(e), traceback=tb
            )

        token_completer = BusTokenCompleter(
            topic_mode_or_user=self.bus_user,
            topic_or_snapshot=self.model,
            bus_line="bus",
            qos_type=QoSType.ROUND_ROBIN_BY_USER,
        )

        try:
            current_file = os.path.abspath(__file__)
            current_dir = os.path.dirname(current_file)
            pr_grader_prompt = os.path.join(current_dir, f"../grader_prompts/pr_score/v0.0.2-pr-grader.md")
            # load gt metrics from json
            gt_metrics_path = os.path.join(current_dir, f"../grader_prompts/pr_score/expert_metric_percentiles.json")
            with open(gt_metrics_path) as f:
                gt_metrics = json.load(f)
            gt_metrics = gt_metrics["oneshot_style_metrics"]

            llm_judgements = [
                generate_llm_judgements(
                    problem,
                    entity,
                    answer,
                    token_completer,
                    pr_grader_prompt,
                )
                for problem, entity, answer in zip(problems, entities, answers)
            ]
            log_rewards_list = [llm_judgements_to_standardized_reward(l_g, gt_metrics) for l_g in llm_judgements]

            try:
                results: list[types.SampleWithGrade] = []
                for sample, given_answer, log_reward, llm_judgement in zip(
                    samples, answers, log_rewards_list, llm_judgements, strict=True
                ):
                    if len(log_reward) > 0:
                        result = sample.with_grade(
                            log_rewards=log_reward,
                            is_correct=sample_is_correct(llm_judgement, gt_metrics, self.score_threshold),
                            given_answer=given_answer,
                            telemetry_metadata={"PR grade": log_reward},
                        )
                        results.append(result)
                    else:
                        continue
                return results

            except Exception as e:
                tb = traceback.format_exc()
                logger.info(
                    "Project_Report:sample.with_grade:Failed",
                    log_rewards_list=log_rewards_list,
                    samples=len(samples),
                    answers=len(answers),
                    traceback=tb,
                )
        except Exception as e:
            tb = traceback.format_exc()
            logger.info("Project_Report:sample.with_grade:Failed", error=str(e), traceback=tb)
            rsts: list[types.SampleWithGrade] = [
                sample.with_grade(
                    log_rewards={"conciseness": float("-inf")},
                    is_correct=False,
                    given_answer=f"Fail to grade sample: {e}\n{tb}",
                    telemetry_metadata={"error": f"Faile to grade sample: {e}\n{tb}"},
                )
                for sample in samples
            ]
            return rsts

    def _extract_answers(self, samples: Sequence[types.SampleWithCompletion]) -> list[str | None]:
        """
        Determines how the samples are converted into gradable answers. RougeGrader uses utils.extract_answer,
        but this method can be overriden in subclasses to modify the behavior
        """
        answers: list[str | None] = []
        for sample in samples:
            assert len(self.channels_for_answer) == 1, "Only one channel for answer is supported"
            extraction_result = self.extractor.extract_answer(
                sample, channel_for_answer=self.channels_for_answer[0]
            )
            if isinstance(extraction_result, answer_extraction.FailedExtraction):
                answer = None
                sample.metadata.update({"extraction_errors": extraction_result.errors})
            else:
                answer = extraction_result.answer
                sample.metadata.update(extraction_result.metadata)
            answers.append(answer)
        return answers
