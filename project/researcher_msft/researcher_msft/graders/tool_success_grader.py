import asyncio
import math
import re
import traceback
from typing import Optional, Sequence

import chz
import structlog
from qstar.common import types, utils
from qstar.common.tools import asyncio_utils
from qstar.graders import answer_extraction
from qstar.graders import grader as grader_module
from qstar.graders.answer_extraction_experimental import (
    BaseConversationAnswerExtractor,
    BaseSampleAnswerExtractor,
    ToolGradingAnswerExtractor,
)
from qstar.sample_completers import sample_completer
from tool_use.browsing import page_contents as pc_lib

logger = structlog.stdlib.get_logger(component=__name__, _print=True)

from dataclasses import dataclass


@dataclass
class ToolSuccessReward:
    reward: float
    request_count: int
    error_count: int


def get_tool_success_reward(given_answer: Optional[str]) -> ToolSuccessReward:
    total_requests = 0
    total_errors = 0

    if given_answer:
        # Match tool invocations
        request_pattern = re.compile(r"role: tool")
        # Match invalid function name errors
        error_pattern = re.compile(
            r"Error parsing function call: Invalid function_name='([a-zA-Z_]\w*)' call"
        )
        total_requests = len(request_pattern.findall(given_answer))
        total_errors = len(error_pattern.findall(given_answer))

        # Avoid division by zero, penalize if no requests were made, clamp reward to [0.0, 1.0]
        reward = 0.0 if total_requests == 0 else max(0.0, 1.0 - total_errors / total_requests)

        return ToolSuccessReward(
            reward=reward,
            request_count=total_requests,
            error_count=total_errors,
        )

    return ToolSuccessReward(
        reward=0.0,
        request_count=total_requests,
        error_count=total_errors,
    )


@chz.chz(typecheck=True)
class ToolSuccessGrader(grader_module.Grader):

    # extractor: BaseConversationAnswerExtractor = ToolGradingAnswerExtractor()

    answer_extractor: (
        BaseConversationAnswerExtractor[str] | BaseSampleAnswerExtractor[str]
    ) = chz.field(
        doc="Answer extractor to use for extracting answers from samples.",
        blueprint_unspecified=ToolGradingAnswerExtractor,
        default_factory=ToolGradingAnswerExtractor,
    )

    score_threshold: float = chz.field(
        doc="The threshold of grader score. If the score is below this threshold, the response will be considered as incorrect answer",
        default=float("-inf"),
    )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> list[types.SampleWithGrade]:
        try:
            given_answers = [given_answer for given_answer in self._extract_answers(samples)]
            tool_success_rewards = [
                get_tool_success_reward(given_answer) for given_answer in given_answers
            ]

            result: list[types.SampleWithGrade] = [
                sample.with_grade(
                    log_rewards={
                        "tool_success_reward": math.log(tool_success_reward.reward)
                        if tool_success_reward.reward != 0
                        else float("-inf"),
                    },
                    # We do not hard stop on tool usage errors, continuous rewards should influence the behaviour.
                    is_correct=True,
                    given_answer="",
                    telemetry_metadata={
                        "error_count": tool_success_reward.error_count,
                        "request_count": tool_success_reward.request_count,
                        "tool_success_reward": tool_success_reward.reward,
                    },
                )
                for sample, given_answer, tool_success_reward in zip(
                    samples, given_answers, tool_success_rewards, strict=True
                )
            ]
            return result
        except Exception as e:
            tb = traceback.format_exc()
            rsts: list[types.SampleWithGrade] = [
                sample.with_grade(
                    log_rewards={"tool_success_reward": float("-inf")},
                    is_correct=False,
                    given_answer=f"Fail to grade sample: {e}\n{tb}",
                    telemetry_metadata={"error": f"Fail to grade sample: {e}\n{tb}"},
                )
                for sample in samples
            ]
            return rsts

    def _extract_answers(self, samples: Sequence[types.SampleWithCompletion]) -> list[str]:
        """
        Determines how the samples are converted into gradable answers. RougeGrader uses utils.extract_answer,
        but this method can be overriden in subclasses to modify the behavior
        """
        answers: list[str | None] = []
        for sample in samples:
            assert len(self.channels_for_answer) == 1, "Only one channel for answer is supported"
            extraction_result = self.answer_extractor.extract_answer_from_conversation(
                sample.conversation,
                sample.conversation,
            )
            if isinstance(extraction_result, answer_extraction.FailedExtraction):
                answer = ""
            else:
                answer = extraction_result.answer
            answers.append(answer)
        return answers
