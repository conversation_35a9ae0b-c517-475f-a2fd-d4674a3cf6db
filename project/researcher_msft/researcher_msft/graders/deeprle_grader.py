import json
import math
import os
import traceback
from typing import List, Sequence, Union

import chz
import structlog
from bus_token_completer import BusTokenCompleter, QoSType
from chat import Message, chat
from chat.enums import Role
from chat.render import get_renderer
from pydantic import BaseModel, Field
from qstar.common import types
from qstar.graders import answer_extraction
from qstar.graders import grader as grader_module
from qstar.sample_completers import sample_completer

logger = structlog.stdlib.get_logger(component=__name__, _print=True)

STOP_TOKENS = [[200002]]


def BuildReasoningEvaluationPrompt(
    evaluation_prompt_messages: List[dict],
    query: str,
    user_profile: str,
    hop_sequence: str,
    available_tools: str,
) -> List[Message]:
    """
    Build the reasoning evaluation prompt from information in conversation.
    Return List[Message] as required by chat.Conversation() in call_bus.
    """
    result_messages = []
    for message in evaluation_prompt_messages:
        message_content = message["content"]
        message_content = message_content.replace("{{{query}}}", query)
        message_content = message_content.replace("{{{user_profile}}}", user_profile)
        message_content = message_content.replace("{{{hop_sequence_1}}}", hop_sequence)
        message_content = message_content.replace("{{{available_tools}}}", available_tools)
        if message["role"] == "system":
            result_messages.append(chat.Message.system(message_content))
        elif message["role"] == "assistant":
            result_messages.append(chat.Message.assistant(message_content))
        elif message["role"] == "user":
            result_messages.append(chat.Message.user(message_content))

    result_messages.append(
        chat.Message.assistant(
            "# Valid channels: analysis, final. Channel must be included for every message."
        )
    )
    return result_messages


# New model structure matching the current prompt output
class RequestDecomposition(BaseModel):
    information_need: str = Field(
        ..., description="Plain language restatement of the user's information need"
    )
    time_or_recency_components: str = Field(
        ..., description="Any explicit or implicit time or recency requirements"
    )
    user_instructions: str = Field(
        ..., description="Explicit or implicit user instructions identified"
    )
    key_terms_and_placeholders: List[str] = Field(
        ..., description="List of key terms and placeholders"
    )
    sub_tasks: List[str] = Field(..., description="List of sub-tasks or sub-questions")
    dependencies: str = Field(..., description="Description of dependencies between sub-tasks")
    recommended_tools: str = Field(
        ..., description="Which tools should be called and how to parameterize them"
    )


class HopByHopAnalysis(BaseModel):
    hop_index: Union[int, str] = Field(..., description="The index of the hop in the sequence")
    tool_invocations: str = Field(..., description="Description of tool calls in this hop")
    results_quality: str = Field(..., description="Assessment of results obtained")
    criteria_evaluation: Union[str, List[str]] = Field(
        ..., description="Which criteria are most relevant and how well they were met"
    )
    issues_identified: str = Field(..., description="Any problems or missed opportunities")


class StepTwoAssessment(BaseModel):
    hop_by_hop_analysis: List[HopByHopAnalysis] = Field(..., description="Analysis of each hop")
    overall_score: Union[int, str] = Field(
        ..., description="The score assigned based on the grading scale (0-5)"
    )
    score_justification: str = Field(
        ..., description="Explanation for the assigned score based on grading scale"
    )
    assessment_summary: str = Field(..., description="Overall assessment citing relevant criteria")
    improvement_recommendations: str = Field(
        ..., description="Specific suggestions to improve the JRL's actions"
    )


class CurrentReasoningEvaluation(BaseModel):
    """Updated model that matches the current prompt output format"""

    step_1_request_decomposition: RequestDecomposition = Field(
        ..., description="Request decomposition analysis"
    )
    step_2_assessment: StepTwoAssessment = Field(
        ..., description="Assessment of the reasoning process"
    )


# TODO: move to a common util so all graders can use it.
def call_bus(token_completer, messages: list) -> str:
    renderer = get_renderer("harmony_v4.0.15_berry_v3_1mil_orion_lpe")
    convo = chat.Conversation(messages=messages)
    tokens = renderer.render_for_completion_multimodal_toklist(convo, role=chat.Role.ASSISTANT)
    # TODO: max_tokens/temperature/logprobs should be configurable. Currently just inherit what DeepgroundLeoGrader has.
    results = token_completer.completion(
        [tokens], max_tokens=20480, stop=STOP_TOKENS, temperature=1.0, logprobs=1
    )
    res_tokens = results.choices[0].toklist.spans[0].tokens
    rawText = renderer.decode(res_tokens)

    return rawText


@chz.chz(typecheck=True)
class DeepRLEGrader(grader_module.Grader):
    """
    Grader based on Deep-RLE (Reasoning Logic Evaluation)
    """

    extractor: answer_extraction.AnswerExtractor[str] = chz.field(
        doc="Answer extractor to use for extracting answers from samples.",
        default_factory=answer_extraction.ChannelExtractor,
    )

    evaluation_prompt_name: str = chz.field(
        doc="The file name of the evaluation prompt.", default="deep_rle_prompt.json"
    )

    model: str = chz.field(
        doc="The blobpath of the LLM used by the grader",
        default="az://orngscuscresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted",
    )

    score_threshold: float = chz.field(
        doc="The threshold of grader score. If the score is below this threshold, the response will be considered as incorrect answer",
        default=float("-inf"),
    )

    bus_user: str = chz.field(
        doc="The topic mode or user of the bus",
        default="msft",
    )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> List[types.SampleWithGrade]:
        try:
            given_answers = self._extract_answers(samples)

            token_completer = BusTokenCompleter(
                topic_mode_or_user=self.bus_user,
                topic_or_snapshot=self.model,
                bus_line="bus",
                qos_type=QoSType.ROUND_ROBIN_BY_USER,
            )

            rle_prompt_messages = self._load_evaluation_prompt(self.evaluation_prompt_name)

            scores: List[float] = []

            for sample in samples:
                # TODO: we assume problem is a string here, but it can also be a conversation. Need to convert if it's a conversation object.
                query = sample.gt_datapoint.problem

                user_profile = self.get_user_profile(sample)

                sample_messages = sample.conversation.messages

                hop_sequence = json.dumps(
                    self._extract_hops(sample_messages), indent=2, default=str
                )

                available_tools = json.dumps(
                    self._get_available_tools(sample_messages), indent=2, default=str
                )

                reasoning_eval_messages = BuildReasoningEvaluationPrompt(
                    rle_prompt_messages, query, user_profile, hop_sequence, available_tools
                )

                evaluation_result = call_bus(token_completer, reasoning_eval_messages)

                # Remove prefix
                prefix_loc = evaluation_result.find("<|im_sep|>")
                if prefix_loc != -1:
                    evaluation_result = evaluation_result[prefix_loc + len("<|im_sep|>") :]

                # Extract answer from raw string
                score = self.get_rle_reward_score(evaluation_result)

                # Get scores
                scores.append(score)

            # Score has to be in the log scale to be passed to log_rewards
            log_score = math.log(score) if score > 0 else float("-inf")
            rsts: list[types.SampleWithGrade] = [
                sample.with_grade(
                    log_rewards={"deeprle_grader": log_score},
                    is_correct=log_score >= math.log(self.score_threshold + 0.000001),
                    given_answer=given_answer,
                    telemetry_metadata={"rsp_score": score},
                )
                for score, given_answer in zip(scores, given_answers)
            ]

            return rsts
        except Exception as e:
            tb = traceback.format_exc()
            rsts: list[types.SampleWithGrade] = [
                sample.with_grade(
                    log_rewards={"deeprle_grader": float("-inf")},
                    is_correct=False,
                    given_answer=f"Fail to grade sample: {e}\n{tb}",
                    telemetry_metadata={"error": f"Faile to grade sample: {e}\n{tb}"},
                )
                for sample in samples
            ]

            return rsts

    def _load_evaluation_prompt(self, prompt_name) -> List[dict]:
        """
        Load the prompt from grader_prompts directory.
        """
        current_dir = os.path.dirname(os.path.abspath(__file__))
        pr_grader_prompt_path = os.path.join(
            current_dir, f"../grader_prompts/deeprle/{prompt_name}"
        )
        with open(pr_grader_prompt_path, "r") as fp:
            return json.load(fp)

    def get_user_profile(self, sample: types.SampleWithCompletion) -> str:
        """
        Get the user profile from the sample.
        """
        if (
            "context" not in sample.gt_datapoint.metadata
            or "PERSON_SELF" not in sample.gt_datapoint.metadata["context"]
        ):
            logger.debug("User profile context or PERSON_SELF not found in metadata")
            return ""
        user_profile = sample.gt_datapoint.metadata["context"]["PERSON_SELF"]
        if isinstance(user_profile, str):
            logger.debug(f"User profile is empty: {user_profile}")
            return user_profile
        elif isinstance(user_profile, list) and len(user_profile) == 1:
            return json.dumps(user_profile[0])
        else:
            logger.debug(f"User profile is not a string or a single-element list: {user_profile}")
            return ""

    def _extract_hops(self, messages: List[chat.Message]) -> list:
        hops = []
        hop_index = 0
        for message in messages:
            hop_index += 1
            hops.append(
                {
                    "hop_index": hop_index,
                    "response": self._get_response_from_message(message),
                    # TODO: see if we can add tool arguments to tool invocation results.
                    "tool_invocation_results": self._get_tool_invocation_from_message(message),
                }
            )
        return hops

    def _get_available_tools(self, messages: List[chat.Message]) -> List[str]:
        """
        Extract available tools from the messages.
        """
        # To change to set
        available_tools = set()
        for message in messages:
            if message.author.role == Role.TOOL:
                available_tools.add(message.author.name)
        # TODO: add tool description if we can find it.
        return list(available_tools)

    def get_rle_reward_score(self, evaluation_result: str) -> float:
        """
        Extract the RLE reward score from the bus response.
        """
        try:
            json_obj = json.loads(evaluation_result)
            reasoning_evaluation = CurrentReasoningEvaluation.model_validate(json_obj)

            scale = reasoning_evaluation.step_2_assessment.overall_score
            if isinstance(scale, str) or isinstance(scale, int) or isinstance(scale, float):
                return float(scale) / 5.0  # scale of [0,5]
            else:
                raise ValueError(f"Invalid score type: {type(scale)}")
        except Exception as e:
            logger.error(f"Failed to extract RLE reward score: {e}")
            return float("-inf")

    def _get_response_from_message(self, message: chat.Message) -> str:
        """
        Extract the result from a message.
        """
        msg_response = ""
        if isinstance(message.content, chat.TetherBrowsingDisplay):
            msg_response = message.content.result if message.content else ""
        if isinstance(message.content.content_type, chat.Text):
            msg_response = chat.pure_text_message_to_str(message).strip()
        if msg_response is None:
            msg_response = ""
        return msg_response

    def _get_tool_invocation_from_message(self, message: chat.Message) -> List:
        """
        Extract tool invocation information from a message.
        """
        if message.author.role == Role.TOOL:
            tool_result = ""
            # TODO: change hardcoding?
            if message.author.name in [
                "enterprise.search",
                "enterprise.open",
                "enterprise.find",
            ]:
                tool_result = self._get_response_from_message(message)
            # Is it possible to have multiple tool invocations in a single message?
            return [
                {
                    # Omit the stage/cot_summary in rsp DeepRLE implementation.
                    # https://msasg.visualstudio.com/WIT%20Engineering/_git/Scripts?path=/rsp/rsp/seval/sydney_responses.py&version=GBmaster&line=299&lineEnd=305&lineStartColumn=2&lineEndColumn=11&lineStyle=plain&_a=contents
                    "tool_name": message.author.name,
                    # TODO: add response
                    "result": tool_result,
                }
            ]
        return []

    # TODO: move this to a common utility function
    def _extract_answers(self, samples: Sequence[types.SampleWithCompletion]) -> list[str | None]:
        """
        Determines how the samples are converted into gradable answers. RougeGrader uses utils.extract_answer,
        but this method can be overriden in subclasses to modify the behavior
        """
        answers: list[str | None] = []
        for sample in samples:
            assert len(self.channels_for_answer) == 1, "Only one channel for answer is supported"
            extraction_result = self.extractor.extract_answer(
                sample, channel_for_answer=self.channels_for_answer[0]
            )
            if isinstance(extraction_result, answer_extraction.FailedExtraction):
                answer = None
                sample.metadata.update({"extraction_errors": extraction_result.errors})
            else:
                answer = extraction_result.answer
                sample.metadata.update(extraction_result.metadata)
            answers.append(answer)
        return answers
