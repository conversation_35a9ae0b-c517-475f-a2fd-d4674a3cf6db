import json
import os
import platform
import subprocess
import sys
import time
import urllib.request
from pathlib import Path

from playwright.sync_api import sync_playwright

BASE_URL = os.environ.get(
    "TARGET_URL", "https://openai-proxy.int.prod-southcentralus-hpe-3.dev.openai.org/"
)
USER_DATA_DIR = os.environ.get(
    "USER_DATA_DIR", str(Path.home() / ".oai_auth_profile")
)  # Persistent user directory
COOKIE_NAME = os.environ.get("COOKIE_NAME", "_oauth2_proxy")
COOKIE_DOMAIN_SUFFIX = os.environ.get(
    "COOKIE_DOMAIN_SUFFIX", ".dev.openai.org"
)  # Your domain suffix
REMOTE_DEBUG_PORT = int(os.environ.get("REMOTE_DEBUG_PORT", "9222"))
WIN_USER_DATA_DIR = os.environ.get("WIN_USER_DATA_DIR", r"%USERPROFILE%\.oai_auth_profile")


def is_wsl():
    if "WSL_DISTRO_NAME" in os.environ or "WSL_INTEROP" in os.environ:
        return True
    try:
        return "microsoft" in platform.uname().release.lower()
    except Exception:
        return False


def _cdp_version_url(port: int) -> str:
    return f"http://localhost:{port}/json/version"


def cdp_ready(port: int) -> bool:
    try:
        with urllib.request.urlopen(_cdp_version_url(port), timeout=0.5) as r:
            data = json.loads(r.read().decode("utf-8"))
        return bool(data.get("webSocketDebuggerUrl"))
    except Exception:
        return False


def ensure_windows_browser_started(port: int, win_user_data_dir: str):
    """
    If CDP port is not open, start Edge on Windows side with remote debugging enabled.
    (Start via cmd.exe; Edge only.)
    """
    if cdp_ready(port):
        return

    # --- use cmd.exe to start Edge with your fixed path (robust: pass args list; non-empty title) ---
    edge_dir = r"C:\Program Files (x86)\Microsoft\Edge\Application"
    args = [
        "cmd.exe", "/c", "start", "",           # <-- 非空标题，避免被吞
        "/D", edge_dir,
        "msedge.exe",
        f"--remote-debugging-port={port}",
        "--remote-debugging-address=0.0.0.0",
        f'--user-data-dir={win_user_data_dir}',
        "about:blank",
    ]

    subprocess.Popen(
        args,
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL,
        start_new_session=True,
    )

    # 等端口就绪（保持你的原始等待逻辑）
    deadline = time.time() + 25
    while time.time() < deadline:
        if cdp_ready(port):
            return
        time.sleep(0.4)
    raise RuntimeError("Failed to start Windows browser within expected time (CDP port not ready).")



def pick_cookie(cookies):
    # Pick from all cookies: name matches and domain falls under .dev.openai.org (you can modify as needed)
    # Prioritize by latest expiration time/creation time
    candidates = [
        c
        for c in cookies
        if c.get("name") == COOKIE_NAME and c.get("domain", "").endswith(COOKIE_DOMAIN_SUFFIX)
    ]
    if not candidates:
        return None
    # Select the one with the largest expires value
    candidates.sort(key=lambda c: c.get("expires") or 0, reverse=True)
    return candidates[0]


def run_with_windows_browser():
    ensure_windows_browser_started(REMOTE_DEBUG_PORT, WIN_USER_DATA_DIR)
    with sync_playwright() as p:
        browser = p.chromium.connect_over_cdp(f"http://localhost:{REMOTE_DEBUG_PORT}")
        # Connect to the default context of the persistent profile
        context = browser.contexts[0] if browser.contexts else browser.new_context()
        page = context.new_page()

        page.goto(BASE_URL, wait_until="networkidle")
        print(
            "[info] Windows browser is open, please complete login/MFA in that window. Return to this terminal and press Enter to continue...",
            file=sys.stderr,
        )
        try:
            input()
        except KeyboardInterrupt:
            return

        page.goto(BASE_URL, wait_until="networkidle")

        target = pick_cookie(context.cookies())
        if not target:
            print(
                "[error] Target cookie not found (possibly not logged in or domain mismatch).",
                file=sys.stderr,
            )
            sys.exit(2)
        print(f"{target['name']}={target['value']}")


def main():
    if not is_wsl():
        with sync_playwright() as p:
            context = p.chromium.launch_persistent_context(
                USER_DATA_DIR,
                headless=False,  # For first login, recommend --login (with head)
                args=[],
                ignore_https_errors=True,  # Ignore SSL certificate errors
            )
            page = context.new_page()

            # Visit target domain, will automatically 302 redirect to login; first run you manually complete login and MFA
            page.goto(BASE_URL, wait_until="networkidle")

            # If in login mode, give enough time to complete MFA/consent etc. (can be adjusted as needed/continue on prompt)
            print(
                f"[info] Browser opened, please complete login. Press Enter after completion to continue capturing Cookie...",
                file=sys.stderr,
            )
            try:
                input()
            except KeyboardInterrupt:
                context.close()
                return

            # Refresh again to ensure proxy writes site cookies properly
            page.goto(BASE_URL, wait_until="networkidle")

            # Read cookies
            cookies = context.cookies()  # Cookies from all domains
            target = pick_cookie(cookies)
            context.close()

            if not target:
                print(
                    "[error] Target cookie not found (may not be logged in yet, cookie domain mismatch, or proxy hasn't set cookie).",
                    file=sys.stderr,
                )
                sys.exit(2)

            # Print cookie name and value in the format: <cookie_name>=<cookie_value>
            print(f"{target.get('name')}={target.get('value')}")
    else:
        run_with_windows_browser()


if __name__ == "__main__":
    main()
