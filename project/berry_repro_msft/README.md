# `berry_repro_msft`

Microsoft's berry RL recipe (starting from a mid-trained checkpoint).

### Setup
As of 8/27/2025, use the following `torchflow-mirror` branch together with this project: `weijianxu/july_fi`

### Project Structure:
```/train```
Train scripts, only keep training presets here. To train a job, run ```bash train_*.sh```

```/eval```
Eval scripts, only keep peaval presets here. To eval a job, run ```bash eval_*.sh```

```/data```
All the magic happens here. Processing pipelines and dedicate graders. Put each task under a folder.
