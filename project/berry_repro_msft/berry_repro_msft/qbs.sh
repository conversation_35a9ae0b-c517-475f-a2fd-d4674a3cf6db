#!/bin/bash

: <<'COMMENT'

Useful for debugging datasets/graders OR for sampling rollouts.

Can be executed from a CPU-only devbox against a bus.

If you have snowflake configured, the samples will appear at gosb
(https://gosb.int.prod-uksouth-7.dev.openai.org/) after a couple of minutes.

Sam<PERSON> command to create a CPU-only devbox:
```
OAIPKG_OVERRIDE_WHEEL=unsafe_skip \
  twdev create-ray-devbox \
    num_pods=1 \
    num_cpu=4 \
    num_gpu=0 \
    job_name=cpu-uks8 \
    team=<TODO> \
    cluster=<TODO> \
    priority_class=<TODO>
```
COMMENT

# GPT5-mini low-priority bus
# See: https://teams.microsoft.com/l/message/19:aeeeab91f87844088cd0d9bfc08d1c09@thread.tacv2/1756241510804?tenantId=72f988bf-86f1-41af-91ab-2d7cd011db47&groupId=d116f381-35ae-474f-a067-e912496a1c0c&parentMessageId=1756241510804&teamName=Project%20Orange%20Community&channelName=General%20Discussions&createdTime=1756241510804
BUS_TOPIC="bus:snap:orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted:user:swe-main-run"

# Check if bus is up
bup engines -t $BUS_TOPIC

# Some of the SWEAgent code requires existence of this dir for writing logs.
mkdir -p /var/log/supervisor/

ORNGCRESCO=orngscuscresco

MAX_TOKENS=65536
INVERSE_TOKEN_COST=32768

# Uncomment if you want to attach a debugger
#DEBUG="-m debugpy --wait-for-client --listen 0.0.0.0:5678"
DEBUG=""

CMD=(
python $DEBUG -m qstar.bin.sample

experiment_name=${OPENAI_USER}-qbs-$(date +%Y%m%d%H%M%S)-all

# For quick testing and easy one-thread debugging:
sampling_concurrency=1
samples_per_instance=1
...max_n_datapoints=1 # One datapoint from each dataset

# Bus to sample policy completions from:
token_completer=bus_token_completer:BusTokenCompleter.Config
token_completer.topic_or_snapshot="$BUS_TOPIC"

# Model configs (keep consistent with training/evaluation)
defaults.n_ctx=$MAX_TOKENS
defaults.sampler.harmony_constrained_sampling=True
defaults.inverse_token_cost=$INVERSE_TOKEN_COST
...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe
...harmony_constrained_sampling=True
...max_num_yields=100

# Use either training or evaluation presets..
:berry_repro_msft.train.presets:tr_math
:berry_repro_msft.train.presets:tr_code
:berry_repro_msft.train.presets:tr_progcomp
:berry_repro_msft.train.presets:tr_stem
:berry_repro_msft.train.presets:tr_if
:berry_repro_msft.eval.presets:ev_genai_index
...dataset_container=$ORNGCRESCO

# Logging and misc
security_profile=msft-orng

# Either configure Snowflake or enable dry_run to prevent kafka errors:
# dry_run=True
)

"${CMD[@]}" 2>&1 | tee qbs2.log