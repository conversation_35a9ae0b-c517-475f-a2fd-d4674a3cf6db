from typing import Iterator, Dict, Any, Sequence
import hashlib

def get_passrate(dp: dict[str, Any]) -> float:
    """Returns the passrate of a datapoint."""
    turns = dp.get("chat_context", {}).get("turns", [])
    for turn in turns:
        if turn.get("type") == "Human":
            annotations = turn.get("annotations", [])
            for ann in annotations:
                if ann.get("type") == "PassRateAnnotation":
                    return ann.get("pass_rate", 1.0)
    return 1.0  # Default pass rate if no annotation found

def get_category(dp: dict[str, Any]) -> str:
    """Returns the category of a datapoint."""
    turns = dp.get("chat_context", {}).get("turns", [])
    for turn in turns:
        if turn.get("type") == "Human":
            annotations = turn.get("annotations", [])
            for ann in annotations:
                if ann.get("type") == "GroundTruthAnnotation":
                    return ann.get("category", "<unknown>")
    return "<unknown>"  # Default category if no annotation found

def get_id(dp: dict[str, Any]) -> str:
    return dp.get("sample_metadata", {}).get("context_idx", "-1")

def passrate_below(
    dp: dict[str, Any],
    passrate: float = 0.5,
) -> bool:
    """Returns whether a datapoint's passrate is below the given threshold."""
    return get_passrate(dp) < passrate

def convert_mai_to_mathgen(dp: dict[str, Any]) -> Sequence[dict[str, Any]]:
    result = {
        "problem": None,
        "answer": None,
        "solution": None
    }

    turns = dp.get("chat_context", {}).get("turns", [])
    for turn in turns:
        if turn.get("type") == "Human":
            result["problem"] = turn.get("message")
            annotations = turn.get("annotations", [])
            for ann in annotations:
                if ann.get("type") == "MathBmAnnotation":
                    result["answer"] = ann.get("groundtruth")
                    result["solution"] = ann.get("solution") or ann.get("groundtruth")
                    break
            break

    return [result]

def convert_mai_to_mathgen_passrate_0_5(dp: dict[str, Any]) -> Sequence[dict[str, Any]]:
    """Convert a MAI datapoint to a MathGen format, filtering out those with passrate not below 0.5."""
    if not passrate_below(dp, 0.5):
        return []
    else:
        return convert_mai_to_mathgen(dp)  
    
def convert_mai_to_taskgen(dp: dict[str, Any]) -> Sequence[dict[str, Any]]:
    mai_data = {}
    turns = dp.get("chat_context", {}).get("turns", [])
    for turn in turns:
        if turn.get("type") == "Human":
            mai_data["problem"] = turn.get("message")
            annotations = turn.get("annotations", [])
            for ann in annotations:
                if ann.get("type") == "GroundTruthAnnotation":
                    mai_data["answer"] = ann.get("groundtruth")
                    mai_data["solution"] = ann.get("solution") or ann.get("groundtruth")
                    break
                elif ann.get("type") == "MultipleChoiceAnnotation":
                    # Note: Different from the some multiple choice benchmarks like GPQA-Diamond,
                    #       we did not include a specific prompt to ask the model to output letter.
                    #       See convert_gpqa_to_mathgen() for reference.
                    mai_data["answer"] = ann.get("correct_key")
                    mai_data["solution"] = ann.get("groundtruth")
                    break
            break

    # FIXME: Check the jsonl for such missing fields
    if "problem" not in mai_data or "answer" not in mai_data or "solution" not in mai_data:
        return []
    
    question_text = mai_data["problem"]
    question_category = get_category(dp)
    question_id = get_id(dp)
    answer_text = mai_data["answer"]

    question_hash = hashlib.md5(question_text.encode("utf-8", "replace")).hexdigest()
    return [dict(
        source_id=("mai", question_category, question_id, question_hash),
        problem=question_text,
        answer=answer_text,
        solution=mai_data["solution"],
        subject=question_category,
        metadata={}
    )]

