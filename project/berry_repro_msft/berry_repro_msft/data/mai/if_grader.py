import json
from abc import abstractmethod
from typing import Any, Sequence, final

import structlog

import chz
from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)
from caas_swe_bench_train_v2.cotograders import _JsonCotograder

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz(typecheck=True)
class IFGrader(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=32)
    reward_name: str = "if_grader"

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(conversation, self.renderer, token_limit=100)

        if_following_rubric = IF_PROMPT.replace("{rubric}", sample.gt_datapoint.answer)
        return if_following_rubric.replace("{conversation_messages}", conversation_messages)

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        return GradeFnOutput(
            correct=all(f"criteria{i+1}" in j and j[f"criteria{i+1}"]["satisfied"] is True for i in range(len(j.keys()))),
            extra_metadata={"grader": j},
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]


def _truncate_string(
        renderer,
        string: str,
        token_limit: int = 512,
        truncate_behavior: str = "middle",
) -> str:
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
                renderer.decode(toks[: token_limit // 2])
                + "...(truncated)..."
                + renderer.decode(toks[-token_limit // 2:])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"


def _extract_model_actions_with_token_limit(convo, renderer=None, token_limit: int = 512,
                                            tool_truncation_rate: float = 0.5):
    """Extract model actions with token-based truncation"""
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            cur_token_limit = token_limit
        else:
            cur_token_limit = int(token_limit * tool_truncation_rate)

        content_str = str(message.content)

        # If renderer available, use token-based truncation
        if renderer:
            truncated_content = _truncate_string(renderer, content_str, token_limit=cur_token_limit)
        else:
            # Fallback to character-based truncation
            truncated_content = content_str[:cur_token_limit]

        convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {truncated_content}\n"

    return convo_messages


def _extract_model_actions(convo):
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {str(message.content)[:512]}\n"

    return convo_messages


IF_PROMPT = """
You are given a model solution. Your task is to verify whether model completed the task as expected in the rubric. The model messages may be truncated for efficiency and focus on the action that the model took to complete the task, e.g., function calls. 
You should not consider the system prompt as a part of the model solution.

## Criteria

{rubric}

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1", "criteria2", , "criteria3", ... Example is shown below. 
The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification. 
Example:
{
   "criteria1": {
      "satisfied": true,
      "explanation": "The model install dependencies correctly or no dependencies need to be installed."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model run its own tests."
   }
   ...
}
""".strip()