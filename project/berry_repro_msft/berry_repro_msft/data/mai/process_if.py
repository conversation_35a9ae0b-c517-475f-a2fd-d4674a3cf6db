#!/usr/bin/env python3
"""
Script to format MAI IF dataset files.
"""

import json
import argparse
from tqdm import tqdm


def process_data(input_file, output_file):
    num_processed = 0
    with open(input_file) as f, open(output_file, "w") as outfile:
        for line in tqdm(f):
            data = json.loads(line.strip())
            problem = data['chat_context']['turns'][0]['message']
            if data['chat_context']['turns'][0]['annotations'][0]['name']=='response_quality_rubric':
                rubric = [f'{i}: ' + r['annotation'] for i, r in enumerate(data['chat_context']['turns'][0]['annotations'][0]['if_annotations'], start=1)]
                sample = {'problem': problem, 'answer': '\n'.join(rubric)}
                outfile.write(json.dumps(sample) + '\n')
                num_processed += 1
            else:
                print(f"No rubrics found, skipping")
    print("Processed {} samples".format(num_processed))


def main():
    """Main function to parse arguments and run the script."""
    parser = argparse.ArgumentParser(
        description="Format MAI IF dataset JSONL file",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    parser.add_argument(
        "--input",
        type=str,
        required=True,
        help="Path to the input JSONL file to process",
    )

    parser.add_argument(
        "--output",
        type=str,
        required=True,
        help="Path to the output JSONL file to process",
    )

    args = parser.parse_args()
    process_data(args.input, args.output)


if __name__ == "__main__":
    main()
