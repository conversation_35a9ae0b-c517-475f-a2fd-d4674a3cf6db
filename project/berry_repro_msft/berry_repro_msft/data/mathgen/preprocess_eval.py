import json
from datasets import load_dataset

if __name__ == "__main__":
    # ds = load_dataset("Maxwell-Jia/AIME_2024")
    # output_file = "aime_2024.jsonl"
    #
    # with open(output_file, "w") as f:
    #     for i in range(len(ds["train"])):
    #         item = ds["train"][i]
    #         problem = item["Problem"]
    #         answer = item["Answer"]
    #         solution = item["Solution"]
    #         metadata = {'id': item['ID']}
    #         sample = {'problem': problem, 'answer': answer, 'solution': solution, 'metadata': metadata}
    #         f.write(f"{json.dumps(sample)}\n")
    #

    # ds = load_dataset("MathArena/aime_2025")
    # output_file = "aime_2025.jsonl"
    #
    # with open(output_file, "w") as f:
    #     for i in range(len(ds["train"])):
    #         item = ds["train"][i]
    #         problem = item["problem"]
    #         answer = item["answer"]
    #         metadata = {'id': item['problem_idx'], 'problem_type': item['problem_type']}
    #         sample = {'problem': problem, 'answer': answer, 'metadata': metadata}
    #         f.write(f"{json.dumps(sample)}\n")

    ds = load_dataset("Idavidrein/gpqa", "gpqa_diamond")
    output_file = "gpqa_diamond.jsonl"

    with open(output_file, "w") as f:
        for i in range(len(ds["train"])):
            item = ds["train"][i]
            problem = item["Question"]
            solution = item["Explanation"]
            answer = item["Correct Answer"]
            metadata = {'id': item['Record ID'], 'problem_type': item['Subdomain']}
            sample = {'problem': problem, 'solution': solution, 'answer': answer, 'metadata': metadata}
            f.write(f"{json.dumps(sample)}\n")