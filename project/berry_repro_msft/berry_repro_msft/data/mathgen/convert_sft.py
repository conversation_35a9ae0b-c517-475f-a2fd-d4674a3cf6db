import json
import regex as re

if __name__ == "__main__":

    input_jsonl = 'train_sft.jsonl'
    output_jsonl = 'aime_1983_2023.jsonl'

    with open(input_jsonl) as f1, open(output_jsonl, 'w') as f2:
        for line in f1:
            data = json.loads(line.strip())
            metadata = {'id': data['id']}
            metadata.update(data['metadata'])
            message = data['messages'][0]
            problem = message[0]['content']
            solution = message[1]['content']
            match = re.search(r'boxed{(.+)}', solution)
            answer = match[-1]
            psa = {'problem': problem, 'solution':solution, 'answer': answer, 'metadata': metadata}
            f2.write(json.dumps(psa)+'\n')