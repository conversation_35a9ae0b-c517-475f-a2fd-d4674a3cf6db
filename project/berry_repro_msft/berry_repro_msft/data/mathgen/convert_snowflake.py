"""
Extracts **correct** samples from a list of berry experiments
(should work with qbs, run_eval, run_experiment -- even if still in flight)
"""

import chz
from mini.finetune.datasets.jsonl import write_jsonl
from rlsnow import reader
from rlsnow.reader_impl import const


def main(
        experiment_id: str,
        output_path: str,
        security_profile: str,
) -> None:
    SQL_INSTRUCT = f"""
      USE DATABASE RLSNOW_Q;
        WITH expt_samples AS (
            SELECT * 
            FROM public.sample_events 
            WHERE base_experiment_id = '{experiment_id}'
        ),
        aggregated AS (
            SELECT 
                data:dp_uid AS dp_uid,
                AVG(data:accuracy::float) AS avg_accuracy
            FROM expt_samples
            GROUP BY data:dp_uid
            HAVING AVG(data:accuracy::float) > 0.0 
        ),
        ranked AS (
            SELECT 
                a.data,
                ROW_NUMBER() OVER (PARTITION BY data:dp_uid ORDER BY a.sample_id) AS rn
            FROM expt_samples a
            JOIN aggregated ag ON a.data:dp_uid = ag.dp_uid and a.event_type = 'driver_upload_sample_batch'
        )

        SELECT *
        FROM ranked
        WHERE rn = 1
    """

    r = reader.RLSnowReader(security_profile)
    samples = r.execute(SQL_INSTRUCT, parse_json_fields=["gt_datapoint_serializable"])
    converted_samples = [dict(problem=s['problem'], answer=s['answer']) for s in samples]
    write_jsonl(output_path, converted_samples)
    print(f"Success: saved {len(samples)} samples to {output_path}")


if __name__ == "__main__":
    chz.entrypoint(main)