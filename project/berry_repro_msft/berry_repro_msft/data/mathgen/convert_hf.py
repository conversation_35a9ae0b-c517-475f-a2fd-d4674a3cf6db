import json
import regex as re
from datasets import load_dataset

if __name__ == "__main__":
    # ds = load_dataset("open-r1/DAPO-Math-17k-Processed", "all")
    # output_file = "dapo_math_17k.jsonl"
    #
    # with open(output_file, "w") as f:
    #     for i in range(len(ds["train"])):
    #         item = ds["train"][i]
    #         problem = item["prompt"]
    #         answer = item["solution"]
    #         metadata = {'id': item['extra_info']['index'], 'problem_type': item['ability']}
    #         sample = {'problem': problem, 'answer': answer, 'metadata': metadata}
    #         f.write(f"{json.dumps(sample)}\n")

    ds = load_dataset("DeepStudentLlama/AoPS-Instruct", "default")
    output_file = "aops_instruct_filtered.jsonl"
    with open(output_file, "w") as f:
        for i in range(len(ds["train"])):
            item = ds["train"][i]
            problem = item['messages'][0]["content"]
            solution = item['messages'][1]["content"]
            match = re.search(r'boxed{(.+)}', solution)
            if match:
                answer = match[-1]
                sample = {'problem': problem, 'answer': answer}
                f.write(f"{json.dumps(sample)}\n")