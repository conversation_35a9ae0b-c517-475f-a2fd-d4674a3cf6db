CKPT="az://orngscuscresco/models/snapshots/4o-tt-v5-transferred-2025-04-09-decrypted"

CMD=(
oaipkg run qstar.run_eval
nostrict
name=$(date +%Y%m%d%H%M%S)-4o-filter-peaval

auto_inherit_training_args=False
load.restore_from_all_clusters=False

:berry_models.scallion:d64_80g
eval_settings.checkpoint_path="$CKPT"
policy.initial_checkpoint="$CKPT"
policy.is_multimodal=False
defaults.channel_config=None
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget
...n_ctx=65536

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=8
peashooter.num_sampling_processes=16

policy.encoding_name=orion_200k
eval_settings.eval_initial_policy=True

:presets:ev_grader_service
:presets:ev_math
...dataset_container=orngscuscresco

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=berry_repro
kafka_enable=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log