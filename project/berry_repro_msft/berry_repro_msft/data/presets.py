import os
import berry.preset_utils

ORNGCRESCO = os.getenv("ORNGCRESCO", "orngcresco")


gpt4o_grader_service = \
    [
        "dataset.grader.renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
        "dataset.grader.grader_service=TokenCompleterGraderService",
        "dataset.grader.grader_service.redis_config_id=autograder-scallion-ppo-engine-defaults",
        "dataset.grader.grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
        f"dataset.grader.grader_service.token_completer.topic_or_snapshot=az://{ORNGCRESCO}/models/snapshots/4o-tt-v5-transferred-2025-04-09-decrypted",
        "dataset.grader.grader_service.token_completer.topic_mode_or_user=4o-grader",
        "dataset.grader.grader_service.token_completer.bus_line=bus",
    ]


gpt5_mini_grader_service = \
    [
        "dataset.grader.renderer_name=harmony_v4.0.16_berry_v3_128k_orion_mm_no_budget_truncate",
        "dataset.grader.grader_service=TokenCompleterGraderService",
        "dataset.grader.grader_service.redis_config_id=autograder-ref-tbv3_cotograder_0202",
        "dataset.grader.grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
        f"dataset.grader.grader_service.token_completer.topic_or_snapshot=az://{ORNGCRESCO}/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted",
        "dataset.grader.grader_service.token_completer.topic_mode_or_user=gpt5-mini-grading",
        "dataset.grader.grader_service.token_completer.bus_line=bus",
        "dataset.grader.grader_service.token_completer.qos_type=bus_token_completer:QoSType.ROUND_ROBIN_BY_POD",
        "dataset.grader.answer_max_excess_characters=None",
        "dataset.grader.answer_length_multiplier=None",
        "dataset.grader.answer_length_limit=None",
        "dataset.grader.grader_max_tokens=32768",
    ]


# Math data filter presets
filter_math = berry.preset_utils.compose_presets(
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=HarmonyCompletionDatasetConfig",
            "dataset.dataset_id=data.xidai.mathgen.aops_instruct.train",
            "dataset.grader=qstar.graders.normalized_sympy_grader:NormalizedSympyGrader",
            "dataset.override_target_samples_per_instance=4",
            *gpt4o_grader_service
        ]
    ),
)


# STEM data presets
filter_stem = berry.preset_utils.compose_presets(
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=HarmonyCompletionDatasetConfig",
            "dataset.dataset_id=data.xidai.taskgen.oss.chemdata",
            "dataset.override_target_samples_per_instance=4",
            "dataset.grader=berry_repro_msft.data.taskgen.se_graders:SETaskgenGrader",
            *gpt5_mini_grader_service
        ]
    ),
)


# IF data presets
filter_if = berry.preset_utils.compose_presets(
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=HarmonyCompletionDatasetConfig",
            "dataset.dataset_id=data.xidai.if.gen5_25k",
            # "dataset.dataset_id=data.xidai.if.d_r_50k",
            # "dataset.dataset_id=data.xidai.if.evam_solutions",
            # "dataset.dataset_id=data.xidai.if.ooak",
            "dataset.override_target_samples_per_instance=4",
            "dataset.grader=qstar.graders.rlhf_grader.berry_grader:BerryGrader",
            "dataset.grader.prompt_template=contains_rubrics",
            "dataset.grader.prompt_constructor.strip_first_model_identity=True",
            "dataset.grader.prompt_constructor.use_rubric_as_side_information=True",
            "dataset.grader.prompt_constructor.rubric_key_in_metadata=gt_answer",
            "dataset.grader.grade_parser=qstar.graders.rlhf_grader.berry_grader.grade_parser:MatchRubricsGradeParser",
            "dataset.grader.grade_parser.is_correct_threshold=0.8",
            "dataset.grader.harmony_renderer=harmony_v4.0.16_berry_v3_128k_orion_mm_no_budget_truncate",
            *gpt5_mini_grader_service[1:8],
        ]
    ),
)
