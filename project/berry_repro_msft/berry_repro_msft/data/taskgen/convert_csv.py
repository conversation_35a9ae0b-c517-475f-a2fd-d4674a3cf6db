import csv
import json


if __name__ == "__main__":
    # file_path = 'o4mini_easy_pass_gt_0.8.csv'
    # file_path = 'o4mini_medium_pass_bt_0.3_0.8.csv'
    # file_path = 'o4mini_hard_pass_lt_0.3.csv'
    # file_path = 'snowflake_mai_if.csv'
    file_path = 'snowflake_mai_if_gen5_25k.csv'

    with open(file_path, 'r', newline='') as csvfile:
        csv_reader = csv.reader(csvfile)
        header = next(csv_reader)
        print(f"Header: {header}")

        # with open("chemdata_o4mini_easy_train.jsonl", "w") as f, open("chemdata_o4mini_easy_test.jsonl", "w") as f2:
        # with open("chemdata_o4mini_medium_train.jsonl", "w") as f, open("chemdata_o4mini_medium_test.jsonl", "w") as f2:
        # with open("chemdata_o4mini_hard_train.jsonl", "w") as f, open("chemdata_o4mini_hard_test.jsonl", "w") as f2:
        # with open("d_r_50k_o4mini_filtered_train.jsonl", "w") as f, open("d_r_50k_o4mini_filtered_test.jsonl", "w") as f2:
        with open("gen5_25k_o4mini_filtered_train.jsonl", "w") as f, open("gen5_25k_o4mini_filtered_test.jsonl", "w") as f2:
            i = 0
            for row in csv_reader:
                data = json.loads(row[0])
                if 'gen5_25k' not in data['dataset_name']:
                    continue
                answer = data["answer"]
                problem = data["problem"]
                sample = {'problem': problem, 'answer': answer}
                if i<500:
                    f2.write(f"{json.dumps(sample)}\n")
                else:
                    f.write(f"{json.dumps(sample)}\n")
                i += 1