## Fixed version of HarmonyToolMCExtractionTask and MultipleChoiceAutoGrader
# Copied from openai/project/qstar/qstar/graders/mc_grader.py
#         and openai/lib/taskgen/taskgen/autograder/tasks/mc_extraction.py

import abc
import copy
import dataclasses
import re
import traceback
from typing import Literal, Sequence, cast

import editdistance
import numpy as np
import structlog
from latex2sympy2 import latex2sympy  # type: ignore

import chz
from chat import chat, render
from chat.render import BaseRenderer
from chat.render.common import render_content
from chat.render.renderer_registry import get_renderer
from message_completer.token_message_completer import is_continuation
from oaicommon.oai_functools import apply_fn_to_unique_only
from qstar.common import harmony, types, utils
from qstar.common.datapoint import Datapoint
from qstar.common.tools import renderer_worker
from qstar.graders import answer_extraction, taskgen_utils
from qstar.graders import grader as grader_module
from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    GraderService,
    HasGraderService,
    make_toolberryish_prompt_from_convo,
)
from qstar.sample_completers import sample_completer
from taskgen.autograder.tasks.mc_extraction import (
    BerryHarmonyMCExtractionTask,
    HarmonyToolMCExtractionTask,
    extract_options_from_problem,
)
from taskgen.autograder.types import (
    CapitalLetter,
    HarmonyTask,
    MCExtractionError,
    MCExtractionInput,
    MCExtractionResponse,
)

from qstar.graders.mc_grader import (
    MCPartialParseResult,
    MCFinishedParseResult,
    extract_options,
    _string_with_the_multiple_choice_options,
)

from qstar.graders.mc_grader import MultipleChoiceAutoGrader

logger = structlog.stdlib.get_logger(__name__)


@chz.chz(typecheck=True)
class FixedHarmonyToolMCExtractionTask(
    HarmonyToolMCExtractionTask
):
    def make_prompt(self, args: MCExtractionInput) -> chat.Conversation:
        letters, options = extract_options_from_problem(args.problem)
        if not len(letters):
            options_text = args.problem
        else:
            options_text = "\n".join(
                [f"({letter}) {option}" for letter, option in zip(letters, options, strict=True)]
            )

        assert isinstance(args.given_answer, str)
        return chat.Conversation(
            messages=[
                chat.Message.system(
                    instructions="Given the following options, determine what the user's answer should be in multiple choice format."
                    " If the user's answer DOES NOT CLEARLY CORRESPOND TO EXACTLY ONE OF THE OPTIONS, respond with 'None'."
                    " If the user's answer CORRESPONDS TO MULITPLE OPTIONS, response with 'None'.",
                    model_identity_desc="You are the multiple-choice parser. You convert free-form answers into a single letter multi-choice selection.",
                    tools_section=self._make_tools_section(options=tuple(letters)),
                ),
                chat.Message.assistant(options_text),
                chat.Message.user(args.given_answer),
                # FIXME (weijianxu): this will have conflict with dummy message in renderer. we disable it for now, and adjust the dummy message in the renderer with the same setting.
                # chat.Message.assistant(
                #     chat.Code(language=chat.Language.JSON, text=""),
                #     recipient="multiple-choice-parser.submit",
                #     status=chat.Status.FINISHED_PARTIAL_COMPLETION,
                # ),
            ]
        )
    

def _HACK_parse_tokens(
    prompt: chat.Conversation, tokens: Sequence[int], renderer: BaseRenderer
) -> list[chat.Message]:
    """
    Implement renderer_worker.parse_tokens but accept the prompt to see if the last message is a partial message/continuation.

    This is need to support HarmonyToolMCExtractionTask, which ends with a partial message to force the tool.
    """
    try:
        # FIXME (weijianxu)
        # Sometimes the tokens are
        # <|meta_start|>json<|im_sep|>{"letter":"D"}<|im_end|><|im_start|><|im_sep|>D<|fim_suffix|>'
        # As no role is provided in second message, it will cause issue
        #    ParseError: ("When includes_msg_start_and_role is True, the header should start with
        # '<|im_start|>assistant', found: header='<|im_start|>' first_part='<|im_start|>'",
        # 'current_token_num=11')

        # Sometimes the tokens are
        # [' <|meta_start|>json<|im_sep|>{"letter":"D"}<|im_end|><|im_sep|><|fim_suffix|>']
        # This is extremely broken

        # Sometimes the tokens are
        # <|meta_start|>json<|im_sep|>{"letter":"D"}<|im_end|><|im_start|>assistant<|im_sep|>D<|fim_suffix|>'
        
        # BTW, for the above tokens that include two messages, 
        # seems we cannot use renderer.parse but must use renderer.parse_multiple
        # otherwise, it will cause
        #    ParseError: ("Expected OutOfTokens but got [token=200006 (self.decode([token])='<|im_start|>')].",
        #    'current_token_num=10')
        
        # Hack: manual decode via regex
        import re
        decoded = renderer.decode(tokens)
        # Extract the JSON from the decoded string
        match = re.search(r'\{(.*?)\}', decoded)
        if match:
            json_str = match.group(0)  # the whole string match
            message = chat.Message(
                role=chat.Role.ASSISTANT,
                content=chat.Code(language=chat.Language.JSON, text=json_str),
            )
            return [message]
        else:
            # If no match, return the decoded string as a message
            message = chat.Message(
                role=chat.Role.ASSISTANT,
                content=chat.SystemError(text="ParseError", name="ParseError"),
                metadata={
                    "parse_error": True,
                    "decoded": decoded,
                }
            )
            return [message]

        # In the code below, the skip_header is important
        # We need to determine if we want to parse the header 
        # to identify the content type
        if renderer.formatter.supports_multi_message_action:
            return renderer.parse_multiple(tokens, skip_header=is_continuation(prompt.messages[-1]))
        else:
            return [
                renderer.parse(
                    tokens,
                    role=chat.Role.ASSISTANT,
                    skip_header=is_continuation(prompt.messages[-1]),
                )
            ]
    except (render.ParseError, UnicodeDecodeError) as e:
        decoded = renderer.decode(tokens)
        message = chat.Message(
            role=chat.Role.ASSISTANT,
            content=chat.SystemError(text=str(e), name="ParseError"),
            metadata={
                "error_trace": traceback.format_exc(),
                "error_message": str(e),
                "parse_error": True,
                "decoded": decoded,
            },
        )
        logger.info("Parse error when decoding tokens", exc_info=True)
        return [message]


@chz.chz(typecheck=True)
class FixedMultipleChoiceAutoGrader(MultipleChoiceAutoGrader):
    def _extract_mc_with_model(
        self,
        partial_parse_results: Sequence[MCPartialParseResult],
        renderer: BaseRenderer,
        grader_service: GraderService,
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> list[MCFinishedParseResult]:
        """
        Extracts answers "A" "B" "C" "D" "E" from free form text using the model.

        Takes a list of free form strings.

        Returns a list of tuples of
        (raw parser response text, extract single letter answers or None if can't extract)
        """
        if len(partial_parse_results) == 0:
            return []

        def make_key(
            partial_parse_result: MCPartialParseResult,
        ) -> tuple[tuple[CapitalLetter, ...], tuple[str, ...], str]:
            letters, options = extract_options(partial_parse_result.sample.gt_datapoint)
            return (tuple(letters), tuple(options), partial_parse_result.current_extracted_answer())

        def request_autograder_opinion(
            partial_parse_results: Sequence[MCPartialParseResult],
        ) -> list[MCExtractionResponse | MCExtractionError]:
            extraction_task: HarmonyTask[
                MCExtractionInput, MCExtractionResponse | MCExtractionError
            ]
            if self.berry_grader_config:
                extraction_task = BerryHarmonyMCExtractionTask()
                # this is much more than you'll need typically
                max_tokens = 4096
            else:
                # extraction_task = HarmonyToolMCExtractionTask()
                # NOTE (weijianxu): Use fixed version in stead
                extraction_task = FixedHarmonyToolMCExtractionTask()
                max_tokens = 600

            prompt_convos = [
                extraction_task.make_prompt(
                    MCExtractionInput(
                        problem=_string_with_the_multiple_choice_options(
                            parse_result.sample.gt_datapoint
                        ),
                        given_answer=parse_result.current_extracted_answer(),
                    )
                )
                for parse_result in partial_parse_results
            ]

            if self.berry_grader_config:
                prompt_convos = [
                    make_toolberryish_prompt_from_convo(
                        prompt_convo,
                        berry_grader_config=self.berry_grader_config,
                        token_budget=max_tokens,
                    )
                    for prompt_convo in prompt_convos
                ]

            # FIXME (weijianxu): Use a hack to get correct prompt
            # prompts = harmony.render_batch_for_completion(prompt_convos, renderer)
            prompts = [
                renderer.render_for_completion_multimodal_toklist(
                    prompt_convo, 
                    role=chat.Role.ASSISTANT,
                    # channel="analysis",  # seems not a must
                    recipient="multiple-choice-parser.submit",
                    # completion_content_type=chat.Code(language=chat.Language.JSON, text=""), # this seems not effective, since it will not add `code` in the end of prompt tokens
                    # end_header=True,  # seems not a must - if True, this will add <|im_sep|>, and the output will start from the json string. Actually if it is set, in the following parsing should be changed, esp we need to skip header in parsing
                ) for prompt_convo in prompt_convos
            ]
            
            logger.info(
                f"Parsing {len(prompts)} multiple choice answers. Harmony renderer name: {renderer.name}."
            )

            responses = grader_service.sample(
                prefixes=prompts,
                batch=[cast(list[int], [])] * len(prompts),
                seed=0,  # TODO: make this configurable
                stop=renderer_worker.get_stop_tokens(renderer.name),
                echo_stop=True,
                deterministic=not self.berry_grader_config,
                max_tokens=max_tokens,
                special_token_allowlist=harmony.get_harmony_special_tokens(renderer),
		        sample_execution_context=sample_execution_context,
            )

            response_messages = [
                _HACK_parse_tokens(prompt_convo, response.tokens, renderer)
                for prompt_convo, response in zip(prompt_convos, responses, strict=True)
            ]
            extraction_results = []
            for messages in response_messages:
                extraction_results.append(extraction_task.process_response(messages))

            return extraction_results

        def convert_autograder_response_to_result(
            partial_parse_result: MCPartialParseResult,
            extraction_result: MCExtractionResponse | MCExtractionError,
        ) -> MCFinishedParseResult:
            match extraction_result:
                case MCExtractionResponse(parsed_answer=letter, raw_text=response_text):
                    return partial_parse_result.finish(
                        new_extracted_answer=letter,
                        source="MultipleChoiceAutoGrader",
                        reason=f"Autograder extracted {letter} from {response_text}",
                    )
                case MCExtractionError(raw_text=response_text, error=error):
                    return partial_parse_result.fail(
                        new_extracted_answer=partial_parse_result.current_extracted_answer(),
                        source="MultipleChoiceAutoGrader",
                        reason=f"{error}: {response_text}",
                    )
                case _:
                    raise RuntimeError("Unexpected autograder response type")

        extracted_autograder_responses = apply_fn_to_unique_only(
            request_autograder_opinion, partial_parse_results, key=make_key
        )

        return [
            convert_autograder_response_to_result(partial_parse_result, autograder_response)
            for partial_parse_result, autograder_response in zip(
                partial_parse_results, extracted_autograder_responses, strict=True
            )
        ]
        