import json
import regex as re
from datasets import load_dataset


def contains_non_english(text):
    return bool(re.search(r'[^\x00-\x7F]', text))


if __name__ == "__main__":
    # ds = load_dataset("avaliev/ChemistryQA")
    # with open("chemistryqa_train.jsonl", "w") as f:
    #     for i in range(len(ds["train"])):
    #         item = ds["train"][i]
    #         problem = item["question"]
    #         answer = item["answer"]
    #         sample = {'problem': problem, 'answer': answer}
    #         f.write(f"{json.dumps(sample)}\n")
    #     for i in range(len(ds["validation"])):
    #         item = ds["validation"][i]
    #         problem = item["question"]
    #         answer = item["answer"]
    #         sample = {'problem': problem, 'answer': answer}
    #         f.write(f"{json.dumps(sample)}\n")
    # with open("chemistryqa_test.jsonl", "w") as f:
    #     for i in range(len(ds["test"])):
    #         item = ds["test"][i]
    #         problem = item["question"]
    #         answer = item["answer"]
    #         sample = {'problem': problem, 'answer': answer}
    #         f.write(f"{json.dumps(sample)}\n")

    # ds = load_dataset("AI4Chem/ChemData700K")
    # with open('chemdata700k.jsonl', 'w') as outfile:
    #     for sample in ds['train']:
    #         problem = sample['instruction'] + ' ' + sample['input']
    #         answer = sample['output']
    #         if contains_non_english(problem) or contains_non_english(answer):
    #             continue
    #         outfile.write('{}\n'.format(json.dumps({'problem': problem, 'answer': answer})))

    ds = load_dataset("cais/hle")
    with open('hle_text_only.jsonl', 'w') as outfile:
        for sample in ds['test']:
            problem = sample['question']
            solution = sample['rationale']
            answer = sample['answer']
            metadata = {'id': sample['id'], 'category': sample['category']}
            if sample['image']:
                continue
            outfile.write('{}\n'.format(json.dumps({'problem': problem, 'solution':solution, 'answer': answer,
                                                    'metadata': metadata})))