# Copied from openai/project/qstar/qstar/graders/taskgen_grader.py
from __future__ import annotations

import textwrap
from typing import Any, Literal, Sequence, assert_never, cast

import structlog

import chz
from berry import grader as grader_module
from berry.sample import GraderError
from chat import chat
from chat.render.common import render_content
from chat.render.renderer_registry import get_renderer
from multimodal_token import TokList
from peashooter import utils_mm
from qstar.common import harmony, types
from qstar.common.tools import constraint_machine_spec, renderer_worker
from qstar.graders import answer_extraction, taskgen_utils
from qstar.graders.taskgen_grader_metadata_types import (
    CategoricalGradeMetadata,
    ExtractionMetadata,
    GraderMetadata,
    LikertGradeMetadata,
)
from qstar.sample_completers import sample_completer
from taskgen.autograder import few_shots
from taskgen.autograder.tasks.criteria_grading import HarmonyCriteriaGradingTask
from taskgen.autograder.tasks.grading import (
    HarmonyCategoricalGradingTask,
    HarmonyConversationGradingTask,
    HarmonyGradingTask,
    LegacyHarmonyGradingTask,
    SampleGradingTask,
)
from taskgen.autograder.types import (
    CategoricalGradingReport,
    ContinuousGradingReport,
    Criteria,
    GraderConversationInput,
    GraderInput,
    GraderInputWithCriteria,
    GraderInputWithSample,
    GradingReport,
    InvalidGradingReport,
)

from qstar.graders.taskgen_grader import TaskgenGrader

Seed = tuple[Any, ...]

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz(typecheck=True)
class SETaskgenGrader(
    TaskgenGrader
):
    # Rename constrained sampling parameters to avoid being overridden by wildcard settings (i.e., `...harmony_constrained_sampling=True`).
    harmony_constrained_sampling_in_grader: bool = chz.field(
        doc="Whether to use constrained sampling for the discriminator.",
        default=False,
    )

    harmony_constrained_sampling_enable_json_in_grader: bool = chz.field(
        doc="Whether to constrain function calls to JSON when using harmony_constrained_sampling_in_grader.",
        default=False,
    )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> list[grader_module.GraderOutput]:
        """
        Grade a batch of samples by:

        * Extracting an answer from each sample.
        * Formatting each extrated answer into a prompt for the grader.
        * Parsing the grader's response, and interpreting it as a reward.
        * Returning a SampleWithGrade for each input sample.
        """
        renderer = get_renderer(self.renderer_name).model_copy(
            update={
                "allow_image_asset_pointer_processing": True,
            }
        )

        outputs: list[grader_module.GraderOutput] = []

        # This loop will serially grade samples one at a time, instead of batching multiple samples together
        # into a single call. In a batch size 1 world, which is true of peashooter, this is fine. However,
        # some scripts may expect to batch multiple samples together.
        if len(samples) > 1:
            logger.warning(
                "TaskgenGrader was simplified for a peashooter world where batch size is 1. If you are encountering "
                "this warning, reach out to @kevinyu to discuss."
            )

        for sample in samples:
            grader_metadata: GraderMetadata
            if not sample.valid:
                outputs.append(
                    grader_module.GraderOutput.with_binary_reward(
                        grader_nickname=self.name,
                        sample_id=sample.sample_id,
                        reward_name="taskgen_grader",
                        is_correct=False,
                    )
                )
                continue

            given_answer, extraction_metadata = self._extract_answer(sample)

            if given_answer is None:
                grader_metadata = {"answer_extraction": extraction_metadata}
                outputs.append(
                    grader_module.GraderOutput.with_binary_reward(
                        grader_nickname=self.name,
                        sample_id=sample.sample_id,
                        reward_name="taskgen_grader",
                        is_correct=False,
                        metadata=grader_metadata,
                    )
                )
                continue

            prompt_convo = self._make_prompt_convo(sample, given_answer)

            prompt_tokens: TokList
            try:
                prompt_tokens = harmony.render_sample(prompt_convo, renderer)
            except Exception as e:
                logger.warning("Autofailing: Failed to render prompt with error", exc_info=True)
                # We treat a prompt that failed to render as an error. Not sure who to blame in this scenario,
                # probably a problem in our system but we treat it as a negative signal to the policy.
                grader_metadata = {
                    "answer_extraction": extraction_metadata,
                    "autograder": {
                        "metadata": {
                            "prompt": chat.Conversation(
                                messages=prompt_convo.messages
                            ).model_dump_json(),
                            "parse_error": True,
                            "grader_errors": [
                                f"Failed to render prompt for grader with error: {e}"
                            ],
                        },
                    },
                }
                outputs.append(
                    grader_module.GraderOutput.with_binary_reward(
                        grader_nickname=self.name,
                        sample_id=sample.sample_id,
                        reward_name="taskgen_grader",
                        is_correct=False,
                        metadata=grader_metadata,
                    )
                )
                continue

            if sample.gt_datapoint.dataset_config.is_multimodal:
                clip_cache_dir = sample.gt_datapoint.dataset_config.get_clip_cache_dir(
                    self.clip_cache_id
                )
                if clip_cache_dir:
                    [prompt_tokens], cache_replacements = utils_mm.load_embeddings_from_cache(
                        [prompt_tokens], cache_path=clip_cache_dir
                    )
                else:
                    cache_replacements = []
                [prompt_tokens], live_replacements = utils_mm.maybe_pre_embed_images(
                    [prompt_tokens],
                    virtual_factory=(
                        None
                        if sample_execution_context is None
                        else sample_execution_context.virtual_factory()
                    ),
                )

                utils_mm.log_embeddings(
                    prefix="[taskgen_grader_embeddings]",
                    clip_cache_dir=clip_cache_dir,
                    cache_replacements=cache_replacements,
                    live_replacements=live_replacements,
                )

            extensions: list[dict[str, Any]] | None = None
            if self.harmony_constrained_sampling_in_grader:
                stop_tokens_utils = renderer_worker.HarmonyStopTokensUtils(
                    harmony_renderer_name=renderer.name,
                    enable_special_stop_token=False,
                )

                valid_tool_calls: tuple[str, ...] = tuple()
                system_message = prompt_convo.messages[0]
                assert isinstance(system_message.content, chat.SystemContent)
                assert system_message.content.channel_config is not None
                channel_config = system_message.content.channel_config
                valid_channels = tuple(sorted(set(channel_config.valid_channels)))
                channel_required = channel_config.channel_required
                valid_response_formats: tuple[str, ...] = tuple()

                extension = constraint_machine_spec.constraint_machine_extension(
                    harmony_renderer_name=renderer.name,
                    encoding_name=renderer.encoding_name,
                    final_token_sequences=stop_tokens_utils.constraint_machine_final_token_sequences(),
                    json_eot_tokens=stop_tokens_utils.constrained_sampling_json_eot_tokens(),
                    tools=valid_tool_calls,
                    channels=valid_channels,
                    none_channel_allowed=not channel_required,
                    response_format_names=valid_response_formats,
                    enable_json_constraining=self.harmony_constrained_sampling_enable_json_in_grader,
                )
                extensions = [extension]

            response = self.grader_service.sample(
                max_tokens=self.grader_max_tokens,
                special_token_allowlist=harmony.get_harmony_special_tokens(renderer),
                prefixes=[prompt_tokens],
                batch=[cast(list[int], [])],
                seed=[[sample.seed]],
                stop=renderer_worker.get_stop_tokens(renderer.name),
                echo_stop=True,
                deterministic=not self.berry_grader_config,
                is_multimodal=True,
                top_p=self.berry_grader_config.grader_top_p if self.berry_grader_config else None,
                extensions=extensions,
            )[0]

            response_messages = renderer_worker.parse_tokens(response.tokens, renderer.name)
            response_messages_as_strings = harmony.render_batch_messages(response_messages)
            response_as_string = "\n---\n".join(response_messages_as_strings)

            sample.metrics.update(
                grader_module.format_grading_compute_metrics(
                    grader=self,
                    grader_model_id=self.grader_service.model_id,
                    prefill_tokens=len(prompt_tokens),
                    sample_tokens=len(response.tokens),
                )
            )

            errors_blamed_on_grader: list[str] = []
            is_correct: bool | None = None
            log_raw_grader_score: float | CategoricalGradeMetadata | LikertGradeMetadata | None = (
                None
            )
            continuous_reward: float | None = None
            if any("parse_error" in msg.metadata for msg in response_messages):
                logger.debug(
                    "Parse error in autograder response",
                    errors=[
                        render_content(msg)
                        for msg in response_messages
                        if "parse_error" in msg.metadata
                    ],
                )
                errors_blamed_on_grader.append(f"Parse error in grader: {response_as_string}")
            else:
                match self.grading_task.process_response(response_messages):
                    case CategoricalGradingReport(threshold=threshold) as grading_report:
                        p_accept = grading_report.get_acceptance_probability()
                        is_correct = p_accept >= threshold
                        log_raw_grader_score = {
                            "p_accept": p_accept,
                            "category_probabilities": grading_report.category_probabilities,
                            "threshold": grading_report.threshold,
                            "acceptable_categories": list(grading_report.acceptable_categories),
                        }
                        if self.use_continuous_reward:
                            continuous_reward = (
                                1.0
                                if threshold <= 0.0
                                else (
                                    0.0
                                    if threshold >= 1.0
                                    else (p_accept - threshold) / (1.0 - threshold)
                                )
                            )
                    case GradingReport(score=likert):
                        log_raw_grader_score = {
                            "likert": likert,
                        }
                        is_correct = likert >= self.min_passing_likert
                    case ContinuousGradingReport(score=continuous_score) as grading_report:
                        assert 0 <= self.continuous_reward_threshold <= 1, (
                            "continuous_reward_threshold must be between 0 and 1"
                        )
                        is_correct = continuous_score >= self.continuous_reward_threshold
                        continuous_reward = (
                            continuous_score
                            if continuous_score >= self.continuous_reward_threshold
                            else 0
                        )
                    case InvalidGradingReport(errors=parsing_errors):
                        errors_blamed_on_grader.extend(parsing_errors)
                    case _:
                        raise ValueError("Unexpected grading report")

            assert is_correct is not None or len(errors_blamed_on_grader) > 0, (
                "is_correct should be set if there are no errors"
            )

            grader_metadata = {
                "answer_extraction": extraction_metadata,
                "autograder": {
                    "metadata": {
                        "prompt": taskgen_utils.render_grader_system_suffix(
                            prompt_convo, last_n=self.log_last_n_grader_prompt_messages
                        ),
                        "response": chat.Conversation(messages=response_messages).model_dump_json(),
                        "grader_raw_score": log_raw_grader_score,
                        "parse_error": bool(len(errors_blamed_on_grader)),
                        "grader_errors": errors_blamed_on_grader,
                    },
                },
            }

            if len(errors_blamed_on_grader):
                outputs.append(
                    grader_module.GraderOutput.with_system_errors(
                        grader_nickname=self.name,
                        sample_id=sample.sample_id,
                        grader_errors=[GraderError(error_str=e) for e in errors_blamed_on_grader],
                        given_answer=given_answer,
                        metadata=grader_metadata,
                    )
                )
                continue

            assert is_correct is not None
            if continuous_reward is not None:
                outputs.append(
                    grader_module.GraderOutput.with_continuous_reward(
                        grader_nickname=self.name,
                        sample_id=sample.sample_id,
                        reward_name="taskgen_grader",
                        reward=continuous_reward,
                        is_correct_for_analysis=is_correct,
                        given_answer=given_answer,
                        metadata=grader_metadata,
                    )
                )
            else:
                outputs.append(
                    grader_module.GraderOutput.with_binary_reward(
                        grader_nickname=self.name,
                        sample_id=sample.sample_id,
                        reward_name="taskgen_grader",
                        is_correct=is_correct,
                        given_answer=given_answer,
                        metadata=grader_metadata,
                    )
                )

        return outputs