# Codeforces evaluation dataset preparation

[Codeforces](https://codeforces.com/) is a competitive programming platform that hosts contests and allows users to practice coding problems. This directory contains resources for preparing an evaluation dataset based on CodeForces.

The initial dataset is the one provided by MAI, which can be found at: `az://oaiteammoonfiregenaiukso/datasets/berry_data/mai_verifiable_data/annotations/codeforces_unifiedv1`.

To pre-process the data into a peaval-compatible format, download some data from the path above and run the `format_mai_dataset.py` script. The output will be written into `outputs/mai_verifiable_data_reformatted`. Upload that directory to the `orngscuscresco` storage account, which is used by the evaluation script.

As of now, I am only using 230 samples (out of several thousand).