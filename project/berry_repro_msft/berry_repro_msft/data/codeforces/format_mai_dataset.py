#!/usr/bin/env python3
"""
Script to format MAI dataset files.
Reads files from input folder and processes them.
"""

import argparse
import gzip
import json
import os
from pathlib import Path


def read_json_file(file_path):
    """
    Read and parse a JSON file.

    Args:
        file_path (str or Path): Path to the JSON file to read

    Returns:
        dict: Parsed JSON data, or None if there was an error
    """
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)
        return data
    except Exception as e:
        print(f"Error reading JSON file '{file_path}': {e}")
        return None


def process_file(file_path, destination_storage_account_name="orngscuscresco"):
    """
    Process a single JSON file: extract required fields and create formatted object.

    Args:
        file_path (Path): Path to the JSON file to process
        destination_storage_account_name (str): Storage account name for URLs (default: "orngscuscresco")

    Returns:
        dict: Formatted object with unique_id, problem, and metadata, or None if error
    """
    # Extract filename as unique_id (without extension)
    unique_id = file_path.stem

    # Read the JSON file
    data = read_json_file(file_path)
    if data is None:
        return None

    try:
        # Extract required fields
        problem = data.get("statement", "")
        metadata_dict = data.get("metadata", {})
        memory_limit = metadata_dict.get("memory_limit")
        time_limit = metadata_dict.get("time_limit")

        # Check checker_data
        checker_data = data.get("checker_data", {})
        checker_language = checker_data.get("language", "")

        # Verify language is cpp
        if checker_language != "cpp":
            print(
                f"Warning: Skipping file '{file_path.name}' - checker language is '{checker_language}', not 'cpp'"
            )
            return None

        # Check include dictionary in checker_data
        include_dict = checker_data.get("include", {})
        if len(include_dict) > 1:
            print(
                f"Warning: Skipping file '{file_path.name}' - include dictionary has {len(include_dict)} keys, expected 1"
            )
            return None

        # Create checker directory
        checker_dir = Path(f"./outputs/mai_verifiable_data_reformatted/python/checkers/{unique_id}")
        checker_dir.mkdir(parents=True, exist_ok=True)

        # Write checker.cpp file
        checker_content = checker_data.get("checker", "")
        checker_file = checker_dir / "checker.cpp"
        try:
            with open(checker_file, "w", encoding="utf-8") as f:
                f.write(checker_content)
        except Exception as e:
            print(f"Error writing checker file for '{file_path.name}': {e}")
            return None

        # Write include file if it exists
        if include_dict:
            include_filename, include_content = next(iter(include_dict.items()))
            include_file = checker_dir / include_filename
            try:
                with open(include_file, "w", encoding="utf-8") as f:
                    f.write(include_content)
            except Exception as e:
                print(f"Error writing include file for '{file_path.name}': {e}")
                return None

        # Process test cases
        test_cases = data.get("test_cases", [])
        if test_cases:
            # Create test cases directory
            test_cases_dir = Path(
                f"./outputs/mai_verifiable_data_reformatted/python/test_cases/{unique_id}"
            )
            test_cases_dir.mkdir(parents=True, exist_ok=True)

            # Write test cases to compressed JSONL.GZ file
            test_cases_file = test_cases_dir / "test_cases.jsonl.gz"
            try:
                with gzip.open(test_cases_file, "wt", encoding="utf-8") as f:
                    for test_case in test_cases:
                        input_data = test_case.get("input", "")
                        output_data = test_case.get("output", "")
                        test_case_obj = {"inputs": input_data, "outputs": output_data}
                        json_line = json.dumps(test_case_obj, ensure_ascii=False)
                        f.write(json_line + "\n")
            except Exception as e:
                print(f"Error writing test cases file for '{file_path.name}': {e}")
                return None

        # Convert time_limit to testcase_timeout (divide by 1000)
        testcase_timeout = time_limit / 1000 if time_limit is not None else None

        # Create formatted object
        formatted_obj = {
            "unique_id": unique_id,
            "problem": "Solve this problem in python with function signature `def solve()`.\n\n\n"
            + problem,
            "answer": "",
            "metadata": {
                "task_id": unique_id,
                "language": "python3",
                "memory_limit": memory_limit,
                "testcase_timeout": testcase_timeout,
                "test_cases_url": f"https://{destination_storage_account_name}.blob.core.windows.net/data/thopo/codeforces/mai_verifiable_data_reformatted/python/test_cases/{unique_id}/test_cases.jsonl.gz",
                "testlib_header_url": f"https://{destination_storage_account_name}.blob.core.windows.net/data/thopo/codeforces/mai_verifiable_data_reformatted/python/checkers/{unique_id}/{include_filename}",
                "checker_url": f"https://{destination_storage_account_name}.blob.core.windows.net/data/thopo/codeforces/mai_verifiable_data_reformatted/python/checkers/{unique_id}/checker.cpp",
                "contest_start_year": "",
                "difficulty_rating": "",
                "tags": "",
                "rtc": False,
            },
        }

        return formatted_obj

    except Exception as e:
        print(f"Error processing file '{file_path}': {e}")
        return None


def process_input_folder(input_folder, destination_storage_account_name="orngscuscresco"):
    """
    Process all JSON files in the input folder and write results to JSONL file.

    Args:
        input_folder (str): Path to the input folder
        destination_storage_account_name (str): Storage account name for URLs (default: "orngscuscresco")
    """
    input_path = Path(input_folder)

    # Check if folder exists
    if not input_path.exists():
        print(f"Error: Input folder '{input_folder}' does not exist")
        return

    if not input_path.is_dir():
        print(f"Error: '{input_folder}' is not a directory")
        return

    # Get all files in the folder (they are JSON files without .json extension)
    files = [f for f in input_path.iterdir() if f.is_file()]

    if not files:
        print(f"No files found in '{input_folder}'")
        return

    print(f"Processing {len(files)} files from '{input_folder}'...")

    # Create output directory if it doesn't exist
    output_dir = Path("./outputs/mai_verifiable_data_reformatted/python/test")
    output_dir.mkdir(parents=True, exist_ok=True)
    output_file = output_dir / "codeforces_python_mai.jsonl"

    processed_count = 0

    # Process files and write to JSONL
    try:
        with open(output_file, "w", encoding="utf-8") as outfile:
            for file_path in sorted(files):
                formatted_obj = process_file(file_path, destination_storage_account_name)
                if formatted_obj is not None:
                    # Write as JSON line (ensure_ascii=False to handle non-ASCII characters)
                    json_line = json.dumps(formatted_obj, ensure_ascii=False)
                    outfile.write(json_line + "\n")
                    processed_count += 1
                    if processed_count % 100 == 0:
                        print(f"  Processed {processed_count} files...")

        print(f"Completed processing {processed_count} files.")
        print(f"Output written to: {output_file}")

    except Exception as e:
        print(f"Error writing output file: {e}")


def main():
    """Main function to parse arguments and run the script."""
    parser = argparse.ArgumentParser(
        description="Format MAI dataset JSON files into JSONL format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python format_mai_dataset.py
  python format_mai_dataset.py --input-folder ./my_data

This script reads JSON files from the input folder, extracts the required fields
(unique_id, problem statement, memory_limit, and testcase_timeout), and writes
them as a JSONL file to ./outputs/mai_verifiable_data_reformatted/python/test/codeforces_python_mai.jsonl
        """,
    )

    parser.add_argument(
        "--input-folder",
        type=str,
        default="./inputs/mai_verifiable_data/codeforces_unifiedv1",
        help="Path to the input folder containing JSON files to process (default: %(default)s)",
    )

    parser.add_argument(
        "--destination-storage-account-name",
        type=str,
        default="orngscuscresco",
        help="Storage account name for constructing URLs in metadata (default: %(default)s)",
    )

    args = parser.parse_args()

    # Process the input folder
    process_input_folder(args.input_folder, args.destination_storage_account_name)


if __name__ == "__main__":
    main()
