# Change to orngcresco if you run in uksouth.
ORNGCRESCO=orngscuscresco
CKPT="az://$ORNGCRESCO/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted"
JUICE=768

CMD=(
oaipkg run qstar.run_eval
nostrict
name=$(date +%Y%m%d%H%M%S)-o4mini-filter-$JUICE-peaval

auto_inherit_training_args=False
load.restore_from_all_clusters=False
eval_settings.checkpoint_path="$CKPT"
policy.initial_checkpoint="$CKPT"

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
peashooter.sampling_use_ev3=True
root_config="mini.root.dev driver_rpc_timeout=6000"
policy.is_multimodal=True
...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe
...n_ctx=131072
...variant_producer=VarDiscountingVariantProducer
...override_reward_multiplier=768

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=80
peashooter.num_sampling_processes=16

policy.encoding_name=orion_200k
eval_settings.eval_initial_policy=True

:presets:filter_if
...dataset_container=$ORNGCRESCO

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=berry_repro
kafka_enable=False
enable_slackbot=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log