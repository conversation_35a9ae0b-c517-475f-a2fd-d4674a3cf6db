"""
MINIMALIST SWE
"""
import chz
from berry_repro_msft.data.swe.mrfs_setup import setup_fn
from berry_repro_msft.data.swe.caas_setup import CaasContainerResourceConfig
from berry_repro_msft.data.swe.rfs_grader import RFS<PERSON>rader
from qstar.common.datapoint import Harmony<PERSON>ompletionDatapoint
from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.common.tools.caas_container.caas_container_tool import CaasContainerToolConfig
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from qstar.curriculums.variant_producer import VariantProducer, VarDiscountingVariantProducer
from qstar.graders.grader import FunctionalGrader

CAAS_ENDPOINT = "https://eastus2.caas.azure.com"
CAAS_CONTAINER_IMAGE = "aio"

INSTRUCTIONS = """
Use `container.exec` to execute commands to accomplish the task. Avoid using interactive exec (setting `session_name`) unless it is absolutely necessary.
Always test your changes: either write a unit test or use `python -c` to manually invoke the new codepaths.
Internet access is not permitted in this container.

Examples of using `container.exec`:

- Running a script: {"cmd":["python","test.py"],"timeout":10000}
- Running some Python code: {"cmd":["python","-c","x = 0\\nfor i in range(10):\\n  x += i\\nprint(x)"]}
- Listing files: {"cmd":["ls"]}
- Grepping a file: {"cmd":["grep","class Action","aimacode/planning.py"],"timeout":1000}
- Showing lines in a file: {"cmd":["sed","-n","1,100p","path/to/file.py"]}
- Use `apply_patch` to apply patches to a file: {"cmd":["apply_patch","*** Begin Patch\\n*** Update File: path/to/file.py\\n@@ def example():\\n-pass\\n+return 123\\n*** End Patch"]}
""".strip()


@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig):
    caas_endpoint: str = CAAS_ENDPOINT
    caas_container_image: str = CAAS_CONTAINER_IMAGE
    caas_cpu_limit: float = 1.0
    caas_memory_limit: int = 2
    caas_idle_ttl: int = 10 * 60
    enable_network_after_setup: bool = False


@chz.chz
class DeepSWEVardiscProducer(VarDiscountingVariantProducer):
    min_reward_multiplier: int = 32
    max_reward_multiplier: int = 256
    num_reward_multipliers: int = 4


@chz.chz
class RFSDatasetConfig(HarmonyCompletionDatasetConfig):
    tool_configs: tuple[ToolConfig, ...] = (CaasContainerToolConfig(),)
    resource_configs: tuple[ResourceConfig, ...] = (CaasResourceConfig(setup_fn=setup_fn),)
    variant_producer: VariantProducer | None = DeepSWEVardiscProducer()
    grader: FunctionalGrader[HarmonyCompletionDatapoint]= RFSGrader()
    instructions: str = INSTRUCTIONS