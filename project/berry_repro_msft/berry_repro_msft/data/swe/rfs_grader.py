from __future__ import annotations

import dataclasses
from typing import Any
from typing import Mapping

import structlog

import chz
from berry_repro_msft.data.swe.mrfs_setup import mrfs_setup_run_test
from berry_rfs.utils import TestResult
from diffs.v4.chat import consolidate_changes
from diffs.v4.commit import Commit
from qstar.common import datapoint, types
from qstar.graders.repo_graders_v3.rfs_grader_base import RFSGraderBase
from qstar.graders.repo_graders_v3.utils import (
    CollectCommit,
    get_code_commit,
    render_patch_text_from_commit,
)
from berry_rfs.utils import read_jsonl, REPO_CACHE_DIR
from qstar.graders.repo_graders_v3.delegation_utils import collapse_convo

BASE_PATH = "az://orngscuscresco/data/jialei/swe/upload20250303/python-1p-3p/repo_tasks"

logger = structlog.stdlib.get_logger(component=__name__)


async def _evaluate_mrfs_code_core(
        caas_endpoint: str,
        image_name: str,
        commit: Commit,
        repo_id_step1: str,
        repo_id_step2: str,
        gt_metadata: dict[str, Any],
        fail_if_cheating: bool = True,
) -> TestResult:
    """Grades a sample by running it in terminal."""

    grade_user_test_changes = True
    # In the No Test variant, there's no test to run.
    # TODO: Reconsider Multilingual RCS + old test variant treatment here
    if "no_test/" in repo_id_step1 or "old_test/" in repo_id_step1:
        grade_user_test_changes = False

    # Step 1: Run all user code with all masked files
    if grade_user_test_changes:
        tr = await mrfs_setup_run_test(
            caas_endpoint,
            image_name,
            gt_metadata | {"repo_id": repo_id_step1},
            commit.get_changed_files(),
        )

        if not tr.passed:
            return tr
    else:
        tr = None

    # Step 2: Revert changes to protected files
    tr2 = await mrfs_setup_run_test(
        caas_endpoint,
        image_name,
        gt_metadata | {"repo_id": repo_id_step2},
        get_code_commit(commit).get_changed_files(),
    )

    if grade_user_test_changes:
        cheating = not tr2.passed and not tr2.system_error
        if fail_if_cheating:
            if cheating:
                tr2.error = "Cheating!"
                return tr2
            assert tr is not None
            return tr
        else:
            assert tr is not None
            tr.metadata["cheating"] = cheating
            return tr
    else:
        return tr2


def _get_repo_id(gt_datapoint: datapoint.HarmonyCompletionDatapoint) -> str:
    return gt_datapoint.metadata["repo_id"]


def _get_orig_repo_id(repo_id: str) -> str:
    return (
        repo_id.replace("_no_test/", "/")
        .replace("_private_test/", "/")
        .replace("_old_test/", "_new_test/")
    )


async def _evaluate_mrfs_code(
        caas_endpoint: str,
        image_name: str,
        sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
        commit: Commit,
        fail_if_cheating: bool = True,
) -> TestResult:
    """Grades a sample by running it on an ACE machine."""
    with open("/var/log/supervisor/rfs_grader.log", "a") as f:
        f.write(
            f"caas_endpoint={caas_endpoint} image_name={image_name} fail_if_cheating={fail_if_cheating} valid={sample.valid}\n")

    assert sample.valid

    gt_datapoint: datapoint.HarmonyCompletionDatapoint = sample.gt_datapoint
    gt_metadata: dict = gt_datapoint.metadata
    repo_id: str = _get_repo_id(gt_datapoint)
    return await _evaluate_mrfs_code_core(
        caas_endpoint,
        image_name,
        commit,
        repo_id,
        _get_orig_repo_id(repo_id),
        gt_metadata,
        fail_if_cheating=fail_if_cheating,
    )


def get_repo_task_data(
        repo_id: str,
        gz_path: str = BASE_PATH
) -> dict[str, Any]:
    # assert "/" in repo_id
    path = f"{gz_path}/{repo_id}.gz"
    # path = sync_regionally(path)
    rows = read_jsonl(
        path,
        cache_dir=REPO_CACHE_DIR,
        verbose=False,
    )
    return rows[0]


def collect_orig(
        sample: types.SampleWithCompletion,
) -> dict[str, str | None]:
    repo_id = _get_repo_id(sample.gt_datapoint)
    task_data = get_repo_task_data(repo_id)
    files = task_data.get("files", {})
    assert files
    masked_files = task_data.get("masked_files", {})
    orig = {k: v for k, v in (files | masked_files).items() if v is not None}
    return orig


def collect_commit_uncached(
        sample: types.SampleWithCompletion,
        orig: Mapping[str, str | None] | None = None,
) -> Commit:
    convo = collapse_convo(sample)
    assert convo is not None
    if orig is None:
        orig = collect_orig(sample)

    # assert masked_files or task_data.get("commit"), f"Failed to find masked files or commit for {repo_id}, look in az://oaiwhalesong/strawberry-processing/reasoning/repo/data/{repo_id}.gz"
    return consolidate_changes(convo, orig)


def _collect_commit(
        sample: types.SampleWithCompletion,
        orig: Mapping[str, str | None] | None = None,
) -> Commit:
    """Collects the commit from the sample.  Uses a cache.

    If `orig` is provided, it will be used as the original content of the files.
    In this case, no cache is used.
    """
    provided_orig = orig
    if orig is None:
        if commit := sample.ephemeral_metadata.get("commit"):
            return commit

    commit = collect_commit_uncached(sample, orig=orig)
    if provided_orig is None:
        sample.ephemeral_metadata["commit"] = commit
    return commit


@chz.chz(typecheck=True)
class RFSGrader(RFSGraderBase):
    caas_endpoint: str = chz.field(default="https://eastus2.caas.azure.com")
    caas_container_image: str = chz.field(default="aio")
    fail_if_cheating: bool = chz.field(
        doc="If True, the grader will fail the sample if cheating is detected.",
        default=True,
    )
    collect_commit_fn: CollectCommit = chz.field(
        doc="Function to collect a commit from a sample.",
        default=_collect_commit,
    )

    async def _evaluate_code(
            self,
            sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    ) -> types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]:
        try:
            commit = self.collect_commit_fn(sample)
            if get_code_commit(commit).changes:
                tr = await _evaluate_mrfs_code(
                    self.caas_endpoint, self.caas_container_image, sample, commit
                )
            else:
                tr = TestResult(error="No changed code files")
        except Exception as e:
            logger.exception(
                "Unable to collect commit",
            )
            commit = None
            tr = TestResult(system_error=f"{type(e)}: {str(e)}")
        with open("/var/log/supervisor/rfs_grader_results.log", "a") as f:
            f.write(f"{tr}\n")

        if tr.system_error:
            sample = dataclasses.replace(
                sample,
                errors_blamed_on_system=sample.errors_blamed_on_system
                                        | {"rfs_grader_system_error"},
                metadata=sample.metadata
                         | {
                             "grader": {
                                 "prompt": "",
                                 "response": "System Error",
                                 "score": 0,
                                 "rfs_grader": tr.model_dump(),
                             }
                         },
            )
            graded_sample = sample.process_invalid()
            return graded_sample
        assert commit is not None
        if self.fail_if_cheating:
            tr.passed = tr.passed and (not tr.metadata.get("cheating", False))

        response = tr.metadata.get("exec", {}).get("output", "")
        if tr.metadata.get("cheating", False):
            response = "Cheating detected.\n\n" + response

        diff = render_patch_text_from_commit(get_code_commit(commit))
        return sample.with_correctness(
            reward_name="rfs_grader",
            is_correct=tr.passed,
            given_answer=diff,
            additional_metadata={
                "grader": {
                    "prompt": diff,
                    "response": response,
                    "score": 1 if tr.passed else 0,
                    "rfs_grader": tr.model_dump(),
                },
                "cheating": tr.metadata.get("cheating", False),
                "container_code_diff": diff,
            },
        )