import asyncio
import json
import structlog
import time
from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobServiceClient, generate_container_sas, ContainerSasPermissions
from berry_rfs.mrfs_eval_utils import mrfs_run_test_with_test_result
from berry_rfs.utils import TestResult
from caas import ExecError, TimedOutError, ServerError
from caas.api import caas_api
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from datetime import datetime, timedelta
from mini.utils.simple_timer import simple_timer
from mini.metrics import metrics
from typing import Any, Awaitable, Callable

DEFAULT_ATTEMPTS = 10
DEFAULT_TIMEOUT = 3600
MAX_OUTPUT_SIZE = 1_000_000
DEFAULT_SUPPORT_ROOT = "/tmp/repo_support"
HTTP_FILE_PATH = "https://orngcaas.blob.core.windows.net/data/swe-model-training-data/jialei-test/for-caas-0304/upload_repos"

logger = structlog.stdlib.get_logger(component=__name__)


def get_strawberry_ace_token():
    # Set up the credentials and BlobServiceClient
    credential = DefaultAzureCredential()
    blob_service_client = BlobServiceClient(account_url="https://orngcaas.blob.core.windows.net", credential=credential)

    # Set the expiry time for the SAS token
    expiry_time = datetime.now() + timedelta(hours=1)  # Token valid for 1 hour
    # Generate the user delegation SAS token
    user_delegation_key = blob_service_client.get_user_delegation_key(datetime.now(), expiry_time)
    sas_token = generate_container_sas(
        account_name="orngcaas",
        container_name="data",
        user_delegation_key=user_delegation_key,
        permission=ContainerSasPermissions(read=True),  # Set permissions as needed
        expiry=expiry_time
    )
    return sas_token


def truncate_text(text: str) -> str:
    if len(text) <= MAX_OUTPUT_SIZE:
        return text
    return text[: MAX_OUTPUT_SIZE // 2] + " [TRUNCATED] " + text[-MAX_OUTPUT_SIZE // 2 :]


async def try_run_command(
    terminal_session: TerminalSession,
    command: str,
    seconds: int = 120,
    attempts: int = 1,
) -> tuple[bool, dict]:
    for attempt in range(attempts):
        start_time = time.time()
        try:
            result = await terminal_session.session.run(BashScript(command, timeout=seconds))
            text = result.decode("utf-8")
            return True, {
                "command": command,
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except TimedOutError as e:
            text = e.output.decode("utf-8")
            return False, {
                "command": command,
                "error": "TimedOutError",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except ExecError as e:
            if attempt + 1 < attempts:
                await asyncio.sleep(attempt * 5 + 1)
                continue
            text = e.output.decode("utf-8")
            return False, {
                "command": command,
                "error": "ExecError",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except Exception as e:
            text = ""
            return False, {
                "command": command,
                "error": "Exception",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }


def setup_bashrc(repo_root: str, PATHS: list[str]) -> str:
    paths = ":".join(PATHS)
    SETUP_BASH = f"""
cat <<EOF >> /root/.bashrc
export PATH=$PATH:{paths}
export PYTHONPATH={repo_root}:{repo_root}/src
export PY_COLORS=0
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
cat <<EOF >> /root/.bash_profile
export PATH=$PATH:{paths}
export PYTHONPATH={repo_root}:{repo_root}/src
export PY_COLORS=0
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
"""
    return SETUP_BASH


async def setup_repo(
    terminal_session: TerminalSession,
    repo_root: str,
    repo_id: str,
    package_cache_id: str,
    extract_cache_cmd: str,
    install_packages_cmd: str,
    install_seconds: int = DEFAULT_TIMEOUT,
    support_root: str = DEFAULT_SUPPORT_ROOT,
    direct_repo_files: dict[str, str] | None = None,
    files_to_be_edited: dict[str, str] = {},
) -> dict[str, Any]:
    """Setup a repo for testing.

    Args:
        terminal_session: The terminal session to use.
        repo_root: The root directory for the repo.  It must start with / and not end in /.
        repo_id: The repo ID.
        package_cache_id: The package cache ID.  If empty, no package cache is uploaded.
        extract_cache_cmd: The command to extract the cache. Must not be empty if package_cache_id is not empty.
        install_packages_cmd: The command to install packages.  Must be empty if package_cache_id is not empty.
        install_seconds: int
        support_root: The support root directory.  Its path is added to the PATH.
        direct_repo_files: Only used for testing.  If not None, it is a dict
          of filenames and contents to upload to the repo.

    Returns:
        A dict of status information for each step of the setup process.
    """
    assert repo_root.startswith("/") and not repo_root.endswith("/"), (
        f"Invalid repo_root; must start with / and not end in /: {repo_root}"
    )

    result = {
        "setup_done": False,
        "repo_id": repo_id,
        "package_cache_id": package_cache_id,
        "extract_cache_cmd": extract_cache_cmd,
        "install_packages_cmd": install_packages_cmd,
    }
    with open("/var/log/supervisor/yang_repo_setup.log", "a") as f:
        to_write = {'repo_root': repo_root, 'repo_id': repo_id, 'package_cache_id': package_cache_id, 'extract_cache_cmd': extract_cache_cmd, 'install_packages_cmd': install_packages_cmd}
        f.write(json.dumps(to_write)+"\n")

    with simple_timer("download_task_repo", log_fn=lambda x: [metrics.mean(k, v) for k, v in x.items()]):
        ok, d = await try_run_command(
            terminal_session,
            f'rm -rf {repo_root}; mkdir -p {repo_root}; curl -k -sL "{HTTP_FILE_PATH}/{repo_id}.tar.gz?{get_strawberry_ace_token()}" | tar -xz -C {repo_root}',
            seconds=install_seconds,
            attempts=DEFAULT_ATTEMPTS,
        )
    result["copy_repo"] = d

    if not ok:
        metrics.sum("download_task_repo_failed", 1.0)
        return result

    # setup packages for 3p repos
    if install_packages_cmd:
        with simple_timer("setup_task_repo", log_fn=lambda x: [metrics.mean(k,v) for k,v in x.items()]):
            ok, d = await try_run_command(
                terminal_session, f"cd {repo_root}; {install_packages_cmd}", install_seconds,
                attempts=DEFAULT_ATTEMPTS,
            )
        result["install_packages"] = d
        if not ok:
            metrics.sum("setup_task_repo_failed", 1.0)
            return result

    await terminal_session.session.update_network(False)
    setup_bashrc_cmd = setup_bashrc(repo_root, [support_root, repo_root, f"{repo_root}/python"])

    ok, d = await try_run_command(terminal_session, setup_bashrc_cmd)
    result["setup_bashrc"] = d
    if not ok:
        return result

    result["setup_done"] = True
    metrics.sum("setup_task_repo_done", 1.0)
    return result


def patch_metadata(metadata: dict[str, Any]) -> dict[str, Any]:
    exec_cmd = metadata.get("exec_cmd", "")
    if not exec_cmd:
        assert not metadata.get("lang")
        exec_cmd = "pytest"

        extra_setup = metadata.get("extra_setup", "")
        install_packages_cmd = ""
        if extra_setup:
            install_packages_cmd = extra_setup.replace("!", "")

        return {k: v for k, v in metadata.items() if k != "extra_setup"} | {
            "lang": "python",
            "extract_cache_cmd": "",
            "install_packages_cmd": install_packages_cmd,
            "build_cmd": "",
            "exec_cmd": exec_cmd,
        }

    setup_cmd = metadata.get("setup_cmd", "")
    if not setup_cmd:
        return metadata

    extract_cache_cmd = ""
    install_packages_cmd = ""
    build_cmd = ""

    if "npm install" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1]).strip()
        if not extract_cache_cmd:
            # for RCS
            install_packages_cmd = setup_cmd
    elif "mvn -q install" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1])
        build_cmd = setup_cmd.split("; ")[-1]
    elif "cargo build" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1])
    elif "go build" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1])
    elif "pip" in setup_cmd:
        install_packages_cmd = setup_cmd.replace("\n", "; ")
    else:
        assert False

    return metadata | {
        "extract_cache_cmd": extract_cache_cmd,
        "install_packages_cmd": install_packages_cmd,
        "build_cmd": build_cmd,
    }


async def setup_repo_with_gt_metadata(
    terminal_session: TerminalSession,
    gt_metadata: dict[str, Any],
) -> dict[str, Any]:
    repo_id = gt_metadata["repo_id"]
    repo_root = "/root/code"
    package_cache_id = gt_metadata.get("package_cache_id", "")

    metadata = patch_metadata(gt_metadata)
    extract_cache_cmd = metadata.get("extract_cache_cmd", "")
    install_packages_cmd = metadata.get("install_packages_cmd", "")

    out = await setup_repo(
        terminal_session,
        repo_root=repo_root,
        repo_id=repo_id,
        package_cache_id=package_cache_id,
        extract_cache_cmd=extract_cache_cmd,
        install_packages_cmd=install_packages_cmd,
        files_to_be_edited=metadata.get("modified_files", {}),
        direct_repo_files=metadata.get("direct_repo_files", {}).get("files"),
    ) | {"repo_root": repo_root}

    with open("/var/log/supervisor/yang_repo_setup_result.log", "a") as f:
        f.write(json.dumps(out)+"\n")

    return out


async def setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    result = await setup_repo_with_gt_metadata(terminal_session, datapoint["metadata"])
    if not result.get("setup_done"):
        logger.error("mrfs_setup.setup_fn failed", result=result)
        raise Exception("Setup failed")


async def mrfs_setup_run_test(
        caas_endpoint: str,
        image_name: str,
        gt_metadata: dict[str, Any],
        changed_files: dict[str, str | None],
        prep_attempts: int = DEFAULT_ATTEMPTS,
        callback: Callable[[TerminalSession, str], Awaitable[dict[str, Any]]] | None = None,
) -> TestResult:
    metadata = patch_metadata(gt_metadata)

    container = None
    setup_result = {}
    for attempt in range(prep_attempts):
        try:

            caas = caas_api(endpoint=caas_endpoint)
            caas_session = await caas.new_session(
                image=image_name,
                memory_limit="2g",
                cpu_limit="2.0",
                disk_limit="25g",
                pids_limit=200,
            )

            terminal_session = TerminalSession(caas_session, endpoint=caas_endpoint)
            setup_result = await setup_repo_with_gt_metadata(
                terminal_session,
                metadata,
            )
            if not setup_result.get("setup_done"):
                if attempt + 1 < prep_attempts:
                    await asyncio.sleep(attempt * 5 + 1)
                    continue
                logger.error(
                    "setup_repo_with_gt_metadata failed during grading", result=setup_result
                )
                return TestResult(
                    system_error="Setup failed", metadata=setup_result | {"attempt": attempt}
                )

            repo_root: str = setup_result["repo_root"]
            tr = await mrfs_run_test_with_test_result(
                terminal_session,
                metadata,
                changed_files,
                repo_root,
            )

            if tr.passed and callback:
                try:
                    tr.metadata |= await callback(terminal_session, repo_root)
                except Exception as e:
                    tr.metadata["callback_error"] = str(e)
        except ServerError:
            if attempt + 1 < prep_attempts:
                await asyncio.sleep(attempt * 5 + 1)
                continue
            raise
        finally:
            if container:
                await container.teardown()

        if attempt > 0:
            tr.metadata["attempt"] = attempt
        break
    tr.metadata |= setup_result
    return tr