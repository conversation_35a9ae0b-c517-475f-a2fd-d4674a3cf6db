#!/usr/bin/env python3
"""
Solve problem using public api, such azure aoai
"""

import argparse
import json
import logging
import sys
import hashlib
from pathlib import Path
from typing import Any, Dict, List, Optional, Set
from concurrent.futures import ThreadPoolExecutor, as_completed
from azure.identity import get_bearer_token_provider, DefaultAzureCredential

import litellm
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Suppress verbose LiteLLM logging
logging.getLogger("LiteLLM").setLevel(logging.WARNING)
logging.getLogger("litellm").setLevel(logging.WARNING)

class LLMProblemSolver:
    """Generator for processing JSONL files with LLM calls."""

    def __init__(
        self,
        model: str = "gpt-3.5-turbo",
        max_workers: int = 10,
        api_base: Optional[str] = None,
        api_version: Optional[str] = None,
        save_interval: int = 10
    ):
        """
        Initialize the GPT prompt generator.

        Args:
            model: The model to use for LLM calls
            max_workers: Maximum number of parallel workers
            api_base: Optional API base URL
            api_version: Optional API version
            save_interval: Number of processed items between saves to output file
        """
        self.model = model
        self.max_workers = max_workers
        self.api_base = api_base
        self.api_version = api_version
        self.save_interval = save_interval

        credential = DefaultAzureCredential()
        self.token_provider = get_bearer_token_provider(credential, "https://cognitiveservices.azure.com/.default")

    def load_jsonl(self, input_path: Path) -> List[Dict[str, Any]]:
        """
        Load data from a JSONL file.

        Args:
            input_path: Path to the input JSONL file

        Returns:
            List of JSON objects
        """
        data = []
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line:
                        try:
                            data.append(json.loads(line))
                        except json.JSONDecodeError as e:
                            logger.warning(f"Invalid JSON on line {line_num}: {e}")
                            continue
        except FileNotFoundError:
            logger.error(f"Input file not found: {input_path}")
            raise
        except Exception as e:
            logger.error(f"Error reading input file: {e}")
            raise

        logger.info(f"Loaded {len(data)} records from {input_path}")
        return data

    def _generate_item_hash(self, json_obj: Dict[str, Any]) -> str:
        """
        Generate a unique hash for a JSON object based on its content.

        Args:
            json_obj: The JSON object to hash

        Returns:
            Hex string hash of the object
        """
        content_str = json.dumps(json_obj, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(content_str.encode('utf-8')).hexdigest()[:16]

    def load_existing_results(self, output_path: Path) -> tuple[Set[str], Dict[str, Dict[str, Any]]]:
        """
        Load existing results from output file and build hash mapping.

        Args:
            output_path: Path to output file

        Returns:
            Tuple of (processed_hashes, hash_to_result_mapping)
        """
        processed_hashes = set()
        hash_to_result = {}

        if output_path.exists():
            try:
                logger.info("Loading existing results from output file...")
                with open(output_path, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if line:
                            try:
                                result_obj = json.loads(line)
                                # Generate hash from original object (without LLM additions)
                                original_obj = {k: v for k, v in result_obj.items()
                                              if k not in ['answer', 'llm_model', 'llm_error']}
                                item_hash = self._generate_item_hash(original_obj)
                                hash_to_result[item_hash] = result_obj
                                processed_hashes.add(item_hash)
                            except json.JSONDecodeError as e:
                                logger.warning(f"Invalid JSON on line {line_num} in output file: {e}")
                                continue
                logger.info(f"Loaded {len(hash_to_result)} existing results from output file")
            except Exception as e:
                logger.warning(f"Could not load existing results: {e}")
        else:
            logger.info("No existing output file found")

        return processed_hashes, hash_to_result

    def append_results(self, new_results: List[Dict[str, Any]], output_path: Path) -> None:
        """
        Append new results to the output file.

        Args:
            new_results: List of new results to append
            output_path: Path to output file
        """
        if not new_results:
            return

        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'a', encoding='utf-8') as f:
                for item in new_results:
                    f.write(json.dumps(item, ensure_ascii=False) + '\n')
            logger.info(f"Appended {len(new_results)} results to {output_path}")
        except Exception as e:
            logger.error(f"Error appending to output file: {e}")
            raise

    def save_jsonl(self, data: List[Dict[str, Any]], output_path: Path) -> None:
        """
        Save data to a JSONL file.

        Args:
            data: List of JSON objects to save
            output_path: Path to the output JSONL file
        """
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                for item in data:
                    f.write(json.dumps(item, ensure_ascii=False) + '\n')
            logger.info(f"Saved {len(data)} records to {output_path}")
        except Exception as e:
            logger.error(f"Error saving output file: {e}")
            raise

    def call_llm(self, prompt: str, json_obj: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a single LLM call and return the enhanced JSON object.

        Args:
            prompt: The prompt to send to the LLM
            json_obj: The original JSON object

        Returns:
            JSON object with LLM output added
        """
        try:
            response = litellm.completion(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                # Add other parameters as needed
                api_base=self.api_base,
                api_version=self.api_version,
                azure_ad_token=self.token_provider(),
                temperature=1.0,
                max_tokens=10240
            )

            llm_output = response.choices[0].message.content
            logger.debug(f"LLM output: {llm_output}")

            # Create enhanced object with LLM output
            enhanced_obj = json_obj.copy()
            enhanced_obj["answer"] = llm_output
            enhanced_obj["llm_model"] = self.model

            return enhanced_obj

        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            # Return original object with error info
            enhanced_obj = json_obj.copy()
            enhanced_obj["answer"] = None
            enhanced_obj["llm_error"] = str(e)
            return enhanced_obj

    def process_parallel(self, data: List[Dict[str, Any]], output_path: Path) -> List[Dict[str, Any]]:
        """
        Process all JSON objects in parallel using ThreadPoolExecutor with periodic saving.

        Args:
            data: List of JSON objects to process
            output_path: Path to save results

        Returns:
            List of enhanced JSON objects with LLM outputs
        """
        # Load existing results efficiently (single pass)
        processed_hashes, hash_to_result = self.load_existing_results(output_path)

        # Filter out already processed items and collect existing results
        pending_data = []
        results = []

        for json_obj in data:
            item_hash = self._generate_item_hash(json_obj)
            if item_hash in processed_hashes:
                # Use pre-loaded result from hash mapping
                if item_hash in hash_to_result:
                    results.append(hash_to_result[item_hash])
                else:
                    # Hash exists but no result found, reprocess
                    pending_data.append(json_obj)
            else:
                pending_data.append(json_obj)

        if results:
            logger.info(f"Resuming: {len(results)} items already processed")

        if not pending_data:
            logger.info("All items already processed")
            return results

        logger.info(f"Processing {len(pending_data)} remaining items")

        pending_count = 0
        new_results_batch = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_obj = {}
            for json_obj in pending_data:
                prompt = json_obj["problem"]
                future = executor.submit(self.call_llm, prompt, json_obj)
                future_to_obj[future] = json_obj

            # Collect results with progress bar and periodic saving
            for future in tqdm(as_completed(future_to_obj), total=len(pending_data), desc="Processing"):
                try:
                    result = future.result()
                    new_results_batch.append(result)
                    results.append(result)

                except Exception as e:
                    json_obj = future_to_obj[future]
                    logger.error(f"Failed to process object: {e}")
                    # Add error to original object
                    error_obj = json_obj.copy()
                    error_obj["answer"] = None
                    error_obj["llm_error"] = str(e)
                    new_results_batch.append(error_obj)
                    results.append(error_obj)

                pending_count += 1

                # Periodic save - append new results to output file
                if pending_count % self.save_interval == 0:
                    logger.info(f"Periodic save triggered at {pending_count} items")
                    self.append_results(new_results_batch, output_path)
                    new_results_batch = []  # Clear batch after saving

            # Final save - append any remaining results
            if new_results_batch:
                self.append_results(new_results_batch, output_path)

        return results

    def process_file(self, input_path: Path, output_path: Path) -> None:
        """
        Process a complete JSONL file.

        Args:
            input_path: Path to input JSONL file
            output_path: Path to output JSONL file
        """
        logger.info(f"Starting processing: {input_path} -> {output_path}")

        # Load input data
        data = self.load_jsonl(input_path)

        if not data:
            logger.warning("No data to process")
            return

        # Process in parallel with periodic saving
        self.process_parallel(data, output_path)

        logger.info("Processing completed successfully")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Generate GPT prompts from JSONL files")
    parser.add_argument("input", type=Path, help="Input JSONL file path")
    parser.add_argument("output", type=Path, help="Output JSONL file path")
    parser.add_argument("--model", default="azure/o_series/o1", help="LLM model to use")
    parser.add_argument("--max-workers", type=int, default=1, help="Maximum parallel workers")
    parser.add_argument("--api-base", type=str, help="Custom API base URL")
    parser.add_argument("--api-version", help="API version", default="2024-12-01-preview")
    parser.add_argument("--save-interval", type=int, default=10, help="Save results every N processed items")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        generator = LLMProblemSolver(
            model=args.model,
            max_workers=args.max_workers,
            api_base=args.api_base,
            api_version=args.api_version,
            save_interval=args.save_interval
        )

        generator.process_file(args.input, args.output)

    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()