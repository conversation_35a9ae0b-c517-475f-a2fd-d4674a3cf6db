"""
Schedules peashooter, automatically starts Q* training on MATH and automatically
shuts down the cluster after 1 step of training.

Usage from the laptop:
```
pychz qstart_autostart_example.chz.py -e launch_cluster \
    cluster=<your-cluster>
```

Example with autostart:
```
pychz train_d36.chz.py -e launch_cluster \
    cluster=prod-southcentralus-hpe-2 \
    team=team-moonfire-genaicore \
    priority=team-critical \
    rapid_id=d36min \
    experiment_name=20250828161336-d36-minimal
```
to see the job's stdout, you can then SSH into the trainer and run:
```
tmux attach -t 0
```


Example with manual start:
```
pychz train_d28.chz.py -e launch_cluster \
    [...] \
    autostart=False
```
And then on the trainer (preferably in a tmux session):
```
pychz train_d28.chz.py experiment_name=math17k base_storage=orngcresco
```

See README.md for more details.
"""

import datetime
import os
from pathlib import Path

from oaipkg.poly import polyrepo_for_path

GLASS_ROOT = polyrepo_for_path(Path(__file__))

# TODO: expose in a separate library.
CLUSTER_TO_STORAGE_MAP = {
    "prod-uksouth-7": "orngcresco",
    "prod-uksouth-8": "orngcresco",
    "prod-southcentralus-hpe-2": "orngscuscresco",
    "prod-southcentralus-hpe-3": "orngscuscresco",
    "prod-southcentralus-hpe-5": "orngscuscresco",
}


def launch_cluster(
    cluster,
    priority="low-priority",
    team=None,
    rapid_id=None,
    experiment_name=None,
    suffix="d36",
    base_storage=None,
    autostart=True,
    size="minimal",
):
    if experiment_name is None:
        experiment_name = f"{datetime.datetime.now():%Y%m%d%H%M%S}-{suffix}-{size}"
    if rapid_id is None:
        rapid_id = experiment_name
    if base_storage is None:
        base_storage = CLUSTER_TO_STORAGE_MAP[cluster]

    chunks = ["python -m qstar.peashooter.run_peashooter"]
    if team:
        chunks.append(f"rapid.cluster.team={team}")
    chunks.append(
        f"""
rapid.cluster.name={cluster}
rapid.cluster.priority={priority}
rapid.rapid_id={rapid_id}
security_profile=msft-orng
extra_rcall_config={GLASS_ROOT}/project/berry_repro_msft/berry_repro_msft/rcall_config.py

n_gpus_per_sampler=8
"""
    )

    if size == "minimal":
        policy_gpus = 56
        chunks.append(
            f"""
sampling_jobs=2
n_train_gpus={policy_gpus}

cpu_controller=True
n_cpu_cores_for_controller=4
num_controller_nodes=1
"""
        )
    elif size == "standard":
        policy_gpus = 160
        chunks.append(
            f"""
sampling_jobs=40
n_train_gpus={policy_gpus}

cpu_controller=False
num_controller_nodes=2
"""
        )

    if autostart:
        chunks.append(
            f"""
autostart_file={__file__}
autostart_args="experiment_name={experiment_name} base_storage={base_storage} policy_gpus={policy_gpus} size={size}"
autostart_max_attempts=3

# Local validation doesn't work as of Sept 2025, so skip it.
# See Teams thread in Project Orange (CoreAI only): https://aka.ms/AAxxib4
autostart_skip_validation=True
# Starting from August FI, use the following flag instead:
# autostart_skip_local_test=True
"""
        )

    return "\n".join(chunks) + "\n"


def run_experiment(experiment_name, base_storage="orngcresco", policy_gpus=None, size=None):
    SPI = 96
    IPB = 64
    MAX_TOKENS = 65536
    INVERSE_TOKEN_COST = 32768
    CKPT = f"az://{base_storage}/models/snapshots/abv1-nv4-derisk-prior-decrypted2"
    chunks = []
    chunks.append(
        f"""
oaipkg run qstar.run_experiment
nostrict
name={experiment_name}

# Policy config
:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
peashooter.sampling_use_ev3=True
root_config="mini.root.dev driver_rpc_timeout=6000"
policy.initial_checkpoint="{CKPT}"
policy.is_multimodal=True
policy.encoding_name=orion_200k

# Training/sampling configs
defaults.target_samples_per_instance={SPI}
defaults.inverse_token_cost={INVERSE_TOKEN_COST}
defaults.instances_per_batch={IPB}
defaults.n_ctx={MAX_TOKENS}
...sampling_use_ev3=True
...ev3_inference_service_pool_size=32

# Datasets
:berry_repro_msft.train.presets:tr_math
:berry_repro_msft.train.presets:tr_code
:berry_repro_msft.train.presets:tr_progcomp
:berry_repro_msft.train.presets:tr_stem
:berry_repro_msft.train.presets:tr_if
...dataset_container={base_storage}
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
...harmony_constrained_sampling=True
...max_num_yields=100

save_every=5
seed=42

# Logging/misc
security_profile=msft-orng
enable_slackbot=False 
github_upload=False
wandb_enable=True
wandb.wandb_project=berry_repro
kafka_enable=False
"""
    )
    if policy_gpus is not None:
        chunks.append(f"policy.n_gpus={policy_gpus}")
    if size == "minimal":
        chunks.append("max_steps=10")
    return "\n".join(chunks)


_CMD = run_experiment
