import os

import berry.preset_utils
from qstar.sonic.presets.wildcards import default_variant_producer

ORNGCRESCO = os.getenv("ORNGCRESCO", "orngcresco")


gpt4o_grader_service = [
    "grader.renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
    "grader.grader_service=TokenCompleterGraderService",
    "grader.grader_service.redis_config_id=autograder-scallion-ppo-engine-defaults",
    "grader.grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
    f"grader.grader_service.token_completer.topic_or_snapshot=az://{ORNGCRESCO}/models/snapshots/4o-tt-v5-transferred-2025-04-09-decrypted",
    "grader.grader_service.token_completer.topic_mode_or_user=4o-grader",
    "grader.grader_service.token_completer.bus_line=bus",
]


gpt5_mini_grader_service = [
    "grader.renderer_name=harmony_v4.0.16_berry_v3_128k_orion_mm_no_budget_truncate",
    "grader.grader_service=TokenCompleterGraderService",
    "grader.grader_service.redis_config_id=autograder-ref-tbv3_cotograder_0202",
    "grader.grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
    f"grader.grader_service.token_completer.topic_or_snapshot=az://{ORNGCRESCO}/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted",
    "grader.grader_service.token_completer.topic_mode_or_user=gpt5-mini-grading",
    "grader.grader_service.token_completer.bus_line=bus",
    "grader.grader_service.token_completer.qos_type=bus_token_completer:QoSType.ROUND_ROBIN_BY_POD",
    "grader.grader_max_tokens=16384",
    "grader.answer_max_excess_characters=None",
    "grader.answer_length_multiplier=None",
    "grader.answer_length_limit=None",
]


default_variant_producer = [
    "variant_producer=CompositeVariantProducer",
    "variant_producer.variant_producers.0=VarDiscountingVariantProducer",
    "variant_producer.variant_producers.0.min_reward_multiplier=32",
    "variant_producer.variant_producers.0.max_reward_multiplier=256",
    "variant_producer.variant_producers.0.num_reward_multipliers=4",
]

# Math data presets
tr_math = berry.preset_utils.compose_presets(
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.xidai.mathgen.aime_1983_2023.train",
            "inverse_token_cost_multiplier=1",
            "grader=qstar.graders.normalized_sympy_grader:NormalizedSympyGrader",
            *gpt4o_grader_service,
            *default_variant_producer,
        ]
    ),
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.xidai.mathgen.dapo_math_17k.train",
            "inverse_token_cost_multiplier=1",
            "grader=qstar.graders.normalized_sympy_grader:NormalizedSympyGrader",
            *gpt4o_grader_service,
            *default_variant_producer,
        ]
    ),
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.xidai.mathgen.aops_instruct.train",
            "inverse_token_cost_multiplier=1",
            "grader=qstar.graders.normalized_sympy_grader:NormalizedSympyGrader",
            *gpt4o_grader_service,
            *default_variant_producer,
        ]
    ),
)


# Code data presets
tr_code = berry.preset_utils.compose_presets(
    berry.preset_utils.training_dataset_preset(
        [
            "=berry_repro_msft.data.swe.configs:RFSDatasetConfig",
            "dataset_id=data.swang.swe.upload20250320.wave1_1.train.rfs",
            "inverse_token_cost_multiplier=4",
        ]
    ),
    berry.preset_utils.training_dataset_preset(
        [
            "=berry_repro_msft.data.swe.configs:RFSDatasetConfig",
            "dataset_id=data.swang.swe.u20250423.bb_hard.train_1930",
            "inverse_token_cost_multiplier=4",
        ]
    ),
)
tr_progcomp = berry.preset_utils.compose_presets(
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.xidai.progcomp.codeforces.python.train",
            "inverse_token_cost_multiplier=2",
            "grader=qstar.graders.progcomp_caas.code_forces.code_forces_grader:CodeForcesGrader",
            "grader.extractor=qstar.graders.answer_extraction:LongestCodeBlockExtractor",
            *default_variant_producer,
            "variant_producer.variant_producers.1=ToolVariantProducer",
            "variant_producer.variant_producers.1.tool_variants.0=qstar.presets.progcomps_train:NoToolVariant",
        ]
    ),
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.xidai.progcomp.coderpad.python.train",
            "inverse_token_cost_multiplier=2",
            "grader=qstar.graders.progcomp_caas.code_forces.code_forces_grader:CodeForcesGrader",
            "grader.extractor=qstar.graders.answer_extraction:LongestCodeBlockExtractor",
            *default_variant_producer,
            "variant_producer.variant_producers.1=ToolVariantProducer",
            "variant_producer.variant_producers.1.tool_variants.0=qstar.presets.progcomps_train:NoToolVariant",
        ]
    ),
)


# STEM data presets
tr_stem = berry.preset_utils.compose_presets(
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.xidai.taskgen.oss.chemistryqa.train",
            "inverse_token_cost_multiplier=4",
            "grader=qstar.graders.taskgen_grader:TaskgenGrader",
            *gpt5_mini_grader_service,
            *default_variant_producer,
        ]
    ),
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.weijianxu.mai.hq_stem.pass_rate_filtered_accgt09_08052025.train",
            "inverse_token_cost_multiplier=4",
            "grader=qstar.graders.taskgen_grader:TaskgenGrader",
            "datapoint_converters.0.name=berry_repro_msft.data.mai.converters:convert_mai_to_taskgen",
            *gpt5_mini_grader_service,
            *default_variant_producer,
        ]
    ),
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.ziyuwan.o3.0807_SE_075_filter_ratiosample_60K_backup20K",
            "inverse_token_cost_multiplier=4",
            "grader=qstar.graders.taskgen_grader:TaskgenGrader",
            *gpt5_mini_grader_service,
            *default_variant_producer,
        ]
    ),
)


# IF data presets
tr_if = berry.preset_utils.compose_presets(
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.xidai.if.gen5_25k.train",
            "inverse_token_cost_multiplier=1",
            "grader=berry_repro_msft.data.mai.if_grader:IFGrader",
            *gpt5_mini_grader_service[:9],
            *default_variant_producer,
        ]
    ),
    berry.preset_utils.training_dataset_preset(
        [
            "=HarmonyCompletionDatasetConfig",
            "dataset_id=data.xidai.if.d_r_50k.train",
            "inverse_token_cost_multiplier=1",
            "grader=berry_repro_msft.data.mai.if_grader:IFGrader",
            *gpt5_mini_grader_service[:9],
            *default_variant_producer,
        ]
    ),
)
