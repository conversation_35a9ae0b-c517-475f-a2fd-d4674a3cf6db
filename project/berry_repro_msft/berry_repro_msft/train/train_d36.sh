SPI=96
IPB=64
MAX_TOKENS=32768
INVERSE_TOKEN_COST=32768

# Change to orngcresco if you run in uksouth.
ORNGCRESCO=orngscuscresco
CKPT="az://$ORNGCRESCO/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted"

CMD=(
oaipkg run qstar.run_experiment
nostrict
name=$(date +%Y%m%d%H%M%S)-berry-dummy

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
peashooter.sampling_use_ev3=True
root_config="mini.root.dev driver_rpc_timeout=6000"
policy.initial_checkpoint="$CKPT"
policy.n_gpus=120
policy.is_multimodal=True
policy.encoding_name=orion_200k
defaults.target_samples_per_instance=$SPI
defaults.inverse_token_cost=$INVERSE_TOKEN_COST
defaults.instances_per_batch=$IPB
defaults.n_ctx=$MAX_TOKENS

:presets:tr_math
:presets:tr_code
...dataset_container=$ORNGCRESCO
...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe
...harmony_constrained_sampling=True
...max_num_yields=100

save_every=10
seed=42

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=berry_repro
kafka_enable=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a train.log