#!/bin/bash

: <<'COMMENT'
REMEMBER TO SET CLUSTER AND TEAM TO YOUR ALLOCATION

RUN THIS FIRST TO GET A PEASHOOTER CLUSTER (here we show the smallest possible cluster):
RCALL_ASK_TO_REMOVE_DIR=1 python -m qstar.peashooter.run_peashooter \
    ...rapid_id=derisk-d28 \
    rapid.cluster.name=<TODO> \
    rapid.cluster.team=<TODO> \
    rapid.cluster.priority=team-high \
    rapid.pull_git_on_restart=False \
    n_gpus_per_sampler=8 \
    sampling_jobs=1 \
    n_train_pods=6 \
    cpu_controller=False \
    num_controller_nodes=1 \
    security_profile=msft-orng

THEN ssh+tux INTO TRAIN NODE AND RUN THIS
bash ~/code/glass/project/berry_repro_msft/berry_repro_msft/train/train_d28.sh

COMMENT

SPI=96
IPB=64
MAX_TOKENS=65536
INVERSE_TOKEN_COST=4096

# Change to orngcresco if you run in uksouth.
ORNGCRESCO=orngscuscresco
CKPT="az://$ORNGCRESCO/models/snapshots/abv1-nv4-derisk-prior-decrypted2"

CMD=(
oaipkg run qstar.run_experiment
nostrict
name=$(date +%Y%m%d%H%M%S)-d36-prior-derisk-run

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
peashooter.sampling_use_ev3=True
root_config="mini.root.dev driver_rpc_timeout=6000"
policy.initial_checkpoint="$CKPT"
policy.n_gpus=160
policy.is_multimodal=True
policy.encoding_name=orion_200k
defaults.target_samples_per_instance=$SPI
defaults.inverse_token_cost=$INVERSE_TOKEN_COST
defaults.instances_per_batch=$IPB
defaults.n_ctx=$MAX_TOKENS

:berry_repro_msft.train.presets:tr_math
:berry_repro_msft.train.presets:tr_code
:berry_repro_msft.train.presets:tr_progcomp
:berry_repro_msft.train.presets:tr_stem
:berry_repro_msft.train.presets:tr_if
...dataset_container=$ORNGCRESCO
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
...harmony_constrained_sampling=True
...max_num_yields=100

save_every=5
seed=42

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=berry_repro
kafka_enable=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a train.log