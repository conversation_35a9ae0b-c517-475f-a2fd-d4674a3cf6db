"""
Schedules peashooter, automatically starts Q* training on MATH and automatically
shuts down the cluster after 1 step of training.

Usage from the laptop:
```
pychz qstart_autostart_example.chz.py -e launch_cluster \
    cluster=<your-cluster>
```

Example with autostart:
```
pychz eval_d36.chz.py -e launch_cluster \
    cluster=prod-southcentralus-hpe-2 \
    team=team-moonfire-genaicore \
    priority=team-critical \
    rapid_id=d36eval8-del \
    experiment_id=weijianxu-20250829055348-d36-prior-derisk-run-mix5
```
to see the job's stdout, you can then SSH into the trainer and run:
```
tmux attach -t 0
```


Example with manual start:
```
pychz train_d28.chz.py -e launch_cluster \
    [...] \
    autostart=False
```
And then on the trainer (preferably in a tmux session):
```
pychz train_d28.chz.py experiment_name=math17k base_storage=orngcresco
```

See README.md for more details.
"""

import datetime
import os
from pathlib import Path

from oaipkg.poly import polyrepo_for_path

GLASS_ROOT = polyrepo_for_path(Path(__file__))

# TODO: expose in a separate library.
CLUSTER_TO_STORAGE_MAP = {
    "prod-uksouth-7": "orngcresco",
    "prod-uksouth-8": "orngcresco",
    "prod-southcentralus-hpe-2": "orngscuscresco",
    "prod-southcentralus-hpe-3": "orngscuscresco",
    "prod-southcentralus-hpe-5": "orngscuscresco",
    "stage-southcentralus-hpe-1": "orngscuscresco",
}


def launch_cluster(
    cluster,
    experiment_id,
    priority="low-priority",
    team=None,
    rapid_id=None,
    base_storage=None,
    autostart=True,
    size="minimal",
):
    if rapid_id is None:
        rapid_id = experiment_name + "-eval"
    if base_storage is None:
        base_storage = CLUSTER_TO_STORAGE_MAP[cluster]

    chunks = ["python -m qstar.peashooter.run_peashooter"]
    if team:
        chunks.append(f"rapid.cluster.team={team}")
    chunks.append(
        f"""
rapid.cluster.name={cluster}
rapid.cluster.priority={priority}
rapid.rapid_id={rapid_id}
security_profile=msft-orng
extra_rcall_config={GLASS_ROOT}/project/berry_repro_msft/berry_repro_msft/rcall_config.py

n_gpus_per_sampler=8
n_train_gpus=0
eval_only=True
"""
    )

    if size == "minimal":
        chunks.append(
            f"""
sampling_jobs=2

cpu_controller=True
n_cpu_cores_for_controller=4
num_controller_nodes=2
"""
        )
    elif size == "standard":
        chunks.append(
            f"""
sampling_jobs=4

# Looks like CPU SKU has too little memory for the eval controller.
cpu_controller=False
num_controller_nodes=1
"""
        )

    if autostart:
        chunks.append(
            f"""
autostart_file={__file__}
autostart_args="experiment_id={experiment_id} base_storage={base_storage}"
autostart_max_attempts=3

# Local validation doesn't work as of Sept 2025, so skip it.
# See Teams thread in Project Orange (CoreAI only): https://aka.ms/AAxxib4
autostart_skip_validation=True
# Starting from August FI, use the following flag instead:
# autostart_skip_local_test=True
"""
        )

    return "\n".join(chunks) + "\n"


def run_eval(experiment_id, base_storage="orngcresco", size=None):
    CKPT = f"az://{base_storage}/models/snapshots/abv1-nv4-derisk-prior-decrypted2"
    JUICE = 768
    chunks = []
    chunks.append(
        f"""
oaipkg run qstar.run_eval
nostrict
name="{datetime.datetime.now():%Y%m%d%H%M%S}-d36-{JUICE}-peaval"
experiment_id={experiment_id}

auto_inherit_training_args=False
load.restore_from_all_clusters=False
policy.initial_checkpoint="{CKPT}"

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
root_config="mini.root.dev driver_rpc_timeout=6000"
policy.is_multimodal=True
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget
...n_ctx=131072

...override_reward_multiplier={JUICE}

...harmony_constrained_sampling=True
...max_num_yields=100

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=8
peashooter.num_sampling_processes=16
...sampling_use_ev3=True

policy.encoding_name=orion_200k
eval_settings.eval_initial_policy=True
eval_settings.exit_on_no_new_checkpoint=True
eval_settings.eval_every=10
eval_settings.min_step=10
eval_settings.max_step=500

:berry_repro_msft.eval.presets:ev_genai_index
#:berry_repro_msft.eval.presets:ev_math
#:berry_repro_msft.eval.presets:ev_stem
#:berry_repro_msft.eval.presets:ev_code
#:berry_repro_msft.eval.presets:ev_if
...dataset_container={base_storage}

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=berry_repro
kafka_enable=False
enable_slackbot=False
"""
    )
    return "\n".join(chunks)


_CMD = run_eval
