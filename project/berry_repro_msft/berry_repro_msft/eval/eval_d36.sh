# Change to orngcresco if you run in uksouth.
ORNGCRESCO=orngscuscresco
CKPT="az://$ORNGCRESCO/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted"
JUICE=768

CMD=(
oaipkg run qstar.run_eval
nostrict
name=$(date +%Y%m%d%H%M%S)-d36-$JUICE-peaval

auto_inherit_training_args=False
load.restore_from_all_clusters=False
eval_settings.checkpoint_dir="$CKPT"
policy.initial_checkpoint="$CKPT"

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
root_config="mini.root.dev driver_rpc_timeout=6000"
policy.is_multimodal=True
...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe
...n_ctx=131072
...override_reward_multiplier=$JUICE
...max_num_yields=100

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=8
peashooter.num_sampling_processes=16
...sampling_use_ev3=True

policy.encoding_name=orion_200k
eval_settings.eval_initial_policy=True
eval_settings.eval_every=10
eval_settings.min_step=10
eval_settings.max_step=150
eval_settings.exit_on_no_new_checkpoint=False

:berry_repro_msft.eval.presets:ev_genai_index
#:berry_repro_msft.eval.presets:ev_math
#:berry_repro_msft.eval.presets:ev_stem
#:berry_repro_msft.eval.presets:ev_code
...dataset_container=$ORNGCRESCO

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=berry_repro
kafka_enable=False
enable_slackbot=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log