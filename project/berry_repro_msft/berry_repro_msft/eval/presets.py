import os
import berry.preset_utils

ORNGCRESCO = os.getenv("ORNGCRESCO", "orngcresco")


gpt4o_grader_service = \
    [
        "dataset.grader.renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
        "dataset.grader.grader_service=TokenCompleterGraderService",
        "dataset.grader.grader_service.redis_config_id=autograder-scallion-ppo-engine-defaults",
        "dataset.grader.grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
        f"dataset.grader.grader_service.token_completer.topic_or_snapshot=az://{ORNGCRESCO}/models/snapshots/4o-tt-v5-transferred-2025-04-09-decrypted",
        "dataset.grader.grader_service.token_completer.topic_mode_or_user=4o-grader",
        "dataset.grader.grader_service.token_completer.bus_line=bus",
    ]


gpt5_mini_grader_service = \
    [
        "dataset.grader.renderer_name=harmony_v4.0.16_berry_v3_128k_orion_mm_no_budget_truncate",
        "dataset.grader.grader_service=TokenCompleterGraderService",
        "dataset.grader.grader_service.redis_config_id=autograder-ref-tbv3_cotograder_0202",
        "dataset.grader.grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
        f"dataset.grader.grader_service.token_completer.topic_or_snapshot=az://{ORNGCRESCO}/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted",
        "dataset.grader.grader_service.token_completer.topic_mode_or_user=gpt5-mini-grading",
        "dataset.grader.grader_service.token_completer.bus_line=bus",
        "dataset.grader.grader_service.token_completer.qos_type=bus_token_completer:QoSType.ROUND_ROBIN_BY_POD",
        "dataset.grader.answer_max_excess_characters=None",
        "dataset.grader.answer_length_multiplier=None",
        "dataset.grader.answer_length_limit=None",
        "dataset.grader.grader_max_tokens=32768",
    ]


# Math evaluation presets
ev_aime24 = berry.preset_utils.eval_dataset_preset(
    [
        "dataset=HarmonyCompletionDatasetConfig",
        "dataset.dataset_id=data.xidai.mathgen.aime_2024.test",
        "dataset.grader=qstar.graders.normalized_sympy_grader:NormalizedSympyGrader",
        "dataset.override_target_samples_per_instance=1",
        "dataset.variant_producer=VarDiscountingVariantProducer",
        *gpt4o_grader_service,
    ]
)
ev_aime25 = berry.preset_utils.eval_dataset_preset(
    [
        "dataset=HarmonyCompletionDatasetConfig",
        "dataset.dataset_id=data.xidai.mathgen.aime_2025.test",
        "dataset.grader=qstar.graders.normalized_sympy_grader:NormalizedSympyGrader",
        "dataset.override_target_samples_per_instance=1",
        "dataset.variant_producer=VarDiscountingVariantProducer",
        *gpt4o_grader_service,
    ]
)
ev_hmmt25 = berry.preset_utils.eval_dataset_preset(
    [
        "dataset=HarmonyCompletionDatasetConfig",
        "dataset.dataset_id=data.xidai.mathgen.hmmt_feb_2025.test",
        "dataset.grader=qstar.graders.normalized_sympy_grader:NormalizedSympyGrader",
        "dataset.override_target_samples_per_instance=1",
        "dataset.variant_producer=VarDiscountingVariantProducer",
        *gpt4o_grader_service,
    ]
)
ev_math = berry.preset_utils.compose_presets(ev_aime24, ev_aime25, ev_hmmt25)


# STEM evaluation presets
ev_gpqa_diamond = berry.preset_utils.eval_dataset_preset(
    [
        "dataset=HarmonyCompletionDatasetConfig",
        "dataset.dataset_id=data.weijianxu.taskgen.gpqa.gpqa-diamond.psa.test",
        "dataset.datapoint_converters.0.name=qstar.common.datapoint_converters:convert_gpqa_to_mathgen",
        "dataset.grader=berry_repro_msft.data.taskgen.gpqa_diamond_graders:FixedMultipleChoiceAutoGrader",
        "dataset.grader.channels_for_answer=final",
        "dataset.override_target_samples_per_instance=8",
        "dataset.variant_producer=VarDiscountingVariantProducer",
        *gpt4o_grader_service
    ]
)
ev_gpqa_diamond_per_category = berry.preset_utils.compose_presets(
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=HarmonyCompletionDatasetConfig",
            "dataset.dataset_id=data.weijianxu.taskgen.gpqa.gpqa-diamond.psa.test_per_category.biology",
            "dataset.datapoint_converters.0.name=qstar.common.datapoint_converters:convert_gpqa_to_mathgen",
            "dataset.grader=berry_repro_msft.data.taskgen.gpqa_diamond_graders:FixedMultipleChoiceAutoGrader",
            "dataset.grader.channels_for_answer=final",
            "dataset.override_target_samples_per_instance=8",
            "dataset.variant_producer=VarDiscountingVariantProducer",
            *gpt4o_grader_service
        ]
    ),
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=HarmonyCompletionDatasetConfig",
            "dataset.dataset_id=data.weijianxu.taskgen.gpqa.gpqa-diamond.psa.test_per_category.chemistry",
            "dataset.datapoint_converters.0.name=qstar.common.datapoint_converters:convert_gpqa_to_mathgen",
            "dataset.grader=berry_repro_msft.data.taskgen.gpqa_diamond_graders:FixedMultipleChoiceAutoGrader",
            "dataset.grader.channels_for_answer=final",
            "dataset.override_target_samples_per_instance=8",
            "dataset.variant_producer=VarDiscountingVariantProducer",
            *gpt4o_grader_service
        ]
    ),
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=HarmonyCompletionDatasetConfig",
            "dataset.dataset_id=data.weijianxu.taskgen.gpqa.gpqa-diamond.psa.test_per_category.physics",
            "dataset.datapoint_converters.0.name=qstar.common.datapoint_converters:convert_gpqa_to_mathgen",
            "dataset.grader=berry_repro_msft.data.taskgen.gpqa_diamond_graders:FixedMultipleChoiceAutoGrader",
            "dataset.grader.channels_for_answer=final",
            "dataset.override_target_samples_per_instance=8",
            "dataset.variant_producer=VarDiscountingVariantProducer",
            *gpt4o_grader_service
        ]
    )
)
ev_stem_iid = berry.preset_utils.compose_presets(
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=HarmonyCompletionDatasetConfig",
            # "dataset.dataset_id=data.weijianxu.se1m_v2.07142025.test_first_100_07142025_daniel_physics_07_split_test_train",
            # "dataset.dataset_id=data.xidai.taskgen.oss.chemistryqa.test",
            # "dataset.dataset_id=data.xidai.taskgen.oss.chemdata.easy.test",
            # "dataset.dataset_id=data.xidai.taskgen.oss.chemdata.medium.test",
            "dataset.dataset_id=data.xidai.taskgen.oss.chemdata.hard.test",
            "dataset.override_target_samples_per_instance=4",
            "dataset.grader=berry_repro_msft.data.taskgen.se_graders:SETaskgenGrader",
            "dataset.variant_producer=VarDiscountingVariantProducer",
            *gpt5_mini_grader_service
        ]
    ),
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=HarmonyCompletionDatasetConfig",
            "dataset.dataset_id=data.weijianxu.mai.hq_stem.pass_rate_filtered_accgt09_08052025.test_first_100",
            "dataset.override_target_samples_per_instance=4",
            "dataset.grader=berry_repro_msft.data.taskgen.se_graders:SETaskgenGrader",
            "dataset.datapoint_converters.0.name=berry_repro_msft.data.mai.converters:convert_mai_to_taskgen",
            "dataset.variant_producer=VarDiscountingVariantProducer",
            *gpt5_mini_grader_service
        ]
    )
)
ev_hle = berry.preset_utils.eval_dataset_preset(
    [
        "dataset=HarmonyCompletionDatasetConfig",
        "dataset.dataset_id=data.xidai.taskgen.hle.test.text_only",
        "dataset.override_target_samples_per_instance=4",
        "dataset.grader=qstar.graders.taskgen_grader:TaskgenGrader",
        "dataset.variant_producer=VarDiscountingVariantProducer",
        *gpt5_mini_grader_service
    ]
)
ev_stem = berry.preset_utils.compose_presets(ev_gpqa_diamond, ev_stem_iid, ev_hle)


# Coding evaluation presets
ev_sbv = berry.preset_utils.eval_dataset_preset(
    [
        "dataset=deep_swe_eval_msft.swe_bench.peaval.bash.dataset_config:SWEBenchVerifiedDatasetConfigBASH",
        "dataset.override_target_samples_per_instance=1",
        "dataset.variant_producer=VarDiscountingVariantProducer",
    ]
)
ev_codeforces_python = berry.preset_utils.eval_dataset_preset(
    [
        "dataset=HarmonyCompletionDatasetConfig",
        "dataset.dataset_id=data.thopo.codeforces.mai_verifiable_data_reformatted.python.test_no_signature",
        "dataset.grader=qstar.graders.progcomp_caas.code_forces.code_forces_grader:CodeForcesGrader",
        "dataset.grader.extractor=qstar.graders.answer_extraction:LongestCodeBlockExtractor",
        "dataset.variant_producer=CompositeVariantProducer",
        "dataset.variant_producer.num_variants_to_subsample=None",
        "dataset.variant_producer.variant_producers.0=VarDiscountingVariantProducer",
        "dataset.variant_producer.variant_producers.0.override_reward_multiplier=768",
        "dataset.variant_producer.variant_producers.1=ToolVariantProducer",
        "dataset.variant_producer.variant_producers.1.tool_variants.0=qstar.presets.progcomps_train:NoToolVariant",
        "dataset.override_target_samples_per_instance=1",
    ]
)
ev_code = berry.preset_utils.compose_presets(ev_sbv, ev_codeforces_python)


# IF evaluation presets
ev_if_iid = berry.preset_utils.eval_dataset_preset(
    [
        "dataset=HarmonyCompletionDatasetConfig",
        "dataset.dataset_id=data.xidai.if.gen5_25k.test",
        "dataset.override_target_samples_per_instance=4",
        "dataset.grader=qstar.graders.rlhf_grader.berry_grader:BerryGrader",
        "dataset.grader.prompt_template=contains_rubrics",
        "dataset.grader.prompt_constructor.strip_first_model_identity=True",
        "dataset.grader.prompt_constructor.use_rubric_as_side_information=True",
        "dataset.grader.prompt_constructor.rubric_key_in_metadata=gt_answer",
        "dataset.grader.grade_parser=qstar.graders.rlhf_grader.berry_grader.grade_parser:MatchRubricsGradeParser",
        "dataset.grader.grade_parser.is_correct_threshold=0.8",
        "dataset.grader.harmony_renderer=harmony_v4.0.16_berry_v3_128k_orion_mm_no_budget_truncate",
        "dataset.variant_producer=VarDiscountingVariantProducer",
        *gpt5_mini_grader_service[1:8],
    ]
)
ev_if = berry.preset_utils.compose_presets(ev_if_iid)


# GenAI-Index evaluation presets
ev_genai_index = berry.preset_utils.compose_presets(ev_aime25, ev_gpqa_diamond, ev_sbv, ev_codeforces_python)