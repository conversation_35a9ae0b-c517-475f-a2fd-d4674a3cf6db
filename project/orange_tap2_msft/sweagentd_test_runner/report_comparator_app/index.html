<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Compare multiple test report HTMLs side by side with detailed case-level diff highlighting and log viewing.">
  <title>Padawan P0 Basic Report Comparator</title>
  <!-- original report styles -->
  <style>
  /* typography and layout */
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
  html,body{font-family:Inter, ui-sans-serif, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial; font-size:16px;}
  body { padding: 20px 0; }
  header.container { background: linear-gradient(180deg, rgba(11,16,32,0.98), rgba(11,16,32,0.9)); padding: 18px 16px; border-radius: 8px; display:flex; flex-direction:column; align-items:center; text-align:center; }
  h1.h-title { font-size: 30px; margin: 0; }
  p.h-desc { margin: 6px 0 0 0; color: var(--muted); font-size: 14px; }
  .controls-row { display:flex; gap:12px; align-items:center; margin-top:12px; }
  #fileInput { background: var(--panel); color: var(--text); padding: 8px; border-radius: 8px; border: 1px solid var(--border); }
  .file-chooser { display:flex; gap:12px; align-items:center; }
  .file-list { color:var(--muted); font-size:13px; max-width:420px; overflow:hidden; text-overflow:ellipsis; }
  .file-btn { display:inline-flex; gap:8px; align-items:center; }
  .summary-panel { display:flex; gap:12px; flex-wrap:wrap; margin-top:12px; }
  .summary-tile { background: linear-gradient(180deg, var(--panel-2), var(--panel)); padding: 12px 16px; border-radius: 8px; min-width: 160px; }
  .summary-tile .title { font-weight:600; font-size:13px; color:var(--muted); }
  .summary-tile .value { font-size:20px; font-weight:700; margin-top:6px; }
  .table thead th { font-size: 16px; padding: 16px 14px; color: var(--text); }
  .table td { font-size: 13px; }
  .badge { padding: 2px 8px; border-radius: 999px; background: transparent; }
  .badge.pass { color: var(--pass); }
  .badge.fail { color: var(--fail); }
  .badge.invalid { color: var(--invalid); }
  .search-input { padding:8px 12px; border-radius:8px; border:1px solid var(--border); background:var(--panel); color:var(--text); }
  .btn-ghost { background: transparent; border: 1px solid var(--border); color:var(--text); padding:8px 10px; border-radius:8px; cursor:pointer; }
  /* modal */
  .modal-backdrop{position:fixed;inset:0;background:rgba(0,0,0,0.6);display:none;align-items:center;justify-content:center;z-index:2000}
  .modal{background:var(--panel);padding:16px;border-radius:10px;max-width:900px;width:95%;max-height:80vh;overflow:auto;border:1px solid var(--border)}
  .modal pre{white-space:pre-wrap;color:var(--text);}

:root {
  --bg: #0b1020;        /* deep navy */
  --panel: #111731;     /* card */
  --panel-2: #0f142b;   /* darker */
  --text: #e7ecf3;      /* near-white */
  --muted: #9aa7bd;
  --pass: #22c55e;      /* green */
  --fail: #ef4444;      /* red */
  --invalid: #ffa500;   /* orange */
  --invalid-bg: #ff8c00; /* darker orange for background */
  --border: #253056;
}
* { box-sizing: border-box; }
html, body { margin:0; padding:0; font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji","Segoe UI Emoji"; background: var(--bg); color: var(--text); }
header { position: sticky; top:0; z-index: 10; backdrop-filter: blur(8px); background: linear-gradient(180deg, rgba(11,16,32,0.9), rgba(11,16,32,0.6)); border-bottom: 1px solid var(--border); }
header.banner-offset { top: 50px; }
.container { max-width: 1200px; margin: 0 auto; padding: 16px; }
.h-row { display:flex; align-items:center; justify-content: space-between; gap:12px; }
.h-title { font-size: 20px; font-weight: 700; }
.h-sub { color: var(--muted); font-size: 12px; }
.chips { display:flex; gap:8px; flex-wrap: wrap; }
.chip { padding: 6px 10px; border: 1px solid var(--border); border-radius: 999px; font-size: 12px; cursor: pointer; user-select:none; background: var(--panel); }
.chip.active { outline: 2px solid #fff2; }
.chip.invalid-warning { background: var(--invalid-bg); color: #000; border-color: var(--invalid); font-weight: bold; animation: pulse-warning 2s infinite; box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.3); }
@keyframes pulse-warning { 0% { box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.3); } 50% { box-shadow: 0 0 0 6px rgba(255, 165, 0, 0.6); } 100% { box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.3); } }
.controls { display:flex; gap:8px; }
.btn { background: var(--panel); border: 1px solid var(--border); color: var(--text); padding: 8px 12px; border-radius: 10px; cursor:pointer; font-size: 13px; }
.btn:hover { background: var(--panel-2); }
.btn.active { background: var(--fail); border-color: var(--fail); }
.banner { position: fixed; top: 0; left: 0; right: 0; z-index: 20; background: var(--invalid); color: #000; padding: 12px 16px; display: flex; align-items: center; justify-content: space-between; font-size: 14px; font-weight: 600; }
.banner .close-btn { background: transparent; border: none; color: #000; font-size: 18px; font-weight: bold; cursor: pointer; padding: 4px; }
.banner.hidden { display: none; }
.invalid-tooltip { position: relative; cursor: help; }
.invalid-tooltip:hover::after { content: "INVALID TEST CASES: The checks from these test cases are not included in totals. Click View Log for details."; position: absolute; bottom: -35px; left: 50%; transform: translateX(-50%); background: var(--panel); color: var(--text); padding: 8px 12px; border-radius: 6px; font-size: 11px; white-space: nowrap; z-index: 15; border: 1px solid var(--border); }
main { padding: 16px; }
.case { border: 1px solid var(--border); border-radius: 14px; background: linear-gradient(180deg, var(--panel), var(--panel-2)); margin-bottom: 14px; }
.case[hidden] { display:none !important; }
.case details { border-radius: 14px; }
.case summary { list-style: none; cursor: pointer; padding: 14px 16px; display:flex; align-items:center; gap:12px; }
.case summary::-webkit-details-marker { display:none; }
.case .title { font-weight: 700; font-size: 15px; }
.tag { padding: 2px 8px; border-radius: 999px; font-size: 11px; border: 1px solid var(--border); }
.dot { width: 10px; height: 10px; border-radius: 999px; display:inline-block; }
.dot.pass { background: var(--pass); }
.dot.fail { background: var(--fail); }
.dot.invalid { background: var(--invalid); }
.meta { color: var(--muted); font-size: 12px; margin-left: auto; display:flex; gap:14px; align-items:center; }
.table { width: 100%; border-collapse: collapse; }
.table th, .table td { text-align: left; padding: 10px 12px; border-bottom: 1px solid var(--border); vertical-align: top; }
.table th { position: sticky; top: 66px; background: var(--panel); z-index: 5; font-size: 12px; color: var(--muted); }
.badge { font-weight: 600; }
.badge.pass { color: var(--pass); }
.badge.fail { color: var(--fail); }
.badge.invalid { color: var(--invalid); }

.footer { color: var(--muted); font-size: 12px; padding: 16px; text-align: center; }
.search { width: 280px; background: var(--panel); border: 1px solid var(--border); border-radius: 10px; padding: 8px 12px; color: var(--text); }

  /* layout tweaks */
  .container { padding-left: 20px; padding-right: 20px; }
  main.container { padding-top: 20px; }

  /* summary tiles */
  .summary-panel { gap:16px; }
  .summary-tile { display:flex; flex-direction:column; gap:6px; align-items:flex-start; }
  .summary-tile .value { display:flex; gap:10px; align-items:center; }
  .score-pill { min-width:40px; text-align:center; padding:6px 10px; border-radius:999px; color:#000; font-weight:700; }
  .score-pass { background:var(--pass); }
  .score-fail { background:var(--fail); }
  .score-invalid { background:var(--invalid); }

  /* table layout */
  .results-wrapper { overflow:auto; padding-top:12px; }
  .table { width:100%; border-collapse: collapse; table-layout: fixed; }
  .table th, .table td { padding: 12px 14px; vertical-align: middle; word-break: break-word; }
  .table th:first-child, .table td:first-child { width: 240px; max-width:240px; white-space:normal; }
  .table th:nth-child(2), .table td:nth-child(2) { width: 260px; }
  .table td { background: transparent; }

  .overall-col { width:120px; text-align:left; }

  /* stronger diff highlight */
  tr.diff { box-shadow: inset 6px 0 0 0 rgba(250,200,30,0.12); background: linear-gradient(90deg, rgba(255,255,255,0.02), transparent); }
  tr.diff td:not(:first-child) { border-left: 4px solid rgba(250,200,30,0.9); }
  tr.diff td:first-child { background: transparent !important; }

  /* badges and chips */
  .chip, .summary-chip { display:inline-flex; gap:8px; align-items:center; padding:8px 10px; border-radius:8px; }
  .summary-chip .badge { margin-left:6px; }

  /* modal styles */
  .modal pre { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, 'Roboto Mono', monospace; font-size:13px; color:var(--text); }
  .modal .modal-header { display:flex; justify-content:space-between; align-items:center; margin-bottom:8px; }

  /* responsive */
  @media (max-width:900px){
    .table th, .table td { padding:10px 8px; }
    .table th:first-child, .table td:first-child { width: 160px; }
  }

  /* filename cell truncation */
  .fname { max-width: 220px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display:inline-block; vertical-align: middle; }
  .fname:hover { overflow: visible; white-space: normal; background: rgba(0,0,0,0.35); padding: 6px 8px; border-radius:6px; }

  /* improved Case / Subtask columns */
  .case-col { font-weight: 800; font-size: 16px; color: var(--text); }
  .subtask-col { font-weight: 700; font-size: 15px; color: var(--muted); }

  /* status cells: bold and colored */
  td.pass { color: var(--pass); font-weight: 800; font-size: 14px; }
  td.fail { color: var(--fail); font-weight: 800; font-size: 14px; }
  td.invalid { color: var(--invalid); font-weight: 800; font-size: 14px; }

  /* ensure score pills are bold */
  .score-pill { font-weight: 800; font-size: 14px; }

  /* summary tile improvements */
  .summary-panel { margin-bottom: 12px; }
  .summary-tile { min-width: 180px; }
  .summary-tile .title { font-size:13px; color:var(--muted); }
  .summary-tile .value { margin-top:6px; display:flex; gap:8px; align-items:center; }
  .score-pill { min-width:36px; padding:6px 8px; border-radius:999px; font-weight:700; color:#000; }
  .score-pass { background: var(--pass); }
  .score-fail { background: var(--fail); }
  .score-invalid { background: var(--invalid); }

  /* small visual polish for table header file names */
  th .fname { display:block; }

  /* upload card */
  .upload-card { background: linear-gradient(180deg, rgba(17,23,49,0.9), rgba(15,20,43,0.9)); border: 1px solid var(--border); padding:18px; border-radius:10px; display:flex; gap:16px; align-items:center; justify-content:space-between; }
  .upload-left { display:flex; gap:12px; align-items:center; }
  .upload-title { font-size:18px; font-weight:700; }
  #fileInput { padding:12px 16px; font-size:15px; border-radius:8px; }
  .file-list { font-size:14px; color:var(--muted); max-width:640px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; }
  .choose-btn { background:var(--panel-2); color:var(--text); border:1px solid var(--border); padding:10px 14px; border-radius:8px; cursor:pointer; }
  .upload-help { color:var(--muted); font-size:13px; }
  .overall-row td { background: linear-gradient(180deg, rgba(255,255,255,0.01), transparent); font-weight:600; }

  /* hide original orange invalid banner/chips added by reports */
  .chip.invalid-warning, .banner { display: none !important; }

  /* open-log link styling to replace default blue */
  .open-log-btn { color: var(--muted); background: rgba(255,255,255,0.03); padding:6px 8px; border-radius:6px; border:1px solid var(--border); text-decoration:none; font-weight:700; font-size:13px; }
  .open-log-btn:hover { color: var(--text); background: rgba(255,255,255,0.06); }
  </style>
</head>
<body>
  <header class="container">
    <h1 class="h-title">Padawan P0 Basic Tests Report Comparator</h1>
  <p class="h-desc">Upload two or more report HTML files to compare each test case and its subtasks across runs.</p>
  </header>
  <div class="container">
    <div class="upload-card">
      <div class="upload-left">
        <div>
          <div class="upload-title">Choose test report files</div>
          <div class="upload-help">Select two or more HTML test reports to compare results.</div>
        </div>
        <div class="file-list" id="fileList">No files chosen</div>
      </div>
      <div>
        <label class="choose-btn">
          <input type="file" id="fileInput" multiple accept=".html" style="display:none" />
          Select files
        </label>
      </div>
    </div>
    <div style="display:flex;gap:12px;margin-top:12px;align-items:center;">
      <input type="text" id="globalSearch" class="search-input" placeholder="Search case or subtask..." />
      <button id="diffOnly" class="btn-ghost">Show Diff Only</button>
    </div>
    <div id="results"></div>
  </div>

  <script>
    const input = document.getElementById('fileInput');
    const resultsDiv = document.getElementById('results');
    const logScripts = [];

    input.addEventListener('change', () => {
      const files = Array.from(input.files);
      // show chosen file names
      const fileList = document.getElementById('fileList');
      if(fileList){ fileList.textContent = files.length? files.map(f=>f.name).join(', ') : 'No files chosen'; }
      const reports = [];
      let processed = 0;
      files.forEach(file => {
        const reader = new FileReader();
        reader.onload = e => {
          const doc = new DOMParser().parseFromString(e.target.result, 'text/html');
          const cases = {};
          // collect inlined log scripts
          doc.querySelectorAll('script[type="text/plain"]').forEach(s => {
            logScripts.push({ id: s.id, content: s.textContent });
          });
          doc.querySelectorAll('.case').forEach(node => {
            const caseName = node.getAttribute('data-case-name');
            const subtasks = {};
            node.querySelectorAll('table.table tbody tr').forEach(row => {
              const cols = row.querySelectorAll('td');
              const subName = cols[1] ? cols[1].textContent.trim() : cols[0].textContent.trim();
              const status = row.getAttribute('data-status');
              subtasks[subName] = status;
            });
            const logButton = node.querySelector('button')?.outerHTML || '';
            cases[caseName] = { subtasks, logButton };
          });
          reports.push({ name: file.name, cases });
          processed++;
          if (processed === files.length) renderComparison(reports);
        };
        reader.readAsText(file);
      });
    });

    function renderComparison(reports) {
      // append the collected log scripts into the page
      logScripts.forEach(ls => {
        const scr = document.createElement('script');
        scr.type = 'text/plain'; scr.id = ls.id;
        scr.textContent = ls.content;
        document.body.appendChild(scr);
      });
      resultsDiv.innerHTML = '';
      // overall summary
      const summary = document.createElement('div'); summary.className = 'summary-panel';
      reports.forEach(r => {
        const allSt = Object.values(r.cases).flatMap(c => Object.values(c.subtasks));
        const pass = allSt.filter(s=>s==='pass').length;
        const fail = allSt.filter(s=>s==='fail').length;
        const invalid = allSt.filter(s=>s==='invalid').length;
        const tile = document.createElement('div'); tile.className='summary-tile';
        tile.innerHTML = `<div class="title" title="${escapeHtml(r.name)}">${escapeHtml(truncateName(r.name, 36))}</div><div class="value"><span class="score-pill score-pass" title="Passes: ${pass}">${pass}</span><span class="score-pill score-fail" title="Fails: ${fail}">${fail}</span>${invalid? `<span class="score-pill score-invalid" title="Invalid: ${invalid}">${invalid}</span>`: ''}</div>`;
        summary.appendChild(tile);
      });
      resultsDiv.appendChild(summary);
      // define openLog to display logs
      window.openLog = function(id) {
        const txt = document.getElementById(id)?.textContent || 'Log not found';
        const w = window.open('', '_blank');
        w.document.write('<pre>' + txt.replace(/</g, '&lt;') + '</pre>');
      };
      const caseNames = Array.from(new Set(reports.flatMap(r => Object.keys(r.cases)))).sort();
      const table = document.createElement('table'); table.className = 'table';
      const thead = document.createElement('thead');
      const headerRow = document.createElement('tr');
      const headerHtml = ['<th>Case</th>','<th>Subtask</th>'];
      reports.forEach(r=>{
        const safe = escapeHtml(r.name);
        const short = escapeHtml(truncateName(r.name, 36));
        headerHtml.push(`<th><div class="fname" title="${safe}">${short}</div></th>`);
      });
      headerRow.innerHTML = headerHtml.join('');
      thead.appendChild(headerRow); table.appendChild(thead);
      const tbody = document.createElement('tbody');
      caseNames.forEach(caseName => {
        const allSub = Array.from(new Set(reports.flatMap(r => Object.keys((r.cases[caseName]?.subtasks) || {})))).sort();
        allSub.forEach((sub, i) => {
          const tr = document.createElement('tr');
          // determine if diff across reports
          const statuses = reports.map(r=>r.cases[caseName]?.subtasks[sub]||'');
          if(new Set(statuses).size>1) { tr.classList.add('diff'); tr.title = 'Different results across reports'; }
          // build cells
          let rowHtml = `<td class="case-col">${i===0?escapeHtml(caseName):''}</td><td class="subtask-col">${escapeHtml(sub)}</td>`;
          reports.forEach(r=>{
            const data = r.cases[caseName]||{};
            const st = data.subtasks?.[sub]||'';
            // render status cell (View Log links are shown only in the Overall row)
            rowHtml += `<td class="${st}" title="Status: ${escapeHtml(st || 'N/A')}">${st}</td>`;
           });
           tr.innerHTML = rowHtml;
           tbody.appendChild(tr);
         });
        // after listing subtasks, add an overall sub-row for this case showing per-report totals
        const overallTr = document.createElement('tr'); overallTr.className='overall-row';
        overallTr.innerHTML = `<td class="case-col"></td><td class="subtask-col">Overall</td>`;
        reports.forEach(r=>{
          const stats = Object.values((r.cases[caseName]?.subtasks)||{});
          const p = stats.filter(s=>s==='pass').length;
          const f = stats.filter(s=>s==='fail').length;
          const inv = stats.filter(s=>s==='invalid').length;
          let statsHtml = `<span class="score-pill score-pass" title="Passes: ${p}">${p}</span> <span class="score-pill score-fail" title="Fails: ${f}">${f}</span>`;
          if(inv) statsHtml += ` <span class="score-pill score-invalid" title="Invalid: ${inv}">${inv}</span>`;
          // attempt to extract log id from saved button HTML
          let logHtml = '';
          const lb = r.cases[caseName] && r.cases[caseName].logButton ? r.cases[caseName].logButton : '';
          const m = lb && (lb.match(/openLog\('\s*([^'\)]+)\s*'/) || lb.match(/data-log=\"([^\"]+)\"/) || lb.match(/id=\"([^\"]+)\"/));
          const logId = m ? m[1] : '';
          if(logId) logHtml = ` <a href="#" class="open-log-btn" data-log="${logId}" title="View log">View Log</a>`;
          overallTr.innerHTML += `<td title="Passes: ${p}, Fails: ${f}${inv?(', Invalid: '+inv):''}">${statsHtml}${logHtml}</td>`;
         });
         tbody.appendChild(overallTr);
       });
      table.appendChild(tbody);
      resultsDiv.appendChild(table);
    }
    // wire search and diff toggle
    document.addEventListener('click', (e)=>{
      if(e.target && e.target.matches('.open-log-btn')){
        e.preventDefault();
        const id = e.target.getAttribute('data-log');
        openLog(id);
      }
    });
    document.getElementById('diffOnly')?.addEventListener('click', (ev)=>{
      const btn = ev.currentTarget;
      btn.classList.toggle('active');
      const show = !btn.classList.contains('active');
      document.querySelectorAll('table.table tbody tr').forEach(r=>{
        if(r.classList.contains('diff')) r.style.display = show? 'table-row' : 'none';
      });
    });
    document.getElementById('globalSearch')?.addEventListener('input', (ev)=>{
      const q = ev.target.value.toLowerCase();
      document.querySelectorAll('table.table tbody tr').forEach(r=>{
        const text = r.textContent.toLowerCase();
        r.style.display = text.includes(q)? 'table-row' : 'none';
      });
    });
    // modal elements for logs
    const modalBackdrop = document.createElement('div'); modalBackdrop.className='modal-backdrop';
    const modalBox = document.createElement('div'); modalBox.className='modal';
    modalBackdrop.appendChild(modalBox);
    document.body.appendChild(modalBackdrop);
    function showModal(html){ modalBox.innerHTML = html; modalBackdrop.style.display='flex'; }
    modalBackdrop.addEventListener('click',(e)=>{ if(e.target===modalBackdrop) modalBackdrop.style.display='none'; });
    window.openLog = function(id){ const txt = document.getElementById(id)?.textContent || 'Log not found'; showModal('<button class="btn-ghost" onclick="(this.closest(\'.modal-backdrop\').style.display=\'none\')">Close</button><pre>'+txt.replace(/</g,'&lt;')+'</pre>'); };
    // helper to add attribute-safe titles
    function safeTitle(text){ return escapeHtml(String(text || '')).replace(/\n/g,' '); }
    function escapeHtml(str){ return (str||'').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;').replace(/"/g,'&quot;'); }
    function truncateName(name, maxLen=30){ if(!name) return ''; if(name.length<=maxLen) return name; const pre = Math.floor((maxLen-3)/2); const suf = maxLen-3-pre; return name.slice(0,pre)+'...'+name.slice(name.length-suf); }
  </script>
  
 </body>
 </html>