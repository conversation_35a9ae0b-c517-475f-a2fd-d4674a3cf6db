#!/bin/bash

# ANSI color codes
BOLD_CYAN='\033[1;36m'
NC='\033[0m' # No Color

# Default test root folder
TEST_ROOT="$HOME/code/sweagentd/runtime"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --test-root)
      TEST_ROOT="$2"
      shift # past argument
      shift # past value
      ;;
    -h|--help)
      echo "Usage: $0 [--test-root PATH]"
      echo "  --test-root PATH    Path to the test root folder (default: \$HOME/code/sweagentd/runtime)"
      echo "  -h, --help          Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option $1"
      echo "Use $0 --help for usage information"
      exit 1
      ;;
  esac
done

echo "Using test root folder: $TEST_ROOT"

# Change to the test root directory
echo -e "${BOLD_CYAN}Change to the test root directory${NC}"
cd "$TEST_ROOT" || { echo "Test root directory not found: $TEST_ROOT"; exit 1; }

# Kill any existing Playwright MCP processes
echo -e "${BOLD_CYAN}Kill any existing Playwright MCP processes${NC}"
pkill -9 -f "@playwright/mcp@latest --port 1234"

# Start a new Playwright MCP server
echo -e "${BOLD_CYAN}Start a new Playwright MCP server${NC}"
npx @playwright/mcp@latest --port 1234 &

# Run the Vitest test with Playwright MCP enabled
echo -e "${BOLD_CYAN}Run the Vitest test with Playwright MCP enabled${NC}"
launch_datetime=$(date +%Y%m%d_%H%M%S)
npx vitest run test/evals/basic.test.ts --maxWorkers=5 --reporter=verbose 2>&1 | tee basic-test-$launch_datetime.log
echo -e "${BOLD_CYAN}The test log was saved to $TEST_ROOT/basic-test-$launch_datetime.log${NC}"

# Generate the test report
echo -e "${BOLD_CYAN}Generate the test report${NC}"
python $HOME/code/glass/project/orange_tap2_msft/orange_tap2_msft/generate_test_report.py --input $TEST_ROOT/basic-test-$launch_datetime.log --out $TEST_ROOT/basic-test-$launch_datetime.html
echo -e "${BOLD_CYAN}Test report was generated at $TEST_ROOT/basic-test-$launch_datetime.html${NC}"
