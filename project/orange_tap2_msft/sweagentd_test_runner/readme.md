# SweAgentD Test Runner

A comprehensive test runner and HTML report generator for the Padawan P0 Basic Test via OrangeTap.

## Overview

This tool provides automated testing capabilities for the P0 Basic Test with rich HTML report generation. It processes test logs from Vitest runs and generates beautiful, interactive HTML reports with embedded logs and detailed check results.

## Features

- **Automated Test Execution**: Runs the P0 Basic Test with Playwright MCP integration
- **Rich HTML Reports**: Generates standalone HTML reports with embedded logs and interactive filtering
- **Log Processing**: Parses both `.log` files and `.json` test results
- **Interactive UI**: Collapsible sections, status filters, search functionality, and detailed check views

## Files

- `run.sh` - Main test execution script
- `../orange_tap2_msft/orange_tap2_msft/generate_test_report.py` - Python script for generating HTML reports from test results
- `test_invalid_testcases.json` - Sample test data with invalid test cases
- `test_no_invalid_cases.json` - Sample test data with only valid test cases

## Prerequisites

Before using this test runner, ensure you have:

1. **P0 Basic Test Setup**: Follow the setup instructions in the parent directory's `README_p0.md`
2. **Node.js 22+**: Required for running Vitest and Playwright
3. **Python 3.7+**: Required for report generation
4. **Playwright MCP**: Installed and configured
5. **Required Python packages**: `pydantic`, `pathlib`, etc.

## Usage

### Running Tests

Execute the test runner with default settings:

```bash
./run.sh
```

Or specify a custom test root directory:

```bash
./run.sh --test-root /path/to/your/sweagentd/runtime
```

### Options

- `--test-root PATH`: Specify the path to the SweAgentD runtime folder (default: `$HOME/code/sweagentd/runtime`)
- `-h, --help`: Show help message

### What the Test Runner Does

1. **Environment Setup**: Changes to the test root directory
2. **Process Cleanup**: Kills any existing Playwright MCP processes
3. **MCP Server**: Starts a new Playwright MCP server on port 1234
4. **Test Execution**: Runs Vitest tests with verbose reporting
5. **Log Generation**: Saves test logs with timestamp
6. **Report Generation**: Creates an interactive HTML report

### Manual Report Generation

You can also generate reports manually from existing logs:

```bash
python ../orange_tap2_msft/orange_tap2_msft/generate_test_report.py --input results.json --out report.html [--logs ./logs] [--suite "Custom Title"]
```

#### Arguments

- `--input, -i`: Path to results JSON or log file (required)
- `--out, -o`: Output HTML path (required)
- `--logs`: Directory or glob pattern for log files (optional)
- `--suite`: Override suite name shown in the report (optional)

## Input Formats

### AI Agent Reasoning Test Suite Format

```json
{
  "name": "AI Agent Reasoning Test Suite",
  "duration": 930000,
  "overallPassRate": 78,
  "totalChecks": 47,
  "passedChecks": 37,
  "failedChecks": 10,
  "testCases": [
    {
      "name": "Mathematical Reasoning",
      "status": "pass",
      "passRate": 85,
      "totalChecks": 20,
      "passedChecks": 17,
      "failedChecks": 3,
      "checks": [
        {"name": "Basic arithmetic verification", "status": "pass", "message": "..."}
      ],
      "rolloutLogPath": "testcase-math.log"
    }
  ]
}
```

### Raw Log Files

The tool can process raw Vitest log files (`.log` extension) and automatically extract test case information, durations, and failure summaries.

## Report Features

### Interactive HTML Reports

Generated reports include:

- **Header with Statistics**: Total checks, pass/fail counts, and overall metrics
- **Filtering**: Filter by test status (pass/fail/invalid)
- **Search**: Real-time search through test case names
- **Collapsible Details**: Expandable test case sections with detailed check results
- **Embedded Logs**: View full logs in new tabs with syntax highlighting
- **Status Indicators**: Visual status badges and progress indicators
- **Invalid Test Case Handling**: Special handling and warnings for invalid test cases

### Status Types

- **Pass**: All checks passed successfully
- **Fail**: At least one check failed
- **Invalid**: Test case encountered errors during execution (excluded from totals)

## Test Case Structure

Each test case contains:

- **Name**: Descriptive test case name
- **Status**: Overall status (pass/fail/invalid)
- **Checks**: Individual verification steps with status and messages
- **Logs**: Associated log files for debugging
- **Failure Summary**: Detailed failure information for failed cases

## Output

After running tests, you'll find:

- **Log File**: `basic-test-YYYYMMDD_HHMMSS.log` in the test root directory
- **HTML Report**: `basic-test-YYYYMMDD_HHMMSS.html` with interactive results
- **Console Output**: Real-time test progress and status updates

## Example Workflow

```bash
# 1. Navigate to the test runner directory
cd ~/code/glass/project/orange_tap2_msft/sweagentd_test_runner

# 2. Run tests (assumes SweAgentD is set up in default location)
./run.sh

# 3. View results
# - Check console output for summary
# - Open the generated HTML file in a browser
# - Review individual test logs as needed
```

## Troubleshooting

### Common Issues

1. **Test Root Not Found**: Ensure SweAgentD is properly set up at the specified path
2. **Port Conflicts**: Make sure port 1234 is available for Playwright MCP
3. **Node.js Version**: Verify you're using Node.js 22+ as required by the P0 Basic Test
4. **Permission Issues**: Ensure the script has execute permissions (`chmod +x run.sh`)

### Log Locations

- **SweAgentD Logs**: `/tmp/sweagent-evals/*/cpd.log`
- **Model Logs**: `~/code/glass/project/orange_tap2_msft/orchestrator/log`
- **Test Runner Logs**: Generated in the test root directory

## Update report comparator web app

To update the static web app used by the report comparator, create a zip of the `report_comparator_app` folder and deploy it using the Azure CLI.

1. Create a zip archive:

```bash
cd /home/<USER>/code/glass/project/orange_tap2_msft/sweagentd_test_runner/report_comparator_app
zip -r ../report_comparator_app.zip .
```

2. Ensure you're logged in to Azure and using the correct subscription:

```bash
az login
az account set --subscription "TScienceGPU"
```

3. Deploy the zip to the App Service:

```bash
az webapp deployment source config-zip \
  --resource-group SWE-Eval-RG \
  --name report-comparator \
  --src report_comparator_app.zip
```


## Contributing

When adding new test cases or modifying the report generator:

1. Follow the existing JSON schema formats
2. Test with both valid and invalid test cases
3. Update sample JSON files if schema changes
4. Ensure HTML reports render correctly across browsers

## Related Documentation

- `../README_p0.md` - SweAgentD setup instructions
