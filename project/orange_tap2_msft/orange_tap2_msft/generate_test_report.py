#!/usr/bin/env python3
"""
Generate a fully standalone HTML report for JavaScript (npm) tests, grouped by test case
with per-check results and embedded per-test logs that open in a new tab via a "View Log" button.

Input options
-------------
- Supported input JSON schemas

{
    "name": "AI Agent Reasoning Test Suite",
    "duration": 930000,
    "overallPassRate": 78,
    "totalChecks": 47,
    "passedChecks": 37,
    "failedChecks": 10,
    "testCases": [
        {
            "name": "Mathematical Reasoning",
            "status": "pass",
            "passRate": 85,
            "totalChecks": 20,
            "passedChecks": 17,
            "failedChecks": 3,
            "checks": [
                {"name": "Basic arithmetic verification", "status": "pass", "message": "..."}
            ],
            "rolloutLogPath": "testcase-math.log",  # OR use "rolloutlogs": "direct log content as string"
        }
    ]
}

Usage
-----
python generate_test_report.py --input results.json --out report.html [--logs ./logs] [--suite "Title override"]

- If `--logs` is provided and a test case named "X" has a log file `X.log` or `X.txt` under that directory (or matching glob),
  the file will be embedded and connected to the case's "View Log" button. 
- In your schema, `rolloutLogPath` specifies a file path to read logs from, while `rolloutlogs` can contain direct log content as a string.

Notes
-----
- The produced HTML is a single, self-contained file (inline CSS + JS + embedded logs).
- Collapsible sections (<details>) are used per test case.
- The header includes filters (status chips) and an "Expand/Collapse All" control.
- The "View Log" button opens a new tab and renders the embedded text in a minimal HTML wrapper.
"""  # noqa: E501

import argparse
import glob
import html
import json
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class Check(BaseModel):
    name: str
    status: str
    actual: Optional[Any] = None


class TestCase:
    def __init__(self, name, log_folder_path: str):
        self.name: str = name
        self.log_folder_path: str = log_folder_path
        self.is_completed: bool = False
        self.stats: List[int] = [0, 0]  # [# pass, # fail]
        self.checks: List[Check] = []
        self.failure_summary: str = ""
        self.read_checks()

    def read_checks(self):
        if not self.log_folder_path:
            raise ValueError("Log path is not set.")
        if not os.path.exists(self.log_folder_path):
            raise FileNotFoundError(f"Log folder does not exist: {self.log_folder_path}")

        check_result_file_path = os.path.join(self.log_folder_path, "checks.json")
        if os.path.exists(check_result_file_path):
            self.is_completed = True
            with open(check_result_file_path, "r") as file:
                data = json.load(file)
                for item in data:
                    item["status"] = "pass" if item["assertion"] else "fail"
                    del item["assertion"]
                    self.checks.append(Check(**item))
                    if item["status"] == "pass":
                        self.stats[0] += 1
                    else:
                        self.stats[1] += 1

    def generate_summary_dict(self) -> dict:
        return {
            "name": self.name,
            "status": "pass" if self.stats[1] == 0 else "fail",
            "passRate": self.stats[0] / sum(self.stats) if sum(self.stats) > 0 else 0.0,
            "totalChecks": sum(self.stats),
            "passedChecks": self.stats[0],
            "failedChecks": self.stats[1],
            "checks": [check.model_dump() for check in self.checks],
            "rolloutLogPath": os.path.join(self.log_folder_path, "cpd.log"),
            "failureSummary": self.failure_summary.strip() if self.failure_summary else "",
        }


class TestLogProcssor:
    def __init__(self, test_log_file_path: str):
        self.test_log_file_path: str = test_log_file_path
        self.duration: float = 0.0
        self.stats: List[int] = [0, 0]  # [# pass, # fail]
        self.testcases = {}
        self.process_logs()
        self.compute_stats()

    def process_logs(self):
        if not os.path.exists(self.test_log_file_path):
            raise FileNotFoundError(f"Log file does not exist: {self.test_log_file_path}")

        log_folder_path_pattern = re.compile(
            r"^Test outputs folder for (?P<testcase>.*?): (?P<logpath>.*)$"
        )
        duration_pattern = re.compile(r"^\s*Duration\s*(?P<duration>\d*\.\d*)s\s*\(.*\)$")
        failure_summary_start_pattern = re.compile(
            r"^ FAIL  test/evals/basic\.test\.ts > basic evals > '(?P<testcase>.*)'"
        )
        failure_summary_end_pattern = re.compile(r"^⎯*\[\d/\d\]⎯*$")
        read_failure_summary_lines = False
        with open(self.test_log_file_path, "r") as file:
            for line in file:
                if log_folder_path_match := log_folder_path_pattern.match(line):
                    testcase_name = log_folder_path_match.group("testcase")
                    log_folder_path = log_folder_path_match.group("logpath")
                    if testcase_name not in self.testcases:
                        self.testcases[testcase_name] = TestCase(
                            name=testcase_name, log_folder_path=log_folder_path
                        )
                    else:
                        raise ValueError(
                            f"Duplicate test case found: {testcase_name}."
                            " Each test case should be unique."
                        )
                if duration_match := duration_pattern.match(line):
                    self.duration = float(duration_match.group("duration"))
                if not read_failure_summary_lines and (
                    failure_summary_start_match := failure_summary_start_pattern.match(line)
                ):
                    failure_summary_lines = []
                    failed_testcase_name = self.find_testcase_name(
                        failure_summary_start_match.group("testcase")
                    )
                    read_failure_summary_lines = True
                if read_failure_summary_lines and failure_summary_end_pattern.match(line):
                    self.testcases[failed_testcase_name].failure_summary = "\n".join(
                        failure_summary_lines
                    )
                    read_failure_summary_lines = False
                if read_failure_summary_lines:
                    failure_summary_lines.append(line)

    def find_testcase_name(self, name: str) -> str:
        parts = name.split()
        name_prefix = " ".join(parts[:-1]) if len(parts) > 1 else name
        for key in self.testcases.keys():
            if key.startswith(name_prefix):
                return key
        raise ValueError(f"Test case not found: {name}")

    def compute_stats(self):
        for _, testcase in self.testcases.items():
            self.stats[0] += testcase.stats[0]
            self.stats[1] += testcase.stats[1]

    def generate_summary_dict(self) -> dict:
        return {
            "name": "Padawan P0 Basic Test",
            "duration": self.duration,
            "overallPassRate": self.stats[0] / sum(self.stats) if sum(self.stats) > 0 else 0.0,
            "totalChecks": sum(self.stats),
            "passedChecks": self.stats[0],
            "failedChecks": self.stats[1],
            "testCases": [
                testcase.generate_summary_dict() for _, testcase in self.testcases.items()
            ],
        }

    def save_summary_json(self, output_path: str):
        data = self.generate_summary_dict()
        with open(output_path, "w") as json_file:
            json.dump(data, json_file, indent=2)


Status = str  # "pass" | "fail" | "invalid"

# --------------------------
# Utilities
# --------------------------


def read_text_file(path: Path) -> Optional[str]:
    try:
        return path.read_text(encoding="utf-8", errors="replace")
    except Exception:
        return None


def norm_status(s: Optional[str]) -> Status:
    return s if s in {"pass", "fail"} else "invalid"


def slugify(name: str) -> str:
    s = re.sub(r"[^a-zA-Z0-9_-]+", "-", name.strip())
    s = re.sub(r"-+", "-", s).strip("-")
    return s or "case"


# --------------------------
# Input loading & normalization
# --------------------------


def load_and_normalize(path: Path) -> Dict[str, Any]:
    ext = path.suffix.lower().lstrip(".")
    if ext not in ("json", "log"):
        raise ValueError(f"Unsupported input file extension: '{ext}'. Expected 'json' or 'log'.")
    if ext == "log":
        # for .log inputs, run the log processor and get a summary dict
        data = TestLogProcssor(str(path)).generate_summary_dict()
    else:
        # for .json inputs, load JSON as before
        data = json.loads(path.read_text(encoding="utf-8"))
    # data = json.loads(path.read_text(encoding="utf-8"))

    # --- Your schema: AI Agent Reasoning Test Suite ---
    if not ("testCases" in data and isinstance(data["testCases"], list) and "name" in data):
        raise ValueError("Invalid input JSON format for AI Agent Reasoning Test Suite.")

    suite = str(data.get("name") or f"Test Run ({path.name})")
    out_cases = []
    for tc in data["testCases"]:
        name = str(tc.get("name", "Unnamed Test Case"))
        checks = []
        for ch in tc.get("checks", []) or []:
            checks.append(
                {
                    "name": str(ch.get("name", "check")),
                    "status": norm_status(ch.get("status")),
                    "duration_ms": None,  # not provided in this schema
                    "message": str(ch.get("actual", "")),
                }
            )
        out_cases.append(
            {
                "name": name,
                "checks": checks,
                "log_file": tc.get("rolloutLogPath"),
                "log_content": tc.get("rolloutlogs"),  # Direct log content as string
                "failure_summary": tc.get("failureSummary", ""),
                "_meta": {
                    "status": norm_status(tc.get("status")),
                    "passRate": tc.get("passRate"),
                    "totalChecks": tc.get("totalChecks"),
                    "passedChecks": tc.get("passedChecks"),
                    "failedChecks": tc.get("failedChecks"),
                },
            }
        )
    return {"suite_name": suite, "test_cases": out_cases}


# --------------------------
# Log association
# --------------------------


def attach_logs(cases: List[Dict[str, Any]], logs_hint: Optional[str]) -> None:
    if not logs_hint:
        return

    # Resolve globs or directory into candidate files
    paths: List[Path] = []
    p = Path(logs_hint)
    if p.is_dir():
        paths = sorted([x for x in p.rglob("*") if x.is_file()])
    else:
        paths = [Path(x) for x in glob.glob(logs_hint)]

    # Map by normalized slug and also by stem
    by_slug: Dict[str, Path] = {}
    by_stem: Dict[str, Path] = {}
    for fp in paths:
        if fp.suffix.lower() not in {".log", ".txt", ".out", ".err"}:
            continue
        by_slug[slugify(fp.stem).lower()] = fp
        by_stem[fp.stem.lower()] = fp

    for case in cases:
        name = case.get("name", "")
        if case.get("log_file"):
            continue  # already set by input
        key1 = slugify(name).lower()
        key2 = name.lower()
        fp = by_slug.get(key1) or by_stem.get(key2)
        if fp:
            case["log_file"] = str(fp)


# --------------------------
# HTML generation
# --------------------------

CSS = r"""
:root {
  --bg: #0b1020;        /* deep navy */
  --panel: #111731;     /* card */
  --panel-2: #0f142b;   /* darker */
  --text: #e7ecf3;      /* near-white */
  --muted: #9aa7bd;
  --pass: #22c55e;      /* green */
  --fail: #ef4444;      /* red */
  --invalid: #ffa500;   /* orange */
  --invalid-bg: #ff8c00; /* darker orange for background */
  --border: #253056;
}
* { box-sizing: border-box; }
html, body { margin:0; padding:0; font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji","Segoe UI Emoji"; background: var(--bg); color: var(--text); }
header { position: sticky; top:0; z-index: 10; backdrop-filter: blur(8px); background: linear-gradient(180deg, rgba(11,16,32,0.9), rgba(11,16,32,0.6)); border-bottom: 1px solid var(--border); }
header.banner-offset { top: 50px; }
.container { max-width: 1200px; margin: 0 auto; padding: 16px; }
.h-row { display:flex; align-items:center; justify-content: space-between; gap:12px; }
.h-title { font-size: 20px; font-weight: 700; }
.h-sub { color: var(--muted); font-size: 12px; }
.chips { display:flex; gap:8px; flex-wrap: wrap; }
.chip { padding: 6px 10px; border: 1px solid var(--border); border-radius: 999px; font-size: 12px; cursor: pointer; user-select:none; background: var(--panel); }
.chip.active { outline: 2px solid #fff2; }
.chip.invalid-warning {
  background: var(--invalid-bg);
  color: #000;
  border-color: var(--invalid);
  font-weight: bold;
  animation: pulse-warning 2s infinite;
  box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.3);
}
@keyframes pulse-warning {
  0% { box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.3); }
  50% { box-shadow: 0 0 0 6px rgba(255, 165, 0, 0.6); }
  100% { box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.3); }
}
.controls { display:flex; gap:8px; }
.btn { background: var(--panel); border: 1px solid var(--border); color: var(--text); padding: 8px 12px; border-radius: 10px; cursor:pointer; font-size: 13px; }
.btn:hover { background: var(--panel-2); }
.btn.active { background: var(--fail); border-color: var(--fail); }

.banner { position: fixed; top: 0; left: 0; right: 0; z-index: 20; background: var(--invalid); color: #000; padding: 12px 16px; display: flex; align-items: center; justify-content: space-between; font-size: 14px; font-weight: 600; }
.banner .close-btn { background: transparent; border: none; color: #000; font-size: 18px; font-weight: bold; cursor: pointer; padding: 4px; }
.banner.hidden { display: none; }
.invalid-tooltip { position: relative; cursor: help; }
.invalid-tooltip:hover::after { content: "INVALID TEST CASES: The checks from these test cases are not included in totals. Click View Log for details."; position: absolute; bottom: -35px; left: 50%; transform: translateX(-50%); background: var(--panel); color: var(--text); padding: 8px 12px; border-radius: 6px; font-size: 11px; white-space: nowrap; z-index: 15; border: 1px solid var(--border); }

main { padding: 16px; }
.case { border: 1px solid var(--border); border-radius: 14px; background: linear-gradient(180deg, var(--panel), var(--panel-2)); margin-bottom: 14px; }
.case[hidden] { display:none !important; }
.case details { border-radius: 14px; }
.case summary { list-style: none; cursor: pointer; padding: 14px 16px; display:flex; align-items:center; gap:12px; }
.case summary::-webkit-details-marker { display:none; }
.case .title { font-weight: 700; font-size: 15px; }
.tag { padding: 2px 8px; border-radius: 999px; font-size: 11px; border: 1px solid var(--border); }
.dot { width: 10px; height: 10px; border-radius: 999px; display:inline-block; }
.dot.pass { background: var(--pass); }
.dot.fail { background: var(--fail); }
.dot.invalid { background: var(--invalid); }

.meta { color: var(--muted); font-size: 12px; margin-left: auto; display:flex; gap:14px; align-items:center; }

.table { width: 100%; border-collapse: collapse; }
.table th, .table td { text-align: left; padding: 10px 12px; border-bottom: 1px solid var(--border); vertical-align: top; }
.table th { position: sticky; top: 66px; background: var(--panel); z-index: 5; font-size: 12px; color: var(--muted); }
.badge { font-weight: 600; }
.badge.pass { color: var(--pass); }
.badge.fail { color: var(--fail); }
.badge.invalid { color: var(--invalid); }

.footer { color: var(--muted); font-size: 12px; padding: 16px; text-align: center; }
.search { width: 280px; background: var(--panel); border: 1px solid var(--border); border-radius: 10px; padding: 8px 12px; color: var(--text); }
"""  # noqa: E501

JS = r"""
(() => {
  const $ = sel => document.querySelector(sel);
  const $$ = sel => Array.from(document.querySelectorAll(sel));

  // Banner handling
  const banner = $('#invalidBanner');
  const closeBanner = $('#closeBanner');
  const header = $('header');

  if (closeBanner) {
    closeBanner.addEventListener('click', () => {
      banner.classList.add('hidden');
      header.classList.remove('banner-offset');
    });
  }

  // Filters
  const chips = $$('.chip[data-status]');
  chips.forEach(ch => ch.addEventListener('click', () => {
    ch.classList.toggle('active');
    applyFilters();
  }));

  const search = $('#search');
  search.addEventListener('input', () => applyFilters());

  function applyFilters() {
    const active = new Set($$('.chip.active').map(el => el.dataset.status));
    const term = search.value.trim().toLowerCase();

    $$('.case').forEach(card => {
      const st = card.dataset.caseStatus; // aggregated status
      const name = card.dataset.caseName;
      let ok = true;
      if (active.size > 0 && !active.has(st)) ok = false;
      if (term && !name.includes(term)) ok = false;
      card.hidden = !ok;
    });
  }

  // Expand / Collapse
  $('#expandAll').addEventListener('click', () => {
    $$('details').forEach(d => d.open = true);
  });
  $('#collapseAll').addEventListener('click', () => {
    $$('details').forEach(d => d.open = false);
  });

  // Toggle Failed Only
  const toggleFailedBtn = $('#toggleFailedOnly');
  let showFailedOnly = false;

  function applyFailedOnlyFilter(active) {
    // Use JavaScript to hide/show rows instead of CSS
    $$('.table tbody tr').forEach(row => {
      const status = row.getAttribute('data-status');
      if (active && status === 'pass') {
        row.style.display = 'none';
      } else {
        row.style.display = '';
      }
    });
  }

  toggleFailedBtn.addEventListener('click', () => {
    showFailedOnly = !showFailedOnly;
    if (showFailedOnly) {
      applyFailedOnlyFilter(true);
      toggleFailedBtn.classList.add('active');
      toggleFailedBtn.textContent = 'Show All';
    } else {
      applyFailedOnlyFilter(false);
      toggleFailedBtn.classList.remove('active');
      toggleFailedBtn.textContent = 'Show Failed Only';
    }
  });

  // Expand all by default on page load
  $$('details').forEach(d => d.open = true);

  // Log viewing
  window.openLog = function(id, testCaseName, passCount, failCount, failureSummary) {
    console.log('openLog called:', id, testCaseName, passCount, failCount, failureSummary);
    const holder = document.getElementById(id);
    const content = holder ? holder.textContent : '';
    const win = window.open('', '_blank');
    if (!win) return;
    // Create the HTML document with proper escaping only for the title
    const doc = win.document;
    doc.open();
    const escapedTestName = (testCaseName || 'Unknown Test Case').replace(/[<>&"']/g, function(m) { return {'<':'&lt;','>':'&gt;','&':'&amp;','"':'&quot;',"'":'&#39;'}[m]; });
    const totalChecks = (passCount || 0) + (failCount || 0);
    const escapedFailureSummary = (failureSummary || '').replace(/[<>&"']/g, function(m) { return {'<':'&lt;','>':'&gt;','&':'&amp;','"':'&quot;',"'":'&#39;'}[m]; });
    const failureSummarySection = failureSummary ? '<div class="failure-summary"><h2>Failure Summary</h2><pre>' + escapedFailureSummary + '</pre></div>' : '';
    doc.write('<!doctype html><html><head><meta charset="utf-8"><title>Log: ' + escapedTestName + '</title><style>body{margin:0;font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial;background:#0b1020;color:#e7ecf3}.header{position:fixed;top:0;left:0;right:0;z-index:100;background:#111731;border-bottom:1px solid #253056;padding:12px 16px;backdrop-filter:blur(8px);display:flex;align-items:center;gap:12px}.header h1{margin:0;font-size:16px;font-weight:600}.stats{display:flex;gap:8px;font-size:12px}.stats .stat{padding:3px 7px;border-radius:12px;border:1px solid #253056}.stats .pass{color:#22c55e}.stats .fail{color:#ef4444}.failure-summary{position:fixed;top:60px;left:0;right:0;z-index:99;background:#0f142b;border-bottom:1px solid #253056;padding:12px 16px;max-height:200px;overflow-y:auto}.failure-summary h2{margin:0 0 8px 0;font-size:14px;font-weight:600;color:#ef4444}.failure-summary pre{margin:0;font-size:11px;line-height:1.3;white-space:pre-wrap;font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.content{margin-top:' + (failureSummary ? '260px' : '80px') + '}</style></head><body><div class="header"><h1>' + escapedTestName + '</h1><div class="stats"><span class="stat">' + totalChecks + ' checks</span><span class="stat pass">✓ ' + (passCount || 0) + '</span><span class="stat fail">✗ ' + (failCount || 0) + '</span></div></div>' + failureSummarySection + '<div class="content"><pre id="logContent" style="white-space:pre-wrap;word-wrap:break-word;font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;padding:16px;margin:0;font-size:12px;line-height:1.4;"></pre></div></body></html>');
    doc.close();
    // Set the text content directly to preserve special characters
    doc.getElementById('logContent').textContent = content;
  }

  // Add click event listeners to all View Log buttons to ensure they work
  document.addEventListener('click', function(e) {
    if (e.target && e.target.matches('.btn') && e.target.textContent.includes('View Log')) {
      console.log('View Log button clicked:', e.target);
      e.stopPropagation();
      // Let the onclick handler run
    }
  });
})();
"""  # noqa: E501

# --------------------------
# HTML doc builder
# --------------------------


def aggregate_status(checks: List[Dict[str, Any]]) -> Status:
    statuses = [norm_status(c.get("status")) for c in checks]
    if not statuses:
        return "invalid"
    if any(s == "fail" for s in statuses):
        return "fail"
    if all(s == "pass" for s in statuses):
        return "pass"
    return "invalid"


def html_report(suite_name: str, cases: List[Dict[str, Any]], now: Optional[str] = None) -> str:
    now = now or datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Embed logs via <script type="text/plain" id="log-...">content</script>
    embedded_logs: List[str] = []

    total_checks = 0
    check_counts = {"pass": 0, "fail": 0}  # Only count pass/fail checks
    invalid_test_cases = 0  # Count invalid test cases separately

    # Preprocess cases, read logs
    prepared_cases = []
    for i, case in enumerate(cases, start=1):
        checks = case.get("checks", [])
        agg = aggregate_status(checks)

        # Count invalid test cases (those with invalid aggregate status)
        if agg == "invalid":
            invalid_test_cases += 1
        else:
            # Only count checks from valid test cases
            for ch in checks:
                st = norm_status(ch.get("status"))
                if st in ["pass", "fail"]:  # Only count pass/fail checks
                    check_counts[st] = check_counts.get(st, 0) + 1
            total_checks += len(checks)

        log_id = None
        log_file = case.get("log_file")
        log_content = case.get("log_content")
        txt = None

        if log_content and isinstance(log_content, str):
            # Direct log content provided as string
            txt = log_content
        elif log_file:
            # Log file path provided, read from file
            txt = read_text_file(Path(log_file))

        if txt is not None:
            log_id = f"log-{i}-{slugify(case.get('name','case'))}"
            # Escape only script-breaking sequences, preserve other special chars
            safe_txt = txt.replace("</script>", "<\\/script>")
            embedded_logs.append(
                f'<script type="text/plain" id="{html.escape(log_id)}">{safe_txt}</script>'
            )

        prepared_cases.append(
            {
                "name": case.get("name", f"Case {i}"),
                "checks": checks,
                "agg": agg,
                "log_id": log_id,
                "failure_summary": case.get("failure_summary", ""),
            }
        )

    # Header chips
    chip_html = []
    for st in ["pass", "fail"]:
        chip_html.append(
            f'<div class="chip" data-status="{st}">'
            f'<span class="dot {st}"></span> {st.title()} '
            f'<span class="h-sub">({check_counts.get(st,0)})</span></div>'
        )

    # Add invalid test cases count with tooltip if there are any
    if invalid_test_cases > 0:
        chip_html.append(
            f'<div class="chip invalid-warning invalid-tooltip" data-status="invalid">'
            f'⚠️ <span class="dot invalid"></span> INVALID TEST CASES '
            f'<span class="h-sub">({invalid_test_cases})</span></div>'
        )

    # Cases HTML
    cases_html = []
    for idx, case in enumerate(prepared_cases, start=1):
        name = html.escape(str(case["name"]))
        agg = case["agg"]

        # Count pass/fail for this case
        case_checks = case.get("checks", [])
        case_pass_count = sum(1 for ch in case_checks if norm_status(ch.get("status")) == "pass")
        case_fail_count = sum(1 for ch in case_checks if norm_status(ch.get("status")) == "fail")

        # Get failure summary and escape it for JavaScript string
        failure_summary = case.get("failure_summary", "")
        # Escape for JavaScript string literal (inside single quotes)
        escaped_failure_summary = (
            failure_summary.replace("\\", "\\\\")
            .replace("'", "\\'")
            .replace("\n", "\\n")
            .replace("\r", "\\r")
        )

        log_btn = (
            f'<button class="btn" onclick="event.stopPropagation(); openLog(\'{case["log_id"]}\', \'{html.escape(str(case["name"]), quote=True)}\', {case_pass_count}, {case_fail_count}, \'{escaped_failure_summary}\'); return false;">View Log</button>'  # noqa: E501
            if case["log_id"]
            else ""
        )

        # Checks table
        rows = []
        for j, ch in enumerate(case["checks"], start=1):
            st = norm_status(ch.get("status"))
            nm = html.escape(str(ch.get("name", "check")))
            # dur = ch.get("duration_ms")
            # dur_s = f"{dur} ms" if isinstance(dur, (int, float)) else ""
            msg = html.escape(str(ch.get("message", "")))
            rows.append(
                f'<tr data-status="{st}">\n'
                f"  <td>{j}</td>\n"
                f"  <td>{nm}</td>\n"
                f"  <td>"
                # f'<span class="dot {st}"></span> <span class="badge {st}">{st.upper()}</span>'
                f'<span class="badge {st}">{st.upper()}</span>'
                f"</td>\n"
                # f"  <td>{dur_s}</td>\n"
                f'  <td><pre style="white-space:pre-wrap;margin:0">{msg}</pre></td>\n'
                f"</tr>"
            )

        table = (
            "<table class=table>"
            # "<thead><tr><th>#</th><th>Check</th><th>Status</th><th>Duration</th><th>Message</th></tr></thead>"
            "<thead><tr><th>#</th><th>Check</th><th>Status</th><th>Message</th></tr></thead>"
            f"<tbody>{''.join(rows)}</tbody>"
            "</table>"
        )

        cases_html.append(
            f'<article class=case data-case-status="{agg}" data-case-name="{html.escape(name.lower())}">'  # noqa: E501
            f"  <details>"
            f"    <summary>"
            # f"      <span class='dot {agg}'></span>"
            f"      <span class='tag badge {agg}' style='margin-left:6px'>{agg.upper()}</span>"
            f"      <span class=title>{name}</span>"
            f"      <span class='tag'>{len(case['checks'])} checks</span>"
            f"      <span class='tag' style='color: var(--pass);'>✓ {case_pass_count}</span>"
            f"      <span class='tag' style='color: var(--fail);'>✗ {case_fail_count}</span>"
            # f"      <span class='tag badge {agg}' style='margin-left:6px'>{agg.upper()}</span>"
            f"      <span class='meta'>{log_btn}</span>"
            f"    </summary>"
            f"    <div class='content' style='padding: 0 16px 14px 16px'>{table}</div>"
            f"  </details>"
            f"</article>"
        )

    # Check if there are invalid test cases to show banner
    has_invalid = invalid_test_cases > 0
    banner_html = ""
    header_class = ""

    if has_invalid:
        banner_html = (
            '<div id="invalidBanner" class="banner">'
            "<span>🚨 ATTENTION: Invalid test cases detected! The checks from these test cases are not included in the totals. Click View Log button to see details.</span>"  # noqa: E501
            '<button id="closeBanner" class="close-btn">×</button>'
            "</div>"
        )
        header_class = " banner-offset"

    # Combine HTML
    html_doc = f"""<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>{html.escape(suite_name)} — Test Report</title>
  <style>{CSS}</style>
</head>
<body>
  {banner_html}
  <header class="{header_class.strip()}">
    <div class=container>
      <div class=h-row>
        <div>
          <div class=h-title>{html.escape(suite_name)}</div>
          <div class=h-sub>Generated {html.escape(now)} — {total_checks} checks</div>
        </div>
        <input id=search class=search placeholder="Search test cases..." />
      </div>
      <div class=h-row style="margin-top:10px">
        <div class=chips>{''.join(chip_html)}</div>
        <div class=controls>
          <button id=expandAll class=btn>Expand All</button>
          <button id=collapseAll class=btn>Collapse All</button>
          <button id=toggleFailedOnly class=btn>Show Failed Only</button>
        </div>
      </div>
    </div>
  </header>

  <main class=container>
    {''.join(cases_html)}
  </main>

  <footer class=footer>Standalone report • no external assets • logs embedded</footer>

  {''.join(embedded_logs)}
  <script>{JS}</script>
</body>
</html>
"""
    return html_doc


# --------------------------
# CLI
# --------------------------


def main(argv=None):
    ap = argparse.ArgumentParser(
        description="Generate a standalone HTML test report from Your schema, Generic or Jest JSON."
    )
    ap.add_argument("--input", "-i", required=True, help="Path to results JSON")
    ap.add_argument("--out", "-o", required=True, help="Output HTML path")
    ap.add_argument(
        "--logs", help="Directory or glob of log files. Matched by test case name (stem). Optional."
    )
    ap.add_argument("--suite", help="Override suite name shown in the report.")
    args = ap.parse_args(argv)

    input_path = Path(args.input)
    output_path = Path(args.out)

    norm = load_and_normalize(input_path)
    cases = norm.get("test_cases", [])

    # Optionally auto-attach logs by name
    attach_logs(cases, args.logs)

    suite = args.suite or norm.get("suite_name") or "Test Run"

    html_out = html_report(suite, cases)
    output_path.write_text(html_out, encoding="utf-8")

    print(f"Wrote {output_path} ({len(html_out)} bytes)")


def generate_report_from_structured_data(structured_data: Dict[str, Any], output_path: str) -> str:
    """
    Generate HTML report directly from structured test data (for metrics collector integration)

    Args:
        structured_data: Dictionary in the format expected by html_report
        output_path: Path where to write the HTML file

    Returns:
        Path to the generated HTML file
    """
    import os
    from datetime import datetime

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Extract suite name and test cases
    suite_name = structured_data.get("name", "Test Suite")
    test_cases = structured_data.get("testCases", [])

    # Convert to the format expected by html_report
    cases = []
    for test_case in test_cases:
        case = {
            "name": test_case.get("name", "Unknown Test Case"),
            "checks": test_case.get("checks", []),
            "log_file": test_case.get("rolloutLogPath"),
            "log_content": test_case.get("rolloutlogs"),  # Direct log content as string
            "failure_summary": test_case.get("failureSummary", ""),
            "_meta": {
                "status": test_case.get("status"),
                "passRate": test_case.get("passRate"),
                "totalChecks": test_case.get("totalChecks"),
                "passedChecks": test_case.get("passedChecks"),
                "failedChecks": test_case.get("failedChecks"),
            },
        }
        cases.append(case)

    # Generate the HTML
    html_content = html_report(suite_name, cases)

    # Write to file
    Path(output_path).write_text(html_content, encoding="utf-8")

    return output_path


if __name__ == "__main__":
    import sys

    sys.exit(main())
