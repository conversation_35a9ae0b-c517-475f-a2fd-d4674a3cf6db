dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="pdw2-cs-if-ivt-mixed-32x32-o4mini-tef03-5-tpm2-rm-lr1e-5-run1"

CMD=(
beam python --use-cwd -m qstar.run_experiment nostrict
name=$EXPERIMENT_NAME
# name=pdw2-test-sbh-run1
seed=20250501

skip_validate_config=True

# Policy settings
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True'

# nv4
# policy.initial_checkpoint=az://orngscuscresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted/
# o4-mini
policy.initial_checkpoint=az://orngscuscresco/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted/
# codex-mini
# policy.initial_checkpoint=az://orngscuscresco/models/nv4-codex-tc-may11-s200-decrypted

policy.n_gpus=56
policy.is_multimodal=True
...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe

# Systems settings
:qstar.presets.common:longer_timeout
peashooter.timeout_seconds.stalled_datapoint=3600
timeout.default=None

# Model settings
policy.n_ctx=131072
defaults.n_ctx=131072
defaults.sampler.harmony_constrained_sampling=True
# defaults.sample_completer="deep_swe_msft.vsc_data.sampler:PadawanSampleCompleter"
optimizer.hparam_scaler.lr_per_instance_d16=1e-5
...save_every=5

# Dataset configs!
:deep_swe_msft.rrb_if_ml_vsc.csharp.presets:train
# :deep_swe_msft.presets:train_vsc_mix11
...dataset_container=orngscuscresco
...max_num_yields=256
...override_pass_rate_minimum=0.01

berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=5

# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
defaults.inverse_token_cost=16384
defaults.instances_per_batch=32
defaults.target_samples_per_instance=32

batch_completer.n_batches_in_flight=30 # number of batches in flight
peashooter.num_sampling_processes=32 # number of sampling processes per instance worker
peashooter.sampling_concurrency=16 # concurrency sampling threads per process
peashooter.num_instance_workers=64 # number of instance workers

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=swe-bench-train-v2
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}" 2>&1 | tee -a swe-nv4.log
