from functools import partial

import structlog
import chz
from berry.function_wrapper import FunctionWrapper
from deep_swe_msft.rfs_rcs_bb_ml_vsc.java.datasets.mrfs_setup import mrfs_setup_fn_coreutils
from chat.render.v4.experimental.strawberry.formatter import Berry<PERSON>hannel
from qstar.common.datapoint import Harmony<PERSON>ompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig

from berry.grader import FunctionalGrader, Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from deep_swe_msft.vsc_graders.grader_picker import GRADER_PICKER

from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.configs import (
    RFSPythonDatasetConfig, 
    CaasResourceConfig,
    make_multistage_grader,
    conversation_converter,
)
from deep_swe_msft.rfs_rcs_bb_ml_vsc.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK
from deep_swe_msft.rrb_if_ml_vsc.python.datasets.configs import make_swe_if_multistage_grader
from deep_swe_msft.vsc_graders.cotograder_utils import COTOGRADER_RENDERER, COTOGRADER_CHZ_ARGV
from deep_swe_msft.vsc_graders.cotograder_utils import (
    COTOGRADER_BUS_USER,
    COTOGRADER_BUS_TOPIC,
    COTOGRADER_BUS_LINE,
    COTOGRADER_RENDERER,
    COTOGRADER_QOS_TYPE,
)

logger = structlog.stdlib.get_logger(component=__name__)


def make_rrb_if_multistage_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_cotograder: bool = False,
    use_sp_hard: bool = False,
    use_repeat_read_grader: bool = False,
    use_tool_grader: bool = False,
    use_function_enforce_grader: bool = False,
    **kwargs,
) -> MultiStageGrader:
    grader_argvs = []
    
    # Conditionally add RepeatReadingGrader
    if use_repeat_read_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.read_file_grader:RepeatReadingGrader",
        ])
    
    # Always add RFSGrader
    grader_argvs.append([
        "=deep_swe_msft.rfs_rcs_bb_ml_vsc.java.graders.rfs_grader:RFSGrader",
        f"caas_container_image={caas_container_image}",
        f"caas_endpoint={caas_endpoint}",
        f"collect_commit_fn=deep_swe_msft.rfs_rcs_bb_ml_vsc.java.graders.rfs_grader:_vsc_collect_commit",
    ])

    # Always add InstructFollowRubricCotograder (this is specific to IF datasets)
    grader_argvs.append([
        "=deep_swe_msft.vsc_graders.instruction_following_grader:InstructFollowRubricCotograder",
        # "=deep_swe_msft.vsc_graders.instruction_following_grader:InstructFollowCotograder",
        *COTOGRADER_CHZ_ARGV,
        f"grader_max_tokens={16384}",
    ])
    
    # Conditionally add ToolCallGrader
    if use_tool_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.tool_call_grader:ToolCallGrader",
            f"topic={COTOGRADER_BUS_TOPIC}",
            f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            f"line={COTOGRADER_BUS_LINE}",
            f"renderer_name={COTOGRADER_RENDERER}",
            f"qos_type=QoSType.{COTOGRADER_QOS_TYPE}",
        ])
    
    return make_multistage_grader(grader_argvs, channels_for_answer)


@chz.chz
class RRBIFJavaDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_if.rrb_java_v2"
    grader: Grader | FunctionalGrader = override(
        lambda: make_rrb_if_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["java"],
            **GRADER_PICKER
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["java"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            conversation_converter("deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.conversation_init:conversation_init_fn"),
        )
    )


@chz.chz
class SWEIFJavaDatasetConfig(RRBIFJavaDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_if.rrb_java_sweif"
    grader: Grader | FunctionalGrader = override(
        lambda: make_swe_if_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["java"],
            **GRADER_PICKER
        )
    )
