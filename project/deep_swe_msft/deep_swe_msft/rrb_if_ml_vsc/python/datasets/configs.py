from functools import partial
from typing import Any

import chz
import structlog
from berry.function_wrapper import FunctionWrapper
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.configs import (
    RFSPythonDatasetConfig,
    make_multistage_grader,
    conversation_converter,
)
from deep_swe_msft.rfs_rcs_bb_ml_vsc.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK
from deep_swe_msft.vsc_graders.cotograder_utils import COTOGRADER_RENDERER, COTOGRADER_CHZ_ARGV
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from berry.grader import FunctionalGrader, Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from deep_swe_msft.vsc_graders.cotograder_utils import (
    COTOGRADER_BUS_USER,
    COTOGRADER_BUS_TOPIC,
    COTOGRADER_BUS_LINE,
    COTOGRADER_RENDERER,
    COTOGRADER_QOS_TYPE,
)
from deep_swe_msft.vsc_graders.grader_picker import GRADER_PICKER

logger = structlog.stdlib.get_logger(component=__name__)


def make_rrb_if_multistage_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    channels_for_answer: tuple[BerryChannel | None, ...] = (
        BerryChannel.FINAL_ANSWER,),
    use_repeat_read_grader: bool = False,
    use_tool_grader: bool = False,
    use_function_enforce_grader: bool = False,
    **kwargs: Any,
) -> MultiStageGrader:
    grader_argvs = []
    
    # Conditionally add RepeatReadingGrader
    if use_repeat_read_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.read_file_grader:RepeatReadingGrader",
        ])
    
    # Always add RFSGrader
    grader_argvs.append([
        "=deep_swe_msft.rfs_rcs_bb_ml_vsc.python.graders.rfs_grader:RFSGrader",
        f"caas_container_image={caas_container_image}",
        f"caas_endpoint={caas_endpoint}",
        f"collect_commit_fn=deep_swe_msft.rfs_rcs_bb_ml_vsc.python.graders.rfs_grader:_vsc_collect_commit",
    ])
    
    # Always add InstructFollowRubricCotograder (this is specific to IF datasets)
    grader_argvs.append([
        "=deep_swe_msft.vsc_graders.instruction_following_grader:InstructFollowRubricCotograder",
        *COTOGRADER_CHZ_ARGV,
        f"grader_max_tokens={16384}",
    ])
    
    # Conditionally add ToolCallGrader
    if use_tool_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.tool_call_grader:ToolCallGrader",
            f"topic={COTOGRADER_BUS_TOPIC}",
            f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            f"line={COTOGRADER_BUS_LINE}",
            f"renderer_name={COTOGRADER_RENDERER}",
            f"qos_type=QoSType.{COTOGRADER_QOS_TYPE}",
        ])
    
    return make_multistage_grader(grader_argvs, channels_for_answer)


def make_swe_if_multistage_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    channels_for_answer: tuple[BerryChannel | None, ...] = (
        BerryChannel.FINAL_ANSWER,),
    use_repeat_read_grader: bool = False,
    use_tool_grader: bool = False,
    use_function_enforce_grader: bool = False,
    **kwargs: Any,
) -> MultiStageGrader:
    grader_argvs = []
    
    # Conditionally add RepeatReadingGrader
    if use_repeat_read_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.read_file_grader:RepeatReadingGrader",
        ])
    
    # Always add SWEInstructFollowCotograder
    grader_argvs.append([
        "=deep_swe_msft.vsc_graders.instruction_following_grader:SWEInstructFollowCotograder",
        # "=deep_swe_msft.vsc_graders.instruction_following_grader:InstructFollowRubricCotograder",
        # "=deep_swe_msft.vsc_graders.instruction_following_grader:InstructFollowCotograder",
        *COTOGRADER_CHZ_ARGV,
        f"grader_max_tokens={16384}",
    ])
    
    # Conditionally add ToolCallGrader
    if use_tool_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.tool_call_grader:ToolCallGrader",
            f"topic={COTOGRADER_BUS_TOPIC}",
            f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            f"line={COTOGRADER_BUS_LINE}",
            f"renderer_name={COTOGRADER_RENDERER}",
            f"qos_type=QoSType.{COTOGRADER_QOS_TYPE}",
        ])
    
    return make_multistage_grader(grader_argvs, channels_for_answer)

@chz.chz
class RRBIFPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_if.rrb_py_v2"
    grader: Grader | FunctionalGrader = override(
        lambda: make_rrb_if_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
            **GRADER_PICKER
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            conversation_converter("deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.conversation_init:conversation_init_fn"),
        )
    )


@chz.chz
class SWEIFPythonDatasetConfig(RRBIFPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.qingruzhang.swe.rrb_if.rrb_py_sweif"
    grader: Grader | FunctionalGrader = override(
        lambda: make_swe_if_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
            **GRADER_PICKER
        )
    )
