"""
unit test for caas of swe-bench train v2
"""
import asyncio
import json
import os

import caas
from caas.api import caas_api
from caas.commands import Exec

# from deep_swe_msft.qa_vsc.datasets.setup import repo_qa_setup_fn_coreutils
# from deep_s
from caas.protocol import NetworkMode, VolumeMount
from caas.terminal.api import TerminalSession
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.javascript.datasets.mrfs_setup import (
    mrfs_setup_fn_coreutils,
    try_run_command,
)
from deep_swe_msft.tools.utils import get_strawberry_ace_token
from deep_swe_tasks.task_metadata import DeepSWETaskMetadata

# HTTP_FILE_PATH = os.environ.get(
#     "SWE_UPLOAD_REPO",
#     "https://codemodeldata.blob.core.windows.net/swe-model-training-data/github_repos_v2_training/wave1_1/merged_upload_repos",
# )
HTTP_FILE_PATH = "https://orngcaas.blob.core.windows.net/data/swe-model-training-data/weijianxu/github_repos_v2_training/wave1_2/wave1_1/merged_upload_repos"


# def get_strawberry_ace_token():
#     # return os.environ.get("STRAWBERRYACE_CENTRALUS_TOKEN")
#     # return "?sv=2023-01-03&st=2025-06-03T23%3A53%3A46Z&se=2025-06-10T23%3A53%3A00Z&skoid=2ef04b63-21b8-4491-b4e2-4327a78d7c3a&sktid=72f988bf-86f1-41af-91ab-2d7cd011db47&skt=2025-06-03T23%3A53%3A46Z&ske=2025-06-10T23%3A53%3A00Z&sks=b&skv=2023-01-03&sr=c&sp=rl&sig=EFYhR8AaAF0udCKu2q%2F9BpD2hTs44NFoGb721RkHHWo%3D"
#     return "?sp=r&st=2025-06-06T21:53:33Z&se=2025-06-13T05:53:33Z&skoid=23dfe465-2a5e-4b11-8284-63893e809dae&sktid=72f988bf-86f1-41af-91ab-2d7cd011db47&skt=2025-06-06T21:53:33Z&ske=2025-06-13T05:53:33Z&sks=b&skv=2024-11-04&spr=https&sv=2024-11-04&sr=c&sig=nqPBmMKdkuQs%2BTuWwUx%2BuD7Kbk3RYclUZw4etv40y6o%3D"


def load_jsonl(file_path) -> list[dict]:
    data = []
    with open(file_path, "r", encoding="utf-8") as f:
        for line in f:
            item = json.loads(line.strip())
            data.append(item)
    return data


async def main(
    datapoint: dict[str, str],
    endpoint: str | None = "https://eastus2.caas.azure.com",
    use_terminal_server=True,
) -> None:
    if use_terminal_server:
        cmd = ["/server.py"]
    else:
        cmd = []
    caas_session = None
    try:
        print("=" * 100)
        print("Creating caas session")
        caas_key = caas_api(endpoint=endpoint)
        caas_session = await caas_key.new_session(
            image="aio",
            cmd=cmd,
            cpu_limit="2",
            memory_limit=f"4096m",
            idle_ttl=1200,
            num_gpus=0,
            # network='bridge',
            enable_network=True,
            # network=caas.protocol.NetworkMode.BRIDGE,
            timeout=1200,
            volume_mounts=[],
        )
        terminal_session = TerminalSession(caas_session)
        print("=" * 100 + "\n")

        print("=" * 100)
        print("Setting up repo")
        # out = await repo_qa_setup_fn_coreutils(terminal_session=terminal_session, datapoint=datapoint)
        ok, d = await try_run_command(
            terminal_session,
            f'rm -rf {datapoint["repo_root"]}; mkdir -p {datapoint["repo_root"]}; curl -k -sL "{HTTP_FILE_PATH}/{datapoint["repo_id"]}.tar.gz?{get_strawberry_ace_token()}" | tar -xz -C {datapoint["repo_root"]}',
            seconds=200,
            attempts=10,
        )
        print("Result Success:", ok, d)

        ok, d = await try_run_command(
            terminal_session,
            f"pwd",
            seconds=200,
            attempts=10,
        )
        print("Result Success:", ok, d)

        ok, d = await try_run_command(
            terminal_session,
            f'pwd & ls {datapoint["repo_root"]}',
            seconds=200,
            attempts=10,
        )
        print("Result Success:", ok, d)
        print("=" * 100 + "\n")

    except Exception as e:
        print("Error: ", e)
        ok, d = None, None
    finally:
        if caas_session:
            await caas_session.close()
            print("caas_session closed")
        return ok, d


if __name__ == "__main__":

    # import subprocess
    # download_cmd = f'bbb cp az://{CRESCO_STORAGE_NAME}/data/chenliang1/swe/repoqa/u05222025/train_cpp_mu035_sig02_shuffled/train.jsonl ./'
    # result = subprocess.run(download_cmd, shell=True)
    # data = load_jsonl("train.jsonl")

    datapoint = {
        "repo_root": "/root/bgriederMM",
        "repo_id": "bgrieder__MM_rfs_18dfe477-4575-4200-b686-5c592823720d",
        "package_cache_id": "",
        "extract_cache_cmd": "",
        "install_packages_cmd": "bash setup.sh",
        "lang": "javascript",
    }

    ok, d = asyncio.run(
        main(datapoint, endpoint="https://eastus2.caas.azure.com", use_terminal_server=True)
    )

    # endpoint = "https://eastus2.caas.azure.com"
    # use_terminal_server = True

    # print("="*100)
    # print("Creating caas session")
    # caas = caas_api(endpoint=endpoint)
    # caas_session = await caas.new_session(
    #     image='aio',
    #     cmd=cmd,
    #     cpu_limit="2",
    #     memory_limit=f"4096m",
    #     idle_ttl=1200,
    #     num_gpus=0,
    #     network='bridge',
    #     sandbox=False,
    #     timeout=1200,
    #     volume_mounts=VSC_MNT,
    # )
    # terminal_session = TerminalSession(caas_session)
    # print("="*100+'\n')

    # print("="*100)
    # print("Setting up repo")

    cmd = "curl -k -sL az://orngcaas/data/swe-model-training-data/weijianxu/github_repos_v2_training/wave1_2/wave1_1/merged_upload_repos/bgrieder__MM_rfs_18dfe47  17189 7-4575-4200-b686-5c592823720d.tar.gz | tar -xz -C ./data/"
