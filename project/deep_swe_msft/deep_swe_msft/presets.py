import berry.preset_utils
import deep_swe_msft.qa_padawan_v2.datasets.configs as qa_padawan_v2_configs
import deep_swe_msft.qa_vsc.datasets.configs as qa_configs
import deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.configs as rrb_python_padawan_v2_configs
import deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.configs as baseline_vsc_configs
import deep_swe_msft.swe_bench_train_v2_padawan_v2.dataset_config as swb_padawan_v2_configs
import deep_swe_msft.swe_bench_train_v2_vsc.dataset_config as swb_vsc_configs
import prbot_msft.configs.swebench_hard as swb_hard_configs
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe.datasets.config_utils import chz_path
from deep_swe_msft.qa_padawan_v2.presets import (
    CODE_LOCALIZATION_CONFIGS,
    REPO_QA_ML_250_CONFIGS,
    REPO_QA_ML_500_CONFIGS,
    REPO_QA_ML_1k_CONFIGS,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.csharp.datasets import (
    configs as rrb_csharp_padawan_v2_configs,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.java.datasets import (
    configs as rrb_java_padawan_v2_configs,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.javascript.datasets import (
    configs as rrb_javascript_padawan_v2_configs,
)
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.typescript.datasets import (
    configs as rrb_typescript_padawan_v2_configs,
)

from deep_swe_msft.env_setup_padawan_v2.datasets import (
    configs as env_setup_padawan_v2_configs,
)

VSC_DATASET_MIX1_CONFIGS = [
    (baseline_vsc_configs.RFSPythonDatasetConfig, []),
    (baseline_vsc_configs.RCSPythonDatasetConfig, []),
    (baseline_vsc_configs.BugBountyPythonDatasetConfig, []),
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, []),
]

VSC_DATASET_MIX2_CONFIGS = [
    (baseline_vsc_configs.RFSPythonDatasetConfig, ["max_n_datapoints=2000"]),
    (baseline_vsc_configs.RCSPythonDatasetConfig, ["max_n_datapoints=2000"]),
    (baseline_vsc_configs.BugBountyPythonDatasetConfig, ["max_n_datapoints=1000"]),
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, []),
]

VSC_DATASET_MIX3_CONFIGS = [
    (baseline_vsc_configs.RFSPythonDatasetConfig, ["max_n_datapoints=1000"]),
    (baseline_vsc_configs.RCSPythonDatasetConfig, ["max_n_datapoints=1000"]),
    (baseline_vsc_configs.BugBountyPythonDatasetConfig, ["max_n_datapoints=1000"]),
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, []),
    (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
]

VSC_DATASET_MIX4_CONFIGS = [
    (baseline_vsc_configs.RFSPythonDatasetConfig, ["max_n_datapoints=4000"]),
    (baseline_vsc_configs.RCSPythonDatasetConfig, ["max_n_datapoints=4000"]),
    (baseline_vsc_configs.BugBountyPythonDatasetConfig, ["max_n_datapoints=1500"]),
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, []),
    (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
]

VSC_DATASET_MIX5_CONFIGS = [
    (baseline_vsc_configs.RFSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.RCSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.BugBountyHardPythonDatasetConfig, []),
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, []),
    (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
]



VSC_HARD_DATASET_CONFIGS = [
    (baseline_vsc_configs.RFSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.RCSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.BugBountyHardPythonDatasetConfig, []),
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, []),
]

format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)

train_vsc_mix1 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DATASET_MIX1_CONFIGS
    ],
    format,
)

train_vsc_mix3 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DATASET_MIX3_CONFIGS
    ],
    format,
)

train_vsc_mix4 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DATASET_MIX4_CONFIGS
    ],
    format,
)

train_vsc_mix5 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DATASET_MIX5_CONFIGS
    ],
    format,
)



train_vsc_hard = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_HARD_DATASET_CONFIGS
    ],
    format,
)


REPO_QA_1K_CONFIGS = [
    (qa_configs.RepoQAPythonDatasetConfig, ["max_n_datapoints=1000"]), # 6281
    (qa_configs.RepoQACppDatasetConfig, ["max_n_datapoints=1000"]), # 1805
    (qa_configs.RepoQAJavaDatasetConfig, ["max_n_datapoints=1000"]), # 3507
    (qa_configs.RepoQAJsDatasetConfig, ["max_n_datapoints=1000"]), # 3449
    (qa_configs.RepoQARustDatasetConfig, ["max_n_datapoints=1000"]), # 3645
    (qa_configs.RepoQATsDatasetConfig, ["max_n_datapoints=1000"]), # 2614
]

VSC_DATASET_MIX6_CONFIGS = [
    (baseline_vsc_configs.RFSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.RCSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.BugBountyHardPythonDatasetConfig, []),
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, []),
    (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
] + REPO_QA_1K_CONFIGS

train_vsc_mix6 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DATASET_MIX6_CONFIGS
    ],
    format,
)

REPO_QA_500_CONFIGS = [
    (qa_configs.RepoQAPythonDatasetConfig, ["max_n_datapoints=500"]), # 6281
    (qa_configs.RepoQACppDatasetConfig, ["max_n_datapoints=500"]), # 1805
    (qa_configs.RepoQAJavaDatasetConfig, ["max_n_datapoints=500"]), # 3507
    (qa_configs.RepoQAJsDatasetConfig, ["max_n_datapoints=500"]), # 3449
    (qa_configs.RepoQARustDatasetConfig, ["max_n_datapoints=500"]), # 3645
    (qa_configs.RepoQATsDatasetConfig, ["max_n_datapoints=500"]), # 2614
]

VSC_DATASET_MIX7_CONFIGS = [
    (baseline_vsc_configs.RFSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.RCSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.BugBountyHardPythonDatasetConfig, []),
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, []),
    (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
] + REPO_QA_500_CONFIGS

train_vsc_mix7 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DATASET_MIX7_CONFIGS
    ],
    format,
)

VSC_DATASET_MIX8_CONFIGS = VSC_DATASET_MIX6_CONFIGS + [
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yang.swe-bench-train-vsc-rewritten"]),
]
train_vsc_mix8 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DATASET_MIX8_CONFIGS
    ],
    format,
)


RFS_RCS_BB_HARD_CONFIGS = [
    (baseline_vsc_configs.RFSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.RCSHardPythonDatasetConfig, []),
    (baseline_vsc_configs.BugBountyHardPythonDatasetConfig, []),
]

SWB_TRAIN_V2_REWRITE_CONFIGS = [
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yang.swe-bench-train-vsc-rewritten"]),
]

SWB_HARD_CONFIGS = [
    (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
]

VSC_DATASET_MIX9_CONFIGS = (
    RFS_RCS_BB_HARD_CONFIGS
    + SWB_TRAIN_V2_REWRITE_CONFIGS
    + REPO_QA_500_CONFIGS
    + SWB_HARD_CONFIGS
)
train_vsc_mix9 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DATASET_MIX9_CONFIGS
    ],
    format,
)


VSC_DATASET_MIX10_CONFIGS = (
    RFS_RCS_BB_HARD_CONFIGS
    + SWB_TRAIN_V2_REWRITE_CONFIGS
    + SWB_HARD_CONFIGS
    + REPO_QA_500_CONFIGS
    + CODE_LOCALIZATION_CONFIGS
)
train_vsc_mix10 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DATASET_MIX10_CONFIGS
    ],
    format,
)

PADAWAN_V2_DATASET_CONFIGS = [
    (rrb_python_padawan_v2_configs.RFSPythonDatasetConfig, []),
    (rrb_python_padawan_v2_configs.RCSPythonDatasetConfig, []),
    (rrb_python_padawan_v2_configs.BugBountyPythonDatasetConfig, []),
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, []),
]

PADAWAN_V2_HARD_DATASET_CONFIGS = [
    (rrb_python_padawan_v2_configs.RFSHardPythonDatasetConfig, []),
    (rrb_python_padawan_v2_configs.RCSHardPythonDatasetConfig, []),
    (rrb_python_padawan_v2_configs.BugBountyHardPythonDatasetConfig, []),
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, []),
]


format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)

train_padawan_v2 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_DATASET_CONFIGS
    ],
    format,
)

train_padawan_v2_hard = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_HARD_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX2_DATASET_CONFIGS = [
    (rrb_python_padawan_v2_configs.RFSHardPythonDatasetConfig, []),
    (rrb_python_padawan_v2_configs.RCSHardPythonDatasetConfig, []),
    (rrb_python_padawan_v2_configs.BugBountyHardPythonDatasetConfig, []),
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, []),
] + REPO_QA_ML_1k_CONFIGS

train_padawan_v2_mix2 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX2_DATASET_CONFIGS
    ],
    format,
)
PADAWAN_V2_SBH_DATASET_CONFIGS = [
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
]

train_padawan_v2_sbh = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_SBH_DATASET_CONFIGS
    ],
    format,
)


PADAWAN_V2_MIX3_DATASET_CONFIGS = PADAWAN_V2_MIX2_DATASET_CONFIGS + [
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
]

train_padawan_v2_mix3 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX3_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX4_DATASET_CONFIGS = PADAWAN_V2_MIX3_DATASET_CONFIGS + [
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yang.swe-bench-train-vsc-rewritten"]),
]

train_padawan_v2_mix4 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX4_DATASET_CONFIGS
    ],
    format,
)





RFS_RCS_BB_PY_HARD_DATASET_CONFIGS = [
    (rrb_python_padawan_v2_configs.RFSHardPythonDatasetConfig, []),
    (rrb_python_padawan_v2_configs.RCSHardPythonDatasetConfig, []),
    (rrb_python_padawan_v2_configs.BugBountyHardPythonDatasetConfig, []),
]

SWE_BENCH_TRAIN_X5_DATASET_CONFIGS = [
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, []),
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yang.swe-bench-train-vsc-rewritten"]), # 4X
]

SWB_REWRITE_DATASET_CONFIGS = [
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yang.swe-bench-train-vsc-rewritten"]), # 4X
]


PADAWAN_V2_MIX5_DATASET_CONFIGS = RFS_RCS_BB_PY_HARD_DATASET_CONFIGS + SWE_BENCH_TRAIN_X5_DATASET_CONFIGS + REPO_QA_ML_500_CONFIGS
train_padawan_v2_mix5 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX5_DATASET_CONFIGS
    ],
    format,
)


PADAWAN_V2_MIX6_DATASET_CONFIGS = RFS_RCS_BB_PY_HARD_DATASET_CONFIGS + SWB_REWRITE_DATASET_CONFIGS + REPO_QA_ML_250_CONFIGS
train_padawan_v2_mix6 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX6_DATASET_CONFIGS
    ],
    format,
)

RFS_RCS_BB_PY_DATASET_CONFIGS = [
    (rrb_python_padawan_v2_configs.RFSPythonDatasetConfig, []), # 4093
    (rrb_python_padawan_v2_configs.RCSPythonDatasetConfig, []), # 7562
    (rrb_python_padawan_v2_configs.BugBountyPythonDatasetConfig, []), # 1930
]

SWB_HARD_DATASET_CONFIGS =  [
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),
]

PADAWAN_V2_MIX7_DATASET_CONFIGS = (
    RFS_RCS_BB_PY_DATASET_CONFIGS
    + SWB_REWRITE_DATASET_CONFIGS
    + SWB_HARD_DATASET_CONFIGS
    + REPO_QA_ML_500_CONFIGS
    + CODE_LOCALIZATION_CONFIGS
)
train_padawan_v2_mix7 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX7_DATASET_CONFIGS
    ],
    format,
)

RFS_RCS_BB_PY_DATASET_CONFIGS = [
    (rrb_python_padawan_v2_configs.RFSPythonDatasetConfig, ["max_n_datapoints=1500"]), # 4093
    (rrb_python_padawan_v2_configs.RCSPythonDatasetConfig, ["max_n_datapoints=1000"]), # 7562
    (rrb_python_padawan_v2_configs.BugBountyPythonDatasetConfig, ["max_n_datapoints=1000"]), # 1930
]

RFS_RCS_BB_CS_DATASET_CONFIGS = [
    (rrb_csharp_padawan_v2_configs.RFSCSharpFilteredNormalAndEasyDatasetConfig, []), # 634
    (rrb_csharp_padawan_v2_configs.RCSCSharpFilteredNormalAndEasyDatasetConfig, []), # 101
    (rrb_csharp_padawan_v2_configs.BugBountyCSharpFilteredNormalAndEasyDatasetConfig, []), # 105
]

RFS_RCS_BB_JAVA_DATASET_CONFIGS = [
    (rrb_java_padawan_v2_configs.RFSJavaDatasetConfig, ["max_n_datapoints=400"]), # 795
    (rrb_java_padawan_v2_configs.RCSJavaDatasetConfig, ["max_n_datapoints=400"]), # 856
    (rrb_java_padawan_v2_configs.BugBountyJavaDatasetConfig, []), # 181
]

RFS_RCS_BB_TS_DATASET_CONFIGS = [
    (rrb_typescript_padawan_v2_configs.RFSTYPESCRIPTDatasetConfig, []), # 700
    (rrb_typescript_padawan_v2_configs.RCSTYPESCRIPTDatasetConfig, []), # 80
    (rrb_typescript_padawan_v2_configs.BBTYPESCRIPTDatasetConfig, []), # 35
]

RRB_ML_DATASET_CONFIGS = (
    RFS_RCS_BB_TS_DATASET_CONFIGS
    + RFS_RCS_BB_JAVA_DATASET_CONFIGS
    + RFS_RCS_BB_CS_DATASET_CONFIGS
    + RFS_RCS_BB_PY_DATASET_CONFIGS
)
PADAWAN_V2_MIX8_DATASET_CONFIGS = (
    RRB_ML_DATASET_CONFIGS
    + SWB_REWRITE_DATASET_CONFIGS
    + SWB_HARD_DATASET_CONFIGS
    + REPO_QA_ML_500_CONFIGS
    + CODE_LOCALIZATION_CONFIGS
)

train_padawan_v2_mix8 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX8_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX9_DATASET_CONFIGS = [
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten"]), # 1200
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig, []), # 3000
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []), # 793
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig, []), # 1832
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig, []), # 840

    (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
]

train_padawan_v2_mix9 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX9_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX10_DATASET_CONFIGS = [
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten"]), # 1200
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_py_diverse_repo"]), # 4363
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []), # 793
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig, []), # 1832
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig, []), # 840

    (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
]

train_padawan_v2_mix10 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX10_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX11_DATASET_CONFIGS = [
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig, []), # 3000
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []), # 793
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig, []), # 1832
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig, []), # 840

    (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
]

train_padawan_v2_mix11 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX11_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX12_DATASET_CONFIGS = [
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_py_diverse_repo"]), # 4363
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []), # 793
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig, []), # 1832
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig, []), # 840
    (rrb_javascript_padawan_v2_configs.RRBJAVASCRIPTDatasetConfig, []), # 1000

    (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
    (qa_padawan_v2_configs.FileQAPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.file_qa"]), # 500 py + 1000 others
]


train_padawan_v2_mix12 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX12_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX13_DATASET_CONFIGS = [
    
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_py_diverse_repo"]), # 4363
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []), # 793
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig, []), # 1832
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig, []), # 840
    (rrb_javascript_padawan_v2_configs.RRBJAVASCRIPTDatasetConfig, []), # 1000

    (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
    (qa_padawan_v2_configs.FileQAPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.file_qa"]), # 500 py + 1000 others
    (env_setup_padawan_v2_configs.EnvSetupPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.env_setup_task"]), # 1900: py1000 + java300 + js300 + ts300
]

train_padawan_v2_mix13 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX13_DATASET_CONFIGS
    ],
    format,
)

from deep_swe_msft.rrb_if_ml_padawan_v2.python.datasets import configs as rrb_if_python_padawan_v2_configs
from deep_swe_msft.rrb_if_ml_padawan_v2.csharp.datasets import configs as rrb_if_csharp_padawan_v2_configs
from deep_swe_msft.rrb_if_ml_padawan_v2.java.datasets import configs as rrb_if_java_padawan_v2_configs
from deep_swe_msft.rrb_if_ml_padawan_v2.javascript.datasets import configs as rrb_if_javascript_padawan_v2_configs
from deep_swe_msft.rrb_if_ml_padawan_v2.typescript.datasets import configs as rrb_if_typescript_padawan_v2_configs

# PADAWAN_V2_MIX14_DATASET_CONFIGS = [
#     # swe bench train
#     (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400
#     (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

#     # rrb merged datasets
#     (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.rrb_filtered.py.train"]),   # 3248
#     (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig,             ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.rrb_filtered.java.train"]), # 1555
#     (rrb_javascript_padawan_v2_configs.RRBJAVASCRIPTDatasetConfig,       ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.rrb_filtered.js.train"]),   # 791
#     (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.rrb_filtered.ts.train"]),   # 599
#     (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.rrb_filtered.cs.train"]),   # 635
    
#     # repo qa + file qa
#     (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
#     (qa_padawan_v2_configs.FileQAPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.file_qa"]), # 500 py + 1000 others

#     # env setup
#     (env_setup_padawan_v2_configs.EnvSetupPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.env_setup_task"]), # 1900: py1000 + java300 + js300 + ts300
#     (env_setup_padawan_v2_configs.EnvSetupSBHDatasetConfig, []), # 180
#     (env_setup_padawan_v2_configs.EnvSetupSBHCustomImgDatasetConfig, []), # 180

#     # pr-if datasets
#     (rrb_if_python_padawan_v2_configs.RRBIFPythonDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.pr_if_revised.py.train"]),   # 1115
#     (rrb_if_java_padawan_v2_configs.RRBIFJavaDatasetConfig,             ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.pr_if_revised.java.train"]), # 277
#     (rrb_if_javascript_padawan_v2_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.pr_if_revised.js.train"]),   # 209
#     (rrb_if_typescript_padawan_v2_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.pr_if_revised.ts.train"]),   # 194
#     (rrb_if_csharp_padawan_v2_configs.RRBIFCSharpDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.pr_if_revised.cs.train"]),   # 205
#     # swe-if datasets
#     (rrb_if_python_padawan_v2_configs.SWEIFPythonDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.swe_if_sampled.py.train"]),   # 1337
#     (rrb_if_java_padawan_v2_configs.SWEIFJavaDatasetConfig,             ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.swe_if_sampled.java.train"]), # 212
#     (rrb_if_javascript_padawan_v2_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.swe_if_sampled.js.train"]),   # 243
#     (rrb_if_typescript_padawan_v2_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.swe_if_sampled.ts.train"]),   # 172
#     (rrb_if_csharp_padawan_v2_configs.SWEIFCSharpDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_split_data_0630.swe_if_sampled.cs.train"]),   # 36
# ]

PADAWAN_V2_MIX14_DATASET_CONFIGS = [
    # swe bench train
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    # rrb merged datasets
    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_py_diverse_repo"]),  # 4363
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig,             []),   # 1832
    (rrb_javascript_padawan_v2_configs.RRBJAVASCRIPTDatasetConfig,       []),   # 1000
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []),   # 793
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig,         []),   # 840
    
    # repo qa + file qa
    (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
    (qa_padawan_v2_configs.FileQAPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.file_qa"]), # 500 py + 1000 others

    # env setup
    (env_setup_padawan_v2_configs.EnvSetupPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.env_setup_task"]), # 1900: py1000 + java300 + js300 + ts300
    (env_setup_padawan_v2_configs.EnvSetupSBHDatasetConfig, []), # 180
    (env_setup_padawan_v2_configs.EnvSetupSBHCustomImgDatasetConfig, []), # 180

    # pr-if datasets
    (rrb_if_python_padawan_v2_configs.RRBIFPythonDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_py_v3",   "max_n_datapoints=2000"]),  # 1115
    (rrb_if_java_padawan_v2_configs.RRBIFJavaDatasetConfig,             ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_java_v3", "max_n_datapoints=900"]),   # 277
    (rrb_if_javascript_padawan_v2_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_js_v3",   "max_n_datapoints=500"]),   # 209
    (rrb_if_typescript_padawan_v2_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_ts_v3",   "max_n_datapoints=400"]),   # 194
    (rrb_if_csharp_padawan_v2_configs.RRBIFCSharpDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_cs_v3",   "max_n_datapoints=400"]),   # 205
    # swe-if datasets
    (rrb_if_python_padawan_v2_configs.SWEIFPythonDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.py"]),
    (rrb_if_java_padawan_v2_configs.SWEIFJavaDatasetConfig,             ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.java"]),
    (rrb_if_javascript_padawan_v2_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.js"]),
    (rrb_if_typescript_padawan_v2_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.ts"]),
    (rrb_if_csharp_padawan_v2_configs.SWEIFCSharpDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.cs"]),
]

train_padawan_v2_mix14 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX14_DATASET_CONFIGS
    ],
    format,
)

import deep_swe_msft.webdev_padawan_v2.datasets.configs as webdev_configs
import deep_swe_msft.env_setup_v2_padawan_v2.dataset_config as setup_configs

# MIX15
PADAWAN_V2_MIX15_DATASET_CONFIGS = [
    # swe bench train
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    # rrb merged datasets
    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_py_diverse_repo"]),  # 4363
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig,             []),   # 1832
    (rrb_javascript_padawan_v2_configs.RRBJAVASCRIPTDatasetConfig,       []),   # 1000
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []),   # 793
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig,         []),   # 840
    
    # repo qa + file qa
    (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
    # (qa_padawan_v2_configs.FileQAPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.file_qa"]), # 500 py + 1000 others

    # env setup
    # (env_setup_padawan_v2_configs.EnvSetupPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.env_setup_task"]), # 1900: py1000 + java300 + js300 + ts300
    # (env_setup_padawan_v2_configs.EnvSetupSBHDatasetConfig, []), # 180
    # (env_setup_padawan_v2_configs.EnvSetupSBHCustomImgDatasetConfig, []), # 180

    # pr-if datasets
    (rrb_if_python_padawan_v2_configs.RRBIFPythonDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_py_v3",   "max_n_datapoints=2000"]),  # 1115
    (rrb_if_java_padawan_v2_configs.RRBIFJavaDatasetConfig,             ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_java_v3", "max_n_datapoints=900"]),   # 277
    (rrb_if_javascript_padawan_v2_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_js_v3",   "max_n_datapoints=500"]),   # 209
    (rrb_if_typescript_padawan_v2_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_ts_v3",   "max_n_datapoints=400"]),   # 194
    (rrb_if_csharp_padawan_v2_configs.RRBIFCSharpDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_cs_v3",   "max_n_datapoints=400"]),   # 205
    # swe-if datasets
    (rrb_if_python_padawan_v2_configs.SWEIFPythonDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.py", "max_n_datapoints=1000"]), # 1000
    (rrb_if_java_padawan_v2_configs.SWEIFJavaDatasetConfig,             ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.java"]),                        # 565
    (rrb_if_javascript_padawan_v2_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.js"]),                          # 644
    (rrb_if_typescript_padawan_v2_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.ts"]),                          # 458
    (rrb_if_csharp_padawan_v2_configs.SWEIFCSharpDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.cs"]),                          # 96

    # repro task
    (swb_hard_configs.SWEBenchHardReproTrainPadawanDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.repro_12878.train_hq"]), # 1700

    # swb-ml:
    (swb_padawan_v2_configs.MSWEBenchTrainV2DatasetConfig, ["dataset_id=data.yaliu10.swe_train_ml_rewrite.py.v1"]), # 600 x 4 = 2400
    # (swb_padawan_v2_configs.MSWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_ml.ts"]), # 100

    # webdev task
    (webdev_configs.WebDevDatasetConfig, ["max_n_datapoints=2000"]),

    # rcr_rewrite
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.yaliu10.rcr_12878.train_hq"]), # 1700

]

train_padawan_v2_mix15 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX15_DATASET_CONFIGS
    ],
    format,
)


train_padawan_v2_swb_only = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in SWB_REWRITE_DATASET_CONFIGS
    ],
    format,
)

# MIX15 Backup 
PADAWAN_V2_MIX15_BACKUP_DATASET_CONFIGS = [
    # swe bench train
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    # rrb merged datasets
    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_py_diverse_repo"]),  # 4363
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig,             []),   # 1832
    (rrb_javascript_padawan_v2_configs.RRBJAVASCRIPTDatasetConfig,       []),   # 1000
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []),   # 793
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig,         []),   # 840
    
    # repo qa + file qa
    (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
    # (qa_padawan_v2_configs.FileQAPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.file_qa"]), # 500 py + 1000 others

    # env setup
    # (env_setup_padawan_v2_configs.EnvSetupPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.env_setup_task"]), # 1900: py1000 + java300 + js300 + ts300
    # (env_setup_padawan_v2_configs.EnvSetupSBHDatasetConfig, []), # 180
    # (env_setup_padawan_v2_configs.EnvSetupSBHCustomImgDatasetConfig, []), # 180

    # pr-if datasets
    (rrb_if_python_padawan_v2_configs.RRBIFPythonDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_py_v3",   "max_n_datapoints=2000"]),  # 1115
    (rrb_if_java_padawan_v2_configs.RRBIFJavaDatasetConfig,             ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_java_v3", "max_n_datapoints=900"]),   # 277
    (rrb_if_javascript_padawan_v2_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_js_v3",   "max_n_datapoints=500"]),   # 209
    (rrb_if_typescript_padawan_v2_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_ts_v3",   "max_n_datapoints=400"]),   # 194
    (rrb_if_csharp_padawan_v2_configs.RRBIFCSharpDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_cs_v3",   "max_n_datapoints=400"]),   # 205
    # swe-if datasets
    (rrb_if_python_padawan_v2_configs.SWEIFPythonDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.py", "max_n_datapoints=800"]),  # 800
    (rrb_if_java_padawan_v2_configs.SWEIFJavaDatasetConfig,             ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.java"]),                        # 565
    (rrb_if_javascript_padawan_v2_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.js"]),                          # 644
    (rrb_if_typescript_padawan_v2_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.ts"]),                          # 458
    (rrb_if_csharp_padawan_v2_configs.SWEIFCSharpDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.cs"]),                          # 96

    # # repro task
    # (swb_hard_configs.SWEBenchHardReproTrainPadawanDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    # swb-ml:
    (swb_padawan_v2_configs.MSWEBenchTrainV2DatasetConfig, ["dataset_id=data.yaliu10.swe_train_ml_rewrite.py.v1"]), # 600 x 4 = 2400
    # (swb_padawan_v2_configs.MSWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_ml.ts"]), # 100

    # # webdev task
    # (webdev_configs.WebDevDatasetConfig, ["max_n_datapoints=2000"]),

    # rcr_rewrite
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.yaliu10.rcr_12878.train_hq"]), # 1700

]

train_padawan_v2_mix15_backup = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX15_BACKUP_DATASET_CONFIGS
    ],
    format,
)


# MIX15.5 
PADAWAN_V2_MIX15_5_DATASET_CONFIGS = [
    # swe bench train
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload06272025.sbhv2.train"]), # 20000

    # rrb merged datasets
    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_py_diverse_repo"]),  # 4363
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig,             []),   # 1832
    (rrb_javascript_padawan_v2_configs.RRBJAVASCRIPTDatasetConfig,       []),   # 1000
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []),   # 793
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig,         []),   # 840
    
    # repo qa + file qa
    (qa_padawan_v2_configs.RepoQAMergedDatasetConfig, []), # 2000 + 920
    # (qa_padawan_v2_configs.FileQAPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.file_qa"]), # 500 py + 1000 others

    # env setup
    # (env_setup_padawan_v2_configs.EnvSetupPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.env_setup_task"]), # 1900: py1000 + java300 + js300 + ts300
    # (env_setup_padawan_v2_configs.EnvSetupSBHDatasetConfig, []), # 180
    # (env_setup_padawan_v2_configs.EnvSetupSBHCustomImgDatasetConfig, []), # 180

    # pr-if datasets
    (rrb_if_python_padawan_v2_configs.RRBIFPythonDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_py_v3",   "max_n_datapoints=2000"]),  # 1115
    (rrb_if_java_padawan_v2_configs.RRBIFJavaDatasetConfig,             ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_java_v3", "max_n_datapoints=900"]),   # 277
    (rrb_if_javascript_padawan_v2_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_js_v3",   "max_n_datapoints=500"]),   # 209
    (rrb_if_typescript_padawan_v2_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_ts_v3",   "max_n_datapoints=400"]),   # 194
    (rrb_if_csharp_padawan_v2_configs.RRBIFCSharpDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_cs_v3",   "max_n_datapoints=400"]),   # 205
    # swe-if datasets
    (rrb_if_python_padawan_v2_configs.SWEIFPythonDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.py", "max_n_datapoints=1000"]), # 1000
    (rrb_if_java_padawan_v2_configs.SWEIFJavaDatasetConfig,             ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.java"]),                        # 565
    (rrb_if_javascript_padawan_v2_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.js"]),                          # 644
    (rrb_if_typescript_padawan_v2_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.ts"]),                          # 458
    (rrb_if_csharp_padawan_v2_configs.SWEIFCSharpDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.cs"]),                          # 96

    # repro task
    (swb_hard_configs.SWEBenchHardReproTrainPadawanDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.repro_12878.train_hq"]), # 1700

    # swb-ml:
    # (swb_padawan_v2_configs.MSWEBenchTrainV2DatasetConfig, ["dataset_id=data.yaliu10.swe_train_ml_rewrite.py.v1"]), # 600 x 4 = 2400
    # (swb_padawan_v2_configs.MSWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_ml.ts"]), # 100

    # webdev task
    (webdev_configs.WebDevDatasetConfig, ["max_n_datapoints=2000"]),

    # # rcr_rewrite
    # (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.yaliu10.rcr_12878.train_hq"]), # 1700

]

train_padawan_v2_mix15_5 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX15_5_DATASET_CONFIGS
    ],
    format,
)


FORMAT_TUNE_DATASET_CONFIGS = [
    # (swb_padawan_v2_configs.MSWEBenchTrainV2DatasetConfig, []), # 218
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten"]), # 1200
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    (rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig, []), # 4363
    (rrb_typescript_padawan_v2_configs.RRBTypeScriptMergedDatasetConfig, []), # 793
    (rrb_java_padawan_v2_configs.RRBJavaMergedDatasetConfig, []), # 1832
    (rrb_csharp_padawan_v2_configs.RRBCSharpMergedDatasetConfig, []), # 840
]

train_padawan_v2_ft = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in FORMAT_TUNE_DATASET_CONFIGS
    ],
    format,
)

debug_run = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in [(rrb_python_padawan_v2_configs.RRBPythonMergedDatasetConfig, [])]
    ],
    format,
)

# MIX 16
PADAWAN_V2_MIX16_DATASET_CONFIGS = [
    # swe bench train
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400

    # swe bench hard v2
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload06272025.sbhv2.train"]), # 20000

    # pr-if datasets
    (rrb_if_python_padawan_v2_configs.RRBIFPythonDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_py_v3",   "max_n_datapoints=2000"]),  # 1115
    (rrb_if_java_padawan_v2_configs.RRBIFJavaDatasetConfig,             ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_java_v3", "max_n_datapoints=900"]),   # 277
    (rrb_if_javascript_padawan_v2_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_js_v3",   "max_n_datapoints=500"]),   # 209
    (rrb_if_typescript_padawan_v2_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_ts_v3",   "max_n_datapoints=400"]),   # 194
    (rrb_if_csharp_padawan_v2_configs.RRBIFCSharpDatasetConfig,         ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_cs_v3",   "max_n_datapoints=400"]),   # 205

    # swe-if datasets
    (rrb_if_python_padawan_v2_configs.SWEIFPythonDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.py", "max_n_datapoints=1000"]), # 1000
    (rrb_if_java_padawan_v2_configs.SWEIFJavaDatasetConfig,             ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.java"]),                        # 565
    (rrb_if_javascript_padawan_v2_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.js"]),                          # 644
    (rrb_if_typescript_padawan_v2_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.ts"]),                          # 458
    (rrb_if_csharp_padawan_v2_configs.SWEIFCSharpDatasetConfig,         ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.cs"]),                          # 96

    # webdev task
    (webdev_configs.WebDevDatasetConfig, ["max_n_datapoints=2000"]),
]

train_padawan_v2_mix16 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX16_DATASET_CONFIGS
    ],
    format,
)

# MIX 16 ARM 1
PADAWAN_V2_MIX16_ARM_1_DATASET_CONFIGS = [
    # swe bench train
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400

    # webdev task
    (webdev_configs.WebDevArenaSingleTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    (webdev_configs.WebDevArenaMultiTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),

    # setup task
    # (setup_configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgsetup_dbsetup_train_v0712_280"]), # 280
    # (setup_configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_155"]), # 155
    # (setup_configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_23"]), # 23

    (setup_configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_4200"]), # 4200
    (setup_configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_775"]), # 775
    (setup_configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_270"]), # 270

    # swe bench hard v2
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload06272025.sbhv2.train"]), # 20000

]

train_padawan_v2_mix16_arm_1 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX16_ARM_1_DATASET_CONFIGS
    ],
    format,
)

# MIX 16 ARM 2
PADAWAN_V2_MIX16_ARM_2_DATASET_CONFIGS = [
    # swe bench train
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400

    # swe bench hard v2
    # (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload06272025.sbhv2.train"]), # 20000
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    # webdev task
    (webdev_configs.WebDevArenaSingleTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    (webdev_configs.WebDevArenaMultiTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    
    # setup task
    (setup_configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgsetup_dbsetup_train_v0712_840"]), # 840
    (setup_configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_155"]), # 155
    (setup_configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_69"]), # 69

    # sbhv1 rewrite
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.yaliu10.rcr_12878.train_hq"]), # 1700
]

train_padawan_v2_mix16_arm_2 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX16_ARM_2_DATASET_CONFIGS
    ],
    format,
)

# MIX 16 ARM 1
PADAWAN_V2_MIX16_ARM_1_SBHV2_DATASET_CONFIGS = [
    # swe bench train
    # (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400

    # swe bench hard v2
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload06272025.sbhv2.train"]), # 20000

    # webdev task
    # (webdev_configs.WebDevArenaSingleTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    # (webdev_configs.WebDevArenaMultiTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
]

train_padawan_v2_mix16_arm_1_sbhv2 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX16_ARM_1_SBHV2_DATASET_CONFIGS
    ],
    format,
)

# MIX 16 ARM 3
PADAWAN_V2_MIX16_ARM_3_DATASET_CONFIGS = [
    # swe bench train
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]), # 2400

    # webdev task
    (webdev_configs.WebDevArenaSingleTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    (webdev_configs.WebDevArenaMultiTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),

    # setup task
    (setup_configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_1680"]), # 1680
    (setup_configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_310"]), # 310
    (setup_configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_108"]), # 108

    # swe bench hard v2
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload07312025.sbhv2.train"]), # 20000
    # swe bench hard v1
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700
    # (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]), # 1700

    # swe bench hard v1 python
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload07312025.rcr_12878.train_hq_python"]), # 430
]

train_padawan_v2_mix16_arm_3 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX16_ARM_3_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX16_ARM_4_DATASET_CONFIGS = [
    # swe bench train, 4800
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.chenliang1.swe_train.data_mix_v1.swb_train_rewritten_4x_2rp"]),

    # webdev task ~3K
    (webdev_configs.WebDevArenaSingleTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    (webdev_configs.WebDevArenaMultiTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    (webdev_configs.WebDevArenaMultiTurnFilteredCommentReplyDatasetConfig, ["max_n_datapoints=None"]),
    
    # setup task 2200
    (setup_configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_2520", "max_n_datapoints=2000"]),
    (setup_configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_162", "max_n_datapoints=None"]),

    # swe bench hard v2
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload07312025.sbhv2.train"]), # 20000
]


train_padawan_v2_mix16_arm_4 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX16_ARM_4_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX17_DATASET_CONFIGS = [
    # webdev task ~3K
    (webdev_configs.WebDevArenaSingleTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    (webdev_configs.WebDevArenaMultiTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    (webdev_configs.WebDevArenaMultiTurnFilteredCommentReplyDatasetConfig, ["max_n_datapoints=None"]),
    
    # setup task 2200
    (setup_configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_2520", "max_n_datapoints=2000"]),
    (setup_configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_162", "max_n_datapoints=None"]),

    # swe bench hard v2
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload07312025.sbhv2.train"]), # 20000

    # swe bench train, 4800
    (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.chenliang1.swe_train.data_mix_v1.swb_train_rewritten_4x_2rp"]),
]


train_padawan_v2_mix17 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX17_DATASET_CONFIGS
    ],
    format,
)

PADAWAN_V2_MIX18_DATASET_CONFIGS = [
    # webdev task ~3K
    (webdev_configs.WebDevArenaSingleTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    (webdev_configs.WebDevArenaMultiTurnFilteredDatasetConfig, ["max_n_datapoints=None"]),
    (webdev_configs.WebDevArenaMultiTurnFilteredCommentReplyDatasetConfig, ["max_n_datapoints=None"]),
    
    # setup task 2200
    # (setup_configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_2520", "max_n_datapoints=2000"]),
    # (setup_configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_162", "max_n_datapoints=None"]),

    # swe bench hard v2
    (swb_hard_configs.SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader, ["dataset_id=data.damajercak.swe.upload07312025.sbhv2.train"]), # 20000

    # swe bench train, 4800
    # (swb_padawan_v2_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.chenliang1.swe_train.data_mix_v1.swb_train_rewritten_4x_2rp"]),
]


train_padawan_v2_mix18 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PADAWAN_V2_MIX18_DATASET_CONFIGS
    ],
    format,
)