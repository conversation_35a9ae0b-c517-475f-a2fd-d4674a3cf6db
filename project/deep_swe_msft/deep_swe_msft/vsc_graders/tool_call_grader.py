import json
import os
import random
import re
import traceback
from typing import Sequence, <PERSON><PERSON>

import chat
import chz
from bus.registry import DEFAULT_BUS_LINE
from bus.qos_type import QoSType
from bus_token_completer.bus_token_completer import BusTokenCompleter
from chat.render import get_renderer
from qstar.common import datapoint, types
from qstar.graders import grader as grader_module
from qstar.sample_completers import sample_completer
from chat.render.v4.harmony_v4 import HarmonyRendererV4

from .cotograder_utils import (
    create_logger,
    log_exception_to_file,
    write_to_local_file,
)
from .grader_prompts import TOOL_USING_UNIFIED_GRADER, PLACE_HOLDER_KEY_FOR_MESSAGE
from .grader_picker import TOOL_CALL_MODE

# Constants
REWARD_NAME = "tool_call_grader_reward"
ERROR_LOG_PATH = "/var/log/supervisor/haoran_tool_call_grader_error.log"
STANDARD_LOG_PATH = "/var/log/supervisor/haoran_tool_call_grader_std.log"
LOG_SAMPLING_RATE = 0.01

logger = create_logger()


def extract_code_from_last_block(text: str) -> <PERSON><PERSON>[str, bool]:
    """
    Extracts the code content from the last triple-backtick block.

    Args:
        text: Input text containing potential code blocks

    Returns:
        tuple: (extracted_text, match_found) where extracted_text is the content
               from the last code block, and match_found is True if a
               triple-backtick block was found, otherwise False.
    """
    pattern = r"```[^\n]*\n([\s\S]*?)```"
    matches = re.findall(pattern, text)
    return (matches[-1], True) if matches else (text, False)


def parse_score_list(text: str) -> list[dict]:
    """
    Parse the score list from the text, handling JSON parsing.
    
    Args:
        text: The text to parse
        
    Returns:
        list: List of score dictionaries
    """
    code_content, found = extract_code_from_last_block(text)
    if not found:
        # If no code block found, try to parse the entire text as JSON
        return [ {"category": "grader_invalid_category_1"} ]

    try:
        # Try to parse as JSON array
        score_list = json.loads(code_content)
        if not isinstance(score_list, list):
            return [ {"category": "grader_invalid_category_2"} ]
        return score_list
    except json.JSONDecodeError:
        logger.error(f"Failed to parse score list as JSON: {code_content[:200]}...")
        return [ {"category": "grader_invalid_category_3"} ]


def get_completions(message_completer, prompt, renderer, max_retries=5):
    """
    Get completions from the language model with retry logic.
    
    Args:
        message_completer: The completer instance
        prompt: The prompt to send
        renderer: The renderer to use
        max_retries: Maximum number of retry attempts
        
    Returns:
        list: List of score dictionaries
    """
    messages: list[chat.Message] = [chat.Message.user(content=prompt)]
    convo = chat.Conversation(messages=messages)
    cur_convo = convo.model_copy()
    tokens = renderer.render_for_completion_multimodal_toklist(cur_convo, role=chat.Role.ASSISTANT)

    for attempt in range(max_retries):
        try:
            # Retry logic to handle potential issues with the completion
            completion = message_completer.completion(
                [tokens],
                max_tokens=32768,
                stop=[[200002]],
                temperature=1.0,
            )
            
            response_tokens = completion.choices[0].toklist.spans[0].tokens
            output_messages = renderer.decode(response_tokens)
            
            # Parse the score list from the output
            score_list = parse_score_list(output_messages)
            
            # Validate categories
            invalid_categories = False
            for score in score_list:
                if isinstance(score, dict) and "category" in score:
                    if score["category"] not in TOOL_USING_UNIFIED_GRADER[TOOL_CALL_MODE]["categories"]:
                        invalid_categories = True
                        break
                else:
                    invalid_categories = True
                    break
            
            if not invalid_categories:
                return score_list
            
        except Exception as e:
            write_to_local_file(
                message=f"Exception in LLM reward model generation (attempt {attempt + 1}/{max_retries}): {e}\n{traceback.format_exc()}",
                local_file_path=ERROR_LOG_PATH,
                threshold=1.0,
            )
            if attempt < max_retries - 1:
                logger.warning(f"Exception occurred, retrying... (attempt {attempt + 1}/{max_retries})")
            
    return [{"category": "grader_invalid_category"}]


@chz.chz(typecheck=True)
class ToolCallGrader(grader_module.Grader[datapoint.HarmonyCompletionDatapoint]):
    """
    Grader for evaluating tool usage in conversations.
    """

    topic: str = chz.field(
        doc="The topic or snapshot used for creating the engines.",
    )

    topic_mode_or_user: str = chz.field(
        doc="The topic mode or user used for creating the engines.",
    )

    renderer_name: str | None = chz.field(
        doc="The renderer name to use.",
        default=None,
    )

    line: str = chz.field(
        doc="The bus line to use.",
        default=DEFAULT_BUS_LINE,
    )

    qos_type: QoSType = chz.field(
        doc="The QoS type to use.",
        default=QoSType.ROUND_ROBIN_BY_POD,
    )

    max_grader_retries: int = chz.field(
        doc="The maximum number of retries for the grader.",
        default=5,
    )

    @chz.init_property
    def _llm_grader(self) -> BusTokenCompleter:
        bus_token_completer = BusTokenCompleter(
            topic_or_snapshot=self.topic,
            topic_mode_or_user=self.topic_mode_or_user,
            bus_line=self.line,
            qos_type=self.qos_type,

        )
        return bus_token_completer
    
    @chz.init_property
    def _renderer(self) -> HarmonyRendererV4:
        return get_renderer(self.renderer_name)

    @log_exception_to_file(ERROR_LOG_PATH)
    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]]:
        """
        Grade a batch of code generation samples.

        Args:
            samples: Batch of samples to grade
            sample_execution_context: Execution context (unused)
            profile_label: Profiling label (unused)

        Returns:
            List of graded samples
        """
        results = []

        for sample in samples:
            try:
                reward, score_list, prompt = self._grade_tool_usage(sample)
            except Exception as e:
                logger.error(f"Exception in grader {type(self).__name__}: {e}")
                write_to_local_file(
                    message=f"Exception in grader {type(self).__name__}: {e}\n{traceback.format_exc()}",
                    local_file_path=ERROR_LOG_PATH,
                    threshold=1.0,
                )
                reward, score_list, prompt = False,  [{"category": "grader_invalid_category_4"}], ""

            sample.metadata["grader"] = {
                "prompt": prompt,
                "response": "\n".join([json.dumps(score) for score in score_list]),
                "score": int(reward),
            }

            results.append(sample.with_correctness(reward_name=REWARD_NAME, is_correct=reward))

        return results

    def _truncate_string(self, text: str, max_length: int) -> str:
        """
        Truncate a string to a maximum length, keeping the beginning and end.

        Args:
            text: The input string to truncate
            max_length: Maximum number of tokens allowed

        Returns:
            The truncated string
        """ 
        tokens = self._renderer.encode(text)
        if len(tokens) <= max_length:
            return text
        
        half_length = max_length // 2
        return (
            self._renderer.decode(tokens[:half_length])
            + "...(truncated)..."
            + self._renderer.decode(tokens[-half_length:])
        )

    def _grade_tool_usage(self, sample) -> tuple[bool, list[dict], str]:
        """
        Grade a single tool usage sample.

        Args:
            sample: The sample datapoint generated by the model

        Returns:
            Tuple of (is_correct, score_list, prompt)
        """
        # Build conversation messages string
        conversation = sample.conversation
        sys_user_prompt = self._renderer.decode(sample.prompt_tokens.spans[0].tokens)
        convo_messages = f"<BEGIN_OF_PROMPT>\n\n{sys_user_prompt}\n\n<END_OF_PROMPT>\n\n"
        for message in conversation.messages:
            content_str = str(message.content)
            # Use different truncation lengths based on message role
            if message.author.role != 'tool':
                truncated_content = self._truncate_string(content_str, max_length=512)
            else:
                truncated_content = self._truncate_string(content_str, max_length=128)
            convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}> in <channel: {message.channel}>: {truncated_content}\n\n"

        write_to_local_file(
            message=f"An example of (truncated) conversation messages: {convo_messages}\n",
            local_file_path=STANDARD_LOG_PATH,
            threshold=LOG_SAMPLING_RATE,
        )

        reward, score_list, prompt = self.call_llm_grader(rollout=convo_messages)
        return reward, score_list, prompt

    def call_llm_grader(self, rollout: str) -> tuple[bool, list[dict], str]:
        """
        Grade code using LLM (Large Language Model).

        Args:
            rollout: Generated conversation messages

        Returns:
            Tuple of (reward, score_list, prompt)
        """
        prompt = TOOL_USING_UNIFIED_GRADER[TOOL_CALL_MODE]["instructions"].replace(
            PLACE_HOLDER_KEY_FOR_MESSAGE, rollout
        )

        score_list = get_completions(
            message_completer=self._llm_grader,
            prompt=prompt,
            renderer=self._renderer,
            max_retries=self.max_grader_retries,
        )

        # Determine reward based on score_list validity
        reward = len(score_list) == 0
        
        return reward, score_list, prompt
