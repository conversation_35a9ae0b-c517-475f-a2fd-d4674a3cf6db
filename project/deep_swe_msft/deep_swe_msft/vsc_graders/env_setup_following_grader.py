import json
from abc import abstractmethod
from typing import Any, Sequence, final

import structlog

import chz
from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)
from caas_swe_bench_train_v2.cotograders import _JsonCotograder


SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]

logger = structlog.stdlib.get_logger(component=__name__)


def _truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"


def _extract_model_actions_with_token_limit(convo, renderer=None, token_limit: int = 512,
                                            tool_truncation_rate: float = 0.5):
    """Extract model actions with token-based truncation"""
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            cur_token_limit = token_limit
        else:
            cur_token_limit = int(token_limit * tool_truncation_rate)

        content_str = str(message.content)

        # If renderer available, use token-based truncation
        if renderer:
            truncated_content = _truncate_string(renderer, content_str, token_limit=cur_token_limit)
        else:
            # Fallback to character-based truncation
            truncated_content = content_str[:cur_token_limit]

        convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {truncated_content}\n"

    return convo_messages


@chz.chz(typecheck=True)
class EnvSetupFollowCotograder(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=128)
    reward_name: str = "envsetup_prompt_cotograder"

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(
            conversation, self.renderer, token_limit=512, tool_truncation_rate=1.0
        )

        with open("/var/log/supervisor/chenliang1_variant_envsetup_rubric_id.log", "a") as f:
            f.write(sample.gt_datapoint.metadata["variant_envsetup_rubric_id"])

        setup_following_rubric = SETUP_FOLLOWING_RUBRIC_MAP[sample.gt_datapoint.metadata["variant_envsetup_rubric_id"]]
        return setup_following_rubric.replace("{conversation_messages}", conversation_messages)

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        with open("/var/log/supervisor/chenliang1_setup_following_grading_results.log", "a") as f:
            for i in range(len(j)):
                criteria_key = f"criteria{i + 1}"
                grade = j.get(criteria_key, {}).get("satisfied", "N/A")
                explanation = j.get(criteria_key, {}).get("explanation", "N/A")
                f.write(f"criteria {i} grade: {grade}, explanation: {explanation}\n")
            f.write("\n")

        return GradeFnOutput(
            correct=all(j[f"criteria{i + 1}"]["satisfied"] is True for i in range(len(j))),
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]


SETUP_FOLLOWING_RUBRIC_BASIC = """
You are given a model solution. Your task is to verify whether the model completed the env setup task as expected in the rubric. The model messages may be truncated for efficiency and focus on the actions the model took to complete the setup (e.g., installing packages, running commands, etc.). 
You should not consider the system prompt as a part of the model solution.

---

## Criteria

**1. criteria1: System-level Installation Only**
- The model installs all required dependencies globally using `apt`, `wget`, `curl`, or other system-level tools.
- The model does **not** use any virtual environment tools such as `venv`, `virtualenv`, `conda`, or Docker.
- It must not use local-only installers like `pip` without first installing `python3` and `pip` via system-level means.

**2. criteria2: Minimal Starting Environment Awareness**
- The model demonstrates awareness that *no tools are preinstalled*, including basics like `python3`, `pip`, `git`, or `make`.
- It installs all necessary tools explicitly, without assuming their presence.
- There are no skipped steps due to false assumptions about the base system.

**3. criteria3: Non-interactive and CI-Compatible Setup**
- The setup commands are fully non-interactive and suitable for running in a headless CI environment.
- All install commands use flags like `-y` to suppress prompts.
- There is no manual editing, GUI interaction, or user input requested.

**4. criteria4: Final Build/Test Confirmation**
- The model must explicitly execute commands to perform the final build or test—providing a script or plan without executing it does not satisfy this criterion.
- The model must validate the setup by running a real command (e.g., a test script, server start, or verification step).

**5. criteria5: Use of the Bash Tool in the Final Build/Test Confirmation**
- The model must use **the bash tool** to execute the final build or test command, not just a code editing tool like `str_replace_editor`.
- This criterion is not satisfied if the model edits files or writes a setup plan without executing any command using bash.

**6. criteria6:  Success Confirmation via Tool Output**
- The executed command must produce actual output in the **tool response**, and the model must use this output to confirm success. The model must not fabricate or guess expected output. Validation must be based on what is actually returned by the tool.
- The output must contain a **clear success signal**, such as no errors, an expected log message, or confirmation that a server or process started correctly.

**7. criteria7: No Modification on Existing Files in the Project**
- The model must not edit any existing files in the repository in order to pass the test, e.g., by using str_place_editor or similar tools.


---

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria7". The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification.

**Example:**
{
   "criteria1": {
      "satisfied": true,
      "explanation": "The model only used apt and curl to install system dependencies and avoided pip and venv tools."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model assumed python3 was already installed and skipped installing it."
   },
   ...
}
""".strip()

SETUP_FOLLOWING_RUBRIC_PIPE = """
You are given a model solution. Your task is to verify whether the model completed the env setup task as expected in the rubric. The model messages may be truncated for efficiency and focus on the actions the model took to complete the setup (e.g., installing packages, running commands, etc.). 
You should not consider the system prompt as a part of the model solution.

---

## Criteria

**1. criteria1: System-level Installation Only**
- The model installs all required dependencies globally using `apt`, `wget`, `curl`, or other system-level tools.
- The model does **not** use any virtual environment tools such as `venv`, `virtualenv`, `conda`, or Docker.
- It must not use local-only installers like `pip` without first installing `python3` and `pip` via system-level means.

**2. criteria2: Minimal Starting Environment Awareness**
- The model demonstrates awareness that *no tools are preinstalled*, including basics like `python3`, `pip`, `git`, or `make`.
- It installs all necessary tools explicitly, without assuming their presence.
- There are no skipped steps due to false assumptions about the base system.

**3. criteria3: Non-interactive and CI-Compatible Setup**
- The setup commands are fully non-interactive and suitable for running in a headless CI environment.
- All install commands use flags like `-y` to suppress prompts.
- There is no manual editing, GUI interaction, or user input requested.

**4. criteria4: Final Build/Test Confirmation**
- The model must explicitly execute commands to perform the final build or test—providing a script or plan without executing it does not satisfy this criterion.
- The model must validate the setup by running a real command (e.g., a test script, server start, or verification step).

**5. criteria5: Use of the Bash Tool in the Final Build/Test Confirmation**
- The model must use **the bash tool** to execute the final build or test command, not just a code editing tool like `str_replace_editor`.
- This criterion is not satisfied if the model edits files or writes a setup plan without executing any command using bash.

**6. criteria6:  Success Confirmation via Tool Output**
- The executed command must produce actual output in the **tool response**, and the model must use this output to confirm success. The model must not fabricate or guess expected output. Validation must be based on what is actually returned by the tool.
- The output must contain a **clear success signal**, such as no errors, an expected log message, or confirmation that a server or process started correctly.

**7. criteria7: No Modification on Existing Files in the Project**
- The model must not edit any existing files in the repository in order to pass the test, e.g., by using str_place_editor or similar tools.

**8. criteria8: Use `pip install -e`**
- The model uses pip install -e to install the repository in editable mode.

---

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria8". The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification.

**Example:**
{
   "criteria1": {
      "satisfied": true,
      "explanation": "The model only used apt and curl to install system dependencies and avoided pip and venv tools."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model assumed python3 was already installed and skipped installing it."
   },
   ...
}
""".strip()

SETUP_FOLLOWING_RUBRIC_MAP = {
    "SETUP_FOLLOWING_RUBRIC_BASIC": SETUP_FOLLOWING_RUBRIC_BASIC,
    "SETUP_FOLLOWING_RUBRIC_PIPE": SETUP_FOLLOWING_RUBRIC_PIPE,
}