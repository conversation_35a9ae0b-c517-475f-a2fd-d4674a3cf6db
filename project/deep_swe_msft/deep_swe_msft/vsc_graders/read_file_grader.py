from typing import Sequence
import json

import berry
import chz
from berry.sample import GraderError
from qstar.graders.grader import Grader

REWARD_NAME = "repeat_reading"

@chz.chz(typecheck=True)
class RepeatReadingGrader(Grader[berry.DatapointT]):
    def _grade_batch_inner(
        self,
        samples: Sequence[berry.SampleWithCompletion[berry.DatapointT]],
        sample_execution_context: berry.SampleExecutionContext | None = None,
    ) -> list[berry.SampleWithGrade[berry.DatapointT]]:
        return [self._grade_sample(sample) for sample in samples]

    def _grade_sample(
        self,
        sample: berry.SampleWithCompletion[berry.DatapointT],
    ) -> berry.SampleWithGrade[berry.DatapointT]:
        
        if not sample.conversation:
            return sample.with_grader_error(
                GraderError(
                    error_str="No sample.conversation, cannot grade",
                ),
                with_reward=REWARD_NAME,
            )

        prompt = "RepeatReadingGrader checks for overlapping file reads"
        conversation = sample.conversation
        
        # Track file read ranges for this sample
        file_read_ranges = {}  # {filepath: [(start, end), ...]}

        for message in conversation.messages:
            if message.recipient == "functions.read_file":
                # Parse read_file call
                try:
                    params = json.loads(message.content.text)
                    filepath = params.get("filePath")
                    offset = params.get("offset", 1)
                    limit = params.get("limit", 2000)

                    if filepath:
                        start_line = offset
                        end_line = offset + limit - 1

                        if filepath not in file_read_ranges:
                            file_read_ranges[filepath] = []

                        file_read_ranges[filepath].append((start_line, end_line))
                except Exception as e:
                    sample.metadata["grader"] = {
                        "prompt": prompt,
                        "response": f"Error parsing read_file call: {e}",
                        "score": 0,
                    }
                    return sample.with_correctness(reward_name=REWARD_NAME, is_correct=False)

        # Check for overlaps after collecting all ranges
        has_repeat_reading = False
        repeat_reading_details = []
        for filepath, ranges in file_read_ranges.items():
            if len(ranges) <= 1:
                continue
            
            # Sort ranges by start position
            sorted_ranges = sorted(ranges, key=lambda x: x[0])
            
            # Check for overlaps in sorted ranges
            for i in range(len(sorted_ranges) - 1):
                current_start, current_end = sorted_ranges[i]
                next_start, next_end = sorted_ranges[i + 1]
                
                # Check if current range overlaps with next range
                if current_end >= next_start:
                    has_repeat_reading = True
                    overlap_start = max(current_start, next_start)
                    overlap_end = min(current_end, next_end)
                    repeat_reading_details.append({
                        "file": filepath,
                        "range1": (current_start, current_end),
                        "range2": (next_start, next_end),
                        "overlap": (overlap_start, overlap_end)
                    })
                    break
            
            if has_repeat_reading:
                break

        if not has_repeat_reading:
            sample.metadata["grader"] = {
                "prompt": prompt,
                "response": "No repeat reading detected",
                "score": 1,
            }
            return sample.with_correctness(reward_name=REWARD_NAME, is_correct=True)
        else:
            detail = repeat_reading_details[0]  # Get first detected overlap
            response = f"Repeat reading detected in file '{detail['file']}': range {detail['range1']} overlaps with range {detail['range2']} (overlap: lines {detail['overlap'][0]}-{detail['overlap'][1]})"
            sample.metadata["grader"] = {
                "prompt": prompt,
                "response": response,
                "score": 0,
            }
            return sample.with_correctness(reward_name=REWARD_NAME, is_correct=False)
