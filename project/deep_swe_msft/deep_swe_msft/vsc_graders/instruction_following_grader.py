import json
from abc import abstractmethod
from typing import Any, Sequence, final

import structlog

import chz
from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)
from caas_swe_bench_train_v2.cotograders import _JsonCotograder

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]

logger = structlog.stdlib.get_logger(component=__name__)

def _truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"


def _extract_model_actions_with_token_limit(convo, renderer=None, token_limit: int = 512,
                                            tool_truncation_rate: float = 0.5):
    """Extract model actions with token-based truncation"""
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            cur_token_limit = token_limit
        else:
            cur_token_limit = int(token_limit * tool_truncation_rate)

        content_str = str(message.content)

        # If renderer available, use token-based truncation
        if renderer:
            truncated_content = _truncate_string(renderer, content_str, token_limit=cur_token_limit)
        else:
            # Fallback to character-based truncation
            truncated_content = content_str[:cur_token_limit]

        convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {truncated_content}\n"

    return convo_messages


@chz.chz(typecheck=True)
class InstructFollowCotograder(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=128)
    reward_name: str = "instruct_follow_cotograder"

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(
            conversation, self.renderer, token_limit=256, tool_truncation_rate=0.25
        )

        instruct_following_rubic = RRB_INSTRUCT_FOLLOWING_INSTRUCTONLY_TEMPLATE.replace(
            "{model_instruct}", sample.gt_datapoint.metadata["instruct"],
        ).replace(
            "{conversation_messages}", conversation_messages,
        )
        with open("/var/log/supervisor/qingruzhang_rrb_if_instruct_grader.log", "a") as f:
            f.write(
                f"Rubic: {sample.gt_datapoint.metadata['instruct']} \n Grader Prompt: {instruct_following_rubic}\n")

        return instruct_following_rubic

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        with open("/var/log/supervisor/qingruzhang_instruct_following_grading_results.log", "a") as f:
            for i in range(len(j)):
                criteria_key = f"judgement"
                # criteria_key = f"criteria{i + 1}"
                grade = j.get(criteria_key, {}).get("satisfied", "N/A")
                explanation = j.get(criteria_key, {}).get("explanation", "N/A")
                f.write(
                    f"criteria {i} grade: {grade}, explanation: {explanation}\n")
            f.write("\n")

        grade = j.get("judgement", {}).get("satisfied", None)
        return GradeFnOutput(
            correct=(grade is True),
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]


@chz.chz(typecheck=True)
class InstructFollowRubricCotograder(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=128)
    reward_name: str = "instruct_follow_cotograder"

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(
            conversation, self.renderer, token_limit=256, tool_truncation_rate=0.25
        )

        instruct_following_rubic = RRB_INSTRUCT_FOLLOWING_RUBIC_TEMPLATE.replace(
            "{model_instruct}", sample.gt_datapoint.metadata["instruct"],
        ).replace(
            "{criteria}", sample.gt_datapoint.metadata["rubic"],
        ).replace(
            "{conversation_messages}", conversation_messages,
        )
        with open("/var/log/supervisor/qingruzhang_rrb_if_rubic_grader.log", "a") as f:
            f.write(f"Rubic: {sample.gt_datapoint.metadata['rubic']} \n Grader Prompt: {instruct_following_rubic}\n")

        return instruct_following_rubic

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        with open("/var/log/supervisor/qingruzhang_instruct_following_grading_results.log", "a") as f:
            for i in range(len(j)):
                criteria_key = f"criteria{i + 1}"
                grade = j.get(criteria_key, {}).get("satisfied", "N/A")
                explanation = j.get(criteria_key, {}).get("explanation", "N/A")
                f.write(f"criteria {i} grade: {grade}, explanation: {explanation}\n")
            f.write("\n")

        return GradeFnOutput(
            correct=all(j[f"criteria{i + 1}"]["satisfied"] is True for i in range(len(j))),
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]


@chz.chz(typecheck=True)
class SWEInstructFollowCotograder(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=128)
    reward_name: str = "swe_instruct_follow_cotograder"

    def make_prompt(self, sample: SampleWithCompletion) -> str:

        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(
            conversation, self.renderer, token_limit=256, tool_truncation_rate=0.25
        )

        instruct_following_rubic = SWE_INSTRUCT_FOLLOWING_FILETREE_TEMPLATE.replace(
            "{model_instruct}", sample.gt_datapoint.metadata["prompt"],
        ).replace(
            "{file_tree}", sample.gt_datapoint.metadata["file_tree"],
        ).replace(
            "{conversation_messages}", conversation_messages,
        )
        with open("/var/log/supervisor/qingruzhang_swe_if_instruct_grader.log", "a") as f:
            f.write(
                f"Instruct: {sample.gt_datapoint.metadata['prompt']} \n Grader Prompt: {instruct_following_rubic}\n")

        return instruct_following_rubic

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:

        with open("/var/log/supervisor/qingruzhang_instruct_following_grading_results.log", "a") as f:
            for key in j:
                grade_i = j.get(key, {}).get("satisfied", "N/A")
                explanation = j.get(key, {}).get("explanation", "N/A")
                f.write(
                    f"{key} grade: {grade_i}, explanation: {explanation}\n")
            f.write("\n")

        grade = True
        for key, val in j.items():
            if val["satisfied"] is not True:
                grade = False
                break

        return GradeFnOutput(
            correct=(grade is True),
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]


RRB_INSTRUCT_FOLLOWING_INSTRUCTONLY_TEMPLATE = """
You will judge whether a model solution correctly followed a given instruction.
The model messages may be truncated for efficiency and focus on the actions the model took to complete the insturction (e.g., add/delete specific files, write readme or summaries, add tests, etc.). 
You should not consider the system prompt as a part of the model solution.
*Do not* run external tools, CI pipelines, or the code itself for evaluation. Your decision must be based *only* on the provided model's conversation history and its solution.

---
Here is the instruction that the model was supposed to follow 

## Instruction

{model_instruct}

---

§ Model Solution:
```
{conversation_messages}
```

---

Evaluate the Model Solution against the Instruction and decide if the instruction was fully satisfied. Your final # Answer should be in JSON. You must list your judgement as the following examples. The "satisfied" field should be true if the model met all requirements in the instruction; otherwise false. The "explanation" field should provide a brief justification.

**Example:**
{
   "judgement": {
      "satisfied": true,
      "explanation": "All existing and newly added tests were run and reported as passing."
   },
}
""".strip()

RRB_INSTRUCT_FOLLOWING_RUBIC_TEMPLATE = """
You are given a model solution. Your task is to verify whether the model completed the insturction following task as expected in the rubric. The model messages may be truncated for efficiency and focus on the actions the model took to complete the insturction (e.g., add/delete specific files, write readme, add tests, etc.). 
You should not consider the system prompt as a part of the model solution.
*Do not* run external tools, CI pipelines, or the code itself for evaluation. Your decision must be based *only* on the provided model's conversation history and its solution.

---

Here is the instruction that the model was supposed to complete:

## Instruction 

{model_instruct}

---

Here is the rubic that you should follow to evaluate the model solution. 

## Criteria

{criteria} 

---

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as the following examples. The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification.

**Example:**
{
   "criteria1": {
      "satisfied": true,
      "explanation": "The model only used apt and curl to install system dependencies and avoided pip and venv tools."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model assumed python3 was already installed and skipped installing it."
   },
   ...
}
""".strip()

SWE_INSTRUCT_FOLLOWING_FILETREE_TEMPLATE = """
You will judge whether a model correctly addressed a set of software-engineering tasks for a repository whose File Tree structure are shown below. Each task is described in detail as an instruction.
The model messages may be truncated for efficiency and focus on the actions the model took to complete the insturction (e.g., add/delete specific files, write readme or summaries, add tests, etc.). 
You should not consider the system prompt as a part of the model solution.
*Do not* run external tools, CI pipelines, or the code itself for evaluation. Your decision must be based *only* on the provided model's conversation history and its solution.

---
Here is the problem that the model was supposed to address:  

## Problem -- contains a list of instructions the model had to follow

{model_instruct}

---

## Repository File Tree – provided for reference:

{file_tree}

---

§ Model Solution:
```
{conversation_messages}
```

---

Evaluate the Model Solution against the Tasks and decide if each instruction was fully satisfied. Your final # Answer should be in JSON. You must list your judegement on all instructions as the following examples. The "satisfied" field should be true if the model met this instruction; otherwise false. The "explanation" field should provide a brief justification.

**Example:**
{
   "instruction1": {
      "satisfied": true,
      "explanation": "The model only edit the specified files."
   },
    "instruction2": {
        "satisfied": false,
        "explanation": "The model did not add any new tests as required."
    }, 
    ...
}
""".strip()




