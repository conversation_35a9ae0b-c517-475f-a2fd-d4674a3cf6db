from typing import Sequence

import berry
import chz
from berry.sample import GraderError
from qstar.graders.grader import Grader

REWARD_NAME = "func_enforcement"
ENCOURAGED_FUNCS = ['functions.grep_search', 'functions.apply_patch', 'functions.file_search', 'functions.read_file', 'functions.semantic_search']

@chz.chz(typecheck=True)
class FuncEnforcementGrader(Grader[berry.DatapointT]):
    target_ratio: float = 0.1

    def _grade_batch_inner(
        self,
        samples: Sequence[berry.SampleWithCompletion[berry.DatapointT]],
        sample_execution_context: berry.SampleExecutionContext | None = None,
    ) -> list[berry.SampleWithGrade[berry.DatapointT]]:
        return [self._grade_sample(sample) for sample in samples]

    def _grade_sample(
        self,
        sample: berry.SampleWithCompletion[berry.DatapointT],
    ) -> berry.SampleWithGrade[berry.DatapointT]:
        if not sample.conversation:
            return sample.with_grader_error(
                GraderError(
                    error_str="No sample.conversation, cannot grade",
                ),
                with_reward=REWARD_NAME,
            )

        prompt = "FuncEnforcementGrader requires functions: " + ", ".join(ENCOURAGED_FUNCS)
        len_messages = len(sample.conversation.messages)

        num_encouraged_funcs = 0
        for message in sample.conversation.messages:
            if message.recipient in ENCOURAGED_FUNCS:
                num_encouraged_funcs += 1

        encourage_ratio = num_encouraged_funcs / len_messages
        match_requirement = encourage_ratio >= self.target_ratio

        if match_requirement:
            sample.metadata["grader"] = {
                "prompt": prompt,
                "response": f"encouraged_funcs: {encourage_ratio}",
                "score": 1,
            }
            return sample.with_correctness(reward_name=REWARD_NAME, is_correct=True)
        else:
            sample.metadata["grader"] = {
                "prompt": prompt,
                "response": f"Required functions were not used. encouraged_funcs: {encourage_ratio}",
                "score": 0,
            }
            return sample.with_correctness(reward_name=REWARD_NAME, is_correct=False)
