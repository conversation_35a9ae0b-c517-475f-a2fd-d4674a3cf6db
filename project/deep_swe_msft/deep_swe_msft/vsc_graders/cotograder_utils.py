import chz
import berry
from functools import partial
from qstar.presets.chz_utils import IsOverride, override
from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    GraderService,
    TokenCompleterGraderService,
)
from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from token_completer import TokenCompleter
import logging
import functools
import traceback
import datetime
import random

# BUS configuration for CotoGrader
COTOGRADER_BUS_USER = "gpt-oss-rkld"
COTOGRADER_BUS_TOPIC = "az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
COTOGRADER_BUS_LINE = "bus"
COTOGRADER_RENDERER = "harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"
COTOGRADER_QOS_TYPE = QoSType.ROUND_ROBIN_BY_POD
COTOGRADER_REDIS_ID = "cotograder-290"  # Makes it easy to swap out the bus user if we need to

COTOGRADER_CHZ_ARGV = [
    f"grader_service.token_completer.bus_line={COTOGRADER_BUS_LINE}",
    f"grader_service.token_completer.qos_type=bus_token_completer:QoSType.{COTOGRADER_QOS_TYPE}",
    f"grader_service.token_completer.topic_mode_or_user={COTOGRADER_BUS_USER}",
    f"grader_service.token_completer.topic_or_snapshot={COTOGRADER_BUS_TOPIC}",
    "grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
    f"grader_service.redis_config_id={COTOGRADER_REDIS_ID}",
    "grader_service=TokenCompleterGraderService",
    f"renderer_name={COTOGRADER_RENDERER}",
]

# COTOGRADER_BUS_USER = "swe-main-run"
# COTOGRADER_BUS_TOPIC = f"az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted"
# COTOGRADER_BUS_LINE = "bus"
# COTOGRADER_RENDERER = "harmony_v4.0.16_berry_v3_128k_orion_mm_no_budget_truncate"
# COTOGRADER_REDIS_ID = "cotograder-290"
# COTOGRADER_QOS_TYPE = QoSType.ROUND_ROBIN_BY_POD
# COTOGRADER_CHZ_ARGV = [
#     f"grader_service.token_completer.bus_line={COTOGRADER_BUS_LINE}",
#     "grader_service.token_completer.qos_type=bus_token_completer:QoSType.ROUND_ROBIN_BY_POD",
#     f"grader_service.token_completer.topic_mode_or_user={COTOGRADER_BUS_USER}",
#     f"grader_service.token_completer.topic_or_snapshot={COTOGRADER_BUS_TOPIC}",
#     "grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
#     "grader_service.token_completer.default_timeout=1200",
#     f"grader_service.redis_config_id={COTOGRADER_REDIS_ID}",
#     "grader_service=TokenCompleterGraderService",
#     f"renderer_name={COTOGRADER_RENDERER}",
# ]

@chz.chz(typecheck=True)
class CotoGraderService(TokenCompleterGraderService, IsOverride):
    token_completer: TokenCompleter.Config = override(
        partial(
            BusTokenCompleter.Config,
            topic_or_snapshot=COTOGRADER_BUS_TOPIC,
            topic_mode_or_user=COTOGRADER_BUS_USER,
            bus_line=COTOGRADER_BUS_LINE,
            qos_type=QoSType.ROUND_ROBIN_BY_POD,
        ),
    )
    redis_config_id: str = COTOGRADER_REDIS_ID

@chz.chz(typecheck=True)
class CotoGraderConfig(BerryGraderConfig, IsOverride):
    grader_reward_multiplier: int = 64
    grader_top_p: float = 0.985

def create_logger() -> logging.Logger:
    """
    Create and configure a logger for codechat operations.

    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(__name__)

    # Avoid duplicate handlers
    if logger.handlers:
        return logger

    logger.setLevel(logging.INFO)

    # Create file handler
    file_handler = logging.FileHandler("codechat.log")
    file_handler.setLevel(logging.INFO)

    # Create formatter
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(formatter)

    logger.addHandler(file_handler)
    return logger

logger = create_logger()

def log_exception_to_file(log_file: str = "exceptions.log"):
    """
    Decorator to log exceptions to a file while preserving the original exception.

    Args:
        log_file: Path to the log file

    Returns:
        Decorator function
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Create detailed error message
                error_message = (
                    f"Exception in {func.__name__}:\n"
                    f"Error: {str(e)}\n"
                    f"Traceback:\n{traceback.format_exc()}\n"
                    f"{'='*50}\n"
                )

                # Log to file
                try:
                    with open(log_file, "a") as file:
                        file.write(f"{datetime.datetime.now()} - {error_message}")
                except IOError as log_error:
                    logger.error(f"Failed to write exception to log file: {log_error}")

                # Re-raise the original exception
                raise e

        return wrapper

    return decorator

def write_to_local_file(
    message: str, local_file_path: str = "/var/log/supervisor/haoran_grader_std_logs", threshold: float = 1.0
) -> None:
    """
    Write message to local file with sampling based on threshold.

    Args:
        message: Message to write
        local_file_path: Path to log file
        threshold: Probability of writing (0.0 to 1.0)
    """
    if random.random() < threshold:
        timestamp = str(datetime.datetime.now())
        formatted_message = f"{timestamp} {message}"

        if not message.endswith("\n"):
            formatted_message += "\n"

        try:
            with open(local_file_path, "a+") as log_file:
                log_file.write(formatted_message)
        except IOError as e:
            logger.error(f"Failed to write to log file {local_file_path}: {e}")