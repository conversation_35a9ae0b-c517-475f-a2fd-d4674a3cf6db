PLACE_HOLDER_KEY_FOR_MESSAGE = "<place_holder_key_for_message>"

TOOL_USING_PROMPT_CATEGORIES_LOOKUP = {
    # ordered by the most severe to the least severe
    "invalid_tool_call": """A tool call is valid only if ALL are true:
- The assistant message includes recipient="<fully-qualified-tool-name>" that exists in system/developer tools.
- The tool name exactly matches a defined tool (no aliases/typos).
- Arguments conform to the tool schema (correct field names, required fields present, correct types/shapes, and value constraints).
- The tool executes without a usage error (schema/validation/type/unknown-field/missing-field errors, e.g., VSCode Copilot tool error). 
  Errors occurring after a successful invocation that are unrelated to call correctness (e.g., remote runtime failure, rate limit from the external service) do NOT make the call invalid.""",

    "overlap_reading": """(read_file tool only) Multiple reads of the SAME file must NOT have overlapping line ranges unless justified.
- Line range = [offset, offset+limit-1]; defaults: offset=1, limit=2000.
- Overlap includes identical, partial, superset, or subset ranges.
- Exceptions where overlap is acceptable, necessary and reasonable (e.g., re-examining code after gathering new context, verifying changes, or when earlier context was lost due to conversation length).""",

    "hallucinated_tool_arguments": """Every argument in a tool call must be grounded in prior context (conversation text, developer instructions, or earlier tool results). If a value cannot be grounded, the LLM must obtain it through a tool call.""",

    "hallucinated_cot": """All factual details in the assistant’s natural-language reply that depend on tool outputs **must** be traceable to the conversation history or tool responses.  If the LLM did not actually call a tool, it must not claim that it did.""",
    
    "not_following_instructions": """When calling a tool, the LLM should follow the instructions specified in the system or developer prompt relating to the tool usage. This includes the functions definition in the developer section when it is present.""",
   
    "unnecessary_tool_call": """A tool call is necessary only if it:
1. Directly addresses the user’s request,
2. Performs a justified intermediate step (e.g., search → filter → answer), or
3. Fulfills an explicit developer/system requirement (e.g.,
    authentication).
Redundant tool calls that are not useful for problem solving are categorized into unnecessary_tool_call. Repeating the same call is allowed only when the previous response indicates a transient error that may succeed on retry or the information LLM will get is not redundant (such as `read_file`).""",

    "invalid_final_answer": """The final message in the final channel must include a comprehensive, factual report of the solution. INVALID if any of the following hold:
- Missing a clear, chronological trajectory of work explaining how the solution was reached. The trajectory must: (a) enumerate major steps in order (investigation, reproduction, hypotheses, tool calls/commands, edits, verification); (b) for each step, state the goal, inputs/assumptions, evidence/results (summaries of logs/tool outputs), and the decision/rationale for the next action; (c) map steps to concrete changes (files/lines/commits) if applicable.
- Any statement is untrue, contradicted by tools/conversation, or not traceable to repository state and tool outputs (e.g., claiming edits to files that were not changed, fabricating test outcomes).
- Vague/non-actionable descriptions (“fixed issue”, “tests passed”) without evidence or commands to reproduce.
- For partial/incomplete work, failure to clearly state what remains, blockers, and next steps.
- For non-code tasks, absence of an analogous final report covering objective, actions taken, verification, and results.""",
}

TOOL_USING_PROMPT_CATEGORIES_LOOKUP_EASY = {
    # ordered by the most severe to the least severe
#     "invalid_tool_call": """A tool call is valid only if ALL are true:
# - The assistant message includes recipient="<fully-qualified-tool-name>" that exists in system/developer tools.
# - The tool name exactly matches a defined tool (no aliases/typos).
# - Arguments conform to the tool schema (correct field names, required fields present, correct types/shapes, and value constraints).
# - The tool executes without a usage error (schema/validation/type/unknown-field/missing-field errors, e.g., VSCode Copilot tool error). 
#   Errors occurring after a successful invocation that are unrelated to call correctness (e.g., remote runtime failure, rate limit from the external service) do NOT make the call invalid.""",

    "no_overlap_reading": """(read_file tool only) Multiple reads of the SAME file must NOT have overlapping line ranges unless justified.
- Line range = [offset, offset+limit-1]; defaults: offset=1, limit=2000.
- Overlap includes identical, partial, superset, or subset ranges.
- Exceptions where overlap is acceptable, necessary and reasonable (e.g., re-examining code after gathering new context, verifying changes, or when earlier context was lost due to conversation length).""",

    "done_all_requests": """The assistant must complete ALL tasks and actions requested by the user. INVALID if:
- Any explicit user request is ignored or not attempted (e.g., "fix the bug AND add tests" but only bug is fixed).
- Required subtasks are skipped (e.g., user asks to refactor code but assistant doesn't update related documentation/tests).
- The assistant stops prematurely without completing all requested work or providing clear blockers.
- Partial solutions are presented as complete without acknowledging remaining work.
- User's specific requirements/constraints are not followed (e.g., "use regex" but assistant uses string methods instead).""",

    "use_run_tests_tool": """When testing files or running test suites, the assistant MUST use the "runTests" tool exclusively. INVALID if:
- Using direct pytest commands (e.g., "pytest", "pytest -q", "python -m pytest tests/...")
- Using bash scripts to run tests (e.g., "bash run_tests.sh", "bash /root/run_tests.sh")
- Using any other testing command or method instead of the runTests tool
- The only valid way to run tests is by calling the runTests tool with appropriate arguments.""",

    "proper_todo_list": """(manage_todo_list tool only) Proper creation and maintenance of a structured execution plan (todo list). Apply ONLY if at least one manage_todo_list tool call appears during the process; if the tool is never invoked (e.g., task judged trivial) do NOT assign a proper_todo_list error. INVALID if ANY of:
- No coherent, self-contained plan established early: first write operation must enumerate a multi-step plan.
- Inconsistent or lossy evolution: later write calls drop, rename, or merge unresolved items without explicit rationale or status carry‑over (silent deletion / rewrite to hide unfinished work).
- Premature completion hack: marking all items done in one jump without intermediate evidence of progress (no prior partial updates, especially for inherently multi-step tasks) OR replacing the list with only completed items erasing history.
- Final state not fully resolved: final write (or absence thereof when conversation ends) leaves any item neither clearly Done nor explicitly Deferred/Cancelled with justification; or introduces brand‑new unfinished items at the end.
- Structural shortcutting or stagnation: never updating statuses as work proceeds (all items remain "pending" until a sudden all-done), or repeatedly rewriting the entire list with materially identical content just to satisfy tool usage without reflecting progress.
""",
}


CATEGORIES_FORMATTED = "\n\n".join(
    [f"<{k}>\n{v}\n</{k}>" for k, v in TOOL_USING_PROMPT_CATEGORIES_LOOKUP.items()]
)

CATEGORIES_FORMATTED_EASY = "\n\n".join(
    [f"<{k}>\n{v}\n</{k}>" for k, v in TOOL_USING_PROMPT_CATEGORIES_LOOKUP_EASY.items()]
)


TOOL_USING_ERROR_COUNTER_PROMPT = f"""
We aim to train a steerable assistant that is the most capable at following tool specifications to use the tools.

The tool specifications will be in the system prompt. In addition, the developer prompt may include developer defined functions available to the LLM and instructions for the LLM to use the functions. Tools are grouped by namespace, and each namespace has a unique name and may have one or more sub tools.

IMPORTANT: Only tools defined under `# Tools` in system and developer message are available to the LLM for the current turn you are grading. Tools used by assistant in previous turns but not defined in system/developer message should be considered as disabled/invalid tools.

A "tool call" is defined as a message with recipient: $tool_name, role: assistant.
A "tool response" is defined as a message with role: tool, name: $tool_name.
$tool_name here should be fully qualified, in the format of $namespace.$sub_tool_name.

There is not a 1:1 relation between tool calls and tool responses.
Each tool call may have many responses; all messages between the tool call and the next tool call or the end of the conversation are responses to that tool call.
After calling all the tools and receiving the responses, the LLM ALWAYS provides a final text (non tool call) response (empty response allowed) to the user as the last message to end the turn. It is impossible for the LLM to end the turn without providing a final text response.

Certain tools are always called in the analysis (chain-of-thought) channels, which means they are not visible to the user. Currently, ALL tools are always called in the analysis channels.

We want you to closely examine the following:

{CATEGORIES_FORMATTED}

Remember to only apply these errors to specific tools, not general errors. Note that the messages you got to examine may be truncated, so you may not see the full conversation. You may find the system prompt, user prompt and tool use messages. Please ONLY examine the following messages after <END_OF_PROMPT> and identify any errors in the tool calls made by the assistant.

{PLACE_HOLDER_KEY_FOR_MESSAGE}

Your response should be a json object: a list of dicts, with one dict per error identified.  Each dict MUST HAVE THE FOLLOWING two keys:
- "category", which is the category of the error; valid categories are: {list(TOOL_USING_PROMPT_CATEGORIES_LOOKUP.keys())}.
- "tool", which is the tool associated with the error. For invalid_final_answer, use N/A.
- "rationale", which is a 1-2 sentence explanation of the error

For example, [{{"category": "overlap_reading", "tool": "read_file", "rationale": "Multiple reads of the same file have overlapping lines from line 200 to line 230."}}]

If there are no errors, respond with an empty list.

**Use the following format at the end of your response, where you need to put the list in a json code block:**
```json
[]
```
"""

TOOL_USING_ERROR_COUNTER_PROMPT_EASY = f"""
We aim to train a steerable assistant that is the most capable at following tool specifications to use the tools.

The tool specifications will be in the system prompt. In addition, the developer prompt may include developer defined functions available to the LLM and instructions for the LLM to use the functions. Tools are grouped by namespace, and each namespace has a unique name and may have one or more sub tools.

IMPORTANT: Only tools defined under `# Tools` in system and developer message are available to the LLM for the current turn you are grading. Tools used by assistant in previous turns but not defined in system/developer message should be considered as disabled/invalid tools.

A "tool call" is defined as a message with recipient: $tool_name, role: assistant.
A "tool response" is defined as a message with role: tool, name: $tool_name.
$tool_name here should be fully qualified, in the format of $namespace.$sub_tool_name.

There is not a 1:1 relation between tool calls and tool responses.
Each tool call may have many responses; all messages between the tool call and the next tool call or the end of the conversation are responses to that tool call.
After calling all the tools and receiving the responses, the LLM ALWAYS provides a final text (non tool call) response (empty response allowed) to the user as the last message to end the turn. It is impossible for the LLM to end the turn without providing a final text response.

Certain tools are always called in the analysis (chain-of-thought) channels, which means they are not visible to the user. Currently, ALL tools are always called in the analysis channels.

We want you to closely examine the following:

{CATEGORIES_FORMATTED_EASY}

Remember to only apply these errors to specific tools, not general errors. Note that the messages you got to examine may be truncated, so you may not see the full conversation. You may find the system prompt, user prompt and tool use messages. Please ONLY examine the following messages after <END_OF_PROMPT> and identify any errors in the tool calls made by the assistant.

{PLACE_HOLDER_KEY_FOR_MESSAGE}

Your response should be a json object: a list of dicts, with one dict per error identified.  Each dict MUST HAVE THE FOLLOWING two keys:
- "category", which is the category of the error; valid categories are: {list(TOOL_USING_PROMPT_CATEGORIES_LOOKUP_EASY.keys())}.
- "tool", which is the tool associated with the error. For invalid_final_answer, use N/A.
- "rationale", which is a 1-2 sentence explanation of the error

For example, [{{"category": "overlap_reading", "tool": "read_file", "rationale": "Multiple reads of the same file have overlapping lines from line 200 to line 230."}}]

If there are no errors, respond with an empty list.

**Use the following format at the end of your response, where you need to put the list in a json code block:**
```json
[]
```
"""


TOOL_USING_UNIFIED_GRADER_ALL = {
    "instructions": TOOL_USING_ERROR_COUNTER_PROMPT,
    "categories": TOOL_USING_PROMPT_CATEGORIES_LOOKUP.keys(),
}

TOOL_USING_UNIFIED_GRADER_EASY = {
    "instructions": TOOL_USING_ERROR_COUNTER_PROMPT_EASY,
    "categories": TOOL_USING_PROMPT_CATEGORIES_LOOKUP_EASY.keys(),
}


TOOL_USING_UNIFIED_GRADER = {
    "all": TOOL_USING_UNIFIED_GRADER_ALL,
    "easy": TOOL_USING_UNIFIED_GRADER_EASY,
}