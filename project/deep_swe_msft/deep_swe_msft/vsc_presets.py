import berry.preset_utils
import deep_swe_msft.qa_vsc.datasets.configs as qa_configs
import deep_swe_msft.telemetry_vsc.telemetry_qa.datasets.configs as telemetry_qa_configs
import deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.configs as baseline_vsc_configs
import deep_swe_msft.swe_bench_train_v2_vsc.dataset_config as swb_vsc_configs
import deep_swe_msft.swe_bench_train_v2_vsc.swebench_hard as swb_hard_configs
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe.datasets.config_utils import chz_path

from deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets import (
    configs as rrb_csharp_vsc_configs,
)
from deep_swe_msft.rfs_rcs_bb_ml_vsc.java.datasets import (
    configs as rrb_java_vsc_configs,
)
from deep_swe_msft.rfs_rcs_bb_ml_vsc.javascript.datasets import (
    configs as rrb_javascript_vsc_configs,
)
from deep_swe_msft.rfs_rcs_bb_ml_vsc.typescript.datasets import (
    configs as rrb_typescript_vsc_configs,
)
from deep_swe_msft.env_setup_vsc.datasets import (
    configs as env_setup_vsc_configs,
)

from deep_swe_msft.rrb_if_ml_vsc.python.datasets import (
    configs as rrb_if_python_vsc_configs,
)
from deep_swe_msft.rrb_if_ml_vsc.csharp.datasets import (
    configs as rrb_if_csharp_vsc_configs,
)
from deep_swe_msft.rrb_if_ml_vsc.java.datasets import (
    configs as rrb_if_java_vsc_configs,
)
from deep_swe_msft.rrb_if_ml_vsc.javascript.datasets import (
    configs as rrb_if_javascript_vsc_configs,
)
from deep_swe_msft.rrb_if_ml_vsc.typescript.datasets import (
    configs as rrb_if_typescript_vsc_configs,
)

format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)

VSC_ALL_AVAILABLE_DATASET_CONFIGS = [
    ## swe bench train v2
    # (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.luw.swe-bench-train-vsc.test_train"]),  # 662
    # swe bench train v2.5
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yunsheng.swe.swe_bench_train_variant_prompt.0828.filter.easy"]),  # 1459
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yunsheng.swe.swe_bench_train_variant_prompt.0828.filter.medium"]),  # 250
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yunsheng.swe.swe_bench_train_variant_prompt.0828.filter.hard"]),  # 384
    # swe bench train v2.5 rewrite
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbt2_5.easy"]),  # 1459 x 2
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbt2_5.medium"]),  # 250 x 2
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbt2_5.hard"]),  # 384 x 2

    # sbhv1:
    (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rcr_12878.train_hq"]),  # 1787
    # sbhv1 telemetry rewrite:
    (swb_hard_configs.SWEBenchHardRepairVSCTelemetryTrainDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbhv1"]),  # 1787

    # sbhv2:
    (swb_hard_configs.SWEBenchHardRepairV2VSCTrainDatasetConfig, ["dataset_id=data.jadhuang.swe.upload08182025.sbhv2.vsc.train"]),  # 4517
    # Telemetry rewrite swe bench hard v2:
    (swb_hard_configs.SWEBenchHardRepairV2VSCTelemetryTrainDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbhv2"]),  # 4517 X 2

    # rrb merged datasets
    (baseline_vsc_configs.RRBPythonMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_py_diverse_repo", "max_n_datapoints=2000"]),  # 4363
    (rrb_typescript_vsc_configs.RRBTypeScriptMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_typescript"]),  # 793
    (rrb_java_vsc_configs.RRBJavaMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_java", "max_n_datapoints=800"]),  # 1832
    (rrb_csharp_vsc_configs.RRBCSharpMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_csharp", "max_n_datapoints=800"]),  # 840
    (rrb_javascript_vsc_configs.RRBJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_javascript", "max_n_datapoints=800"]),  # 1000

    # RFS 0828 data
    (baseline_vsc_configs.RFSPython0825VSCDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rfs_0827_2025.python"]),  # 5258
    (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetConfig082825, ["dataset_id=data.haoranxu.swe.swe_vsc.rfs_0827_2025.typescript"]),  # 2274
    (rrb_javascript_vsc_configs.RFSJAVASCRIPT0828DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rfs_0827_2025.javascript"]),  # 1306

    # RCS ++
    (baseline_vsc_configs.RFSPythonRcsPpVSCDatasetConfig, ["dataset_id=data.yunsheng.swe.python.rcspp_prf_0904.easy"]), # 4569
    (baseline_vsc_configs.RFSPythonRcsPpVSCDatasetConfig, ["dataset_id=data.yunsheng.swe.python.rcspp_prf_0904.medium"]), # 2012
    (baseline_vsc_configs.RFSPythonRcsPpVSCDatasetConfig, ["dataset_id=data.yunsheng.swe.python.rcspp_prf_0904.hard"]), # 2546

    (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.easy"]),  # 1071
    (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.medium"]),  # 409
    (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.hard"]),  # 474

    (rrb_java_vsc_configs.RFSJavaRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.java.rcspp_prf_0904.easy"]),  # 527
    (rrb_java_vsc_configs.RFSJavaRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.java.rcspp_prf_0904.medium"]),  # 181
    (rrb_java_vsc_configs.RFSJavaRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.java.rcspp_prf_0904.hard"]),  # 221

    (rrb_csharp_vsc_configs.RFSCSharpRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.csharp.rcspp_prf_0904.easy"]),  # 302
    (rrb_csharp_vsc_configs.RFSCSharpRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.csharp.rcspp_prf_0904.medium"]),  # 103
    (rrb_csharp_vsc_configs.RFSCSharpRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.csharp.rcspp_prf_0904.hard"]),  # 126

    (rrb_javascript_vsc_configs.RFSJAVASCRIPTRCSPPDatasetConfig, ["dataset_id=data.yunsheng.swe.javascript.rcspp_prf_0904.easy"]), # 2547
    (rrb_javascript_vsc_configs.RFSJAVASCRIPTRCSPPDatasetConfig, ["dataset_id=data.yunsheng.swe.javascript.rcspp_prf_0904.medium"]), # 1138
    (rrb_javascript_vsc_configs.RFSJAVASCRIPTRCSPPDatasetConfig, ["dataset_id=data.yunsheng.swe.javascript.rcspp_prf_0904.hard"]), # 1259

    # repo qa + file qa
    (qa_configs.RepoQAMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.repo_qa_v2", "max_n_datapoints=500"]),  # 2000 + 920
    (qa_configs.FileQAPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.file_qa_v2", "max_n_datapoints=500"]),  # 1500

    # repo qa + file qa telemetry rewrite:
    (qa_configs.RepoQAMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.repo_qa_v2", "max_n_datapoints=500"]),  # 2000 * 2 + 920
    (qa_configs.FileQAPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.file_qa_v2", "max_n_datapoints=500"]), # 1500

    # pr-if datasets
    (rrb_if_python_vsc_configs.RRBIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_py_v3", "max_n_datapoints=300"]),  # 4245
    (rrb_if_java_vsc_configs.RRBIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_java_v3", "max_n_datapoints=300"]),  # 1784
    (rrb_if_javascript_vsc_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_js_v3", "max_n_datapoints=300"]),  # 969
    (rrb_if_typescript_vsc_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_ts_v3", "max_n_datapoints=300"]),  # 761
    (rrb_if_csharp_vsc_configs.RRBIFCSharpDatasetConfig,  ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_cs_v3", "max_n_datapoints=300"]),  # 809

    # pr-if telemetry rewrite:
    (rrb_if_python_vsc_configs.RRBIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_py_v3", "max_n_datapoints=300"]),  # 4245
    (rrb_if_java_vsc_configs.RRBIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_java_v3", "max_n_datapoints=300"]),  # 1784
    (rrb_if_javascript_vsc_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_js_v3", "max_n_datapoints=300"]),  # 969
    (rrb_if_typescript_vsc_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_ts_v3", "max_n_datapoints=300"]),  # 761
    (rrb_if_csharp_vsc_configs.RRBIFCSharpDatasetConfig,  ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_cs_v3", "max_n_datapoints=300"]),  # 809

    # swe-if datasets
    (rrb_if_python_vsc_configs.SWEIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.py", "max_n_datapoints=300"]), # 3552
    (rrb_if_java_vsc_configs.SWEIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.java", "max_n_datapoints=300"]), # 565
    (rrb_if_javascript_vsc_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.js", "max_n_datapoints=300"]), # 644
    (rrb_if_typescript_vsc_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.ts", "max_n_datapoints=300"]), # 458
    (rrb_if_csharp_vsc_configs.SWEIFCSharpDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.cs"]), #96

    # swe-if telemetry rewrite:
    (rrb_if_python_vsc_configs.SWEIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.py", "max_n_datapoints=300"]), # 3552
    (rrb_if_java_vsc_configs.SWEIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.java", "max_n_datapoints=300"]), # 565
    (rrb_if_javascript_vsc_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.js", "max_n_datapoints=300"]), # 644
    (rrb_if_typescript_vsc_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.ts", "max_n_datapoints=300"]), # 458
    (rrb_if_csharp_vsc_configs.SWEIFCSharpDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.cs"]), #96

    ## Telemetry QA
    (telemetry_qa_configs.RepoQATelemetryDatasetConfig, ["dataset_id=data.datasets.swe.telemetry_nontestable.3k1_v1.train"]), # 2484
]

VSC_MIX14_DATASET_CONFIGS = [
    # swe bench train
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.swb_train_rewritten_4x"]),  # 2400
    (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.damajercak.swe.upload05202025.rcr_12878.train_hq"]),  # 1700

    # rrb merged datasets
    (baseline_vsc_configs.RRBPythonMergedDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.rrb_py_diverse_repo"]),  # 4363
    (rrb_typescript_vsc_configs.RRBTypeScriptMergedDatasetConfig, []),  # 793
    (rrb_java_vsc_configs.RRBJavaMergedDatasetConfig, []),  # 1832
    (rrb_csharp_vsc_configs.RRBCSharpMergedDatasetConfig, []),  # 840
    (rrb_javascript_vsc_configs.RRBJAVASCRIPTDatasetConfig, []),  # 1000

    # repo qa + file qa
    (qa_configs.RepoQAMergedDatasetConfig, []),  # 2000 + 920
    (qa_configs.FileQAPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.file_qa"]),
    # 500 py + 1000 others

    # env setup
    # (env_setup_vsc_configs.EnvSetupPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.env_setup_task"]),  # 1900: py1000 + java300 + js300 + ts300
    # (env_setup_vsc_configs.EnvSetupSBHDatasetConfig, []),  # 180
    # (env_setup_vsc_configs.EnvSetupSBHCustomImgDatasetConfig, []),  # 180

    # pr-if datasets
    (rrb_if_python_vsc_configs.RRBIFPythonDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_py_v3", "max_n_datapoints=2000"]),  # 1115
    (rrb_if_java_vsc_configs.RRBIFJavaDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_java_v3", "max_n_datapoints=900"]),  # 277
    (rrb_if_javascript_vsc_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_js_v3", "max_n_datapoints=500"]),  # 209
    (rrb_if_typescript_vsc_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_ts_v3", "max_n_datapoints=400"]),  # 194
    (rrb_if_csharp_vsc_configs.RRBIFCSharpDatasetConfig,  ["dataset_id=data.qingruzhang.swe.rrb_if.rrb_cs_v3", "max_n_datapoints=400"]),  # 205
    # swe-if datasets
    (rrb_if_python_vsc_configs.SWEIFPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.py"]),
    (rrb_if_java_vsc_configs.SWEIFJavaDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.java"]),
    (rrb_if_javascript_vsc_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.js"]),
    (rrb_if_typescript_vsc_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.ts"]),
    (rrb_if_csharp_vsc_configs.SWEIFCSharpDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.sweif2.cs"]),
]

train_vsc_mix14 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_MIX14_DATASET_CONFIGS
    ],
    format,
)


VSC_MIX15_DATASET_CONFIGS = [
    # swe bench train
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.swb_train_rewritten_4x.swe_bench_train_updated"]),  # 2400
    (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rcr_12878.train_hq"]),  # 1700

    # rrb merged datasets
    (baseline_vsc_configs.RRBPythonMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_py_diverse_repo"]),  # 4363
    (rrb_typescript_vsc_configs.RRBTypeScriptMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_typescript"]),  # 793
    (rrb_java_vsc_configs.RRBJavaMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_java"]),  # 1832
    (rrb_csharp_vsc_configs.RRBCSharpMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_csharp"]),  # 840
    (rrb_javascript_vsc_configs.RRBJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_javascript"]),  # 1000

    # repo qa + file qa
    (qa_configs.RepoQAMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.repo_qa_v2"]),  # 2000 + 920
    (qa_configs.FileQAPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.file_qa_v2"]),
    # 500 py + 1000 others

    # env setup
    # (env_setup_vsc_configs.EnvSetupPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.env_setup_task"]),  # 1900: py1000 + java300 + js300 + ts300
    # (env_setup_vsc_configs.EnvSetupSBHDatasetConfig, []),  # 180
    # (env_setup_vsc_configs.EnvSetupSBHCustomImgDatasetConfig, []),  # 180

    # pr-if datasets
    (rrb_if_python_vsc_configs.RRBIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_py_v3", "max_n_datapoints=2000"]),  # 1115
    (rrb_if_java_vsc_configs.RRBIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_java_v3", "max_n_datapoints=900"]),  # 277
    (rrb_if_javascript_vsc_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_js_v3", "max_n_datapoints=500"]),  # 209
    (rrb_if_typescript_vsc_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_ts_v3", "max_n_datapoints=400"]),  # 194
    (rrb_if_csharp_vsc_configs.RRBIFCSharpDatasetConfig,  ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_cs_v3", "max_n_datapoints=400"]),  # 205
    # # swe-if datasets
    (rrb_if_python_vsc_configs.SWEIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.py"]),
    (rrb_if_java_vsc_configs.SWEIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.java"]),
    (rrb_if_javascript_vsc_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.js"]),
    (rrb_if_typescript_vsc_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.ts"]),
    (rrb_if_csharp_vsc_configs.SWEIFCSharpDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.cs"]),
]


train_vsc_mix15 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_MIX15_DATASET_CONFIGS
    ],
    format,
)


VSC_MIX16_DATASET_CONFIGS = [
    # swe bench train
    # (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.swb_train_rewritten_4x.swe_bench_train_updated"]),  # 2400
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.luw.swe-bench-train-vsc.test_train"]),  # 662

    # rrb merged datasets
    (baseline_vsc_configs.RRBPythonMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_py_diverse_repo", "max_n_datapoints=2000"]),  # 4363
    (rrb_typescript_vsc_configs.RRBTypeScriptMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_typescript"]),  # 793
    (rrb_java_vsc_configs.RRBJavaMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_java", "max_n_datapoints=800"]),  # 1832
    (rrb_csharp_vsc_configs.RRBCSharpMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_csharp", "max_n_datapoints=800"]),  # 840
    (rrb_javascript_vsc_configs.RRBJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_javascript", "max_n_datapoints=800"]),  # 1000

    # repo qa + file qa
    (qa_configs.RepoQAMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.repo_qa_v2", "max_n_datapoints=500"]),  # 2000 + 920
    # (qa_configs.FileQAPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.file_qa_v2", "max_n_datapoints=500"]), 
    # 500 py + 1000 others

    # env setup
    # (env_setup_vsc_configs.EnvSetupPythonDatasetConfig, ["dataset_id=data.zhendongw.swe_train.data_mix_v1.env_setup_task"]),  # 1900: py1000 + java300 + js300 + ts300
    # (env_setup_vsc_configs.EnvSetupSBHDatasetConfig, []),  # 180
    # (env_setup_vsc_configs.EnvSetupSBHCustomImgDatasetConfig, []),  # 180

    # pr-if datasets
    (rrb_if_python_vsc_configs.RRBIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_py_v3", "max_n_datapoints=300"]),  # 1115
    (rrb_if_java_vsc_configs.RRBIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_java_v3", "max_n_datapoints=300"]),  # 277
    (rrb_if_javascript_vsc_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_js_v3", "max_n_datapoints=300"]),  # 209
    (rrb_if_typescript_vsc_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_ts_v3", "max_n_datapoints=300"]),  # 194
    (rrb_if_csharp_vsc_configs.RRBIFCSharpDatasetConfig,  ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_if_cs_v3", "max_n_datapoints=300"]),  # 205
    # swe-if datasets
    (rrb_if_python_vsc_configs.SWEIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.py", "max_n_datapoints=300"]),
    (rrb_if_java_vsc_configs.SWEIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.java", "max_n_datapoints=300"]),
    (rrb_if_javascript_vsc_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.js", "max_n_datapoints=300"]),
    (rrb_if_typescript_vsc_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.ts", "max_n_datapoints=300"]),
    (rrb_if_csharp_vsc_configs.SWEIFCSharpDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.sweif2.cs"]),
]


train_vsc_mix16 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_MIX16_DATASET_CONFIGS
    ],
    format,
)

VSC_MIX17_DATASET_CONFIGS = [

    # swe bench train v2.5 and telemetry rewrite: 2.2K
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yunsheng.swe.swe_bench_train_variant_prompt.0828.filter.easy", "max_n_datapoints=300"]),  # 1459
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yunsheng.swe.swe_bench_train_variant_prompt.0828.filter.medium"]),  # 250
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.yunsheng.swe.swe_bench_train_variant_prompt.0828.filter.hard"]),  # 384

    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbt2_5.easy", "max_n_datapoints=300"]),  # 1459 x 2
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbt2_5.medium"]),  # 250 x 2
    (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbt2_5.hard"]),  # 384 x 2

    # sbhv1 telemetry rewrite: 1.7K
    (swb_hard_configs.SWEBenchHardRepairVSCTelemetryTrainDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbhv1"]),  # 1787

    # swe bench hard v2 and telemetry rewrite: 11K
    (swb_hard_configs.SWEBenchHardRepairV2VSCTrainDatasetConfig, ["dataset_id=data.jadhuang.swe.upload08182025.sbhv2.vsc.train", "max_n_datapoints=2000"]),  # 4517
    (swb_hard_configs.SWEBenchHardRepairV2VSCTelemetryTrainDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbhv2"]),  # 4517 X 2

    # rrb merged datasets: 5.2K
    (baseline_vsc_configs.RRBPythonMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_py_diverse_repo", "max_n_datapoints=2000"]),  # 4363
    # (rrb_typescript_vsc_configs.RRBTypeScriptMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_typescript"]),  # 793
    (rrb_java_vsc_configs.RRBJavaMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_java", "max_n_datapoints=800"]),  # 1832
    (rrb_csharp_vsc_configs.RRBCSharpMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_csharp", "max_n_datapoints=800"]),  # 840
    (rrb_javascript_vsc_configs.RRBJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_javascript", "max_n_datapoints=800"]),  # 1000

    ## RCS ++: 7.8K
    (baseline_vsc_configs.RFSPythonRcsPpVSCDatasetConfig, ["dataset_id=data.yunsheng.swe.python.rcspp_prf_0904.easy", "max_n_datapoints=1000"]), # 4569 # real runTests
    (baseline_vsc_configs.RFSPythonRcsPpVSCDatasetConfig, ["dataset_id=data.yunsheng.swe.python.rcspp_prf_0904.medium", "max_n_datapoints=1000"]), # 2012
    (baseline_vsc_configs.RFSPythonRcsPpVSCDatasetConfig, ["dataset_id=data.yunsheng.swe.python.rcspp_prf_0904.hard", "max_n_datapoints=1000"]), # 2546

    (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.easy", "max_n_datapoints=500"]),  # 1071
    (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.medium"]),  # 409
    (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.hard"]),  # 474

    (rrb_java_vsc_configs.RFSJavaRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.java.rcspp_prf_0904.easy"]),  # 527
    (rrb_java_vsc_configs.RFSJavaRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.java.rcspp_prf_0904.medium"]),  # 181
    (rrb_java_vsc_configs.RFSJavaRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.java.rcspp_prf_0904.hard"]),  # 221

    (rrb_csharp_vsc_configs.RFSCSharpRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.csharp.rcspp_prf_0904.easy"]),  # 302
    (rrb_csharp_vsc_configs.RFSCSharpRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.csharp.rcspp_prf_0904.medium"]),  # 103
    (rrb_csharp_vsc_configs.RFSCSharpRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.csharp.rcspp_prf_0904.hard"]),  # 126

    (rrb_javascript_vsc_configs.RFSJAVASCRIPTRCSPPDatasetConfig, ["dataset_id=data.yunsheng.swe.javascript.rcspp_prf_0904.easy", "max_n_datapoints=1000"]), # 2547
    (rrb_javascript_vsc_configs.RFSJAVASCRIPTRCSPPDatasetConfig, ["dataset_id=data.yunsheng.swe.javascript.rcspp_prf_0904.medium", "max_n_datapoints=500"]), # 1138
    (rrb_javascript_vsc_configs.RFSJAVASCRIPTRCSPPDatasetConfig, ["dataset_id=data.yunsheng.swe.javascript.rcspp_prf_0904.hard", "max_n_datapoints=500"]), # 1259

    # repo qa + file qa telemetry rewrite: 6K
    (qa_configs.RepoQAMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.repo_qa_v2", "max_n_datapoints=3000"]),  # 2000 * 2 + 920
    (qa_configs.FileQAPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.file_qa_v2"]),  # 1500

    # pr-if telemetry rewrite: 4.3K
    (rrb_if_python_vsc_configs.RRBIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_py_v3", "max_n_datapoints=2000"]),  # 4245
    (rrb_if_java_vsc_configs.RRBIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_java_v3", "max_n_datapoints=800"]),  # 1784
    (rrb_if_javascript_vsc_configs.RRBIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_js_v3", "max_n_datapoints=500"]),  # 969
    # (rrb_if_typescript_vsc_configs.RRBIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_ts_v3", "max_n_datapoints=500"]),  # 761
    (rrb_if_csharp_vsc_configs.RRBIFCSharpDatasetConfig,  ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.pr_if.rrb_if_cs_v3", "max_n_datapoints=500"]),  # 809

    # swe-if telemetry rewrite: 1.5K
    (rrb_if_python_vsc_configs.SWEIFPythonDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.py", "max_n_datapoints=2000"]), # 3552
    (rrb_if_java_vsc_configs.SWEIFJavaDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.java", "max_n_datapoints=300"]), # 565
    (rrb_if_javascript_vsc_configs.SWEIFJAVASCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.js", "max_n_datapoints=300"]), # 644
    (rrb_if_typescript_vsc_configs.SWEIFTYPESCRIPTDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.ts", "max_n_datapoints=300"]), # 458
    (rrb_if_csharp_vsc_configs.SWEIFCSharpDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sweif2.cs"]), #96

    # Telemetry QA 2.5K
    (telemetry_qa_configs.RepoQATelemetryDatasetConfig, ["dataset_id=data.datasets.swe.telemetry_nontestable.3k1_v1.train"]), # 2484
]


train_vsc_mix17 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_MIX17_DATASET_CONFIGS
    ],
    format,
)

VSC_DERISK_DATASET_CONFIGS = [
    # (swb_hard_configs.SWEBenchHardRepairV2VSCTrainDatasetConfig, ["dataset_id=data.jadhuang.swe.upload08182025.sbhv2.vsc.train"]),  # 4517
    # (baseline_vsc_configs.RFSPython0825VSCDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rfs_0827_2025.python"]),  # 5258
    # (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetConfig082825, ["dataset_id=data.haoranxu.swe.swe_vsc.rfs_0827_2025.typescript"]),  # 2274
    # (rrb_javascript_vsc_configs.RFSJAVASCRIPT0828DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rfs_0827_2025.javascript"]),  # 1306
    # (baseline_vsc_configs.RRBPythonMergedDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rrb_py_diverse_repo", "max_n_datapoints=2000"]),  # 4363
    # (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.luw.swe-bench-train-vsc.test_train"]),  # 662
    # (swb_hard_configs.SWEBenchHardRepairVSCTrainDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.rcr_12878.train_hq"]),  # 1700
    # (swb_hard_configs.SWEBenchHardRepairV2VSCTelemetryTrainDatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbhv2"]),  # 4517 X 2
    # (swb_vsc_configs.SWEBenchTrainV2DatasetConfig, ["dataset_id=data.haoranxu.swe.swe_vsc.telemetry.xuga_telemetry_data_0901"]),  # 381
    # (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.easy"]),  # 1071
    # (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.medium"]),  # 409
    # (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.hard"]),  # 474
    # (baseline_vsc_configs.RFSPythonRcsPpVSCDatasetConfig, ["dataset_id=data.yunsheng.swe.python.rcspp_prf_0904.easy"]), # 4569
    # (baseline_vsc_configs.RFSPythonRcsPpVSCDatasetConfig, ["dataset_id=data.yunsheng.swe.python.rcspp_prf_0904.medium"]), # 2012
    # (baseline_vsc_configs.RFSPythonRcsPpVSCDatasetConfig, ["dataset_id=data.yunsheng.swe.python.rcspp_prf_0904.hard"]), # 2546

    # (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.easy"]),  # 1071
    # (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.medium"]),  # 409
    # (rrb_typescript_vsc_configs.RFSTYPESCRIPTDatasetRcsPPConfig, ["dataset_id=data.yunsheng.swe.typescript.rcspp_prf_0904.hard"]),  # 474

    # (rrb_java_vsc_configs.RFSJavaRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.java.rcspp_prf_0904.easy"]),  # 527
    # (rrb_java_vsc_configs.RFSJavaRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.java.rcspp_prf_0904.medium"]),  # 181
    # (rrb_java_vsc_configs.RFSJavaRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.java.rcspp_prf_0904.hard"]),  # 221

    # (rrb_csharp_vsc_configs.RFSCSharpRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.csharp.rcspp_prf_0904.easy"]),  # 302
    # (rrb_csharp_vsc_configs.RFSCSharpRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.csharp.rcspp_prf_0904.medium"]),  # 103
    # (rrb_csharp_vsc_configs.RFSCSharpRcsPPDatasetConfig, ["dataset_id=data.yunsheng.swe.csharp.rcspp_prf_0904.hard"]),  # 126

    # (rrb_javascript_vsc_configs.RFSJAVASCRIPTRCSPPDatasetConfig, ["dataset_id=data.yunsheng.swe.javascript.rcspp_prf_0904.easy"]), # 2547
    # (rrb_javascript_vsc_configs.RFSJAVASCRIPTRCSPPDatasetConfig, ["dataset_id=data.yunsheng.swe.javascript.rcspp_prf_0904.medium"]), # 1138
    # (rrb_javascript_vsc_configs.RFSJAVASCRIPTRCSPPDatasetConfig, ["dataset_id=data.yunsheng.swe.javascript.rcspp_prf_0904.hard"]), # 1259
    (telemetry_qa_configs.RepoQATelemetryDatasetConfig, ["dataset_id=data.datasets.swe.telemetry_nontestable.3k1_v1.train"]), # 2484

]

derisk_vsc = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in VSC_DERISK_DATASET_CONFIGS
    ],
    format,
)
