import os
import shlex
from typing import Any

import caas
import structlog
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas_utils.utils import run_with_retries
from deep_swe_tasks import DeepSWETaskMetadata, SWEBenchV2Task

from deep_swe_msft.swe_bench_train_v2_vsc.setup.get_coreutils import setup_coreutils
from deep_swe_msft.tools.utils import get_strawberry_ace_token

logger = structlog.get_logger(component=__name__)

BLOBSTORE_URL = {
    "sbt": "https://orngcaas.blob.core.windows.net/data/swe-model-training-data/swang-test/swe_bench_train_v2",
    "telemetry": "https://genaitextdatawu2.blob.core.windows.net/code/rfs_data/train_data/mrfs_caas/0604/python/passed_repos/artifacts",
}

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_v2_setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
    data_version: str = "sbt",
) -> None:
    metadata = DeepSWETaskMetadata.model_validate(datapoint["metadata"])
    try:
        terminal_session.session.start_keepalive_task(keepalive_interval=60)
        language = datapoint["metadata"].get("lang", None) or datapoint["metadata"].get("top_language", None) or "python"
        vscode_settings = datapoint["metadata"].get("task", {}).get("vscode_settings", None)
        if data_version == "sbt":
            await swe_bench_v2_setup_fn_internal(terminal_session=terminal_session, language=language, metadata=metadata, vscode_settings=vscode_settings, data_version=data_version)
        elif data_version == "telemetry":
            await swe_bench_v2_telemetry_setup_fn_internal(terminal_session=terminal_session, language=language, metadata=metadata, vscode_settings=vscode_settings, data_version=data_version)
        else:
            raise ValueError(f"Unknown data_version: {data_version}")
    finally:
        await terminal_session.session.stop_keepalive_task()

async def swe_bench_v2_telemetry_setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    """
    Wrapper for mrfs_setup_fn_coreutils with data_version=rfs-0828-25
    """
    await swe_bench_v2_setup_fn(
        datapoint=datapoint,
        terminal_session=terminal_session,
        data_version="telemetry",
    )

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_swe_bench_train_v2/caas_swe_bench_train_v2/setup.py
async def swe_bench_v2_telemetry_setup_fn_internal(
    *,
    terminal_session: TerminalSession,
    language: str | None,
    metadata: DeepSWETaskMetadata,
    vscode_settings: dict[str, Any] | None = None,
    data_version: str = "telemetry",
) -> None:
    task = metadata.task
    assert isinstance(task, SWEBenchV2Task)

    presetup_script = metadata.presetup_script.replace("$COMMIT", task.base_commit)
    if presetup_script:
        await run_with_retries(terminal_session, presetup_script, attempts=3)

    sas_token = ""
    for tarball in metadata.setup_tarballs:
        logger.info("Loading cache", id=task.instance_id)
        await run_with_retries(
            terminal_session,
            f"mkdir -p {metadata.cwd} ; curl -f -sL '{BLOBSTORE_URL[data_version]}/{tarball}?{sas_token}' | tar -xz -C {metadata.cwd}",
            seconds=1200,
            attempts=3,
        )

        await run_with_retries(
            terminal_session,
            f"cd {metadata.cwd}; {metadata.setup_script}",
            seconds=1200,
            attempts=3,
        )

    # TODO(hanson): pydantic pre-commits require a non-master branch
    if task.repo == "pydantic/pydantic" and "git checkout -b fix" not in metadata.setup_script:
        await run_with_retries(terminal_session, f"cd {metadata.cwd}; git checkout -b fix", attempts=3)

    post_setup = [
        f"echo 'cd {metadata.cwd}' >> /root/.bashrc",
        "git config --global user.name User",
        "git config --global user.email user@localhost",
        "pre-commit install || true",
    ]
    # Mark these with SKIP so the agent ignores these by default
    if task.PRECOMMIT_FAIL:
        skip = shlex.quote(",".join(task.PRECOMMIT_FAIL))
        post_setup.append(f"echo export SKIP={skip} >> /root/.bashrc")

    await terminal_session.session.run(BashScript("\n".join(post_setup), login=True, timeout=900))
    await setup_coreutils(terminal_session.session, {}, repo_root=metadata.cwd, language=language, vscode_settings=vscode_settings)


async def swe_bench_v2_setup_fn_internal(
    *,
    terminal_session: TerminalSession,
    language: str | None,
    metadata: DeepSWETaskMetadata,
    vscode_settings: dict[str, Any] | None = None,
    data_version: str = "sbt",
) -> None:
    task = metadata.task
    assert isinstance(task, SWEBenchV2Task)

    presetup_script = metadata.presetup_script.replace("$COMMIT", task.base_commit)
    if presetup_script:
        await run_with_retries(terminal_session, presetup_script, attempts=3)

    sas_token = get_strawberry_ace_token()
    for tarball in metadata.setup_tarballs:
        logger.info("Loading cache", id=task.instance_id)
        await run_with_retries(
            terminal_session,
            f"mkdir -p {metadata.cwd} ; curl -f -sL '{BLOBSTORE_URL[data_version]}/{tarball}?{sas_token}' | tar -xz -C {metadata.cwd}",
            seconds=1200,
            attempts=3,
        )

    # TODO(hanson): pydantic pre-commits require a non-master branch
    if task.repo == "pydantic/pydantic" and "git checkout -b fix" not in metadata.setup_script:
        await run_with_retries(terminal_session, f"cd {metadata.cwd}; git checkout -b fix", attempts=3)

    post_setup = [
        f"echo 'cd {metadata.cwd}' >> /root/.bashrc",
        "git config --global user.name User",
        "git config --global user.email user@localhost",
        "pre-commit install || true",
    ]
    # Mark these with SKIP so the agent ignores these by default
    if task.PRECOMMIT_FAIL:
        skip = shlex.quote(",".join(task.PRECOMMIT_FAIL))
        post_setup.append(f"echo export SKIP={skip} >> /root/.bashrc")

    await terminal_session.session.run(BashScript("\n".join(post_setup), login=True, timeout=900))
    await setup_coreutils(terminal_session.session, {}, repo_root=metadata.cwd, language=language, vscode_settings=vscode_settings)
