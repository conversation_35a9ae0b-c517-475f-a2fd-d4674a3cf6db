from datetime import datetime
from functools import partial

import caas_autograding.grader as caas_graders
import chz
import qstar.instance_completers
import qstar.instance_optimizers
from berry import InstanceCompleter, InstanceOptimizer, SampleAllocator
from berry.datapoint import Datapoint, PromptT
from berry.prompt_postprocessor import PromptPostprocessor
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig
from qstar.allocators.continuous_rewards_adaptive_sample_allocator import (
    ContinuousRewardsAdaptiveSampleAllocator,
)
from qstar.common.datapoint import HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from qstar.graders.grader import Grader
from qstar.presets.chz_utils import IsOverride, override

from deep_swe_msft.swe_bench_train_v2_vsc.setup.caas_resource_config import (
    DeepSWECaasContainerResourceConfig,
)
from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn, swe_bench_v2_telemetry_setup_fn
from deep_swe_msft.tools.caas_vscode_tool import VSCodeToolConfig
from deep_swe_eval_msft.tools.caas_vscode_tool import EvalVSCodeToolConfig
from deep_swe_msft.vsc_data.system_prompt import VSC_MODEL_IDENTITY, VSC_SYSTEM_PROMPT
from deep_swe_msft.tools.utils import CAAS_ENDPOINT

from deep_swe_msft.vsc_graders.cotograder_utils import (
    CotoGraderService,
    COTOGRADER_BUS_USER,
    COTOGRADER_BUS_TOPIC,
    COTOGRADER_BUS_LINE,
    COTOGRADER_RENDERER,
    COTOGRADER_QOS_TYPE,
)
from deep_swe_msft.vsc_graders.grader_picker import GRADER_PICKER

CAAS_IDLE_TTL = 1200


def make_swebench_train_v2_resource_configs(
    caas_resource_config: DeepSWECaasContainerResourceConfig,
) -> tuple[ResourceConfig, ...]:
    caas_resource_config = chz.replace(caas_resource_config, setup_fn=swe_bench_v2_setup_fn)
    return (caas_resource_config,)


def make_swebench_v2_graders(
    use_tool_grader: bool = False,
    use_repeat_read_grader: bool = False,
    use_function_enforce_grader: bool = False,
    **kwargs,
) -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    from caas_swe_bench_train_v2.cotograders import (
        ContributorGuideCotograder,
        PatchComparisonCotograder,
        PatchConsistencyCotograder,
    )

    # from deep_swe_msft.swe_bench_train_v2_vsc.graders.if_enforcement_grader import IFEnforcementGrader
    from deep_swe_msft.vsc_graders.func_enforcement_grader import FuncEnforcementGrader
    from deep_swe_msft.vsc_graders.read_file_grader import RepeatReadingGrader
    from deep_swe_msft.vsc_graders.tool_call_grader import ToolCallGrader

    graders = []
    
    # Stage 1: for repairing task
    if use_repeat_read_grader:
        graders.append(RepeatReadingGrader())
    
    graders.append(
        caas_graders.TermberryGrader(
            grade_fn="deep_swe_msft.swe_bench_train_v2_vsc.graders.unitest_grader:grade_fn_v2"
        )
    )

    if use_function_enforce_grader:
        graders.append(
            FuncEnforcementGrader(
                target_ratio=0.12
            )
        )

    if use_tool_grader:
        graders.append(
            ToolCallGrader(
                topic=COTOGRADER_BUS_TOPIC,
                topic_mode_or_user=COTOGRADER_BUS_USER,
                line=COTOGRADER_BUS_LINE,
                renderer_name=COTOGRADER_RENDERER,
                qos_type=COTOGRADER_QOS_TYPE,
            )
        )
    
    # PatchConsistencyCotograder(
    #     grader_service=CotoGraderService(),
    #     renderer_name=COTOGRADER_RENDERER,
    #     grader_max_tokens=32768,
    # ),
    # # NOTE: this one could still use some iteration cycles, it's not very well calibrated.
    # PatchComparisonCotograder(
    #     grader_service=CotoGraderService(),
    #     renderer_name=COTOGRADER_RENDERER,
    #     grader_max_tokens=32768,
    # ),
    # ContributorGuideCotograder(
    #     grader_service=CotoGraderService(),
    #     renderer_name=COTOGRADER_RENDERER,
    #     grader_max_tokens=32768,
    # ),
    
    return tuple(graders)


def _make_tool_configs(
    container_tool_config: VSCodeToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
    real_tool: str = "fake",
) -> tuple[ToolConfig, ...]:
    # Ensure the VSCode tool runs as a real tool
    container_tool_config = chz.replace(container_tool_config, real_tool=real_tool)
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)

def _make_tool_eval_configs(
    container_tool_config: EvalVSCodeToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
    real_tool: str = "fake",
) -> tuple[ToolConfig, ...]:
    # Ensure the VSCode tool runs as a real tool
    container_tool_config = chz.replace(container_tool_config, real_tool=real_tool)
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)

DEFAULT_ENFORCE_PROBABILITY = 0.25
# IMPORTANT: must be paired with IFEnforcementGrader.
def enforce_commands(
    probability: float = DEFAULT_ENFORCE_PROBABILITY,
    extra_good_commands: tuple[str, ...] = (),
    extra_bad_commands: tuple[str, ...] = (),
) -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.vsc_data.datapoint_converters:enforce_commands",
        kwargs={
            "probability": probability,
            "extra_good_commands": extra_good_commands,
            "extra_bad_commands": extra_bad_commands,
        },
    )


@chz.chz
class SWEBenchTrainV2Grader(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(make_swebench_v2_graders)


@chz.chz
class SWEBenchV2InstanceCompleter(
    qstar.instance_completers.ContinuousRewardsInstanceCompleter, IsOverride
):
    # Because there's so much continuous reward in the grading
    sample_allocator: SampleAllocator = override(ContinuousRewardsAdaptiveSampleAllocator)
    instance_optimizer: InstanceOptimizer = override(
        qstar.instance_optimizers.VariantsInstanceOptimizer
    )


@chz.chz(typecheck=True)
class AddUserMessage(PromptPostprocessor):
    def postprocess_prompt(
        self,
        dp: Datapoint[PromptT],
        prompt: PromptT,
    ) -> PromptT:
        print(f"Adding user message to prompt: {prompt=}\n\n{dp=}\n")
        return prompt


@chz.chz(typecheck=True)
class SWEBenchTrainV2DatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = "data.luw.swe-bench-train-vsc.test_train"
    grader: Grader[HarmonyCompletionDatapoint] = override(
        lambda: SWEBenchTrainV2Grader(graders=make_swebench_v2_graders(**GRADER_PICKER))
    )
    tool_configs: tuple[ToolConfig, ...] = override(partial(_make_tool_configs, real_tool="fake"))
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfig(
                setup_fn=swe_bench_v2_setup_fn,
                caas_endpoint=CAAS_ENDPOINT,
                caas_idle_ttl=CAAS_IDLE_TTL,
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                "deep_swe_msft.swe_bench_train_v2_vsc.data_customization.conversation_init:conversation_init_fn"
            ),
            enforce_commands(probability=0.25),
        )
    )
    instance_completer: InstanceCompleter = override(SWEBenchV2InstanceCompleter)
    conversation_start_date: str = datetime.now().strftime("%Y-%m-%d")
    model_identity_str: str = VSC_MODEL_IDENTITY
    instructions: str = VSC_SYSTEM_PROMPT
    # prompt_postprocessors: tuple[PromptPostprocessor, ...] = (AddUserMessage(), )

@chz.chz(typecheck=True)
class SWEBenchTrainTelemetry0901DatasetConfig(SWEBenchTrainV2DatasetConfig, IsOverride):
    dataset_id: str = "data.haoranxu.swe.swe_vsc.telemetry.xuga_telemetry_data_0901"

@chz.chz(typecheck=True)
class SWEBenchEVALTelemetry0901DatasetConfig(SWEBenchTrainV2DatasetConfig, IsOverride):
    dataset_id: str = "data.haoranxu.swe.swe_vsc.telemetry.xuga_telemetry_data_0901"
    tool_configs: tuple[ToolConfig, ...] = override(partial(_make_tool_eval_configs, real_tool="fake"))
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfig(
                setup_fn=swe_bench_v2_telemetry_setup_fn,
                caas_endpoint=CAAS_ENDPOINT,
                caas_idle_ttl=CAAS_IDLE_TTL,
            ),
        )
    )


