import re

from unidiff import PatchSet

import json

STATUSES = ["PASSED", "FAILED"]

# An unofficial prefix we use for precommit rule printing/parsing.
PRECOMMIT_PREFIX = "precommit::"

def parse_typescript_test_results(log: str, repo_root: str) -> tuple[set[str], set[str]]:
    """
    Supports the following formatting:
    1. `vitest --reporter=json` output
    2. `jest --reporter=json` output
    """
    test_results = json.loads(log)

    passed = set()
    failed = set()

    for test_result in test_results.get('testResults', []):
        file_name = test_result.get('name').replace(repo_root + '/', "")

        for assertion in test_result.get('assertionResults', []):
            title = ' > '.join(assertion.get('ancestorTitles', [])) + ' > ' + assertion.get('title')
            status = assertion.get('status')  # passed / failed / skipped / todo

            if status == "passed":
                passed.add(f"{file_name} | {title.strip(' >')}")
            elif status == "failed":
                failed.add(f"{file_name} | {title.strip(' >')}")

    # assert len(passed) == test_results.get('numPassedTests')
    # assert len(failed) == test_results.get('numFailedTests')

    print(f"Passed: {len(passed)}, {test_results.get('numPassedTests')}")
    print(f"Failed: {len(failed)}, {test_results.get('numFailedTests')}")

    return passed, failed

def parse_python_test_results(log: str) -> tuple[set[str], set[str]]:
    """
    Extract PASSED and FAILED test case names from pytest logs.

    Supports:
    - Pytest verbose mode (-v)
    - xdist mode with [gwX] prefix
    - Summary line format

    Ignores skipped/xfailed etc.
    """
    escapes = "".join([chr(char) for char in range(1, 32)])
    translator = str.maketrans("", "", escapes)

    passed = set()
    failed = set()
    pytest_type = "normal"
    """
    These are xdist logs:
        plugins: xdist-3.7.0, asyncio-0.23.8, cov-5.0.0, anyio-4.9.0
        2 workers [12 items]
        [gw0] [ 83%] PASSED tests/operators/test_constraints.py::test_combine_constraints
        [gw1] [ 91%] FAILED tests/operators/test_constraints.py::test_generate_cell_methods_constraint_no_aggregation
        tests/operators/test_constraints.py::test_generate_level_constraint_single_level
    """
    if "xdist" in log and "workers" in log and "[gw" in log:
        pytest_type = "xdist"
        
    for line in log.strip().splitlines():
        line = re.sub(r"\x1b\[[0-9;]*m", "", line)  # remove ANSI colors
        line = line.translate(translator).strip()

        #Check if use xdist 
        if pytest_type == "xdist":
            # [gw0] [  1%] PASSED mypy/test/testcheck.py::test_foo
            # [gw1] [  2%] FAILED some_module::some_test
            match = re.search(r"(PASSED|FAILED)\s+(\S+::\S+)", line)
            if match:
                status, testname = match.group(1), match.group(2)
                if status == "PASSED":
                    passed.add(testname)
                else:
                    failed.add(testname)
        else:
            # tests/operators/test_constraints.py::test_generate_cell_methods_constraint PASSED                                                                                                                         [ 33%]
            # tests/operators/test_constraints.py::test_generate_cell_methods_constraint_no_aggregation FAILED  
            match = re.search(r"(\S+::\S+)\s+(PASSED|FAILED)", line)
            if match:
                testname, status = match.group(1), match.group(2)
                if status == "PASSED":
                    passed.add(testname)
                else:
                    failed.add(testname)

    passed -= failed  # consider test failed if both passed & failed due to flaky runs

    return passed, failed


def parse_test_results(log: str) -> tuple[set[str], set[str]]:
    """
    This currently only supports Pytest style formatting:

    v2: `(PASSED|FAILED) test_name [- error msg]`
    v1: `test_name (PASSED|FAILED)`
    """
    escapes = "".join([chr(char) for char in range(1, 32)])
    translator = str.maketrans("", "", escapes)

    passed = set()
    failed = set()

    for line in log.strip().splitlines():
        line = re.sub(r"\[(\d+)m", "", line)
        line = line.translate(translator)
        if any(line.startswith(x) for x in STATUSES):
            if line.startswith("FAILED"):
                line = line.replace(" - ", " ")
            test_case = line.split()
            parts = (test_case[0].upper(), test_case[1])
        # Support older pytest versions by checking if the line ends with the test status
        elif any(line.endswith(x) for x in STATUSES):
            test_case = line.split()
            parts = (test_case[1].upper(), test_case[0])
        else:
            continue

        if parts[0] == "PASSED":
            passed.add(parts[1])
        else:
            failed.add(parts[1])

    # These are disjoint 99% of the time except for some edge cases with the splitting logic above.
    # In any case if a test name has both passes/fails it should be considered a failure.
    passed -= failed

    return passed, failed


class ModelPatch:
    """
    Parses a model-generated patch into components for grading.

    * :attr:`test_patch`: the subset of hunks that touch files recognised as
      tests (e.g. under test folders or with test‑looking names);
    * :attr:`non_test_patch`: the complementary subset (all other files);
    * :attr:`test_files`: the set of test file paths touched;
    * :attr:`compact_patch`: a human‑readable version of the diff suitable for
      the grader output (binary or removed files are summarised and large diffs
      are truncated).
    """

    # Heuristics to determine which files count as tests.
    #TEST_PATH_PATTERNS = re.compile(r"(^test_|/test_|_test\.|\.test\.|test\/test)")
    TEST_PATH_PATTERNS = re.compile(r"(^test_|/test_|_test\.|\.test\.|test/test|/tests?/|^tests?/|spec\.[jt]sx?$|/spec/|/__tests__/)")
    # Hard cap for the compact patch body in characters. Bigger patches get
    # summarised instead of dumped in full:
    PATCH_CHARACTER_LIMIT = 100_000

    def __init__(self, raw_patch: str) -> None:
        self.raw_patch = raw_patch
        self.test_patch: str = ""
        self.non_test_patch: str = ""
        self.test_files: set[str] = set()
        self.compact_patch: str = ""

        patch_set = PatchSet.from_string(self.raw_patch)
        for hunk in patch_set:
            str_hunk = str(hunk)
            if self.TEST_PATH_PATTERNS.search(hunk.path):
                self.test_patch += str_hunk
                self.test_files.add(hunk.path)
            else:
                self.non_test_patch += str_hunk

            # 1. show a placeholder for binary/deleted files
            # 2. only show the stat for larger changes
            hunk_header = "" if hunk.patch_info is None else str(hunk.patch_info)
            if hunk.is_binary_file:
                self.compact_patch += hunk_header
                self.compact_patch += "(Binary file changed)\n"
            elif hunk.is_removed_file:
                self.compact_patch += hunk_header
                self.compact_patch += "(File deleted)\n"
            elif len(self.compact_patch) + len(str_hunk) > self.PATCH_CHARACTER_LIMIT:
                self.compact_patch += hunk_header
                self.compact_patch += (
                    f"(Large patch not shown. {hunk.added} additions, {hunk.removed} deletions)\n"
                )
            else:
                self.compact_patch += str_hunk

    def has_test_and_non_test(self) -> bool:
        return bool(self.test_patch) and bool(self.non_test_patch)
    def has_non_test(self) -> bool:
        return bool(self.non_test_patch)
