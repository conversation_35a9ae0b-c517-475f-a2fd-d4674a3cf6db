'''
unit test for caas of swe-bench train v2
'''
import asyncio
import json

from caas.api import caas_api
from caas.commands import Exec
from caas.protocol import VolumeMount
from caas.terminal.api import TerminalSession
from deep_swe_tasks.task_metadata import DeepSWETaskMetadata

from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn_internal
from deep_swe_msft.tools.vscode_copilot_tool import (
    VSC_MNT,
)


def load_jsonl(file_path) -> list[dict]:
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            item = json.loads(line.strip())
            data.append(item)
    return data

async def main(metadata: dict[str, str], endpoint: str | None = "https://eastus2.caas.azure.com", use_terminal_server = True) -> None:
    if use_terminal_server:
        cmd = ["/server.py"]
    else:
        cmd = []
    caas_session = None
    try:
        print("="*100)
        print(metadata.docker_image)
        print("Creating caas session")
        caas = caas_api(endpoint=endpoint)
        caas_session = await caas.new_session(
            image=metadata.docker_image,
            cmd=cmd,
            cpu_limit=str(metadata.limits.cpu),
            memory_limit=f"{metadata.limits.memory}m",
            idle_ttl=1200,
            num_gpus=0,
            network='bridge',
            timeout=1200,
            volume_mounts=VSC_MNT,
            disk_limit="32g",
        )
        terminal_session = TerminalSession(caas_session)
        print("="*100+'\n')

        print("="*100)
        print("Setting up repo")
        out = await swe_bench_v2_setup_fn_internal(terminal_session=terminal_session, metadata=metadata)

        print(out)
        print("="*100+'\n')

        # Tool 1
        tool_name = 'str_replace_editor'

        input_data = {
            "command": "create",
            "path": "/home/<USER>/vsc_test.txt",
            # "path": "/usr/local/bin/vsc_tools/vsc_test.txt",
            "file_text": "hello world"
        }
        input_json = json.dumps(input_data)

        print(f"[DEBUG] command: {tool_name} {input_json}")
        output = await caas_session.run(Exec(
            ["bash", "-lc", f"{tool_name} '{input_json}'"],
            workdir="/",
            timeout=900,
            env=None
        ))
        print(output.decode())

        # Tool 2
        tool_name = 'report_progress'

        input_data = {
            "commitMessage": "test-commit",
            "prDescription": "today is a good day",
        }
        input_json = json.dumps(input_data)

        print(f"[DEBUG] command: {tool_name} {input_json}")
        output = await caas_session.run(Exec(
            ["bash", "-lc", f"{tool_name} '{input_json}'"],
            workdir="/",
            timeout=900,
            env=None
        ))
        print(output.decode())

        # print("="*100)
        # print("Running unit tests")
        # ok, d = await try_run_command(
        #     terminal_session, f"source ~/.bashrc; cd {repo_root}; {exec_cmd}", exec_seconds
        # )
        # print(d)

        # output = passes_tests("py" ,d["output"])
        # # expect to see `grader = True | unittest result = {'passed': 274, 'warnings': 12, 'skipped': 33}`
        # result_counts = gather_log_test_results(d["output"])
        # result_counts_json = {"passed": result_counts.passed, "failed": result_counts.failed, "errors": result_counts.errors, "skipped":result_counts.skipped,"warnings":result_counts.warnings}
        # print(f'grader = {output} | unittest result = {result_counts_json}')
        # print("="*100+'\n')

    except Exception as e:
        print("Error: ", e)
    finally:
        if caas_session:
            await caas_session.close()
            print("caas_session closed")

if __name__ == "__main__":

    data = load_jsonl("swe_bench_train_updated.jsonl")
    metadata = data[0]['metadata']
    # print(f'before: {metadata}')
    metadata = DeepSWETaskMetadata.model_validate(metadata)
    # print(f'after: {metadata}')
    asyncio.run(main(metadata, endpoint="https://eastus2.caas.azure.com", use_terminal_server=True))






# try:
#     if self.setup_fn is not None:
#         await self.setup_fn(
#             datapoint=dataclasses.asdict(dp),
#             terminal_session=terminal_session,
#         )

#     if not metadata.allow_internet:
#         await caas_session.update_network(enable_network=False)
# except Exception:
#     await caas_session.close()
#     raise

# return CaasContainer(
#     caas_endpoint=self.caas_endpoint,
#     image_name=metadata.docker_image,
#     caas_session_state=caas_session.save(),
#     user=self.user,
#     is_owner=True,
# )
