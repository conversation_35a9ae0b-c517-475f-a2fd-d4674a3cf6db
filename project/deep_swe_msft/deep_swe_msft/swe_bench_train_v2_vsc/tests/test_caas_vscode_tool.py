import asyncio
import json
import logging
import os
import re

import blobfile as bf
import chat
import structlog
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer

from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn
from deep_swe_msft.tools.caas_vscode_tool import VSCodeTool
from deep_swe_msft.tools.vscode_copilot_tool import (
    INSTALL_ALL_FOR_TESTING,
    exec,
    new_container,
    setup_vscutils,
)

logger = structlog.stdlib.get_logger(component=__name__)


CAAS_ENDPOINT = "https://eastus2.caas.azure.com"

def get_swe_sample() -> dict:
    logger.info("Loading sample data")
    with bf.BlobFile(
        "az://orngscuscresco/data/zhendongw/swe-bench-train-vsc/test_train/swe_bench_train_updated.jsonl",
        "r",
    ) as f:
        samples = [json.loads(line) for line in f]
    logger.info("Loaded sample data", num_samples=len(samples))
    return samples[0]

def get_multilingual_samples() -> list[dict]:
    logger.info("Loading multilingual sample data")
    with bf.BlobFile(
        "az://oaiorangeluw/tmp/multi_lingual.jsonl",
        "r",
    ) as f:
        samples = [json.loads(line) for line in f]
    logger.info("Loaded multilingual sample data", num_samples=len(samples))
    return samples

async def get_caas_container(sample: dict) -> CaasContainer:
    logger.info("Creating CAAS container")
    container = await new_container(CAAS_ENDPOINT, sample["metadata"]["docker_image"])

    await swe_bench_v2_setup_fn(
        datapoint=sample,
        terminal_session=TerminalSession(container.caas_session, endpoint=CAAS_ENDPOINT),
    )

    logger.info("CAAS container created")
    return container

def tool(caas_container: CaasContainer) -> VSCodeTool:
    return VSCodeTool(
        caas_container,
        terminal_emulator_path="teammate_service.termites.lean_terminal:LeanTerminal",
    )

async def invoke_tool(tool: VSCodeTool, tool_name: str, args: dict) -> list[any]:
    message = chat.Message.assistant(
        recipient=f"{tool.get_tool_name()}.{tool_name}",
        content=json.dumps(args),
    )
    msgs = [response.content async for response in tool.process(message)]
    return msgs

async def test_read_project_structure(caas_container: CaasContainer) -> None:
    project_structure, metadata = await exec(
        caas_container.caas_session, "read_project_structure", {}
    )
    assert project_structure, "Expected non-empty project structure"
    logger.info(project_structure)
    assert "I am working in a workspace that has the following structure:" in project_structure

def exist_text(text: str, messages: list[chat.Text]) -> bool:
    return any(text in "".join(m.parts) for m in messages)

async def test_list_code_usages(tool: VSCodeTool, sample: dict) -> None:
    cwd = sample["metadata"]["cwd"]
    content_func = "def test_list_code_usages_add(x: int, y: int) -> int:\n    return x + y\n"
    path = os.path.join(cwd, "test_list_code_usages.py")
    await invoke_tool(tool, "create_file", {
        "filePath": path,
        "content": content_func,
    })

    messages = await invoke_tool(tool, "list_code_usages", {
        "symbolName": "test_list_code_usages_add",
    })

    assert exist_text(path, messages), f"Expected {path} found in test_list_code_usages.py: {messages=}"

async def test_errors(tool: VSCodeTool, workdir: str, filename: str, expected_error: str, content_with_error: str = "error") -> None:
    new_file = os.path.join(workdir, filename)
    await invoke_tool(tool, "create_file", {
        "filePath": new_file,
        "content": content_with_error,
    })
    errors = await invoke_tool(tool, "get_errors", {
        "filePaths": [new_file],
    })

    print(f"Errors in {new_file}:", errors)
    assert exist_text(expected_error, errors), f"Expected error message not found: {errors=}"


async def test_create_file(tool: VSCodeTool, sample: dict) -> None:
    cwd = sample["metadata"]["cwd"]
    new_file = os.path.join(cwd, "test.txt")
    content = """line 1
line 2
line 3
line 4
"""
    await invoke_tool(tool, "create_file", {
        "filePath": new_file,
        "content": content,
    })

    msgs = await invoke_tool(tool, "read_file", {
        "filePath": new_file,
        "startLineNumberBaseZero": 1,
        "endLineNumberBaseZero": 2,
    })

    assert exist_text("line 2", msgs), f"Expected line 2 not found: {msgs=}"
    assert exist_text("line 3", msgs), f"Expected line 3 not found: {msgs=}"

async def test_test_search(tool: VSCodeTool, sample: dict) -> None:
    cwd = sample["metadata"]["cwd"]
    logger.info("Testing test_search functionality", cwd=cwd)
    content_func = "def add(x: int, y: int) -> int:\n    return x + y\n"
    content_test = 'import unittest\nfrom add import add\nclass TestAdd(unittest.TestCase):\n    def test_add(self):\n        self.assertEqual(add(1, 2), 3)\nif __name__ == "__main__":\n    unittest.main()\n'
    await invoke_tool(tool, "create_file", {
        "filePath": os.path.join(cwd, "add.py"),
        "content": content_func,
    })
    await invoke_tool(tool, "create_file", {
        "filePath": os.path.join(cwd, "add_test.py"),
        "content": content_test,
    })
    test_search_messages = await invoke_tool(tool, "test_search", {
        "filePaths": [os.path.join(cwd, "add_test.py")],
    })
    assert exist_text("The test file /home/<USER>/add_test.py contains tests for the following file:\n```python\n# filepath: /home/<USER>/add.py", test_search_messages), f"Expected test_add not found: {test_search_messages=}"

async def test_search_workspace_symbols(tool: VSCodeTool, sample: dict) -> None:
    cwd = sample["metadata"]["cwd"]
    content_func = "def add(x: int, y: int) -> int:\n    return x + y\n"
    await invoke_tool(tool, "create_file", {
        "filePath": os.path.join(cwd, "test_search_workspace_symbols.py"),
        "content": content_func,
    })

    search_workspace_symbols_messages = await invoke_tool(tool, "search_workspace_symbols", {
        "symbolName": "add",
    })
    assert exist_text("add", search_workspace_symbols_messages), f"Expected add not found: {search_workspace_symbols_messages=}"

async def swe_test() -> None:
    python_sample = get_swe_sample()
    python_sample["metadata"]["top_language"] = INSTALL_ALL_FOR_TESTING
    python_container = await get_caas_container(python_sample)
    python_tool = tool(python_container)
    await test_read_project_structure(python_container)
    await test_errors(python_tool, python_sample["metadata"]["cwd"], "add.py", '"error" is not defined')
    await test_create_file(python_tool, python_sample)
    await test_test_search(python_tool, python_sample)
    await test_search_workspace_symbols(python_tool, python_sample)
    await test_list_code_usages(python_tool, python_sample)
    await test_errors(python_tool, python_sample["metadata"]["cwd"], "add.ts", "Cannot find name 'error'. Did you mean 'Error'?")

async def test_java_version(python_tool, target_version) -> None:
    run_in_terminal_res = await invoke_tool(python_tool, "run_in_terminal", {
        "command": "java --version && which java && echo JAVA_HOME=$JAVA_HOME && echo JDK_HOME=$JDK_HOME",
        "explanation": "Check Java version and location",
        "isBackground": False,
    })
    assert exist_text(target_version, run_in_terminal_res), f"Expected Java version output not found: {run_in_terminal_res=}"

async def language_error_test(language: str, workdir: str, filename: str, expected_error: str, content_with_error: str = "error") -> VSCodeTool:
    container = await new_container(CAAS_ENDPOINT, 'aio')
    await container.exec(["mkdir", "-p", "/testbed"], workdir="/", timeout=60, env=None, user=None)
    await setup_vscutils(datapoint=None, session=container.caas_session, workdir="/testbed", language=language)
    tool_instance = tool(container)
    await test_errors(tool_instance, workdir, filename, expected_error, content_with_error)

async def multilingual_test() -> None:
    tasks = {}
    # for language in ['python', 'java', 'typescript', 'javascript', 'c#', 'c++', 'c', 'go', 'rust']:
    for language in ['python', 'java', 'typescript', 'javascript', 'c#', 'c++', 'c']:
        if language.lower() == "java":
            tasks[language] = asyncio.create_task(language_error_test(language, "/testbed", "add.java", 'cannot find symbol\n  symbol:   method printlnerr(String)', """
public class HelloWorld {
    public static void main(String[] args) {
        System.out.printlnerr("Hello, World!");
    }
}
"""))
        elif language.lower() == "typescript":
            tasks[language] = asyncio.create_task(language_error_test(language, "/testbed", "add.ts", "Cannot find name 'error'. Did you mean 'Error'?"))
        elif language.lower() == "python":
            tasks[language] = asyncio.create_task(language_error_test(language, "/testbed", "add.py", '"error" is not defined'))
        elif language.lower() == "javascript":
            tasks[language] = asyncio.create_task(language_error_test(language, "/testbed", "add.js", 'Invalid character.', '###'))
        elif language.lower() == "c#":
            tasks[language] = asyncio.create_task(language_error_test(language, "/testbed", "add.cs", 'Identifier expected'))
        elif language.lower() == "c++":
            tasks[language] = asyncio.create_task(language_error_test(language, "/testbed", "add.cpp", 'this declaration has no storage class or type specifier'))
        elif language.lower() == "c":
            tasks[language] = asyncio.create_task(language_error_test(language, "/testbed", "add.c", "'#' not expected here", "error#*##"))
        elif language.lower() == "go":
            tasks[language] = asyncio.create_task(language_error_test(language, "/testbed", "add.go", '"error" is not defined'))
        elif language.lower() == "rust":
            tasks[language] = asyncio.create_task(language_error_test(language, "/testbed", "add.rs", '"error" is not defined'))
        else:
            logger.warning("Unsupported language for testing", language=language)
            continue
    results = await asyncio.gather(*tasks.values(), return_exceptions=True)
    for result in results:
        if isinstance(result, Exception):
            logger.error("Multilingual test failed", error=str(result))
        else:
            logger.info("Multilingual test passed", result=result)


async def search_and_install_extensions() -> None:
    python_sample = get_swe_sample()
    python_sample["metadata"]["top_language"] = INSTALL_ALL_FOR_TESTING
    python_container = await get_caas_container(python_sample)
    python_tool = tool(python_container)
    searchResult = await invoke_tool(
        python_tool, "vscode_searchExtensions_internal", {"keywords": ["Remote development"]}
    )
    searchResult = "".join(["".join(t.parts) for t in searchResult])
    pattern = r'"id"\s*:\s*"([^"]+)"'
    ids = re.findall(pattern, searchResult)
    assert ids, f"Expected at least one extension ID found in search result, but got {ids=}. {searchResult=}"
    logger.info("Found extension IDs", ids=ids)
    for id in ids[:3]:
        logger.info("Extension found", id=id)
        install_result = await invoke_tool(
            python_tool, "install_extension", {"name": id, "id": id}
        )
        assert exist_text("extension successfully", install_result) or exist_text("extension is already installed", install_result), f"Failed to install extension {id}, {install_result=}"



async def main() -> None:
    # await swe_test()
    await multilingual_test()
    await search_and_install_extensions()


if __name__ == "__main__":
    asyncio.run(main())
