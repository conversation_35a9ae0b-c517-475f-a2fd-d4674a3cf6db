from typing import Any
import berry.preset_utils
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe.datasets.config_utils import chz_path
from chz_presets.core import CompositePreset, Preset
from deep_swe_eval_msft.data_converter.data_converter import get_vardisc_args
import deep_swe_msft.swe_bench_train_v2_vsc.dataset_config as configs



DATASET_CONFIGS = [
    (configs.SWEBenchTrainV2DatasetConfig, []),
]

TELEMETRY_TESTABLE_EVAL_DATASET_CONFIGS = [
    (configs.SWEBenchEVALTelemetry0901DatasetConfig, []),
]


format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)

train = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in DATASET_CONFIGS
    ],
    format,
)


def ev_telemetry_testable_0901v1_juice_vsc(
    juice: int | tuple[int, ...] = 128,
    override_target_samples_per_instance: int = 1,
    max_num_yields: int = 256,
    ) -> CompositePreset[Preset[Any]]:
    return berry.preset_utils.compose_presets(
        *[
            berry.preset_utils.eval_dataset_preset(
                [
                    "dataset=" + chz_path(dataset_config),
                    f"dataset.{override_target_samples_per_instance=}",
                    f"dataset.{max_num_yields=}",
                    *get_vardisc_args(juice),
                ]
            )
            for dataset_config, args in TELEMETRY_TESTABLE_EVAL_DATASET_CONFIGS
        ],
    )
