import random
from datetime import datetime
from typing import Any, Literal, Sequence

import chat
from caas_utils.conversation_init import InstructionInsertionFormat
from deep_swe_msft.vsc_data.datapoint_converters import PROMPT_IN_FILE_PROB, RUN_TEST_HINT_PROB


def log_with_timestamp(message: str, instance_id: str = None):
    with open(f"/var/log/supervisor/swebenchhard_conversation_init_vsc.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}][{instance_id}]: {message}\n")


REPAIR_INSTRUCTION = """
You will be provided with a full code base and an issue statement explaining a problem to resolve.
The code base `{repo_simple_name}` is located at `{repo_directory}` with README file inside

Hint: You may ignore .git folder in "`{repo_directory}` to avoid useless files.
Hint: Better first identify relevant files and functions through the issue, and then fix the errors.

Please resolve the issue. See the issue below.
<issue>
{issue_description}
</issue>
""".strip()

REPAIR_SUFFIX = """
Please summarize all the changes you made in the end.

Please make sure the updated repo can pass unit tests by using the `runTests` tool rather than directly running `bash run_tests.sh` or using pytest. The outputs of the tests may not render to the terminal, but all the outputs should be available in the `{repo_directory}/junit_xml_reports` folder in JUnitXML format. 

Remember what you read and do NOT read the overlapping lines.

After solving the problem, please write a comprehensive trajectory of work explaining how the solution was reached. The trajectory must: (a) enumerate major steps in order; (b) for each step, state the goal, evidence/results, and the decision/rationale for the next action; (c) map steps to concrete changes (files/lines/commits) if applicable ; (d) highlight the key steps by **bolding** or _italicizing_.
""".strip()

REPAIR_SUFFIX_NO_HINT = """
Please summarize all the changes you made in the end.

The outputs of the tests may not render to the terminal, but all the outputs should be available in the `{repo_directory}/junit_xml_reports` folder in JUnitXML format. 
""".strip()

SBHV1_RUN_TESTS = """Please do NOT modify the `run_tests.sh` file.
You are not allowed to pip install new packages. The environment should contain all the dependencies for running inside the code base."""

REQUIRED_HINT = "The outputs of the tests may not render to the terminal, but all the outputs should be available in the `{repo_directory}/junit_xml_reports` folder in JUnitXML format."

def conversation_init_fn(
    *,
    datapoint: dict[str, Any],
    instruction: str = REPAIR_INSTRUCTION,
    instruction_insertion_format: InstructionInsertionFormat = InstructionInsertionFormat.PREPEND_TO_USER_INSTRUCTION,
    language: str | None = None,
    apply_language_instruction_probability: float = 0.75,
    additional_language_instruction_dropout_probability: float = 0.25,
    add_contributor_criteria: (
        bool | list[str]
    ) = True,  # if True, will fetch from |CONTRIBUTOR_CRITERIA|
    extra_appended_instructions: tuple[str, ...] = (),
    custom_instruction_preamble: (
        str | None
    ) = None,  # if not set, will use default ones that instruct on issue solving
    task_header: Literal["Issue", "Task"] = "Issue",
    telemetry_style: bool = False,
) -> Sequence[chat.Message]:
    instance_id = datapoint.get("metadata", {}).get("instance_id", None)
    log_with_timestamp(
        f"conversation_init_fn called with instruction: {instruction}", instance_id=instance_id
    )

    problem = datapoint.get("metadata", {}).get("problem_statement", "")
    repo_name = datapoint.get("metadata", {}).get("repo", "")
    log_with_timestamp(
        f"conversation_init_fn called with problem: {problem}, repo_name: {repo_name}",
        instance_id=instance_id,
    )

    if telemetry_style:
        user_msg = problem + "\n" + REQUIRED_HINT.format(repo_directory="/testbed")
    else:
        user_msg = instruction.format(
            repo_simple_name=repo_name,
            repo_directory="/testbed",
            issue_description=problem,
        )
        user_msg = (
            user_msg
            + "\n"
            + REPAIR_SUFFIX.format(
                repo_directory="/testbed",
            )
            if random.random() < RUN_TEST_HINT_PROB
            else user_msg + "\n" + REPAIR_SUFFIX_NO_HINT.format(repo_directory="/testbed")
        )
    version = datapoint["metadata"].get("version", "sbh-12878")
    sbhv1 = not version.endswith("-noexec")
    if sbhv1:
        user_msg = (user_msg) + SBHV1_RUN_TESTS

    if random.random() < PROMPT_IN_FILE_PROB:
        user_msg = f"""<attachments>
<attachment id="prompt:{datapoint["unique_id"]}.prompt.md">
Prompt instruction file:
```prompt
{user_msg}
```
</attachment>

<userRequest>
Follow instructions in [{datapoint["unique_id"]}.prompt.md](/testbed/{datapoint["unique_id"]}.prompt.md)
</userRequest>"""
    else:
        user_msg = f"""<userRequest>
{user_msg}
</userRequest>
"""

    workspace_msg = f"""<workspace_info>
I am working in a workspace with the following folders:
- /testbed
I am working in a workspace that has the following structure:
```
{datapoint["metadata"]["project_structure"]}
```
This is the state of the context at this point in the conversation. The view of the workspace structure may be truncated. You can use tools to collect more context if needed.
</workspace_info>

<reminderInstructions>
You are an agent—keep going until the user's query is completely resolved before ending your turn. ONLY stop if solved or genuinely blocked.
Take action when possible; the user expects you to do useful work without unnecessary questions.
After any parallel, read-only context gathering, give a concise progress update and what's next.
Avoid repetition across turns: don't restate unchanged plans or sections (like the todo list) verbatim; provide delta updates or only the parts that changed.
Tool batches: You MUST preface each batch with a one-sentence why/what/outcome preamble.
Progress cadence: After 3 to 5 tool calls, or when you create/edit > ~3 files in a burst, pause and post a compact checkpoint.
Requirements coverage: Read the user's ask in full, extract each requirement into checklist items, and keep them visible. Do not omit a requirement. If something cannot be done with available tools, note why briefly and propose a viable alternative.
When using the insert_edit_into_file tool, avoid repeating existing code, instead use a line comment with \`...existing code...\` to represent regions of unchanged code.
Skip filler acknowledgements like "Sounds good" or "Okay, I will…". Open with a purposeful one-liner about what you're doing next.
When sharing setup or run steps, present terminal commands in fenced code blocks with the correct language tag. Keep commands copyable and on separate lines.
Avoid definitive claims about the build or runtime setup unless verified from the provided context (or quick tool checks). If uncertain, state what's known from attachments and proceed with minimal steps you can adapt later.
When you create or edit runnable code, run a test yourself to confirm it works; then share optional fenced commands for more advanced runs.
For non-trivial code generation, produce a complete, runnable solution: necessary source files, a tiny runner or test/benchmark harness, a minimal `README.md`, and updated dependency manifests (e.g., `package.json`, `requirements.txt`, `pyproject.toml`). Offer quick "try it" commands and optional platform-specific speed-ups when relevant.
Your goal is to act like a pair programmer: be friendly and helpful. If you can do more, do more. Be proactive with your solutions, think about what the user needs and what they want, and implement it proactively.
<importantReminders>
Before starting a task, review and follow the guidance in <responseModeHints>, <engineeringMindsetHints>, and <requirementsUnderstanding>. ALWAYS start your response with a brief task receipt and a concise high-level plan for how you will proceed.
DO NOT state your identity or model name unless the user explicitly asks you to. 
You MUST use the todo list tool to plan and track your progress. NEVER skip this step, and START with this step whenever the task is multi-step. This is essential for maintaining visibility and proper execution of large tasks. Follow the todoListToolInstructions strictly.
When referring to a filename or symbol in the user's workspace, wrap it in backticks.

</importantReminders>

</reminderInstructions>
"""
    log_with_timestamp(
        f"conversation_init_fn generated user_msg: {user_msg}", instance_id=instance_id
    )
    return [chat.Message.user(workspace_msg), chat.Message.user(user_msg)]
