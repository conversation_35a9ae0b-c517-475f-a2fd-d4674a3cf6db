from functools import partial
from typing import Any, Literal, cast

import chz
from berry.function_wrapper import FunctionWrapper
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.datasets.configs import (
    DeepSWEDatasetConfig,
    DeepSWEVardiscProducer,
    conversation_converter,
    make_multistage_grader,
)

from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from bus.qos_type import QoSType

from deep_swe_msft.vsc_graders.cotograder_utils import (
    COTOGRADER_BUS_TOPIC,
    COTOGRADER_BUS_USER,
    COTOGRADER_REDIS_ID,
    COTOGRADER_RENDERER,
    COTOGRADER_BUS_LINE,
    COTOGRADER_QOS_TYPE,
)
from deep_swe_msft.swe_bench_train_v2_vsc.dataset_config import CotoGraderService
from deep_swe_msft.swe_bench_train_v2_vsc.dataset_config import (
    enforce_commands as enforce_vsc_commands,
)
from deep_swe_msft.tools.caas_vscode_tool import VSCodeToolConfig
from deep_swe_msft.tools.vscode_copilot_tool import setup_vscutils
from deep_swe_msft.vsc_data.system_prompt import VSC_MODEL_IDENTITY, VSC_SYSTEM_PROMPT
from prbot_msft.configs.caas_container_tool import (
    SweBenchHardCaasContainerResourceConfig,
    SWEBenchHardContainerToolConfig,
    SweBenchHardSetupFn,
    SweBenchHardVSCCaasContainerResourceConfig,
)
from prbot_msft.graders.swebenchhard_repair_comparison_grader import (
    SWEBenchHardRepairComparisonGrader,
)
from prbot_msft.graders.swebenchhard_repair_criteria_grader import SWEBenchHardRepairCriteriaGrader
from prbot_msft.graders.swebenchhard_repro_criteria_grader import SWEBenchHardReproCriteriaGrader
from prbot_msft.tools.get_vscode_tools import setup_fn_vscode
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from qstar.curriculums.variant_producer import VariantProducer
from berry.grader import FunctionalGrader, Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.graders.taskgen_utils import TokenCompleterGraderService
from qstar.presets.chz_utils import IsOverride, override
from deep_swe_msft.vsc_graders.grader_picker import GRADER_PICKER


def log_with_timestamp(message: str):
    from datetime import datetime

    with open(f"/var/log/supervisor/swebenchhard_dataset_config.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}]: {message}\n")


def get_sbh_criteria_grader_chz_argv(
    topic_mode_or_user: str = COTOGRADER_BUS_USER,
    topic_or_snapshot: str = COTOGRADER_BUS_TOPIC,
    bus_renderer: str = COTOGRADER_RENDERER,
    bus_redis_id: str = COTOGRADER_REDIS_ID,
    qos_type: QoSType = COTOGRADER_QOS_TYPE,
):
    log_with_timestamp(f"SBHCRITERIAGRADER_CHZ_ARGV topic_or_snapshot: {topic_or_snapshot}")
    log_with_timestamp(f"SBHCRITERIAGRADER_CHZ_ARGV topic_mode_or_user: {topic_mode_or_user}")
    log_with_timestamp(f"SBHCRITERIAGRADER_CHZ_ARGV bus_renderer: {bus_renderer}")

    SBHCRITERIAGRADER_CHZ_ARGV = [
        f"grader_service.token_completer.bus_line=bus",
        f"grader_service.token_completer.qos_type=bus_token_completer:QoSType.{qos_type}",
        f"grader_service.token_completer.topic_mode_or_user={topic_mode_or_user}",
        f"grader_service.token_completer.topic_or_snapshot={topic_or_snapshot}",
        "grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
        f"grader_service.redis_config_id=msft-swebenchhard-1",
        "grader_service=TokenCompleterGraderService",
        f"renderer_name={bus_renderer}",
    ]
    log_with_timestamp(f"SBHCRITERIAGRADER_CHZ_ARGV: {SBHCRITERIAGRADER_CHZ_ARGV}")
    return SBHCRITERIAGRADER_CHZ_ARGV


@chz.chz(typecheck=True)
class SWEBenchReproCriteriaCotGrader(SWEBenchHardReproCriteriaGrader, IsOverride):
    grader_service: TokenCompleterGraderService = override(CotoGraderService)
    grader_max_tokens: int = 16384
    renderer_name: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"


@chz.chz(typecheck=True)
class SWEBenchRepairCriteriaCotGrader(SWEBenchHardRepairCriteriaGrader, IsOverride):
    grader_service: TokenCompleterGraderService = override(CotoGraderService)
    grader_max_tokens: int = 16384
    renderer_name: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"


def make_swebenchhard_multistage_grader(
    # arguments below can be overridden via chz wildcards
    scenario: Literal["repair", "repro"] = "repair",
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_lupo_and_likertpp_graders: bool = False,
    use_final_patch_for_secondary: bool = False,
    # New: make read-file and tool-call stages optional (defaults keep previous behavior)
    use_repeat_read_grader: bool = False,
    use_tool_grader: bool = False,
    use_function_enforce_grader: bool = False,
    sbh_version: Literal["v1", "v2"] = "v1",
    **kwargs: Any,
) -> MultiStageGrader:
    if scenario == "repro":
        grader_argvs: list[list[str]] = []
        if use_repeat_read_grader:
            grader_argvs.append(
                [
                    "=deep_swe_msft.vsc_graders.read_file_grader:RepeatReadingGrader",
                ]
            )
        grader_argvs.append(["=prbot_msft.graders.swebenchhard_repro_grader:SWEBenchHardReproGrader"])
        grader_argvs.append(
            [
                "=deep_swe_msft.swe_bench_train_v2_vsc.swebench_hard:SWEBenchReproCriteriaCotGrader",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=COTOGRADER_BUS_USER,
                    topic_or_snapshot=COTOGRADER_BUS_TOPIC,
                    bus_renderer=COTOGRADER_RENDERER,
                    bus_redis_id=COTOGRADER_REDIS_ID,
                    qos_type=COTOGRADER_QOS_TYPE,
                ),
            ]
        )
        if use_tool_grader:
            grader_argvs.append(
                [
                    "=deep_swe_msft.vsc_graders.tool_call_grader:ToolCallGrader",
                    f"topic={COTOGRADER_BUS_TOPIC}",
                    f"topic_mode_or_user={COTOGRADER_BUS_USER}",
                    f"line={COTOGRADER_BUS_LINE}",
                    f"renderer_name={COTOGRADER_RENDERER}",
                    f"qos_type=QoSType.{COTOGRADER_QOS_TYPE}",
                ]
            )
        if use_function_enforce_grader:
            grader_argvs.append(
                [
                    "=deep_swe_msft.vsc_graders.func_enforcement_grader:FuncEnforcementGrader",
                    "target_ratio=0.12",
                ]
            )
    else:
        grader_argvs = []
        if use_repeat_read_grader:
            grader_argvs.append(
                [
                    "=deep_swe_msft.vsc_graders.read_file_grader:RepeatReadingGrader",
                ]
            )
        if sbh_version == "v1":
            grader_argvs.append(["=prbot_msft.graders.swebenchhard_repair_grader:SWEBenchHardRepairGrader"])
            criteria_stage = [
                "=deep_swe_msft.swe_bench_train_v2_vsc.swebench_hard:SWEBenchRepairCriteriaCotGrader",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=COTOGRADER_BUS_USER,
                    topic_or_snapshot=COTOGRADER_BUS_TOPIC,
                    bus_renderer=COTOGRADER_RENDERER,
                    bus_redis_id=COTOGRADER_REDIS_ID,
                    qos_type=COTOGRADER_QOS_TYPE,
                ),
            ]
            grader_argvs.append(criteria_stage)
        else:
            grader_argvs.append(
                [
                    "=prbot_msft.configs.swebench_hard:SWEBenchRepairComparisonCotGrader",
                    *get_sbh_criteria_grader_chz_argv(
                        topic_mode_or_user=COTOGRADER_BUS_USER,
                        topic_or_snapshot=COTOGRADER_BUS_TOPIC,
                        bus_renderer=COTOGRADER_RENDERER,
                        bus_redis_id=COTOGRADER_REDIS_ID,
                        qos_type=COTOGRADER_QOS_TYPE,
                    ),
                ]
            )
        if use_tool_grader:
            grader_argvs.append(
                [
                    "=deep_swe_msft.vsc_graders.tool_call_grader:ToolCallGrader",
                    f"topic={COTOGRADER_BUS_TOPIC}",
                    f"topic_mode_or_user={COTOGRADER_BUS_USER}",
                    f"line={COTOGRADER_BUS_LINE}",
                    f"renderer_name={COTOGRADER_RENDERER}",
                    f"qos_type=QoSType.{COTOGRADER_QOS_TYPE}",
                ]
            )
        if use_final_patch_for_secondary:
            grader_argvs[1].append("use_final_patch=True")

    if use_function_enforce_grader:
        grader_argvs.append(
            [
                "=deep_swe_msft.vsc_graders.func_enforcement_grader:FuncEnforcementGrader",
                "target_ratio=0.12",
            ]
        )

    return make_multistage_grader(grader_argvs, channels_for_answer, use_lupo_and_likertpp_graders)


def replace_problem_with_user_prompt(
    dp: dict[str, Any],
) -> list[dict[str, Any]]:
    user_prompt = dp.get("metadata", {}).get("user_prompt", None)
    if user_prompt:
        log_with_timestamp(
            f"Replacing problem with user prompt: \nPROBLEM: {dp['problem']}\nUSER_PROMPT: {user_prompt}\n"
        )
        dp["problem"] = dp["metadata"]["user_prompt"][-1]["content"]["parts"][0]
    else:
        log_with_timestamp(
            f"Not replacing problem with user prompt: \nPROBLEM: {dp['problem']}\nUSER_PROMPT: {user_prompt}\n"
        )

    return [dp]


def replace_problem_with_user_prompt_wrapper() -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.swe_bench_train_v2_vsc.swebench_hard:replace_problem_with_user_prompt",
    )


@chz.chz
class SWEBenchHardRepairTrainDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = (SWEBenchHardContainerToolConfig(),)
    dataset_container: str = chz.field(default="orngcresco")
    dataset_id: str = "data.damajercak.swe.upload05202025.rcr_12878.train_hq"
    grader: Grader | FunctionalGrader = override(
        partial(make_swebenchhard_multistage_grader, scenario="repair")
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            SweBenchHardCaasContainerResourceConfig(
                caas_endpoint=CAAS_ENDPOINT,
                setup_fn=cast(SweBenchHardSetupFn, setup_vscutils)
            ),
        )
    )
    max_num_yields: int = 256
    variant_producer: VariantProducer | None = override(DeepSWEVardiscProducer)


def _make_vsc_tool_configs(
    container_tool_config: VSCodeToolConfig,
    real_tool: str = "fake"
) -> tuple[ToolConfig, ...]:
    container_tool_config = chz.replace(container_tool_config, real_tool=real_tool)
    return (container_tool_config,)


@chz.chz
class SWEBenchHardRepairVSCTrainDatasetConfig(SWEBenchHardRepairTrainDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(partial(_make_vsc_tool_configs, real_tool="skip"))
    grader: Grader | FunctionalGrader = override(
        lambda: make_swebenchhard_multistage_grader(
            scenario="repair",
            sbh_version="v1",
            **GRADER_PICKER
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            SweBenchHardVSCCaasContainerResourceConfig(
                setup_fn=cast(SweBenchHardSetupFn, setup_vscutils)
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                "deep_swe_msft.swe_bench_train_v2_vsc.data_customization.sbh_conversation_init:conversation_init_fn"
            ),
            replace_problem_with_user_prompt_wrapper(),
            enforce_vsc_commands(),
        )
    )
    model_identity_str: str = VSC_MODEL_IDENTITY
    instructions: str = VSC_SYSTEM_PROMPT

@chz.chz
class SWEBenchHardRepairVSCTelemetryTrainDatasetConfig(SWEBenchHardRepairVSCTrainDatasetConfig, IsOverride):
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                "deep_swe_msft.swe_bench_train_v2_vsc.data_customization.sbh_conversation_init:conversation_init_fn",
                telemetry_style=True,
            ),
            replace_problem_with_user_prompt_wrapper(),
            enforce_vsc_commands(),
        )
    )

@chz.chz
class SWEBenchHardRepairV2VSCTrainDatasetConfig(SWEBenchHardRepairVSCTrainDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(partial(_make_vsc_tool_configs, real_tool="skip"))
    grader: Grader | FunctionalGrader = override(
        lambda: make_swebenchhard_multistage_grader(
            scenario="repair",
            sbh_version="v2",
            **GRADER_PICKER
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            SweBenchHardVSCCaasContainerResourceConfig(
                setup_fn=cast(SweBenchHardSetupFn, setup_fn_vscode),
                caas_container_image="aio",
            ),
        )
    )

@chz.chz
class SWEBenchHardRepairV2VSCTelemetryTrainDatasetConfig(SWEBenchHardRepairV2VSCTrainDatasetConfig, IsOverride):
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                "deep_swe_msft.swe_bench_train_v2_vsc.data_customization.sbh_conversation_init:conversation_init_fn",
                telemetry_style=True,
            ),
            replace_problem_with_user_prompt_wrapper(),
            enforce_vsc_commands(),
        )
    )


@chz.chz
class SWEBenchHardRepairVSCEvalDatasetConfig(SWEBenchHardRepairVSCTrainDatasetConfig, IsOverride):
    grader: Grader | FunctionalGrader = override(
        lambda: make_swebenchhard_multistage_grader(
            scenario="repair",
            **GRADER_PICKER
        )
    )