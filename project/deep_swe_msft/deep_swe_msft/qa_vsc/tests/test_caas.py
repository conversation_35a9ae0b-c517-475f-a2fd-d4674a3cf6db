'''
unit test for caas of swe-bench train v2
'''
import asyncio
import json

from caas.api import caas_api
from caas.commands import Exec
from caas.protocol import VolumeMount
from caas.terminal.api import TerminalSession
from deep_swe_tasks.task_metadata import DeepSWETaskMetadata

from deep_swe_msft.qa_vsc.datasets.setup import repo_qa_setup_fn_coreutils
from deep_swe_msft.tools.vscode_copilot_tool import (
    VSC_MNT,
)


def load_jsonl(file_path) -> list[dict]:
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            item = json.loads(line.strip())
            data.append(item)
    return data

async def main(datapoint: dict[str, str], endpoint: str | None = "https://eastus2.caas.azure.com", use_terminal_server = True) -> None:
    if use_terminal_server:
        cmd = ["/server.py"]
    else:
        cmd = []
    caas_session = None
    try:
        print("="*100)
        print("Creating caas session")
        caas = caas_api(endpoint=endpoint)
        caas_session = await caas.new_session(
            image='aio',
            cmd=cmd,
            cpu_limit="2",
            memory_limit=f"4096m",
            idle_ttl=1200,
            num_gpus=0,
            network='bridge',
            timeout=1200,
            volume_mounts=VSC_MNT,
            disk_limit="32g",
        )
        terminal_session = TerminalSession(caas_session)
        print("="*100+'\n')

        print("="*100)
        print("Setting up repo")
        out = await repo_qa_setup_fn_coreutils(terminal_session=terminal_session, datapoint=datapoint)

        print(out)
        print("="*100+'\n')

    except Exception as e:
        print("Error: ", e)
    finally:
        if caas_session:
            await caas_session.close()
            print("caas_session closed")

if __name__ == "__main__":
    
    import subprocess
    download_cmd = f'bbb cp az://orngscuscresco/data/chenliang1/swe/repoqa/u05222025/train_cpp_mu035_sig02_shuffled/train.jsonl ./'
    result = subprocess.run(download_cmd, shell=True)
    data = load_jsonl("train.jsonl")

    asyncio.run(main(data[0], endpoint="https://eastus2.caas.azure.com", use_terminal_server=True))



