import random
import string
from typing import Any

import blobfile as bf
import structlog
from blackberry_utils.error_types import ProblemSetupError
from caas import ExecError
from caas.commands import BashScript, Exec, UploadFile
from caas.terminal.api import TerminalSession
from caas_utils import hacky_pip_mitm_ca_env, setup_path
from deep_swe_msft.tools.vscode_copilot_tool import setup_vscutils
from deep_swe_msft.tools.utils import get_strawberry_ace_token

COPY_FILE = 'curl -sL "https://{STRAWBERRYACE_PATH}.blob.core.windows.net/data/swe-model-training-data/chenliang1-test/qa_repo_all_pls/{golden_tar}?{STRAWBERRYACE_TOKEN}" | tar -xz -C {repo_root}'
FILEQA_COPY_FILE = (
    'curl -sL "https://{STRAWBERRYACE_PATH}.blob.core.windows.net/data/swe-model-training-data/chenliang1-test/fileqa_repo_all_pls/{golden_tar}?{STRAWBERRYACE_TOKEN}" '
    "-o {golden_tar} && unzip {golden_tar} -d {repo_root}"
)

COPY_CMD_DICT = {
    "repoqa": COPY_FILE,
    "codeloc": COPY_FILE,
    "fileqa": FILEQA_COPY_FILE,
}

logger = structlog.get_logger(component=__name__)


def generate_random_string() -> str:
    length = random.randint(5, 8)  # Choose a random length between 5 and 8
    letters = string.ascii_letters  # Includes both uppercase and lowercase letters
    return "".join(random.choices(letters, k=length))


async def qa_setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:

    try:
        extra_setup = datapoint["metadata"].get("extra_setup", "") or datapoint["metadata"].get(
            "setup_cmd", ""
        )
        repo_name = datapoint["metadata"]["repo_name"].split("/")[-1]
        # Randomize the CWD to make the agent more robust
        random.seed(hash(datapoint["unique_id"]))
        repo_root = datapoint["metadata"]["cwd"]
        golden_tar = datapoint["metadata"].get("golden_tar", f"{repo_name}.tar.gz")
        copy_file_cmd = COPY_CMD_DICT[datapoint["metadata"].get("type", "repoqa")].format(
            golden_tar=golden_tar,
            STRAWBERRYACE_PATH="orngcaas",
            STRAWBERRYACE_TOKEN=get_strawberry_ace_token(),
            repo_root=repo_root,
        )

        paths = [repo_root, f"{repo_root}/src"]
        setup_bash_cmd = setup_path(
            repo_root,
            paths,
            randomly_cd_parent=0.2,
        )

        # Adjust extra_setup
        extra_setup = extra_setup.replace("!", "").format(
            STRAWBERRYACE_PATH="orngcaas",
            STRAWBERRYACE_TOKEN=get_strawberry_ace_token(),
        )

        cmd_list = [
            f"rm -rf {repo_root}",
            f"mkdir -p {repo_root}",
            f"cd {repo_root}",
            copy_file_cmd,
            extra_setup,
            setup_bash_cmd,
        ]
        extra_setup = datapoint["metadata"].get("extra_setup", "") or datapoint["metadata"].get(
            "setup_cmd", ""
        )

        setup_cmd = "\n".join(cmd_list)
        with open("/var/log/supervisor/chenliang1_repo_setup_output_1.log", "a") as f:
            f.write(f"{setup_cmd}\n")
    except Exception as e:
        with open("/var/log/supervisor/chenliang1_repo_setup_output_error_1.log", "a") as f:
            f.write(f"Error preparing setup commands: {e},\n{setup_cmd}\n")

    try:
        output = await terminal_session.session.run(
            Exec(cmd=["bash", "-c", setup_cmd], timeout=300, env=hacky_pip_mitm_ca_env())
        )
        with open("/var/log/supervisor/chenliang1_repo_setup_output_2.log", "a") as f:
            f.write(f"{output}\n")
        logger.info("Setup script output", output=output)
    except ExecError as err:
        with open("/var/log/supervisor/chenliang1_repo_setup_output_error_2.log", "a") as f:
            f.write(f"Error preparing setup commands: {err},\n{setup_cmd}\n")
        raise ProblemSetupError(
            f"Error in setup script (exit code {err.status}): {err.output.decode(errors='ignore')}"
        ) from err

    return repo_root


async def qa_setup_fn_coreutils(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    repo_root = await qa_setup_fn(datapoint=datapoint, terminal_session=terminal_session)
    datapoint["metadata"]["cwd"] = repo_root
    try:
        # TODO: Please set the correct language based on the metadata
        language = None
        if "metadata" in datapoint:
            metadata = datapoint["metadata"]
            if "top_language" in metadata:
                language = metadata["top_language"]
        await setup_vscutils(
            datapoint=None,
            session=terminal_session.session,
            workdir=repo_root,
            language=language,
        )
    except Exception as e:
        with open("/var/log/supervisor/luw_caas_vscode_test.log", "a") as f:
            f.write(f"Failed to run ls command: {e}\n")
        raise RuntimeError(f"Failed to install coreutils: {e}") from e
