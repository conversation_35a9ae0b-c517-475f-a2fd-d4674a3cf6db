import berry.preset_utils
import deep_swe_msft.qa_vsc.datasets.configs as qa_configs
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe.datasets.config_utils import chz_path

QA_DATASET_CONFIGS = [
    (qa_configs.RepoQAPythonDatasetConfig, ["max_n_datapoints=3500"]),
    # (configs.RepoLocalizationDatasetConfig, ["max_n_datapoints=3500"]),
]

format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)

train = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in QA_DATASET_CONFIGS
    ],
    format,
)


REPO_QA_ML_1k_CONFIGS = [
    (qa_configs.RepoQAPythonDatasetConfig, ["max_n_datapoints=1000"]), # 6281
    (qa_configs.RepoQACppDatasetConfig, ["max_n_datapoints=1000"]), # 1805
    (qa_configs.RepoQAJavaDatasetConfig, ["max_n_datapoints=1000"]), # 3507
    (qa_configs.RepoQAJsDatasetConfig, ["max_n_datapoints=1000"]), # 3449
    (qa_configs.RepoQARustDatasetConfig, ["max_n_datapoints=1000"]), # 3645
    (qa_configs.RepoQATsDatasetConfig, ["max_n_datapoints=1000"]), # 2614
]

REPO_QA_ML_500_CONFIGS = [
    (qa_configs.RepoQAPythonDatasetConfig, ["max_n_datapoints=500"]), # 6281
    (qa_configs.RepoQACppDatasetConfig, ["max_n_datapoints=500"]), # 1805
    (qa_configs.RepoQAJavaDatasetConfig, ["max_n_datapoints=500"]), # 3507
    (qa_configs.RepoQAJsDatasetConfig, ["max_n_datapoints=500"]), # 3449
    (qa_configs.RepoQARustDatasetConfig, ["max_n_datapoints=500"]), # 3645
    (qa_configs.RepoQATsDatasetConfig, ["max_n_datapoints=500"]), # 2614
]

REPO_QA_ML_250_CONFIGS = [
    (qa_configs.RepoQAPythonDatasetConfig, ["max_n_datapoints=250"]), # 6281
    (qa_configs.RepoQACppDatasetConfig, ["max_n_datapoints=250"]), # 1805
    (qa_configs.RepoQAJavaDatasetConfig, ["max_n_datapoints=250"]), # 3507
    (qa_configs.RepoQAJsDatasetConfig, ["max_n_datapoints=250"]), # 3449
    (qa_configs.RepoQARustDatasetConfig, ["max_n_datapoints=250"]), # 3645
    (qa_configs.RepoQATsDatasetConfig, ["max_n_datapoints=250"]), # 2614
]

CODE_LOCALIZATION_CONFIGS = [
    (qa_configs.RepoLocalizationPythonDatasetConfig, ["max_n_datapoints=400"]), # 643
    (qa_configs.RepoLocalizationCppDatasetConfig, ["max_n_datapoints=70"]), # 72
    (qa_configs.RepoLocalizationJavaDatasetConfig, ["max_n_datapoints=100"]), # 204
    (qa_configs.RepoLocalizationJsDatasetConfig, ["max_n_datapoints=100"]), # 240
    (qa_configs.RepoLocalizationRustDatasetConfig, ["max_n_datapoints=100"]), # 264
    (qa_configs.RepoLocalizationTsDatasetConfig, ["max_n_datapoints=150"]), # 165
]
