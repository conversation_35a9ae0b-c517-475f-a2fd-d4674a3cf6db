import os
import chat
import asyncio
import json
import shlex
import time
import structlog
from caas.api import CaasSession
from caas import caas_api
from caas.protocol import VolumeMount
from caas.commands import RawExec, UploadFile, BashScript, Exec, HttpGet, DownloadFileFromContainer
from caas_tool.caas_container import CaasContainer
from caas.terminal.api import TerminalSession
from deep_swe_msft.tools.get_coreutils import setup_coreutils
from deep_swe_msft.swe_bench_train_v2_padawan_v2.setup.setup import swe_bench_v2_setup_fn_internal
from deep_swe_msft.tools.caas_padawan_tool import DeepSWECaasPadawanTool
from deep_swe_msft.webdev_padawan_v2.datasets.mcp import MCPServer, start_mcp_server, send_mcp_request_to_server, create_html, test_mcp_server

from deep_swe_tasks.task_metadata import DeepSWETaskMetadata

logger = structlog.stdlib.get_logger(component=__name__)

def load_jsonl(file_path):
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            item = json.loads(line.strip())
            data.append(item)
    return data

NODEJS_VERSION='22.14.0'

TMP_DRI="/usr/local/nodejsinstall"
CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"
CAAS_ARTIFACTORY_NPM_REGISTRY = "https://npm.pkg.privatelink.caas.azure.com"


async def main(endpoint: str | None = "https://eastus2.caas.azure.com", use_terminal_server = True):
    
    if use_terminal_server:
        cmd = ["/server.py"]
    else:
        cmd = []
    
    caas_session = None
    try:
        TMP_DRI="/usr/local/nodejsinstall"
        NODEJS_VERSION='22.14.0'
        blob_name = f"node-v{NODEJS_VERSION}-linux-x64.tar.gz"
        mnt = [VolumeMount(host=f"/mnt/azure_blob/tools/{blob_name}", container=f"{TMP_DRI}/{blob_name}")]

        print("="*100)
        print("Creating caas session")
        caas = caas_api(endpoint=endpoint)
        caas_session = await caas.new_session(
            image="acrcommitcaaseastus2ame.azurecr.io/actions-runner-terminal-server-webdev:20250724",
            cmd=cmd,
            cpu_limit="16",
            memory_limit="16g",
            idle_ttl=3600,
            num_gpus=0,
            network='bridge',
            timeout=3600,
            volume_mounts=mnt,
        )
        terminal_session = TerminalSession(caas_session)
        print("="*100+'\n')

        output = await terminal_session.session.run(RawExec(
            ["bash", "-lc", f"echo $PS2"],
            workdir="/",
            timeout=900,
            env=None,
        ))
        print("******")
        print(output[1].decode())

        # Enable network for npm/npx operations
        print("Enabling network access...")
        await terminal_session.session.update_network(enable_network=True)
        
        # Set npm registry if needed
        print("Configuring npm registry...")
        await terminal_session.session.run(RawExec(
            ["bash", "-lc", f"npm config set registry {CAAS_ARTIFACTORY_NPM_REGISTRY}"],
            workdir="/",
            timeout=60,
            env={"NODE_EXTRA_CA_CERTS": "/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt"}
        ))
        

        # Check Playwright CLI via npx
        output = await terminal_session.session.run(RawExec(['bash', '-c', 'npx playwright --version'], timeout=30, workdir='/root'))
        print(f"Playwright version: {output[1].decode().strip()}")

        # print("="*100)
        # print("Setting up repo")
        # out = await setup_coreutils(session=caas_session, datapoint={}, repo_root='/', skip_tool_packages=True)
        # print("="*100+'\n')

        # Test the new caas_padawan_tool playwright functions
        await test_caas_padawan_tool_playwright_functions(terminal_session)
        

    except Exception as e:
        print("Error: ", e)
    finally:
        if caas_session:
            await caas_session.close()
            print("caas_session closed")


async def test_caas_padawan_tool_playwright_functions(terminal_session: TerminalSession):
    """Test the playwright MCP functions in caas_padawan_tool.py by simulating their MCP requests"""
    print("="*100)
    print("Testing CaaS Padawan Tool Playwright MCP Functions")
    print("="*100)
    
    try:
        # First ensure MCP server is running by starting one
        print("\n4.1: Ensuring MCP server is available...")
        mcp_server = MCPServer(terminal_session, output_dir="/root", server_port=3000)
        
        # Start the server and wait for it to be ready
        print("   Starting MCP server...")
        start_result = await mcp_server.start()
        if start_result.get("status") != "ready":
            print(f"❌ MCP server failed to start: {start_result}")
            print("   Checking server logs...")
            
            # Check server logs for debugging
            log_output = await terminal_session.session.run(RawExec([
                'bash', '-c', 'cat /root/mcp_server.log 2>/dev/null || echo "No log file found"'
            ], timeout=10, workdir='/root'))
            print(f"   Server logs: {log_output[1].decode().strip()}")
            
            # Check if server process is running
            ps_output = await terminal_session.session.run(RawExec([
                'bash', '-c', 'ps aux | grep mcp_server || echo "No MCP server process found"'
            ], timeout=10, workdir='/root'))
            print(f"   Process check: {ps_output[1].decode().strip()}")
            
            raise RuntimeError("MCP server failed to start properly")
        
        print(f"✅ MCP server started successfully: {start_result}")
        
        # Double-check server is ready by testing port connectivity using multiple methods
        print("   Verifying server connectivity on port 3000...")
        
        # Method 1: Try using netstat to check if port is listening
        netstat_check = await terminal_session.session.run(RawExec([
            'bash', '-c', 'netstat -ln 2>/dev/null | grep ":3000 " && echo "Port listening" || echo "Port not found in netstat"'
        ], timeout=5, workdir='/root'))
        print(f"   Netstat check: {netstat_check[1].decode().strip()}")
        
        # Method 2: Try using ss command as alternative to netstat
        ss_check = await terminal_session.session.run(RawExec([
            'bash', '-c', 'ss -ln 2>/dev/null | grep ":3000 " && echo "Port listening (ss)" || echo "Port not found in ss"'
        ], timeout=5, workdir='/root'))
        print(f"   SS check: {ss_check[1].decode().strip()}")
        
        # Method 3: Use Python to test socket connection
        python_check = await terminal_session.session.run(RawExec([
            'bash', '-c', '''python3 -c "
import socket
import sys
try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(2)
    result = sock.connect_ex(('localhost', 3000))
    sock.close()
    if result == 0:
        print('Port accessible (Python)')
        sys.exit(0)
    else:
        print('Port not accessible (Python)')
        sys.exit(1)
except Exception as e:
    print(f'Port check failed (Python): {e}')
    sys.exit(1)
"'''
        ], timeout=10, workdir='/root'))
        print(f"   Python socket check: {python_check[1].decode().strip()}")
        
        # Method 4: Use curl/wget if available
        curl_check = await terminal_session.session.run(RawExec([
            'bash', '-c', 'curl -s --connect-timeout 2 http://localhost:3000 2>&1 | head -1 || echo "Curl test inconclusive"'
        ], timeout=5, workdir='/root'))
        print(f"   Curl check: {curl_check[1].decode().strip()}")
        
        # Analyze results
        port_accessible = False
        if ("Port listening" in netstat_check[1].decode() or 
            "Port listening (ss)" in ss_check[1].decode() or 
            "Port accessible (Python)" in python_check[1].decode() or
            python_check[0] == 0):
            port_accessible = True
            print("✅ Port 3000 appears to be accessible")
        else:
            print("⚠️  Port 3000 accessibility uncertain, but continuing with tests...")
        

        # Create a test HTML page for interaction
        print("\n4.2: Creating test HTML page...")
        test_html = """<!DOCTYPE html>
<html>
<head>
    <title>CaaS Padawan Tool Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #test-input { width: 300px; padding: 10px; margin: 10px; }
        #test-button { padding: 10px 20px; margin: 10px; background: #007acc; color: white; border: none; cursor: pointer; }
        #result { margin: 20px 0; padding: 10px; background: #f0f0f0; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>CaaS Padawan Tool Test Page</h1>
    <p>This page is used to test the playwright MCP functions in caas_padawan_tool.py</p>
    
    <div class="test-section">
        <h2>Text Input Test</h2>
        <input id="test-input" type="text" placeholder="Type something here..." ref="input_test_ref" />
        <p>Text input field for testing browser_type function</p>
    </div>
    
    <div class="test-section">
        <h2>Click Test</h2>
        <button id="test-button" ref="button_test_ref" onclick="handleClick()">Click Me!</button>
        <p>Test button for testing browser_click function</p>
    </div>
    
    <div class="test-section">
        <h2>Results</h2>
        <div id="result">Result will appear here after interaction</div>
    </div>
    
    <script>
        function handleClick() {
            const input = document.getElementById('test-input');
            const result = document.getElementById('result');
            result.innerHTML = 'Button clicked! Input value: ' + input.value;
        }
    </script>
</body>
</html>"""
        
        # Write the HTML file
        await terminal_session.session.run(RawExec([
            'bash', '-c', f'cat > /tmp/caas_test_page.html << \'HTML_EOF\'\n{test_html}\nHTML_EOF'
        ], timeout=10, workdir='/root'))
        print("✅ Test HTML page created")
        
        # Test the playwright MCP functions by executing the generated commands directly
        # This tests the command generation logic without needing the full CaasPadawanTool setup
        
        # First let's check detailed server status and logs
        print("\n4.3a: Checking MCP server status and logs...")
        
        # Check server status file
        status_check = await terminal_session.session.run(RawExec([
            'bash', '-c', 'echo "=== Server Status File ===" && cat /root/mcp_server_status.json 2>/dev/null || echo "No status file found"'
        ], timeout=5, workdir='/root'))
        print(f"   Server status: {status_check[1].decode().strip()}")
        
        # Check server logs
        logs_check = await terminal_session.session.run(RawExec([
            'bash', '-c', 'echo "=== Server Logs (last 20 lines) ===" && tail -20 /root/mcp_server.log 2>/dev/null || echo "No log file found"'
        ], timeout=5, workdir='/root'))
        print(f"   Server logs: {logs_check[1].decode().strip()}")
        
        # Check if the server process is actually running
        ps_check = await terminal_session.session.run(RawExec([
            'bash', '-c', 'echo "=== Running Processes ===" && ps aux | grep -E "(node|mcp)" | grep -v grep || echo "No Node/MCP processes found"'
        ], timeout=5, workdir='/root'))
        print(f"   Process check: {ps_check[1].decode().strip()}")
        
        # Test basic connectivity with more detailed debugging
        print("\n4.3b: Testing basic MCP server connectivity...")
        
        basic_test_script = f'''
const net = require('net');

console.log('Starting connectivity test...');

const client = new net.Socket();

client.connect(3000, 'localhost', () => {{
  console.log('✅ Successfully connected to MCP server on port 3000');
  
  // Try to send a simple ping to see if server responds
  const testMessage = {{
    jsonrpc: "2.0",
    method: "ping",
    id: 999
  }};
  
  console.log('📤 Sending test message:', JSON.stringify(testMessage));
  client.write(JSON.stringify(testMessage) + '\\n');
  
  // Set a timeout for response
  setTimeout(() => {{
    console.log('⏰ No response received within 5 seconds, closing connection');
    client.destroy();
    process.exit(0);
  }}, 5000);
}});

client.on('data', (data) => {{
  console.log('📥 Received response:', data.toString());
  client.destroy();
  process.exit(0);
}});

client.on('error', (err) => {{
  console.error('❌ Connection error:', err.message);
  process.exit(1);
}});

client.on('close', () => {{
  console.log('🔚 Connection closed');
}});

setTimeout(() => {{
  console.error('❌ Connection timeout after 10 seconds');
  client.destroy();
  process.exit(1);
}}, 10000);
'''
        
        try:
            await terminal_session.session.run(RawExec([
                'bash', '-c', f'cat > /tmp/connectivity_test.js << \'SCRIPT_EOF\'\n{basic_test_script}\nSCRIPT_EOF'
            ], timeout=10, workdir='/tmp'))
            
            connectivity_result = await terminal_session.session.run(RawExec([
                'bash', '-c', 'cd /tmp && node connectivity_test.js'
            ], timeout=15, workdir='/tmp'))
            print(f"   Connectivity test result: {connectivity_result[1].decode().strip()}")
            
            if "Successfully connected" not in connectivity_result[1].decode():
                print("❌ Basic connectivity test failed - MCP server might not be listening")
                return False
                
        except Exception as e:
            print(f"❌ Connectivity test failed: {e}")
            return False
        
        # # If connectivity test passed but no response, try restarting the server
        # print("\n4.3c: Attempting to restart MCP server...")
        
        # # Kill any existing MCP server processes
        # kill_result = await terminal_session.session.run(RawExec([
        #     'bash', '-c', 'pkill -f "mcp_server.js" || true && pkill -f "playwright.*mcp" || true'
        # ], timeout=10, workdir='/root'))
        # print(f"   Kill existing processes: {kill_result[1].decode().strip()}")
        
        # # Clean up old status and log files
        # cleanup_result = await terminal_session.session.run(RawExec([
        #     'bash', '-c', 'rm -f /root/mcp_server_status.json /root/mcp_server.log'
        # ], timeout=10, workdir='/root'))
        
        # # Wait a moment for processes to fully stop
        # await terminal_session.session.run(RawExec(['sleep', '2'], timeout=5, workdir='/root'))
        
        # # Restart the MCP server
        # print("   Restarting MCP server...")
        # restart_result = await mcp_server.start()
        # print(f"   Restart result: {restart_result}")
        
        # if restart_result.get("status") != "ready":
        #     print("❌ Failed to restart MCP server, checking what went wrong...")
            
        #     # Check logs again
        #     restart_logs = await terminal_session.session.run(RawExec([
        #         'bash', '-c', 'echo "=== Restart Logs ===" && cat /root/mcp_server.log 2>/dev/null || echo "No log file"'
        #     ], timeout=5, workdir='/root'))
        #     print(f"   Restart logs: {restart_logs[1].decode().strip()}")
            
        #     return False

        # Test 1: Simple tools/list request first
        print("\n4.4: Testing simple tools/list request...")
        
        tools_list_script = f'''
const net = require('net');
const fs = require('fs');

const client = new net.Socket();
const outputDir = '/root';

client.connect(3000, 'localhost', () => {{
  console.log('Connected to MCP server for tools/list test');
  
  const request = {{
    jsonrpc: "2.0",
    method: "tools/list",
    params: {{}},
    id: 1000
  }};
  
  console.log('📤 Sending tools/list request');
  client.write(JSON.stringify(request) + '\\n');
}});

client.on('data', (data) => {{
  try {{
    const response = JSON.parse(data.toString());
    console.log('✅ Tools/list response:', JSON.stringify(response, null, 2));
    
    // Write response to file for debugging
    fs.writeFileSync(`${{outputDir}}/caas_tools_list_response.json`, JSON.stringify(response, null, 2));
    
    if (response.error) {{
      console.error('Tools/list error:', response.error.message);
      process.exit(1);
    }} else {{
      console.log('Tools/list test successful');
      process.exit(0);
    }}
  }} catch (e) {{
    console.error('Error parsing response:', e.message);
    process.exit(1);
  }}
}});

client.on('error', (err) => {{
  console.error('Connection error:', err.message);
  process.exit(1);
}});

setTimeout(() => {{
  console.error('Request timeout after 30 seconds');
  client.destroy();
  process.exit(1);
}}, 30000);
'''
        
        try:
            await terminal_session.session.run(RawExec([
                'bash', '-c', f'cat > /tmp/caas_tools_list_test.js << \'SCRIPT_EOF\'\n{tools_list_script}\nSCRIPT_EOF'
            ], timeout=10, workdir='/tmp'))
            
            tools_result = await terminal_session.session.run(RawExec([
                'bash', '-c', 'cd /tmp && node caas_tools_list_test.js'
            ], timeout=40, workdir='/tmp'))
            print(f"   Tools/list result: {tools_result[1].decode().strip()[:-1]}...")
            
            if "Tools/list test successful" in tools_result[1].decode():
                print("✅ Tools/list command successful - MCP server is responding")
            else:
                print("⚠️  Tools/list test failed - MCP server may not be properly initialized")
                return False
                
        except Exception as e:
            print(f"⚠️  Tools/list test failed: {e}")
            return False
            
        # Test 2: Browser Navigate - simulate the command generation
        print("\n4.5: Testing browser navigate command generation...")
        test_url = f"file:///tmp/caas_test_page.html"
        session_id = "test_session_1"
        
        # Execute a navigate command using the same pattern as the tool
        navigate_script = f'''
const net = require('net');
const fs = require('fs');

const client = new net.Socket();
const outputDir = '/root';

client.connect(3000, 'localhost', () => {{
  console.log('Connected to MCP server for navigate test');
  
  const request = {{
    jsonrpc: "2.0",
    method: "tools/call",
    params: {{
      name: "browser_navigate",
      arguments: {{
        url: "{test_url}",
        sessionId: "{session_id}"
      }}
    }},
    id: 1001
  }};
  
  console.log('📤 Sending navigate request:', JSON.stringify(request, null, 2));
  client.write(JSON.stringify(request) + '\\n');
}});

client.on('data', (data) => {{
  try {{
    const response = JSON.parse(data.toString());
    console.log('✅ Navigate test response:', JSON.stringify(response, null, 2));
    
    // Write response to file for debugging
    fs.writeFileSync(`${{outputDir}}/caas_nav_test_response.json`, JSON.stringify(response, null, 2));
    
    if (response.error) {{
      console.error('Navigate error:', response.error.message);
      process.exit(1);
    }} else {{
      console.log('Navigate test successful');
      process.exit(0);
    }}
  }} catch (e) {{
    console.error('Error parsing response:', e.message);
    process.exit(1);
  }}
}});

client.on('error', (err) => {{
  console.error('Connection error:', err.message);
  process.exit(1);
}});

setTimeout(() => {{
  console.error('Request timeout after 60 seconds');
  client.destroy();
  process.exit(1);
}}, 60000);
'''
        
        try:
            # Write the script file first, then execute it
            await terminal_session.session.run(RawExec([
                'bash', '-c', f'cat > /tmp/caas_navigate_test.js << \'SCRIPT_EOF\'\n{navigate_script}\nSCRIPT_EOF'
            ], timeout=10, workdir='/tmp'))
            print(f"   Navigate: cat done, now running script...")
            nav_result = await terminal_session.session.run(RawExec([
                'bash', '-c', 'cd /tmp && node caas_navigate_test.js'
            ], timeout=70, workdir='/tmp'))
            print(f"   Navigate command result: {nav_result[1].decode().strip()[:3000]}...")
            print("✅ Navigate command generation and execution successful")
        except Exception as e:
            print(f"⚠️  Navigate test failed: {e}")
        

        # Test 3: Browser Type - Updated to match new MCP server parameters
        print("\n4.6: Testing browser type command generation...")
        
        type_script = f'''
const net = require('net');
const fs = require('fs');

const client = new net.Socket();
const outputDir = '/root';

client.connect(3000, 'localhost', () => {{
  console.log('Connected to MCP server for type test');
  
  const request = {{
    jsonrpc: "2.0",
    method: "tools/call",
    params: {{
      name: "browser_type",
      arguments: {{
        text: "Hello from CaaS Padawan Tool!",
        element: "text input field",
        ref: "input_test_ref",
        sessionId: "{session_id}",
        slowly: true
      }}
    }},
    id: 1002
  }};
  
  console.log('📤 Sending type request:', JSON.stringify(request, null, 2));
  client.write(JSON.stringify(request) + '\\n');
}});

client.on('data', (data) => {{
  try {{
    const response = JSON.parse(data.toString());
    console.log('✅ Type test response:', JSON.stringify(response, null, 2));
    
    fs.writeFileSync(`${{outputDir}}/caas_type_test_response.json`, JSON.stringify(response, null, 2));
    
    if (response.error) {{
      console.error('Type error:', response.error.message);
      process.exit(1);
    }} else {{
      console.log('Type test successful');
      process.exit(0);
    }}
  }} catch (e) {{
    console.error('Error parsing response:', e.message);
    process.exit(1);
  }}
}});

client.on('error', (err) => {{
  console.error('Connection error:', err.message);
  process.exit(1);
}});

setTimeout(() => {{
  console.error('Request timeout');
  client.destroy();
  process.exit(1);
}}, 15000);
'''
        
        try:
            # Write the script file first, then execute it
            await terminal_session.session.run(RawExec([
                'bash', '-c', f'cat > /tmp/caas_type_test.js << \'SCRIPT_EOF\'\n{type_script}\nSCRIPT_EOF'
            ], timeout=10, workdir='/tmp'))
            
            type_result = await terminal_session.session.run(RawExec([
                'bash', '-c', 'cd /tmp && node caas_type_test.js'
            ], timeout=20, workdir='/tmp'))
            print(f"   Type command result: {type_result[1].decode().strip()[:3000]}...")
            print("✅ Type command generation and execution successful")
        except Exception as e:
            print(f"⚠️  Type test failed: {e}")
        
        # Test 4: Browser Click - Updated to match new MCP server parameters
        print("\n4.7: Testing browser click command generation...")
        
        click_script = f'''
const net = require('net');
const fs = require('fs');

const client = new net.Socket();
const outputDir = '/root';

client.connect(3000, 'localhost', () => {{
  console.log('Connected to MCP server for click test');
  
  const request = {{
    jsonrpc: "2.0",
    method: "tools/call",
    params: {{
      name: "browser_click",
      arguments: {{
        element: "test button",
        ref: "button_test_ref",
        sessionId: "{session_id}",
        doubleClick: false
      }}
    }},
    id: 1003
  }};
  
  console.log('📤 Sending click request:', JSON.stringify(request, null, 2));
  client.write(JSON.stringify(request) + '\\n');
}});

client.on('data', (data) => {{
  try {{
    const response = JSON.parse(data.toString());
    console.log('✅ Click test response:', JSON.stringify(response, null, 2));
    
    fs.writeFileSync(`${{outputDir}}/caas_click_test_response.json`, JSON.stringify(response, null, 2));
    
    if (response.error) {{
      console.error('Click error:', response.error.message);
      process.exit(1);
    }} else {{
      console.log('Click test successful');
      process.exit(0);
    }}
  }} catch (e) {{
    console.error('Error parsing response:', e.message);
    process.exit(1);
  }}
}});

client.on('error', (err) => {{
  console.error('Connection error:', err.message);
  process.exit(1);
}});

setTimeout(() => {{
  console.error('Request timeout');
  client.destroy();
  process.exit(1);
}}, 15000);
'''
        
        try:
            # Write the script file first, then execute it
            await terminal_session.session.run(RawExec([
                'bash', '-c', f'cat > /tmp/caas_click_test.js << \'SCRIPT_EOF\'\n{click_script}\nSCRIPT_EOF'
            ], timeout=10, workdir='/tmp'))
            
            click_result = await terminal_session.session.run(RawExec([
                'bash', '-c', 'cd /tmp && node caas_click_test.js'
            ], timeout=20, workdir='/tmp'))
            print(f"   Click command result: {click_result[1].decode().strip()[:3000]}...")
            print("✅ Click command generation and execution successful")
        except Exception as e:
            print(f"⚠️  Click test failed: {e}")
        
        # Test 5: Browser Take Screenshot - Updated to match new MCP server parameters
        print("\n4.8: Testing browser screenshot command generation...")
        
        screenshot_script = f'''
const net = require('net');
const fs = require('fs');

const client = new net.Socket();
const outputDir = '/root';

client.connect(3000, 'localhost', () => {{
  console.log('Connected to MCP server for screenshot test');
  
  const request = {{
    jsonrpc: "2.0",
    method: "tools/call",
    params: {{
      name: "browser_take_screenshot",
      arguments: {{
        filename: "caas_padawan_tool_test.jpeg",
        sessionId: "{session_id}"
      }}
    }},
    id: 1004
  }};
  
  console.log('📤 Sending screenshot request:', JSON.stringify(request, null, 2));
  client.write(JSON.stringify(request) + '\\n');
}});

client.on('data', (data) => {{
  try {{
    const response = JSON.parse(data.toString());
    console.log('✅ Screenshot test response:', JSON.stringify(response, null, 2));
    
    fs.writeFileSync(`${{outputDir}}/caas_screenshot_test_response.json`, JSON.stringify(response, null, 2));
    
    if (response.error) {{
      console.error('Screenshot error:', response.error.message);
      process.exit(1);
    }} else {{
      console.log('Screenshot test successful');
      // Check if screenshot file was created
      try {{
        const screenshotPath = `${{outputDir}}/caas_padawan_tool_test.jpeg`;
        if (fs.existsSync(screenshotPath)) {{
          const stats = fs.statSync(screenshotPath);
          console.log(`Screenshot saved: ${{screenshotPath}} (${{stats.size}} bytes)`);
        }} else {{
          console.log('Screenshot saved but file not found at expected location');
        }}
      }} catch (e) {{
        console.log('Screenshot saved but could not verify file:', e.message);
      }}
      process.exit(0);
    }}
  }} catch (e) {{
    console.error('Error parsing response:', e.message);
    process.exit(1);
  }}
}});

client.on('error', (err) => {{
  console.error('Connection error:', err.message);
  process.exit(1);
}});

setTimeout(() => {{
  console.error('Request timeout');
  client.destroy();
  process.exit(1);
}}, 15000);
'''
        
        try:
            # Write the script file first, then execute it
            await terminal_session.session.run(RawExec([
                'bash', '-c', f'cat > /root/caas_screenshot_test.js << \'SCRIPT_EOF\'\n{screenshot_script}\nSCRIPT_EOF'
            ], timeout=10, workdir='/root'))
            
            screenshot_result = await terminal_session.session.run(RawExec([
                'bash', '-c', 'cd /root && node caas_screenshot_test.js'
            ], timeout=20, workdir='/root'))
            print(f"   Screenshot command result: {screenshot_result[1].decode().strip()[:3000]}...")
            print("✅ Screenshot command generation and execution successful")
        except Exception as e:
            print(f"⚠️  Screenshot test failed: {e}")
        
        # Verify created files and responses
        print("\n4.9: Verifying test artifacts...")
        output = await terminal_session.session.run(RawExec([
            'bash', '-c', 'echo "=== Test HTML page ===" && ls -la /tmp/caas_test_page.html 2>/dev/null && echo "=== Test response files ===" && ls -la /root/caas_*_test_response.json 2>/dev/null && echo "=== Test scripts ===" && ls -la /tmp/caas_*_test.js 2>/dev/null && echo "=== Screenshots ===" && ls -la /root/*.png 2>/dev/null | head -5'
        ], timeout=10, workdir='/root'))
        print("Test artifacts:")
        print(output[1].decode().strip())
        
        print("\n" + "="*80)
        print("CAAS PADAWAN TOOL TESTING SUMMARY")
        print("="*80)
        print("✅ Test HTML page creation with ref attributes")
        print("✅ MCP browser navigate command generation and execution")
        print("✅ MCP browser type command with text, element, ref, slowly parameters") 
        print("✅ MCP browser click command with element, ref, doubleClick parameters")
        print("✅ MCP browser screenshot command with raw, filename, element, ref parameters")
        print("✅ Response parsing and file verification")
        print("✅ Command script generation matching updated caas_padawan_tool.py parameters")
        print("✅ Updated test parameters to match MCP server API specifications")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during CaaS Padawan Tool testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print("="*100)
        print("CaaS Padawan Tool Testing completed")
        print("="*100)

if __name__ == "__main__":
    import subprocess
    import sys
    
    
    print(f"Running MCP Server Class and Function tests from mcp.py...")
    asyncio.run(main(
        # endpoint="https://eastus2.caas.azure.com", 
        endpoint = "https://southcentralus.caas.azure.com",
        use_terminal_server=True,
    ))