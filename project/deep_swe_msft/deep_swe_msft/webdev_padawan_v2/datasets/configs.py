from functools import partial
from typing import Any
import random

import berry
import chat

import caas_autograding.grader as caas_graders
import chz
from chat.render.v4.experimental.strawberry.formatter import <PERSON><PERSON>hannel
from deep_swe.datasets.configs import DeepSWEDatasetConfig
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig  # type: ignore
from deep_swe_msft.webdev_padawan_v2.datasets.caas_resource_config import CaasContainerResourceConfig
from deep_swe_msft.webdev_padawan_v2.datasets.setup import webdev_setup_fn_coreutils, webdev_setup_fn_coreutils_with_mcp_tools
from deep_swe_msft.webdev_padawan_v2.datasets.caas_padawan_tool import PadawanToolConfig
from deep_swe_msft.tools.caas_mix_tool import MixToolConfig
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CRESCO_STORAGE_NAME
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from qstar.sample_completers.basic_sample_completer import BasicSampleCompleter

from qstar.graders.taskgen_utils import TokenCompleterGraderService
from deep_swe.graders.code_plan_patch_grader import CodePlanConsistencyGrader, CodePlanPatchGrader
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK

from deep_swe_msft.padawan_graders.cotograder_utils import (
    COTOGRADER_RENDERER,
    COTOGRADER_CHZ_ARGV,
    CotoGraderService
)

from deep_swe_msft.webdev_padawan_v2.datasets.web_dev_prompt import PROMPT_LATEST, PROMPT_EVAL
from qstar.graders.accepts_all_grader import AcceptsAllGrader

from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.configs import (
    make_multistage_grader, 
)

ENABLE_NETWORK = True
HINT_PROBABILITY = 0.0  # Probability of including hints in the prompt

@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig, IsOverride):
    caas_cpu_limit: float = 1.0  # Increase to 2 if actually using VSCode
    caas_memory_limit: int = 2
    caas_container_image: str = "vscode-agent"  # Has more pip dependencies!
    caas_idle_ttl: int = 30 * 60 # in seconds, 30 minutes
    enable_network_after_setup: bool = False
    caas_endpoint: str = CAAS_ENDPOINT


def _make_tool_configs(
    container_tool_config: str = "padawan_tool",
    internet_policy: str = "no_access",
    tool_channel: BerryChannel = BerryChannel.CHAIN_OF_THOUGHT,
) -> tuple[ToolConfig, ...]:
    container_tool_config = "padawan_tool" # Force to use padawan tool for now.
    if container_tool_config == "padawan_tool":
        return (PadawanToolConfig(internet_policy=internet_policy, tool_channel=tool_channel),)
    elif container_tool_config == "mix_tool":
        return (MixToolConfig(internet_policy=internet_policy, tool_channel=tool_channel),)
    else:
        raise ValueError(f"Unknown padawan tool config: {container_tool_config}")


# Copied from project/deep_swe/deep_swe/datasets/configs.py with updated grader path and bus line.
# let's not checking tool use for now, as we only care about the final answer
def make_webdev_multistage_grader(use_sp_hard_v2: bool = False) -> MultiStageGrader:
    grader_argvs = [
        [
            "=deep_swe_msft.webdev_padawan_v2.graders.webdev_grader:WebDevCotograder",
            f"renderer_name={COTOGRADER_RENDERER}",
            *COTOGRADER_CHZ_ARGV,
            f"grader_max_tokens={16384}",
        ]
    ]

    if use_sp_hard_v2:
        grader_argvs.append(
            [
                "=deep_swe_msft.padawan_graders.system_prompt_following_grader_hard_v2:SPFollowHardV2Cotograder",
                f"renderer_name={COTOGRADER_RENDERER}",
                *COTOGRADER_CHZ_ARGV,
                f"grader_max_tokens={16384}",
            ]
        )

    return make_multistage_grader(
        grader_argvs,
        tuple([]),
    )  # channels_for_answer not used.

    # [TODO](xiaofewa): create mult-stage grader for webdev arena.
    # return MultiStageGrader(
    #         graders=(
    #             AcceptsAllGrader(),
    #         )
    #     )

def make_webdev_commentreply_multistage_grader() -> MultiStageGrader:
    grader_argvs = [
        [
            "=deep_swe_msft.webdev_padawan_v2.graders.webdev_grader:WebDevCommentReplyCotograder",
            f"renderer_name={COTOGRADER_RENDERER}",
            *COTOGRADER_CHZ_ARGV,
            f"grader_max_tokens={16384}",
        ]
    ]
    return make_multistage_grader(
        grader_argvs,
        tuple([]),
    )  # channels_for_answer not used.


def random_tool_set() -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:random_tool_set",
    )


# Copied from project/deep_swe/deep_swe/datasets/configs.py with an updated dataset_id, tool_configs, resource_conigs, datapoint_converters
@chz.chz
class WebDevDatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.train"
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_webdev_multistage_grader,
            use_sp_hard_v2=True,
        )
    )
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            container_tool_config='padawan_tool',
            internet_policy='enabled' if ENABLE_NETWORK else 'no_access',
        ),
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                # setup_fn=webdev_setup_fn_coreutils,
                # caas_container_image="acrbuiltincaasglobalame.azurecr.io/aio:20250703-1260143-official",
                setup_fn=webdev_setup_fn_coreutils_with_mcp_tools,
                caas_container_image="acrcommitcaassouthcentralusame.azurecr.io/actions-runner-terminal-server-webdev:20250724",
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            # [TODO](xiaofewa): use a more appropriate datapoint converter for this dataset.
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_datapoint_converter",
            ),
        )
    )


def webdev_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    system_message = chat.Message.system(
        instructions=PROMPT_LATEST,
    )

    hint_message = '''
Hints:
- You must use `npm create-react-app` and `npm start` to create a new React app
- You must take a screenshot using `playwright_browser_take_screenshot`
- Screenshot paths should be included in the final PR description using markdown syntax: `![screenshot](path/to/screenshot.png)`
- Playwright MCP tools default output folder is `/root/screenshots/`. This path will be used to store screenshots.
- You must attempt to use these MCP tools:
  - `playwright_browser_navigate`
  - `playwright_browser_take_screenshot`
  - `playwright_browser_type`
  - `playwright_browser_click`
'''

    # Randomly decide whether to append the screenshot message
    if random.random() < HINT_PROBABILITY:  # Use HINT_PROBABILITY to set the chance to append the screenshot message
        screenshot_message = chat.Message.user(
            content=hint_message,
        )
        new_convo = convo.with_messages(
            [
                system_message,
                *convo.messages,
                screenshot_message,
            ]
        )
    else:
        new_convo = convo.with_messages(
            [
                system_message,
                *convo.messages,
            ]
        )
    dp = {"problem": new_convo}
    return [dp]


def webdev_singleturn_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    
    # Extract user message for problem statement
    user_messages = [msg for msg in convo.messages if msg.author.role == "user"]
    user_content = user_messages[0].content.parts[0] if user_messages[0].content.parts else ""
    
    # Create structured prompt for single-turn
    structured_prompt = f"""
You are working on an issue in a web development repository.

<repository_context>

</repository_context>

Consider the following problem statement:

<problem_statement>
---

_This section details on the original issue you should resolve_

<issue_title>

</issue_title>

<issue_description>
{user_content}
</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>

</problem_statement>

Implement the necessary changes to the repository so that the requirements specified in the <problem_statement> are met. 
If the repository is empty, initialize a new React application using the `npx create-react-app` scaffolding tool.
"""

    hint_message = '''
Hints:
- You must use `npm create-react-app` and `npm start` to create a new React app
- You must take a screenshot using `playwright_browser_take_screenshot`
- Screenshot paths should be included in the final PR description using markdown syntax: `![screenshot](path/to/screenshot.png)`
- Playwright MCP tools default output folder is `/root/screenshots/`. This path will be used to store screenshots.
- You must attempt to use these MCP tools:
  - `playwright_browser_navigate`
  - `playwright_browser_take_screenshot`
  - `playwright_browser_type`
  - `playwright_browser_click`
'''

    # Create system message with the latest prompt
    system_message = chat.Message.system(
        instructions=PROMPT_LATEST,
    )
    
    # Create a new user message with the structured prompt
    structured_user_message = chat.Message.user(
        content=structured_prompt,
    )
    
    # Randomly decide whether to append the screenshot message
    if random.random() < HINT_PROBABILITY:
        screenshot_message = chat.Message.user(
            content=hint_message,
        )
        new_convo = convo.with_messages(
            [
                system_message,
                structured_user_message,
                screenshot_message,
            ]
        )
    else:
        new_convo = convo.with_messages(
            [
                system_message,
                structured_user_message,
            ]
        )

    dp = {"problem": new_convo}
    return [dp]


def webdev_multiturn_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    
    # Extract messages by role
    user_messages = [msg for msg in convo.messages if msg.author.role == "user"]
    assistant_messages = [msg for msg in convo.messages if msg.author.role == "assistant"]
    
    # Get first user message for problem statement
    first_user_content = user_messages[0].content.parts[0] if user_messages[0].content.parts else ""
    
    # Get second user message for issue description  
    second_user_content = user_messages[1].content.parts[0] if user_messages[1].content.parts else ""
    
    # Get first assistant message for repository context
    first_assistant_content = assistant_messages[0].content.parts[0] if assistant_messages[0].content.parts else ""
    
    # Create structured prompt
    structured_prompt = f"""
You are working on an issue in a web development repository.

<repository_context>
The repository contains the following code:

{first_assistant_content}
</repository_context>

Consider the following problem statement:

<problem_statement>

_This section details on the original issue you should resolve_
<issue_title>

{second_user_content}
</issue_title>

<issue_description>

The user initially requested: "{first_user_content}"

However, after reviewing the current repository implementation, additional requirements and modifications have been identified. The specific issues and improvements needed are detailed as follows:

{second_user_content}

</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>

</comments>

</problem_statement>

Implement the necessary changes to the repository so that the requirements specified in the <problem_statement> are met.
"""

    hint_message = '''
Hints:
- You must use `npm create-react-app` and `npm start` to create a new React app
- You must take a screenshot using `playwright_browser_take_screenshot`
- Screenshot paths should be included in the final PR description using markdown syntax: `![screenshot](path/to/screenshot.png)`
- Playwright MCP tools default output folder is `/root/screenshots/`. This path will be used to store screenshots.
- You must attempt to use these MCP tools:
  - `playwright_browser_navigate`
  - `playwright_browser_take_screenshot`
  - `playwright_browser_type`
  - `playwright_browser_click`
'''

    # Create system message with the latest prompt
    system_message = chat.Message.system(
        instructions=PROMPT_LATEST,
    )
    
    # Create a new user message with the structured prompt
    structured_user_message = chat.Message.user(
        content=structured_prompt,
    )
    
    # Include any remaining messages after the first two user messages
    remaining_messages = convo.messages[4:] if len(convo.messages) > 4 else []
    
    # Randomly decide whether to append the screenshot message
    if random.random() < HINT_PROBABILITY:  # Use HINT_PROBABILITY to set the chance to append the screenshot message
        screenshot_message = chat.Message.user(
            content=hint_message,
        )
        new_convo = convo.with_messages(
            [
                system_message,
                structured_user_message,
                *remaining_messages,
                screenshot_message,
            ]
        )
    else:
        new_convo = convo.with_messages(
            [
                system_message,
                structured_user_message,
                *remaining_messages,
            ]
        )

    dp = {"problem": new_convo}
    return [dp]


def webdev_multiturn_commentreply_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    
    # Extract messages by role
    user_messages = [msg for msg in convo.messages if msg.author.role == "user"]
    assistant_messages = [msg for msg in convo.messages if msg.author.role == "assistant"]
    
    # Get first user message for problem statement
    first_user_content = user_messages[0].content.parts[0] if user_messages[0].content.parts else ""
    
    # Get second user message for issue description  
    second_user_content = user_messages[1].content.parts[0] if user_messages[1].content.parts else ""
    
    # Get first assistant message for repository context
    first_assistant_content = assistant_messages[0].content.parts[0] if assistant_messages[0].content.parts else ""
    
    # Create structured prompt
    structured_prompt = f"""
You have been given comments on the previous commits you made in the repository.

You are working on an issue in a web development repository.

<repository_context>
The repository contains the following code:

{first_assistant_content}
</repository_context>

Consider the following problem statement:

<problem_statement>

_This section details on the original issue you should resolve_
<issue_title>

{second_user_content}
</issue_title>

<issue_description>

The user initially requested: "{first_user_content}".

However, after reviewing the current repository implementation, additional requirements and modifications have been identified. The specific issues and improvements needed are detailed in <comments> below:

</issue_description>

## Comments on the Issue (you are @copilot in this section)

<comments>
<pr_comments>
<comment_new>
<comment_id>{random.randint(1000000000, 9999999999)}</comment_id>
<author>@me</author>
{'@copilot ' + second_user_content + '.' if random.random() < 0.5 else second_user_content + '. @copilot'}
</comment_new>
</comments>

</problem_statement>

Analyze and determine if action or explanation is needed
<rules_for_making_changes>
* Start by fully understanding <problem_statement> and the <comments> before making any changes. You will only address the <comment_new> comments. Reply to new comments as you address them. Ignore comments that are not actionable or directed at you.
* For each new comment, determine:
    - If it a request for changes, a question on the code or a suggestion.
    - Is it addressed to you or someone else, e.g. "Hey @username, ..." or "@username can you ...".
    - Is it a general comment or praise.
* If there are no comments you need to address or reply to, you can stop now without exploring or explaining yourself.
* If you need more information, you can reply to the comment and ask for clarification.
* If an action or explanation is needed, follow the steps below.
</rules_for_making_changes>

<replying_to_comments>
* Use **reply_to_comment** to reply to <comment_new> comments only.
* Do not reply to a comment more than once. Not all comments need a reply.
* Use the following guidelines to determine if you need to reply to a comment:
    - If the comment is a question, provide a clear and concise answer. Only reply to questions about the code you wrote.
    - If the comment is a request for changes, reply once you have made the changes and include the short hash of the commit that addresses the comment.
    - If the comment is a suggestion or feedback, determine if it is actionable and relevant to the changes you made. If so, reply after you make the changes. If not, do not reply.
    - If the comment is a general comment or praise, do not reply.
* Use the following guidelines for the content of your reply:
    - Be concise and to the point. Avoid unnecessary details or explanations.
    - Do not restate or summarize the comment. Focus on addressing the specific request or question.
    - Use a friendly and professional tone. Do not thank the user or compliment their feedback or comments in your response.

**ALWAYS** include a screenshot of any UI changes so the user can see the impact of the change."
</replying_to_comments>

"""

    hint_message = '''
Hints:
- You must use `npm create-react-app` and `npm start` to create a new React app
- You must take a screenshot using `playwright_browser_take_screenshot`
- Screenshot paths should be included in the final PR description using markdown syntax: `![screenshot](path/to/screenshot.png)`
- Playwright MCP tools default output folder is `/root/screenshots/`. This path will be used to store screenshots.
- You must attempt to use these MCP tools:
  - `playwright_browser_navigate`
  - `playwright_browser_take_screenshot`
  - `playwright_browser_type`
  - `playwright_browser_click`
'''

    # Create system message with the latest prompt
    system_message = chat.Message.system(
        instructions=PROMPT_LATEST,
    )
    
    # Create a new user message with the structured prompt
    structured_user_message = chat.Message.user(
        content=structured_prompt,
    )
    
    # Include any remaining messages after the first two user messages
    remaining_messages = convo.messages[4:] if len(convo.messages) > 4 else []
    
    # Randomly decide whether to append the screenshot message
    if random.random() < HINT_PROBABILITY:  # Use HINT_PROBABILITY to set the chance to append the screenshot message
        screenshot_message = chat.Message.user(
            content=hint_message,
        )
        new_convo = convo.with_messages(
            [
                system_message,
                structured_user_message,
                *remaining_messages,
                screenshot_message,
            ]
        )
    else:
        new_convo = convo.with_messages(
            [
                system_message,
                structured_user_message,
                *remaining_messages,
            ]
        )

    dp = {"problem": new_convo}
    return [dp]


def webdev_eval_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    system_message = chat.Message.system(
        instructions=PROMPT_EVAL,
    )
    new_convo = convo.with_messages(
        [
            system_message,
            *convo.messages,
        ]
    )
    dp = {"problem": new_convo}
    return [dp]


# ===== WebDevArena Train datasets ======

@chz.chz
class WebDevArenaSingleTurnFilteredDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.train_v2"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_singleturn_datapoint_converter",
            ),
        )
    )


@chz.chz
class WebDevArenaMultiTurnFilteredDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.multi_turn.train"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_multiturn_datapoint_converter",
            ),
        )
    )


@chz.chz
class WebDevArenaMultiTurnFilteredCommentReplyDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.multi_turn.train_duplicate"
    grader: Grader[HarmonyCompletionDatapoint] = override(make_webdev_commentreply_multistage_grader)
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_multiturn_commentreply_datapoint_converter",
            ),
        )
    )

# ===== WebDevArena Eval datasets =====

@chz.chz
class WebDevArenaSingleTurnEvalDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena.single_turn.test"

@chz.chz
class WebDevArenaSingleTurnFilteredEvalDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.test"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_eval_datapoint_converter",
            ),
        )
    )

@chz.chz
class WebDevSweagentdEvalDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.sweagentd"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_eval_datapoint_converter",
            ),
        )
    )

@chz.chz
class WebDevArenaMultiTurnEvalDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena.multi_turn.test"

