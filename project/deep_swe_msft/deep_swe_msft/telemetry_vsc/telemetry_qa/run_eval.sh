WANDB_PROJECT_NAME=mswe-pevals

# vsc checkpoint
CKPT="az://orngscuscresco/twapi/mini/e/haoranxu-vsc-mix16-arm2-prerun-itc-768-juice-128-32x320820-0903/policy/step_000070/250821092713KTLR7IJU-0/"

RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"
# RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"

dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="vsc-telemetry-qa-yield256-$dt-peaval"

CMD=(
beam python --start-daemon --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}

:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken

root_config='mini.root.dev init_actors_rpc_timeout=600 driver_rpc_timeout=600 dedicated_driver_node=False'

auto_inherit_training_args=False

policy.initial_checkpoint=${CKPT}
policy.is_multimodal=True
...encoding_name=orion_200k
...harmony_renderer_name=${RENDERER_NAME}

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=2                   # swang concurrent thread in one process
peashooter.num_sampling_processes=100               # swang concurrent process in one worker
peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600

# Set this if you want to eval all checkpoints at once in parallel (Not needed if your training run just started.)
# max_workers_per_step=4 # swang nodes per step, cannot exceed total sample nodes
# max_active_steps=2    # swang active steps

eval_settings.eval_every=1
# eval_settings.min_step=150
# eval_settings.max_step=150
eval_settings.exit_on_no_new_checkpoint=True # False to keep it running and watching for new checkpoints saved from training experiment id
# Set to True if you want to evaluate step0 (your prior)
eval_settings.eval_initial_policy=False
# set it if no training experiment id
eval_settings.checkpoint_dir=${CKPT}

# data
# telemetry qa data
':deep_swe_msft.telemetry_vsc.telemetry_qa.presets:eval_telemetry_qa'

# swe eval
# :deep_swe_eval_msft.swe_bench.peaval.vsc.presets:ev_sm_bench_vsc
# eval_settings.evals.0.dataset.override_target_samples_per_instance=1
# eval_settings.evals.0.dataset.max_num_yields=100

# # mswe
# ':deep_swe_eval_msft.msweb.peaval.vsc.presets:ev_sm_bench_juice_vsc(juice=768 override_target_samples_per_instance=1 max_num_yields=256 langs=js-ts)'

eval_settings.evals.0.dataset.override_target_samples_per_instance=1

metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

policy.n_ctx=262144
defaults.n_ctx=262144
defaults.sampler.max_num_yields=256
defaults.sampler.harmony_constrained_sampling=True

# msft special
load.restore_from_all_clusters=False

# defaults.channel_config.channels="analysis,final"
peashooter.sampling_use_ev3=True
seed=47

...dataset_container=orngscuscresco

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=${WANDB_PROJECT_NAME}
kafka_enable=False
enable_slackbot=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log




# dt=`date '+%Y%m%d-%H%M%S'`
# EXPERIMENT_NAME="vsc-telemetry-qa-$dt-peaval"

# # INIT_CKPT="az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted/"
# # CKPT="az://orngscuscresco/twapi/mini/e/qingruzhang-sft_workflow_swe-3k-sbh-3k-web-2k-rrb-2k_gpt5miniv2_lr8e-5-20250809-013442-run1/51c519e2-4dbf-485c-b1f3-40ed3bdab1eb/checkpoint/model1/"
# # RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"

# INIT_CKPT="az://orngscuscresco/twapi/mini/e/haoranxu-vsc-mix16-arm2-prerun-itc-768-juice-128-32x320820-0903/policy/step_000070/250821092713KTLR7IJU-0/"
# CKPT="az://orngscuscresco/twapi/mini/e/haoranxu-vsc-mix16-arm2-prerun-itc-768-juice-128-32x320820-0903/policy/step_000070/250821092713KTLR7IJU-0/"
# RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"

# CMD=(
# oaipkg run qstar.run_eval nostrict
# name=${EXPERIMENT_NAME}

# # :berry_models.scallion_lpe:d36_80g_mbg16_bf16
# :berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
# root_config="mini.root.dev init_actors_rpc_timeout=600"
# policy.initial_checkpoint="$INIT_CKPT"
# eval_settings.checkpoint_dir="$CKPT"
# auto_inherit_training_args=False

# # policy.model_config_name=falcon.multimodal.runs.scallion-d36-s64-lpe
# # policy.sampling_ml_config='raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=2 pipe_depth=4 n_replicas=1'
# policy.is_multimodal=True
# ...encoding_name=orion_200k
# ...harmony_renderer_name=${RENDERER_NAME}

# peashooter.use_stale_kv_cache=False
# peashooter.sampling_concurrency=2              # swang concurrent thread in one process
# peashooter.num_sampling_processes=16           # swang concurrent process in one worker
# # peashooter.tool_pool_config.num_tool_workers=64 # swang concurrent processes in tool pool
# peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
# peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
# peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
# peashooter.sampling_use_ev3=True
# peashooter.buffer_store_config_internal=beam_object_store
# peashooter.distributed_sampling_config=peashooter.distributed_sampling.distributed_sampling_config:DistributedSamplingConfig
# peashooter.distributed_sampling_config.load_balancing.max_kv_util=90.0

# eval_settings.eval_every=1
# # eval_settings.min_step=50
# # eval_settings.max_step=500
# eval_settings.exit_on_no_new_checkpoint=True
# eval_settings.eval_initial_policy=False

# metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector


# # telemetry qa data
# ':deep_swe_msft.telemetry_vsc.telemetry_qa.presets:eval_telemetry_qa'

# # # # sbv data new
# # ':deep_swe_eval_msft.swe_bench.peaval.padawan.presets:ev_sm_bench_juice_padawan(juice=768 override_target_samples_per_instance=1 max_num_yields=256)'


# # #policy.n_ctx=524288
# # #defaults.n_ctx=524288
# # policy.n_ctx=262144
# # defaults.n_ctx=262144
# # defaults.sampler.max_num_yields=256
# # # defaults.sampler.max_num_yields=100
# # defaults.sampler.harmony_constrained_sampling=True

# policy.n_ctx=262144
# defaults.n_ctx=262144
# ...max_num_yields=256
# # defaults.sampler.max_num_yields=256
# defaults.sampler.harmony_constrained_sampling=True
# defaults.channel_config.channels=analysis,final
# defaults.sampler.interleave_channels.0=analysis
# defaults.sampler.interleave_channels.1=commentary
# ...harmony_renderer_name=${RENDERER_NAME}
# ...encoding_name=orion_200k


# # msft special
# load.restore_from_all_clusters=False

# # Logging/misc
# security_profile=msft-orng
# github_upload=False
# wandb_enable=True
# wandb.wandb_project=swe-pevals
# kafka_enable=False
# )

# GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log




# WANDB_PROJECT_NAME=swe-pevals
# JUICE=256
# YIELDS=256
# MAX_TOKENS=131072

# EVAL_INITIAL_POLICY=True
# EVAL_EVERY=1

# dt=`date '+%Y%m%d-%H%M%S'`
# EXPERIMENT_NAME="vsc-telemetry-qa-ctx${MAX_TOKENS}-juice${JUICE}-yields${YIELDS}-$dt-evals-peaval"


# # INITIAL_CHECKPOINT="az://orngtransfer/ameinbound/snapshots/osmini-20250623-r16s110-safetys15-step-15-tc"
# # INITIAL_CHECKPOINT="az://orngscuscresco/twapi/mini/e/zhendongwang-gpt-oss-rl-rkld-32x32-mix1-0823-1555/policy/step_000050/"
# INITIAL_CHECKPOINT="az://orngscuscresco/twapi/mini/e/haoranxu-vsc-mix16-arm2-prerun-itc-768-juice-128-32x320820-0903/policy/step_000070/250821092713KTLR7IJU-0/"
# RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"

# CMD=(
# # oaipkg run qstar.run_eval nostrict
# beam python --start-daemon --use-cwd -m qstar.run_eval nostrict
# name=${EXPERIMENT_NAME}

# :berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
# # :berry_models.scallion_lpe:d36_80g_mbg16_bf16
# # :berry_models.osmini:d36_qatmoe_fp8kv
# policy.initial_checkpoint=${INITIAL_CHECKPOINT}
# policy.is_multimodal=True

# root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'

# peashooter.use_stale_kv_cache=False
# peashooter.sampling_concurrency=2
# peashooter.num_sampling_processes=16
# peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
# peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
# peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
# peashooter.sampling_use_ev3=True
# peashooter.buffer_store_config_internal=beam_object_store
# peashooter.distributed_sampling_config=peashooter.distributed_sampling.distributed_sampling_config:DistributedSamplingConfig
# peashooter.distributed_sampling_config.load_balancing.max_kv_util=90.0

# eval_settings.eval_every=${EVAL_EVERY}
# eval_settings.exit_on_no_new_checkpoint=True
# eval_settings.eval_initial_policy=${EVAL_INITIAL_POLICY}
# eval_settings.checkpoint_dir=${INITIAL_CHECKPOINT}

# # Inherit model params from training experiment!
# auto_inherit_training_args=False
# seed=47

# # telemetry qa data
# ':deep_swe_msft.telemetry_vsc.telemetry_qa.presets:eval_telemetry_qa'
# # ...variant_producer.override_reward_multiplier=${JUICE}

# # mswe mini data 
# # ':deep_swe_eval_msft.msweb.peaval.oai.presets:ev_sm_bench_mswe_mini_juice_oai_gpt5(juice=512 override_target_samples_per_instance=1 max_num_yields=512)'
# # ...variant_producer.override_reward_multiplier=${JUICE}

# # # avoid tool penalty errors
# # ...instance_objective.aux_objective_0=caas_autograding.tool_penalty_objective:ToolPenaltyObjective

# metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

# policy.n_ctx=${MAX_TOKENS}
# defaults.n_ctx=${MAX_TOKENS}
# ...max_num_yields=${YIELDS}
# defaults.sampler.harmony_constrained_sampling=True
# defaults.channel_config.channels=analysis,final
# defaults.sampler.interleave_channels.0=analysis
# defaults.sampler.interleave_channels.1=commentary
# ...harmony_renderer_name=${RENDERER_NAME}
# ...encoding_name=orion_200k

# # Set to True if you want to evaluate step0 (your prior)
# eval_settings.eval_initial_policy=${EVAL_INITIAL_POLICY}

# # msft special
# load.restore_from_all_clusters=False

# # Logging/misc
# security_profile=msft-orng
# github_upload=False
# wandb_enable=True
# wandb.wandb_project=${WANDB_PROJECT_NAME}
# kafka_enable=False
# enable_slackbot=False

# )
# GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval-ossmini-peaval.log
