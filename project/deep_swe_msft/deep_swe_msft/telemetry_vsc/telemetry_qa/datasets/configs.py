from functools import partial
from typing import Any

import caas_autograding.grader as caas_graders
import chz
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.datasets.configs import DeepSWEDatasetConfig
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig  # type: ignore
from deep_swe_msft.telemetry_vsc.telemetry_qa.datasets.caas_resource_config import CaasContainerResourceConfig
from deep_swe_msft.telemetry_vsc.telemetry_qa.datasets.setup import qa_setup_fn_coreutils
from deep_swe_msft.tools.caas_mix_tool_vsc import VSCMixToolConfig
from deep_swe_msft.vsc_data.system_prompt import VSC_MODEL_IDENTITY, VSC_SYSTEM_PROMPT
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from berry.grader import FunctionalGrader, Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override

from deep_swe_eval_msft.tools.caas_vscode_tool import EvalVSCodeToolConfig
from qstar.graders.taskgen_utils import TokenCompleterGraderService
from deep_swe_msft.vsc_graders.read_file_grader import RepeatReadingGrader
from deep_swe_msft.vsc_graders.tool_call_grader import ToolCallGrader
from deep_swe.graders.code_plan_patch_grader import CodePlanConsistencyGrader, CodePlanPatchGrader
from deep_swe_msft.vsc_graders.grader_picker import GRADER_PICKER

from deep_swe_msft.vsc_graders.cotograder_utils import (
    COTOGRADER_CHZ_ARGV,
    CotoGraderService,
    COTOGRADER_BUS_USER,
    COTOGRADER_BUS_TOPIC,
    COTOGRADER_BUS_LINE,
    COTOGRADER_RENDERER,
    COTOGRADER_QOS_TYPE,
)


# TODO(gross): use whatever TBv3 is using, don't copy code
RFS_LANG_TO_IMAGE_NAME = {
    "py": "aio",
    "js": "terminal-typescript",
    "java": "terminal-java",
    "go": "terminal-golang",
    "autogen": "acrbuiltincaasglobalame.azurecr.io/repos/autogen:313b",
}


@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig, IsOverride):
    caas_cpu_limit: float = 1.0  # Increase to 2 if actually using VSCode
    caas_memory_limit: int = 2
    caas_container_image: str = "vscode-agent"  # Has more pip dependencies!
    caas_idle_ttl: int = 10 * 60
    enable_network_after_setup: bool = False
    caas_endpoint: str = CAAS_ENDPOINT


# Copied from project/deep_swe/deep_swe/datasets/configs.py.
# with tool configs changed to vsc tool configs.
def _make_tool_configs(
    container_tool_config: VSCMixToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
    real_tool: str = "fake",
) -> tuple[ToolConfig, ...]:
    container_tool_config = chz.replace(container_tool_config, real_tool=real_tool)
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)

def _make_tool_eval_configs(
    container_tool_config: EvalVSCodeToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
    real_tool: str = "fake",
) -> tuple[ToolConfig, ...]:
    container_tool_config = chz.replace(container_tool_config, real_tool=real_tool)
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)


# IMPORTANT: must be paired with IFEnforcementGrader.
DEFAULT_ENFORCE_PROBABILITY = 0.25
def enforce_commands(
    probability: float = DEFAULT_ENFORCE_PROBABILITY,
    extra_good_commands: tuple[str, ...] = (),
    extra_bad_commands: tuple[str, ...] = (),
) -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.vsc_data.datapoint_converters:enforce_commands",
        kwargs={
            "probability": probability,
            "extra_good_commands": extra_good_commands,
            "extra_bad_commands": extra_bad_commands,
        },
    )

# Copied from project/deep_swe/deep_swe/datasets/configs.py
def make_multistage_grader(
    grader_argvs: list[list[str]] | None = None,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
) -> caas_graders.MultiStageGraderWithAuxReinforcements:
    # NB: we can freely set |channels_for_answer| here. See NOTE [ DeepSWEDatasetConfig.channel_format ]
    if grader_argvs is None:
        grader_argvs = []
    grader_argvs = list(grader_argvs)

    blueprint = chz.Blueprint(caas_graders.MultiStageGraderWithAuxReinforcements)
    for ii, grader_argv in enumerate(grader_argvs):
        argv: list[str] = []
        for arg in grader_argv:
            if not arg.startswith(("=", ".")):  # allow wildcards
                arg = "." + arg
            argv.append(f"graders.{ii}{arg}")
        blueprint = blueprint.apply_from_argv(argv)
    if channels_for_answer:
        channels_for_answer_arg = "channels_for_answer=" + ",".join(
            str(c) for c in channels_for_answer
        )
        blueprint = blueprint.apply_from_argv(["..." + channels_for_answer_arg])
    return blueprint.make()


# Copied from project/deep_swe/deep_swe/datasets/configs.py with updated grader path and bus line.
# let's not checking tool use for now, as we only care about the final answer
def make_telemetry_qa_multistage_grader(
    use_repeat_read_grader: bool = False,
    use_tool_grader: bool = False,
    use_answer_grader: bool = False,
    use_function_enforce_grader: bool = False,
    **kwargs,
) -> MultiStageGrader:
    grader_argvs = []
    
    # Conditionally add RepeatReadingGrader
    if use_repeat_read_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.read_file_grader:RepeatReadingGrader",
        ])
    
    # Main QA Grader
    grader_argvs.append(
        [
            "=deep_swe_msft.telemetry_vsc.telemetry_qa.graders.qa_grader:TelemetryQACriteriaRewardCotoGrader",
            *COTOGRADER_CHZ_ARGV,
            f"renderer_name={COTOGRADER_RENDERER}",
            f"grader_max_tokens={16384}",
            f"is_correct_threshold={0.35}",
            f"target_min_reward={0.01}",
        ]
    )
    if use_answer_grader:
        grader_argvs.append([
            "=deep_swe_msft.telemetry_vsc.telemetry_qa.graders.qa_grader:ToolberrySingleQAGrader",
            f"renderer_name={COTOGRADER_RENDERER}",
            "tool_penalty_scale=0.0",
            "check_channel_and_tool_use=False",
            "min_passing_grade=0.9",
            *COTOGRADER_CHZ_ARGV,
        ])
    
    # Conditionally add ToolCallGrader
    if use_tool_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.tool_call_grader:ToolCallGrader",
            f"topic={COTOGRADER_BUS_TOPIC}",
            f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            f"line={COTOGRADER_BUS_LINE}",
            f"renderer_name={COTOGRADER_RENDERER}",
            f"qos_type=QoSType.{COTOGRADER_QOS_TYPE}",
        ])
    
    return make_multistage_grader(
        grader_argvs,
        tuple([]),
    )  # channels_for_answer not used.

def conversation_converter(func_name: str, **kwargs: dict[str, Any]) -> FunctionWrapper:
    return FunctionWrapper(
        name="caas_converters:conversation_init_fn",
        kwargs={"func": func_name, **kwargs},
    )

# Telemetry QA Train datasets
@chz.chz
class RepoQATelemetryDatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngcresco")
    dataset_id: str = "data.datasets.swe.telemetry_nontestable.3k1_v1.train"
    # dataset_id: str = "data.datasets.swe.eval.telemetry_dataset.nontestable.2025_09_08_v4_800_samples_test"
    grader: Grader | FunctionalGrader = override(
        lambda: make_telemetry_qa_multistage_grader(**GRADER_PICKER)
    )
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(_make_tool_configs, enable_retrieval=False, real_tool="fake")
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=qa_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            conversation_converter(
                "deep_swe_msft.telemetry_vsc.telemetry_qa.datasets.conversation_init:conversation_init_fn"),
        )
    )
    model_identity_str: str = VSC_MODEL_IDENTITY
    instructions: str = VSC_SYSTEM_PROMPT


@chz.chz
class RepoQATelemetryEvalDatasetConfig(RepoQATelemetryDatasetConfig, IsOverride):
    dataset_id: str = "data.datasets.swe.telemetry_nontestable.3k1_v1.test"
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_eval_configs,
            enable_retrieval=False,
            real_tool="fake",
        )
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_telemetry_qa_multistage_grader,
            use_repeat_read_grader=False, 
            use_tool_grader=False, 
            use_answer_grader=False,
            use_function_enforce_grader=False,
        ),
    )
