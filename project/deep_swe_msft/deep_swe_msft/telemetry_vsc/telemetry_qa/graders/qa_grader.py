import chz
import json
import structlog
from abc import abstractmethod
from typing import Any, Sequence, final

from qstar.graders.taskgen_utils import TokenCompleterGraderService
from qstar.presets.chz_utils import IsOverride
from scientist_assistant_berry.graders.graders import ToolberryBusGraderService
from scientist_assistant_berry.graders.single_qa_grader import (
    ScientistAssistantRetrievalSingleQAGrader,
    get_final_channel_message, 
)

from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders import taskgen_utils
from qstar.graders.grader import Grader
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    CotogradeRequest,
    CotograderMixin,
    GradeFnOutput,
)
from caas_swe_bench_train_v2.cotograders import _JsonCotograder

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]


def _truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"


def _extract_model_actions_with_token_limit(convo, renderer=None, token_limit: int = 512,
                                            tool_truncation_rate: float = 0.5):
    """Extract model actions with token-based truncation"""
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            cur_token_limit = token_limit
        else:
            cur_token_limit = int(token_limit * tool_truncation_rate)

        content_str = str(message.content)

        # If renderer available, use token-based truncation
        if renderer:
            truncated_content = _truncate_string(renderer, content_str, token_limit=cur_token_limit)
        else:
            # Fallback to character-based truncation
            truncated_content = content_str[:cur_token_limit]

        convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {truncated_content}\n"

    return convo_messages


@chz.chz(typecheck=True)
class TelemetryQACriteriaRewardCotoGrader(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=32)
    reward_name: str = "test_patch_cotograder"
    is_correct_threshold: float = chz.field(default=0.35)
    reward_val_base: float = chz.field(default=0.5)
    target_min_reward: float = chz.field(default=0.01)

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        if (
            not sample.conversation
        ):  # This is only to satisfy type check. Empty response should have been filtered.
            raise ValueError("Sample conversation is empty which should not happen!")

        datapoint = sample.gt_datapoint
        final_channel_message = get_final_channel_message(sample)
        student_answer = (
            render_content(final_channel_message, 0) if final_channel_message else ""
        )
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(
            conversation, self.renderer, token_limit=256, tool_truncation_rate=0.2
        )

        criteria = "\n".join(
            [
                f"- Criteria{i}: {c}" for i, c in enumerate(datapoint.metadata["criteria"])
            ]
        )
        with open("/var/log/supervisor/qingruzhang_qa_grader.log", "a") as f:
            f.write(f"Rubic: {criteria}\n")

        instruction = TELEMETRY_QA_CRITERIA_GRADING_PROMPT.replace(
            "{question}", datapoint.problem
        ).replace(
            "{all_criteria}", criteria
        ).replace(
            "{conversation_messages}", conversation_messages
        ).replace(
            "{student_answer}", student_answer
        )
        
        with open("/var/log/supervisor/qingruzhang_qa_grader.log", "a") as f:
            f.write(
                f"Rubic: {criteria} \n Grader Prompt: {instruction}\n")

        return instruction

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        total_criteria = len(j)
        num_unsatisfied = sum(not criteria["satisfied"] for criteria in j.values())
        
        # Adjust base value so that failing all criteria gives similar penalty regardless of count
        # For example, target that failing all criteria gives reward ≈ 0.01
        target_min_reward = 0.01
        reward_val_base = 0.5
        adjusted_base = target_min_reward ** (1.0 / total_criteria) if total_criteria > 0 else reward_val_base
        
        return GradeFnOutput(
            correct=True, 
            extra_metadata={
                "reward": adjusted_base ** num_unsatisfied,
            }
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return grade["grade_fn_metadata"]["reward"] if grade["grade_fn_metadata"] else 0

    @classmethod
    def is_correct(cls, grade: dict[str, Any], is_correct_threshold=0.95) -> bool:
        return cls.reward(grade) > is_correct_threshold


# ScientistAssistantRetrievalSingleQAGrader: default mode is f1, computing the precision/recall of the student answer vs. the gold answer.
# See the detailed prompt in project/scientist_assistant_berry/scientist_assistant_berry/graders/single_qa_grader.py
# Copied from project/scientist_assistant_berry/scientist_assistant_berry/graders/graders.py
@chz.chz(typecheck=True)
class ToolberrySingleQAGrader(ScientistAssistantRetrievalSingleQAGrader, IsOverride):
    grader_service: TokenCompleterGraderService = chz.field(
        default_factory=ToolberryBusGraderService,
        blueprint_unspecified=ToolberryBusGraderService,
    )  # these values would be overridden by the COTOGRADER_CHZ_ARGV
    chatberry_reward: int = chz.field(default=256)
    renderer_name: str = chz.field(default="harmony_v4.0.15_berry_v3_128k_orion_text")
    use_toolberry: bool = chz.field(default=True)


TELEMETRY_QA_CRITERIA_GRADING_PROMPT = """
You are a **grader**. Your task is to evaluate whether a model’s solution address a problem and satisfies a set of criteria.

You are provided with:
1. **Model Final Solution** – this is the primary artifact to grade.
2. **Model Conversation History** – a truncated log of the model’s intermediate steps, provided only as context.

### Important Instructions
- **Focus on the Model Final Solution** when grading.  
- Use the conversation history only to clarify intent or resolve ambiguity.  
- **Ignore the system prompt** – it is not part of the model’s answer.  
- **Do not run external tools, CI pipelines, or code execution.** Base your evaluation solely on the provided text.  

---

## Problem Statement
The task the model was supposed to solve is as follows:

{question}

---

## Evaluation Criteria
Judge the model’s solution against the following criteria:

**Criteria:**  

{all_criteria}

---

§ Model Solution:

## Model Conversation History
```
{conversation_messages}
```


## Model Final Solution 

{student_answer}

---

Your final # Answer should be in JSON. You must list all criterias as the following examples. The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification.

**Example:**
{
   "criteria1": {
      "satisfied": true,
      "content": <criteria content>,
      "explanation": "The model only used apt and curl to install system dependencies and avoided pip and venv tools."
   },
   "criteria2": {
     "satisfied": false,
     "content": <criteria content>,
     "explanation": "The model summarize README in a wrong way."
   },
   ...
}
""".strip()

