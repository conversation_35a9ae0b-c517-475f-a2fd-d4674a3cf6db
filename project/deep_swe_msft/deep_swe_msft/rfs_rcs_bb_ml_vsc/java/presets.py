import berry.preset_utils
import deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.configs as py_configs
import deep_swe_msft.rfs_rcs_bb_ml_vsc.java.datasets.configs as configs
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe.datasets.config_utils import chz_path

BASELINE_DATASET_CONFIGS = [
    (py_configs.RFSPythonDatasetConfig, []),
    (py_configs.RCSPythonDatasetConfig, []),
    (py_configs.BugBountyPythonDatasetConfig, []),
]

PYTHON_JAVA_MIX_V1_TRAIN_DATASET_CONFIGS = [  #  RCS~8000 RFS~5000 BB~2150
    
    (py_configs.RFSPythonDatasetConfig, []), # 4093
    (py_configs.RCSPythonDatasetConfig, []), # 7562
    (py_configs.BugBountyPythonDatasetConfig, []), # 1930

    (configs.RFSJavaDatasetConfig, []), # 795
    (configs.RCSJavaDatasetConfig, []), # 856
    (configs.BugBountyJavaDatasetConfig, []), # 181
    
]

JAVA_DATASET_CONFIGS = [
    (configs.RFSJavaDatasetConfig, []), # 795
    (configs.RCSJavaDatasetConfig, []), # 856
    (configs.BugBountyJavaDatasetConfig, []), # 181
]

JAVA_DATASET_CONFIGS = [
    (configs.RRBJavaMergedDatasetConfig, []), # 795
]

DATASET_CONFIGS = BASELINE_DATASET_CONFIGS

format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)


train_baseline = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in BASELINE_DATASET_CONFIGS
    ],
    format,
)

train = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in JAVA_DATASET_CONFIGS
    ],
    format,
)


mix_v1_train = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in PYTHON_JAVA_MIX_V1_TRAIN_DATASET_CONFIGS
    ],
    format,
)