import dataclasses
import json
import time
from typing import Self

import structlog

import caas
import caas.terminal
import chz
from berry.utils import chz_stable_hash, json_stable_hash
from caas.api import caas_api
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from caas.commands import HttpGet, RawBashScript
from caas.protocol import NetworkMode, SandboxRuntime
from deep_swe_msft.tools.vscode_copilot_tool import VSC_MNT
from qstar.common import datapoint
from berry_caas_container_tool.caas_container_tool import (
    SetupFn,
    _caas_container_resource_name,
)
from qstar.common.tools import azure_sas_token
from berry_harmony.tools.berry_tool_interface import (
    ResourceConfig,
    ResourceContinuationError,
)


logger = structlog.get_logger(component=__name__)


@chz.chz
class CaasContainerResourceConfig(ResourceConfig):
    caas_cpu_limit: float = chz.field(default=1.0)
    caas_memory_limit: int = chz.field(default=1)
    caas_endpoint: str = chz.field(default="https://caas-prod.ace-research.openai.org")
    caas_blobstore_container_name: str = chz.field(default="oaistrawberryacecus")
    caas_container_image: str = chz.field(default="terminal")
    caas_idle_ttl: int = chz.field(default=1200)
    caas_network: NetworkMode | None = chz.field(default=None)
    use_terminal_server: bool = True
    setup_fn: SetupFn | None = chz.field(
        default=None,
        doc="You can pass a path to a function or a `chz` factory (e.g a class) that implements the SetupFn protocol.",
    )
    user: str = chz.field(default="root")
    enable_network_after_setup: bool | None = chz.field(
        default=False,
        doc=(
            "If True or False, network mode will be set to fully enabled/disabled after setup. "
            "If None, this will respect original network policy, e.g., set by `caas_network`."
        ),
    )
    caas_session_state: str | None = chz.field(default=None)
    caas_num_gpus: int | None = chz.field(default=None)
    caas_sandbox_runtime: SandboxRuntime = chz.field(default=SandboxRuntime.UNSAFE)
    caas_env: tuple[tuple[str, str], ...] | None = chz.field(default=None)
    caas_new_session_cmd: tuple[str, ...] | None = chz.field(default=None)

    @chz.validate
    def _validate_caas_network_enable_network_after_setup(self) -> None:
        if self.caas_network is not None and self.enable_network_after_setup is not None:
            raise ValueError(
                f"{self.caas_network=} but {self.enable_network_after_setup=} is not None, which will reset "
                "network policy after setup. Please only specify one of these args to be not None."
            )

    def name(self) -> str:
        return "caas_container"

    @chz.validate
    def _have_azure_sas_token(self) -> None:
        azure_sas_token.assert_sas_token_properly_configured(
            caas_blobstore_name=self.caas_blobstore_container_name
        )

    @chz.validate
    def _setup_fn_is_hashable(self) -> None:
        if self.setup_fn is not None:
            if chz.is_chz(self.setup_fn):
                chz_stable_hash(self.setup_fn)
            else:
                json_stable_hash(self.setup_fn)

    def with_serialized_state(self, state: bytes) -> Self:
        tmp_caas_container = CaasContainer.load_sync(state)
        return chz.replace(self, caas_session_state=tmp_caas_container.caas_session_state)

    async def _initialize_resource_from_state(
        self, dp: datapoint.HarmonyDatapoint, caas_session_state: str
    ) -> CaasContainer:
        del dp  # We are not using it right now.
        try:
            container = CaasContainer(
                caas_endpoint=self.caas_endpoint,
                image_name=self.caas_container_image,
                caas_session_state=caas_session_state,
                is_owner=False,
                user=self.user,
            )
            await container.send_heartbeat()
            return container
        except (
            ValueError,
            caas.NotFoundError,
            caas.ServerError,
            caas.TransportError,
            caas.ClientError,
        ) as e:
            raise ResourceContinuationError(
                resource_name=self.name(),
                resource_state=caas_session_state.encode(),
                message=(
                    f"Failed to initialize caas container '{self.name()}' from session state "
                    f"'{caas_session_state}': {e}"
                ),
            ) from e

    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        azure_sas_token.assert_sas_token_properly_configured(
            caas_blobstore_name=self.caas_blobstore_container_name
        )
        if self.caas_session_state is not None:
            # We are not the owner of the container, so we don't need to run the setup function.
            resource = await self._initialize_resource_from_state(dp, self.caas_session_state)
            assert not resource.is_owner
            return resource

        if self.use_terminal_server:
            cmd = ["/server.py"]
        else:
            cmd = []

        caas = caas_api(endpoint=self.caas_endpoint)
        caas_session = await caas.new_session(
            image=self.caas_container_image,
            cmd=list(self.caas_new_session_cmd) if self.caas_new_session_cmd is not None else cmd,
            cpu_limit=str(self.caas_cpu_limit),
            memory_limit=f"{self.caas_memory_limit}g",
            idle_ttl=self.caas_idle_ttl,
            num_gpus=self.caas_num_gpus,
            network=self.caas_network,
            timeout=1200,
            volume_mounts=VSC_MNT,
            disk_limit="32g",
        )
        terminal_session = TerminalSession(caas_session)

        if self.setup_fn is not None:
            await self.setup_fn(
                datapoint=dataclasses.asdict(dp),
                terminal_session=terminal_session,
            )
        if self.enable_network_after_setup is not None:
            # modify the network policy after because the setup may have required (temporary)
            # internet access
            # NB: this completely overwrites whatever `caas_network` policy that was used to create the
            #     container, no matter the flag is True or False. Hence we allow None (skipping this).
            await terminal_session.session.update_network(enable_network=self.enable_network_after_setup)
        caas_session_state = terminal_session.session.save()
        resource = CaasContainer(
            caas_endpoint=self.caas_endpoint,
            image_name=self.caas_container_image,
            caas_session_state=caas_session_state,
            user=self.user,
        )
        assert resource.is_owner
        return resource