import functools

import structlog

import chat
import chat.render
import chz
from berry.sample import Sam<PERSON><PERSON><PERSON><PERSON>ompletion
from caas_autograding._run_coro import run_coro
from caas_autograding.grader import maybe_get_caas_state
from caas_tool.caas_container import CaasContainer
from chat.render.common import render_content
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.misc import git_checkpoint_metadata
from diffs.v4.commit import Commit
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.common.tools.delegate import deliberate_tool
from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.graders.rfs_grader import _evaluate_mrfs_code
from qstar.graders.repo_graders_v3.utils import (
    CollectCommit,
    collect_commit,
    get_code_commit,
    render_patch_text_from_commit,
)
from qstar.sample_completers.simulated_user_sample_completer import SimulatedUser
from token_completer import TokenCompleter
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter

logger = structlog.stdlib.get_logger(component=__name__)


class UserMessageResponseType:
    CLARIFICATION = "clarification"
    FEEDBACK = "feedback"


_CANDIDATE_CHANNELS = {None, BerryChannel.FINAL_ANSWER.value, BerryChannel.DELEGATE_SUMMARY.value}


def _get_chatberry_system_message(juice: int) -> chat.Message:
    return chat.Message.system(
        instructions="By default, do not reply with Markdown formatting (e.g., **, ```) or LaTeX unless requested. You should carefully adhere to all formatting instructions.",
        model_identity_desc=None,
        channel_config=chat.SystemChannelConfig(valid_channels=("analysis", "final")),
        metadata=chat.SystemContentMetadata(reward_multiplier=juice),
    )


@functools.cache
def _get_token_completer(
    config: TokenCompleter.Config,
    renderer: str,
) -> BerryMultiMessageTurnCompleter:
    return BerryMultiMessageTurnCompleter(
        token_completer=config.build(),
        renderer=renderer,
        completion_params={"top_p": 0.985},
    )


# TODO: make this configurable!
async def grade_sample(
    sample: SampleWithCompletion[HarmonyCompletionDatapoint],
    collect_commit_fn: CollectCommit,
) -> str | None:
    caas_state = maybe_get_caas_state(sample)
    assert caas_state is not None, "caas state not found in sample"
    caas_container = CaasContainer.load_sync(caas_state)
    caas_endpoint = caas_container.caas_endpoint
    image_name = caas_container.image_name

    test_results = await _evaluate_mrfs_code(
        caas_endpoint=caas_endpoint,
        image_name=image_name,
        sample=sample,
        commit=collect_commit_fn(sample),
    )
    if test_results.passed or test_results.system_error:
        return None

    return (
        test_results.error
        or test_results.metadata.get("exec", {}).get("output", "")
        or test_results.metadata.get("build", {}).get("output", "")
        or "(Unknown error occurred while running tests)"
    )


def git_checkpoint_or_collect_convo(
    sample: SampleWithCompletion[HarmonyCompletionDatapoint],
) -> Commit:
    try:
        return git_checkpoint_metadata.collect_commit_using_git_checkpoint(sample)
    except git_checkpoint_metadata.NoGitCheckpointFoundError:
        return collect_commit(sample)


@chz.chz
class RFSGoldPatchSimulatedUserV2(SimulatedUser):
    # It's highly recommended to use a BusTokenCompleter.Config.
    token_completer: TokenCompleter.Config
    renderer_name: str

    collect_commit_fn: CollectCommit = chz.field(
        doc="Function to collect a commit from a sample.",
        default=git_checkpoint_or_collect_convo,
    )

    feedback_multiplier: float = chz.field(
        default=0.5,
        doc="Multiplier for feedback cost",
    )
    clarification_multiplier: float = chz.field(
        default=0.75,
        doc="Multiplier for clarification cost",
    )

    @property
    def token_message_completer(self) -> BerryMultiMessageTurnCompleter:
        return _get_token_completer(self.token_completer, self.renderer_name)

    def respond(
        self, sample: types.SampleWithCompletion[HarmonyCompletionDatapoint]
    ) -> chat.Message | None:
        datapoint = sample.gt_datapoint_serializable
        metadata = datapoint["metadata"]
        gold_patch = metadata.get("gold_patch") or metadata.get("canonical_solution")
        if not gold_patch:
            logger.warning("No gold patch found for sample", sample_id=sample.sample_id)
            return None

        # For metrics: make sure every sample has a user_penalty key.
        sample.metrics.setdefault("user_penalty", 0.0)
        commit = self.collect_commit_fn(sample)
        student_patch = render_patch_text_from_commit(get_code_commit(commit))
        if student_patch.count("\n") > 3:
            try:
                test_output = run_coro(grade_sample(sample, self.collect_commit_fn))
                if test_output is None:
                    return None
            except Exception:
                logger.warning(
                    "Error grading sample in simulated user",
                    sample_id=sample.sample_id,
                    exc_info=True,
                )
                return None

            conversation = chat.Conversation(
                messages=[
                    _get_chatberry_system_message(juice=256),
                    chat.Message.user(
                        self._provide_feedback_prompt(
                            sample,
                            gold_patch=gold_patch,
                            student_patch=student_patch,
                            test_output=test_output,
                        )
                    ),
                ]
            )
            message_type = UserMessageResponseType.FEEDBACK
        else:
            test_output = ""
            conversation = chat.Conversation(
                messages=[
                    _get_chatberry_system_message(juice=128),
                    chat.Message.user(self._answer_question_prompt(sample, gold_patch=gold_patch)),
                ]
            )
            message_type = UserMessageResponseType.CLARIFICATION

        completion = run_coro(self.token_message_completer.async_completion(conversation))
        message_text = render_content(completion.output_messages[-1]).strip()
        if message_text == "RTFM":
            sample.errors_blamed_on_model.add("bad_clarification")
            return None

        message_cot = "\n\n".join(
            [
                render_content(m)
                for m in completion.output_messages
                if m.channel == BerryChannel.CHAIN_OF_THOUGHT
            ]
        )

        # Add the message type
        sample.metadata.setdefault(message_type, []).append(
            {
                "message": message_text,
                "test_result": (
                    test_output if len(test_output) <= 5000 else "..." + test_output[-5000:]
                ),
            }
        )

        # Update the user penalty.
        # (Must be paired with a GraderWithAuxReinforcements to take effect.)
        sample.metrics["user_penalty"] = (
            (
                self.feedback_multiplier
                ** len(sample.metadata.get(UserMessageResponseType.FEEDBACK, []))
            )
            * (
                self.clarification_multiplier
                ** len(sample.metadata.get(UserMessageResponseType.CLARIFICATION, []))
            )
        ) - 1

        logger.info(
            "Continuing sample with simulated user response",
            sample_id=sample.sample_id,
            response=message_text,
            cot=message_cot,
            prompt=render_content(conversation.messages[-1]),
        )
        # Flip the response into a user message.
        return chat.Message.user(
            message_text, metadata={"simulated_user_message_type": message_type}
        )

    def intercept_tool(
        self, sample: SampleWithCompletion[HarmonyCompletionDatapoint]
    ) -> SampleWithCompletion[HarmonyCompletionDatapoint] | None:
        return None

    def _truncate_string(
        self,
        string: str,
        token_limit: int = 50000,
        truncate_behavior: str = "middle",
    ) -> str:
        renderer = self.token_message_completer.renderer
        toks = renderer.encode(string)
        if len(toks) < token_limit:
            return string

        if truncate_behavior == "middle":
            return (
                renderer.decode(toks[: token_limit // 2])
                + "...(truncated)..."
                + renderer.decode(toks[-token_limit // 2 :])
            )

        return "...(truncated)\n" + renderer.decode(toks[-token_limit:])

    def _provide_feedback_prompt(
        self,
        sample: SampleWithCompletion[HarmonyCompletionDatapoint],
        *,
        gold_patch: str,
        student_patch: str,
        test_output: str,
    ) -> str:
        prompt_messages = deliberate_tool.without_deliberate_prompt(
            sample.gt_datapoint.prompt.messages
        )
        problem = render_content(prompt_messages[-1])

        transcript_msgs = []
        transcript_msgs.append("INTERVIEWER:\n" + problem)

        assert sample.conversation, "Sample must have a conversation"
        for msg in sample.conversation.messages:
            if msg.author.role == chat.Role.USER:
                transcript_msgs.append("INTERVIEWER:\n" + render_content(msg))
            elif (
                msg.author.role == chat.Role.ASSISTANT
                and msg.recipient == "all"
                and msg.channel in _CANDIDATE_CHANNELS
                and (response := render_content(msg).strip())
            ):
                transcript_msgs.append("CANDIDATE:\n" + response)
        transcript = "\n---\n".join(transcript_msgs)

        return f"""You are an expert software engineer interviewing a software engineering candidate.
Here's a transcript of the interview so far:

<transcript>
{self._truncate_string(transcript, token_limit=20_000, truncate_behavior="middle")}
</transcript>

Here is the reference solution:

<reference_solution>
{self._truncate_string(gold_patch, token_limit=30_000)}
</reference_solution>

And here is their solution so far:

<candidate_solution>
{self._truncate_string(student_patch, token_limit=30_000)}
</candidate_solution>

Unfortunately, the candidate's solution did not pass the hidden test suite (not visible to the candidate):

<hidden_test_results>
{self._truncate_string(test_output, token_limit=10_000, truncate_behavior="end")}
</hidden_test_results>

Provide exactly one hint of constructive, actionable feedback in response to the candidate's last message.
The feedback should help the candidate towards the solution, but never reveal the reference solution or the hidden test results.

- Avoid unnecessary details or being overly strict on minor issues (like missing documentation, variable names, or function naming).
- Focus only on a meaningful functionality improvement if one is needed.
- You also don't need to worry about differences in test files.
- If the candidate happened to just choose a different function/class name than the reference solution, point this out.

Example feedback:
- Add a check to handle the case where the input is None.
- You need to apply gamma-correction and hue adjustments consistently across all files.
- Switch from recursion to an iterative approach to prevent stack overflow.
- You should make the function signature my_func(a: int, b: float) instead.

Respond with just your feedback, without any additional formatting or quotes."""

    def _answer_question_prompt(
        self,
        sample: SampleWithCompletion[HarmonyCompletionDatapoint],
        *,
        gold_patch: str,
    ) -> str:
        prompt_messages = deliberate_tool.without_deliberate_prompt(
            sample.gt_datapoint.prompt.messages
        )
        assert prompt_messages, "Prompt must have messages after removing deliberate prompt"
        last_message = prompt_messages[-1]
        problem = render_content(last_message)
        transcript_msgs = []
        transcript_msgs.append("INTERVIEWER:\nHere is your problem statement:\n" + problem)

        assert sample.conversation, "Sample must have a conversation"
        for msg in sample.conversation.messages:
            if msg.author.role == chat.Role.USER:
                transcript_msgs.append("INTERVIEWER:\n" + render_content(msg))
            elif (
                msg.author.role == chat.Role.ASSISTANT
                and msg.recipient == "all"
                and msg.channel in _CANDIDATE_CHANNELS
                and (response := render_content(msg).strip())
            ):
                transcript_msgs.append("CANDIDATE:\n" + response)
        transcript = "\n---\n".join(transcript_msgs)

        return f"""You are an expert software engineer interviewing a software engineering candidate.
Here's a transcript of the interview so far:

<transcript>
{transcript}
</transcript>

Please respond to the last message from the candidate, but do not reveal too much detail about the reference solution, or give away the key approach.
If the candidate is asking a question that can already be answered from the initial problem statement, just respond with "RTFM".
Respond with just your answer, without any additional formatting or quotes.

For context, here is the reference solution:

<reference_solution>
{self._truncate_string(gold_patch, token_limit=30_000)}
</reference_solution>"""
