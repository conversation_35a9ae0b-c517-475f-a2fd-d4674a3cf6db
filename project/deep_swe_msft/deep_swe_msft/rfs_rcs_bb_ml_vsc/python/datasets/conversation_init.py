import textwrap
from typing import Any, Literal, Sequence

import chat
import random
from caas_utils.conversation_init import (
    InstructionInsertionFormat,
    datapoint_rng,
    get_style_criteria_instructions,
)

from deep_swe_msft.vsc_data.datapoint_converters import RUN_TEST_HINT_PROB, PROMPT_IN_FILE_PROB 


def conversation_init_fn(
    *,
    datapoint: dict[str, Any],
    instruction: str = "",
    instruction_insertion_format: InstructionInsertionFormat = InstructionInsertionFormat.PREPEND_TO_USER_INSTRUCTION,
    language: str | None = None,
    apply_language_instruction_probability: float = 0.75,
    additional_language_instruction_dropout_probability: float = 0.25,
    add_contributor_criteria: (
        bool | list[str]
    ) = False,  # if True, will fetch from |CONTRIBUTOR_CRITERIA|
    extra_appended_instructions: tuple[str, ...] = (),
    custom_instruction_preamble: (
        str | None
    ) = None,  # if not set, will use default ones that instruct on issue solving
    task_header: Literal["Issue", "Task"] = "Issue",
) -> Sequence[chat.Message]:
    problem = datapoint["problem"]

    # <TODO> remove "bash run_tests.sh" statement in data, as VSC prefer to use run_tests tool.
    if random.random() < RUN_TEST_HINT_PROB:
        post_user_msg = f"Please make sure the updated repo can pass unit tests by using the `runTests` tool rather than directly running `bash run_tests.sh` or using pytest. Please do NOT modify the `run_tests.sh` file. Remember what you read and do NOT read overlapping lines. After solving the problem, please write a comprehensive trajectory of work explaining how the solution was reached. The trajectory must: (a) enumerate major steps in order; (b) for each step, state the goal, evidence/results, and the decision/rationale for the next action; (c) map steps to concrete changes (files/lines/commits) if applicable."
    else:
        post_user_msg = ""
    for ex_instruction in extra_appended_instructions:
        if not ex_instruction.startswith("- "):
            ex_instruction = "- " + ex_instruction.strip()
        instruction = instruction + "\n" + ex_instruction

    user_msg = f"""{instruction}
### {task_header}
{problem}
{post_user_msg}
"""
    
    if random.random() < PROMPT_IN_FILE_PROB:
        user_msg = f"""<attachments>
<attachment id="prompt:{datapoint["unique_id"]}.prompt.md">
Prompt instruction file:
```prompt
{user_msg}
```
</attachment>

<userRequest>
Follow instructions in [{datapoint["unique_id"]}.prompt.md](/root/code/{datapoint["unique_id"]}.prompt.md)
</userRequest>"""
    else:
        user_msg = f"""<userRequest>
{user_msg}
</userRequest>
"""

    user_msg = user_msg.replace("Please make sure the updated repo can pass unit tests by running `bash run_tests.sh`.", "")

    workspace_msg = f"""<workspace_info>
I am working in a workspace with the following folders:
- /root/code
I am working in a workspace that has the following structure:
```
{datapoint["metadata"]["project_structure"]}
```
This is the state of the context at this point in the conversation. The view of the workspace structure may be truncated. You can use tools to collect more context if needed.
</workspace_info>

<reminderInstructions>
You are an agent—keep going until the user's query is completely resolved before ending your turn. ONLY stop if solved or genuinely blocked.
Take action when possible; the user expects you to do useful work without unnecessary questions.
After any parallel, read-only context gathering, give a concise progress update and what's next.
Avoid repetition across turns: don't restate unchanged plans or sections (like the todo list) verbatim; provide delta updates or only the parts that changed.
Tool batches: You MUST preface each batch with a one-sentence why/what/outcome preamble.
Progress cadence: After 3 to 5 tool calls, or when you create/edit > ~3 files in a burst, pause and post a compact checkpoint.
Requirements coverage: Read the user's ask in full, extract each requirement into checklist items, and keep them visible. Do not omit a requirement. If something cannot be done with available tools, note why briefly and propose a viable alternative.
When using the insert_edit_into_file tool, avoid repeating existing code, instead use a line comment with \`...existing code...\` to represent regions of unchanged code.
Skip filler acknowledgements like "Sounds good" or "Okay, I will…". Open with a purposeful one-liner about what you're doing next.
When sharing setup or run steps, present terminal commands in fenced code blocks with the correct language tag. Keep commands copyable and on separate lines.
Avoid definitive claims about the build or runtime setup unless verified from the provided context (or quick tool checks). If uncertain, state what's known from attachments and proceed with minimal steps you can adapt later.
When you create or edit runnable code, run a test yourself to confirm it works; then share optional fenced commands for more advanced runs.
For non-trivial code generation, produce a complete, runnable solution: necessary source files, a tiny runner or test/benchmark harness, a minimal `README.md`, and updated dependency manifests (e.g., `package.json`, `requirements.txt`, `pyproject.toml`). Offer quick "try it" commands and optional platform-specific speed-ups when relevant.
Your goal is to act like a pair programmer: be friendly and helpful. If you can do more, do more. Be proactive with your solutions, think about what the user needs and what they want, and implement it proactively.
<importantReminders>
Before starting a task, review and follow the guidance in <responseModeHints>, <engineeringMindsetHints>, and <requirementsUnderstanding>. ALWAYS start your response with a brief task receipt and a concise high-level plan for how you will proceed.
DO NOT state your identity or model name unless the user explicitly asks you to. 
You MUST use the todo list tool to plan and track your progress. NEVER skip this step, and START with this step whenever the task is multi-step. This is essential for maintaining visibility and proper execution of large tasks. Follow the todoListToolInstructions strictly.
When referring to a filename or symbol in the user's workspace, wrap it in backticks.

</importantReminders>

</reminderInstructions>
"""
    
    return [chat.Message.user(workspace_msg), chat.Message.user(user_msg)]

