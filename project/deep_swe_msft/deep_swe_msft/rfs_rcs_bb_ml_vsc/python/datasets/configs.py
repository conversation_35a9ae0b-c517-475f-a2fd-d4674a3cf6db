from functools import partial
from typing import Any

import structlog

import caas_autograding.grader as caas_graders
import chz
from berry.function_wrapper import FunctionWrapper
from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.mrfs_setup import (
    mrfs_setup_fn_coreutils,
    mrfs_setup_fn_coreutils_rfs_0828_25,
    mrfs_setup_fn_coreutils_rcs_pp,
)
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe_msft.tools.caas_mix_tool_vsc import VSCMixToolConfig
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from deep_swe_msft.vsc_data.system_prompt import VSC_MODEL_IDENTITY, VSC_SYSTEM_PROMPT
from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.caas_resource_config import CaasContainerResourceConfig
from deep_swe.datasets.setup import setup_fn_coreutils
from deep_swe.graders.thoroughness_grader import Thor<PERSON>ness<PERSON>rader
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig  # type: ignore
from qstar.common.datapoint import HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from qstar.curriculums.variant_producer import (
    VarDiscountingVariantProducer,
    VariantProducer,
)
from berry.grader import FunctionalGrader, Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    TokenCompleterGraderService,
)
from qstar.presets.chz_utils import IsOverride, override
from deep_swe_msft.rfs_rcs_bb_ml_vsc.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK
from token_completer import TokenCompleter
from deep_swe_msft.vsc_graders.cotograder_utils import (
    COTOGRADER_BUS_USER,
    COTOGRADER_BUS_TOPIC,
    COTOGRADER_BUS_LINE,
    COTOGRADER_RENDERER,
    COTOGRADER_QOS_TYPE,
)

from deep_swe_msft.vsc_graders.grader_picker import GRADER_PICKER

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig, IsOverride):
    caas_cpu_limit: float = 4.0  # Increase to 2 if actually using VSCode
    caas_memory_limit: int = 8
    caas_container_image: str = "vscode-agent"  # Has more pip dependencies!
    caas_idle_ttl: int = 10 * 60
    enable_network_after_setup: bool = False
    caas_endpoint: str = CAAS_ENDPOINT


@chz.chz
class DeepSWEVardiscProducer(VarDiscountingVariantProducer, IsOverride):
    # Non-standard settings designed for the low data-efficiency, high compute-efficiency regime
    reward_distribution_power: float = 1.0
    min_reward_multiplier: int = 64
    max_reward_multiplier: int = 1024
    num_reward_multipliers: int = 1


def _make_tool_configs(
    container_tool_config: VSCMixToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
    real_tool: str = "fake",
) -> tuple[ToolConfig, ...]:
    container_tool_config = chz.replace(container_tool_config, real_tool=real_tool)
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)


@chz.chz
class DeepSWEDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs)

    # Defaults to an empty container (with oai coreutils installed).
    # Most tasks will probably want to override this with custom setup.
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(CaasResourceConfig(setup_fn=setup_fn_coreutils),)
    )

    max_num_yields: int = 400

    variant_producer: VariantProducer | None = override(DeepSWEVardiscProducer)


def conversation_converter(func_name: str, **kwargs: dict[str, Any]) -> FunctionWrapper:
    return FunctionWrapper(
        name="caas_converters:conversation_init_fn",
        kwargs={"func": func_name, **kwargs},
    )


# IMPORTANT: must be paired with IFEnforcementGrader.
DEFAULT_ENFORCE_PROBABILITY = 0.5
def enforce_commands(
    probability: float = DEFAULT_ENFORCE_PROBABILITY,
    extra_good_commands: tuple[str, ...] = (),
    extra_bad_commands: tuple[str, ...] = (),
) -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.vsc_data.datapoint_converters:enforce_commands",
        kwargs={
            "probability": probability,
            "extra_good_commands": extra_good_commands,
            "extra_bad_commands": extra_bad_commands,
        },
    )


def make_multistage_grader(
    grader_argvs: list[list[str]] | None = None,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_lupo_and_likertpp_graders: bool = False,
) -> caas_graders.MultiStageGraderWithAuxReinforcements:
    # NB: we can freely set |channels_for_answer| here. See NOTE [ DeepSWEDatasetConfig.channel_format ]
    if grader_argvs is None:
        grader_argvs = []
    grader_argvs = list(grader_argvs)
    blueprint = chz.Blueprint(caas_graders.MultiStageGraderWithAuxReinforcements)
    for ii, grader_argv in enumerate(grader_argvs):
        argv: list[str] = []
        for arg in grader_argv:
            if not arg.startswith(("=", ".")):  # allow wildcards
                arg = "." + arg
            argv.append(f"graders.{ii}{arg}")
        blueprint = blueprint.apply_from_argv(argv)
    if channels_for_answer:
        channels_for_answer_arg = "channels_for_answer=" + ",".join(
            str(c) for c in channels_for_answer
        )
        blueprint = blueprint.apply_from_argv(["..." + channels_for_answer_arg])
    return blueprint.make()


def make_rfs_multistage_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    enable_test_grader: bool = False,
    enable_thoroughness_grader: bool = False,
    use_lupo_and_likertpp_graders: bool = False,
    use_repeat_read_grader: bool = False,
    use_tool_grader: bool = False,
    use_function_enforce_grader: bool = False,
    data_version: str = "rrb",
    **kwargs: Any,
) -> MultiStageGrader:
    grader_argvs = []
    
    # Conditionally add RepeatReadingGrader
    if use_repeat_read_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.read_file_grader:RepeatReadingGrader",
        ])
    
    # Always add RFSGrader
    grader_argvs.append([
        "=deep_swe_msft.rfs_rcs_bb_ml_vsc.python.graders.rfs_grader:RFSGrader",
        f"caas_container_image={caas_container_image}",
        f"caas_endpoint={caas_endpoint}",
        f"data_version={data_version}",
    ])
    
    # Conditionally add ToolCallGrader
    if use_tool_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.tool_call_grader:ToolCallGrader",
            f"topic={COTOGRADER_BUS_TOPIC}",
            f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            f"line={COTOGRADER_BUS_LINE}",
            f"renderer_name={COTOGRADER_RENDERER}",
            f"qos_type=QoSType.{COTOGRADER_QOS_TYPE}",
        ])
    
    # Conditionally add FuncEnforcementGrader
    if use_function_enforce_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.func_enforcement_grader:FuncEnforcementGrader",
            "target_ratio=0.15",
        ])

    return make_multistage_grader(grader_argvs, channels_for_answer, use_lupo_and_likertpp_graders)

@chz.chz
class RFSPythonDatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngcresco")
    dataset_id: str = (
        "data.swang.swe.u20250423.rfs_7func.train_4093"
    )
    grader: Grader | FunctionalGrader = override(
        lambda: make_rfs_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
            **GRADER_PICKER
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            conversation_converter("deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.conversation_init:conversation_init_fn"),
            enforce_commands(
                probability=0.25,
            ),
        )
    )
    model_identity_str: str = VSC_MODEL_IDENTITY
    instructions: str = VSC_SYSTEM_PROMPT
    

@chz.chz
class RCSPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str =  "data.swang.swe.u20250423.rcs.train_7562"

@chz.chz
class BugBountyPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.swang.swe.u20250423.bb_hard.train_1930"


# ================ RRB Hard Datasets =================

@chz.chz
class RFSHardPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str =  "data.haoranxu.swe.data.rl.rfs_threshold_150_2136_0427"

@chz.chz
class RCSHardPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str =  "data.haoranxu.swe.data.rl.rcs_threshold_150_1399_0427"

@chz.chz
class BugBountyHardPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.haoranxu.swe.data.rl.bb_threshold_150_761_0427"

@chz.chz
class RRBPythonMergedDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.zhendongw.swe_train.data_mix_v1.rrb_py"


@chz.chz
class RFSPython0825VSCDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(partial(_make_tool_configs, real_tool="real"))
    dataset_id: str = "data.haoranxu.swe.swe_vsc.rfs_0827_2025.python"
    grader: Grader | FunctionalGrader = override(
        lambda: make_rfs_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
            data_version="rfs-0828-25",
            **GRADER_PICKER
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils_rfs_0828_25,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class RFSPythonRcsPpVSCDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(partial(_make_tool_configs, real_tool="real"))
    grader: Grader | FunctionalGrader = override(
        lambda: make_rfs_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
            data_version="rcs++",
            **GRADER_PICKER
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils_rcs_pp,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )