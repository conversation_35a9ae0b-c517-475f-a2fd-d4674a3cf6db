from __future__ import annotations

import dataclasses
from typing import Any
from typing import Mapping

import structlog

import chz
from berry.grader import GraderOutput
from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.mrfs_setup import mrfs_setup_run_test
from berry_rfs.utils import TestResult
from diffs.v4.commit import Commit
from qstar.common import datapoint, types
from qstar.graders.repo_graders_v3.rfs_grader_base import RFSGraderBase
from qstar.graders.repo_graders_v3.utils import (
    CollectCommit,
    get_code_commit,
    render_patch_text_from_commit,
    # collect_orig,
)
from berry_rfs.utils import read_jsonl, REPO_CACHE_DIR
from qstar.graders.repo_graders_v3.delegation_utils import collapse_convo
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from deep_swe_msft.rfs_rcs_bb_ml_vsc.utils.vsc_diff import consolidate_changes



logger = structlog.stdlib.get_logger(component=__name__)


async def _evaluate_mrfs_code_core(
    caas_endpoint: str,
    image_name: str,
    commit: Commit,
    repo_id_step1: str,
    repo_id_step2: str,
    gt_metadata: dict[str, Any],
    fail_if_cheating: bool = True,
    data_version: str = "rrb",
) -> TestResult:
    """Grades a sample by running it in terminal."""

    grade_user_test_changes = True
    # In the No Test variant, there's no test to run.
    # TODO: Reconsider Multilingual RCS + old test variant treatment here
    if "no_test/" in repo_id_step1 or "old_test/" in repo_id_step1:
        grade_user_test_changes = False

    # Step 1: Run all user code with all masked files
    if grade_user_test_changes:
        tr = await mrfs_setup_run_test(
            caas_endpoint,
            image_name,
            gt_metadata | {"repo_id": repo_id_step1},
            commit.get_changed_files(),
            data_version=data_version,
        )

        if not tr.passed:
            return tr
    else:
        tr = None

    # Step 2: Revert changes to protected files
    tr2 = await mrfs_setup_run_test(
        caas_endpoint,
        image_name,
        gt_metadata | {"repo_id": repo_id_step2},
        get_code_commit(commit).get_changed_files(),
        data_version=data_version,
    )

    if grade_user_test_changes:
        cheating = not tr2.passed and not tr2.system_error
        if fail_if_cheating:
            if cheating:
                tr2.error = "Cheating!"
                return tr2
            assert tr is not None
            return tr
        else:
            assert tr is not None
            tr.metadata["cheating"] = cheating
            return tr
    else:
        return tr2


def _get_repo_id(gt_datapoint: datapoint.HarmonyCompletionDatapoint) -> str:
    return gt_datapoint.metadata["repo_id"]


def _get_orig_repo_id(repo_id: str) -> str:
    return (
        repo_id.replace("_no_test/", "/")
        .replace("_private_test/", "/")
        .replace("_old_test/", "_new_test/")
    )


async def _evaluate_mrfs_code(
    caas_endpoint: str,
    image_name: str,
    sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    commit: Commit,
    fail_if_cheating: bool = True,
    data_version: str = "rrb",
) -> TestResult:
    """Grades a sample by running it on an ACE machine."""
    with open("/var/log/supervisor/yang_rf_grader.log", "a") as f:
        f.write(f"caas_endpoint={caas_endpoint} image_name={image_name} fail_if_cheating={fail_if_cheating} valid={sample.valid}\n")
        
    assert sample.valid

    gt_datapoint: datapoint.HarmonyCompletionDatapoint = sample.gt_datapoint
    gt_metadata: dict = gt_datapoint.metadata
    repo_id: str = _get_repo_id(gt_datapoint)
    return await _evaluate_mrfs_code_core(
        caas_endpoint,
        image_name,
        commit,
        repo_id,
        _get_orig_repo_id(repo_id),
        gt_metadata,
        fail_if_cheating=fail_if_cheating,
        data_version=data_version,
    )

def get_repo_task_data(repo_id: str, data_version: str = "rrb", gz_path: str = None) -> dict[str, Any]:
    # assert "/" in repo_id
    if gz_path is None:
        if data_version == "rfs-0828-25":
            gz_path = "az://orngscuscresco/data/haoranxu/swe/swe_vsc/rfs_0827_2025/python_repo_tasks/repo_tasks"
        elif data_version == "rrb":
            gz_path = "az://orngscuscresco/data/jialei/swe/upload20250303/python-1p-3p/repo_tasks"
        elif data_version == "rcs++":
            gz_path = "az://orngscuscresco/data/jialei/swe/upload20250303/python-1p-3p/repo_tasks"
        else:
            raise ValueError(f"Unknown data_version: {data_version}")
            
    path = f"{gz_path}/{repo_id}.gz"
    # path = sync_regionally(path)
    rows = read_jsonl(
        path,
        cache_dir=REPO_CACHE_DIR,
        verbose=False,
    )
    return rows[0]

def collect_orig(
    sample: types.SampleWithCompletion,
    data_version: str = "rrb",
) -> dict[str, str | None]:
    repo_id = _get_repo_id(sample.gt_datapoint)
    task_data = get_repo_task_data(repo_id, data_version=data_version)
    files = task_data.get("files", {})
    assert files
    masked_files = task_data.get("masked_files", {})
    orig = {k: v for k, v in (files | masked_files).items() if v is not None}
    return orig

def collect_commit_uncached(
    sample: types.SampleWithCompletion,
    orig: Mapping[str, str | None] | None = None,
    data_version: str = "rrb",
) -> Commit:
    convo = collapse_convo(sample)
    assert convo is not None
    if orig is None:
        orig = collect_orig(sample, data_version=data_version)

    # assert masked_files or task_data.get("commit"), f"Failed to find masked files or commit for {repo_id}, look in az://oaiwhalesong/strawberry-processing/reasoning/repo/data/{repo_id}.gz"
    return consolidate_changes(convo, orig)


def _vsc_collect_commit(
    sample: types.SampleWithCompletion,
    orig: Mapping[str, str | None] | None = None,
    data_version: str = "rrb",
) -> Commit:
    """Collects the commit from the sample.  Uses a cache.

    If `orig` is provided, it will be used as the original content of the files.
    In this case, no cache is used.
    """
    provided_orig = orig
    if orig is None:
        if commit := sample.ephemeral_metadata.get("commit"):
            return commit

    commit = collect_commit_uncached(sample, orig=orig, data_version=data_version)
    if provided_orig is None:
        sample.ephemeral_metadata["commit"] = commit
    return commit


@chz.chz(typecheck=True)
class RFSGrader(RFSGraderBase):
    caas_endpoint: str = chz.field(default=CAAS_ENDPOINT)
    caas_container_image: str = chz.field(default="aio")
    fail_if_cheating: bool = chz.field(
        doc="If True, the grader will fail the sample if cheating is detected.",
        default=True,
    )
    collect_commit_fn: CollectCommit = chz.field(
        doc="Function to collect a commit from a sample.",
        default=_vsc_collect_commit,
    )
    data_version: str = chz.field(
        doc="Data version to decide different setup",
        default="rrb",
    )

    async def _evaluate_code(
        self,
        sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    ) -> GraderOutput:
        try:
            def collect_commit_with_data_version(sample_arg, orig=None):
                return _vsc_collect_commit(sample_arg, orig=orig, data_version=self.data_version)

            commit = collect_commit_with_data_version(sample)
            if get_code_commit(commit).changes:
                tr = await _evaluate_mrfs_code(
                    self.caas_endpoint, self.caas_container_image, sample, commit, data_version=self.data_version
                )
            else:
                tr = TestResult(error="No changed code files")
        except Exception as e:
            logger.exception(
                "Unable to collect commit",
            )
            commit = None
            tr = TestResult(system_error=f"{type(e)}: {str(e)}")

        with open("/var/log/supervisor/haoran_rf_grader_results.log", "a") as f:
            f.write(f"{tr}\n")

        if tr.system_error:
            return GraderOutput.maybe_with_instance_average_reward_by_blaming_system(
                grader_nickname="rfs_grader",
                sample_id=sample.sample_id,
                errors=["rfs_grader_system_error"],
                given_answer=None,
                ephemeral_metadata=sample.ephemeral_metadata,
                metadata=sample.metadata
                | {
                    "grader": {
                        "prompt": "",
                        "response": f"System Error: {tr.system_error}",
                        "score": 0,
                        "rfs_grader": tr.model_dump(),
                    }
                },
            )

        assert commit is not None
        if self.fail_if_cheating:
            tr.passed = tr.passed and (not tr.metadata.get("cheating", False))

        response = tr.metadata.get("exec", {}).get("output", "")
        # if tr.metadata.get("cheating", False):
        #     response = "Cheating detected.\n\n" + response

        diff = render_patch_text_from_commit(get_code_commit(commit))
        return GraderOutput.with_binary_reward(
            grader_nickname="rfs_grader",
            sample_id=sample.sample_id,
            reward_name="rfs_grader",
            is_correct=tr.passed,
            metadata={
                "grader": {
                    "prompt": diff,
                    "response": response,
                    "score": 1 if tr.passed else 0,
                    "rfs_grader": tr.model_dump(),
                },
                "cheating": tr.metadata.get("cheating", False),
                "container_code_diff": diff,
            },
            # The collect_commit_fn may write to ephemeral metadata in place, and it needs to make
            # it into the GraderOutput. Typically it will just write to the key "commit", but the
            # function we are in does not have visibility into that, so we pass it all just in case.
            ephemeral_metadata=sample.ephemeral_metadata,
            metrics={
                "cheating": tr.metadata.get("cheating", False),
            },
        )