from functools import partial

import structlog
import chz
from deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets.mrfs_setup import (
    mrfs_setup_fn_coreutils,
    mrfs_setup_fn_coreutils_rcs_pp,
)
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig

from berry.grader import FunctionalGrader, Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from deep_swe_msft.vsc_graders.grader_picker import GRADER_PICKER


from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.configs import (
    RFSPythonDatasetConfig, 
    CaasResourceConfig,
    make_multistage_grader,
    _make_tool_configs,
)
from deep_swe_msft.rfs_rcs_bb_ml_vsc.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK

from deep_swe_msft.vsc_graders.cotograder_utils import (
    COTOGRADER_BUS_USER,
    COTOGRADER_BUS_TOPIC,
    COTOGRADER_BUS_LINE,
    COTOGRADER_RENDERER,
    COTOGRADER_QOS_TYPE,
)


logger = structlog.stdlib.get_logger(component=__name__)


def make_rfs_multistage_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    pass_condition: str | None = None,
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_cotograder: bool = False,
    use_sp_hard: bool = False,
    use_repeat_read_grader: bool = False,
    use_tool_grader: bool = False,
    use_function_enforce_grader: bool = False,
    data_version: str = "rrb",
    **kwargs,
) -> MultiStageGrader:
    if pass_condition:
        pass_condition_list = [f"pass_condition={pass_condition}"]
    else:
        pass_condition_list = []

    grader_argvs = []
    
    # Conditionally add RepeatReadingGrader
    if use_repeat_read_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.read_file_grader:RepeatReadingGrader",
        ])
    
    # Always add RFSGrader
    grader_argvs.append([
        "=deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.graders.rfs_grader:RFSGrader",
        f"caas_container_image={caas_container_image}",
        f"caas_endpoint={caas_endpoint}",
        f"collect_commit_fn=deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.graders.rfs_grader:_vsc_collect_commit",
        f"data_version={data_version}",
    ] + pass_condition_list)
    
    # Conditionally add ToolCallGrader
    if use_tool_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.tool_call_grader:ToolCallGrader",
            f"topic={COTOGRADER_BUS_TOPIC}",
            f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            f"line={COTOGRADER_BUS_LINE}",
            f"renderer_name={COTOGRADER_RENDERER}",
            f"qos_type=QoSType.{COTOGRADER_QOS_TYPE}",
        ])
    
    # Conditionally add FuncEnforcementGrader
    if use_function_enforce_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.func_enforcement_grader:FuncEnforcementGrader",
            "target_ratio=0.15",
        ])
    
    return make_multistage_grader(grader_argvs, channels_for_answer)


@chz.chz
class RFSCSharpDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.swang.swe.u20250423.csharp_rfs.train_2956_with_lang_fix_original_test_results"
    grader: Grader | FunctionalGrader = override(
        lambda: make_rfs_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["cs"],
            pass_condition="equal_to_or_better_than_original",
            **GRADER_PICKER
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["cs"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )


@chz.chz
class RFSCSharpRcsPPDatasetConfig(RFSCSharpDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(partial(_make_tool_configs, real_tool="fake"))
    grader: Grader | FunctionalGrader = override(
        lambda: make_rfs_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["cs"],
            pass_condition="equal_to_or_better_than_original",
            data_version="rcs++",
            **GRADER_PICKER,
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils_rcs_pp,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["cs"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class RFSCSharpFilteredNormalAndEasyDatasetConfig(RFSCSharpDatasetConfig, IsOverride):
    dataset_id: str = "data.weijianxu.csharp_rfs.train_634_filtered_normal_and_easy"

@chz.chz
class RCSCSharpFilteredNormalAndEasyDatasetConfig(RFSCSharpDatasetConfig, IsOverride):
    dataset_id: str = "data.weijianxu.csharp_rcs.rcs_wave3.default.rcs.train_101_filtered_normal_and_easy"

@chz.chz
class BugBountyCSharpFilteredNormalAndEasyDatasetConfig(RFSCSharpDatasetConfig, IsOverride):
    dataset_id: str = "data.weijianxu.csharp_bb.bugbounty.20250519.train_105_filtered_normal_and_easy"

@chz.chz
class RRBCSharpMergedDatasetConfig(RFSCSharpDatasetConfig, IsOverride):
    dataset_id: str = "data.zhendongw.swe_train.data_mix_v1.rrb_cs"