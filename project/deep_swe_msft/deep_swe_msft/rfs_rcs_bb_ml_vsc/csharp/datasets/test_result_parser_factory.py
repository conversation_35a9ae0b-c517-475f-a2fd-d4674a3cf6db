import logging
import re


def parse_python_test_results(test_result, test_type):
    patterns = {
        "pytest": {
            "passed": r"(\d+)\s+passed",
            "failed": r"(\d+)\s+failed",
            "time": r"in\s+([\d.]+)s",
        },
        "unittest": {
            "passed": r"Ran\s+(\d+)\s+tests?",
            "failed": r"(?:failures|errors)=(\d+)",
            "time": r"in\s+([\d.]+)s",
        },
        "nox": {
            "passed": r"(\d+)\s+passed",
            "failed": r"(\d+)\s+failed",
            "time": r"in\s+([\d.]+)s",
        },
        "tox": {
            "passed": r"(\d+)\s+passed",
            "failed": r"(\d+)\s+(?:failed|error)",
            "xfailed": r"(\d+)\s+xfailed",
            "time": r"in\s+([\d.]+)s|in\s+\d+\s+minutes?\s+([\d.]+)\s+seconds",
        },
    }

    results = {"passed": 0, "failed": 0, "time": 0.0}

    # General case
    if test_type in patterns:
        for key, pattern in patterns[test_type].items():
            matches = re.findall(pattern, test_result)
            for match in matches:
                try:
                    if isinstance(match, tuple):
                        # Handle multi-group (like tox time)
                        for val in match:
                            if val:
                                results[key] += float(val)
                                break
                    else:
                        val = float(match) if key == "time" else int(match)
                        results[key] += val
                except ValueError:
                    continue

        # Adjust passed count for unittest/nose/nose2
        if test_type in ["unittest", "nose", "nose2"]:
            results["passed"] -= results["failed"]
    return results


def parse_java_test_results(test_result, test_type=None):
    maven_pattern = (
        r"Tests run:\s*(\d+),\s*Failures:\s*(\d+),\s*Errors:\s*(\d+),\s*Skipped:\s*(\d+)"
    )
    matches = re.findall(maven_pattern, test_result)
    if matches:
        tests_run, failures, errors, skipped = map(int, matches[-1])
        time_pattern = r"Total time:\s*([\d.]+)\s*s"
        times = re.findall(time_pattern, test_result)
        total_time = float(times[-1]) if times else 0.0
        return {
            "passed": tests_run - (failures + errors + skipped),
            "failed": failures + errors,
            "time": total_time,
        }
    gradle_pattern = r"Total tests:\s*(\d+),\s*Failed:\s*(\d+),\s*Skipped:\s*(\d+)"
    gradle_match = re.search(gradle_pattern, test_result)
    if gradle_match:
        total_tests = int(gradle_match.group(1))
        failed = int(gradle_match.group(2))
        skipped = int(gradle_match.group(3))
        passed = total_tests - (failed + skipped)
        time_pattern = r"Total time:\s*([\d.]+)\s*(?:secs?|seconds)?"
        time_match = re.search(time_pattern, test_result)
        total_time = float(time_match.group(1)) if time_match else 0.0
        return {"passed": passed, "failed": failed, "time": total_time}
    return {"passed": 0, "failed": 0, "time": 0.0}


import re
from typing import Dict, Optional


def parse_javascript_test_results(
    test_result: str, test_type: Optional[str] = None
) -> Dict[str, float]:
    # Format with "Tests: ... failed, ... skipped, ... passed, ... total" and "Time: ..."
    comprehensive_pattern = (
        r"Tests:\s+"
        r"(?:(?P<failed>\d+)\s+failed,\s+)?"
        r"(?:(?P<skipped>\d+)\s+skipped,\s+)?"
        r"(?:(?P<passed>\d+)\s+passed,\s+)?"
        r"(?P<total>\d+)\s+total.*?"
        r"Time:\s+(?P<time>[\d.]+)\s+s"
    )

    match = re.search(comprehensive_pattern, test_result, re.DOTALL)
    if match:
        groups = match.groupdict()
        num_passed = int(groups.get("passed") or 0)
        num_failed = int(groups.get("failed") or 0)
        total_time = float(groups["time"])
        return {
            "passed": num_passed,
            "failed": num_failed,
            "time": total_time,
        }

    # Simple "176 passing (149ms)" format
    # 73 passing (2s)
    simple_pattern = r"(?P<passed>\d+)\s+passing\s+\((?P<time>[\d.]+)(?P<unit>ms|s)\)"
    match = re.search(simple_pattern, test_result)
    if match:
        passed = int(match.group("passed"))
        time = float(match.group("time"))
        unit = match.group("unit")
        total_time = time / 1000 if unit == "ms" else time
        return {
            "passed": passed,
            "failed": 0,
            "time": total_time,
        }


def parse_rust_test_results(test_result: str, test_type=None) -> dict:
    pattern = r"test result: (\w+)\. (\d+) passed; (\d+) failed; (\d+) ignored; (\d+) measured; (\d+) filtered out; finished in ([\d\.]+s)"
    matches = re.findall(pattern, test_result, re.DOTALL)
    if matches:
        matches = list(zip(*matches))
        num_passed = sum(int(x) for x in matches[1])
        num_failed = sum(int(x) for x in matches[2])
        try:
            exec_time = sum(float(x) for x in matches[6] if x)
        except:
            exec_time = 0.0
        total_time = float(exec_time) if exec_time else 0.0
        return {"passed": num_passed, "failed": num_failed, "time": total_time}
    return {"passed": 0, "failed": 0, "time": 0.0}


def parse_go_test_results(test_result: str, test_type=None) -> dict:
    passed_tests = len(re.findall(r"ok\s+\w+", test_result))
    failed_tests = len(re.findall(r"FAIL\t+", test_result))
    total_time_match = re.findall(r"(\t\d+\.\d+s)", test_result)
    if passed_tests or failed_tests:
        total_time = 0
        for item in total_time_match:
            total_time += float(item[1:-1])
        return {"passed": passed_tests, "failed": failed_tests, "time": total_time}

    return {"passed": 0, "failed": 0, "time": 0.0}


def parse_cpp_test_results(test_result, test_type=None):
    """
    Parse the output of CTest.
    """
    test_match = re.search(r"(\d+)% tests passed, (\d+) tests failed out of (\d+)", test_result)
    if test_match:
        failed_tests = int(test_match.group(2))
        total_tests = int(test_match.group(3))
        passed_tests = total_tests - failed_tests
        time_match = re.search(r"Total Test time \(real\) =\s+([\d.]+) (\w+)", test_result)
        if time_match:
            time_value = float(time_match.group(1))
            time_unit = time_match.group(2).lower()
            unit_conversion = {"ms": 0.001, "s": 1, "sec": 1, "min": 60, "h": 3600}
            if time_unit in unit_conversion:
                time_in_seconds = time_value * unit_conversion[time_unit]
            else:
                time_in_seconds = 0.0
        else:
            time_in_seconds = 0.0
        return {"passed": passed_tests, "failed": failed_tests, "time": time_in_seconds}
    else:
        # Support google test
        total_test_match = re.search(
            r"\[\s*==========\s*\] (\d+) tests from \d+ test suites ran\. \(([\d.]+) (\w+) total\)",
            test_result,
        )
        success_test_match = re.search(r"\[\s*PASSED\s*\] (\d+) tests\.", test_result)
        if total_test_match and success_test_match:
            total_tests = int(total_test_match.group(1))
            time_value = float(total_test_match.group(2))
            time_unit = total_test_match.group(3).lower()
            passed_tests = int(success_test_match.group(1))
            unit_conversion = {"ms": 0.001, "s": 1, "sec": 1, "min": 60, "h": 3600}
            if time_unit in unit_conversion:
                time_in_seconds = time_value * unit_conversion[time_unit]
            else:
                time_in_seconds = 0.0
            return {
                "passed": passed_tests,
                "failed": total_tests - passed_tests,
                "time": time_in_seconds,
            }
        else:
            return {"passed": 0, "failed": 0, "time": 0.0}


def parse_csharp_test_results(test_result: str, test_type=None) -> dict:
    total_passed = 0
    total_failed = 0
    total_skipped = 0
    total_time = 0.0

    # 1. "Passed!Failed - Failed:"
    pattern1 = re.compile(
        r"(Passed|Failed)!.*?Failed:\s*(\d+).*?Passed:\s*(\d+).*?Total:\s*\d+.*?Duration:\s*([<\d.]+)\s*(s|ms)?",
        re.DOTALL,
    )
    matches1 = pattern1.findall(test_result)
    if matches1:
        for match in matches1:
            failed, passed = int(match[1]), int(match[2])
            duration_str = match[3]
            duration_ms = 0.0

            if duration_str.startswith("<"):
                duration_ms = 0.0
            elif duration_str.replace(".", "", 1).isdigit():
                duration_ms = float(duration_str)

            if match[-1] == "ms":
                duration_ms /= 1000.0

            total_passed += passed
            total_failed += failed
            total_time += duration_ms
        return {"passed": total_passed, "failed": total_failed, "time": total_time}

    # 2.  "Total tests:"
    blocks = re.split(r"(?=Total tests:\s*\d+)", test_result)
    for block in blocks:
        if not block.strip():
            continue
        passed_match = re.search(r"Passed:\s*(\d+)", block)
        failed_match = re.search(r"Failed:\s*(\d+)", block)
        skipped_match = re.search(r"Skipped:\s*(\d+)", block)
        time_match = re.search(r"Total time:\s*([\d.]+)\s*Seconds", block)
        total_tests_match = re.search(r"Total tests:\s*(\d+)", block)

        passed = int(passed_match.group(1)) if passed_match else 0
        failed = int(failed_match.group(1)) if failed_match else 0
        skipped = int(skipped_match.group(1)) if skipped_match else 0
        time = float(time_match.group(1)) if time_match else 0.0

        if not failed_match and total_tests_match:
            total_tests = int(total_tests_match.group(1))
            failed = max(total_tests - passed - skipped, 0)

        total_passed += passed
        total_failed += failed
        total_skipped += skipped
        total_time += time

    if total_passed == 0 and total_failed == 0 and total_time == 0.0:
        return {"passed": 0, "failed": 0, "time": 0}
    return {"passed": total_passed, "failed": total_failed, "time": total_time}


def parse_ruby_test_results(test_result: str, test_type=None) -> dict:
    time_search = re.search(r"Finished in (\d+\.\d+) seconds", test_result)
    examples_search = re.search(r"(\d+) examples, (\d+) failures", test_result)
    if time_search and examples_search:
        total = int(examples_search.group(1))
        failed = int(examples_search.group(2))
        total_time = float(time_search.group(1))
        return {"passed": total - failed, "failed": failed, "time": total_time}
    return {"passed": 0, "failed": 0, "time": 0.0}


def parse_typescript_test_results(test_result: str, test_type=None) -> dict:
    pattern = r"Tests:.*?\s(\d+)\s+passed,\s+(\d+)\s+total.*?Time:\s+([\d.]+)\s*s"
    match = re.search(pattern, test_result, re.DOTALL)
    if match:
        passed_tests = int(match.group(1))
        total_tests = int(match.group(2))
        runtime = float(match.group(3))
        return {"passed": passed_tests, "failed": total_tests - passed_tests, "time": runtime}
    return {"passed": 0, "failed": 0, "time": 0.0}


def parse_php_test_results(test_result: str, test_type=None) -> dict:
    test_result_pattern = r"Tests: (\d+)?(?:, Assertions: (\d+))?(?:, Failures: (\d+))?(?:, Skipped: (\d+))?(?:, Errors: (\d+))?"
    matches = re.findall(test_result_pattern, test_result)

    def get_duration(test_result):
        duration_pattern = r"Time: ([\d:.]+)"
        time_str = re.search(duration_pattern, test_result).group()
        if not time_str:
            return 0.0
        time_list = [0, 0, 0, 0]
        time_splited = time_str.replace("Time: ", "").split(":")
        time_splited = ["0.0"] * (4 - len(time_splited)) + list(time_splited)
        for idx, t in enumerate(time_splited):
            time_list[idx] = float(t)
        duration = time_list[0] * 24 * 3600 + time_list[1] * 3600 + time_list[2] * 60 + time_list[3]
        return duration

    if matches:
        total = int(matches[0][0]) if matches[0][0] else 0
        failed = 0
        if matches[0][2]:
            failed = int(matches[0][2])
        elif matches[0][4]:
            failed = int(matches[0][4])
        success = total - failed
        duration = get_duration(test_result)
        return {"passed": success, "failed": failed, "time": duration}
    return {"passed": 0, "failed": 0, "time": 0.0}


def get_test_parser(language):
    parsers = {
        "python": parse_python_test_results,
        "java": parse_java_test_results,
        "javascript": parse_javascript_test_results,
        "rust": parse_rust_test_results,
        "go": parse_go_test_results,
        "cpp": parse_cpp_test_results,
        "csharp": parse_csharp_test_results,
        "ruby": parse_ruby_test_results,
        "php": parse_php_test_results,
        "typescript": parse_typescript_test_results,
    }
    return parsers.get(language.lower())
