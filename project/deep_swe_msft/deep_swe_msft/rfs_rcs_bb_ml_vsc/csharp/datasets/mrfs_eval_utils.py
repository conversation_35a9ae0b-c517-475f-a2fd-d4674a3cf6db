# Modified from torchflow-mirror at commit 772c4f2c9d05baa198a0ea127c23926d648adf5d
# Original path: /lib/berry_rfs/berry_rfs/mrfs_eval_utils.py
# Forked on: 2025-06-02
import asyncio
import json
import re
from typing import Any, Awaitable, Callable, Mapping

import structlog

from berry_rfs.mrfs_setup import patch_metadata, setup_repo_with_gt_metadata, try_run_command
from berry_rfs.utils import TestResult
from caas import ServerError
from caas.commands import UploadFile
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets.test_result_parser_factory import parse_csharp_test_results


DEFAULT_ATTEMPTS = 3
DEFAULT_TIMEOUT = 250


logger = structlog.stdlib.get_logger(component=__name__)


CONFIG_PATH_SUFFIXES = [
    ".config.js",
    ".config.ts",
    "package.json",
    "pom.xml",
    "build.gradle",
    "cargo.toml",
    "go.mod",
    "go.sum",
    "setup.cfg",
    "pyproject.toml",
    "conftest.py",
    "pytest.ini",
    "requirements.txt",
    "requirements-dev.txt",
    "tox.ini",
    ".eslintrc",
    ".eslintrc.json",
    ".eslintrc.js",
    ".prettierrc",
    ".prettierrc.json",
    ".prettierrc.js",
    "webpack.config.js",
    "webpack.config.ts",
    "rollup.config.js",
    "rollup.config.ts",
    "vite.config.js",
    "vite.config.ts",
    "settings.gradle",
    "CMakeLists.txt",
    "Gemfile",
    "Gemfile.lock",
    "rust-toolchain",
    "Dockerfile",
    ".dockerignore",
    ".gitignore",
    ".gitattributes",
    "Makefile",
    ".env",
]

LOWER_CASE_CONFIG_SUFFIXES = tuple([s.lower() for s in CONFIG_PATH_SUFFIXES])


def is_config_path(path: str) -> bool:
    # https://chatgpt.com/share/e/6798367c-eb30-8004-810f-56f369d3b209
    return (
        path.lower().endswith(LOWER_CASE_CONFIG_SUFFIXES)
        or "mocharc" in path.lower()
        or path == "setup.py"
    )


def is_test_path(path: str) -> bool:
    return (
        "test" in path.lower()
        or path.endswith((".spec.js", ".spec.ts"))
        or path.startswith("spec/")
    ) and not is_config_path(path)


def is_protected_path(path: str) -> bool:
    return is_test_path(path) or is_config_path(path)


def fails_tests_golang(text: str) -> bool:
    if "--- FAIL:" in text:
        return True
    if "SIGSEGV" in text:
        return True
    if "FAIL" in text:
        return True
    return False


def fails_tests_cpp(text: str) -> bool:
    if "tests failed out of" in text and " 0 tests failed out of" not in text:
        return True
    return False


def fails_tests_java(text: str) -> bool:
    if [s for s in text.split("\n") if "Tests run: " in s and "FAILURE!" in s]:
        return True
    return False


def fails_tests_js(text: str) -> bool:
    if ": not found" in text:
        return False
    if "no-unused-vars" in text:
        return False
    if "is declared but its value is never read." in text:
        return False
    if "has no initializer and is not definitely assigned in the constructor" in text:
        return False
    if [
        s
        for s in text.split("\n")
        if " failed" in s
        and " 0 failed" not in s
        or " failing" in s
        and " 0 failing" not in s
        or " failure" in s
        and " 0 failures" not in s
    ]:
        return True
    return False


def fails_tests_rust(text: str) -> bool:
    return "test result: FAILED" in text or "error: test failed" in text


def fails_tests_swift(text: str) -> bool:
    if [s for s in text.split("\n") if "Test Suite" in s and "failed at" in s]:
        return True
    return False


def fails_tests_py(text: str) -> bool:
    lines = [s for s in text.split("\n") if s]
    if lines:
        last_line = lines[-1]
        if " failed" in last_line or " error" in last_line:
            return True
    return False


def fails_tests(lang: str, text: str) -> bool:
    if lang == "go":
        return fails_tests_golang(text)
    if lang == "cpp":
        return fails_tests_cpp(text)
    if lang == "java":
        return fails_tests_java(text)
    if lang == "js":
        return fails_tests_js(text)
    if lang == "rust":
        return fails_tests_rust(text)
    if lang == "swift":
        return fails_tests_swift(text)
    if lang == "python":
        return fails_tests_py(text)
    return False



def passes_tests(
    lang: str,
    text: str,
    allow_unknown_lang: bool = False,
    test_results: dict = {},
    original_test_results: dict = {},
    pass_condition: str | None = None,
) -> bool:
    if lang == "python":
        lines = [s for s in text.split("\n") if s]
        if lines:
            last_line = lines[-1]
            if " passed" in last_line and " failed" not in last_line:
                return True
        return False
    if lang == "go":
        return "--- PASS: " in text and "--- FAIL: " not in text
    if lang == "cpp":
        return "100% tests passed" in text
    if lang == "js":
        if "SUCCESS" in [s for s in text.split("\n") if s.strip()][-1]:
            return True
        keywords = ["passing", "passed", "tests complete", " 0 failures"]
        if [kw for kw in keywords if kw in text]:
            return True
        return False
    if lang == "java":
        raise NotImplementedError
    if lang == "csharp":
        results = parse_csharp_test_results(text)
        if pass_condition == "equal_to_or_better_than_original":
            if (
                original_test_results
                and results["passed"] >= original_test_results["passed"]
                and results["failed"] <= original_test_results["failed"]
            ):
                return True
            return False
        elif pass_condition == "no_failures":
            if results["failed"] == 0:
                return True
            return False
        else:
            raise ValueError(f"Invalid pass condition: {pass_condition}")
    if lang == "ruby":
        if "examples, 0 failures" in text and "0 examples, 0 failures" not in text:
            return True
        return False
    if lang == "rust":
        if "test result: FAILED" in text:
            return False
        started = len([s for s in text.split("\n") if s.strip().startswith("running ")])
        ended = len([s for s in text.split("\n") if s.strip().startswith("test result:")])
        if started > ended:
            return False
        if [s for s in text.split("\n") if "test result: ok" in s and " 0 passed" not in s]:
            return True
        return False
    if lang == "swift":
        if [s for s in text.split("\n") if "Test Suite" in s and "failed at" in s]:
            return False
        if [s for s in text.split("\n") if "Test Suite" in s and "passed at" in s]:
            return True
        return False
    return True  # FIXME (weijianxu): Perhaps the default behavior should be False or raise an error


async def upload_files(
    terminal_session: TerminalSession,
    changed_files: Mapping[str, str | bytes | None],
    repo_root: str,
) -> tuple[bool, dict[str, Any]]:
    try:
        for task_path, task_code in changed_files.items():
            if task_code is None:
                ok, d = await try_run_command(
                    terminal_session, f'rm -f "{repo_root}/{task_path}"', 10
                )
                if not ok:
                    return False, {
                        "path": task_path,
                        "error": "cannot delete",
                        "error_message": d,
                    }
            else:
                await terminal_session.session.run(
                    UploadFile(
                        path=f"{repo_root}/{task_path}",
                        data=(task_code.encode() if isinstance(task_code, str) else task_code),
                    )
                )
    except Exception as e:
        return False, {
            "error": "Exception",
            "error_message": str(e),
        }

    return True, {
        "file_count": len(changed_files),
    }


async def mrfs_run_test(
    terminal_session: TerminalSession,
    gt_metadata: dict[str, Any],
    changed_files: dict[str, str | None],
    repo_root: str,
    build_seconds: int = DEFAULT_TIMEOUT,
    exec_seconds: int = DEFAULT_TIMEOUT,
) -> dict[str, Any]:
    repo_id = gt_metadata["repo_id"]
    build_cmd = gt_metadata.get("build_cmd", "")
    exec_cmd = gt_metadata.get("exec_cmd", "")
    result = {
        "repo_id": repo_id,
        "build_cmd": build_cmd,
        "exec_cmd": exec_cmd,
        "exec_done": False,
    }

    ok, d = await upload_files(terminal_session, changed_files, repo_root)
    result["upload"] = d
    if not ok:
        return result

    if build_cmd:
        ok, d = await try_run_command(
            terminal_session, f"source ~/.bashrc; cd {repo_root}; {build_cmd}", build_seconds
        )
        result["build"] = d
        if not ok:
            return result

    assert exec_cmd
    ok, d = await try_run_command(
        terminal_session, f"source ~/.bashrc; cd {repo_root}; {exec_cmd}", exec_seconds
    )
    result["exec"] = d
    if not ok:
        return result

    result["exec_done"] = True
    return result


async def mrfs_run_test_with_test_result(
    terminal_session: TerminalSession,
    gt_metadata: dict[str, Any],
    changed_files: dict[str, str | None],
    repo_root: str,
    pass_condition: str | None = None,
) -> dict[str, Any]:
    result = await mrfs_run_test(
        terminal_session,
        gt_metadata,
        changed_files,
        repo_root,
    )

    lang = gt_metadata.get("lang", "python")
    original_test_results = gt_metadata.get("original_test_results", {})

    if result["exec"]["output"] and passes_tests(
        lang,
        result["exec"]["output"],
        original_test_results=original_test_results,
        pass_condition=pass_condition,
    ):
        passed = True
    else:
        passed = False

    with open("/var/log/supervisor/weijian_test_results.log", "a") as f:
        if lang == "csharp":
            parsed_result = parse_csharp_test_results(result["exec"]["output"])
        else:
            parsed_result = {}
        to_write = {
            "gt_metadata": gt_metadata,
            "repo_root": repo_root,
            "repo_id": gt_metadata["repo_id"],
            "result": result,
            "parsed_result": parsed_result,
            "original_test_results": original_test_results,
            "lang": lang,
            "passed": passed,
        }
        f.write(json.dumps(to_write) + "\n")

    if passed:
        return TestResult(passed=True, metadata=result)
    else:
        return TestResult(metadata=result)
