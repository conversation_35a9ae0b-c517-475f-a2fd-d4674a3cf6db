import asyncio
import base64
import os
import re
import time
from textwrap import dedent
from typing import Any, Awaitable, Callable, Mapping
import json
import structlog

from caas import ExecError, TimedOutError, ServerError
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas.commands import UploadFile
from caas.api import caas_api
from deep_swe_msft.tools.vscode_copilot_tool import setup_vscutils
from deep_swe_msft.tools.utils import get_strawberry_ace_token
from deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets.mrfs_eval_utils import mrfs_run_test_with_test_result
# from berry_rfs.mrfs_eval_utils import mrfs_run_test_with_test_result
from berry_rfs.utils import TestResult
from caas_rfs.setup import get_repo_name

from mini.utils.simple_timer import simple_timer
from mini.metrics import metrics

DEFAULT_ATTEMPTS = 10
DEFAULT_TIMEOUT = 3600
MAX_OUTPUT_SIZE = 1_000_000
DEFAULT_SUPPORT_ROOT = "/tmp/repo_support"

HTTP_FILE_PATH = {
    "rrb": "https://orngcaas.blob.core.windows.net/data/swe-model-training-data/mingao/wave1_1/merged_upload_repos",
    "rcs++": "https://orngcaas.blob.core.windows.net/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/python/train/rfs/3p/upload_repos_enable_vscode_test", 
}

logger = structlog.stdlib.get_logger(component=__name__)


async def mrfs_setup_fn_coreutils(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
    data_version: str = "rrb",
) -> None:
    """
    Thin wrapper over regular RFS setup that installs VSC coreutils
    """
    repo_root = '/root/code'
    await setup_repo_with_gt_metadata(terminal_session, datapoint["metadata"], data_version=data_version)
    await setup_vscutils(
        datapoint=None,
        session=terminal_session.session,
        workdir=repo_root,
        language='c#',
    )


async def mrfs_setup_fn_coreutils_rcs_pp(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    """Variant pinned to data_version="rcs++".

    Exposed so configs can target the new dataset version without
    overriding arguments manually elsewhere.
    """
    await mrfs_setup_fn_coreutils(
        datapoint=datapoint,
        terminal_session=terminal_session,
        data_version="rcs++",
    )

def clean_up_repo_name(repo_name: str) -> str:
    repo_name = repo_name.split("/")[-1]
    repo_name = re.sub(r"[^a-zA-Z0-9]", "", repo_name)
    return repo_name


def get_repo_directory(repo_name: str) -> str:
    return f"/root/{repo_name}"


def truncate_text(text: str) -> str:
    if len(text) <= MAX_OUTPUT_SIZE:
        return text
    return text[: MAX_OUTPUT_SIZE // 2] + " [TRUNCATED] " + text[-MAX_OUTPUT_SIZE // 2 :]


async def try_run_command(
    terminal_session: TerminalSession,
    command: str,
    seconds: int = 120,
    attempts: int = 1,
) -> tuple[bool, dict]:
    for attempt in range(attempts):
        start_time = time.time()
        try:
            result = await terminal_session.session.run(BashScript(command, timeout=seconds))
            text = result.decode("utf-8")
            return True, {
                "command": command,
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except TimedOutError as e:
            text = e.output.decode("utf-8")
            return False, {
                "command": command,
                "error": "TimedOutError",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except ExecError as e:
            if attempt + 1 < attempts:
                await asyncio.sleep(attempt * 5 + 1)
                continue
            text = e.output.decode("utf-8")
            return False, {
                "command": command,
                "error": "ExecError",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except Exception as e:
            text = ""
            return False, {
                "command": command,
                "error": "Exception",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }


def setup_bashrc(repo_root: str, PATHS: list[str]) -> str:
    paths = ":".join(PATHS)
    SETUP_BASH = f"""
cat <<EOF >> /root/.bashrc
export PATH=$PATH:{paths}
export PYTHONPATH={repo_root}:{repo_root}/src
export PY_COLORS=0
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
cat <<EOF >> /root/.bash_profile
export PATH=$PATH:{paths}
export PYTHONPATH={repo_root}:{repo_root}/src
export PY_COLORS=0
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
"""
    return SETUP_BASH


def setup_csharp_package_index_cache(repo_root: str) -> str:
    setup_package_index_cache_cmd = dedent(
        rf"""
        echo "CAAS_ARTIFACTORY_NUGET_REGISTRY: $CAAS_ARTIFACTORY_NUGET_REGISTRY" && \
        mkdir -p {repo_root} && \
        cat > {repo_root}/nuget.config << EOF
        <?xml version="1.0" encoding="utf-8"?>
        <configuration>
            <packageSources>
            <clear />
            <add key="caas_local_packages" value="$CAAS_ARTIFACTORY_NUGET_REGISTRY" />
            </packageSources>
        </configuration>
        EOF
        """
    )
    return setup_package_index_cache_cmd


def setup_csharp_bashrc(repo_root: str, PATHS: list[str]) -> str:
    paths = ":".join(PATHS)
    SETUP_BASH = f"""
cat <<EOF >> /root/.bashrc
export PS2=''
cd {repo_root}
EOF
cat <<EOF >> /root/.bash_profile
export PATH=$PATH:{paths}
export PS2=''
cd {repo_root}
EOF
"""
    return SETUP_BASH

dump_run_tests_command = f"""
cat <<EOF >> /root/autogen/run_tests.sh
#!/bin/bash
set -e
ulimit -v unlimited
export OPENBLAS_NUM_THREADS=1
cd /root/autogen/python
source .venv/bin/activate
cd /root/autogen
pushd /root/autogen > /dev/null
cd python
source ".venv/bin/activate"
mkdir -p test_reports
export OPENAI_API_KEY=abc
poe test
popd > /dev/null
EOF
"""

async def _setup_direct_repo_files(
    terminal_session: TerminalSession,
    repo_root: str,
    repo_id: str,
    direct_repo_files: dict[str, str],
) -> tuple[dict[str, Any], bool]:
    """Returns `(file_contents, success)`.

    Args:
        terminal_session: TerminalSession
        repo_root: str
        repo_id: str
        direct_repo_files: dict[str, str].  Keys are filenames,
          values are file contents.

    Returns:
        Tuple of `(file_statuses, success)`.  `file_statuses` is a dict of
        status information for each uploaded file.  `success` is a boolean indicating
        whether the operation was successful.
    """
    result = {}
    ok, d = await try_run_command(
        terminal_session,
        f"rm -rf {repo_root}; mkdir -p {repo_root}",
        attempts=DEFAULT_ATTEMPTS,
    )
    result["copy_repo_init"] = d
    if not ok:
        return result
    coros = [
        try_run_command(
            terminal_session,
            (
                f"mkdir -p '{repo_root}/{os.path.dirname(filename)}'; "
                f"echo '{base64.b64encode(content.encode()).decode()}' | base64 -d > {repo_root}/{filename}"
            ),
            attempts=DEFAULT_ATTEMPTS,
        )
        for filename, content in direct_repo_files.items()
    ]
    results = await asyncio.gather(*coros)
    for filename, (ok, d) in zip(direct_repo_files, results):
        result[f"copy_test_file_{filename}"] = d
        if not ok:
            return result, False

    return result, True


async def setup_repo(
    terminal_session: TerminalSession,
    repo_root: str,
    repo_id: str,
    lang: str,
    package_cache_id: str,
    extract_cache_cmd: str,
    install_packages_cmd: str,
    install_seconds: int = DEFAULT_TIMEOUT,
    support_root: str = DEFAULT_SUPPORT_ROOT,
    direct_repo_files: dict[str, str] | None = None,
    files_to_be_edited: dict[str, str] = {},
    data_version: str = "rrb",
) -> dict[str, Any]:
    """Setup a repo for testing.

    Args:
        terminal_session: The terminal session to use.
        repo_root: The root directory for the repo.  It must start with / and not end in /.
        repo_id: The repo ID.
        package_cache_id: The package cache ID.  If empty, no package cache is uploaded.
        extract_cache_cmd: The command to extract the cache. Must not be empty if package_cache_id is not empty.
        install_packages_cmd: The command to install packages.  Must be empty if package_cache_id is not empty.
        install_seconds: int
        support_root: The support root directory.  Its path is added to the PATH.
        direct_repo_files: Only used for testing.  If not None, it is a dict
          of filenames and contents to upload to the repo.

    Returns:
        A dict of status information for each step of the setup process.
    """
    assert repo_root.startswith("/") and not repo_root.endswith("/"), (
        f"Invalid repo_root; must start with / and not end in /: {repo_root}"
    )

    result = {
        "setup_done": False,
        "repo_id": repo_id,
        "package_cache_id": package_cache_id,
        "extract_cache_cmd": extract_cache_cmd,
        "install_packages_cmd": install_packages_cmd,
    }
    with open("/var/log/supervisor/yang_repo_setup.log", "a") as f:
        to_write = {'repo_root': repo_root, 'repo_id': repo_id, 'package_cache_id': package_cache_id, 'extract_cache_cmd': extract_cache_cmd, 'install_packages_cmd': install_packages_cmd}
        f.write(json.dumps(to_write)+"\n")

    http_file_path = HTTP_FILE_PATH[data_version]
    assert "autogen" not in repo_root
    with simple_timer("download_task_repo", log_fn=lambda x: [metrics.mean(k, v) for k, v in x.items()]):
        ok, d = await try_run_command(
            terminal_session,
            f'rm -rf {repo_root}; mkdir -p {repo_root}; curl -k -sL "{http_file_path}/{repo_id}.tar.gz?{get_strawberry_ace_token()}" | tar -xz -C {repo_root}',
            seconds=install_seconds,
            attempts=DEFAULT_ATTEMPTS,
        )
    result["copy_repo"] = d

    if not ok:
        metrics.sum("download_task_repo_failed", 1.0)
        return result

    # setup package index cache
    if lang == "csharp":
        setup_package_index_cache_cmd = setup_csharp_package_index_cache(repo_root)
        ok, d = await try_run_command(terminal_session, setup_package_index_cache_cmd)
        result["setup_package_index_cache"] = d

    # setup packages
    if install_packages_cmd:
        with simple_timer("setup_task_repo", log_fn=lambda x: [metrics.mean(k,v) for k,v in x.items()]):
            ok, d = await try_run_command(
                terminal_session, f"cd {repo_root}; {install_packages_cmd}", install_seconds,
                attempts=DEFAULT_ATTEMPTS,
            )
        result["install_packages"] = d
        if not ok:
            metrics.sum("setup_task_repo_failed", 1.0)
            return result

    if lang == "csharp":
        setup_bashrc_cmd = setup_csharp_bashrc(
            repo_root, [support_root, repo_root, f"{repo_root}/csharp"]
        )
        # FIXME (weijianxu): Seems f"{repo_root}/csharp" is not useful here as PATH
    else:
        setup_bashrc_cmd = setup_bashrc(repo_root, [support_root, repo_root, f"{repo_root}/python"])

    ok, d = await try_run_command(terminal_session, setup_bashrc_cmd)
    result["setup_bashrc"] = d
    if not ok:
        return result

    # give model permission to access the repo
    ok, d = await try_run_command(terminal_session, f"chmod -R +rwx {repo_root}")

    result["setup_done"] = True
    metrics.sum("setup_task_repo_done", 1.0)
    return result


def patch_metadata(metadata: dict[str, Any]) -> dict[str, Any]:
    exec_cmd = metadata.get("exec_cmd", "")
    if not exec_cmd:
        assert not metadata.get(
            "lang"
        ), f"lang should be empty if exec_cmd is empty; but got {metadata.get('lang')}; metadata: {metadata}"
        exec_cmd = "pytest"

        extra_setup = metadata.get("extra_setup", "")
        install_packages_cmd = ""
        if extra_setup:
            install_packages_cmd = extra_setup.replace("!", "")

        return {k: v for k, v in metadata.items() if k != "extra_setup"} | {
            "lang": "python",
            "extract_cache_cmd": "",
            "install_packages_cmd": install_packages_cmd,
            "build_cmd": "",
            "exec_cmd": exec_cmd,
        }

    setup_cmd = metadata.get("setup_cmd", "")
    if not setup_cmd:
        return metadata

    extract_cache_cmd = ""
    install_packages_cmd = ""
    build_cmd = ""

    if "npm install" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1]).strip()
        if not extract_cache_cmd:
            # for RCS
            install_packages_cmd = setup_cmd
    elif "mvn -q install" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1])
        build_cmd = setup_cmd.split("; ")[-1]
    elif "cargo build" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1])
    elif "go build" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1])
    elif "pip" in setup_cmd:
        install_packages_cmd = setup_cmd.replace("\n", "; ")
    else:
        print(f"setup_cmd not supported: {setup_cmd}")
        assert False, f"setup_cmd not supported: {setup_cmd}; metadata: {metadata}"

    return metadata | {
        "extract_cache_cmd": extract_cache_cmd,
        "install_packages_cmd": install_packages_cmd,
        "build_cmd": build_cmd,
    }


async def setup_repo_with_gt_metadata(
    terminal_session: TerminalSession,
    gt_metadata: dict[str, Any],
    data_version: str = "rrb",
) -> dict[str, Any]:
    repo_id = gt_metadata["repo_id"]
    repo_name = clean_up_repo_name(gt_metadata.get("repo_name", "workdir"))
    repo_root = '/root/code'
    package_cache_id = gt_metadata.get("package_cache_id", "")

    metadata = patch_metadata(gt_metadata)
    extract_cache_cmd = metadata.get("extract_cache_cmd", "")
    install_packages_cmd = metadata.get("install_packages_cmd", "")
    lang = gt_metadata.get("lang", "python")

    out = await setup_repo(
        terminal_session,
        repo_root=repo_root,
        repo_id=repo_id,
        lang=lang,
        package_cache_id=package_cache_id,
        extract_cache_cmd=extract_cache_cmd,
        install_packages_cmd=install_packages_cmd,
        files_to_be_edited=metadata.get("modified_files", {}),
        direct_repo_files=metadata.get("direct_repo_files", {}).get("files"),
        data_version=data_version,
    ) | {"repo_root": repo_root}

    with open("/var/log/supervisor/yang_repo_setup_result.log", "a") as f:
        f.write(json.dumps(out)+"\n")

    return out

async def setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
    data_version: str = "rrb",
) -> None:
    result = await setup_repo_with_gt_metadata(terminal_session, datapoint["metadata"], data_version=data_version)
    if not result.get("setup_done"):
        logger.error("mrfs_setup.setup_fn failed", result=result)
        raise Exception("Setup failed")


async def mrfs_setup_run_test(
    caas_endpoint: str,
    image_name: str,
    gt_metadata: dict[str, Any],
    changed_files: dict[str, str | None],
    pass_condition: str | None = None,
    prep_attempts: int = DEFAULT_ATTEMPTS,
    callback: Callable[[TerminalSession, str], Awaitable[dict[str, Any]]] | None = None,
    data_version: str = "rrb",
) -> TestResult:
    metadata = patch_metadata(gt_metadata)

    container = None
    setup_result = {}
    for attempt in range(prep_attempts):
        try:

            caas = caas_api(endpoint=caas_endpoint)
            caas_session = await caas.new_session(
                image=image_name if "autogen" not in metadata.get(
                    "repo_id") else "acrbuiltincaasglobalame.azurecr.io/repos/autogen:313b",
                memory_limit="64g" if "autogen" in metadata.get("repo_id") else "2g",
                cpu_limit="50.0" if "autogen" in metadata.get("repo_id") else "2.0",
                disk_limit="25g",
                pids_limit=200,
            )

            terminal_session = TerminalSession(caas_session, endpoint=caas_endpoint)
            setup_result = await setup_repo_with_gt_metadata(
                terminal_session,
                metadata,
                data_version=data_version,
            )
            if not setup_result.get("setup_done"):
                if attempt + 1 < prep_attempts:
                    await asyncio.sleep(attempt * 5 + 1)
                    continue
                logger.error(
                    "setup_repo_with_gt_metadata failed during grading", result=setup_result
                )
                return TestResult(
                    system_error="Setup failed", metadata=setup_result | {"attempt": attempt}
                )

            repo_root: str = setup_result["repo_root"]
            tr = await mrfs_run_test_with_test_result(
                terminal_session,
                metadata,
                changed_files,
                repo_root,
                pass_condition,
            )

            if tr.passed and callback:
                try:
                    tr.metadata |= await callback(terminal_session, repo_root)
                except Exception as e:
                    tr.metadata["callback_error"] = str(e)
        except ServerError:
            if attempt + 1 < prep_attempts:
                await asyncio.sleep(attempt * 5 + 1)
                continue
            raise
        finally:
            if container:
                await container.teardown()

        if attempt > 0:
            tr.metadata["attempt"] = attempt
        break
    tr.metadata |= setup_result
    return tr