import berry.preset_utils
import deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.configs as py_configs
import deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets.configs as configs
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe.datasets.config_utils import chz_path

BASELINE_DATASET_CONFIGS = [
    (py_configs.RFSPythonDatasetConfig, []),
    (py_configs.RCSPythonDatasetConfig, []),
    (py_configs.BugBountyPythonDatasetConfig, []),
]

CSHARP_FILTERED_MIX_V0_DATASET_CONFIGS = [  # Total: 840 data points
    # RFS
    (configs.RFSCSharpFilteredNormalAndEasyDatasetConfig, []),  # 634 data points
    # RCS
    (configs.RCSCSharpFilteredNormalAndEasyDatasetConfig, []),  # 101 data points
    # BB
    (configs.BugBountyCSharpFilteredNormalAndEasyDatasetConfig, []),  # 105 data points
]

CSHARP_FILTERED_MIX_V0_30_PERCENT_AND_PYTHON_70_PERCENT_DATASET_CONFIGS = [
    # C# datasets (840 data points)
    (configs.RFSCSharpFilteredNormalAndEasyDatasetConfig, []),  # 634 data points
    (configs.RCSCSharpFilteredNormalAndEasyDatasetConfig, []),  # 101 data points
    (configs.BugBountyCSharpFilteredNormalAndEasyDatasetConfig, []),  # 105 data points
    # Python datasets (840 / 3 * 7 = 1960 data points)
    (py_configs.RFSPythonDatasetConfig, ["max_n_datapoints=591"]),  # 4093 data points --> 591
    (py_configs.RCSPythonDatasetConfig, ["max_n_datapoints=1091"]),  # 7562 data points --> 1091
    (py_configs.BugBountyPythonDatasetConfig, ["max_n_datapoints=278"]),  # 1930 data points --> 278
]

DATASET_CONFIGS = BASELINE_DATASET_CONFIGS

format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)


train_baseline = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in BASELINE_DATASET_CONFIGS
    ],
    format,
)


train = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in DATASET_CONFIGS
    ],
    format,
)


train_csharp_filtered_mix_v0 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in CSHARP_FILTERED_MIX_V0_DATASET_CONFIGS
    ],
    format,
)


train_csharp_filtered_mix_v0_30_percent_and_python_70_percent = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in CSHARP_FILTERED_MIX_V0_30_PERCENT_AND_PYTHON_70_PERCENT_DATASET_CONFIGS
    ],
    format,
)