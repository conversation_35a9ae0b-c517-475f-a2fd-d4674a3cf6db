from functools import partial
from typing import Any

import chz
import structlog
from berry.function_wrapper import FunctionWrapper
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe_msft.rfs_rcs_bb_ml_vsc.javascript.datasets.mrfs_setup import (
    mrfs_setup_fn_coreutils,
    mrfs_setup_fn_coreutils_rfs_0828_25,
    mrfs_setup_fn_coreutils_rcs_pp,
)
from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.configs import (
    CaasResourceConfig,
    RFSPythonDatasetConfig,
    make_multistage_grader,
    _make_tool_configs,
)
from deep_swe_msft.rfs_rcs_bb_ml_vsc.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from berry_harmony.tools.berry_tool_interface import ResourceConfig
from berry.grader import FunctionalGrader, Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from qstar.samplers import BaseSampler
from token_completer import TokenCompleter
from deep_swe_msft.vsc_graders.cotograder_utils import (
    COTOGRADER_BUS_USER,
    COTOGRADER_BUS_TOPIC,
    COTOGRADER_BUS_LINE,
    COTOGRADER_RENDERER,
    COTOGRADER_QOS_TYPE,
)
from deep_swe_msft.vsc_graders.grader_picker import GRADER_PICKER

logger = structlog.stdlib.get_logger(component=__name__)


def make_rfs_multistage_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_repeat_read_grader: bool = False,
    use_tool_grader: bool = False,
    use_function_enforce_grader: bool = False,
    data_version: str = "rrb",
    **kwargs: Any,
) -> MultiStageGrader:
    grader_argvs = []
    
    # Conditionally add RepeatReadingGrader
    if use_repeat_read_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.read_file_grader:RepeatReadingGrader",
        ])
    
    # Always add RFSGrader
    grader_argvs.append([
        "=deep_swe_msft.rfs_rcs_bb_ml_vsc.javascript.graders.rfs_grader:RFSGrader",
        f"caas_container_image={caas_container_image}",
        f"caas_endpoint={caas_endpoint}",
        f"collect_commit_fn=deep_swe_msft.rfs_rcs_bb_ml_vsc.javascript.graders.rfs_grader:_vsc_collect_commit",
        f"data_version={data_version}",
    ])
    
    # Conditionally add ToolCallGrader
    if use_tool_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.tool_call_grader:ToolCallGrader",
            f"topic={COTOGRADER_BUS_TOPIC}",
            f"topic_mode_or_user={COTOGRADER_BUS_USER}",
            f"line={COTOGRADER_BUS_LINE}",
            f"renderer_name={COTOGRADER_RENDERER}",
            f"qos_type=QoSType.{COTOGRADER_QOS_TYPE}",
        ])
    
    # Conditionally add FuncEnforcementGrader
    if use_function_enforce_grader:
        grader_argvs.append([
            "=deep_swe_msft.vsc_graders.func_enforcement_grader:FuncEnforcementGrader",
            "target_ratio=0.15",
        ])

    return make_multistage_grader(grader_argvs, channels_for_answer)


@chz.chz
class RFSJAVASCRIPTDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.yunsheng.swe.javascript.javascript_derisk_rfs_sanity_hard_filtered"
    grader: Grader | FunctionalGrader = override(
        lambda: make_rfs_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["javascript"],
            **GRADER_PICKER
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["javascript"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )


@chz.chz
class RFSJAVASCRIPT0828DatasetConfig(RFSJAVASCRIPTDatasetConfig, IsOverride):
    """Dataset config targeting the rfs-0828-25 data_version for JS.

    Uses the variant setup function and passes data_version into grader via override.
    """
    tool_configs: tuple[ToolConfig, ...] = override(partial(_make_tool_configs, real_tool="fake"))
    dataset_id: str = "data.haoranxu.swe.javascript.rfs_0828_25"
    grader: Grader | FunctionalGrader = override(
        lambda: make_rfs_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["javascript"],
            data_version="rfs-0828-25",
            **GRADER_PICKER,
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils_rfs_0828_25,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["javascript"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )


@chz.chz
class RFSJAVASCRIPTRCSPPDatasetConfig(RFSJAVASCRIPTDatasetConfig, IsOverride):
    """Dataset config targeting the rcs++ data_version for JS.

    Uses the variant setup function and passes data_version into grader via override.
    """
    tool_configs: tuple[ToolConfig, ...] = override(partial(_make_tool_configs, real_tool="fake"))
    grader: Grader | FunctionalGrader = override(
        lambda: make_rfs_multistage_grader(
            caas_container_image=RFS_LANG_TO_IMAGE_NAME["javascript"],
            data_version="rcs++",
            **GRADER_PICKER,
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils_rcs_pp,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["javascript"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )


@chz.chz
class RCSJAVASCRIPTDatasetConfig(RFSJAVASCRIPTDatasetConfig, IsOverride):
    dataset_id: str = "data.yunsheng.swe.javascript.javascript_derisk_rcs_sanity_hard_filtered"


@chz.chz
class BBJAVASCRIPTDatasetConfig(RFSJAVASCRIPTDatasetConfig, IsOverride):
    dataset_id: str = "data.yunsheng.swe.javascript.javascript_derisk_bb_sanity_hard_filtered"

@chz.chz
class RRBJAVASCRIPTDatasetConfig(RFSJAVASCRIPTDatasetConfig, IsOverride):
    dataset_id: str = "data.zhendongw.swe_train.data_mix_v1.rrb_js"

# ================TODO: RRB Hard Datasets =================
