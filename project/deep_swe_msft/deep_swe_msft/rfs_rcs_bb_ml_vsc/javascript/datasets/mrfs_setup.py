import asyncio
import base64
import json
import os
import re
import time
from typing import Any, Awaitable, Callable, Mapping

import structlog
from berry_rfs.utils import TestResult
from caas import ExecError, ServerError, TimedOutError
from caas.api import caas_api
from caas.commands import BashScript, UploadFile
from caas.terminal.api import TerminalSession
from caas_rfs.setup import get_repo_name
from deep_swe_msft.rfs_rcs_bb_ml_vsc.javascript.datasets.mrfs_setup_utils import (
    mrfs_run_test_with_test_result,
)
from deep_swe_msft.tools.vscode_copilot_tool import setup_vscutils

# todo
from deep_swe_msft.tools.utils import get_strawberry_ace_token
from mini.metrics import metrics
from mini.utils.simple_timer import simple_timer

DEFAULT_ATTEMPTS = 10
DEFAULT_TIMEOUT = 3600
MAX_OUTPUT_SIZE = 1_000_000
DEFAULT_SUPPORT_ROOT = "/tmp/repo_support"

HTTP_FILE_PATH = {
    "rrb": "https://orngcaas.blob.core.windows.net/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/javascript/train/rfs/3p/upload_repos_enable_vscode_test",
    "rfs-0828-25": "https://orngcaas.blob.core.windows.net/data/haoranxu/swe/rfs-0828-25/javascript/upload_repos",
    "rcs++": "https://orngcaas.blob.core.windows.net/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/python/train/rfs/3p/upload_repos_enable_vscode_test",
}

logger = structlog.stdlib.get_logger(component=__name__)

# def get_strawberry_ace_token():
#     # return os.environ.get("STRAWBERRYACE_CENTRALUS_TOKEN")
#     # return "?sv=2023-01-03&st=2025-06-03T23%3A53%3A46Z&se=2025-06-10T23%3A53%3A00Z&skoid=2ef04b63-21b8-4491-b4e2-4327a78d7c3a&sktid=72f988bf-86f1-41af-91ab-2d7cd011db47&skt=2025-06-03T23%3A53%3A46Z&ske=2025-06-10T23%3A53%3A00Z&sks=b&skv=2023-01-03&sr=c&sp=rl&sig=EFYhR8AaAF0udCKu2q%2F9BpD2hTs44NFoGb721RkHHWo%3D"
#     return "sp=r&st=2025-06-06T21:53:33Z&se=2025-06-13T05:53:33Z&skoid=23dfe465-2a5e-4b11-8284-63893e809dae&sktid=72f988bf-86f1-41af-91ab-2d7cd011db47&skt=2025-06-06T21:53:33Z&ske=2025-06-13T05:53:33Z&sks=b&skv=2024-11-04&spr=https&sv=2024-11-04&sr=c&sig=nqPBmMKdkuQs%2BTuWwUx%2BuD7Kbk3RYclUZw4etv40y6o%3D"


async def mrfs_setup_fn_coreutils(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
    data_version: str = "rrb",
) -> None:
    """
    Thin wrapper over regular RFS setup that installs oai coreutils
    """
    repo_root = "/root/code"
    vscode_settings = datapoint["metadata"].get("vscode_settings", None)
    vscode_commands = datapoint["metadata"].get("vscode_commands", None)
    vscode_extensions = datapoint["metadata"].get("vscode_extensions", None)
    await setup_repo_with_gt_metadata(terminal_session, datapoint["metadata"], data_version=data_version)
    await setup_vscutils(
        datapoint=None,
        session=terminal_session.session,
        workdir=repo_root,
        language="javascript",
        vscode_settings=vscode_settings,
        vscode_commands=vscode_commands,
        vscode_extensions=vscode_extensions,
    )


async def mrfs_setup_fn_coreutils_rfs_0828_25(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    """Variant setup function pinned to data_version="rfs-0828-25".

    Provides a convenient hook for configs that want to target the new data
    source without having to override arguments everywhere.
    """
    await mrfs_setup_fn_coreutils(
        datapoint=datapoint,
        terminal_session=terminal_session,
        data_version="rfs-0828-25",
    )

async def mrfs_setup_fn_coreutils_rcs_pp(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    """Variant setup function pinned to data_version="rcs++".

    Provides a convenient hook for configs that want to target the new data
    source without having to override arguments everywhere.
    """
    await mrfs_setup_fn_coreutils(
        datapoint=datapoint,
        terminal_session=terminal_session,
        data_version="rcs++",
    )

def clean_up_repo_name(repo_name: str) -> str:
    repo_name = repo_name.split("/")[-1]
    repo_name = re.sub(r"[^a-zA-Z0-9]", "", repo_name)
    return repo_name


def get_repo_directory(repo_name: str) -> str:
    return f"/root/{repo_name}"


def truncate_text(text: str) -> str:
    if len(text) <= MAX_OUTPUT_SIZE:
        return text
    return text[: MAX_OUTPUT_SIZE // 2] + " [TRUNCATED] " + text[-MAX_OUTPUT_SIZE // 2 :]


async def try_run_command(
    terminal_session: TerminalSession,
    command: str,
    seconds: int = 120,
    attempts: int = 1,
) -> tuple[bool, dict]:
    for attempt in range(attempts):
        start_time = time.time()
        try:
            result = await terminal_session.session.run(BashScript(command, timeout=seconds))
            text = result.decode("utf-8")
            return True, {
                "command": command,
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except TimedOutError as e:
            text = e.output.decode("utf-8")
            return False, {
                "command": command,
                "error": "TimedOutError",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except ExecError as e:
            if attempt + 1 < attempts:
                await asyncio.sleep(attempt * 5 + 1)
                continue
            text = e.output.decode("utf-8")
            return False, {
                "command": command,
                "error": "ExecError",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except Exception as e:
            text = ""
            return False, {
                "command": command,
                "error": "Exception",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }


def setup_bashrc(repo_root: str, PATHS: list[str]) -> str:
    paths = ":".join(PATHS)
    SETUP_BASH = f"""
cat <<EOF >> /root/.bashrc
export PATH=$PATH:{paths}
export PYTHONPATH={repo_root}:{repo_root}/src
export PY_COLORS=0
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
cat <<EOF >> /root/.bash_profile
export PATH=$PATH:{paths}
export PYTHONPATH={repo_root}:{repo_root}/src
export PY_COLORS=0
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
"""
    return SETUP_BASH


def setup_typescript_bashrc(repo_root: str, PATHS: list[str]) -> str:
    paths = ":".join(PATHS)
    SETUP_BASH = f"""
cat <<EOF >> /root/.bashrc
export PATH=$PATH:{paths}
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
cat <<EOF >> /root/.bash_profile
export PATH=$PATH:{paths}
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
"""
    return SETUP_BASH


def setup_javascript_bashrc(repo_root: str, PATHS: list[str]) -> str:
    paths = ":".join(PATHS)
    SETUP_BASH = f"""
cat <<EOF >> /root/.bashrc
export PATH=$PATH:{paths}
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
cat <<EOF >> /root/.bash_profile
export PATH=$PATH:{paths}
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
EOF
"""
    return SETUP_BASH


def setup_bashrc_autogen(repo_root: str, PATHS: list[str]) -> str:
    paths = ":".join(PATHS)
    SETUP_BASH = f"""
cat <<EOF >> /root/.bashrc
export PATH=$PATH:{paths}
export PYTHONPATH={repo_root}:{repo_root}/src
export PY_COLORS=0
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
source "python/.venv/bin/activate"
EOF
cat <<EOF >> /root/.bash_profile
export PATH=$PATH:{paths}
export PYTHONPATH={repo_root}:{repo_root}/src
export PY_COLORS=0
export OPENBLAS_NUM_THREADS=32
export PS2=''
cd {repo_root}
source "python/.venv/bin/activate"
EOF
"""
    return SETUP_BASH


dump_run_tests_command = f"""
cat <<EOF >> /root/autogen/run_tests.sh
#!/bin/bash
set -e
ulimit -v unlimited
export OPENBLAS_NUM_THREADS=1
cd /root/autogen/python
source .venv/bin/activate
cd /root/autogen
pushd /root/autogen > /dev/null
cd python
source ".venv/bin/activate"
mkdir -p test_reports
export OPENAI_API_KEY=abc
poe test
popd > /dev/null
EOF
"""


async def _setup_direct_repo_files(
    terminal_session: TerminalSession,
    repo_root: str,
    repo_id: str,
    direct_repo_files: dict[str, str],
) -> tuple[dict[str, Any], bool]:
    """Returns `(file_contents, success)`.

    Args:
        terminal_session: TerminalSession
        repo_root: str
        repo_id: str
        direct_repo_files: dict[str, str].  Keys are filenames,
          values are file contents.

    Returns:
        Tuple of `(file_statuses, success)`.  `file_statuses` is a dict of
        status information for each uploaded file.  `success` is a boolean indicating
        whether the operation was successful.
    """
    result = {}
    ok, d = await try_run_command(
        terminal_session,
        f"rm -rf {repo_root}; mkdir -p {repo_root}",
        attempts=DEFAULT_ATTEMPTS,
    )
    result["copy_repo_init"] = d
    if not ok:
        return result
    coros = [
        try_run_command(
            terminal_session,
            (
                f"mkdir -p '{repo_root}/{os.path.dirname(filename)}'; "
                f"echo '{base64.b64encode(content.encode()).decode()}' | base64 -d > {repo_root}/{filename}"
            ),
            attempts=DEFAULT_ATTEMPTS,
        )
        for filename, content in direct_repo_files.items()
    ]
    results = await asyncio.gather(*coros)
    for filename, (ok, d) in zip(direct_repo_files, results):
        result[f"copy_test_file_{filename}"] = d
        if not ok:
            return result, False

    return result, True


async def setup_repo(
    terminal_session: TerminalSession,
    repo_root: str,
    repo_id: str,
    lang: str,
    package_cache_id: str,
    extract_cache_cmd: str,
    install_packages_cmd: str,
    install_seconds: int = DEFAULT_TIMEOUT,
    support_root: str = DEFAULT_SUPPORT_ROOT,
    direct_repo_files: dict[str, str] | None = None,
    files_to_be_edited: dict[str, str] = {},
    data_version: str = "rrb",
) -> dict[str, Any]:
    """Setup a repo for testing.

    Args:
        terminal_session: The terminal session to use.
        repo_root: The root directory for the repo.  It must start with / and not end in /.
        repo_id: The repo ID.
        package_cache_id: The package cache ID.  If empty, no package cache is uploaded.
        extract_cache_cmd: The command to extract the cache. Must not be empty if package_cache_id is not empty.
        install_packages_cmd: The command to install packages.  Must be empty if package_cache_id is not empty.
        install_seconds: int
        support_root: The support root directory.  Its path is added to the PATH.
        direct_repo_files: Only used for testing.  If not None, it is a dict
          of filenames and contents to upload to the repo.

    Returns:
        A dict of status information for each step of the setup process.
    """
    assert repo_root.startswith("/") and not repo_root.endswith(
        "/"
    ), f"Invalid repo_root; must start with / and not end in /: {repo_root}"

    result = {
        "setup_done": False,
        "repo_id": repo_id,
        "package_cache_id": package_cache_id,
        "extract_cache_cmd": extract_cache_cmd,
        "install_packages_cmd": install_packages_cmd,
        "lang": lang,
    }
    with open("/var/log/supervisor/yunsheng40_repo_setup.log", "a") as f:
        to_write = {
            "repo_root": repo_root,
            "repo_id": repo_id,
            "package_cache_id": package_cache_id,
            "extract_cache_cmd": extract_cache_cmd,
            "install_packages_cmd": install_packages_cmd,
            "lang": lang,
        }
        f.write(json.dumps(to_write) + "\n")

    http_file_path = HTTP_FILE_PATH[data_version]
    if "autogen" not in repo_root:  # for 3p cases only
        with simple_timer(
            "download_task_repo", log_fn=lambda x: [metrics.mean(k, v) for k, v in x.items()]
        ):
            ok, d = await try_run_command(
                terminal_session,
                f'rm -rf {repo_root}; mkdir -p {repo_root}; curl -k -sL "{http_file_path}/{repo_id}.tar.gz?{get_strawberry_ace_token()}" | tar -xz -C {repo_root}',
                seconds=install_seconds,
                attempts=DEFAULT_ATTEMPTS,
            )
        result["copy_repo"] = d

        if not ok:
            metrics.sum("download_task_repo_failed", 1.0)
            return result

    else:  # 1p autogen setup
        with open("/root/code/garden/lib/berry_rfs/berry_rfs/autogen_patch.diff", "rb") as f:
            data = f.read()
        await terminal_session.session.run(UploadFile(f"{repo_root}/patch.diff", data))
        ok, d = await try_run_command(
            terminal_session,
            f"cd {repo_root} && git apply patch.diff",
            attempts=DEFAULT_ATTEMPTS,
        )
        await try_run_command(
            terminal_session,
            f"cd {repo_root} && rm patch.diff",
            attempts=DEFAULT_ATTEMPTS,
        )
        result["apply_patch"] = d

        if not ok:
            return result

    if "autogen" not in repo_root:
        # setup packages for 3p repos
        if lang == "typescript":
            # setup packages
            if install_packages_cmd:
                with simple_timer(
                    "setup_task_repo", log_fn=lambda x: [metrics.mean(k, v) for k, v in x.items()]
                ):
                    # typescript_cmd = 'bash -lc "npm config set registry \\$CAAS_ARTIFACTORY_NPM_REGISTRY"'
                    # typescript_result = await terminal_session.session.run(RawExec(["bash", "-lc", "npm config set registry $CAAS_ARTIFACTORY_NPM_REGISTRY"], timeout=install_seconds, enable_public_logging=True))
                    ok_tp, d_tp = await try_run_command(
                        terminal_session,
                        f"echo $CAAS_ARTIFACTORY_NPM_REGISTRY; npm config set registry $CAAS_ARTIFACTORY_NPM_REGISTRY",
                        install_seconds,
                        attempts=DEFAULT_ATTEMPTS,
                    )
                    ok, d = await try_run_command(
                        terminal_session,
                        f"cd {repo_root}; export NODE_TLS_REJECT_UNAUTHORIZED=0; export UV_THREADPOOL_SIZE=1; {install_packages_cmd}",
                        # f"cd {repo_root}; {install_packages_cmd}",
                        install_seconds,
                        attempts=DEFAULT_ATTEMPTS,
                    )
                result["install_packages"] = d
                result["lang"] = lang
                if not ok:
                    metrics.sum("setup_task_repo_failed", 1.0)
                    return result
        elif lang == "javascript":
            # setup packages
            if install_packages_cmd:
                with simple_timer(
                    "setup_task_repo", log_fn=lambda x: [metrics.mean(k, v) for k, v in x.items()]
                ):
                    # javascript_cmd = 'bash -lc "npm config set registry \\$CAAS_ARTIFACTORY_NPM_REGISTRY"'
                    # javascript_result = await terminal_session.session.run(RawExec(["bash", "-lc", "npm config set registry $CAAS_ARTIFACTORY_NPM_REGISTRY"], timeout=install_seconds, enable_public_logging=True))
                    ok_tp, d_tp = await try_run_command(
                        terminal_session,
                        f"echo $CAAS_ARTIFACTORY_NPM_REGISTRY; npm config set registry $CAAS_ARTIFACTORY_NPM_REGISTRY",
                        install_seconds,
                        attempts=DEFAULT_ATTEMPTS,
                    )
                    ok, d = await try_run_command(
                        terminal_session,
                        f"cd {repo_root}; {install_packages_cmd}",
                        # f"cd {repo_root}; {install_packages_cmd}",
                        install_seconds,
                        attempts=DEFAULT_ATTEMPTS,
                    )
                result["install_packages"] = d
                result["lang"] = lang
                if not ok:
                    metrics.sum("setup_task_repo_failed", 1.0)
                    return result
        else:
            if install_packages_cmd:
                with simple_timer(
                    "setup_task_repo", log_fn=lambda x: [metrics.mean(k, v) for k, v in x.items()]
                ):
                    ok, d = await try_run_command(
                        terminal_session,
                        f"cd {repo_root}; {install_packages_cmd}",
                        install_seconds,
                        attempts=DEFAULT_ATTEMPTS,
                    )
                result["install_packages"] = d
                if not ok:
                    metrics.sum("setup_task_repo_failed", 1.0)
                    return result

    else:
        # setup function masks for autogen repos
        try:
            # remove the setup.sh/run_tests.sh files
            ok, d = await try_run_command(
                terminal_session,
                f"cd {repo_root}; rm -f setup*; rm -f run_test*",
                attempts=DEFAULT_ATTEMPTS,
            )
            result["remove_old_sh_files"] = d
            # write a new run_tests.sh file
            ok, d = await try_run_command(terminal_session, dump_run_tests_command)
            result["override_run_tests"] = d
            ok, d = await try_run_command(terminal_session, "chmod +x /root/autogen/run_tests.sh")
            result["give_run_tests_access"] = d

            # mask function for RFS for example
            for code_path, diff_code in files_to_be_edited.items():
                await terminal_session.session.run(
                    UploadFile(
                        path=f"{repo_root}/{code_path}",
                        data=(diff_code or "").encode(),
                    )
                )

            # remove unnecessary files
            await try_run_command(
                terminal_session,
                f"cd {repo_root} && rm -r .*",
                attempts=DEFAULT_ATTEMPTS,
            )
            await try_run_command(
                terminal_session,
                f"cd {repo_root} && rm autogen_test_setup_unittest_0305_v1.tar.gz",
                attempts=DEFAULT_ATTEMPTS,
            )

            result["exec_patch_repo"] = {"done": True}
        except Exception as e:
            result["exec_patch_repo"] = {
                "error": "Exception uploading diff code",
                "error_message": str(e),
            }
            return result

    if "autogen" not in repo_root:
        if lang == "typescript":
            setup_bashrc_cmd = setup_typescript_bashrc(
                repo_root, [support_root, repo_root, f"{repo_root}/typescript"]
            )
        elif lang == "javascript":
            setup_bashrc_cmd = setup_javascript_bashrc(
                repo_root, [support_root, repo_root, f"{repo_root}/javascript"]
            )
        else:
            # setup_bashrc_cmd = setup_bashrc(repo_root, [support_root, repo_root, f"{repo_root}/src"])
            setup_bashrc_cmd = setup_bashrc(
                repo_root, [support_root, repo_root, f"{repo_root}/python"]
            )
    else:
        setup_bashrc_cmd = setup_bashrc_autogen(
            repo_root, [support_root, repo_root, f"{repo_root}/python"]
        )

    ok, d = await try_run_command(terminal_session, setup_bashrc_cmd)
    result["setup_bashrc"] = d
    if not ok:
        return result

    # if "mrfs_js_" in repo_id:
    #     # Hack: JS repos sometimes include build files that contain the answers.
    #     ok, d = await try_run_command(
    #         terminal_session, f"rm -rf {repo_root}/dist; rm -rf {repo_root}/build"
    #     )
    #     result["rm_dist"] = d
    #     if not ok:
    #         return result

    result["setup_done"] = True
    metrics.sum("setup_task_repo_done", 1.0)
    return result


def patch_metadata(metadata: dict[str, Any]) -> dict[str, Any]:
    exec_cmd = metadata.get("exec_cmd", "")
    if not exec_cmd:
        assert not metadata.get("lang")
        exec_cmd = "pytest"

        extra_setup = metadata.get("extra_setup", "")
        install_packages_cmd = ""
        if extra_setup:
            install_packages_cmd = extra_setup.replace("!", "")

        return {k: v for k, v in metadata.items() if k != "extra_setup"} | {
            "lang": "python",
            "extract_cache_cmd": "",
            "install_packages_cmd": install_packages_cmd,
            "build_cmd": "",
            "exec_cmd": exec_cmd,
        }

    setup_cmd = metadata.get("setup_cmd", "")
    if not setup_cmd:
        return metadata

    extract_cache_cmd = ""
    install_packages_cmd = ""
    build_cmd = ""

    if "npm install" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1]).strip()
        if not extract_cache_cmd:
            # for RCS
            install_packages_cmd = setup_cmd
    elif "mvn -q install" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1])
        build_cmd = setup_cmd.split("; ")[-1]
    elif "cargo build" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1])
    elif "go build" in setup_cmd:
        extract_cache_cmd = "; ".join(setup_cmd.split("; ")[:-1])
    elif "pip" in setup_cmd:
        install_packages_cmd = setup_cmd.replace("\n", "; ")
    else:
        assert False

    return metadata | {
        "extract_cache_cmd": extract_cache_cmd,
        "install_packages_cmd": install_packages_cmd,
        "build_cmd": build_cmd,
    }


async def setup_repo_with_gt_metadata(
    terminal_session: TerminalSession,
    gt_metadata: dict[str, Any],
    data_version: str = "rrb",
) -> dict[str, Any]:
    repo_id = gt_metadata["repo_id"]
    repo_name = clean_up_repo_name(gt_metadata.get("repo_name", "workdir"))
    repo_root = "/root/code"
    package_cache_id = gt_metadata.get("package_cache_id", "")

    metadata = patch_metadata(gt_metadata)
    extract_cache_cmd = metadata.get("extract_cache_cmd", "")
    install_packages_cmd = metadata.get("install_packages_cmd", "")
    lang = gt_metadata.get("lang", "python")

    out = await setup_repo(
        terminal_session,
        repo_root=repo_root,
        repo_id=repo_id,
        lang=lang,
        package_cache_id=package_cache_id,
        extract_cache_cmd=extract_cache_cmd,
        install_packages_cmd=install_packages_cmd,
        files_to_be_edited=metadata.get("modified_files", {}),
        direct_repo_files=metadata.get("direct_repo_files", {}).get("files"),
        data_version=data_version,
    ) | {"repo_root": repo_root}

    with open("/var/log/supervisor/yunsheng40_repo_setup_result.log", "a") as f:
        f.write(json.dumps(out) + "\n")

    return out


async def setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    result = await setup_repo_with_gt_metadata(terminal_session, datapoint["metadata"])
    if not result.get("setup_done"):
        logger.error("mrfs_setup.setup_fn failed", result=result)
        raise Exception("Setup failed")


async def mrfs_setup_run_test(
    caas_endpoint: str,
    image_name: str,
    gt_metadata: dict[str, Any],
    changed_files: dict[str, str | None],
    prep_attempts: int = DEFAULT_ATTEMPTS,
    callback: Callable[[TerminalSession, str], Awaitable[dict[str, Any]]] | None = None,
    data_version: str = "rrb",
) -> TestResult:
    metadata = patch_metadata(gt_metadata)

    container = None
    setup_result = {}
    for attempt in range(prep_attempts):
        try:

            caas = caas_api(endpoint=caas_endpoint)
            caas_session = await caas.new_session(
                image=image_name
                if "autogen" not in metadata.get("repo_id")
                else "acrbuiltincaasglobalame.azurecr.io/repos/autogen:313b",
                memory_limit="64g" if "autogen" in metadata.get("repo_id") else "16g",
                cpu_limit="50.0" if "autogen" in metadata.get("repo_id") else "4.0",
                pids_limit=None,
                disk_limit="32g",
            )

            terminal_session = TerminalSession(caas_session, endpoint=caas_endpoint)
            setup_result = await setup_repo_with_gt_metadata(
                terminal_session,
                metadata,
                data_version=data_version
            )
            if not setup_result.get("setup_done"):
                if attempt + 1 < prep_attempts:
                    await asyncio.sleep(attempt * 5 + 1)
                    continue
                logger.error(
                    "setup_repo_with_gt_metadata failed during grading", result=setup_result
                )
                return TestResult(
                    system_error="Setup failed", metadata=setup_result | {"attempt": attempt}
                )

            repo_root: str = setup_result["repo_root"]
            # with open("/var/log/supervisor/yunsheng40_test1.log", "a") as f:
            #    f.write("before mrfs_run_test_with_test_result.\n")
            try:
                tr = await mrfs_run_test_with_test_result(
                    terminal_session,
                    metadata,
                    changed_files,
                    repo_root,
                )
            except Exception as e:
                with open("/var/log/supervisor/yunsheng40_run_test_error.log", "a") as f:
                    f.write(f"{e}\n")
                raise e

            if tr.passed and callback:
                try:
                    tr.metadata |= await callback(terminal_session, repo_root)
                except Exception as e:
                    tr.metadata["callback_error"] = str(e)
        except ServerError:
            if attempt + 1 < prep_attempts:
                await asyncio.sleep(attempt * 5 + 1)
                continue
            raise
        finally:
            if container:
                await container.teardown()

        if attempt > 0:
            tr.metadata["attempt"] = attempt
        break
    tr.metadata |= setup_result
    return tr
