import copy
import json
from typing import Mapping

from chat import chat
from diffs.v4.commit import ActionType, Commit, assemble_changes
from diffs.v4.diff_error import DiffError
from diffs.v4.parser_v4a import identify_files_needed, text_to_patch
from diffs.v4.patch import Patch
from diffs.v4.patch_to_commit import patch_to_commit


def apply_to_files(commit: Commit, files: dict[str, str | None]) -> dict[str, str | None]:
    files = copy.deepcopy(files)
    for path, change in commit.changes.items():
        if change.type == ActionType.DELETE:
            assert change.old_content == files[path]
            files[path] = None
        elif change.type == ActionType.UPDATE and change.move_path:
            assert change.old_content == files[path]
            assert files.get(change.move_path) is None
            files[change.move_path] = change.new_content
            files[path] = None
        elif change.type == ActionType.UPDATE:
            assert change.old_content == files[path]
            files[path] = change.new_content
        elif change.type == ActionType.ADD:
            assert files.get(path) is None
            files[path] = change.new_content
        else:
            assert False

    return files


def pull_files_from_convo(
    convo: chat.Conversation,
) -> dict[str, str]:
    """
    If you don't have access to the repo used for the conversation. This method
    can try to extract the needed files directly from the convo. It's usually
    sufficient, but in some really rare cases, the model has tried to update
    files directly based on what it saw in search results, so this would not
    cover those cases.
    """
    files: dict[str, str] = {}
    for message in convo.messages:
        if (
            message.author.name in {"python", "repo_browser"}
            and "repo_browser_v2" in message.metadata
        ):
            d = message.metadata["repo_browser_v2"]
            if "path" not in d or "content" not in d:
                # ignore non-file messages
                continue
            if files.get(d["path"]) is None:
                files[d["path"]] = d["content"]
            else:
                if files[d["path"]] != d["content"]:
                    print(
                        f"chat_to_files_and_patches: files[d['path']] != d['content']: {d['path']}"
                    )
            continue
    return files


def extract_patch_text(text: str) -> str:
    pos = text.find("*** Begin Patch")
    pos2 = text.find("*** End Patch")
    if pos != -1 and pos2 != -1 and pos2 > pos:
        patch_text = text[pos : pos2 + len("*** End Patch")]
        return patch_text
    return ""


def get_text(message: chat.Message) -> str:
    if isinstance(message.content, chat.Text) and isinstance(message.content.parts[0], str):
        return message.content.parts[0]
    elif isinstance(message.content, chat.Code):
        return message.content.text
    return ""


def get_patch_code(message: chat.Message, next_message=None, orig=None) -> str:
    try:
        tool = ""
        if (
            message.recipient == "repo_browser.apply_patch"
            and message.content.content_type == "code"
        ):
            text = message.content.text
            j = json.loads(text)
            text = j.get("patch")
            if text:
                return text
        elif message.recipient in {"functions.feed_chars", "container.feed_chars"}:
            text = get_text(message)
            j = json.loads(text)
            text = j.get("chars")
            if text:
                return text
        elif message.recipient in {"functions.exec", "container.exec"}:
            text = get_text(message)
            j = json.loads(text)
            cmd = j.get("cmd")
            if isinstance(cmd, list):
                for text in cmd:
                    # default apply patch
                    if "*** Begin Patch" in text and (next_message==None or
                                                      get_text(next_message).startswith(("Done!", "Success"))):
                        return text
        elif message.recipient == "functions.apply_patch":
            text = get_text(message)
            j = json.loads(text)
            cmd = j.get("input")
            if "*** Begin Patch" in cmd and (next_message==None or
                                             'successfully edited' in get_text(next_message)):
                # convert absolute path to relative path
                cmd = cmd.replace("*** Update File: /root/code/", "*** Update File: ")
                cmd = cmd.replace("*** Add File: /root/code/", "*** Add File: ")
                cmd = cmd.replace("*** Delete File: /root/code/", "*** Delete File: ")
                return cmd
        elif message.recipient == "python" and message.content.content_type == "code":
            return message.content.text
    except Exception as e:
        with open("/var/log/supervisor/xidai_get_patch_errors.log", "a") as f:
            if tool:
                f.write(f"{e} | {text}\n")
        return ""
    return ""


def chat_to_files_and_patches(
    convo: chat.Conversation,
    orig: dict[str, str | None],
    ignores_error: bool = False,
) -> tuple[dict[str, str | None], list[Patch], int]:
    files: dict[str, str | None] = {}
    patches: list[Patch] = []
    total_fuzz = 0
    messages = convo.messages
    if [m for m in messages if m.channel == "final" and extract_patch_text(get_patch_code(m))]:
        messages = [m for m in messages if m.channel == "final"]

    for i, message in enumerate(messages):
        if i!= len(messages) - 1:
            next_message = messages[i + 1]
        else:
            next_message = None
        # pass in next_message and orig for 1p tools to generate the patch
        text = get_patch_code(message, next_message, orig | files)
        if not text:
            continue

        patch_text = extract_patch_text(text)
        if not patch_text:
            continue
        try:
            paths = identify_files_needed(patch_text)
            for p in paths:
                if files.get(p) is None:
                    if p not in orig:
                        raise DiffError(f"File {p} is missing")
                    files[p] = orig[p]
            patch, fuzz = text_to_patch(
                patch_text, {k: v for k, v in files.items() if v is not None}
            )
            patches.append(patch)
            commit = patch_to_commit(patch, {k: v for k, v in files.items() if v is not None})
            files = apply_to_files(commit, files)
            total_fuzz += fuzz
        # except DiffError:
        except Exception as e:
            with open("/var/log/supervisor/xidai_apply_patch_errors.log", "a") as f:
                f.write(f"{e} | {paths} | {orig.keys()} | {patch_text}\n")
            if not ignores_error:
                raise
    return files, patches, total_fuzz


def consolidate_changes(convo: chat.Conversation, orig: Mapping[str, str | None]) -> Commit:
    updated_files, _, _ = chat_to_files_and_patches(convo, orig, ignores_error=True)
    return assemble_changes(orig, updated_files)
