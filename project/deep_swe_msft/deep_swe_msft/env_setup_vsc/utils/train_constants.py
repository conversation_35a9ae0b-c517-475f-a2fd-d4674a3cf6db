# Enable CaaS Networking
ENABLE_NETWORK = True

# Base Image Name
RFS_LANG_TO_IMAGE_NAME = {
    "py": "aio",
    "typescript": "aio",
    "cs": "aio",
    "java": "aio",
    "javascript": "aio",
    "js": "terminal-typescript",
}

# Base Image Name
# Reference base image in msbench
# https://dev.azure.com/devdiv/InternalTools/_git/MicrosoftSweBench?path=%2Fbenchmarks%2Fsetup%2Fsetupbench%2Fdataset.jsonl&_a=contents&version=GBmain
RFS_LANG_TO_CLEAN_IMAGE_NAME = {
    "py": "acrbuiltincaasglobalame.azurecr.io/ubuntu:22.04",  # not ready to use.
    "typescript": "acrbuiltincaasglobalame.azurecr.io/ubuntu:22.04",  # not ready to use.
    "javascript": "acrbuiltincaasglobalame.azurecr.io/ubuntu:22.04",  # not ready to use.
    "java": "acrbuiltincaasglobalame.azurecr.io/ubuntu:22.04",  # not ready to use.
}