import asyncio
import base64
import json
import os
import random
import re
import time
from typing import Any, Awaitable, Callable, Mapping

import structlog
from berry_rfs.mrfs_eval_utils import mrfs_run_test_with_test_result
from berry_rfs.utils import TestResult
from caas import ExecError, ServerError, TimedOutError
from caas.api import caas_api
from caas.commands import BashScript, UploadFile
from caas.terminal.api import TerminalSession
from caas_rfs.setup import get_repo_name
from deep_swe_msft.tools.vscode_copilot_tool import setup_vscutils
from deep_swe_msft.tools.utils import get_strawberry_ace_token
from mini.metrics import metrics
from mini.utils.simple_timer import simple_timer

DEFAULT_ATTEMPTS = 10
DEFAULT_TIMEOUT = 3600
MAX_OUTPUT_SIZE = 1_000_000
DEFAULT_SUPPORT_ROOT = "/tmp/repo_support"
HTTP_FILE_PATH = "https://orngcaas.blob.core.windows.net/data/swe-model-training-data/chenliang1-test/rrb_envsetup_all_pls"

logger = structlog.stdlib.get_logger(component=__name__)

REMOVE_SETUP = 0.9
REMOVE_RUN_TEST = 0.9


async def mrfs_setup_fn_coreutils(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    """
    Thin wrapper over regular RFS setup that installs oai coreutils
    """
    repo_root = "/root/code"
    await setup_repo_with_gt_metadata(terminal_session, datapoint["metadata"])
    await setup_vscutils(
        datapoint=None,
        session=terminal_session.session,
        workdir=repo_root,
        language=None,
    )

def clean_up_repo_name(repo_name: str) -> str:
    repo_name = repo_name.split("/")[-1]
    repo_name = re.sub(r"[^a-zA-Z0-9]", "", repo_name)
    return repo_name


def get_repo_directory(repo_name: str) -> str:
    return f"/testbed/{repo_name}"


def truncate_text(text: str) -> str:
    if len(text) <= MAX_OUTPUT_SIZE:
        return text
    return text[: MAX_OUTPUT_SIZE // 2] + " [TRUNCATED] " + text[-MAX_OUTPUT_SIZE // 2 :]


async def try_run_command(
    terminal_session: TerminalSession,
    command: str,
    seconds: int = 120,
    attempts: int = 1,
) -> tuple[bool, dict]:
    for attempt in range(attempts):
        start_time = time.time()
        try:
            result = await terminal_session.session.run(BashScript(command, timeout=seconds))
            text = result.decode("utf-8")
            return True, {
                "command": command,
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except TimedOutError as e:
            text = e.output.decode("utf-8")
            return False, {
                "command": command,
                "error": "TimedOutError",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except ExecError as e:
            if attempt + 1 < attempts:
                await asyncio.sleep(attempt * 5 + 1)
                continue
            text = e.output.decode("utf-8")
            return False, {
                "command": command,
                "error": "ExecError",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }
        except Exception as e:
            text = ""
            return False, {
                "command": command,
                "error": "Exception",
                "error_message": str(e),
                "output": truncate_text(text),
                "output_length": len(text),
                "time": int(time.time() - start_time),
                "attempt": attempt,
            }


async def setup_repo(
    terminal_session: TerminalSession,
    repo_root: str,
    repo_id: str,
    install_seconds: int = DEFAULT_TIMEOUT,
) -> dict[str, Any]:
    """Setup a repo for testing.

    Args:
        terminal_session: The terminal session to use.
        repo_root: The root directory for the repo.  It must start with / and not end in /.
        repo_id: The repo ID.

    Returns:
        A dict of status information for each step of the setup process.
    """
    assert repo_root.startswith("/") and not repo_root.endswith(
        "/"
    ), f"Invalid repo_root; must start with / and not end in /: {repo_root}"

    result = {
        "setup_done": False,
        "repo_id": repo_id,
    }
    with open("/var/log/supervisor/chenliang1_repo_setup.log", "a") as f:
        to_write = {"repo_root": repo_root, "repo_id": repo_id}
        f.write(json.dumps(to_write) + "\n")

    with simple_timer(
        "download_task_repo", log_fn=lambda x: [metrics.mean(k, v) for k, v in x.items()]
    ):
        ok, d = await try_run_command(
            terminal_session,
            f'rm -rf {repo_root}; mkdir -p {repo_root}; curl -k -sL "{HTTP_FILE_PATH}/{repo_id}.tar.gz?{get_strawberry_ace_token()}" | tar -xz -C {repo_root}',
            seconds=install_seconds,
            attempts=DEFAULT_ATTEMPTS,
        )

        # remove the setup and test files with some probability
        if random.random() < REMOVE_SETUP:
            ok_remove_setup, d = await try_run_command(
                terminal_session,
                f"rm -f {repo_root}/*setup*",
                attempts=DEFAULT_ATTEMPTS,
            )
            ok_ls, d = await try_run_command(
                terminal_session,
                f"ls {repo_root}",
                attempts=DEFAULT_ATTEMPTS,
            )
            with open("/var/log/supervisor/chenliang1_after_remove_setup.log", "a") as f:
                f.write(f"{ok_remove_setup} {ok_ls} {d}\n")

        if random.random() < REMOVE_RUN_TEST:
            ok_remove_test, d = await try_run_command(
                terminal_session,
                f"rm -f {repo_root}/*test*",
                attempts=DEFAULT_ATTEMPTS,
            )
            ok_ls, d = await try_run_command(
                terminal_session,
                f"ls {repo_root}",
                attempts=DEFAULT_ATTEMPTS,
            )
            with open("/var/log/supervisor/chenliang1_after_remove_runtest.log", "a") as f:
                f.write(f"{ok_remove_test} {ok_ls} {d}\n")

    result["copy_repo"] = d

    if not ok:
        metrics.sum("download_task_repo_failed", 1.0)
        return result

    # give model permission to access the repo and the root directory
    ok, d = await try_run_command(terminal_session, f"chmod -R +rwx {repo_root}")
    ok, d = await try_run_command(terminal_session, "chmod -R a+rwx /")
    if not ok:
        with open("/var/log/supervisor/chenliang1_permission.log", "a") as f:
            logger.error("Failed to give model permission to access the root", error=d)

    result["setup_done"] = True
    metrics.sum("setup_task_repo_done", 1.0)
    return result


async def setup_repo_with_gt_metadata(
    terminal_session: TerminalSession,
    gt_metadata: dict[str, Any],
) -> dict[str, Any]:
    repo_id = gt_metadata["repo_id"]
    repo_name = clean_up_repo_name(gt_metadata.get("repo_name", "workdir"))
    repo_root = "/root/code"

    out = await setup_repo(
        terminal_session,
        repo_root=repo_root,
        repo_id=repo_id,
    ) | {"repo_root": repo_root}

    with open("/var/log/supervisor/chenliang1_repo_setup_result.log", "a") as f:
        f.write(json.dumps(out) + "\n")

    return out


async def setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    result = await setup_repo_with_gt_metadata(terminal_session, datapoint["metadata"])
    if not result.get("setup_done"):
        logger.error("mrfs_setup.setup_fn failed", result=result)
        raise Exception("Setup failed")
