from functools import partial
from typing import Any

import berry
import caas_autograding.grader as caas_graders
import chz
import structlog
from berry.function_wrapper import FunctionWrapper
from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from caas_swe_bench_train import SWEBenchDatapoint
from caas_swe_bench_train import setup_fn as caas_swe_bench_train_setup_fn
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.datasets.setup import setup_fn_coreutils
from deep_swe.graders.thoroughness_grader import ThoroughnessGrader
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig  # type: ignore
from deep_swe_msft.vsc_data.system_prompt import VSC_MODEL_IDENTITY, VSC_SYSTEM_PROMPT
from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.caas_resource_config import (
    CaasContainerResourceConfig,
)
from deep_swe_msft.env_setup_vsc.datasets.mrfs_setup import mrfs_setup_fn_coreutils
from deep_swe_msft.env_setup_vsc.utils.train_constants import (
    ENABLE_NETWORK,
    RFS_LANG_TO_CLEAN_IMAGE_NAME,
    RFS_LANG_TO_IMAGE_NAME,
)
from deep_swe_msft.vsc_graders.cotograder_utils import COTOGRADER_RENDERER, COTOGRADER_CHZ_ARGV

from deep_swe_msft.tools.caas_mix_tool_vsc import VSCMixToolConfig
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from qstar.curriculums.variant_producer import VarDiscountingVariantProducer, VariantProducer
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.graders.taskgen_utils import BerryGraderConfig, TokenCompleterGraderService
from qstar.presets.chz_utils import IsOverride, override
from qstar.samplers import BaseSampler
from token_completer import TokenCompleter

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig, IsOverride):
    caas_cpu_limit: float = 2.0  # Increase to 2 if actually using VSCode
    caas_memory_limit: int = 2
    caas_container_image: str = "vscode-agent"  # Has more pip dependencies!
    caas_idle_ttl: int = 10 * 60
    enable_network_after_setup: bool = False
    caas_endpoint: str = CAAS_ENDPOINT


@chz.chz
class DeepSWEVardiscProducer(VarDiscountingVariantProducer, IsOverride):
    # Non-standard settings designed for the low data-efficiency, high compute-efficiency regime
    reward_distribution_power: float = 1.0
    min_reward_multiplier: int = 64
    max_reward_multiplier: int = 1024
    num_reward_multipliers: int = 1


DEFAULT_PREPEND_PROBABILITY = 0.25
DEFAULT_ENFORCE_PROBABILITY = 0.9


def _make_tool_configs(
    container_tool_config: VSCMixToolConfig,
    retrieval_tool_config: BerryJamRetrievalToolConfig,
    enable_retrieval: bool = False,
) -> tuple[ToolConfig, ...]:
    if enable_retrieval:
        return container_tool_config, retrieval_tool_config
    return (container_tool_config,)


@chz.chz
class DeepSWEDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs)

    # Defaults to an empty container (with oai coreutils installed).
    # Most tasks will probably want to override this with custom setup.
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(CaasResourceConfig(setup_fn=setup_fn_coreutils),)
    )
    max_num_yields: int = 400
    variant_producer: VariantProducer | None = override(DeepSWEVardiscProducer)


def make_multistage_grader(
    grader_argvs: list[list[str]] | None = None,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
) -> caas_graders.MultiStageGraderWithAuxReinforcements:
    # NB: we can freely set |channels_for_answer| here. See NOTE [ DeepSWEDatasetConfig.channel_format ]
    if grader_argvs is None:
        grader_argvs = []
    grader_argvs = list(grader_argvs)

    blueprint = chz.Blueprint(caas_graders.MultiStageGraderWithAuxReinforcements)
    for ii, grader_argv in enumerate(grader_argvs):
        argv: list[str] = []
        for arg in grader_argv:
            if not arg.startswith(("=", ".")):  # allow wildcards
                arg = "." + arg
            argv.append(f"graders.{ii}{arg}")
        blueprint = blueprint.apply_from_argv(argv)
    if channels_for_answer:
        channels_for_answer_arg = "channels_for_answer=" + ",".join(
            str(c) for c in channels_for_answer
        )
        blueprint = blueprint.apply_from_argv(["..." + channels_for_answer_arg])
    return blueprint.make()


def make_envsetup_multistage_grader(
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
) -> MultiStageGrader:
    grader_argvs = [
        [
            "=deep_swe_msft.vsc_graders.env_setup_following_grader:EnvSetupFollowCotograder",
            *COTOGRADER_CHZ_ARGV,
            f"grader_max_tokens={16384}",
        ]
    ]
    return make_multistage_grader(grader_argvs, channels_for_answer)


@chz.chz
class EnvSetupPythonDatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngcresco")
    dataset_id: str = "data.chenliang1.swe.rrb_py_envsetup_train_1700.u06212025_v2"
    grader: Grader[HarmonyCompletionDatapoint] = override(partial(make_envsetup_multistage_grader))
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],  # use aio for now, ubuntu image is being fixed.
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    tool_configs: tuple[ToolConfig, ...] = chz.field(
        default=(VSCMixToolConfig(internet_policy="enabled" if ENABLE_NETWORK else "no_access"),)
    )

    model_identity_str: str = VSC_MODEL_IDENTITY
    instructions: str = VSC_SYSTEM_PROMPT

    # """
    # NOTE: sample_completer cannot currently be overriden normally because it gets overwritten by qstar.common.defaults.
    # For more context see thread:
    # """
    # @chz.init_property
    # def sample_completer(self) -> berry.SampleCompleter:
    #     return PadawanSampleCompleter()

@chz.chz
class EnvSetupTypescriptDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.rrb_ts_envsetup_train_600.u06212025_v2"
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["typescript"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class EnvSetupJavascriptDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.rrb_js_envsetup_train_600.u06212025_v2"
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["javascript"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class EnvSetupJavaDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.rrb_java_envsetup_train_900.u06212025_v2"
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["java"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )