import berry.preset_utils
import deep_swe_msft.env_setup_vsc.datasets.configs as configs
from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe.datasets.config_utils import chz_path

BASELINE_DATASET_CONFIGS = [
    (configs.EnvSetupPythonDatasetConfig, []),
]

DATASET_CONFIGS = BASELINE_DATASET_CONFIGS

format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)


train_baseline = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in BASELINE_DATASET_CONFIGS
    ],
    format,
)


train = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in DATASET_CONFIGS
    ],
    format,
)
