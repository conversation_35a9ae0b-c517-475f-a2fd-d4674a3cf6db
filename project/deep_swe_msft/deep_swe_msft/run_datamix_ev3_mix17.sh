# for i in {0..47}; do b restart -p damajercak-mix16-a1-0722-rollout-worker-w$i; done
dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="mix17-pdw2-ev3-itc-256-mixed-spi-gpt5-lr1e-5-run-$dt"
# CKPT="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
CKPT="az://orngscuscresco/twapi/mini/e/damajercak-mix17-pdw2-ev3-itc-256-mixed-spi-gpt5-lr1e-5-run-20250827-004219/policy/step_000060/"
# Enforcement settings for datasets 0-6
ENFORCE_PROB=0.3
ENFORCE_FREQ=5
JUICE=128

CMD=(
beam python --use-cwd -m qstar.run_experiment nostrict
name=$EXPERIMENT_NAME
seed=20250724

skip_validate_config=True

# Policy settings
:berry_models.scallion:d64_chicken_80g_bf16
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0}'
policy.initial_checkpoint=$CKPT
policy.initial_checkpoint_mode=resume
policy.n_gpus=224
policy.is_multimodal=True
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_tools_v2
...tools_format_version=v2

# Systems settings
:qstar.presets.common:longer_timeout
peashooter.sampling_use_ev3=True
peashooter.timeout_seconds.stalled_datapoint=7200
timeout.default=None

# Model settings
policy.n_ctx=131072
defaults.n_ctx=131072
...container_tool_config="mix_tool"
...sampler='deep_swe_msft.padawan_data.sampler:PadawanSampler'
...interleave_channels.0=analysis
...interleave_channels.1=commentary
...harmony_constrained_sampling=True
# defaults.sample_completer="deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter"
optimizer.hparam_scaler.lr_per_instance_d16=1e-5
...save_every=5

# Dataset configs!
:deep_swe_msft.presets:train_padawan_v2_mix17
...dataset_container=orngscuscresco
...max_num_yields=256
...average_reward_minimum=0.001

berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=100

# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
defaults.inverse_token_cost=1024
defaults.instances_per_batch=32
defaults.target_samples_per_instance=8

# Enable the COMMENTARY channel
defaults.channel_config.channels="analysis,commentary,final"
defaults.sampler.interleave_channels.0=analysis
defaults.sampler.interleave_channels.1=commentary
...tool_channel="commentary"

batch_completer.n_batches_in_flight=10 # number of batches in flight
peashooter.num_instance_workers=48 # number of instance workers
peashooter.num_sampling_processes=16 # number of sampling processes per instance worker
peashooter.sampling_concurrency=6 # concurrency sampling threads per process

# Dataset configs
defaults.instance_completer=qstar.instance_completers:VariantsInstanceCompleter
defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer
...instance_objective.aux_objective_0=caas_autograding.tool_penalty_objective:ToolPenaltyObjective
...target_samples_per_instance=8

# webdev task
# batcher.curriculum.training_datasets.1.dataset.inverse_token_cost_multiplier=16
# batcher.curriculum.training_datasets.1.dataset.override_target_samples_per_instance=8
# batcher.curriculum.training_datasets.2.dataset.inverse_token_cost_multiplier=16
# batcher.curriculum.training_datasets.2.dataset.override_target_samples_per_instance=8
# batcher.curriculum.training_datasets.3.dataset.inverse_token_cost_multiplier=16
# batcher.curriculum.training_datasets.3.dataset.override_target_samples_per_instance=8

# setup task - bgdb
# batcher.curriculum.training_datasets.4.dataset.inverse_token_cost_multiplier=8
# setup task - dependency
# batcher.curriculum.training_datasets.5.dataset.inverse_token_cost_multiplier=8

# setup task - repo setup
# batcher.curriculum.training_datasets.5.dataset.inverse_token_cost_multiplier=8

# swe-bench-hard-v2 repair
# batcher.curriculum.training_datasets.5.dataset.inverse_token_cost_multiplier=8
# batcher.curriculum.training_datasets.5.dataset.override_target_samples_per_instance=8
batcher.curriculum.training_datasets.5.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
batcher.curriculum.training_datasets.5.dataset.sample_completer.enforce_prob=0
batcher.curriculum.training_datasets.5.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ
batcher.curriculum.training_datasets.5.dataset.instance_completer='prbot_msft.instance_completers:SWEBenchHardGroupedSimpleInstanceCompleter'
batcher.curriculum.training_datasets.5.dataset.instance_completer.instance_objective.main_objective.c=1
batcher.curriculum.training_datasets.5.dataset.instance_completer.instance_objective.main_objective.d=1
batcher.curriculum.training_datasets.5.dataset.instance_completer.grouped_sample_allocation_args.sample_group_size=8
batcher.curriculum.training_datasets.5.dataset.instance_completer.grouped_sample_allocation_args.completion_wait_timeout=2400
batcher.curriculum.training_datasets.5.dataset.instance_completer.grouped_sample_allocation_args.grading_wait_timeout=1800
batcher.curriculum.training_datasets.5.dataset.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer
batcher.curriculum.training_datasets.5.dataset.instance_completer.target_samples_per_instance=8
batcher.curriculum.training_datasets.5.dataset.instance_completer.sample_roller=berry.sample_roller:SampleCompleterSampleRoller
batcher.curriculum.training_datasets.5.dataset.instance_completer.sample_roller.tail_cutoff_fraction=0.6
batcher.curriculum.training_datasets.5.dataset.instance_completer.sample_roller.tail_cutoff_seconds=300
...graders_always_correct=True
...peer_grader_minimum_group_size=5

# swe-bench-train
batcher.curriculum.training_datasets.6.dataset.instance_completer="deep_swe_msft.swe_bench_train_v2_padawan_v2.dataset_config:SWEBenchV2InstanceCompleter"
batcher.curriculum.training_datasets.6.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
batcher.curriculum.training_datasets.6.dataset.sample_completer.enforce_prob=$ENFORCE_PROB
batcher.curriculum.training_datasets.6.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ
batcher.curriculum.training_datasets.6.dataset.instance_completer.num_initial_samples=8
batcher.curriculum.training_datasets.6.dataset.instance_completer.min_initial_samples_per_variant=8

# swe-bench-hard-v1 repair
# batcher.curriculum.training_datasets.7.dataset.inverse_token_cost_multiplier=16
# batcher.curriculum.training_datasets.7.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
# batcher.curriculum.training_datasets.7.dataset.sample_completer.enforce_prob=$ENFORCE_PROB
# batcher.curriculum.training_datasets.7.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ

# swe-bench-hard-v1 repair exec - python
# batcher.curriculum.training_datasets.8.dataset.inverse_token_cost_multiplier=16
# batcher.curriculum.training_datasets.8.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
# batcher.curriculum.training_datasets.8.dataset.sample_completer.enforce_prob=$ENFORCE_PROB
# batcher.curriculum.training_datasets.8.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ

...dataset.variant_producer=CompositeVariantProducer
...dataset.variant_producer.variant_producers.0=VarDiscountingVariantProducer
...dataset.variant_producer.variant_producers.0.override_reward_multiplier=${JUICE}

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=swe-main-run
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}" 2>&1 | tee -a swe-train-$dt.log
