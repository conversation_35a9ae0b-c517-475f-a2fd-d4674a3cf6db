import json
from abc import abstractmethod
from typing import Any, Sequence, final

import structlog

import chz
from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)
from deep_swe_msft.padawan_graders.system_prompt_following_grader_hard import SPFollowHardCotograder, _extract_model_actions_with_token_limit
from deep_swe_msft.padawan_graders.system_prompt_following_prompts import _build_rubric

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz(typecheck=True)
class SPFollowHardV2Cotograder(SPFollowHardCotograder):
    grader_reward_multiplier: int = chz.field(default=32)
    reward_name: str = "system_prompt_cotograder"
    use_sp_sbh_hard: bool = False

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(conversation, self.renderer, token_limit=256)
        spfollow_rubric = SP_FOLLOWING_V2_RUBRIC
        if self.use_sp_sbh_hard:
            spfollow_rubric = _build_rubric(rubric_id="SP_FOLLOWING_RUBRIC_EASY")
        return spfollow_rubric.replace("{conversation_messages}", conversation_messages)

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:

        with open("/var/log/supervisor/chenliang1_spfv2_grader_results.log", "a") as f:
            for i in range(len(j)):
                criteria_key = f"criteria{i + 1}"
                grade = j.get(criteria_key, {}).get("satisfied", "N/A")
                explanation = j.get(criteria_key, {}).get("explanation", "N/A")
                f.write(f"criteria {i} grade: {grade}, explanation: {explanation}\n")
            f.write("\n")

        return GradeFnOutput(
            correct=all(j[f"criteria{i + 1}"]["satisfied"] is True for i in range(len(j)))
        )


SP_FOLLOWING_V2_RUBRIC = """
You are given a model solution. Your task is to verify whether model completed the task as expected in the rubric. The model messages may be truncated for efficiency and focus on the action that the model took to complete the task, e.g., function calls. 
You should not consider the system prompt as a part of the model solution.

## Criteria

1. criteria1: **Report Progress**
    - Call <functions.report_progress> at least twice (start & end), and >=3 times if >20 turns (with at least one middle call) to report progress regularly.
    - First call: markdown checklist with incomplete items.
    - Last call: markdown checklist with only completed items - no pending items.
    - Last call must not contain excuses or justifications for any pending items.
    - Any violation of these rules fails this criterion.
    
2. criteria2: **PR Description**
    - At the end of the fix, the model should write a PR description for the changes made to fix the issue in format: <pr_title> ... </pr_title> <pr_description> ... </pr_description>.
    - Both <pr_title> ... </pr_title> and <pr_description> ... </pr_description> must be present and follow the format exactly.
    - PR description should not appear in report progress, and should be in pure text and the last message in the conversation.
    - Any violation of these rules fails this criterion.

3. criteria3: **Dependency**
   - If missing dependencies, the model should at least try to install them, for example `pip install pytest` or `npm install <package>`. 
   - If there are no missing dependencies from tool responses, the solution should not install anything.

4. criteria4: **Linting, building and testing**
   - The solution should try to lint, build and test the code according to the system prompt.
   - These steps may be skipped only if they are clearly not relevant or possible (e.g., task does not involve code, required tooling is unavailable, or the environment does not supp

5. criteria5: **Code Style**
    - The solution should follow the code style guidelines specified in the system prompt.

6. criteria6: **Function Call Format**
    - The model should never call unsupported commands in <functions.str_replace_editor>. str_replace_editor only supports the following fields in "command" argument: ["view", "create", "str_replace", "insert", "undo_edit"], for example {"command":"grep ...","path":"src"} is not a valid command.
    - The model should never include 'bash' in the `command` argument in <functions.bash>.
    - The model should never have argument errors in function calls, e.g., missing required arguments or using unsupported arguments.
    - Any violation of these rules fails this criterion.

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria6". Example is shown below. 
The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification. 
Example:
{
   "criteria1": {
      "satisfied": true,
      "explanation": "the model install dependencies correctly or no dependencies need to be installed."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model run its own tests."
   }
   ...
}
""".strip()
