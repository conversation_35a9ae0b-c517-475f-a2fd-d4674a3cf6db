import dataclasses
from datetime import datetime
import json
from abc import abstractmethod
from typing import Any, Sequence, final

import structlog

import chz
from berry.sample import GraderError
from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders import taskgen_utils
from qstar.graders.grader import Grader
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    CotogradeRequest,
    CotograderMixin,
    GradeFnOutput,
)
from qstar.sample_completers.sample_completer import SampleExecutionContext

from deep_swe_msft.padawan_graders.system_prompt_following_prompts import _build_rubric
from prbot_msft.configs.utils import include_install_for_easy_langs

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]

logger = structlog.stdlib.get_logger(component=__name__)

def log_with_timestamp(message: str):
    with open(f"/var/log/supervisor/deepswe_system_prompt_following_grader.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}]: {message}\n")

# NB: tweak of caas_swe_bench_train_v2.cotograders._JsonCotograder to allow for sample mutation
@chz.chz(typecheck=True)
class JsonCotograderWithSample(Grader[HarmonyCompletionDatapoint], CotograderMixin):
    grader_reward_multiplier: int
    reward_name: str

    @property
    def accepts_invalid(self) -> bool:
        return True

    @abstractmethod
    def make_prompt(self, sample: SampleWithCompletion) -> str: ...

    @classmethod
    @abstractmethod
    def reward(cls, grade: dict[str, Any]) -> float: ...

    @classmethod
    @abstractmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool: ...

    @abstractmethod
    def grade_json_with_sample(self, sample: SampleWithCompletion, j: dict[str, Any]) -> GradeFnOutput: ...

    def ensure_ranking_data(self, sample: SampleWithCompletion):
        if "sp_follow_grader_ranking_info_added" not in sample.ephemeral_metadata:
            sample.ephemeral_metadata["ranking_strict"] = sample.ephemeral_metadata.get("ranking_strict", []) + [ False ]
            sample.ephemeral_metadata["sp_follow_grader_ranking_info_added"] = 1
        return sample

    @final
    def grade(self, sample: SampleWithCompletion, answer: str) -> GradeFnOutput:
        try:
            return self.grade_json_with_sample(sample, json.loads(answer))
        except Exception as e:
            logger.warning("Error parsing grader answer", error=str(e), answer=answer, grader=self.reward_name)
            self.ensure_ranking_data(sample)
            return GradeFnOutput(correct=False, error=e)

    @final
    def _grade_batch_inner(
        self,
        samples: Sequence[SampleWithCompletion],
        sample_execution_context: SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[HarmonyCompletionDatapoint]]:
        def _build_sample(sample, grade) -> types.SampleWithGrade[HarmonyCompletionDatapoint]:
            if not sample.valid:
                return sample.with_correctness(reward_name=self.reward_name, is_correct=self.always_correct)

            try:
                reward = self.reward(grade)
                logged_additional_metrics = grade.get("grade_fn_metadata", {}).get("logged_additional_metrics", {})
                logger.info(f"score: {reward}", grader=self.reward_name)
                return sample.with_reward(
                    reward_name=self.reward_name,
                    is_correct=self.is_correct(grade),
                    reward=reward,
                    given_answer=grade["answer"],
                    additional_metadata={
                        "grader": {
                            "prompt": taskgen_utils.render_grader_system_suffix(grade["prompt_convo"]),
                            "response": grade["response"],
                            "score": reward,
                            **logged_additional_metrics,
                        }
                    },
                    additional_metrics=logged_additional_metrics,
                )
            except Exception as e:
                logger.error("Error building sample with grade", error=str(e), grader=self.reward_name)
                grader_error = GraderError.from_exception(e)
                return dataclasses.replace(
                    self.ensure_ranking_data(sample),
                    errors_blamed_on_system=sample.errors_blamed_on_system | {"grading_bus_error"},
                ).with_grader_error(grader_error)

        try:
            def make_grade_function(self, s):
                instance_id = s.gt_datapoint.metadata.get("instance_id", "unknown_instance")
                log_with_timestamp(f"{instance_id} {s.seed} make_grade_function(s)")
                return lambda answer: self.grade(sample=s, answer=answer)

            grades = [None]*len(samples)
            valid_tuples = [ (idx, s) for idx, s in enumerate(samples) if s.valid ]
            if valid_tuples:
                valid_indices, valid_samples = zip(*valid_tuples)
                valid_grades = self.grade_prompt_texts(
                    [CotogradeRequest(self.make_prompt(s), s.seed, make_grade_function(self, s)) for s in valid_samples],
                    reward_multiplier=self.grader_reward_multiplier, attempts=5
                )
                for idx, grade in zip(valid_indices, valid_grades):
                    grades[idx] = grade

        except Exception as e:
            logger.error("Error grading samples", error=str(e), grader=self.reward_name)
            grader_error = GraderError.from_exception(e)
            return [
                dataclasses.replace(
                    self.ensure_ranking_data(sample),
                    errors_blamed_on_system=sample.errors_blamed_on_system | {"grading_bus_error"},
                ).with_grader_error(grader_error)
                for sample in samples
            ]

        log_with_timestamp(f"GRADE: {grades=}")
        built_samples = [_build_sample(sample, grade) for sample, grade in zip(samples, grades, strict=True)]
        return built_samples

@chz.chz(typecheck=True)
class SPFollowCotograder(JsonCotograderWithSample):
    grader_reward_multiplier: int = chz.field(default=32)
    reward_name: str = "system_prompt_cotograder"
    always_correct: bool = chz.field(default=False) # used in conjunction with relative grading

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(conversation, self.renderer, token_limit=256)

        rubric_id = sample.gt_datapoint.metadata["variant_sp_rubric_id"]
        instance_id = sample.gt_datapoint.metadata.get("instance_id", "unknown_instance")
        log_with_timestamp(f"[{instance_id}] {sample.seed} Making prompt for rubric_id: {rubric_id}")

        sp_following_rubric = _build_rubric(
            rubric_id=rubric_id,
        )
        return sp_following_rubric.replace("{conversation_messages}", conversation_messages)

    def grade_json_with_sample(self, sample: SampleWithCompletion, j: dict[str, Any]) -> GradeFnOutput:
        instance_id = sample.gt_datapoint.metadata.get("instance_id", "unknown_instance")

        log_with_timestamp(f"{instance_id} {sample.seed} Grading JSON: {json.dumps(j, indent=2)}")
        keys = [ key for idx in range(len(j)) for key in (f"criteria{1+idx}",) if key in j ]
        criteria_lst = [ j[key]["satisfied"] is True for key in keys ]
        logged_additional_metrics = { f"{self.reward_name}.{key}":val for idx, (key, val) in enumerate(zip(keys, criteria_lst)) }

        assert "sp_follow_grader_ranking_info_added" not in sample.ephemeral_metadata
        sample.ephemeral_metadata["ranking_info_list"] = sample.ephemeral_metadata.get("ranking_info_list", []) + [ sum(criteria_lst) ]
        # strict criterion: report_progress, pr_description
        sample.ephemeral_metadata["ranking_strict"] = sample.ephemeral_metadata.get("ranking_strict", []) + [ all(criteria_lst[:2]) ]
        sample.ephemeral_metadata["sp_follow_grader_ranking_info_added"] = 1

        log_with_timestamp(f"{instance_id} {sample.seed} Sample ephemeral metadata after grading: {sample.ephemeral_metadata['ranking_info_list']}")

        return GradeFnOutput(correct=self.always_correct or (bool(criteria_lst) and all(criteria_lst)),
                             extra_metadata={'logged_additional_metrics': logged_additional_metrics })

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]


def _truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"

def _extract_model_actions_with_token_limit(convo, renderer=None, token_limit: int = 512):
    """Extract model actions with token-based truncation"""
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            content_str = str(message.content)
            
            # If renderer available, use token-based truncation
            if renderer:
                truncated_content  = _truncate_string(renderer, content_str)
            else:
                # Fallback to character-based truncation
                truncated_content = content_str[:token_limit]
                
            convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {truncated_content}\n"
    
    return convo_messages

def _extract_model_actions(convo):
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {str(message.content)[:512]}\n"
        # else:
        #     convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {str(message.content)[:512]}\n"

    return convo_messages
