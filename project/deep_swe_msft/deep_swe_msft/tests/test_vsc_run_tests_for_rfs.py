import asyncio
import json
import traceback

import blobfile as bf
import pytest
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from caas.commands import UploadFile, RawBashScript

from deep_swe_msft.tests.vsc_helpers import get_tool, invoke_tool, exist_text
from deep_swe_msft.tools.utils import CRESCO_STORAGE_NAME
from deep_swe_msft.tools.vscode_copilot_tool import (
    LANGUAGE_UNIFORMS,
    new_container,
    debug_startup,
)
from mini.metrics import metrics_init
from deep_swe_msft.tools.utils import CAAS_ENDPOINT

pytestmark = pytest.mark.asyncio

def _get_samples(path: str) -> dict:
    print("Loading sample data")
    with bf.BlobFile(
        path,
        "r",
    ) as f:
        samples = [json.loads(line) for line in f]
    print(f"Loaded sample data, num_samples={len(samples)}")
    return samples

async def _get_container(sample: dict) -> CaasContainer:
    print("Creating CAAS container")
    container = await new_container(CAAS_ENDPOINT, "aio")
    container.terminal_session.session.start_keepalive_task(keepalive_interval=30)
    print("CAAS container created, setting up VSC utils")

    language = "python"
    if "metadata" in sample:
        metadata = sample["metadata"]
        language = metadata.get("lang", None) or metadata.get("top_language", None) or "python"
    language = language.lower()
    if language in LANGUAGE_UNIFORMS:
        language = LANGUAGE_UNIFORMS[language]
    if language == "python":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "javascript":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.javascript.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "java":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.java.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "csharp":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "typescript":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.typescript.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    else:
        raise ValueError(f"Unsupported language: {language}")

    res = await mrfs_setup_fn_coreutils(
        datapoint=sample,
        terminal_session=TerminalSession(container.caas_session, endpoint=CAAS_ENDPOINT),
    )

    assert res["setup_done"] is True, f"Expected setup to complete successfully, but got {res=}"

    print("CAAS container created")
    return container


async def test_run_tests():
    metrics_init(config=dict())
    samples = _get_samples(f"az://{CRESCO_STORAGE_NAME}/data/luw/rfs_data/train_data/mrfs_caas/latest/python/train/rfs/3p/processed_train.jsonl")
    python_sample = [s for s in samples if "metadata" in s and s["metadata"]["repo_id"] == "0ddfell0w__cp_rfs_51c48082-ae27-4902-8b16-88e9a74673a6"][0]
    python_container = await _get_container(python_sample)
    python_tool = get_tool(python_container, 10000)
    print(f"Tool is ready.")

    # Run tests
    run_result = await invoke_tool(
        python_tool, "runTests", { "files": [] }
    )
    assert exist_text("be/test/test_pokerMove.py::PokerMoveTest::test_move_strength_ordering failed:", run_result), f"Expected test failure not found in {run_result=}"

    run_result = await invoke_tool(
        python_tool, "runTests", { "files": ["/root/code/be/test/test_kindMove.py"] }
    )
    assert exist_text("No test failures were found.", run_result), f"Expected no test failures in {run_result=}"

    run_result = await invoke_tool(
        python_tool, "runTests", { "files": ["/root/code/be/test/test_round.py"] }
    )
    assert exist_text("be/test/test_round.py::RoundTest::test_append_first_move_kind_move failed:", run_result), f"Expected test failures not found in {run_result=}"

    # Create a long-running test to ensure the timeout works
    long_test = """import unittest
import time
from ..poker_move import PokerMove
from .utils import CustomAssertions
INVALID = "3S 4S 5S 6S 2H"
STRAIGHT = "3S 4C 5D 6S 7H"
FLUSH = "3S JS QS KS 7S"
FULL_HOUSE = "3S 3H 3C 7D 7C"
FOUR_OF_A_KIND = "3S 3H 3C 3D 7S"
STRAIGHT_FLUSH = "7H 6H 5H 4H 3H"
class PokerMoveTest(unittest.TestCase, CustomAssertions):
  def test_move_strength_ordering(self):
    time.sleep(20)
    self.assert_in_order_from_string(PokerMove, [
      INVALID,
      STRAIGHT,
      FLUSH,
      FULL_HOUSE,
      FOUR_OF_A_KIND,
      STRAIGHT_FLUSH,
    ])
if __name__ == '__main__':
  unittest.main()
"""
                    
    await python_container.caas_session.run(UploadFile(
        path="/root/code/be/test/test_long_run.py",
        data=long_test.encode(),
    ))

    long_run_result = await invoke_tool(
        python_tool, "runTests", { "files": ["/root/code/be/test/test_long_run.py"] }
    )
    assert exist_text("VSCode Copilot tool error: Command failed because it timed out.", long_run_result), f"Expected long-running test failure not found in {long_run_result=}"

    await python_container.caas_session.run(RawBashScript(f"rm /root/code/be/test/test_long_run.py"))
    run_all_result = await invoke_tool(
        python_tool, "runTests", { "files": [] }
    )
    assert exist_text("be/test/test_deck.py::DefaultDeckTest::test_get_hands_3_players_3D_excluded failed:", run_all_result), f"Expected test failures not found in: {run_all_result=}"
    print(f"Run all runTests passed.")
    await python_container.terminal_session.session.stop_keepalive_task()
    await python_container.terminal_session.close()

async def test_run_tests_for_all():
    metrics_init(config=dict())
    python_dataset = "az://orngcaas/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/python/train/rfs/3p/processed_train.jsonl"
    javascript_dataset = "az://orngcaas/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/javascript/train/rfs/3p/processed_train_add_configs.jsonl"
    typescript_dataset = "az://orngcaas/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/typescript/train/rfs/3p/processed_train_add_configs.jsonl"

    samples = _get_samples(python_dataset)[:5]
        # [s for s in _get_samples(typescript_dataset)[:5] if s["metadata"]["vscode_extensions"]] + \
        # [s for s in _get_samples(javascript_dataset)[:5] if s["metadata"]["vscode_extensions"]]

    tasks = [asyncio.create_task(_run_single_test(sample)) for sample in samples]
    run_results = await asyncio.gather(*tasks, return_exceptions=True)
    run_results_by_id = {s["unique_id"]: r for s, r in zip(samples, run_results)}
    failures = []
    for unique_id, run_result in run_results_by_id.items():
        if isinstance(run_result, Exception):
            failures.append(unique_id)
            continue
        if exist_text("No test failures were found.", run_result):
            failures.append(unique_id)
            continue
        if exist_text("VSCode Copilot tool error", run_result):
            failures.append(unique_id)
            continue
        if not exist_text("<testResult passed=false>", run_result):
            failures.append(unique_id)
            continue
    assert len(failures) == 0, f"Expected all run_tests to succeed, but got {len(failures)} failures: {failures}"

async def test_run_tests_for_typescript():
    metrics_init(config=dict())
    dataset = "az://orngcaas/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/typescript/train/rfs/3p/processed_train_add_configs.jsonl"
    sample = [s for s in _get_samples(dataset) if s["metadata"]["vscode_extensions"]][0]
    result = await _run_single_test(sample)
    if isinstance(result, Exception):
        tb = getattr(result, "__traceback__", None)
        stack = "".join(traceback.format_exception(type(result), result, tb))
        raise Exception(f"Expected run_tests to succeed, but got exception: {result} ({type(result)}).\nTraceback:\n{stack}")
    assert exist_text("<testResult passed=false>", result), f"Expected test failures in {result=}"

async def test_run_tests_for_javascript():
    metrics_init(config=dict())
    dataset = "az://orngcaas/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/javascript/train/rfs/3p/processed_train_add_configs.jsonl"
    sample = [s for s in _get_samples(dataset) if s["metadata"]["vscode_extensions"]][0]

    result = await _run_single_test(sample)
    if isinstance(result, Exception):
        tb = getattr(result, "__traceback__", None)
        stack = "".join(traceback.format_exception(type(result), result, tb))
        raise Exception(f"Expected run_tests to succeed, but got exception: {result} ({type(result)}).\nTraceback:\n{stack}")
    assert exist_text("testResult passed=false", result) and exist_text("<testFailure", result) and exist_text("path=", result), f"Expected test failures in {result=}"

async def _run_single_test(sample):
    container = await _get_container(sample)
    python_tool = get_tool(container, 120000)
    print(f"Tool is ready.")
    res = await invoke_tool(
        python_tool, "runTests", { "files": [] }
    )
    await container.terminal_session.session.stop_keepalive_task()
    await container.terminal_session.close()
    return res
