import asyncio
import os
import json
import re

import pytest
import blobfile as bf

from deep_swe_msft.tools.utils import CRESCO_STORAGE_NAME, CAAS_ENDPOINT
from deep_swe_msft.tools.vscode_copilot_tool import (
    LANGUAGE_UNIFORMS,
    exec,
    install_vsc_extensions,
    new_container
)
from mini.metrics import metrics_init
from deep_swe_msft.tests.vsc_helpers import get_tool, invoke_tool, exist_text
from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn
from caas_tool.caas_container import CaasContainer
from caas.terminal.api import TerminalSession


pytestmark = pytest.mark.asyncio

def _get_sample() -> dict:
    print("Loading sample data")
    with bf.BlobFile(
        f"az://{CRESCO_STORAGE_NAME}/data/luw/swe-bench-train-vsc/test_train/swe_bench_train_updated.jsonl",
        "r",
    ) as f:
        samples = [json.loads(line) for line in f]
    print(f"Loaded sample data, num_samples={len(samples)}")
    return samples[0]


async def _get_container(sample: dict) -> CaasContainer:
    print("Creating CAAS container")
    container = await new_container(CAAS_ENDPOINT, sample["metadata"]["docker_image"])

    await swe_bench_v2_setup_fn(
        datapoint=sample,
        terminal_session=TerminalSession(container.caas_session, endpoint=CAAS_ENDPOINT),
    )

    print("CAAS container created")
    return container


@pytest.fixture(scope="module")
def swe_setup():
    """Initialize and setup the SWE test environment (one-time initialization)"""
    metrics_init(config=dict())
    sample = _get_sample()
    container = asyncio.run(_get_container(sample))
    tool = get_tool(container, 300000)
    return tool, sample, container

async def test_read_project_structure(swe_setup):
    tool, sample, container = swe_setup
    project_structure, metadata = await exec(
        container.caas_session, "read_project_structure", {}
    )
    assert project_structure, "Expected non-empty project structure"
    print(project_structure)
    assert "I am working in a workspace that has the following structure:" in project_structure


async def test_create_file(swe_setup):
    tool, sample, container = swe_setup
    cwd = sample["metadata"]["cwd"]
    new_file = os.path.join(cwd, "test.txt")
    content = """line 1
line 2
line 3
line 4
line a
line b
line c
line d
"""
    await invoke_tool(tool, "create_file", {
        "filePath": new_file,
        "content": content,
    })

    msgs = await invoke_tool(tool, "read_file", {
        "filePath": new_file,
        "offset": 2,
        "limit": 1,
    })

    assert not exist_text("line 1", msgs), f"Not expected line 1 found: {msgs=}"
    assert exist_text("line 2", msgs), f"Expected line 2 not found: {msgs=}"
    assert exist_text("line 3", msgs), f"Expected line 3 not found: {msgs=}"
    assert not exist_text("line 4", msgs), f"Not expected line 4 found: {msgs=}"

    grep_result = await invoke_tool(tool, "grep_search", {
        "query": "line [a,b,c]",
        "isRegexp": True,
        "includePattern": "test.*",
        "maxResults": 1,
    })
    
    assert not exist_text("line 1", grep_result), f"Not expected line 1 found in grep result: {grep_result=}"
    assert not exist_text("line 2", grep_result), f"Not expected line 2 found in grep result: {grep_result=}"
    assert not exist_text("line 3", grep_result), f"Not expected line 3 found in grep result: {grep_result=}"
    assert not exist_text("line 4", grep_result), f"Not expected line 4 found in grep result: {grep_result=}"
    assert exist_text("line a", grep_result), f"Expected line a found in grep result: {grep_result=}"
    assert exist_text("line b", grep_result), f"Expected line b found in grep result: {grep_result=}"
    assert not exist_text("line c", grep_result), f"Not expected line c found in grep result: {grep_result=}"
    assert not exist_text("line d", grep_result), f"Not expected line d found in grep result: {grep_result=}"


async def test_test_search(swe_setup):
    tool, sample, container = swe_setup
    cwd = sample["metadata"]["cwd"]
    print(f"Testing test_search functionality, cwd={cwd}")
    content_func = "def add(x: int, y: int) -> int:\n    return x + y\n"
    content_test = 'import unittest\nfrom add import add\nclass TestAdd(unittest.TestCase):\n    def test_add(self):\n        self.assertEqual(add(1, 2), 3)\nif __name__ == "__main__":\n    unittest.main()\n'
    await invoke_tool(tool, "create_file", {
        "filePath": os.path.join(cwd, "add.py"),
        "content": content_func,
    })
    await invoke_tool(tool, "create_file", {
        "filePath": os.path.join(cwd, "add_test.py"),
        "content": content_test,
    })
    test_search_messages = await invoke_tool(tool, "test_search", {
        "filePaths": [os.path.join(cwd, "add_test.py")],
    })
    assert exist_text("The test file /home/<USER>/add_test.py contains tests for the following file:\n```python\n# filepath: /home/<USER>/add.py", test_search_messages), f"Expected test_add not found: {test_search_messages=}"


async def test_search_workspace_symbols(swe_setup):
    tool, sample, container = swe_setup
    cwd = sample["metadata"]["cwd"]
    content_func = "def add(x: int, y: int) -> int:\n    return x + y\n"
    await invoke_tool(tool, "create_file", {
        "filePath": os.path.join(cwd, "test_search_workspace_symbols.py"),
        "content": content_func,
    })

    search_workspace_symbols_messages = await invoke_tool(tool, "search_workspace_symbols", {
        "symbolName": "add",
    })
    assert exist_text("add", search_workspace_symbols_messages), f"Expected add not found: {search_workspace_symbols_messages=}"


async def test_list_code_usages(swe_setup):
    tool, sample, container = swe_setup
    cwd = sample["metadata"]["cwd"]
    content_func = "def test_list_code_usages_add(x: int, y: int) -> int:\n    return x + y\n"
    path = os.path.join(cwd, "test_list_code_usages.py")
    await invoke_tool(tool, "create_file", {
        "filePath": path,
        "content": content_func,
    })

    messages = await invoke_tool(tool, "list_code_usages", {
        "symbolName": "test_list_code_usages_add",
    })

    assert exist_text(path, messages), f"Expected {path} found in test_list_code_usages.py: {messages=}"

async def test_search_and_install_extensions(swe_setup):
    tool, sample, container = swe_setup
    searchResult = await invoke_tool(
        tool, "vscode_searchExtensions_internal", {"keywords": ["Remote development"]}
    )
    searchResult = "".join(["".join(t.parts) for t in searchResult])
    pattern = r'"id"\s*:\s*"([^"]+)"'
    ids = re.findall(pattern, searchResult)
    assert ids, f"Expected at least one extension ID found in search result, but got {ids=}. {searchResult=}"
    print(f"Found extension IDs: {ids=}")
    for id in ids[:3]:
        print(f"Extension found: {id=}")
        install_result = await invoke_tool(
            tool, "install_extension", {"name": id, "id": id}
        )

        print(f"Install result for {id}: {install_result}")
        assert exist_text("extension successfully", install_result) or exist_text("extension is already installed", install_result), f"Failed to install extension {id}, {install_result=}"


async def test_run_tests(swe_setup):
    tool, sample, container = swe_setup
    run_tests_result = await invoke_tool(tool, "runTests", {
        "files": [],
    })
    assert exist_text("testResult passed=false", run_tests_result), f"Expected test failure not found in {run_tests_result=}"

async def test_auto_trust_publisher(swe_setup):
    tool, _, _ = swe_setup
    install_result = await invoke_tool(
        tool, "install_extension", {"name": "orta.vscode-jest", "id": "orta.vscode-jest"}
    )

    assert exist_text("extension successfully", install_result), f"Failed to install extension orta.vscode-jest, {install_result=}"
    
    install_result = await invoke_tool(
        tool, "install_extension", {"name": "hbenl.vscode-mocha-test-adapter", "id": "hbenl.vscode-mocha-test-adapter"}
    )

    assert exist_text("extension successfully", install_result), f"Failed to install extension hbenl.vscode-mocha-test-adapter, {install_result=}"

async def test_install_all_extensions(swe_setup):
    tool, sample, container = swe_setup
    failed_extensions = []
    for language in LANGUAGE_UNIFORMS.keys():
        res = await install_vsc_extensions(tool.container.caas_session, language, [])
        for ext_id, result in res.items():
            if "extension successfully" not in result["message"] and "extension is already installed" not in result["message"]:
                failed_extensions.append((ext_id, result))
    assert not failed_extensions, f"Failed to install extensions: {failed_extensions}"

@pytest.mark.skip(reason="This test is not rready yet, needs to be fixed")
async def test_npm_install(swe_setup):
    tool, sample, container = swe_setup
    cwd = sample["metadata"]["cwd"]
    package_name = "lodash"
    install_result = await invoke_tool(tool, "run_in_terminal", {
        "command": """
npm install -g puppeteer@latest
npm install -g cypress@latest
npm install -g playwright@latest
npm install -g sharp@latest
npm install -g screener-ngrok@latest
npm install -g @sentry/cli
""",
        "explanation": "Installing npm packages for testing",
        "isBackground": False,
    })
    assert exist_text(f"Successfully installed {package_name}", install_result), f"Failed to install npm package {package_name}, {install_result=}"
