import json
import os

import blobfile as bf
import chat
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import <PERSON><PERSON>s<PERSON><PERSON>r

from deep_swe_msft.tools.utils import CRESCO_STORAGE_NAME
from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn
from deep_swe_msft.tools.caas_vscode_tool import VSCodeTool
from deep_swe_msft.tools.vscode_copilot_tool import (
    new_container,
)
from deep_swe_msft.tools.utils import CAAS_ENDPOINT


def get_tool(caas_container: CaasContainer, timeout: int) -> VSCodeTool:
    return VSCodeTool(
        caas_container,
        terminal_emulator_path="teammate_service.termites.lean_terminal:LeanTerminal",
        default_exec_timeout=timeout,
        real_tool="real",
    )

async def invoke_tool(tool: VSCodeTool, tool_name: str, args: dict) -> list[any]:
    message = chat.Message.assistant(
        recipient=f"{tool.get_tool_name()}.{tool_name}",
        content=json.dumps(args),
    )
    msgs = [response.content async for response in tool.process(message)]
    return msgs

def exist_text(text: str, messages: list[chat.Text]) -> bool:
    return any(text.lower() in ("".join(m.parts)).lower() for m in messages)
