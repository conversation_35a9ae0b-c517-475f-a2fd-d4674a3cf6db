import asyncio
import os

import pytest

from deep_swe_msft.tests.vsc_helpers import exist_text, get_tool, invoke_tool
from deep_swe_msft.tools.caas_vscode_tool import VSCodeTool
from deep_swe_msft.tools.vscode_copilot_tool import (
    new_container,
    setup_vscutils,
    debug_startup,
)
from mini.metrics import metrics_init
from deep_swe_msft.tools.utils import CAAS_ENDPOINT

pytestmark = pytest.mark.asyncio


async def _language_error_test(language: str, workdir: str, filename: str, expected_error: str, content_with_error: str = "error") -> VSCodeTool:
    container = await new_container(CAAS_ENDPOINT, 'aio')
    await container.exec(["mkdir", "-p", "/testbed"], workdir="/", timeout=60, env=None, user=None)
    await setup_vscutils(datapoint=None, session=container.caas_session, workdir="/testbed", language=language)
    tool = get_tool(container, 300000)
    new_file = os.path.join(workdir, filename)
    await invoke_tool(tool, "create_file", {
        "filePath": new_file,
        "content": content_with_error,
    })
    errors = await invoke_tool(tool, "get_errors", {
        "filePaths": [new_file],
    })

    print(f"Errors in {new_file}:", errors)
    assert exist_text(expected_error, errors), f"Expected error message not found: {errors=}"

async def test_multilingual():
    metrics_init(config=dict())
    tasks = {}
    # for language in ['python', 'java', 'typescript', 'javascript', 'c#', 'c++', 'c', 'go', 'rust']:
    for language in ['python', 'java', 'typescript', 'javascript', 'c#', 'c++', 'c']:
        if language.lower() == "java":
            tasks[language] = asyncio.create_task(_language_error_test(language, "/testbed", "add.java", 'Syntax error, insert "Identifier (" to complete MethodHeaderName'))
        elif language.lower() == "typescript":
            tasks[language] = asyncio.create_task(_language_error_test(language, "/testbed", "add.ts", "Cannot find name 'error'. Did you mean 'Error'?"))
        elif language.lower() == "python":
            tasks[language] = asyncio.create_task(_language_error_test(language, "/testbed", "add.py", '"error" is not defined'))
        elif language.lower() == "javascript":
            tasks[language] = asyncio.create_task(_language_error_test(language, "/testbed", "add.js", 'Invalid character.', '###'))
        elif language.lower() == "c#":
            tasks[language] = asyncio.create_task(_language_error_test(language, "/testbed", "add.cs", 'Identifier expected'))
        elif language.lower() == "c++":
            tasks[language] = asyncio.create_task(_language_error_test(language, "/testbed", "add.cpp", 'this declaration has no storage class or type specifier'))
        elif language.lower() == "c":
            tasks[language] = asyncio.create_task(_language_error_test(language, "/testbed", "add.c", "'#' not expected here", "error#*##"))
        elif language.lower() == "go":
            tasks[language] = asyncio.create_task(_language_error_test(language, "/testbed", "add.go", '"error" is not defined'))
        elif language.lower() == "rust":
            tasks[language] = asyncio.create_task(_language_error_test(language, "/testbed", "add.rs", '"error" is not defined'))
        else:
            print(f"Unsupported language for testing, language={language}")
            continue
    results = await asyncio.gather(*tasks.values(), return_exceptions=True)
    failures = [result for result in results if isinstance(result, Exception)]
    assert not failures, f"Multilingual test failed with exceptions: {failures}"
