'''
unit test for caas of swe-bench train v2
'''
import json
import dataclasses
import logging
import asyncio
from smokey import Smokey

import structlog
import tenacity

import caas
import chz
from caas.api import caas_api
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import <PERSON>aasContainer
from deep_swe_tasks.task_metadata import DeepSWETaskMetadata
from qstar.common import datapoint
from berry_harmony.tools.berry_tool_interface import ResourceContinuationError
from berry_caas_container_tool.caas_container_tool import CaasContainerResourceConfig
from deep_swe.sweberry_msft.swe_bench_train_v2_padawan_v2.setup.setup import swe_bench_v2_setup_fn_internal
from caas.commands import BashScript, Exec, UploadFile
from caas.protocol import VolumeMount

def load_jsonl(file_path):
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            item = json.loads(line.strip())
            data.append(item)
    return data

async def main(metadata: dict[str, str], endpoint: str | None = "https://eastus2.caas.azure.com", use_terminal_server = True):
    
    if use_terminal_server:
        cmd = ["/server.py"]
    else:
        cmd = []
    
    caas_session = None
    try:
        TMP_DRI="/usr/local/nodejsinstall"
        NODEJS_VERSION='22.14.0'
        blob_name = f"node-v{NODEJS_VERSION}-linux-x64.tar.gz"
        mnt = [VolumeMount(host=f"/mnt/azure_blob/tools/{blob_name}", container=f"{TMP_DRI}/{blob_name}", deprecated_use_blobfuse=True)]

        print("="*100)
        print(metadata.docker_image)
        print("Creating caas session")
        caas = caas_api(endpoint=endpoint)
        caas_session = await caas.new_session(
            image=metadata.docker_image,
            cmd=cmd,
            cpu_limit=str(metadata.limits.cpu),
            memory_limit=f"{metadata.limits.memory}m",
            idle_ttl=1200,
            num_gpus=0,
            network='bridge',
            timeout=1200,
            volume_mounts=mnt,
        )
        terminal_session = TerminalSession(caas_session)
        print("="*100+'\n')

        print("="*100)
        print("Setting up repo")
        out = await swe_bench_v2_setup_fn_internal(terminal_session=terminal_session, metadata=metadata)
        
        print(out)
        print("="*100+'\n')

        # Tool 1
        tool_name = 'str_replace_editor'

        input_data = {
            "command": "create",
            "path": "/home/<USER>/padawan_test.txt",
            # "path": "/usr/local/bin/padawan_tools/padawan_test.txt",
            "file_text": "hello world"
        }
        input_json = json.dumps(input_data)

        print(f"[DEBUG] command: {tool_name} {input_json}")
        output = await caas_session.run(Exec(
            ["bash", "-lc", f"{tool_name} '{input_json}'"],
            workdir="/",
            timeout=900,
            env=None
        ))
        print(output.decode())

        # Tool 2
        tool_name = 'report_progress'

        input_data = {
            "commitMessage": "test-commit",
            "prDescription": "today is a good day",
        }
        input_json = json.dumps(input_data)

        print(f"[DEBUG] command: {tool_name} {input_json}")
        output = await caas_session.run(Exec(
            ["bash", "-lc", f"{tool_name} '{input_json}'"],
            workdir="/",
            timeout=900,
            env=None
        ))
        print(output.decode())

        # print("="*100)
        # print("Running unit tests")
        # ok, d = await try_run_command(
        #     terminal_session, f"source ~/.bashrc; cd {repo_root}; {exec_cmd}", exec_seconds
        # )
        # print(d)
        
        # output = passes_tests("py" ,d["output"])
        # # expect to see `grader = True | unittest result = {'passed': 274, 'warnings': 12, 'skipped': 33}`
        # result_counts = gather_log_test_results(d["output"])
        # result_counts_json = {"passed": result_counts.passed, "failed": result_counts.failed, "errors": result_counts.errors, "skipped":result_counts.skipped,"warnings":result_counts.warnings}
        # print(f'grader = {output} | unittest result = {result_counts_json}')
        # print("="*100+'\n')

    except Exception as e:
        print("Error: ", e)
    finally:
        if caas_session:
            await caas_session.close()
            print("caas_session closed")

if __name__ == "__main__":

    data = load_jsonl("swe_bench_train_updated.jsonl")
    metadata = data[4]['metadata']
    # print(f'before: {metadata}')
    metadata = DeepSWETaskMetadata.model_validate(metadata)
    # print(f'after: {metadata}')
    asyncio.run(main(metadata, endpoint="https://eastus2.caas.azure.com", use_terminal_server=True))






# try:
#     if self.setup_fn is not None:
#         await self.setup_fn(
#             datapoint=dataclasses.asdict(dp),
#             terminal_session=terminal_session,
#         )

#     if not metadata.allow_internet:
#         await caas_session.update_network(enable_network=False)
# except Exception:
#     await caas_session.close()
#     raise

# return CaasContainer(
#     caas_endpoint=self.caas_endpoint,
#     image_name=metadata.docker_image,
#     caas_session_state=caas_session.save(),
#     user=self.user,
#     is_owner=True,
# )
