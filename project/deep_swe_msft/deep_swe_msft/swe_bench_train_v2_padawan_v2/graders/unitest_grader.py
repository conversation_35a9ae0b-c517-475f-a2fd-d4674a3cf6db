import shlex
from typing import TypedDict
from uuid import uuid4

import structlog

import berry
from caas import ExecError
from caas.api import caas_api
from caas.commands import Exec, RawBashScript, RawExec, UploadFile
from caas.terminal.api import TerminalSession
from deep_swe_msft.swe_bench_train_v2_padawan_v2.graders.test_parsers import PRECOMMIT_PREFIX, ModelPatch, parse_test_results, parse_python_test_results, parse_typescript_test_results
from deep_swe_msft.swe_bench_train_v2_padawan_v2.setup.setup import swe_bench_v2_setup_fn_internal
from caas_utils.utils import run_with_retries
from deep_swe_tasks import DeepSWETaskMetadata, SWEBenchV2Task
from deep_swe_msft.padawan_graders.grader_utils import LongRunningRawExec

logger = structlog.get_logger(__name__)

TEST_OUTPUT_MAX = 262_144


class GradeReport(TypedDict):
    passed: bool
    model_patch: str
    test_output: str
    passed_tests: list[str]


MODEL_DIFF = "/tmp/model_patch.diff"
TEST_DIFF = "/tmp/test_patch.diff"

PROTECTED_FILES = [
    ".config.js",
    ".config.ts",
    "package.json",
    "pom.xml",
    "build.gradle",
    "cargo.toml",
    "go.mod",
    "go.sum",
    "setup.cfg",
    "pyproject.toml",
    "conftest.py",
    "pytest.ini",
    "requirements.txt",
    "requirements-dev.txt",
    "tox.ini",
    "setup.py",
    ".pre-commit-config.yaml",
    ".eslintrc",
    ".eslintrc.json",
    ".eslintrc.js",
    ".prettierrc",
    ".prettierrc.json",
    ".prettierrc.js",
    "webpack.config.js",
    "webpack.config.ts",
    "rollup.config.js",
    "rollup.config.ts",
    "vite.config.js",
    "vite.config.ts",
    ".mocharc.js",
    ".mocharc.json",
    ".mocharc.yaml",
    ".mocharc.yml",
    "jest.config.js",
    "settings.gradle",
    "CMakeLists.txt",
    "Gemfile",
    "Gemfile.lock",
    "rust-toolchain",
    "Dockerfile",
    ".dockerignore",
    ".gitignore",
    ".gitattributes",
    "Makefile",
    ".env",
]


async def get_model_patch(
    terminal_session: TerminalSession,
    base_commit: str,
    repo_root: str,
    protected_files: list[str],
) -> ModelPatch:
    # Download the model diff (excluding config files to avoid hacks)
    exclude_patterns = shlex.join([f":(exclude){file}" for file in protected_files])
    model_patch_raw = await run_with_retries(
        terminal_session,
        f"""
cd {repo_root}
# Do not include warnings about CRLF line endings in git diff output.
git config core.safecrlf false
git add -N .
git diff --binary {base_commit} -- . {exclude_patterns}
""",
        attempts=3,
    )
    return ModelPatch(model_patch_raw.decode())


async def grade_fn_v2(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> bool:
    dp_metadata = sample.gt_datapoint.metadata
    metadata = DeepSWETaskMetadata.model_validate(dp_metadata)
    # Extract lang from raw metadata and pass it separately
    lang = dp_metadata.get("lang", None)
    report = await grade_fn_v2_internal(
        terminal_session=terminal_session,
        metadata=metadata,
        lang=lang,
        fast=fast,
    )
    logger.info("Grading finished", **report)
    sample.ephemeral_metadata["model_patch"] = report["model_patch"]
    sample.metadata["grader"] = {
        "prompt": report["model_patch"],
        "response": report["test_output"],
        "score": int(report["passed"]),
    }
    return report["passed"]


async def grade_fn_v2_internal(
    *,
    terminal_session: TerminalSession,
    metadata: DeepSWETaskMetadata,
    lang: str | None,
    fast: bool,
) -> GradeReport:
    repo_root = metadata.cwd
    assert isinstance(metadata.task, SWEBenchV2Task)

    model_patch = await get_model_patch(
        terminal_session=terminal_session,
        base_commit=metadata.task.base_commit,
        repo_root=repo_root,
        protected_files=PROTECTED_FILES,
    )

    if not model_patch.has_test_and_non_test():
        return {
            "passed": False,
            "test_output": "Model patch does not have both tests/non-tests",
            "model_patch": model_patch.compact_patch,
            "passed_tests": [],
        }

    if fast:
        # Soft reset the repository to the base commit (but don't run setup again)
        await run_with_retries(
            terminal_session,
            f"cd {repo_root} && git add -A && git reset --hard {metadata.task.base_commit}",
            attempts=3,
        )

        # hand off the model patch object instead of individual fields.
        return await _grade_fn_v2_inner(terminal_session, metadata, model_patch, lang)

    # use the secure grading path, passing the full model patch object.
    return await _grade_fn_v2_secure(metadata, model_patch, lang)


from deep_swe_msft.tools.utils import CAAS_ENDPOINT

async def _grade_fn_v2_secure(
    metadata: DeepSWETaskMetadata,
    model_patch: ModelPatch,
    lang: str | None,
) -> GradeReport:
    """
    The "safe" route - re-initialize a fresh container and grade in there.
    """
    caas = caas_api(CAAS_ENDPOINT)
    async with caas.use_session(
        image=metadata.docker_image,
        cpu_limit=str(metadata.limits.cpu),
        memory_limit=f"{metadata.limits.memory}m",
        keepalive_interval=30,
        timeout=1200,
    ) as session:
        grade_session = TerminalSession(session)
        await swe_bench_v2_setup_fn_internal(terminal_session=grade_session, metadata=metadata, random_drop_packages=False)

        return await _grade_fn_v2_inner(
            grade_session=grade_session,
            metadata=metadata,
            model_patch=model_patch,
            lang=lang,
        )


async def _upload_and_apply(
    session: TerminalSession,
    patch: str,
    workdir: str,
) -> None:
    tmpfile = "/tmp/" + uuid4().hex
    await session.session.run(UploadFile(path=tmpfile, data=patch.encode()))
    await session.session.run(Exec(["git", "apply", "-v", tmpfile], workdir=workdir, timeout=60))


async def _grade_fn_v2_inner(
    grade_session: TerminalSession,
    metadata: DeepSWETaskMetadata,
    model_patch: ModelPatch,
    lang: str | None,
) -> GradeReport:
    assert isinstance(metadata.task, SWEBenchV2Task)

    # Apply model test changes, verify that it fails
    try:
        await _upload_and_apply(grade_session, model_patch.test_patch, metadata.cwd)
        exit_code, output = await grade_session.session.run(
            LongRunningRawExec(["pytest", *model_patch.test_files], workdir=metadata.cwd, timeout=900)
        )
        if exit_code == 0:
            return {
                "passed": False,
                "model_patch": model_patch.compact_patch,
                "test_output": "Model test is not a regression test: "
                + output.decode(errors="ignore"),
                "passed_tests": [],
            }
    except ExecError as e:
        return {
            "passed": False,
            "model_patch": model_patch.compact_patch,
            "test_output": "Error running model test: " + e.output.decode(errors="ignore"),
            "passed_tests": [],
        }

    # Assert that the model real patch works
    try:
        await run_with_retries(
            grade_session,
            f"cd {metadata.cwd} && git add -A && git reset --hard",
            attempts=3,
        )
        await _upload_and_apply(grade_session, model_patch.non_test_patch, metadata.cwd)
        # This should be non-conflicting since we filtered to non-test changes.
        await _upload_and_apply(grade_session, metadata.task.test_patch, metadata.cwd)
    except ExecError as e:
        return {
            "passed": False,
            "model_patch": model_patch.compact_patch,
            "test_output": "Error testing model patch: " + e.output.decode(errors="ignore"),
            "passed_tests": [],
        }

    if lang == 'typescript':
        # Run eval script
        _eval_code, eval_output = await grade_session.session.run(
            RawBashScript(metadata.test_script, login=False, timeout=900)
        )
        _, eval_output = await grade_session.session.run(
            RawBashScript(f"cd {metadata.cwd}; cat report.json", login=False, timeout=900)
        )
        # with open('/var/log/supervisor/yunshengli7_ts.diff', 'a') as f:
        #     f.write("\n\n-------------------------------\n\n" + eval_output.decode(errors="ignore") + "\n")
        eval_output = eval_output.decode(errors="ignore")
        # Parse test output to ensure that all expected tests are passing
        passed, failed = parse_typescript_test_results(eval_output, metadata.cwd)
    elif lang == 'python':
        # Run eval script
        _eval_code, eval_output = await grade_session.session.run(
            RawBashScript(metadata.test_script, login=False, timeout=900)
        )
        # with open('/var/log/supervisor/yunshengli7_py.diff', 'a') as f:
        #     f.write("\n\n-------------------------------\n\n" + eval_output.decode(errors="ignore") + "\n")
        eval_output = eval_output.decode(errors="ignore")

        # Parse test output to ensure that all expected tests are passing
        passed, failed = parse_python_test_results(eval_output)
    else:
        _eval_code, eval_output = await grade_session.session.run(
            RawBashScript(metadata.test_script, login=True, timeout=900)
        )
        eval_output = eval_output.decode(errors="ignore")

        # Parse test output to ensure that all expected tests are passing
        passed, failed = parse_test_results(eval_output)
    precommit_fail = {t for t in failed if t.startswith(PRECOMMIT_PREFIX)}

    required_passing_tests = set(metadata.task.PASS_TO_PASS) | set(metadata.task.FAIL_TO_PASS)
    # Any precommit hook that failed in the solution is ok to fail here.
    precommit_ok = not (precommit_fail - set(metadata.task.PRECOMMIT_FAIL))
    # All required tests must pass.
    required_tests_pass = required_passing_tests.issubset(set(passed))

    if not required_tests_pass:
        eval_output = (
            f"Required tests failed: {required_passing_tests - set(passed)}\n\n{eval_output}"
        )
    if not precommit_ok:
        eval_output = f"Precommit hooks failed: {precommit_fail}\n\n{eval_output}"
    if len(eval_output) > TEST_OUTPUT_MAX:
        truncated_chars = len(eval_output) - TEST_OUTPUT_MAX
        eval_output = (
            eval_output[: TEST_OUTPUT_MAX // 2]
            + f"\n(...{truncated_chars} characters truncated...)\n"
            + eval_output[-TEST_OUTPUT_MAX // 2 :]
        )

    return {
        "passed": precommit_ok and required_tests_pass,
        "model_patch": model_patch.compact_patch,
        "test_output": eval_output,
        "passed_tests": list(passed),
    }
