import chz
import berry
from functools import partial

import qstar.instance_completers
import qstar.instance_optimizers
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.curriculums.function_wrapper import FunctionWrapper

import caas_autograding.grader as caas_graders
from berry import InstanceCompleter, InstanceOptimizer, SampleAllocator
from qstar.allocators.continuous_rewards_adaptive_sample_allocator import (
    ContinuousRewardsAdaptiveSampleAllocator,
)

from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig 
from deep_swe.datasets import configs as default_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_msft.swe_bench_train_v2_padawan_v2.setup.setup import swe_bench_v2_setup_fn
from deep_swe_msft.swe_bench_train_v2_padawan_v2.setup.caas_resource_config import DeepSWECaasContainerResourceConfig

from deep_swe_msft.tools.caas_padawan_tool import PadawanToolConfig
from deep_swe_msft.tools.caas_mix_tool import MixToolConfig
from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY, PADAWAN_SYSTEM_PROMPT, INSTRUCTION_PADAWAN
from deep_swe_msft.padawan_data.sampler import PadawanSampleCompleter
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CRESCO_STORAGE_NAME

# from deep_swe_msft.swe_bench_train_v2_padawan_v2.graders.if_enforcement_grader import IFEnforcementGrader
from deep_swe_msft.padawan_graders.func_enforcement_grader import FuncEnforcementGrader
# from deep_swe_msft.swe_bench_train_v2_padawan_v2.graders.func_enforcement_grader_v2 import FuncEnforcementGraderV2
from deep_swe_msft.padawan_graders.system_prompt_following_grader import SPFollowCotograder
from deep_swe_msft.padawan_graders.system_prompt_following_grader_hard import SPFollowHardCotograder
from deep_swe_msft.padawan_graders.swe_workflow_grader import SWEWorkFlowCotograder
from deep_swe_msft.padawan_graders.system_prompt_following_grader_hard_v2 import SPFollowHardV2Cotograder

from deep_swe_msft.padawan_graders.cotograder_utils import (
    COTOGRADER_RENDERER,
    CotoGraderService
)

CAAS_IDLE_TTL = 1200


def make_swebench_train_v2_resource_configs(
    caas_resource_config: DeepSWECaasContainerResourceConfig,
) -> tuple[ResourceConfig, ...]:
    caas_resource_config = chz.replace(caas_resource_config, setup_fn=swe_bench_v2_setup_fn)
    return (caas_resource_config,)

def make_swebench_v2_graders(
    use_cotograder: bool = False, 
    use_sp_hard: bool = False,
    use_sp_hard_v2: bool = False,
    use_sp_sbh_hard: bool = False,
) -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    from caas_swe_bench_train_v2.cotograders import (
        ContributorGuideCotograder,
        PatchComparisonCotograder,
        PatchConsistencyCotograder,
    )
    
    graders = [caas_graders.TermberryGrader(grade_fn="deep_swe_msft.swe_bench_train_v2_padawan_v2.graders.unitest_grader:grade_fn_v2")]
    if use_cotograder:
        graders.extend([
            PatchConsistencyCotograder(
                grader_service=CotoGraderService(),
                renderer_name=COTOGRADER_RENDERER,
                grader_max_tokens=32768,
            ),
            PatchComparisonCotograder(
                grader_service=CotoGraderService(),
                renderer_name=COTOGRADER_RENDERER,
                grader_max_tokens=32768,
            ),
            ContributorGuideCotograder(
                grader_service=CotoGraderService(),
                renderer_name=COTOGRADER_RENDERER,
                grader_max_tokens=32768,
            ),
        ])

    if use_sp_hard_v2 or use_sp_sbh_hard:
        graders.append(
            SPFollowHardV2Cotograder(
                grader_service=CotoGraderService(),
                renderer_name=COTOGRADER_RENDERER,
                grader_max_tokens=16384,
                use_sp_sbh_hard=use_sp_sbh_hard
            )
        )
    else:
        graders.append(
            SWEWorkFlowCotograder(
                grader_service=CotoGraderService(),
                renderer_name=COTOGRADER_RENDERER,
                grader_max_tokens=16384,
            )
        )
    return tuple(graders)


def _make_tool_configs(
    container_tool_config: str = "padawan_tool",
    internet_policy: str = "no_access",
    tool_channel: BerryChannel = BerryChannel.CHAIN_OF_THOUGHT,
) -> tuple[ToolConfig, ...]:
    if container_tool_config == "padawan_tool":
        return (PadawanToolConfig(internet_policy=internet_policy, tool_channel=tool_channel),)
    elif container_tool_config == "mix_tool":
        return (MixToolConfig(internet_policy=internet_policy, tool_channel=tool_channel),)
    else:
        raise ValueError(f"Unknown padawan tool config: {container_tool_config}")


DEFAULT_ENFORCE_PROBABILITY = 0.25
# IMPORTANT: must be paired with IFEnforcementGrader.
def enforce_commands(
    probability: float = DEFAULT_ENFORCE_PROBABILITY,
    extra_good_commands: tuple[str, ...] = (),
    extra_bad_commands: tuple[str, ...] = (),
) -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:enforce_commands",
        kwargs={
            "probability": probability,
            "extra_good_commands": extra_good_commands,
            "extra_bad_commands": extra_bad_commands,
        },
    )

def get_padawan_system_prompt(include_build_style_for_easy_only: bool = False) -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:get_padawan_system_prompt",
        kwargs={
            "include_build_style_for_easy_only": include_build_style_for_easy_only
        }
    )

def random_tool_set() -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:random_tool_set",
    )

ENABLE_NETWORK = True

@chz.chz
class SWEBenchTrainV2Grader(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(make_swebench_v2_graders)

def _swebench_train_v2_graders_func_wrapper(
    use_cotograder: bool = False,
    use_sp_hard: bool = False,
    use_sp_hard_v2: bool = False,
    use_sp_sbh_hard: bool = False
) -> Grader[HarmonyCompletionDatapoint]:
    return SWEBenchTrainV2Grader(graders=make_swebench_v2_graders(use_cotograder=use_cotograder, use_sp_hard=use_sp_hard, use_sp_hard_v2=use_sp_hard_v2, use_sp_sbh_hard=use_sp_sbh_hard))

@chz.chz
class SWEBenchV2InstanceCompleter(
    qstar.instance_completers.ContinuousRewardsInstanceCompleter, IsOverride
):
    # Because there's so much continuous reward in the grading
    sample_allocator: SampleAllocator = override(ContinuousRewardsAdaptiveSampleAllocator)
    instance_optimizer: InstanceOptimizer = override(
        qstar.instance_optimizers.VariantsInstanceOptimizer
    )

@chz.chz(typecheck=True)
class SWEBenchTrainV2DatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = (
        "data.luw.swe-bench-train-vsc.test_train"
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            _swebench_train_v2_graders_func_wrapper,
            use_sp_hard=False,
            use_sp_hard_v2=True,
            use_sp_sbh_hard=False
        )
    )
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            container_tool_config='padawan_tool',
            internet_policy='enabled' if ENABLE_NETWORK else 'no_access',
        ),
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            DeepSWECaasContainerResourceConfig(setup_fn=swe_bench_v2_setup_fn,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL,
                                               enable_network_after_setup=ENABLE_NETWORK),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            random_tool_set(),
            # get_padawan_system_prompt(),
            # enforce_commands(probability=0.1),
            conversation_converter(
                "deep_swe_msft.swe_bench_train_v2_padawan_v2.data_customization.conversation_init:conversation_init_fn",
                instruction=INSTRUCTION_PADAWAN,
            ),
        )
    )
    # sample_completer: berry.SampleCompleter = override(PadawanSampleCompleter)
    instance_completer: InstanceCompleter = override(SWEBenchV2InstanceCompleter)
    model_identity_str: str = PADAWAN_MODEL_IDENTITY
    instructions: str = PADAWAN_SYSTEM_PROMPT

    """
    NOTE: sample_completer cannot currently be overriden normally because it gets overwritten by qstar.common.defaults.
    For more context see thread:
    """
    # @chz.init_property
    # def sample_completer(self) -> berry.SampleCompleter:
    #     return PadawanSampleCompleter()


@chz.chz(typecheck=True)
class MSWEBenchTrainV2DatasetConfig(SWEBenchTrainV2DatasetConfig, IsOverride):
    dataset_id: str = "data.zhendongw.swe_train.data_mix_v1.swb_train_ml.py"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            random_tool_set(),
            get_padawan_system_prompt(),
            # enforce_commands(probability=0.1),
            conversation_converter(
                "deep_swe_msft.swe_bench_train_v2_padawan_v2.data_customization.conversation_init:conversation_init_fn",
                add_contributor_criteria=False,
            )
        )
    )


@chz.chz(typecheck=True)
class SWEBenchTrainV2EvalDatasetConfig(SWEBenchTrainV2DatasetConfig, IsOverride):
    dataset_id: str = "data.zhendongw.swe-bench-test.test_test"
