from functools import partial
from typing import Any

import berry
import caas_autograding.grader as caas_graders
import chz
import structlog
from berry.function_wrapper import FunctionWrapper
from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from caas_swe_bench_train import SWEBenchDatapoint
from caas_swe_bench_train import setup_fn as caas_swe_bench_train_setup_fn
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.datasets.setup import setup_fn_coreutils
from deep_swe.graders.thoroughness_grader import ThoroughnessGrader
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig  # type: ignore
from deep_swe_msft.tools.utils import CRESCO_STORAGE_NAME
from deep_swe_msft.padawan_data.sampler import PadawanSampleCompleter
from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.caas_resource_config import CaasContainerResourceConfig
from deep_swe_msft.env_setup_padawan_v2.datasets.mrfs_setup import mrfs_setup_fn_coreutils
from deep_swe_msft.env_setup_padawan_v2.utils.train_constants import (
    ENABLE_NETWORK,
    RFS_LANG_TO_CLEAN_IMAGE_NAME,
    RFS_LANG_TO_CUSTOM_IMAGE_NAME,
    RFS_LANG_TO_IMAGE_NAME,
)
from deep_swe_msft.padawan_graders.cotograder_utils import COTOGRADER_RENDERER, COTOGRADER_CHZ_ARGV

from deep_swe_msft.tools.caas_padawan_tool import PadawanToolConfig
from deep_swe_msft.tools.caas_mix_tool import MixToolConfig
from deep_swe_msft.tools.utils import CAAS_ENDPOINT
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.common.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from qstar.curriculums.variant_producer import VarDiscountingVariantProducer, VariantProducer
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.graders.taskgen_utils import BerryGraderConfig, TokenCompleterGraderService
from qstar.presets.chz_utils import IsOverride, override
from qstar.samplers import BaseSampler
from token_completer import TokenCompleter

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig, IsOverride):
    caas_cpu_limit: float = 2.0  # Increase to 2 if actually using VSCode
    caas_memory_limit: int = 2
    caas_container_image: str = "vscode-agent"  # Has more pip dependencies!
    caas_idle_ttl: int = 30 * 60 # in seconds, 30 minutes
    enable_network_after_setup: bool = False
    caas_endpoint: str = CAAS_ENDPOINT


@chz.chz
class DeepSWEVardiscProducer(VarDiscountingVariantProducer, IsOverride):
    # Non-standard settings designed for the low data-efficiency, high compute-efficiency regime
    reward_distribution_power: float = 1.0
    min_reward_multiplier: int = 64
    max_reward_multiplier: int = 1024
    num_reward_multipliers: int = 1


def _make_tool_configs(
    container_tool_config: str = "padawan_tool",
    internet_policy: str = "no_access",
) -> tuple[ToolConfig, ...]:
    if container_tool_config == "padawan_tool":
        return (PadawanToolConfig(internet_policy=internet_policy),)
    elif container_tool_config == "mix_tool":
        return (MixToolConfig(internet_policy=internet_policy),)
    else:
        raise ValueError(f"Unknown padawan tool config: {container_tool_config}")

@chz.chz
class DeepSWEDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs)

    # Defaults to an empty container (with oai coreutils installed).
    # Most tasks will probably want to override this with custom setup.
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(CaasResourceConfig(setup_fn=setup_fn_coreutils),)
    )
    max_num_yields: int = 400
    variant_producer: VariantProducer | None = override(DeepSWEVardiscProducer)


def make_multistage_grader(
    grader_argvs: list[list[str]] | None = None,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
) -> caas_graders.MultiStageGraderWithAuxReinforcements:
    # NB: we can freely set |channels_for_answer| here. See NOTE [ DeepSWEDatasetConfig.channel_format ]
    if grader_argvs is None:
        grader_argvs = []
    grader_argvs = list(grader_argvs)

    blueprint = chz.Blueprint(caas_graders.MultiStageGraderWithAuxReinforcements)
    for ii, grader_argv in enumerate(grader_argvs):
        argv: list[str] = []
        for arg in grader_argv:
            if not arg.startswith(("=", ".")):  # allow wildcards
                arg = "." + arg
            argv.append(f"graders.{ii}{arg}")
        blueprint = blueprint.apply_from_argv(argv)
    if channels_for_answer:
        channels_for_answer_arg = "channels_for_answer=" + ",".join(
            str(c) for c in channels_for_answer
        )
        blueprint = blueprint.apply_from_argv(["..." + channels_for_answer_arg])
    return blueprint.make()


def make_envsetup_multistage_grader(
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
) -> MultiStageGrader:
    grader_argvs = [
        [
            "=deep_swe_msft.padawan_graders.env_setup_following_grader:EnvSetupFollowCotograder",
            *COTOGRADER_CHZ_ARGV,
            f"grader_max_tokens={16384}",
        ]
    ]
    return make_multistage_grader(grader_argvs, channels_for_answer)

def random_tool_set() -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:random_tool_set",
    )

@chz.chz
class EnvSetupPythonDatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.chenliang1.swe.rrb_py_envsetup_train_1700.u06212025_v2"
    grader: Grader[HarmonyCompletionDatapoint] = override(partial(make_envsetup_multistage_grader))
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],  # use aio for now, ubuntu image is being fixed.
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            container_tool_config='padawan_tool',
            internet_policy='enabled' if ENABLE_NETWORK else 'no_access',
        ),
    )

    model_identity_str: str = PADAWAN_MODEL_IDENTITY
    # instructions: str = PADAWAN_BUILD_SYSTEM_PROMPT

    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            random_tool_set(),
        )
    )

@chz.chz
class EnvSetupTypescriptDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.rrb_ts_envsetup_train_600.u06212025_v2"
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["typescript"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class EnvSetupJavascriptDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.rrb_js_envsetup_train_600.u06212025_v2"
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["javascript"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class EnvSetupJavaDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.rrb_java_envsetup_train_900.u06212025_v2"
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["java"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class EnvSetupPythonUnusedDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    # rrb unused python repos with aio image, initial pass rate ~50%, easy
    dataset_id: str = "data.chenliang1.swe.rrb_envsetup_unused_train_1250.u06232025"

@chz.chz
class EnvSetupPythonUnusedCustomImgDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    # rrb unused python repos with custom image, initial pass rate ~45%, easy
    # the data instances are the same as EnvSetupPythonUnusedDatasetConfig despite the naming difference
    dataset_id: str = "data.chenliang1.swe.rrb_envsetup_unused_custom_img_train_1250.u06232025"
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_CUSTOM_IMAGE_NAME["py"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class EnvSetupSBHDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    # sbh multi-pl repos with aio image, initial pass rate ~20%
    dataset_id: str = "data.chenliang1.swe.sbh_envsetup_train_180.u06232025"
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["all"], 
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class EnvSetupSBHCustomImgDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    # sbh multi-pl repos with custom image, initial pass rate ~15%
    # the data instances are the same as EnvSetupSBHDatasetConfig despite the naming difference
    dataset_id: str = "data.chenliang1.swe.sbh_envsetup_custom_img_train_180.u06232025"
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_CUSTOM_IMAGE_NAME["all"], 
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )

@chz.chz
class EnvSetupPythonEvalDatasetConfig(EnvSetupPythonDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.chenliang1.swe.rrb_py_envsetup_valid_212.u06212025_v2"

@chz.chz
class EnvSetupTypescriptEvalDatasetConfig(EnvSetupPythonEvalDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.chenliang1.swe.rrb_ts_envsetup_valid_116.u06212025_v2"

@chz.chz
class EnvSetupJavascriptEvalDatasetConfig(EnvSetupPythonEvalDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.chenliang1.swe.rrb_js_envsetup_valid_128.u06212025_v2"

@chz.chz
class EnvSetupJavaDatasetEvalConfig(EnvSetupPythonEvalDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.chenliang1.swe.rrb_java_envsetup_valid_169.u06212025_v2"

@chz.chz
class EnvSetupPythonUnusedEvalDatasetConfig(EnvSetupPythonUnusedDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.chenliang1.swe.rrb_envsetup_unused_valid_216.u06232025"

@chz.chz
class EnvSetupPythonUnusedCustomImgEvalDatasetConfig(EnvSetupPythonUnusedCustomImgDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.chenliang1.swe.rrb_envsetup_unused_custom_img_valid_216.u06232025"

@chz.chz
class EnvSetupSBHEvalDatasetConfig(EnvSetupSBHDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.chenliang1.swe.sbh_envsetup_valid_15.u06232025"

@chz.chz
class EnvSetupSBHCustomImgEvalDatasetConfig(EnvSetupSBHCustomImgDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.chenliang1.swe.sbh_envsetup_custom_img_valid_15.u06232025"