# Modified from garden at commit 4aa78758042b9c9011adae329b71969d8de103c4
# Original path: /project/deep_swe/deep_swe/datasets/caas_container_tool.py
# Forked on: 2025-04-23

import functools
import json
import shlex
from functools import cached_property
from functions import Function
from typing import Any, AsyncIterator, Collection, Literal

import chat
import chz
import structlog
from caas_tool.caas_container import CaasContainer
from chat.render.common import render_content
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from deep_swe_msft.vsc_data.datapoint_converters import ENFORCE_COMMANDS_FORBIDDEN
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from berry_harmony.tools.berry_tool_interface import BerryTool
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from berry_caas_container_tool.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from berry_caas_container_tool.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
)
try:
    from berry_caas_container_tool.caas_container_tool import (
        ToolPluginConfig as CaasToolPluginConfig,
        create_tool_plugins as create_caas_tool_plugins,
    )
except ImportError:
    # Version prior to July FI:
    from berry_caas_container_tool.caas_container_tool import (
        CaasToolPluginConfig,
        create_caas_tool_plugins,
    )

logger = structlog.stdlib.get_logger(component=__name__)


CAAS_CONTAINER_MANUAL = """
Use `container.exec` to execute commands in a terminal if needed.

Regular UNIX commands and common utilities (like python) are available.
Other than those, there's an additional `oai` tool.

```
Usage: oai <subcommand> [<args>]

Subcommands:
    find-files [<file_pattern>]             Similar to fzf. Find files matching an optional (fuzzy) file_pattern.
        - If no file_pattern is provided, it lists the files in the current directory.
        - Instead of using `find` or `ls`, please use this command instead.
        - It supports fuzzy search, so you can use a part of the path to find the file.
        - Example: {"cmd":["oai","find-files","randm/file.py"]}
    file-outline <path>                     Show an outline of the top-level definitions in a file at path.
        - To save time, please look at the file outline first before looking at the individual lines.
    rg [<options>] <pattern> [<path> ...]   Wrapper over ripgrep (rg). Automatically truncates to 50 results.
        - Has exactly the same options as ripgrep.
        - Instead of using `rg` directly or `grep`, please use this command instead.
    apply-patch <patch_content>             Applies a patch to the filesystem.
        - Only one argument <patch_content> is allowed in this command -- no flags or options. The whole patch must be passed as a single string argument.
        - Patch content must be a string beginning with "*** Begin Patch" and ending with "*** End Patch", e.g:
          Updating a file: {"cmd":["oai","apply-patch","*** Begin Patch\\n*** Update File: path/to/file.py\\n@@ def example():\\n-  pass\\n+  return 123\\n*** End Patch"]}
          Adding a file: {"cmd":["oai","apply-patch","*** Begin Patch\\n*** Add File: file_to_add.py\\n+if __name__ == '__main__':\\n+  print(1234)\\n*** End Patch"]}
          Deleting a file: {"cmd":["oai","apply-patch","*** Begin Patch\\n*** Delete File: file_to_delete.py\\n*** End Patch"]}
    show-lines -r START:END                 Print a range of lines from a file.
        - Print lines 40-60 of `path/to/file.py`: {"cmd":["oai","show-lines","-r","40:60","path/to/file.py"]}
```""".strip()


# TODO: we need to make this configurable. For now, manually swap it in get_description()
CAAS_CONTAINER_MANUAL_V3 = """
Use `container.exec` to execute commands in a terminal if needed.
Regular UNIX commands and common utilities (like python) are available.
Other than those, there's an additional `oai` tool.
```
Usage: oai <subcommand> [<args>]
Subcommands:
find-files [<file_pattern>]             Similar to fzf. Find files matching an optional (fuzzy) file_pattern.
- If no file_pattern is provided, it lists the files in the current directory.
- Instead of using `find` or `ls`, please use this command instead.
- It supports fuzzy search, so you can use a part of the path to find the file.
- Example: {"cmd":["oai","find-files","randm/file.py"]}
file-outline <path>                     Show an outline of the top-level definitions in a file at path.
- To save time, please look at the file outline first before looking at the individual lines.
rg [<options>] <pattern> [<path> ...]   Wrapper over ripgrep (rg). Automatically truncates to 50 results.
- Has exactly the same options as ripgrep.
- Instead of using `rg` directly or `grep`, please use this command instead.
apply-patch <patch_content>             Applies a patch to the filesystem.
- Only one argument <patch_content> is allowed in this command -- no flags or options. The whole patch must be passed as a single string argument.
- Patch content must be a string beginning with "*** Begin Patch" and ending with "*** End Patch", e.g:
Updating a file: {"cmd":["oai","apply-patch","*** Begin Patch\n*** Update File: path/to/file.py\n@@ def example():\n-  pass\n+  return 123\n*** End Patch"]}
Adding a file: {"cmd":["oai","apply-patch","*** Begin Patch\n*** Add File: file_to_add.py\n+if __name__ == '__main__':\n+  print(1234)\n*** End Patch"]}
Deleting a file: {"cmd":["oai","apply-patch","*** Begin Patch\n*** Delete File: file_to_delete.py\n*** End Patch"]}
show-lines -r START:END                 Print a range of lines from a file.
- Print lines 40-60 of `path/to/file.py`: {"cmd":["oai","show-lines","-r","40:60","path/to/file.py"]}
```
Given a repository, there is usually also an `oaide` tool that provides access to a virtual IDE that uses language servers (LSPs) to answer queries.
```
Usage: oaide <subcommand> [<args>]
Subcommands:
diagnostics <subsubcommand> [<args>]
- List diagnostics counts by file: {"cmd":["oaide","diagnostics","get","--paths-only"]}
- Get diagnostics for workspace: {"cmd":["oaide","diagnostics","get"]}
- Get diagnostics for file: {"cmd":["oaide","diagnostics","get","path/to/file.py"]}
symbol [--max-count <max_count>] <symbol>
- Perform workspace symbol search for SYMBOL: {"cmd":["oaide","symbol","SYMBOL"]}
file <subsubcommand> [<file> ...]
- Mark file as open: {"cmd":["oaide","file","open","path/to/file.py"]}
- Mark file as closed: {"cmd":["oaide","file","close","path/to/file.py"]}
- List open files: {"cmd":["oaide","file","list"]}
status [--json]
- Ensure the oaide server is running: {"cmd":["oaide","status"]}
request <LSP_NAME> <LSP_METHOD> <LSP_PARAMS>
- Make a direct request to an LSP: {"cmd":["oaide","request","pyright","textDocument/typeDefinition","{"textDocument":{"uri":"file:///home/<USER>/code/project/src/main.py"},"position":{"line":5,"character":33}}"]}
- Example LSP names: pyright, typescript-language-server, eclipse (Java), rust-analyzer
- Remember that LSP uses 0-based indexes for lines and characters
```
"""


CAAS_CONTAINER_PADAWAN = """
Use `container.exec` to execute commands in a terminal if needed.

Regular UNIX commands and common utilities (like python) are available.
Other than those, there's an additional `str_replace_editor` tool.

```
Usage: str_replace_editor --command <subcommand>

Subcommands:
    view --path <PATH> [--view_range START,END]
        - If `PATH` is a directory, `view` lists non-hidden files and directories up to 2 levels deep
        - If `PATH` is a file, `view` uses an extra argument `--view_range START,END` to display the result of applying `cat` from START to END.
        - If it generates a long output, it will be clipped in the middle and marked with `<file too long...`.
        - Example: {"cmd":["str_replace_editor","--command","view","--path","randm/file.py","--view_range","1,100"]}
    str_replace --path <PATH> --old <old_str> --new <new_str>
        - It replaces `old_str` with `new_str` to the file at `PATH`.
        - The `old_str` should match EXACTLY one or more consecutive lines from the original file at `PATH`.
        - If the `old_str` is not unique in the file, the replacement will not be performed. Make sure to include enough context in `old_str` to make it unique.
        - The `new_str` should contain the edited lines that should replace the `old_str`.
        - Example: {"cmd":["str_replace_editor","--command","str_replace","--path","randm/file.py","--old","\"hello\"","--new","\"hi\""]}
    create --path <PATH> --content <new_content>
        - It creates a new file at `PATH` with the `new_content`.
        - The command cannot be used if the specified `PATH` already exists as a file
        - Example: {"cmd":["str_replace_editor","--command","create","--path","randm/file.py","--content","\"def main():\\n  pass\\n  return 123\\n\""]}
    insert --path <PATH> --insert_line <line> --content <new_content>
        - It inserts `new_content` to the file at `PATH`.
        - The `new_content` will be inserted AFTER the `line` provided by insert_line
        - Example: {"cmd":["str_replace_editor","--command","insert","--path randm/file.py","--insert_line","20","--content","\"def example():\\n  pass\\n  return 123\\n\""]}
    undo_edit --path <PATH>
        - It will revert the last edit made to the file at `PATH`.
        - Example: {"cmd":["str_replace_editor","--command","undo_edit","--path","randm/file.py"]}

You must call "str_replace_editor" directly, **DO NOT** call it like ["bash", "-lc", "str_replace_editor"].
```""".strip()


TOOL_PENALTY_ANNEAL_STEPS = 20
TOOL_PENALTY_BASE = 0.05


def calculate_tool_penalty(message: chat.Message) -> float:
    """
    Computes an aux reward penalty for the **response** to a container.exec message.
    `exec_cmd`, `exec_status_code`, `exec_duration_seconds` are populated by CaasContainerTool.
    """

    if not (cmd := message.metadata.get("exec_cmd")):
        return 0.0

    if cmd[0] in ("bash", "sh", "/bin/bash", "/bin/sh"):
        try:
            # Parse out the shell command
            cmd = shlex.split(cmd[-1])
        except Exception:
            # This is not correct but OK for the heuristics below
            cmd = cmd[-1].split()

        # Unless we're piping this, there's no reason to wrap this
        match cmd:
            case [executable, *_] if executable in {"oai", "sed", "ls"} and ("|" not in cmd):
                return TOOL_PENALTY_BASE

    tool_penalty: float = 0
    # find-files and rg should be generally preferred
    if (code := message.metadata.get("exec_status_code")) and code >= 124:
        # Timeout, not found, etc. usually indicate command misuse
        tool_penalty = TOOL_PENALTY_BASE
    if (duration := message.metadata.get("exec_duration_seconds")) and duration > 30:
        # Long commands come with a cost
        tool_penalty = TOOL_PENALTY_BASE
    match cmd:
        case [executable, *_] if executable in {"find", "grep", "apply-patch", "apply_patch"}:
            tool_penalty = TOOL_PENALTY_BASE
        case ["ls", "-R", *_]:
            tool_penalty = TOOL_PENALTY_BASE
        case [executable, *_] if executable in {"oai", "sed", "ls"} and code:
            tool_penalty = TOOL_PENALTY_BASE
        case ["oai", "apply-patch", *_]:
            # v0.0.4 does not respect exit codes but outputs "Done!"
            output = render_content(message).strip()
            if not output.startswith(("Done!", "Success")):
                tool_penalty = TOOL_PENALTY_BASE
        case ["oai", "rg", *_]:
            # Ripgrep errors specifically
            response = render_content(message)
            if "IO error for operation" in response or "rg: unrecognized flag" in response:
                tool_penalty = TOOL_PENALTY_BASE

    return tool_penalty


class DeepSWECaasContainerTool(OriginalBerryCaasContainerTool):
    """
    Applies tool penalties and (forbidden) command enforcement!

    NOTE: Required command enforcement is done in IFEnforcementGrader;
    however failing forbidden commands immediately is more efficient and is a sort of weak credit assignment.
    """

    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty

    @classmethod
    def get_description(cls) -> str | None:
        return CAAS_CONTAINER_MANUAL

    # Remove feed_chars from the tool instructions
    @classmethod
    @functools.cache
    def get_names_of_functions_the_model_can_call(cls) -> Collection[str]:
        result = set(super().get_names_of_functions_the_model_can_call())
        result.discard("feed_chars")
        return result
    
    @classmethod
    @functools.cache
    def get_function_calling_function_schemas(cls) -> list[Function]:
        return [
            cls.get_function_calling_function_schema(name)
            for name in cls.get_names_of_functions_the_model_can_call() if name not in ["feed_chars", "exec"]
        ]

    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        if forbidden_commands := sample.gt_datapoint.metadata.get(ENFORCE_COMMANDS_FORBIDDEN):
            try:
                assert sample.conversation
                args = json.loads(str(sample.conversation.messages[-1].content))
                args_cmd = args.get("cmd")
                if args_cmd[0] in ("bash", "sh", "/bin/bash", "/bin/sh"):
                    args_cmd = args_cmd[-1]
                else:
                    args_cmd = shlex.join(args_cmd)
            except Exception:
                args_cmd = ""

            if args_cmd:
                for forbidden_command in forbidden_commands:
                    if args_cmd.startswith(forbidden_command):
                        # The sample attempted to execute a forbidden command.
                        # Raise a ModelError to abort further processing.
                        raise errors.ModelError(
                            label="used_enforced_forbidden_command",
                            description="Attempted to call a forbidden tool command",
                            sample=sample,
                        )

        sample.metrics.setdefault("tool_penalty", 0.0)  # For correct wandb average
        async for message in super()._process_sample_inner(sample):
            yield message

            # Only apply tool penalties starting from step 10 onwards
            try:
                if (policy_step := sample.metadata.get("sampling/policy_step_initial")) and (
                    "exec_cmd" in message.metadata
                ):
                    # This message is the first **response** tool message
                    multiplier = min(1.0, policy_step / TOOL_PENALTY_ANNEAL_STEPS)
                    # (Penalty must be negative!)
                    penalty = (
                        multiplier * self.tool_penalty_multiplier * calculate_tool_penalty(message)
                    )
                    sample.metrics["tool_penalty"] -= penalty
                    if (
                        self.max_tool_penalty is not None
                        and sample.metrics["tool_penalty"] < -self.max_tool_penalty
                    ):
                        sample.metrics["tool_penalty"] = -self.max_tool_penalty
            except Exception:
                logger.exception("Error applying tool penalty", exc_info=True)


@chz.chz(typecheck=True)
class DeepSWECaasContainerToolConfig(OriginalCaasContainerToolConfig):
    tool_timeout: int = 10 * 60
    exec_output_processor_path: str | None = (
        "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown"] = "no_access"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_channel: BerryChannel | None = chz.field(
        doc="What channel the tool should go in", default=BerryChannel.CHAIN_OF_THOUGHT
    )

    def get_channel_for_tool_call(self) -> BerryChannel | None:
        return self.tool_channel

    def get_tool_name(self) -> str:
        return DeepSWECaasContainerTool.get_tool_name()

    def unique_descriptor_for_variants(self) -> str:
        return DeepSWECaasContainerTool.get_tool_name()

    @cached_property
    def _tool_for_tool_info(self) -> DeepSWECaasContainerTool:
        """Override to use VSCodeTool instead of base BerryCaasContainerTool for tools_format_version=v2 support."""
        tool = DeepSWECaasContainerTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = DeepSWECaasContainerTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
        )
        return tool.instruction()

    def get_names_of_functions_the_model_can_call(self, datapoint: HarmonyDatapoint | None = None) -> Collection[str]:
        return DeepSWECaasContainerTool.get_names_of_functions_the_model_can_call()

    @property
    def _caas_resource_name(self) -> str:
        if self.caas_resource_name_override is not None:
            return self.caas_resource_name_override
        else:
            return super()._caas_resource_name

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = DeepSWECaasContainerTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        return tool
