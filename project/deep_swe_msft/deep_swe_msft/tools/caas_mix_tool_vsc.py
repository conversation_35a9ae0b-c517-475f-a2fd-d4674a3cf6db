import functools
import random
import json
import shlex
import difflib
from typing import Any, AsyncIterator, Collection, Literal, final
from functools import cached_property

import structlog
import orjson
import chat
import chz
from typing import Literal
from functions import Function, FunctionNamespace
from caas_tool.caas_container import <PERSON>aasContainer
from chat.render.common import render_content
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from berry_harmony.tools.berry_tool_interface import BerryTool
from berry_caas_container_tool.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from berry_caas_container_tool.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
    _caas_container_resource_name
)

try:
    from berry_caas_container_tool.caas_container_tool import (
        ToolPluginConfig as CaasToolPluginConfig,
        create_tool_plugins as create_caas_tool_plugins,
    )
except ImportError:
    # Version prior to July FI:
    from berry_caas_container_tool.caas_container_tool import (
        CaasToolPluginConfig,
        create_caas_tool_plugins,
    )

from deep_swe_msft.tools.caas_vscode_tool import VSCodeTool
from chat.render.v4.experimental.strawberry.formatter import BerryChannel

logger = structlog.stdlib.get_logger(component=__name__)

TOOL_MAXTOKENS = 4096

MUST_VARIANTS = ['read_file', 'grep_search']
EDITOR_VARIANTS = ['apply_patch',]
BASH_VARIANTS = ['run_in_terminal']
TEST_VARIANTS = ['runTests']
VIEW_VARIANTS = ['read_file', 'semantic_search']
OTHER_VARIANTS = ['grep_search', 'file_search', 'list_dir', 'create_file', 'get_errors',
                  'test_search', 'search_workspace_symbols', 'list_code_usages']

def _get_random_tool_set() -> list[str]:
    """
    Randomly selects a set of tools from the available variants.
    Ensures at least one tool from each essential category is included.
    """
    result = set(MUST_VARIANTS)

    # Always ensure we have one tool from each essential category
    # Edit tool (required) - choose between individual variant or complete set
    selected_edit_tool = random.choice(EDITOR_VARIANTS)
    result.add(selected_edit_tool)
    
    # Bash tool (optional) - randomly select one or zero variant
    # selected_bash_tool = random.choices(BASH_VARIANTS, k=random.randint(0,1))
    # for tool in selected_bash_tool:
    #     result.add(tool)
    #<TODO> remove this after run, temp lower bash probs.
    if random.random() < 0.2:
        result.add(BASH_VARIANTS[0])

    # View tool (required) - randomly select one variant
    selected_view_tool = random.choice(VIEW_VARIANTS)
    result.add(selected_view_tool)

    # Other tool (optional) - randomly select one variant
    selected_tool = random.choices(OTHER_VARIANTS, k=random.randint(1, len(OTHER_VARIANTS)))
    for tool in selected_tool:
        result.add(tool)

    # View tool (required) - randomly select one variant
    selected_test_tool = random.choice(TEST_VARIANTS)
    result.add(selected_test_tool)

    if random.random() < 0.5:
        shuffle_result = list(result)
        random.shuffle(shuffle_result)
        result = set(shuffle_result)

    return list(result)


class VSCMixTool(VSCodeTool):
    """
    Applies tool penalties and (forbidden) command enforcement!

    NOTE: Required command enforcement is done in IFEnforcementGrader;
    however failing forbidden commands immediately is more efficient and is a sort of weak credit assignment.
    """

    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        default_exec_timeout: int | None = 10_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = 'default',
        real_tool: Literal["real", "fake", "skip"] = "fake",
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set
        # Generate and store the random tool set for this instance
        self._random_tool_set = _get_random_tool_set()
        self.real_tool = real_tool
    
    @classmethod
    def get_tool_name(cls) -> str:
        return "functions"
    
    @classmethod
    def get_description(cls) -> str | None:
        return 'Microsoft VSC tool for code generation and editing'
    
    def get_function_calling_function_schemas(self) -> list[Function]:
        return [
            self.get_function_calling_function_schema(name)
            for name in self.get_names_of_functions_the_model_can_call() if name not in ["feed_chars", "exec"] and name in self._random_tool_set
        ]

@chz.chz(typecheck=True)
class VSCMixToolConfig(OriginalCaasContainerToolConfig):
    exec_output_processor_path: str | None = (
        "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown", "enabled"] = "no_access"

    tool_penalty_multiplier: float = 2.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_timeout: int = 1_980
    default_exec_timeout: int = 600_000

    tool_channel: BerryChannel | None = chz.field(
        doc="What channel the tool should go in", default=BerryChannel.CHAIN_OF_THOUGHT
    )

    real_tool: Literal["real", "fake", "skip"] = chz.field(
        default="fake",
        doc="""Values: fake, real, skipped. If 'real', the tool will be a real VSCode tool. If 'fake', it will be a mock tool.
If 'skipped', the test output will be mocked as though the tests were skipped.""",
    )

    def get_channel_for_tool_call(self) -> BerryChannel | None:
        return self.tool_channel

    def get_tool_name(self) -> str:
        return VSCMixTool.get_tool_name()

    def unique_descriptor_for_variants(self) -> str:
        return VSCMixTool.get_tool_name()
    
    @cached_property
    def _tool_for_tool_info(self) -> VSCMixTool:
        """Override to use VSCMixTool instead of base BerryCaasContainerTool for tools_format_version=v2 support."""
        tool = VSCMixTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
            real_tool=self.real_tool,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool

    @property
    def _caas_resource_name(self) -> str:
        return "caas_container"

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = VSCMixTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
            real_tool=self.real_tool,
        )
        return tool

