# Generate a SAS token for the blob
import os
from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobServiceClient, generate_container_sas, ContainerSasPermissions
from datetime import datetime, timedelta

# CAAS_ENDPOINT = "https://eastus2-09.privatelink.caas.azure.com"
CAAS_ENDPOINT = "https://eastus2.caas.azure.com"
# CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"

# CAAS_IMAGE = "actions-runner-terminal-server"
CAAS_IMAGE = "actions-runner-terminal-server-padawan:********"

MIX_VSC_TOOL = False

def get_strawberry_ace_token():

    # Set up the credentials and BlobServiceClient
    credential = DefaultAzureCredential()
    blob_service_client = BlobServiceClient(account_url="https://orngcaas.blob.core.windows.net", credential=credential)

    # Set the expiry time for the SAS token
    expiry_time = datetime.now() + timedelta(hours=1)  # Token valid for 1 hour
    # Generate the user delegation SAS token
    user_delegation_key = blob_service_client.get_user_delegation_key(datetime.now(), expiry_time)
    sas_token = generate_container_sas(
        account_name="orngcaas",
        container_name="data",
        user_delegation_key=user_delegation_key,
        permission=ContainerSasPermissions(read=True),  # Set permissions as needed
        expiry=expiry_time
    )

    return sas_token

# For installing nodejs
from caas.protocol import VolumeMount

TMP_DRI="/usr/local/nodejsinstall"
NODEJS_VERSION='22.14.0'
BLOB_NAME = f"node-v{NODEJS_VERSION}-linux-x64.tar.gz"

DEFAULT_CONTAINER_CERT_PATH = "/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt"
MNT_CONTAINER_CERT_PATH = "/mnt/azure_blob/certs/mitmproxy-ca-cert.crt"

PDW_MNT = [VolumeMount(host=f"/mnt/azure_blob/tools/{BLOB_NAME}", container=f"{TMP_DRI}/{BLOB_NAME}", deprecated_use_blobfuse=True),
           VolumeMount(host=MNT_CONTAINER_CERT_PATH, container=DEFAULT_CONTAINER_CERT_PATH, deprecated_use_blobfuse=True)]

CRESCO_STORAGE_NAME = "orngcresco" if "uksouth" in os.environ.get("RCALL_KUBE_CLUSTER", "") else "orngscuscresco"

PADAWAN_TOOL_ERROR_MSGS = [
    'Error parsing function call:',
    'Could not parse args as JSON:',
    'parameter is required',
    'Unrecognized function name', 'Invalid functions.', 'Wrong functions (with', 'Model cannot call'
]

CAAS_ENVS = {
    "NODE_EXTRA_CA_CERTS": DEFAULT_CONTAINER_CERT_PATH
}