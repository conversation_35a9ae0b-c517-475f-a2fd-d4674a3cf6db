import functools
import json
import re
from functools import cached_property
from functions import Function
from typing import Annotated, Any, AsyncIterator, Collection, Literal

import chat
import chz
import structlog
from caas_tool.caas_container import CaasContainer
from chat.render.common import render_content
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from function_calling_tool import function_the_model_can_call
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from berry_harmony.tools.berry_tool_interface import BerryTool
from berry_caas_container_tool.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from berry_caas_container_tool.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
)
try:
    from berry_caas_container_tool.caas_container_tool import (
        ToolPluginConfig as CaasToolPluginConfig,
        create_tool_plugins as create_caas_tool_plugins,
    )
except ImportError:
    # Version prior to July FI:
    from berry_caas_container_tool.caas_container_tool import (
        CaasToolPluginConfig,
        create_caas_tool_plugins,
    )

from deep_swe_msft.vsc_data.datapoint_converters import ENFORCE_COMMANDS_FORBIDDEN
from deep_swe_msft.tools.vscode_copilot_tool import exec as caas_exec
from chat.render.v4.experimental.strawberry.formatter import BerryChannel

logger = structlog.stdlib.get_logger(component=__name__)


TOOL_MAXTOKENS = 8192


def _process_tool_message(message: str, is_read_file: bool = False, is_run_tests: bool = False) -> str:
    """
    Truncate the message to a maximum of TOOL_MAXTOKENS tokens.
    """
    max_tokens = TOOL_MAXTOKENS
    if is_read_file:
        max_tokens = TOOL_MAXTOKENS * 2
    if is_run_tests:
        max_tokens = TOOL_MAXTOKENS * 1

    if len(message) > max_tokens:
        # Truncate the message content to TOOL_MAXTOKENS tokens
        if is_read_file:
            message = message[:max_tokens] + " <reached max tokens>..."
        else:
            message = message[:max_tokens // 2] + "\n<truncated due to exceeding max tokens>\n" + message[-max_tokens // 2:]
    return message

import re

def _detect_hardcode_forbidden_tool(cmd: str) -> bool:
    """
    Return True if *cmd* appears to invoke pytest **or**
    calls a Bash script named “run_tests.sh”.

    Detects patterns such as:
      • pytest …                     → “pytest -q”, “python -m pytest tests/…”
      • bash run_tests.sh …          → “bash ./run_tests.sh”, “cd testbed && bash run_tests.sh”
    """
    c = cmd.strip().lower()

    # 1) Any direct or module-style call to pytest
    if re.search(r'\bpytest\b', c) or re.search(r'\bpython\b.*\b-m\b\s*pytest\b', c):
        return True

    # 2) Any bash invocation of run_tests.sh (with or without path)
    if re.search(r'\bbash\b[^\n]*run_tests\.sh\b', c):
        return True

    return False

class VSCodeTool(OriginalBerryCaasContainerTool):
    """
    Applies tool penalties and (forbidden) command enforcement!

    NOTE: Required command enforcement is done in IFEnforcementGrader;
    however failing forbidden commands immediately is more efficient and is a sort of weak credit assignment.
    """

    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        default_exec_timeout: int | None = 600_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = "default",
        real_tool: Literal["real", "fake", "skip"] = "fake",
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set
        self.real_tool = real_tool

    @classmethod
    def get_tool_name(cls) -> str:
        return "functions"

    @classmethod
    def get_description(cls) -> str | None:
        return 'Microsoft VSC tool for code generation and editing'

    @classmethod
    @functools.cache
    def get_function_calling_function_schemas(cls) -> list[Function]:
        return [
            cls.get_function_calling_function_schema(name)
            for name in cls.get_names_of_functions_the_model_can_call() if name not in ["feed_chars", "exec"]
        ]
    
    @function_the_model_can_call
    async def grep_search(
        self,
        query: Annotated[
            str,
            "The pattern to search for in files in the workspace. Use regex with alternation (e.g., 'word1|word2|word3') or character classes to find multiple potential words in a single search. The isRegexp property declares whether it's a regex or plain text pattern. Is case-insensitive.",
        ],
        isRegexp: Annotated[
            bool | None, "Whether the pattern is a regex. False by default.",
        ] = None,
        includePattern: Annotated[
            str | None,
            "Search files matching this glob pattern. Will be applied to the relative path of files within the workspace.",
        ] = None,
        maxResults: Annotated[
            int | None, "The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults.",
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Do a fast text search in the workspace. Use this tool when you want to search with an exact string or regex. If you are not sure what words will appear in the workspace, prefer using regex patterns with alternation (|) or character classes to search for multiple potential words at once instead of making separate searches. For example, use 'function|method|procedure' to look for all of those words at once. Use includePattern to search within files matching a specific pattern, or in a specific file, using a relative path. Use this tool when you want to see an overview of a particular file, instead of using read_file many times to look for code within a file.
        """
        cmd = {"query": query}

        if isRegexp is not None:
            cmd["isRegexp"] = isRegexp
        if includePattern is not None:
            cmd["includePattern"] = includePattern
        if maxResults is not None:
            cmd["maxResults"] = maxResults

        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="grep_search",
            tool_params=cmd,
            timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def apply_patch(
        self,
        input: Annotated[str, "The edit patch to apply."],
        explanation: Annotated[str, "A short description of what the tool call is aiming to achieve."],
    ) -> AsyncIterator[chat.Message]:
        """
        Edit text files. Do not use this tool to edit Jupyter notebooks. `apply_patch` allows you to execute a diff/patch against a text file, but the format of the diff specification is unique to this task, so pay careful attention to these instructions. To use the `apply_patch` command, you should pass a message of the following structure as \"input\":
        
        *** Begin Patch
        [YOUR_PATCH]
        *** End Patch
        
        Where [YOUR_PATCH] is the actual content of your patch, specified in the following V4A diff format.
        
        *** [ACTION] File: [/absolute/path/to/file] -> ACTION can be one of Add, Update, or Delete.
        An example of a message that you might pass as \"input\" to this function, in order to apply a patch, is shown below.
        
        *** Begin Patch
        *** Update File: /Users/<USER>/pygorithm/searching/binary_search.py
        @@class BaseClass
        @@    def search():
        -        pass
        +        raise NotImplementedError()
        
        @@class Subclass
        @@    def search():
        -        pass
        +        raise NotImplementedError()
        
        *** End Patch
        Do not use line numbers in this diff format.
        """
        cmd = {"input": input, "explanation": explanation}
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="apply_patch",
            tool_params=cmd,
            timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def file_search(
        self,
        query: Annotated[str, "Search for files with names or paths matching this glob pattern."],
        maxResults: Annotated[
            int | None,
            "The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults.",
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Search for files in the workspace by glob pattern. This only returns the paths of matching files. Use this tool when you know the exact filename pattern of the files you're searching for. Glob patterns match from the root of the workspace folder. Examples:
        - **/*.{js,ts} to match all js/ts files in the workspace.
        - src/** to match all files under the top-level src folder.
        - **/foo/**/*.js to match all js files under any foo folder in the workspace.
        """
        cmd = {"query": query}
        if maxResults is not None:
            cmd["maxResults"] = maxResults
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="file_search",
            tool_params=cmd,
            timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def read_file(
        self,
        filePath: Annotated[str, "The absolute path of the file to read."],
        offset: Annotated[int, "Optional: the 1-based line number to start reading from. Only use this if the file is too large to read at once. If not specified, the file will be read from the beginning."] = 1,
        limit: Annotated[
            int, "Optional: the maximum number of lines to read. Only use this together with `offset` if the file is too large to read at once."
        ] = 2000,
    ) -> AsyncIterator[chat.Message]:
        """
        Read the contents of a file. Line numbers are 1-indexed. This tool will truncate its output at 2000 lines or 16K words and may be called repeatedly with offset and limit parameters to read larger files in chunks.
        """
        cmd = {
            "filePath": filePath,
            "offset": offset,
            "limit": limit,
        }
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="read_file",
            tool_params=cmd,
            timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message, is_read_file=True)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def list_dir(
        self,
        path: Annotated[str, "The absolute path to the directory to list."],
    ) -> AsyncIterator[chat.Message]:
        """
        List the contents of a directory. Result will have the name of the child. If the name ends in /, it's a folder, otherwise a file
        """
        cmd = {"path": path}
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="list_dir",
            tool_params=cmd,
            timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def run_in_terminal(
        self,
        command: Annotated[str, "The command to run in the terminal."],
        explanation: Annotated[
            str,
            "A one-sentence description of what the command does. This will be shown to the user before the command is run.",
        ],
        isBackground: Annotated[
            bool,
            "Whether the command starts a background process. If true, the command will run in the background and you will not see the output. If false, the tool call will block on the command finishing, and then you will get the output. Examples of background processes: building in watch mode, starting a server. You can check the output of a backgrond process later on by using get_terminal_output.",
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        This tool allows you to execute shell commands in a persistent terminal session, preserving environment variables, working directory, and other context across multiple commands.
        
        Command Execution:
        - Supports multi-line commands 
        
        Directory Management:
        - Must use absolute paths to avoid navigation issues.
        
        Program Execution:
        - Supports Python, Node.js, and other executables.
        - Install dependencies via pip, npm, etc.
        
        Background Processes:
        - For long-running tasks (e.g., servers), set isBackground=true.
        - Returns a terminal ID for checking status and runtime later.
        
        Output Management:
        - Output is automatically truncated if longer than 60KB to prevent context overflow
        - Use filters like 'head', 'tail', 'grep' to limit output size
        - For pager commands, disable paging: use 'git --no-pager' or add '| cat'
        
        Best Practices:
        - Be specific with commands to avoid excessive output
        - Use targeted queries instead of broad scans
        - Consider using 'wc -l' to count before listing many items
        """
        cmd = {
            "command": command,
            "explanation": explanation,
            "isBackground": isBackground,
        }

        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="run_in_terminal",
            tool_params=cmd,
            timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def semantic_search(
        self,
        query: Annotated[
            str,
            "The query to search the codebase for. Should contain all relevant context. Should ideally be text that might appear in the codebase, such as function names, variable names, or comments.",
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        Run a natural language search for relevant code or documentation comments from the user's current workspace. Returns relevant code snippets from the user's current workspace if it is large, or the full contents of the workspace if it is small.
        """
        cmd = {"query": query}
        message, metadata = await caas_exec(
            self.container.caas_session, 
            tool_name="semantic_search", 
            tool_params=cmd,
            timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def create_file(
        self,
        filePath: Annotated[str, "The absolute path to the file to create."],
        content: Annotated[str, "The content to write to the file."],
    ) -> AsyncIterator[chat.Message]:
        """
        This is a tool for creating a new file in the workspace. The file will be created with the specified content. The directory will be created if it does not already exist. Never use this tool to edit a file that already exists.
        """
        cmd = {"filePath": filePath, "content": content}
        message, metadata = await caas_exec(
            self.container.caas_session, tool_name="create_file", tool_params=cmd, timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        print(f"create_file: {message}")
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def get_errors(
        self,
        filePaths: Annotated[list[str], "The absolute path to the file to check for errors."],
    ) -> AsyncIterator[chat.Message]:
        """
        Get any compile or lint errors in a code file. If the user mentions errors or problems in a file, they may be referring to these. Use the tool to see the same errors that the user is seeing. Also use this tool after editing a file to validate the change.
        """
        cmd = {"filePaths": filePaths}
        message, metadata = await caas_exec(
            self.container.caas_session, tool_name="get_errors", tool_params=cmd, timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        print(f"get_errors: {message}")
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def test_search(
        self,
        filePaths: Annotated[
            list[str], "The absolute path to the file to check for test cases."
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        For a source code file, find the file that contains the tests. For a test file find the file that contains the code under test.
        """
        cmd = {"filePaths": filePaths}
        message, metadata = await caas_exec(
            self.container.caas_session, tool_name="test_search", tool_params=cmd, timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def search_workspace_symbols(
        self,
        symbolName: Annotated[
            str,
            "The symbol to search for, such as a function name, class name, or variable name.",
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        Search the user's workspace for code symbols using language services. Use this tool when the user is looking for a specific symbol in their workspace.
        """
        cmd = {"symbolName": symbolName}
        message, metadata = await caas_exec(
            self.container.caas_session, tool_name="search_workspace_symbols", tool_params=cmd, timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def list_code_usages(
        self,
        symbolName: Annotated[
            str,
            "The name of the symbol, such as a function name, class name, method name, variable name, etc.",
        ],
        filePaths: Annotated[
            list[str] | None,
            "One or more file paths which likely contain the definition of the symbol. For instance the file which declares a class or function. This is optional but will speed up the invocation of this tool and improve the quality of its output.",
        ] = None
    ) -> AsyncIterator[chat.Message]:
        """
        Request to list all usages (references, definitions, implementations etc) of a function, class, method, variable etc. Use this tool when 
        1. Looking for a sample implementation of an interface or class
        2. Checking how a function is used throughout the codebase.
        3. Including and updating all usages when changing a function, method, or constructor
        """
        cmd = {"symbolName": symbolName}
        if filePaths is not None:
            cmd["filePaths"] = filePaths
        message, metadata = await caas_exec(
            self.container.caas_session, tool_name="list_code_usages", tool_params=cmd, timeout=self._default_exec_timeout // 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def install_extension(
        self,
        id: Annotated[
            str,
            "The ID of the extension to install. This should be in the format <publisher>.<extension>.",
        ],
        name: Annotated[
            str | None,
            "The name of the extension to install. This should be a clear and concise description of the extension.",
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        Install an extension in VS Code. Use this tool to install an extension in Visual Studio Code as part of a new workspace creation process only.
        """
        cmd = {"id": id, "name": name}
        if name is not None:
            cmd["name"] = name
        message, metadata = await caas_exec(
            self.container.caas_session, tool_name="install_extension", tool_params=cmd
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def vscode_searchExtensions_internal(
        self,
        category: Annotated[
            Literal[
                "AI",
                "Azure",
                "Chat",
                "Data Science",
                "Debuggers",
                "Extension Packs",
                "Education",
                "Formatters",
                "Keymaps",
                "Language Packs",
                "Linters",
                "Machine Learning",
                "Notebooks",
                "Programming Languages",
                "SCM Providers",
                "Snippets",
                "Testing",
                "Themes",
                "Visualization",
                "Other",
            ] | None,
            "The category of extensions to search for",
        ] = None,
        keywords: Annotated[
            list[str] | None,
            "The keywords to search for",
        ] = None,
        ids: Annotated[
            list[str] | None,
            "The ids of the extensions to search for",
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        This is a tool for browsing Visual Studio Code Extensions Marketplace. It allows the model to search for extensions and retrieve detailed information about them. The model should use this tool whenever it needs to discover extensions or resolve information about known ones. To use the tool, the model has to provide the category of the extensions, relevant search keywords, or known extension IDs. Note that search results may include false positives, so reviewing and filtering is recommended.
        """
        cmd = {}
        if category is not None:
            cmd["category"] = category
        if keywords is not None:
            cmd["keywords"] = keywords
        if ids is not None:
            cmd["ids"] = ids

        message, metadata = await caas_exec(
            self.container.caas_session, tool_name="vscode_searchExtensions_internal", tool_params=cmd
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def get_terminal_output(
        self,
        id: Annotated[
            str,
            "The ID of the terminal command output to check.",
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        Get the output of a terminal command previous started with copilot_runInTerminal
        """
        cmd = {"id": id}
        message, metadata = await caas_exec(
            self.container.caas_session, tool_name="get_terminal_output", tool_params=cmd
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def runTests(
        self,
        files: Annotated[
            list[str],
            "Test files to run. If not provided, all test files will be run.",
        ] = [],
    ) -> AsyncIterator[chat.Message]:
        """
        Run unit tests in files. Use this tool if the user asks to run tests or when you want to validate changes using unit tests. When possible, always try to provide `files` paths containing the relevant unit tests in order to avoid unnecessarily long test runs.
        """
        if self.real_tool == "fake":
            # If specific files are provided, run only those files
            # Join the files with spaces and escape them properly
            files_str = " ".join(f'"{f}"' for f in files)
            
            cmd = {
                "command": f"cd $WORKSPACEFOLDER; (if [ -f run_tests.sh ]; then timeout 800 bash run_tests.sh; else timeout 800 pytest {files_str}; fi)",
                "explanation": f"Run tests in specific files: {', '.join(files)}" if files else "Run all tests in the workspace",
                "isBackground": False,
            }

            message, metadata = await caas_exec(
                self.container.caas_session,
                tool_name="run_in_terminal",
                tool_params=cmd,
                timeout=self._default_exec_timeout // 1000,
            )
            # Process the message to extract the output
            message = message.split("and this is the output of running that command instead:")
            if len(message) > 1:
                message = message[1]
            else:
                message = message[0]

            message = _process_tool_message(message, is_run_tests=True)

            if "timeout: failed to run command" in message:
                message = f"""{message}

runTests failed due to timeout. This is likely because the tests are taking too long to run or are running infinitely. If the above issues persist, consider skipping runTests and focusing on implementing the required functionality instead."""
            elif "pytest: command not found" in message:
                message = f"""{message}

runTests failed because pytest is not installed. To fix this, install pytest by running `pip install pytest` or `pip3 install pytest` in your terminal. If the above issues persist, consider skipping runTests and focusing on implementing the required functionality instead."""
        elif self.real_tool == "skip":
            message = f"""runTests was skipped. This is likely because the tests were not applicable or the environment was not set up correctly. You likely don't need to run them again. If the above issues persist, consider focusing on implementing the required functionality instead."""
            metadata = None
        else:
            cmd = {"files": files}
            message, metadata = await caas_exec(
                self.container.caas_session, tool_name="run_tests", tool_params=cmd, timeout=self._default_exec_timeout // 1000
            )
            message = _process_tool_message(message, is_run_tests=True)

        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def manage_todo_list(
        self,
        operation: Annotated[
            Literal["write", "read"],
            "write: Replace entire todo list with new content. read: Retrieve current todo list. ALWAYS provide complete list when writing - partial updates not supported.",
        ],
        todoList: Annotated[
            list[dict],
            "Complete array of all todo items (required for write operation, ignored for read). Must include ALL items - both existing and new.",
        ] | None = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Manage a structured todo list to track progress and plan tasks throughout your coding session. Use this tool VERY frequently to ensure task visibility and proper planning.
        
        When to use this tool:
        - Complex multi-step work requiring planning and tracking
        - When user provides multiple tasks or requests (numbered/comma-separated)
        - After receiving new instructions that require multiple steps
        - BEFORE starting work on any todo (mark as in-progress)
        - IMMEDIATELY after completing each todo (mark completed individually)
        - When breaking down larger tasks into smaller actionable steps
        - To give users visibility into your progress and planning
        
        When NOT to use:
        - Single, trivial tasks that can be completed in one step
        - Purely conversational/informational requests
        - When just reading files or performing simple searches
        
        CRITICAL workflow:
        1. Plan tasks by writing todo list with specific, actionable items
        2. Mark ONE todo as in-progress before starting work
        3. Complete the work for that specific todo
        4. Mark that todo as completed IMMEDIATELY
        5. Move to next todo and repeat
        
        Todo states:
        - not-started: Todo not yet begun
        - in-progress: Currently working (limit ONE at a time)
        - completed: Finished successfully
        
        todoList item structure (all fields required):
        - id: Unique identifier for the todo. Use sequential numbers starting from 1.
        - title: Concise action-oriented todo label (3-7 words). Displayed in UI.
        - description: Detailed context, requirements, or implementation notes. Include file paths, specific methods, or acceptance criteria.
        - status: not-started (Not begun) | in-progress (Currently working, max 1) | completed (Fully finished with no blockers)

        IMPORTANT: Mark todos completed as soon as they are done. Do not batch completions.
        """
        TODO_FILE = "/home/<USER>"

        async def _run_capture(cmd: str, explanation: str) -> str:
            try:
                params = {"command": cmd, "explanation": explanation, "isBackground": False}
                out, _md = await caas_exec(
                    self.container.caas_session,
                    tool_name="run_in_terminal",
                    tool_params=params,
                    timeout=self._default_exec_timeout // 1000,
                )
                splitter = "and this is the output of running that command instead:"
                if splitter in out:
                    out = out.split(splitter, 1)[1]
                return out.strip()
            except Exception as e:
                return f"__ERR__:{e}"

        async def _load() -> tuple[list[dict], str | None]:
            raw = await _run_capture(f'[ -f "{TODO_FILE}" ] && cat "{TODO_FILE}" || (touch "{TODO_FILE}" && echo "")', "Load todo list JSON")
            if raw.startswith("__ERR__:"):
                return [], raw.split(":", 1)[1]
            if raw == "Command produced no output":
                return [], None
            if not raw.strip():
                return [], None
            try:
                data = json.loads(raw)
                if isinstance(data, list):
                    return data, None
                return [], "Stored data is not a list; resetting."
            except Exception as e:
                return [], f"Corrupted JSON: {e}"

        async def _save(lst: list[dict]) -> tuple[bool, str | None]:
            try:
                js = json.dumps(lst, ensure_ascii=False, separators=(",", ":"))
            except Exception:
                return False, "Failed to serialize JSON"
            shell = f"cat > '{TODO_FILE}' <<'EOF'\n{js}\nEOF"
            res = await _run_capture(shell, "Save todo list JSON")
            if res.startswith("__ERR__:"):
                return False, res.split(":", 1)[1]
            return True, None

        def _markdown(lst: list[dict]) -> str:
            if not lst:
                return "No todo list found."
            lines = ["# Todo List", ""]
            for it in lst:
                st = it.get("status", "not-started")
                box = "[x]" if st == "completed" else "[-]" if st == "in-progress" else "[ ]"
                title = it.get("title", "(missing title)")
                lines.append(f"- {box} {title}")
                desc = (it.get("description") or "").strip()
                if desc:
                    for d in desc.splitlines():
                        lines.append(f"  - {d.strip()}")
            return "\n".join(lines)

        def _changes(old: list[dict], new: list[dict]) -> int:
            modified = 0
            for o, n in zip(old, new):
                if (o.get("title"), (o.get("description") or ""), o.get("status")) != (
                    n.get("title"), (n.get("description") or ""), n.get("status")
                ):
                    modified += 1
            added = max(0, len(new) - len(old))
            removed = max(0, len(old) - len(new))
            return modified + added + removed

        VALID = {"not-started", "in-progress", "completed"}
        _ = await _run_capture("mkdir -p /home", "Ensure home dir exists")

        if operation == "read":
            existing, load_err = await _load()
            if load_err:
                yield self.make_response(chat.Text.from_string(f"Error: Failed to read todo list: {load_err}"), metadata=None)
                return
            if not existing:
                yield self.make_response(chat.Text.from_string("No todo list found. Please plan your todo lists before reading"), metadata=None)
                return
            yield self.make_response(chat.Text.from_string(_markdown(existing)), metadata=None)
            return

        # write operation
        if todoList is None:
            yield self.make_response(chat.Text.from_string("Error: todoList is required for write operation"), metadata=None)
            return

        # validate
        normalized: list[dict] = []
        req = {"id", "title", "description", "status"}
        in_progress_count = 0
        for item in todoList:
            if not isinstance(item, dict):
                yield self.make_response(chat.Text.from_string("Error: Each todo must be an object"), metadata=None)
                return
            miss = req - item.keys()
            if miss:
                yield self.make_response(chat.Text.from_string(f"Error: Missing required fields: {', '.join(sorted(miss))}"), metadata=None)
                return
            if item.get("status") not in VALID:
                yield self.make_response(chat.Text.from_string(f"Error: Invalid status '{item.get('status')}'"), metadata=None)
                return
            
            # Count in-progress items
            if item.get("status") == "in-progress":
                in_progress_count += 1
            
            normalized.append({
                "id": int(item["id"]),
                "title": str(item["title"]).strip(),
                "description": str(item["description"]).rstrip(),
                "status": item["status"],
            })

        # Check that at most one item is in-progress
        if in_progress_count > 1:
            yield self.make_response(chat.Text.from_string("Error: Only one todo can be in-progress at a time. Please complete or revert other in-progress todos first."), metadata=None)
            return

        ids = [t["id"] for t in normalized]
        if not all(ids[i] == i + 1 for i in range(len(ids))):
            yield self.make_response(chat.Text.from_string("Error: IDs must be sequential starting at 1 and in ascending order with increments of 1"), metadata=None)
            return

        existing, load_err = await _load()
        if load_err:
            # We can proceed (treat as empty) but inform user in warnings later
            existing = []
        changes = _changes(existing, normalized)
        ok, save_err = await _save(normalized)
        if not ok:
            yield self.make_response(chat.Text.from_string(f"Error: Failed to persist todo list: {save_err}"), metadata=None)
            return

        warnings: list[str] = []
        if len(normalized) < 3:
            warnings.append("Warning: Small todo list (<3 items). This task might not need a todo list.")
        elif len(normalized) > 10:
            warnings.append("Warning: Large todo list (>10 items). Consider keeping the list focused and actionable.")
        if changes > 3 and existing:
            warnings.append("Warning: Did you mean to update so many todos at the same time? Consider working on them one by one.")

        msg = "Successfully wrote todo list"
        if load_err:
            warnings.insert(0, f"Warning: Previous todo list could not be read: {load_err}")
        if warnings:
            msg += "\n\n" + "\n".join(warnings)
        yield self.make_response(chat.Text.from_string(msg), metadata=None)

    # For applying penalties to bash command and enforcing forbidden commands
    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        if forbidden_commands := sample.gt_datapoint.metadata.get(ENFORCE_COMMANDS_FORBIDDEN):
            try:
                assert sample.conversation
                if sample.conversation.messages[-1].recipient == "functions.run_in_terminal":
                    args = json.loads(str(sample.conversation.messages[-1].content))
                    args_cmd = args.get("command")
                else:
                    args_cmd = None
                
                ## encourage the read_file lines:
                # if sample.conversation.messages[-1].recipient == "functions.read_file":
                #     read_file_args = json.loads(str(sample.conversation.messages[-1].content))
                #     read_file_args_limit = read_file_args.get("limit", 2000)
                # else:
                #     read_file_args_limit = None
            except Exception:
                args_cmd = ""
                # read_file_args_limit = None

            if args_cmd:
                for forbidden_command in forbidden_commands:
                    if args_cmd.startswith(forbidden_command) or _detect_hardcode_forbidden_tool(args_cmd):
                        # The sample attempted to execute a forbidden command.
                        # Raise a ModelError to abort further processing.
                        raise errors.ModelError(
                            label="used_enforced_forbidden_command",
                            description="Attempted to call a forbidden tool command",
                            sample=sample,
                        )

            # if read_file_args_limit:
            #     if int(read_file_args_limit) < MINIMAL_LINE_TO_READ:
            #         raise errors.ModelError(
            #             label="used_enforced_forbidden_command",
            #             description="Attempted to read small number of lines",
            #             sample=sample,
            #         )

        sample.metrics.setdefault("tool_penalty", 0.0)  # For correct wandb average
        async for message in super()._process_sample_inner(sample):
            yield message

            # Only apply tool penalties starting from step 10 onwards
            try:
                if (policy_step := sample.metadata.get("sampling/policy_step_initial")) and (
                    "exec_cmd" in message.metadata
                ):
                    # This message is the first **response** tool message
                    multiplier = min(1.0, policy_step / TOOL_PENALTY_ANNEAL_STEPS)
                    # (Penalty must be negative!)
                    penalty = (
                        multiplier * self.tool_penalty_multiplier * calculate_tool_penalty(message)
                    )
                    sample.metrics["tool_penalty"] -= penalty
                    if (
                        self.max_tool_penalty is not None
                        and sample.metrics["tool_penalty"] < -self.max_tool_penalty
                    ):
                        sample.metrics["tool_penalty"] = -self.max_tool_penalty
            except Exception:
                logger.exception("Error applying tool penalty", exc_info=True)


# For bash tool only
TOOL_PENALTY_ANNEAL_STEPS = 10
TOOL_PENALTY_BASE = 0.02
def calculate_tool_penalty(message: chat.Message) -> float:
    """
    Computes an aux reward penalty for the **response** to a container.exec message.
    `exec_cmd`, `exec_status_code`, `exec_duration_seconds` are populated by CaasContainerTool.
    """

    tool_penalty: float = 0.0
    unwanted_cmds_in_bash = ['grep', 'ls -R', 'sed', "find", "grep", "oai", "bash"]
    if message.recipient == "functions.run_in_terminal":
        args = json.loads(str(message.content))
        args_cmd = args.get("command")
        if any(c in args_cmd for c in unwanted_cmds_in_bash):
            return TOOL_PENALTY_BASE * 2
        
    error_messages = ['VSCode Copilot tool error', 'Error parsing', 'Invalid function']
    if message.author.role == 'tool':
        response = render_content(message, tools_format_version="v2")
        if any(c in response for c in error_messages):
            tool_penalty = TOOL_PENALTY_BASE
        
    if (code := message.metadata.get("exec_status_code")) and code >= 124:
        # Timeout, not found, etc. usually indicate command misuse
        tool_penalty = TOOL_PENALTY_BASE
    if (duration := message.metadata.get("exec_duration_seconds")) and duration > 30:
        # Long commands come with a cost
        tool_penalty = TOOL_PENALTY_BASE

    return tool_penalty


@chz.chz(typecheck=True)
class VSCodeToolConfig(OriginalCaasContainerToolConfig):
    exec_output_processor_path: str | None = (
        "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown"] = "no_access"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_timeout: int = 1_980
    default_exec_timeout: int = 1000_000

    tool_channel: BerryChannel | None = chz.field(
        doc="What channel the tool should go in", default=BerryChannel.CHAIN_OF_THOUGHT
    )
    real_tool: Literal["real", "fake", "skip"] = chz.field(
        default="fake",
        doc="""Values: fake, real, skipped. If 'real', the tool will be a real VSCode tool. If 'fake', it will be a mock tool.
If 'skipped', the test output will be mocked as though the tests were skipped.""",
    )

    def get_channel_for_tool_call(self) -> BerryChannel | None:
        return self.tool_channel

    def get_tool_name(self) -> str:
        return VSCodeTool.get_tool_name()

    def unique_descriptor_for_variants(self) -> str:
        return VSCodeTool.get_tool_name()

    @cached_property
    def _tool_for_tool_info(self) -> VSCodeTool:
        """Override to use VSCodeTool instead of base BerryCaasContainerTool for tools_format_version=v2 support."""
        tool = VSCodeTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
            real_tool=self.real_tool,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool
    
    @property
    def _caas_resource_name(self) -> str:
        if self.caas_resource_name_override is not None:
            return self.caas_resource_name_override
        else:
            return super()._caas_resource_name

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = VSCodeTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
            real_tool=self.real_tool,
        )
        return tool
