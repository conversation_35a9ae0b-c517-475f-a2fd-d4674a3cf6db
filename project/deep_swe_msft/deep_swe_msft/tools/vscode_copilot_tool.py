import dis
import json
import logging
import os
import time
from datetime import datetime
from functools import reduce
from typing import Any, Union

from deep_swe_msft.tools.utils import CAAS_ENVS
import structlog
import tenacity
from caas.api import CaasSession
from caas.commands import HttpGet, HttpPost, RawBashScript, UploadFile
from caas.internal.errors import ExecNetworkError
from caas.protocol import NetworkMode, Tmpfs, VolumeMount
from caas_tool.caas_container import CaasContainer

DEBUG = False

os.makedirs(f"/var/log/supervisor/vscode_copilot_tool", exist_ok=True)

def log_with_timestamp(message: str, instance_id: str | None = None, caas_session_id: str | None = None) -> None:
    with open(f"/var/log/supervisor/vscode_copilot_tool/{caas_session_id}.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}][{instance_id}]: {message}\n")

structlog.configure(
    wrapper_class=structlog.make_filtering_bound_logger(logging.INFO if DEBUG else logging.WARNING),
)
logger = structlog.stdlib.get_logger(component=__name__)

## Likely to change
TOOL_TAR = "/mnt/caas_azure_blob/orngcaas/data/tools/vscode-copilot-tools/vscode-copilot-tools.785903861.tar"
NODEJS_VERSION = "22.14.0"
NODEJS_TAR = f"/mnt/azure_blob/tools/node-v{NODEJS_VERSION}-linux-x64.tar.gz"
## only used if cache is not installable
APT_PKGS = "libnss3 libdbus-1-3 libatk1.0-0 xvfb dbus-x11 libatk-bridge2.0-0 libgtk-3-0 libgbm1 libasound2 x11-xkb-utils xfonts-100dpi xfonts-75dpi xfonts-scalable xauth"

CERT_HOST="/mnt/azure_blob/certs/mitmproxy-ca-cert.crt"
CERT_CONTAINER="/usr/local/share/ca-certificates/my_custom_certificate.crt"

## Unlikely to change
TOOL_LOCAL_PORT = 13269
TOOL_LOCAL_ENDPOINT = f"http://localhost:{TOOL_LOCAL_PORT}"
TOOL_LOCAL_DIR = "/app/vscode-copilot"

## Change to smaller value to debug in fast fail / user errors
MAX_RETRY_SECONDS: float = 120.0

## Change to False to debug why tool service cannot start
DETACH = True
STARTUP_OUTPUT_FILE = "/tmp/vscode_copilot_startup_output.txt"
STARTUP_STATUS_FILE = "/tmp/vscode_copilot_startup_status.txt"

LANGUAGE_UNIFORMS = {
    "cpp": "c++",
    "c": "c++",
    "csharp": "c#",
    "js": "javascript",
    "ts": "typescript",
    "py": "python",
    "golang": "go",
}

LANGUAGE_EXTENSIONS = {
    "c++": [
        ['ms-vscode.cpptools', 'C/C++'],
        ['ms-vscode.cpptools-extension-pack', 'C/C++ Extension Pack'],

    ],
    "python": [
        ['ms-python.vscode-pylance', 'Pylance'],
        ['ms-python.debugpy', 'Python Debugger'],
        ['ms-python.python', 'Python'],
    ],
    "java": [
        ['vscjava.vscode-gradle', 'Gradle for Java'],
        ['vscjava.vscode-java-debug', 'Debugger for Java'],
        ['vscjava.vscode-java-dependency', 'Project Manager for Java'],
        ['vscjava.vscode-java-test', 'Test Runner for Java'],
        ['vscjava.vscode-maven', 'Maven for Java'],
        ['visualstudioexptteam.vscodeintellicode', 'IntelliCode'],
        ['redhat.java', 'Language Support for Java(TM) by Red Hat'],
        ['vscjava.vscode-java-pack', 'Extension Pack for Java'],
    ],
    "javascript": [
        ["hbenl.vscode-mocha-test-adapter", "Mocha Test Adapter"],
        ["orta.vscode-jest", "Jest"],
    ],
    "typescript": [
        ["hbenl.vscode-mocha-test-adapter", "Mocha Test Adapter"],
        ["orta.vscode-jest", "Jest"],
    ],
    "c#": [
        ['ms-dotnettools.vscode-dotnet-runtime', '.NET Install Tool'],
        ['ms-dotnettools.csharp', 'C#'],
        ['ms-dotnettools.csdevkit', 'C# Dev Kit'],
    ],
    "go": [
    ],
    'rust': [
        ['rust-lang.rust-analyzer', 'rust-analyzer'],
    ],
    "ruby": [
        ['shopify.ruby-lsp', 'Ruby LSP'],
    ],

    # Add more languages and their extensions as needed
}

VSC_MNT = [
    VolumeMount(host=TOOL_TAR, container=TOOL_TAR, deprecated_use_blobfuse=True),
    VolumeMount(host=NODEJS_TAR, container=NODEJS_TAR, deprecated_use_blobfuse=True),
    VolumeMount(host=CERT_HOST, container=CERT_CONTAINER, deprecated_use_blobfuse=True)
]


@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def new_container(caas_endpoint: str, caas_image: str) -> CaasContainer:
    """
    Create a new container for the VSCode Copilot tool.
    """
    t0 = time.time()
    logger.info(f"Creating caas container at endpoint: {caas_endpoint} image: {caas_image}...")

    memory_limit: str = "32g"
    tmpfs = [Tmpfs(container=TOOL_LOCAL_DIR, size=memory_limit)]
    container = await CaasContainer.new(
        caas_endpoint=caas_endpoint,
        image_name=caas_image,
        volume_mounts=VSC_MNT,
        tmpfs=tmpfs,
        memory_limit=memory_limit,
        disk_limit="32gb",
        cpu_limit="16.0",
        network=NetworkMode.BRIDGE,
        env=CAAS_ENVS,
        idle_ttl=3600,
    )
    logger.info(
        f"Container created in {time.time() - t0:.2f} seconds. caas_session_state: {container.caas_session_state}"
    )

    return container


@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def install_tool(caas_session: CaasSession) -> None:
    """
    Install the VSCode Copilot tool in the container.
    """
    log_with_timestamp(f"Start download and extract {TOOL_TAR}...", caas_session_id=caas_session.id)
    t0 = time.time()
    res = await caas_session.run(
        RawBashScript(
            f"""
mkdir -p {TOOL_LOCAL_DIR}
tar -xf {TOOL_TAR} -C {TOOL_LOCAL_DIR}
""",
            timeout=1200,
            enable_public_logging=True,
        ),  # local latency 20
    )
    log_with_timestamp(f"Download and extract {TOOL_TAR} result: {res} elapsed: {time.time() - t0}",
                       caas_session_id=caas_session.id)


@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def upload_settings(caas_session: CaasSession, workspace_dir: str, settings: str) -> None:
    """
    Upload settings to the container.
    """
    print(f"Debugging to output vscode_settings {settings=} {workspace_dir=}")
    logger.info(f"Start uploading settings: {settings}...")
    t0 = time.time()
    res = await caas_session.run(UploadFile(os.path.join(workspace_dir, ".vscode", "settings.json"), settings.encode('utf-8')))
    log_with_timestamp(f"Upload settings result: {res} elapsed: {time.time() - t0}", caas_session_id=caas_session.id)


@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def install_deb(caas_session: CaasSession) -> None:
    """
    Install the apt dependencies via deb files for the VSCode Copilot tool in the container.
    """
    # logger.info(f"Install deb skipped, try to install via apt directly!! {APT_PKGS}...")
    t0 = time.time()
    current_apt_pkgs = APT_PKGS

    res = await caas_session.run(
        RawBashScript(
            f"""
update-ca-certificates
apt-get update
apt-get install -y {current_apt_pkgs}
""",
            timeout=600,
            enable_public_logging=True,
        ),
    )

    # Check if installation failed due to libasound2 issue and retry with libasound2t64
    if res[0] != 0 and b'libasound2' in res[1]:
        logger.info("Installation failed with libasound2 error, retrying with libasound2t64...")
        current_apt_pkgs = current_apt_pkgs.replace('libasound2', 'libasound2t64')

        res = await caas_session.run(
            RawBashScript(
                f"""
update-ca-certificates
apt-get update
apt-get install -y {current_apt_pkgs}
""",
                timeout=600,
                enable_public_logging=True,
            ),
        )

    log_with_timestamp(f"apt-get install result - exit code: {res[0]} stdout: {res[1].decode()} elapsed: {time.time() - t0}",
                       caas_session_id=caas_session.id)
    if res[0] != 0:
        raise Exception("apt install failed")


@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def fix_cert_issue(caas_session: CaasSession) -> None:
    """
    Fix the certificate issue in the container.
    """
    logger.info("Fixing certificate issue...")
    t0 = time.time()
    code, message = await caas_session.run(
        RawBashScript(
            f"""
apt install -y libnss3-tools

NSSDB_DIR="$HOME/.pki/nssdb"
rm -rf "$NSSDB_DIR"
mkdir -p "$NSSDB_DIR"

if [ -z "$(ls -A $NSSDB_DIR)" ]; then
    echo "NSS database directory is empty. Initializing…" >&2
    certutil -d "$NSSDB_DIR" -N --empty-password
else
    echo "NSS database already initialized." >&2
fi

# this section adds all the certificates that exist in the directory
for cert_file in "/usr/local/share/ca-certificates"/*.crt; do
    echo "Adding certificate [$cert_file] to NSS database…" >&2
    cert_name=$(basename "$cert_file" .crt)
    certutil -d "$NSSDB_DIR" -A -t "C,," -n "CaaS-$cert_name" -i "$cert_file"
done
""",
            timeout=600,
            enable_public_logging=True,
        ),
    )
    log_with_timestamp(f"Good - Fix certificate issue result - exit code: {code} stdout: {message.decode()} elapsed: {time.time() - t0}",
                       caas_session_id=caas_session.id)
    if code != 0:
        code, message = await caas_session.run(
            RawBashScript(
            f"""
set -e
set -x
update-ca-certificates
""",
            timeout=600,
            enable_public_logging=True,
        ),
    )

        log_with_timestamp(f"Arbitrary - Fix certificate issue result - exit code: {code} stdout: {message.decode()} elapsed: {time.time() - t0}",
                           caas_session_id=caas_session.id)
        if code != 0:
            log_with_timestamp(f"Bad - Fix certificate issue result - exit code: {code} stdout: {message.decode()} elapsed: {time.time() - t0}",
                               caas_session_id=caas_session.id)
            raise Exception("Fix certificate issue failed")


@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def install_nodejs(caas_session: CaasSession) -> None:
    """
    Install Node.js in the container.
    """
    logger.info("Installing Node.js...")
    t0 = time.time()
    res = await caas_session.run(
        RawBashScript(
            f"""
    set -ex
    # Remove any existing Node.js installations first
    rm -f /usr/bin/node /usr/bin/npm /usr/bin/npx
    rm -rf /usr/local/bin/node /usr/local/bin/npm /usr/local/bin/npx

    # Since the tar has flat structure (./bin/node), extract directly to /usr
    # This will put bin/node -> /usr/bin/node, lib/* -> /usr/lib/*, etc.
    tar -xf {NODEJS_TAR} -C /usr

    # Make sure the binaries are executable
    chmod +x /usr/bin/node /usr/bin/npm /usr/bin/npx 2>/dev/null || true

    # Verify installation
    echo "Verifying installation..."
    ls -la /usr/bin/node /usr/bin/npm
    which node || (echo "node not in PATH" && exit 1)
    node -v || (echo "node command failed" && exit 1)  
    npm -v || (echo "npm command failed" && exit 1)
    """,
            timeout=600,
            enable_public_logging=True,
        ),  # local latency 2s
    )
    log_with_timestamp(f"Install nodejs result - exit code: {res[0]} stdout: {res[1].decode()} elapsed: {time.time() - t0}",
                       caas_session_id=caas_session.id)

@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def check_workspace_dir(caas_session: CaasSession, workspace_dir: str) -> None:
    t0 = time.time()
    code, message = await caas_session.run(RawBashScript(f"ls {workspace_dir}\n", timeout=600))
    if code != 0:
        error_message = f"Workspace directory {workspace_dir} does not exist or is not accessible. Error: {message.decode()}"
        log_with_timestamp(error_message, caas_session_id=caas_session.id)
        raise Exception(error_message)
    else:
        log_with_timestamp(f"Workspace directory {workspace_dir} exists and is accessible. Elapsed: {time.time() - t0} seconds.",
                           caas_session_id=caas_session.id)

@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def start_service(caas_session: CaasSession, workspace_dir: str) -> None:
    """
    Start the VSCode Copilot tool server in the container.
    """
    # logger.info(f"Starting VSCode Copilot tool server at {TOOL_LOCAL_ENDPOINT}...")
    t0 = time.time()
    res = await caas_session.run(
        RawBashScript(
            f"""
set -x
npm config set registry $CAAS_ARTIFACTORY_NPM_REGISTRY
git config --global url.$CAAS_GIT_MIRROR_URL/github.com/.insteadOf https://github.com/
export GITHUB_PAT=1
export SERVER_MODE=1
export WORKSPACEFOLDER={workspace_dir}
export SERVICE_PORT={TOOL_LOCAL_PORT}
cd {TOOL_LOCAL_DIR}
(npm run test:extension) > {STARTUP_OUTPUT_FILE} 2>&1
echo $? > {STARTUP_STATUS_FILE}
""",
            detach=DETACH,
            timeout=None if DETACH else 60,
        ),  # local latency 12s
    )
    if DETACH:
        log_with_timestamp(f"VSCode Copilot tool server started: {res} latency: {time.time() - t0}...",
                           caas_session_id=caas_session.id)
    else:
        log_with_timestamp(f"Debugging VSCode Copilot tool server startup - latency: {time.time() - t0} exit code: {res[0]} stdout: {res[1].decode()}",
                           caas_session_id=caas_session.id)

@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def wait_until_ready(caas_session: CaasSession) -> None:
    """
    Wait until the VSCode Copilot tool server is ready. The same implementation of terminal server.
    """
    logger.info(f"Waiting for VSCode Copilot tool server to be ready at {TOOL_LOCAL_ENDPOINT}...")
    t0 = time.time()
    response = await caas_session.run(
        HttpGet(
            url=f"{TOOL_LOCAL_ENDPOINT}/healthcheck",
            retry_count=20,
            retry_delay=20,
            enable_public_logging=True,
            timeout=60,
        )
    )
    log_with_timestamp(f"VSCode Copilot tool server healthcheck response: {response} elapsed: {time.time() - t0}",
                       caas_session_id=caas_session.id)
    assert json.loads(response) == {"status": "ok"}

@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def debug_startup(caas_session: CaasSession) -> None:
    """
    Debug the startup of the VSCode Copilot tool server.
    This function reads the startup output and status files to help diagnose issues.
    """
    try:
        status = await caas_session.run(RawBashScript("cat " + STARTUP_STATUS_FILE, enable_public_logging=True))
        log_with_timestamp(f"VSCode Copilot tool server startup status: {status[1].decode()}",
                           caas_session_id=caas_session.id)
        output = await caas_session.run(RawBashScript("cat " + STARTUP_OUTPUT_FILE, enable_public_logging=True))
        log_with_timestamp(f"VSCode Copilot tool server startup output:\n{output[1].decode()}\nEnd of output.",
                           caas_session_id=caas_session.id)
    except Exception as e:
        log_with_timestamp(f"Failed to debug startup: {e}",
                           caas_session_id=caas_session.id)

@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
)
async def exec(
    caas_session: CaasSession, tool_name: str, tool_params: dict, timeout: int | None = 120, retry_count: int = 10
) -> tuple[str, dict]:
    """
    Execute a command in the VSCode Copilot tool.
    :param tool_name: The name of the tool to execute.
    :param tool_params: The parameters to pass to the tool.
    :param timeout: The timeout in seconds for the command execution.
    """

    def extract_text(response: bytes) -> str:
        """
        Extract text from the response bytes.
        Algorithm: recursively extract the 'text' property from the JSON structure.
        Example:
        Input: bytes like b'{"$mid":20,"content":[{"$mid":22,"value":{"node":{"type":1,"ctor":2,"ctorName":"FindFilesResult","children":[{"type":1,"ctor":2,"ctorName":"TextChunk31","children":[{"type":2,"priority":20,"text":"1 total result","references":[],"lineBreakBefore":true}],"props":{"priority":20},"references":[]},{"type":1,"ctor":2,"ctorName":"TextChunk31","children":[{"type":2,"priority":10,"text":"/testbed/hello.txt","references":[{"anchor":{"$mid":1,"fsPath":"/testbed/hello.txt","external":"file:///testbed/hello.txt","path":"/testbed/hello.txt","scheme":"file"},"options":{"isFromTool":true}}],"lineBreakBefore":true}],"props":{"priority":10},"references":[]}],"props":{},"references":[]}}}]}'
        Output: str like '1 total result\n/testbed/hello.txt'
        """
        try:
            response_dict = json.loads(response)
        except json.JSONDecodeError:
            logger.error("Failed to decode JSON response")
            return ""
        def smart_concat(str1: str, str2: str) -> str:
            if not str1:
                return str2
            if not str2:
                return str1
            if str1 == str1.rstrip() and str2 == str2.lstrip():
                return f"{str1} {str2}"
            return f"{str1}{str2}"

        def extract_text_recursive(obj: any) -> str:
            if isinstance(obj, dict):
                if "text" in obj and isinstance(obj["text"], str):
                    return obj["text"].replace("\r", "\n")
                elif "value" in obj and isinstance(obj["value"], str):
                    return obj["value"].replace("\r", "\n")
                else:
                    return reduce(smart_concat, (extract_text_recursive(value) for value in obj.values()), "")
            elif isinstance(obj, list):
                return reduce(smart_concat, (extract_text_recursive(item) for item in obj), "")
            else:
                return ""

        result = extract_text_recursive(response_dict)
        # remove duplicate newlines
        result = "\n".join(line for line in result.splitlines() if line.strip())
        # remove leading and trailing whitespace
        result = result.strip()
        return result

    t0 = time.time()
    log_with_timestamp(
        f"VSCode Copilot tool server executing command: {tool_name} with params: {tool_params}",
        caas_session_id=caas_session.id
    )
    try:
        response = await caas_session.run(
            HttpPost(
                url=f"{TOOL_LOCAL_ENDPOINT}/{tool_name}",
                data=tool_params,
                enable_public_logging=True,
                timeout=timeout,
                retry_count=retry_count,
                retry_delay=1,
            )
        )
        # if tool_name == 'list_dir':
        #     log_with_timestamp(f"list_dir response: {response}")
        duration = time.time() - t0
        # log_with_timestamp(f"vscode response: {response}")
        log_with_timestamp(f"VSCode Copilot tool server response: {response} elapsed: {duration}",
                           caas_session_id=caas_session.id)
        metadata = {
            "exec_cmd": {tool_name: tool_params},
            "exec_status_code": 0,
            "exec_duration_seconds": duration,
            "exec_error": None,
        }
        response_text = extract_text(response)
        log_with_timestamp(f"VSCode Copilot tool server response to text: {response_text}",
                            caas_session_id=caas_session.id)
        if response_text.strip():  
            return response_text, metadata
        else:
            return response.decode(), metadata
    except ExecNetworkError as e:
        duration = time.time() - t0
        msg = f"VSCode Copilot tool error: http_code={e.http_code} output={e.output.decode()}, status={e.status} message={e}"
        logger.warning(msg)
        log_with_timestamp(msg, caas_session_id=caas_session.id)
        metadata = {
            "exec_cmd": {tool_name: tool_params},
            "exec_status_code": e.status,
            "exec_duration_seconds": duration,
            "exec_error": str(e),
        }
        return msg, metadata
    except Exception as e:
        duration = time.time() - t0
        msg = f"VSCode Copilot tool error: {e}"
        logger.error(msg)
        log_with_timestamp(msg, caas_session_id=caas_session.id)
        metadata = {
            "exec_cmd": {tool_name: tool_params},
            "exec_status_code": -1,
            "exec_duration_seconds": duration,
            "exec_error": str(e),
        }
        return msg, metadata

async def install_vsc_extensions(caas_session: CaasSession, language: str, vscode_extensions: list[str] | None) -> dict[str, dict[str, Any]]:
    language = language.lower()
    if language in LANGUAGE_UNIFORMS:
        language = LANGUAGE_UNIFORMS[language]

    if vscode_extensions:
        extensions = [(id, id) for id in vscode_extensions]
    else:
        extensions = LANGUAGE_EXTENSIONS.get(language, [])
    result = {}
    if extensions:
        for ext_id, ext_name in extensions:
            log_with_timestamp(f"Installing extension {ext_id} ({ext_name}) for language {language}",
                               caas_session_id=caas_session.id)
            message, metadata = await exec(caas_session, "install_extension", {"id": ext_id, "name": ext_name})
            result[ext_id] = {"message": message, "metadata": metadata}
            log_with_timestamp(f"Extension {ext_id} installation result. {message=}, {metadata=}",
                               caas_session_id=caas_session.id)
    else:
        log_with_timestamp(f"No extensions found for language {language}. Skipping extension installation.",
                           caas_session_id=caas_session.id)
    return result


async def prepare_vsc_tool(caas_session: CaasSession, workspace_dir: str, language: str | None=None, vscode_settings: str | None = None, vscode_commands: list[str] | None = None, vscode_extensions: list[str] | None = None) -> None:
    """
    Prepare the VSCode Copilot tool in the container.
    This function is a placeholder and does not perform any actions.
    """
    await check_workspace_dir(caas_session, workspace_dir)
    await install_nodejs(caas_session)
    await install_tool(caas_session)
    await install_deb(caas_session)
    await fix_cert_issue(caas_session)
    if vscode_settings:
        await upload_settings(caas_session, workspace_dir, vscode_settings)
    await start_service(caas_session, workspace_dir)
    await wait_until_ready(caas_session)
    if language:
        log_with_timestamp(f"Installing extensions for {language=}",
                           caas_session_id=caas_session.id)
        await install_vsc_extensions(caas_session, language, vscode_extensions)
    if vscode_commands:
        if isinstance(vscode_commands, str):
            vscode_commands = [vscode_commands]
        message, metadata = await exec(caas_session, "run_in_terminal", {"command": " && ".join(vscode_commands), "explanation": "Run initial commands in terminal after setup.", "isBackground": False})
        log_with_timestamp(f"Run initial commands in terminal result: {message}, metadata: {metadata}, commands: {vscode_commands}",
                           caas_session_id=caas_session.id)
        assert metadata["exec_status_code"] == 0, f"Failed to run initial commands in terminal: {message}"


async def setup_vscutils(
    *, datapoint: dict[str, Any], session: CaasSession, workdir: str | None, language: str | None = None, vscode_settings: Union[str, dict[str, Any], None] = None, dump_settings: bool = True, vscode_commands: list[str] | None = None, vscode_extensions: list[str] | None = None
) -> None:
    """
    Prepare the VSCode Copilot tool in the container.
    This function is a placeholder and does not perform any actions.
    """
    try:
        log_with_timestamp(f"setup_vscutils starting with workdir: {workdir}...", caas_session_id=session.id)
        if vscode_settings is not None and dump_settings:
            vscode_settings = json.dumps(vscode_settings)
        await prepare_vsc_tool(session, workdir, language, vscode_settings, vscode_commands, vscode_extensions)
        log_with_timestamp("setup_vscutils done...", caas_session_id=session.id)
    except Exception as e:
        await debug_startup(session)
        import traceback

        log_with_timestamp(f"setup_vscutils error... {traceback.format_exc()}", caas_session_id=session.id)
        raise RuntimeError(f"Failed to install vsc utils: {repr(e)}") from e
