from functools import partial
import berry

import caas_autograding.grader as caas_graders
import chz
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.datasets.configs import DeepSWEDatasetConfig
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig  # type: ignore
from deep_swe_msft.qa_padawan_v2.datasets.caas_resource_config import CaasContainerResourceConfig
from deep_swe_msft.qa_padawan_v2.datasets.setup import qa_setup_fn_coreutils
from deep_swe_msft.tools.caas_padawan_tool import PadawanToolConfig
from deep_swe_msft.tools.caas_mix_tool import MixToolConfig
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CRESCO_STORAGE_NAME
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from qstar.sample_completers.basic_sample_completer import BasicSampleCompleter

from qstar.graders.taskgen_utils import TokenCompleterGraderService
from deep_swe.graders.code_plan_patch_grader import CodePlanConsistencyGrader, CodePlanPatchGrader
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK

from deep_swe_msft.padawan_graders.cotograder_utils import (
    COTOGRADER_RENDERER,
    COTOGRADER_CHZ_ARGV,
    CotoGraderService
)


@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig, IsOverride):
    caas_cpu_limit: float = 1.0  # Increase to 2 if actually using VSCode
    caas_memory_limit: int = 2
    caas_container_image: str = "vscode-agent"  # Has more pip dependencies!
    caas_idle_ttl: int = 10 * 60
    enable_network_after_setup: bool = False
    caas_endpoint: str = CAAS_ENDPOINT


DEFAULT_ENFORCE_PROBABILITY = 0.25

def _make_tool_configs(
    container_tool_config: str = "padawan_tool",
    internet_policy: str = "no_access",
) -> tuple[ToolConfig, ...]:
    if container_tool_config == "padawan_tool":
        return (PadawanToolConfig(internet_policy=internet_policy),)
    elif container_tool_config == "mix_tool":
        return (MixToolConfig(internet_policy=internet_policy),)
    else:
        raise ValueError(f"Unknown padawan tool config: {container_tool_config}")


# IMPORTANT: must be paired with IFEnforcementGrader.
def enforce_commands(
    probability: float = DEFAULT_ENFORCE_PROBABILITY,
    extra_good_commands: tuple[str, ...] = (),
    extra_bad_commands: tuple[str, ...] = (),
) -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:enforce_commands",
        kwargs={
            "probability": probability,
            "extra_good_commands": extra_good_commands,
            "extra_bad_commands": extra_bad_commands,
        },
    )

# Copied from project/deep_swe/deep_swe/datasets/configs.py
def make_multistage_grader(
    grader_argvs: list[list[str]] | None = None,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
) -> caas_graders.MultiStageGraderWithAuxReinforcements:
    # NB: we can freely set |channels_for_answer| here. See NOTE [ DeepSWEDatasetConfig.channel_format ]
    if grader_argvs is None:
        grader_argvs = []
    grader_argvs = list(grader_argvs)
    
    blueprint = chz.Blueprint(caas_graders.MultiStageGraderWithAuxReinforcements)
    for ii, grader_argv in enumerate(grader_argvs):
        argv: list[str] = []
        for arg in grader_argv:
            if not arg.startswith(("=", ".")):  # allow wildcards
                arg = "." + arg
            argv.append(f"graders.{ii}{arg}")
        blueprint = blueprint.apply_from_argv(argv)
    if channels_for_answer:
        channels_for_answer_arg = "channels_for_answer=" + ",".join(
            str(c) for c in channels_for_answer
        )
        blueprint = blueprint.apply_from_argv(["..." + channels_for_answer_arg])
    return blueprint.make()


# Copied from project/deep_swe/deep_swe/datasets/configs.py with updated grader path and bus line.
# let's not checking tool use for now, as we only care about the final answer
def make_repo_qa_multistage_grader() -> MultiStageGrader:
    grader_argvs = [
        [
            "=deep_swe_msft.qa_padawan_v2.graders.qa_grader:ToolberrySingleQAGrader",
            f"renderer_name={COTOGRADER_RENDERER}",
            "tool_penalty_scale=0.0",
            "check_channel_and_tool_use=False",
            "min_passing_grade=0.9",
            *COTOGRADER_CHZ_ARGV,
        ]
    ]
    return make_multistage_grader(
        grader_argvs,
        tuple([]),
    )  # channels_for_answer not used.

def random_tool_set() -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:random_tool_set",
    )

# Copied from project/deep_swe/deep_swe/datasets/configs.py with an updated dataset_id, tool_configs, resource_conigs, datapoint_converters
@chz.chz
class RepoQAPythonDatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_python_18401"  # full
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_python_mu035_sig02_shuffled"  # 6281
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.python_train_17167"
    grader: Grader[HarmonyCompletionDatapoint] = override(make_repo_qa_multistage_grader)
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            container_tool_config='padawan_tool',
            internet_policy='no_access',
        ),
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=qa_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            random_tool_set(),
            # conversation_converter("deep_swe_msft.rfs_rcs_bb_ml_vsc.datasets.conversation_init:conversation_init_fn"),
            enforce_commands(
                probability=0.5,
            ),
        )
    )


# ===== RepoQA Train datasets ======

@chz.chz
class RepoQACppDatasetConfig(RepoQAPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_cpp_3989"  # full
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_cpp_mu035_sig02_shuffled"  # 1805
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.cpp_train_881"  # full

@chz.chz
class RepoQAJavaDatasetConfig(RepoQAPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_java_8359"  # full
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_java_mu035_sig02_shuffled"  # 3507
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.java_train_4743"  # full

@chz.chz
class RepoQAJsDatasetConfig(RepoQAPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_js_9740"  # full
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_js_mu035_sig02_shuffled"  # 3449
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.javascript_train_8614"  # full

@chz.chz
class RepoQARustDatasetConfig(RepoQAPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_rust_8468"  # full
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_rust_mu035_sig02_shuffled"  # 3645
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.rust_train_5556"  # full

@chz.chz
class RepoQATsDatasetConfig(RepoQAPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_ts_8257"  # full
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.train_ts_mu035_sig02_shuffled"  # 2614
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.typescript_train_3866"  # full

# ===== RepoQA Eval datasets ======

@chz.chz
class RepoQAPythonEvalDatasetConfig(RepoQAPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.val_python_2054"  # full
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.python_val_1908"

@chz.chz
class RepoQACppEvalDatasetConfig(RepoQAPythonEvalDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.val_cpp_377"  # full
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.cpp_val_191"

@chz.chz
class RepoQAJavaEvalDatasetConfig(RepoQAPythonEvalDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.val_java_1034"  # full
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.java_val_1141"

@chz.chz
class RepoQAJsEvalDatasetConfig(RepoQAPythonEvalDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.val_js_1011"  # full
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.javascript_val_2262"

@chz.chz
class RepoQARustEvalDatasetConfig(RepoQAPythonEvalDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.val_rust_904"  # full
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.rust_val_1432"

@chz.chz
class RepoQATsEvalDatasetConfig(RepoQAPythonEvalDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.repoqa.u05222025.val_ts_941"  # full
    dataset_id: str = "data.chenliang1.swe.repoqa.u06022025.typescript_val_996"


# ===== RepoLocalization Train datasets ======

@chz.chz
class RepoLocalizationPythonDatasetConfig(RepoQAPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.python_train_3776"  # full
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.python_train_3776_mu0375_sig025"  # 643

@chz.chz
class RepoLocalizationCppDatasetConfig(RepoLocalizationPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.cpp_train_216"  # full
    # dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.cpp_train_216_mu0.375_sig0.25"  # 49
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.cpp_train_216_mu0375_sig0375" # 72

@chz.chz
class RepoLocalizationJavaDatasetConfig(RepoLocalizationPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.java_train_1010"  # full
    # dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.java_train_1010_mu0.375_sig0.25" # 171
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.java_train_1010_mu0375_sig0375" # 204

@chz.chz
class RepoLocalizationJsDatasetConfig(RepoLocalizationPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.javascript_train_1797"  # full
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.javascript_train_1797_mu0375_sig025"  # 240

@chz.chz
class RepoLocalizationRustDatasetConfig(RepoLocalizationPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.rust_train_1128"  # full
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.rust_train_1128_mu0375_sig025"  # 264

@chz.chz
class RepoLocalizationTsDatasetConfig(RepoLocalizationPythonDatasetConfig, IsOverride):
    # dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.typescript_train_898"  # full
    # dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.typescript_train_898_mu0.375_sig0.25"  # 120
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.typescript_train_898_mu0375_sig0375" # 165


# ===== RepoLocalization Eval datasets ======

@chz.chz
class RepoLocalizationPythonEvalDatasetConfig(RepoQAPythonEvalDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.python_val_415"  # full

@chz.chz
class RepoLocalizationCppEvalDatasetConfig(RepoLocalizationPythonEvalDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.cpp_val_44"  # full

@chz.chz
class RepoLocalizationJavaEvalDatasetConfig(RepoLocalizationPythonEvalDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.java_val_238"  # full

@chz.chz
class RepoLocalizationJsEvalDatasetConfig(RepoLocalizationPythonEvalDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.javascript_val_470"  # full

@chz.chz
class RepoLocalizationRustEvalDatasetConfig(RepoLocalizationPythonEvalDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.rust_val_289"  # full

@chz.chz
class RepoLocalizationTsEvalDatasetConfig(RepoLocalizationPythonEvalDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.codeloc.u05292025.typescript_val_237"  # full

@chz.chz
class RepoQAMergedDatasetConfig(RepoQAPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.zhendongw.swe_train.data_mix_v1.repo_qa"



# ========= FileQA Train datasets ======

# Copied from project/deep_swe/deep_swe/pr_bot/configs.py
@chz.chz(typecheck=True)
class CodePlanPatchCotograder(CodePlanPatchGrader, IsOverride):
    grader_service: TokenCompleterGraderService = override(CotoGraderService)
    renderer_name: str = COTOGRADER_RENDERER
    grader_max_tokens: int = chz.field(default=16384)


# Copied from project/deep_swe/deep_swe/pr_bot/configs.py
@chz.chz(typecheck=True)
class CodePlanConsistencyCotograder(CodePlanConsistencyGrader, IsOverride):
    grader_service: TokenCompleterGraderService = override(CotoGraderService)
    renderer_name: str = COTOGRADER_RENDERER
    grader_max_tokens: int = chz.field(default=16384)


# Modified from project/deep_swe/deep_swe/pr_bot/configs.py
@chz.chz
class TicketWriterMultiStageGrader(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    @staticmethod
    def _make_default_graders(
        grader1: CodePlanPatchCotograder,
        grader2: CodePlanConsistencyCotograder,
    ) -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
        # we need this to hit `embs_builder`
        return (grader1, grader2)

    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(_make_default_graders)


# Modified from project/deep_swe/deep_swe/pr_bot/configs.py
@chz.chz(typecheck=True)
class FileQAPythonDatasetConfig(RepoQAPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.fileqa.u06062025.python_train_4118"
    # dataset_id: str = "data.chenliang1.swe.fileqa.u06062025.python_train_4118_mu0125_sig025"  # 1280

    # datapoint_converters modified from project/deep_swe/deep_swe/pr_bot/configs.py
    grader: Grader[HarmonyCompletionDatapoint] = override(TicketWriterMultiStageGrader)

@chz.chz(typecheck=True)
class FileQAOthersDatasetConfig(FileQAPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.fileqa.u06062025.others_train_26663"
    # dataset_id: str = (
    #     "data.chenliang1.swe.fileqa.u06062025.others_train_26663_mu0125_sig025"  # 4722
    # )


# ===== FileQA Eval datasets ======

@chz.chz(typecheck=True)
class FileQAEvalPythonDatasetConfig(FileQAPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.fileqa.u06062025.python_val_234"


@chz.chz(typecheck=True)
class FileQAEvalOthersDatasetConfig(FileQAEvalPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.fileqa.u06062025.others_val_1461"