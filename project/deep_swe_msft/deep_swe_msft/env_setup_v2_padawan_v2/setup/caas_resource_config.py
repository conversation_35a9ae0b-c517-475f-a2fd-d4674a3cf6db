import dataclasses
import logging

import structlog
import tenacity

import caas
import chz
from caas.api import caas_api
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from deep_swe_msft.env_setup_v2_padawan_v2.setup.setup import EnvSetupTaskMetadata
from qstar.common import datapoint
from berry_harmony.tools.berry_tool_interface import ResourceContinuationError
from berry_caas_container_tool.caas_container_tool import CaasContainerResourceConfig
from caas.commands import RawExec
from berry_rfs.mrfs_setup import try_run_command
from caas.protocol import NetworkMode, VolumeMount

from deep_swe_msft.tools.utils import MIX_VSC_TOOL, PDW_MNT, CAAS_ENVS
from deep_swe_msft.tools.vscode_copilot_tool import VSC_MNT

logger = structlog.get_logger(component=__name__)

if MIX_VSC_TOOL:
    VMOUNT = VSC_MNT
else:
    VMOUNT = PDW_MNT

@chz.chz(typecheck=True)
class EnvSetupCaasContainerResourceConfig(CaasContainerResourceConfig):
    """
    Allows per-datapoint control over the image, resource limits, and network access.
    (As a result, sandbox=True must be enabled)
    """

    caas_endpoint: str = "https://eastus2.caas.azure.com"
    caas_idle_ttl: int = 30 * 60 # in seconds, 30 minutes
    caas_disk_limit: int = 32  # in GB
    use_terminal_server: bool = True
    is_eval: bool = False

    async def _initialize_resource_from_state_metadata(
        self, metadata: EnvSetupTaskMetadata, caas_session_state: str
    ) -> CaasContainer:
        try:
            container = CaasContainer(
                caas_endpoint=self.caas_endpoint,
                image_name=metadata.docker_image,
                caas_session_state=caas_session_state,
                is_owner=False,
                user=self.user,
            )
            await container.send_heartbeat()
            return container
        except (
            ValueError,
            caas.NotFoundError,
            caas.ServerError,
            caas.TransportError,
            caas.ClientError,
        ) as e:
            raise ResourceContinuationError(
                resource_name=self.name(),
                resource_state=caas_session_state.encode(),
                message=(
                    f"Failed to initialize caas container '{self.name()}' from session state "
                    f"'{caas_session_state}': {e}"
                ),
            ) from e

    # @tenacity.retry(
    #     wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    #     stop=tenacity.stop_after_attempt(10),
    #     before_sleep=tenacity.before_sleep_log(
    #         logger,  # type: ignore
    #         logging.WARNING,
    #     ),
    #     reraise=True,
    # )
    @tenacity.retry(
        retry=tenacity.retry_if_exception_type(
            (caas.ClientError, caas.ServerError, caas.ExecError, caas.TransportError, Exception, RuntimeError)
        ),
        wait=tenacity.wait_random_exponential(min=10, max=100, multiplier=10),
        stop=tenacity.stop_after_attempt(10),
        before_sleep=tenacity.before_sleep_log(
            logger,  # type: ignore
            logging.WARNING,
        ),
    )
    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        metadata = EnvSetupTaskMetadata.model_validate(dp.metadata)

        if self.caas_session_state is not None:
            # We are not the owner of the container, so we don't need to run the setup function.
            resource = await self._initialize_resource_from_state_metadata(
                metadata, self.caas_session_state
            )
            assert not resource.is_owner
            return resource

        if self.use_terminal_server:
            docker_cmd = dp.metadata.get("docker_command", "/server.py")
            cmd = [docker_cmd]
        else:
            cmd = []
        caas = caas_api(endpoint=self.caas_endpoint)
        caas_session = await caas.new_session(
            image=metadata.docker_image,
            cmd=cmd,
            cpu_limit=str(metadata.limits.cpu),
            memory_limit=f"{metadata.limits.memory}m",
            disk_limit=f"{self.caas_disk_limit}g",
            idle_ttl=self.caas_idle_ttl,
            num_gpus=self.caas_num_gpus,
            network=NetworkMode.CAAS_DEFAULT if self.is_eval else self.caas_network, # chenliang1: use CAAS_DEFAULT for eval, enable network access at caas init to avoid too many invalid.
            pids_limit=1024, # for dongdong's ml data evaluation, we need to set a higher pids limit
            timeout=1200,
            volume_mounts=VMOUNT,
            env=CAAS_ENVS,
        )
        terminal_session = TerminalSession(caas_session)

        try:
            if self.setup_fn is not None:
                await self.setup_fn(
                    datapoint=dataclasses.asdict(dp),
                    terminal_session=terminal_session,
                )

            if self.enable_network_after_setup is not None and not self.is_eval:
                # modify the network policy after because the setup may have required (temporary)
                # internet access
                # NB: this completely overwrites whatever `caas_network` policy that was used to create the
                #     container, no matter the flag is True or False. Hence we allow None (skipping this).
                await terminal_session.session.update_network(
                    enable_network=self.enable_network_after_setup
                )

            ok_1, output_1 = await try_run_command(
                terminal_session,
                f"ls /",
                attempts=3,
            )
            ok_2, output_2 = await try_run_command(
                terminal_session,
                f"ls /testbed",
                attempts=3,
            )
            ok_3, output_3 = await try_run_command(
                terminal_session,
                f"ls /drop",
                attempts=3,
            )

            with open("/var/log/supervisor/chenliang1_check_image_content_1.log", "a") as f:
                f.write(f"{ok_1} {output_1}\n")
            with open("/var/log/supervisor/chenliang1_check_image_content_2.log", "a") as f:
                f.write(f"{ok_2} {output_2}\n")
            with open("/var/log/supervisor/chenliang1_check_image_content_3.log", "a") as f:
                f.write(f"{ok_3} {output_3}\n")

            prerunner_command = metadata.prerunner_command
            if prerunner_command != "":
                exit_code, output = await terminal_session.session.run(
                    RawExec(["bash", prerunner_command], workdir=metadata.cwd, timeout=900)
                )

                with open("/var/log/supervisor/chenliang1_prerunner_command.log", "a") as f:
                    f.write(f"prerunner_command: {prerunner_command}\n")
                    f.write(f"exit_code: {exit_code}; output:{output}\n")


        except Exception as e:
            with open("/var/log/supervisor/chenliang1_caas_resource_config_error.log", "a") as f:
                f.write(f"{e}\n")
            await caas_session.close()
            raise

        return CaasContainer(
            caas_endpoint=self.caas_endpoint,
            image_name=metadata.docker_image,
            caas_session_state=caas_session.save(),
            user=self.user,
            is_owner=True,
        )
