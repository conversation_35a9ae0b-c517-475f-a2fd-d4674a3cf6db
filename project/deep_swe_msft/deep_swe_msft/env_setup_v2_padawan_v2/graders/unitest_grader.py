import shlex
from typing import TypedDict
from uuid import uuid4

import structlog
import shlex

import berry
from caas import ExecError
from caas.api import caas_api
from caas.commands import Exec, RawBashScript, RawExec, UploadFile
from caas.terminal.api import TerminalSession
from deep_swe_msft.env_setup_v2_padawan_v2.setup.setup import env_setup_v2_setup_fn_internal
from deep_swe_msft.env_setup_v2_padawan_v2.setup.setup import EnvSetupTaskMetadata, EnvSetupTask

logger = structlog.get_logger(__name__)

TEST_OUTPUT_MAX = 262_144

class GradeReport(TypedDict):
    passed: bool
    test_output: str

async def grade_fn_v2(
    *,
    sample: berry.SampleWithCompletion[berry.DatapointT],
    terminal_session: TerminalSession,
    # At the risk of hacking, this grades in the same container (vs. changing the container.)
    fast: bool = True,
) -> bool:
    dp_metadata = sample.gt_datapoint.metadata
    metadata = EnvSetupTaskMetadata.model_validate(dp_metadata)
    report = await grade_fn_v2_internal(
        terminal_session=terminal_session,
        metadata=metadata,
        fast=fast,
    )
    logger.info("Grading finished", **report)
    sample.metadata["grader"] = {
        "score": int(report["passed"]),
    }
    return report["passed"]


async def grade_fn_v2_internal(
    *,
    terminal_session: TerminalSession,
    metadata: EnvSetupTaskMetadata,
    fast: bool,
) -> GradeReport:
    assert isinstance(metadata.task, EnvSetupTask)

    if fast:
        # hand off the model patch object instead of individual fields.
        return await _grade_fn_v2_inner(terminal_session, metadata)

    # use the secure grading path, passing the full model patch object.
    return await _grade_fn_v2_secure(metadata)


from deep_swe_msft.tools.utils import CAAS_ENDPOINT

async def _grade_fn_v2_secure(
    metadata: EnvSetupTaskMetadata,
) -> GradeReport:
    """
    The "safe" route - re-initialize a fresh container and grade in there.
    """
    caas = caas_api(CAAS_ENDPOINT)
    async with caas.use_session(
        image=metadata.docker_image,
        cpu_limit=str(metadata.limits.cpu),
        memory_limit=f"{metadata.limits.memory}m",
        keepalive_interval=30,
        timeout=1200,
    ) as session:
        grade_session = TerminalSession(session)
        await env_setup_v2_setup_fn_internal(terminal_session=grade_session, metadata=metadata)

        return await _grade_fn_v2_inner(
            grade_session=grade_session,
            metadata=metadata,
        )

async def _grade_fn_v2_inner(
    grade_session: TerminalSession,
    metadata: EnvSetupTaskMetadata,
) -> GradeReport:
    assert isinstance(metadata.task, EnvSetupTask)
    try:
        success_command = metadata.success_command
        exit_code, output = await grade_session.session.run(
            RawExec(["bash","-c", success_command], workdir=metadata.cwd, timeout=900)
        )

        with open("/var/log/supervisor/chenliang1_success_command.log", "a") as f:
            f.write(f"success_command: {success_command}\n")
            f.write(f"output: {output}\n")

        instance_id = metadata.task.instance_id
        if exit_code == 0 and ("Setup successful" in output.decode(errors="ignore") or metadata.task.subtype == "dependency_resolution"):
            with open("/var/log/supervisor/chenliang1_unit_test_grader_successful.log", "a") as f:
                f.write(f"instance_id: {instance_id}; exit_code: {exit_code}; output: {output}\n")
            return {
                "passed": True,
                "test_output": "test output: "
                + output.decode(errors="ignore"),
            }
        else:
            with open("/var/log/supervisor/chenliang1_unit_test_grader_failed.log", "a") as f:
                f.write(f"instance_id: {instance_id}; exit_code: {exit_code}; output: {output}\n")
            return {
                "passed": False,
                "test_output": "test output: "
                + output.decode(errors="ignore"),
            }
    
    except ExecError as e:
        with open("/var/log/supervisor/chenliang1_unit_test_grader_error.log", "a") as f:
            f.write(f"instance_id: {metadata.instance_id}; exit_code: {exit_code}; output: {e.output}\n")
        return {
            "passed": False,
            "test_output": "Error running model test: "
             + e.output.decode(errors="ignore"),
        }
