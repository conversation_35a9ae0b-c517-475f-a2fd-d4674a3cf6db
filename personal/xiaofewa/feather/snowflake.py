import snowflake.snowpark as snowpark
from snowflake.snowpark.functions import col, lit, when, parse_json, get_path
import json

filter_imagegen = True

def main(session: snowpark.Session): 
    tableName = 'sample_events'

    # exp_id = "xiaofewa-appberry-feather-short-prompt-juice256-compTrue-20250713-224725-peaval" # used for feather batch 1
    # exp_id = "xiaofewa-appberry-feather-long-prompt-juice256-compTrue-20250714-180946-peaval"
    # exp_id = "xiaofewa-appberry-feather-long-prompt-juice256-compTrue-20250716-195258-peaval" # used for feather batch 1
    # exp_id = "xiaofewa-appberry-hero-long-prompt-juice256-compTrue-20250718-172810-peaval"
    exp_id = "chunglin-appberry-feather-long-prompt-juice256-compTrue-20250718-180205-imagegen-hpe2-peaval"
    # with ImageGen no computer tool, from cc
    exp_id = "chunglin-appberry-feather-long-prompt-juice256-compFalse-20250718-193005-imagegen-hpe2-peaval"
    # no ImageGen no computer tool, from cc
    exp_id = "chunglin-appberry-feather-long-prompt-juice256-compFalse-20250718-195915-peaval"

    # AppBerry 0424 with ImageGen"
    exp_id = "chunglin-appberry-0424-feather-long-prompt-juice256-compTrue-20250801-150851-imagegen-peaval"

    # GPT-5 mini, no ImageGen
    # exp_id = "chunglin-appberry-gpt5mini-feather-long-prompt-juice256-compTrue-20250729-204207-3-peaval"

    # GPT-5 d64 530, no ImageGen
    # exp_id = "chunglin-appberry-gpt5d64-530-feather-long-prompt-juice256-compTrue-20250731-163659-hpe2-peaval"

    # Shishito, no ImageGen
    # exp_id = "chunglin-appberry-shishito-feather-long-prompt-juice256-compTrue-20250731-190820-hpe2-peaval"

    # GPT-5 d64 530, with ImageGen
    # exp_id = "chunglin-appberry-gpt5d64-530-feather-long-prompt-juice256-compTrue-20250801-005438-imagegen-hpe2-peaval"
    
    # Filter for the desired experiment and event type
    df = (
        session.table(tableName)
        .filter((col("base_experiment_id") == exp_id) & (col("EVENT_TYPE") == "eval_sample_batch_complete"))
    )


    # Collect all eval_sample_batch_complete events
    eval_complete_rows = df.select(
        col("SAMPLE_ID"),
        col("DATA")
    ).collect()
    
    # Get all sample IDs
    sample_ids = {row["SAMPLE_ID"] for row in eval_complete_rows}
    
    # Get additional event data for prompts and grader metadata
    events_df = (
        session.table(tableName)
        .filter(
            (col("BASE_EXPERIMENT_ID") == exp_id) & 
            col("EVENT_TYPE").isin("driver_train_ready", "worker_sample_complete", "driver_upload_sample_batch")
        )
        .select(
            col("SAMPLE_ID"),
            col("EVENT_TYPE"),
            col("DATA").as_("DATA_JSON")
        )
    )
    
    # Collect event rows
    event_rows = events_df.collect()
    
    # Process rows to extract prompts and grader data
    results = {}
    
    # Initialize results for all samples
    for sample_id in sample_ids:
        results[sample_id] = {
            "experiment_id": exp_id,
            "sample_id": sample_id,
            "is_sample_correct": "unknown",  # Placeholder value
            "prompt": "",
            "cua_graded_rubric": {}
        }
    
    # Process event rows for additional data
    for row in event_rows:
        sample_id = row["SAMPLE_ID"]
        
        if sample_id not in results:
            # Skip events for samples not in our list
            continue
        
        # Extract prompt from gt_datapoint_serializable
        if row["DATA_JSON"]:
            data = row["DATA_JSON"]
            
            # Look for prompt in gt_datapoint_serializable
            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except:
                    continue
                    
            if "gt_datapoint_serializable" in data and "problem" in data["gt_datapoint_serializable"]:
                try:
                    problem_str = data["gt_datapoint_serializable"]["problem"]
                    if isinstance(problem_str, str):
                        problem = json.loads(problem_str)
                    else:
                        problem = problem_str
                        
                    for msg in problem.get("messages", []):
                        if msg.get("author", {}).get("role") == "user":
                            content = msg.get("content", {})
                            if isinstance(content, dict) and "parts" in content:
                                parts = content["parts"]
                                if parts and isinstance(parts[0], str):
                                    results[sample_id]["prompt"] = parts[0]
                                    break
                except Exception as e:
                    print(f"Error parsing problem for sample {sample_id}: {e}")
                    pass
            
            # Look for grader metadata
            if "metadata_slim" in data:
                metadata = data["metadata_slim"]
                for i in range(100):
                    grader_key = f"grader.{i}"
                    if grader_key in metadata:
                        grader_data = metadata[grader_key]
                        if grader_data.get("grader_name") == "AppberryCuaRubricGrader":
                            all_log_rewards = grader_data.get("all_log_rewards", {})
                            results[sample_id]["cua_graded_rubric"] = {
                                k: (v == 0) for k, v in all_log_rewards.items()
                            }
                            break
    
    # Convert results to list
    results_list = list(results.values())
    
    # Create JSONL output
    print(f"\n=== JSONL Output ({len(results_list)} records) ===\n")
    for result in results_list:
        print(json.dumps(result))
    # Ensure logs are flushed so the Snowflake UI can display them immediately
    import sys
    sys.stdout.flush()

    # Create DataFrame for display and ALWAYS show it so the worksheet captures output
    if results_list:
        display_df = session.create_dataframe(results_list)
    else:
        display_df = session.create_dataframe([{"message": "No samples found"}])

    # Print summary, show dataframe and flush again before returning
    print(f"\n\nTotal samples processed: {len(results_list)}")
    display_df.show()
    sys.stdout.flush()
    return display_df