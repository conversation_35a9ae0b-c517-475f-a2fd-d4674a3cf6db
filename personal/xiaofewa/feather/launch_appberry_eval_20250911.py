import asyncio
import json
import uuid  # Add this import
from feather_api_client import <PERSON>ather<PERSON><PERSON>
from feather_api_client.client import FeatherTier
from feather_api_client.types import CreateFeatherTaskParams, ClaimType, CustomTaskOptions, FeatherCampaignMetadata, FeatherCampaignStatus, FeatherTaskBatchStatus, FeatherTaskStatus, UpdateFeatherTaskParams

MY_EMAIL = "<EMAIL>"
FEATHER_ENVIRONMENT = FeatherTier.GENAI_DEV
DEBUGGING_IDENTIFIER = "webapp-comparison-anonymized"
CAMPAIGN_NAME = "WebApp Comparison"
# BATCH_NAME = "WebApp Eval Round 2-2025-08-21-Training"
BATCH_NAME = "WebApp Eval Round 2-2025-09-11"
TASK_KIND = "conversation" # You can find all the task kinds here: https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror?path=/feather/feather-app-server/feather_app_server/graphql/task/types.py&version=GBmsft/genai-feather&_a=contents
BATCH_JSON_FILE = "./feather_eval_v1_20250911.jsonl"  # This file should contain the video URLs and prompts in JSONL format


INST_v2="""
# 🌐 Web App Review Instructions

You will be rating AI-generated web apps. Follow these steps:

---

## 1️⃣ Open and Interact with Apps

- **Launch** Websites in separate browser windows or tabs.
- **Interact with the sites thoroughly:** click links, resize windows, scroll, etc.

---

## 2️⃣ Evaluate Using the 1–7 Likert Scale

- For each category below, **rate both apps on a scale from 1 (worst) to 7 (best)**.
- **Scores should reflect each app’s absolute performance**—rate each app on its own merits, not just relative to the other.
- However, direct comparison should inform your judgment: use the side-by-side experience to help calibrate your ratings and spot strengths or weaknesses.
- **IMPORTANT: Base your interactions on the original prompt (provided below along with the website URLs) used to generate these websites.** The prompt describes the intended functionality and purpose of the apps—use this as your guide for what features to test and how the apps should behave.
- The categories in the evaluation form define what you will be rating. Please read the definitions carefully before rating.
- Note: If you see **App Deployment Failed**, simply rate it 0.

---

## 3️⃣ Justify Your Ratings

- For each score, **briefly explain your reasoning** in the justification fields.
- **Be specific about what you tested.** Mention the features, flows, or elements you interacted with to reach your conclusion.

> **Example:**  
> _"Functionality: I tested the login flow, added and deleted items from the cart, and checked error messages for invalid input. All features worked smoothly except the checkout button, which sometimes didn’t respond."_

---
"""

async def main():
    client = FeatherClient(script_name=DEBUGGING_IDENTIFIER, instance=FEATHER_ENVIRONMENT)
    campaign_metadata = FeatherCampaignMetadata(owner_team ='ai_scientist')

    # Get user by email
    user = await client.get_user_by_email(MY_EMAIL)
    print(f"Hey there, {user.email}! Your user id is {user.id}.")
    
    # Create the Campaign
    campaign = await client.get_or_create_campaign(
        name=CAMPAIGN_NAME,
        owner_user_id=user.id,
        metadata=campaign_metadata,
        status=FeatherCampaignStatus.ACTIVE,
    )

    # Create the Task Batch
    batch = await client.get_or_create_task_batch(
        name=BATCH_NAME,
        campaign_id=campaign.id,
        status=FeatherTaskBatchStatus.ACTIVE,
    )

    print(f"Created Batch {batch.name} with id {batch.id}")
    
    # Load video URLs from local file
    with open(BATCH_JSON_FILE) as f:
        video_urls = [json.loads(line) for line in f]

    custom_options = CustomTaskOptions(
        allow_generated_title=False
    )

    for i, video_url in enumerate(video_urls):
        admin_tags = []

        admin_tags.extend(["intra-model"])
        prompt = video_url["prompt"]
        
        # Collect all URLs from the entry
        urls = []
        url_keys = ["first_URL", "second_URL", "third_URL", "fourth_URL", "fifth_URL"]
        for key in url_keys:
            if key in video_url and video_url[key]:
                urls.append(video_url[key])
        
        # Create preseeded completions for all URLs
        rollouts = []
        for url in urls:
            if url == "null":
                rollouts.append([{
                    "id": str(uuid.uuid4()),
                    "author": {"role": "assistant"},
                    "content": {
                        "content_type": "text",
                        "parts": [f"App Deployment Failed"]
                    },
                    "status": "finished_successfully",
                    "metadata": {}
                }])
            else:
                rollouts.append([{
                    "id": str(uuid.uuid4()),
                    "author": {"role": "assistant"},
                    "content": {
                        "content_type": "text",
                        "parts": [f"[Open deployed app]({url})"]
                    },
                    "status": "finished_successfully",
                    "metadata": {}
                }])
        
        completions_record = {
            "rollouts": rollouts
        }
        
        # Create the conversation with preseeded completions
        convo = {
            "metadata": {},
            "messages": [
                {
                    "role": "assistant",
                    "content": {
                        "content_type": "text",
                        "parts": [INST_v2]
                    }
                },
                {
                    "role": "user",
                    "content": {
                        "content_type": "text",
                        "parts": [f"""## Below is the **PROMPT** used to generate the websites\n\n{prompt}\n\n ## **If you see `App Deployment Failed`, simply rate it 0.**"""]
                    }
                },
                # Preseeded completions message
                {
                    "role": "assistant",
                    "content": {
                        "content_type": "text",
                        "parts": [""]
                    },
                    "status": "in_progress",
                    "metadata": {
                        "completions_record": completions_record,
                        "custom_data": {},
                        "feather_completions": [
                            {"model_definition": None, "highlights": None}
                            for _ in urls  # Create one entry per URL
                        ]
                    }
                }
            ]
        }

        # Create links metadata for all URLs
        links_by_exp = {}
        for idx, url in enumerate(urls):
            model_key = f"model_{chr(97 + idx)}"  # model_a, model_b, model_c, etc.
            links_by_exp[model_key] = [url]

        task_metadata = {
            "links": links_by_exp,
        }
        
        task_obj = CreateFeatherTaskParams(
            title=f"Sample {i+1}",
            instructions=INST_v2,
            kind=TASK_KIND,
            task_batch_id=batch.id,
            input=convo,
            metadata=task_metadata,
            admin_tags=admin_tags,
            tags=["webapp-comparison"],
            custom_options=custom_options,
            claim_type=ClaimType.UNIQUE_CLAIM
        )

        # Create the Task
        task = await client.create_task(task_obj)
        _ = await client.update_task(
            task_id=task.id,
            params=UpdateFeatherTaskParams(
                status=FeatherTaskStatus.UNCLAIMED
            )
        )
        print(f"Created Task {task.title} with id {task.id}.")

        # break


asyncio.run(main())
