{"first_URL": "https://829bba65-words-with-friends-frontend.pages.dev", "second_URL": "https://085343a7-words-with-friends-frontend.pages.dev", "third_URL": "https://1bfd04db-words-with-friends-frontend.pages.dev", "fourth_URL": "https://ae70c888-words-with-friends-frontend.pages.dev", "fifth_URL": "https://c01ce5a0-words-with-friends-frontend.pages.dev", "prompt": "Words With Friends Clone: Create a Words With Friends clone.", "website_name": "words"}
{"first_URL": "https://3a58ac40-wordle-game-make-frontend.pages.dev", "second_URL": "https://b716c224-wordle-game-make-frontend.pages.dev", "third_URL": "https://4ca882b8-wordle-game-make-frontend.pages.dev", "fourth_URL": "https://c160c1bd-wordle-game-make-frontend.pages.dev", "fifth_URL": "https://d8802265-wordle-game-make-frontend.pages.dev", "prompt": "Wordle Game: Make a Wordle game.", "website_name": "wordle"}
{"first_URL": "https://011a9571-ui-edit-tool-frontend.pages.dev", "second_URL": "https://c17b016b-ui-edit-tool-frontend.pages.dev", "third_URL": "https://2d5afd4b-ui-edit-tool-frontend.pages.dev", "fourth_URL": "https://e78de225-ui-edit-tool-frontend.pages.dev", "fifth_URL": "https://6493dc46-ui-edit-tool-frontend.pages.dev", "prompt": "UI Edit Tool (Figma-like): Create a UI edit tool similar to Figma.", "website_name": "ui"}
{"first_URL": "https://87ef043a-tetris-game-create-frontend.pages.dev", "second_URL": "https://5fa5cbe6-tetris-game-create-frontend.pages.dev", "third_URL": "https://7378df9c-tetris-game-create-frontend.pages.dev", "fourth_URL": "https://ea14a736-tetris-game-create-frontend.pages.dev", "fifth_URL": "https://e342d30d-tetris-game-create-frontend.pages.dev", "prompt": "Tetris Game: Create a Tetris game.", "website_name": "tetris"}
{"first_URL": "https://6586988b-tesla-supercharger-m-frontend.pages.dev", "second_URL": "https://5023c0bc-tesla-supercharger-m-frontend.pages.dev", "third_URL": "https://362339e1-tesla-supercharger-m-frontend.pages.dev", "fourth_URL": "https://f176c950-tesla-supercharger-m-frontend.pages.dev", "fifth_URL": "https://2102c5c7-tesla-supercharger-m-frontend.pages.dev", "prompt": "Tesla Supercharger Map: Build a live map app showing all countries in the world that have Tesla Superchargers, with their exact locations marked by a red Tesla logo.", "website_name": "tesla"}
{"first_URL": "https://26f3112d-tax-estimate-tool-frontend.pages.dev", "second_URL": "https://2732450f-tax-estimate-tool-frontend.pages.dev", "third_URL": "https://3fc0d020-tax-estimate-tool-frontend.pages.dev", "fourth_URL": "https://810af5c1-tax-estimate-tool-frontend.pages.dev", "fifth_URL": "https://a44b7171-tax-estimate-tool-frontend.pages.dev", "prompt": "Tax Estimate Tool: Develop a tax estimate tool that asks the user for their income, state, and tax status to estimate their tax due.", "website_name": "tax"}
{"first_URL": "https://c9d88a06-student-grading-desi-frontend.pages.dev", "second_URL": "https://66f53332-student-grading-desi-frontend.pages.dev", "third_URL": "https://2da0d951-student-grading-desi-frontend.pages.dev", "fourth_URL": "https://6a06c8fb-student-grading-desi-frontend.pages.dev", "fifth_URL": "https://32dd9684-student-grading-desi-frontend.pages.dev", "prompt": "Student Grading: Design a responsive page for students to view grades, feedback, and dashboards with filters for subject and assessment. Use tabs for each section (Grades, Feedback, Dashboard). Include visual summaries and collapsible feedback entries.", "website_name": "student"}
{"first_URL": "https://b74742a6-spanish-tutor-create-frontend.pages.dev", "second_URL": "https://580b24fc-spanish-tutor-create-frontend.pages.dev", "third_URL": "https://b6b57ab8-spanish-tutor-create-frontend.pages.dev", "fourth_URL": "https://543516e5-spanish-tutor-create-frontend.pages.dev", "fifth_URL": "https://56703896-spanish-tutor-create-frontend.pages.dev", "prompt": "Spanish Tutor: create an AI powered spanish tutor that helps students practice spanish vocabulary, grammar, and speaking skills using voice interactions", "website_name": "spanish"}
{"first_URL": "https://a35e1174-simple-todo-app-frontend.pages.dev", "second_URL": "https://7c0c8d0a-simple-todo-app-frontend.pages.dev", "third_URL": "https://ffa00eeb-simple-todo-app-frontend.pages.dev", "fourth_URL": "https://0848865c-simple-todo-app-frontend.pages.dev", "fifth_URL": "https://2633c18c-simple-todo-app-frontend.pages.dev", "prompt": "Simple Todo App: Create simple todo app with add, remove, and complete functionality", "website_name": "simple"}
{"first_URL": "https://1b69d639-school-snow-day-frontend.pages.dev", "second_URL": "https://bed5108f-school-snow-day-frontend.pages.dev", "third_URL": "https://4580dcb6-school-snow-day-frontend.pages.dev", "fourth_URL": "https://761ab4c0-school-snow-day-frontend.pages.dev", "fifth_URL": "https://03abc6e6-school-snow-day-frontend.pages.dev", "prompt": "School Snow Day Predictor: Develop a school snow day predictor app.", "website_name": "school"}
{"first_URL": "https://872a644d-property-search-app-frontend.pages.dev", "second_URL": "https://780d95c3-property-search-app-frontend.pages.dev", "third_URL": "https://56eea404-property-search-app-frontend.pages.dev", "fourth_URL": "https://9f92b94c-property-search-app-frontend.pages.dev", "fifth_URL": "https://92c25f3e-property-search-app-frontend.pages.dev", "prompt": "Property Search App: Please create a property search app.", "website_name": "property"}
{"first_URL": "https://3cf890ca-phaser-game-browser-frontend.pages.dev", "second_URL": "https://c0e1a416-phaser-game-browser-frontend.pages.dev", "third_URL": "https://65288b68-phaser-game-browser-frontend.pages.dev", "fourth_URL": "https://920db88a-phaser-game-browser-frontend.pages.dev", "fifth_URL": "https://7f0cee84-phaser-game-browser-frontend.pages.dev", "prompt": "Phaser Game: Browser game using Phaser 3 using Phaser labs resources of dude collection stars and dodging bombs", "website_name": "phaser"}
{"first_URL": "https://74ab5651-npm-package-comparer-frontend.pages.dev", "second_URL": "https://353836c0-npm-package-comparer-frontend.pages.dev", "third_URL": "https://0f2e1e15-npm-package-comparer-frontend.pages.dev", "fourth_URL": "https://b041993e-npm-package-comparer-frontend.pages.dev", "fifth_URL": "https://b98e0abe-npm-package-comparer-frontend.pages.dev", "prompt": "NPM package comparer: Create a web app that allows users to compare details of NPM packages in the public registry", "website_name": "npm"}
{"first_URL": "https://03817d61-music-visualizer-cre-frontend.pages.dev", "second_URL": "https://3a20d8d9-music-visualizer-cre-frontend.pages.dev", "third_URL": "https://4ae367be-music-visualizer-cre-frontend.pages.dev", "fourth_URL": "https://de09c17b-music-visualizer-cre-frontend.pages.dev", "fifth_URL": "https://d4031236-music-visualizer-cre-frontend.pages.dev", "prompt": "Music Visualizer: Create a music visualizer in the style of winamp, where I can upload any music file and have it visualized in a variety of fashions.", "website_name": "music"}
{"first_URL": "https://808e79cf-mock-account-gallery-frontend.pages.dev", "second_URL": "https://9350a44a-mock-account-gallery-frontend.pages.dev", "third_URL": "https://33128b63-mock-account-gallery-frontend.pages.dev", "fourth_URL": "https://447074e5-mock-account-gallery-frontend.pages.dev", "fifth_URL": "https://742f0ed7-mock-account-gallery-frontend.pages.dev", "prompt": "Mock Account Gallery: Build a page showing records as a gallery of cards using modern look & feel. All cards should have fixed size and tall enough to fit 4 lines of titles. Include name, entityimage on the top and, website, email, phone number. Make the component fill 100% of the space. Make the gallery scrollable. Use data from the table. Make each card clickable to open the record in a new window. The target URL should be current location path with following query string parameters: pagetype=entityrecord&etn=[entityname]&id=[recordid] where entityname is account and id is accountid.", "website_name": "mock"}
{"first_URL": "https://23524bc8-mobile-maintenance-d-frontend.pages.dev", "second_URL": "https://4c71d546-mobile-maintenance-d-frontend.pages.dev", "third_URL": "https://d6e99142-mobile-maintenance-d-frontend.pages.dev", "fourth_URL": "https://478259f1-mobile-maintenance-d-frontend.pages.dev", "fifth_URL": "https://9f6bf6fa-mobile-maintenance-d-frontend.pages.dev", "prompt": "Mobile Maintenance: Design a mobile-friendly page where maintenance workers can view tasks in a scrollable list, update statuses, and access repair history with filters for status and location. Maintenance workers can update statuses, access repair history, use filters for status and location, and search functionality for tasks. Include a scrollable list with load more functionality, mobile-friendly UI with appropriate spacing, font sizes, and touch targets, with error and loading states handled gracefully.", "website_name": "mobile"}
{"first_URL": "https://abbf58c3-mermaid-chart-extern-frontend.pages.dev", "second_URL": "https://8b9d52df-mermaid-chart-extern-frontend.pages.dev", "third_URL": "https://fea3ac58-mermaid-chart-extern-frontend.pages.dev", "fourth_URL": "https://f1300a3d-mermaid-chart-extern-frontend.pages.dev", "fifth_URL": "https://4f1528e5-mermaid-chart-extern-frontend.pages.dev", "prompt": "Mermaid Chart (external dependency): Create an AI-based mermaid renderer, where I add a prompt and we generate and render a mermaid chart for the prompt.", "website_name": "mermaid"}
{"first_URL": "https://e7771584-media-relations-desi-frontend.pages.dev", "second_URL": "https://22c63c9a-media-relations-desi-frontend.pages.dev", "third_URL": "https://d695ffc5-media-relations-desi-frontend.pages.dev", "fourth_URL": "https://c921c10a-media-relations-desi-frontend.pages.dev", "fifth_URL": "https://462bcb16-media-relations-desi-frontend.pages.dev", "prompt": "Media Relations: Design a mobile-friendly Power Platform page for public relations officers to manage press contacts and tasks efficiently. Card View (Mobile-Friendly Scrollable Cards): Each card displays: Contact name, Media outlet, Role/title (e.g., Journalist, Editor), Current engagement status (e.g., 'Active,' 'Pending Follow-up'). Action buttons per card: Log Interaction – Opens a quick form for notes, Edit Contact – Allows updating contact details, Assign Task – Opens task form to delegate media actions. Tap on card opens full contact record with offline-capable forms. Horizontal Control Bar (Beneath Header): Search box for quick filtering by name, outlet, or tag. Buttons: 'Card' – Enables vertical card layout view, 'List' – Switches to tabular layout for power users. List View: Sortable table with columns for: Contact name, Media outlet, Last interaction date, Assigned team member, Status tag. This page structure ensures that PR officers can handle fast-paced press coordination and communication from their phones with minimal taps.", "website_name": "media"}
{"first_URL": "https://161c7fc6-marketing-manager-to-frontend.pages.dev", "second_URL": "https://74c44901-marketing-manager-to-frontend.pages.dev", "third_URL": "https://deaf7538-marketing-manager-to-frontend.pages.dev", "fourth_URL": "https://5f9208d2-marketing-manager-to-frontend.pages.dev", "fifth_URL": "https://ded75120-marketing-manager-to-frontend.pages.dev", "prompt": "Marketing Manager: To support marketing managers in coordinating digital campaigns, I propose designing a responsive Power Platform page layout with two main sections: a task board for campaign planning and a data insights panel for analytics. The task board should use a Kanban-style layout with columns for campaign stages (e.g., Planning, In Progress, Review, Completed), where each card represents a task assigned to a marketing manager or content creator, showing task title, owner, due date, and status icons. Clicking a card opens a side panel with task details, attachments, and comments. The data insights panel should feature a dashboard with interactive charts and KPIs for campaign performance, engagement metrics, and conversion rates. Filters at the top should allow users to refine data by campaign, channel, or date range. A toggle bar should let users switch between 'Board View' and 'Analytics View,' ensuring seamless navigation between task management and performance tracking. This layout empowers marketing teams to manage workflows and monitor outcomes in a unified, intuitive interface.", "website_name": "marketing"}
{"first_URL": "https://3d4747c4-make-a-online-frontend.pages.dev", "second_URL": "https://11a0d2fa-make-a-online-frontend.pages.dev", "third_URL": "https://e8a9a484-make-a-online-frontend.pages.dev", "fourth_URL": "https://40921400-make-a-online-frontend.pages.dev", "fifth_URL": "https://0d31c5f3-make-a-online-frontend.pages.dev", "prompt": "Make a online multiplayer version of the game codenames\n\nMake it look just like the original game, with the same color scheme and layout, but call it SecretBerries. Make the logo a berry.\nNo login required, but roles are determined at game start. Users can create a new game, or join an existing one.\nShow a lobby of existing games.\n\nHere are the rules:\nTwo rival spymasters know the secret identities of 25 agents. Their teammates know the agents only by their codenames — single-word labels like \"disease\", \"Germany\", and \"carrot\". Yes, carrot. It's a legitimate codename. Each spymaster wants their team to identify their agents first...without uncovering the assassin by mistake.\nIn Codenames, two teams compete to see who can make contact with all of their agents first. Lay out 25 cards, each bearing a single word. The spymasters look at a card showing the identity of each card, then take turns clueing their teammates. A clue consists of a single word and a number, with the number suggesting how many cards in play have some association to the given clue word. The teammates then identify one agent they think is on their team; if they're correct, they can keep guessing up to the stated number of times; if the agent belongs to the opposing team or is an innocent bystander, the team's turn ends; and if they fingered the assassin, they lose the game.\nSpymasters continue giving clues until one team has identified all of their agents or the assassin has removed one team from play.\nMake sure you thoroughly test the game to make sure it works.", "website_name": "make"}
{"first_URL": "https://32bc66e7-make-a-mock-frontend.pages.dev", "second_URL": "https://215446df-make-a-mock-frontend.pages.dev", "third_URL": "https://1ec4edbc-make-a-mock-frontend.pages.dev", "fourth_URL": "https://abe0fe69-make-a-mock-frontend.pages.dev", "fifth_URL": "https://46aca891-make-a-mock-frontend.pages.dev", "prompt": "Make a mock windows 95 desktop, with the start button, calculator, a notepad app, and a simple minesweeper game, all functional. Make sure the windows are movable and resizeable!", "website_name": "make"}
{"first_URL": "https://08f81fde-kdrama-mood-based-re-frontend.pages.dev", "second_URL": "https://1961a0f5-kdrama-mood-based-re-frontend.pages.dev", "third_URL": "https://398d7905-kdrama-mood-based-re-frontend.pages.dev", "fourth_URL": "https://4ec4b903-kdrama-mood-based-re-frontend.pages.dev", "fifth_URL": "https://4c13b450-kdrama-mood-based-re-frontend.pages.dev", "prompt": "Kdrama Mood-based Recommendation App: Build an app that suggests Kdramas based on my mood.", "website_name": "kdrama"}
{"first_URL": "https://b5b5753c-json-to-csv-frontend.pages.dev", "second_URL": "https://12131c8e-json-to-csv-frontend.pages.dev", "third_URL": "https://1db854a2-json-to-csv-frontend.pages.dev", "fourth_URL": "https://39aa6e2b-json-to-csv-frontend.pages.dev", "fifth_URL": "https://4f3bb717-json-to-csv-frontend.pages.dev", "prompt": "JSON to CSV: Create a simple JSON to CSV converter", "website_name": "json"}
{"first_URL": "https://1589fc41-heineken-build-creat-frontend.pages.dev", "second_URL": "https://e3fcb298-heineken-build-creat-frontend.pages.dev", "third_URL": "https://eb4c0852-heineken-build-creat-frontend.pages.dev", "fourth_URL": "https://fee55962-heineken-build-creat-frontend.pages.dev", "fifth_URL": "https://d4373cd7-heineken-build-creat-frontend.pages.dev", "prompt": "Heineken Build: Create a page for uploading, visualizing, and editing rules for sales order validation. Experience has 3 steps as follows: 1. Customer selection - Show accounts as a gallery of cards. Each card include name, image, website, email, phone number. Use entity record image and Crop the photo as a circle. Give the top half of the card a light-colored background. User can select a single customer and click next to go to next step. 2. Upload a file - User uploads a CSV file with the proposed validation rules. The file will have 3 columns for 'rule name', 'condition', and 'description'. After the file has been uploaded, parse out the rules. When file uploaded and parsed successfully move to step 3. 3. Rule visualization and editing - visualized rules as read-only cards gallery format. For step 3, divide the card container into two sections. Left side for 'Active' rules and right side for 'Pending' - allow the user to drag and drop rule cards from an 'Active' area to 'Pending' and vise versa. Include the following icon-only buttons on each card: Edit button: opens a modal popup to adjust each of the 3 fields comprising the rule, Delete button: to removes the rule. Add a horizontal control bar under the top bar on step 3 that contains: A large search box, A New rule button (right-aligned in the bar) that opens a modal where a user can enter a new rule. Dont use theme colors. Use colors to show the page in dark mode. On the first page, include a retro-style mini-game (e.g., 8-bit arcade) for user engagement. For example, a game where the user can interact with moving objects on a conveyor belt.", "website_name": "heineken"}
{"first_URL": "https://3430f7a7-guitar-hero-stretch-frontend.pages.dev", "second_URL": "https://80137768-guitar-hero-stretch-frontend.pages.dev", "third_URL": "https://fa9249c8-guitar-hero-stretch-frontend.pages.dev", "fourth_URL": "https://58d7949d-guitar-hero-stretch-frontend.pages.dev", "fifth_URL": "https://3c9f8057-guitar-hero-stretch-frontend.pages.dev", "prompt": "Guitar Hero (Stretch Completion): Create a guitar hero game that I can play on my keyboard. I should be able to upload any music file, and it will get transformed into a guitar hero like track.", "website_name": "guitar"}
{"first_URL": "https://41c5d636-google-invites-notes-frontend.pages.dev", "second_URL": "https://37843e90-google-invites-notes-frontend.pages.dev", "third_URL": "https://e9d24e67-google-invites-notes-frontend.pages.dev", "fourth_URL": "https://a59b765f-google-invites-notes-frontend.pages.dev", "fifth_URL": "https://1eda6991-google-invites-notes-frontend.pages.dev", "prompt": "Google Invites Notes Tracker: Develop an app to track notes linked to Google invites, with transcription and note summary generation.", "website_name": "google"}
{"first_URL": "https://3b72a944-github-copilot-metri-frontend.pages.dev", "second_URL": "https://7bfe320a-github-copilot-metri-frontend.pages.dev", "third_URL": "https://00e05b38-github-copilot-metri-frontend.pages.dev", "fourth_URL": "https://19283648-github-copilot-metri-frontend.pages.dev", "fifth_URL": "https://27dd42fd-github-copilot-metri-frontend.pages.dev", "prompt": "GitHub Copilot Metrics Dashboard: Develop a GitHub Copilot metrics dashboard using GitHub APIs.", "website_name": "github"}
{"first_URL": "https://175ba3fc-flower-shop-website-frontend.pages.dev", "second_URL": "https://846d9fef-flower-shop-website-frontend.pages.dev", "third_URL": "https://5c91e564-flower-shop-website-frontend.pages.dev", "fourth_URL": "https://286817b9-flower-shop-website-frontend.pages.dev", "fifth_URL": "https://6acd068e-flower-shop-website-frontend.pages.dev", "prompt": "Flower Shop Website: Create a beautiful, modern marketing website for my flower shop.", "website_name": "flower"}
{"first_URL": "https://a79dbf24-firefighter-meal-to-frontend.pages.dev", "second_URL": "https://1cdc8ec9-firefighter-meal-to-frontend.pages.dev", "third_URL": "https://b9811c9a-firefighter-meal-to-frontend.pages.dev", "fourth_URL": "https://10b5151d-firefighter-meal-to-frontend.pages.dev", "fifth_URL": "https://775fbe3a-firefighter-meal-to-frontend.pages.dev", "prompt": "Firefighter Meal: To streamline firefighter engagement with station meals, I propose creating a responsive page that displays the weekly menu as a gallery of uniform meal cards. Each card should maintain a fixed height sufficient for two lines of the meal name and include a meal photo, name, day of the week, dietary tags (e.g., vegetarian, gluten-free), and a preference toggle labeled 'Will Eat' or 'Skip.' Cards should also include a small icon to provide instant feedback, and tapping on a card should open a detailed meal record in a new window displaying ingredients, allergens, and past ratings. A horizontal control bar beneath the header should feature a search box to filter meals by name or tag and two toggle buttons labeled 'Card' and 'Grid' to switch views of the page. The 'Grid' view should present a table listing meals by date, name, dietary tags, preference status, and feedback history. This page offers an accessible and visually engaging experience for firefighters while allowing kitchen teams to gather actionable feedback.", "website_name": "firefighter"}
{"first_URL": "https://c220fa10-feature-tracker-gene-frontend.pages.dev", "second_URL": "https://1a568539-feature-tracker-gene-frontend.pages.dev", "third_URL": "https://572ecf8b-feature-tracker-gene-frontend.pages.dev", "fourth_URL": "https://72e46415-feature-tracker-gene-frontend.pages.dev", "fifth_URL": "https://88e15cd0-feature-tracker-gene-frontend.pages.dev", "prompt": "Feature tracker: Generate correct typescript code. Use Material UI components and ensure you have the correct syntax. Do not use Fluent components. Build a page that helps me track the features that my team is working on. The table should have the following columns: 1. Feature name 2. Flags as a multi-line string textbox 3. nicely colored Checkboxes for Test, Preview, and Flags Still Needed. If any of these values are true that means the feature is ready to try in that environment; if false, it isn't available. I should be able to edit the features. All the data is stored in the Feature table. Please include a checkbox before the feature name in each row, and a button below the grid that says 'Copy Flags'. This checkbox is how users will choose which features they are interested in testing, and when the Copy Flags button is pressed it should show a simple dialog that includes a string concatenation of the flags which were selected using the checkboxes, with an & between each one. The goal of this button is to return a single URL query string that includes all of the flags for the features that the user wants to test.", "website_name": "feature"}
{"first_URL": "https://54197140-feature-list-build-frontend.pages.dev", "second_URL": "https://df6460ec-feature-list-build-frontend.pages.dev", "third_URL": "https://cf33b4a6-feature-list-build-frontend.pages.dev", "fourth_URL": "https://f58a359e-feature-list-build-frontend.pages.dev", "fifth_URL": "https://7dd9bb26-feature-list-build-frontend.pages.dev", "prompt": "Feature List: Build a page that helps me track the features that my team is working on. The table should have the following columns: 1. Feature name 2. Flags as a multi-line string textbox 3. nicely colored Checkboxes for Test, Preview, and Flags Still Needed. If any of these values (Test, Preview, Flags Still Needed) are true that means the feature is ready to try in that environment; if false, it isn't available. I should be able to edit the features. All the data is stored in the table.", "website_name": "feature"}
{"first_URL": "https://5e88f32b-expense-manager-use-frontend.pages.dev", "second_URL": "https://cc935ab8-expense-manager-use-frontend.pages.dev", "third_URL": "https://97d98b2b-expense-manager-use-frontend.pages.dev", "fourth_URL": "https://be72a3d0-expense-manager-use-frontend.pages.dev", "fifth_URL": "https://fdeeb9c0-expense-manager-use-frontend.pages.dev", "prompt": "Expense manager: Use fields Title, Expense Type, Expense Date, Amount, Location and Approved fields from Expense table. Build an Expense management page with the following tabs: 1. Dashboard Tab - Show 3 card list groups by Expense Approved state - Pending, Approved, Denied. Show a sum of each of the groups at the top of the group. Each card must show Title, Date, Amount. Cards background color is based on the value in the Approved field. Approved = Pastel Green, Pending = Pastel Yellow, Denied = Pastel Red. Add drag drop functionality between the 3 card lists, update the Approved state when a record moves from one group to another. Clicking on a card should popup up a form to edit the details of the Expense record and save it. 2. Calendar tab - Visualize the Expenses in this calendar tab as a calendar based on the Expense Date field. Show Title, Location, Amount and Type. Color the expenses as before. Clicking an expense in the calendar should show a popup to edit the details of the Expense in a popup. Dragging an expense item from one date to another should update the Expense Date field. 3. Map Tabs - Visualize the Expenses in this Map tab on a map based on the Location field. Show a map of the United states and plot the location as cities. On hover over the expense point show Title, Amount, Type and Location as a tooltip flyout. Color the expenses as before. 4. Chart tab - On this Chart tab Show a chart visualizing the expenses. Provide option to choose x axis to be from Expense Date, Expense Type, Approved state, Location. Y axis should be the sum of the Amounts based on grouping by x axis. Show only major tick points on the Y axis with good spacing between the ticks. Format the chart to be modern looking.", "website_name": "expense"}
{"first_URL": "https://563bd644-diet-planner-generat-frontend.pages.dev", "second_URL": "https://b88db70a-diet-planner-generat-frontend.pages.dev", "third_URL": "https://75f63622-diet-planner-generat-frontend.pages.dev", "fourth_URL": "https://452ed66a-diet-planner-generat-frontend.pages.dev", "fifth_URL": "https://7c088e2b-diet-planner-generat-frontend.pages.dev", "prompt": "Diet Planner: Generate diet plan app for planning food based on my glucose levels", "website_name": "diet"}
{"first_URL": "https://714d0e95-dad-joke-generator-frontend.pages.dev", "second_URL": "https://58bea6bf-dad-joke-generator-frontend.pages.dev", "third_URL": "https://b5d88b04-dad-joke-generator-frontend.pages.dev", "fourth_URL": "https://12ba0f92-dad-joke-generator-frontend.pages.dev", "fifth_URL": "https://a95e7fd4-dad-joke-generator-frontend.pages.dev", "prompt": "Dad joke generator: Create a dad joke generator using an LLM", "website_name": "dad"}
{"first_URL": "https://0cebb141-create-a-website-frontend.pages.dev", "second_URL": "https://db976fd2-create-a-website-frontend.pages.dev", "third_URL": "https://532914c3-create-a-website-frontend.pages.dev", "fourth_URL": "https://c756e6da-create-a-website-frontend.pages.dev", "fifth_URL": "https://2ce7e412-create-a-website-frontend.pages.dev", "prompt": "Create a website that presents a gallery of thumbnail images linking to external sites, lets visitors register and log in, cast up/down votes on items, reorder the list by newest or highest rated, and view a profile page showing their own votes. Build it with your own backend and database powering secure sessions and storing users, items and votes via RESTful APIs with hashed passwords and proper validation.", "website_name": "create"}
{"first_URL": "https://849fc8e7-create-a-website-frontend.pages.dev", "second_URL": "https://b07bea3a-create-a-website-frontend.pages.dev", "third_URL": "https://b69225bf-create-a-website-frontend.pages.dev", "fourth_URL": "https://a5571650-create-a-website-frontend.pages.dev", "fifth_URL": "https://08b68df7-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets visitors anonymously create or join multiplayer sessions and run their own AI technology company as a browser‑based idle management game, buying and upgrading data centers and researchers, accruing money and research with global supply/demand cost effects, unlocking technology through a research tree and using a prestige system to start over with bonuses. It should feature a continuous server simulation with real‑time dashboards, panels for actions and economy stats, a live leaderboard, clear controls and help overlays, persistent session state and a robust back end to keep all players in sync.", "website_name": "create"}
{"first_URL": "https://6fbceaa8-create-a-website-frontend.pages.dev", "second_URL": "https://fc456d62-create-a-website-frontend.pages.dev", "third_URL": "https://bfc6fafd-create-a-website-frontend.pages.dev", "fourth_URL": "https://80606b60-create-a-website-frontend.pages.dev", "fifth_URL": "https://161001e7-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users sign up and log in, create or join a family group and then assign and track chores to each other – marking them complete to earn points and unlock predefined badges. It should let people set up simple rewards they can “buy” with their points and provide personal dashboards and a family leaderboard that update in real time with friendly, responsive layouts and little animations for feedback.", "website_name": "create"}
{"first_URL": "https://0bfa9f63-create-a-website-frontend.pages.dev", "second_URL": "https://266a00ce-create-a-website-frontend.pages.dev", "third_URL": "https://bba5ed0b-create-a-website-frontend.pages.dev", "fourth_URL": "https://8e120f4e-create-a-website-frontend.pages.dev", "fifth_URL": "https://f43892e8-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users sign up and log in with their email and password, then view a list of one‑to‑one or group conversations with snippets and timestamps and start new chats via name autocompletion. Within each conversation let them send text and attach a single image or video, toggle simple reactions and see updates via polling in near‑real‑time, with a responsive two‑column layout for desktop and adaptive mobile navigation, backed by your own REST API, database and blob storage.", "website_name": "create"}
{"first_URL": "https://62c1f1dc-create-a-website-frontend.pages.dev", "second_URL": "https://6a0c73fa-create-a-website-frontend.pages.dev", "third_URL": "https://6ecca327-create-a-website-frontend.pages.dev", "fourth_URL": "https://2c49f827-create-a-website-frontend.pages.dev", "fifth_URL": "https://b87f313d-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users register and sign in then create or join groups via generated codes and propose films or series to a shared list, voting on entries with toggling support and live updating across all members. Provide simple inputs for a date and time so groups can set their watch party schedule, with a clean responsive interface and obvious navigation.", "website_name": "create"}
{"first_URL": "https://cfbe128d-create-a-website-frontend.pages.dev", "second_URL": "https://d256d514-create-a-website-frontend.pages.dev", "third_URL": "https://13e56737-create-a-website-frontend.pages.dev", "fourth_URL": "https://339d412b-create-a-website-frontend.pages.dev", "fifth_URL": "https://12c75cfc-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users register and log in to their own account and create named narrative projects where they can drop dialogue “nodes” onto a canvas, drag them around, edit their text and link them together with labelled choice lines, with everything saved to your own backend. Build in a simple project list and logout interface and a preview mode that hides the canvas and lets you step through the branching conversation with one button per choice.", "website_name": "create"}
{"first_URL": "https://f9e79587-create-a-website-frontend.pages.dev", "second_URL": "https://130bdd7a-create-a-website-frontend.pages.dev", "third_URL": "https://edbbaadb-create-a-website-frontend.pages.dev", "fourth_URL": "https://f8d7d4aa-create-a-website-frontend.pages.dev", "fifth_URL": "https://7596ef05-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users register and log in securely, see their personal dashboard of stories, start new ones with titles, predefined starter lines and custom color themes or join existing stories via a short, opaque code. Build story pages that show the ordered contributions from all participants with an input for adding new text and an option to export the finished narrative as a plain‑text download.", "website_name": "create"}
{"first_URL": "https://31a965f3-create-a-website-frontend.pages.dev", "second_URL": "https://9521bd7b-create-a-website-frontend.pages.dev", "third_URL": "https://4cbffd44-create-a-website-frontend.pages.dev", "fourth_URL": "https://845809e7-create-a-website-frontend.pages.dev", "fifth_URL": "https://055d4c5d-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users register and create or join a family group, then log in to a unified interface with tabs for a shared calendar, chores list, manual location updates and their own reminders with full add/edit/delete functionality and due‑highlighting. Build your own RESTful backend exposing JSON APIs enforcing secure authentication, session tokens and family‑scoped permissions so that all the data lives on your own server.", "website_name": "create"}
{"first_URL": "https://7d5e0ed6-create-a-website-frontend.pages.dev", "second_URL": "https://8ce199f9-create-a-website-frontend.pages.dev", "third_URL": "https://73834eea-create-a-website-frontend.pages.dev", "fourth_URL": "https://cd834218-create-a-website-frontend.pages.dev", "fifth_URL": "https://a861ae4a-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users register and choose to be a Podcaster or a Guest, manage their display name, description and topics and – for Guests – set available dates and times. Build functionality so Podcasters can search and filter Experts by keyword/topic, view their profiles and book an available slot, with both sides able to see their upcoming bookings and leave simple star ratings and comments.", "website_name": "create"}
{"first_URL": "https://9ac3992c-create-a-website-frontend.pages.dev", "second_URL": "https://13e0254e-create-a-website-frontend.pages.dev", "third_URL": "https://86f52e40-create-a-website-frontend.pages.dev", "fourth_URL": "https://0c6d5ceb-create-a-website-frontend.pages.dev", "fifth_URL": "https://6e865dac-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users register and authenticate with a username and password, then provides a single global chronologically ordered feed of short text messages where they can add their own posts up to a set length, reply to specific messages and like/unlike posts. Build it with simple session‑based authentication and RESTful endpoints enforcing things like text length limits and secure password storage, and present it in a clean, single‑column responsive interface with a header, input area and card‑style messages.", "website_name": "create"}
{"first_URL": "https://0aea2f0a-create-a-website-frontend.pages.dev", "second_URL": "https://965d2cf6-create-a-website-frontend.pages.dev", "third_URL": "https://235dfe06-create-a-website-frontend.pages.dev", "fourth_URL": "https://406a5677-create-a-website-frontend.pages.dev", "fifth_URL": "https://20e7d236-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users name themselves, create or join a 2‑4 player match via a short code, and play classic Scrabble on a 15×15 board with real‑time updating and server‑enforced rules – including word validation, scoring multipliers, bingos, exchanges, passes, challenges and end‑of‑game logic. Include a lobby and scoreboard, visible rack and bag count, intuitive drag/click controls, responsive design and an always‑available rules view, persisting state without any registration.", "website_name": "create"}
{"first_URL": "https://5c0db52b-create-a-website-frontend.pages.dev", "second_URL": "https://a5d94a06-create-a-website-frontend.pages.dev", "third_URL": "https://6fad9566-create-a-website-frontend.pages.dev", "fourth_URL": "https://c2e5bb36-create-a-website-frontend.pages.dev", "fifth_URL": "https://a04e2518-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets players enter a name, create or join a room with a code, select a historical era and then take turns encoding and decoding messages with ciphers to reveal pieces of a narrative and ultimately answer a final mystery question in either cooperative or competitive modes. Build an intuitive lobby and game view with narrative panels, ciphertext displays, history lists, input fields, scoreboards and turn indicators, real‑time syncing between browsers, persistent server state with session management and reconnect support, and a period‑evocative responsive design.", "website_name": "create"}
{"first_URL": "https://e09210ba-create-a-website-frontend.pages.dev", "second_URL": "https://6c600777-create-a-website-frontend.pages.dev", "third_URL": "https://df4e617c-create-a-website-frontend.pages.dev", "fourth_URL": "https://21cf585f-create-a-website-frontend.pages.dev", "fifth_URL": "https://dfef1c65-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets players enter a name and create or join a short‑code room, gather in a lobby and then be randomly assigned detective, suspect and informant roles for an episodic murder mystery. Players explore atmospheric narrative chapters with clickable hotspots to discover clues, chat globally and in private for interrogations, advance through host‑driven phases of exploration, interrogation, voting and reveal, see the murderer epilogue and rotate roles for next episodes — all with server‑side state management and reconnection support.", "website_name": "create"}
{"first_URL": "https://1443ef32-create-a-website-frontend.pages.dev", "second_URL": "https://a7d5fd99-create-a-website-frontend.pages.dev", "third_URL": "https://39d5a8c5-create-a-website-frontend.pages.dev", "fourth_URL": "https://125f2a8d-create-a-website-frontend.pages.dev", "fifth_URL": "https://99e546ec-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets people register and log in, create and browse user‑defined communities, post text threads and nested comments, and upvote or downvote posts and comments with the scores updating and contributing to user karma. Build an intuitive interface with a header for login/signup or user info, a sidebar listing communities and a main feed for global or per‑community posts and forms for creating content, while still allowing read‑only viewing for visitors. Persist all data on your own backend via simple RESTful endpoints, enforcing validations, unique names, sessions and one vote per user.", "website_name": "create"}
{"first_URL": "https://3e979c83-create-a-website-frontend.pages.dev", "second_URL": "https://26046887-create-a-website-frontend.pages.dev", "third_URL": "https://b8e5169e-create-a-website-frontend.pages.dev", "fourth_URL": "https://dbed498f-create-a-website-frontend.pages.dev", "fifth_URL": "https://5ba95c0b-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets groups of 5–10 friends choose a display name and join or create a short‑code lobby to play Secret Hitler online without registration, automatically dealing secret roles and guiding them through nominations, voting, policy enactment and special powers to the endgame. Build a backend to enforce the rules and maintain game state with real‑time updates via websockets or similar, and design an accessible, responsive UI showing the policy tracks, election tracker, player statuses, chat and an on‑demand rules reference while gracefully handling disconnects and reconnections.", "website_name": "create"}
{"first_URL": "https://318d123c-create-a-website-frontend.pages.dev", "second_URL": "https://13e9787d-create-a-website-frontend.pages.dev", "third_URL": "https://995e39e5-create-a-website-frontend.pages.dev", "fourth_URL": "https://0c167414-create-a-website-frontend.pages.dev", "fifth_URL": "https://a10c7472-create-a-website-frontend.pages.dev", "prompt": "Create a website that hosts a browser‑based, online multiplayer top‑down RPG that combines the visual charm of Paper Mario with Pokémon‑style creature collecting and turn‑based combat, letting players name their avatar, create or join sessions, explore richly illustrated maps, interact with NPCs, solve puzzles, accept quests, gather items, recruit allies and equip badges. Build it so each session shares an authoritative world state on the server with real‑time map syncing, lobby and session code management, and UI overlays for inventory, badges, quest log, chat and help.", "website_name": "create"}
{"first_URL": "https://8fd250e6-create-a-website-frontend.pages.dev", "second_URL": "https://4d97f47b-create-a-website-frontend.pages.dev", "third_URL": "https://81a2bd31-create-a-website-frontend.pages.dev", "fourth_URL": "https://9825c0ae-create-a-website-frontend.pages.dev", "fifth_URL": "https://87ac2395-create-a-website-frontend.pages.dev", "prompt": "Create a website that hosts a browser‑based multiplayer fantasy kingdom‑building strategy game, letting players choose a name, create or join a coded lobby and get placed onto a richly illustrated tiled map where on their turn they build on tiles, manage resources, recruit and move units, attack, trade, form alliances and tackle narrative events, quests, challenges and kingdom politics. It should feature an immersive medieval‑themed UI with intuitive panels for resources, territory, turn order, actions, chat and notifications, overlays for pop‑ups and an easy help system, while the backend persists game state, enforces rules via JSON API calls, handles session tokens, story scheduling and scoring.", "website_name": "create"}
{"first_URL": "https://5e578b0b-connect-4-game-frontend.pages.dev", "second_URL": "https://c1ac8a87-connect-4-game-frontend.pages.dev", "third_URL": "https://cded3f94-connect-4-game-frontend.pages.dev", "fourth_URL": "https://748f8ba3-connect-4-game-frontend.pages.dev", "fifth_URL": "https://161ae259-connect-4-game-frontend.pages.dev", "prompt": "Connect 4 Game: Create a traditional Connect 4 game.", "website_name": "connect"}
{"first_URL": "https://14624849-coach-support-to-frontend.pages.dev", "second_URL": "https://0a038b47-coach-support-to-frontend.pages.dev", "third_URL": "https://d8e456ac-coach-support-to-frontend.pages.dev", "fourth_URL": "https://db24ae4d-coach-support-to-frontend.pages.dev", "fifth_URL": "https://ac2c20b0-coach-support-to-frontend.pages.dev", "prompt": "Coach Support: To enable wellness coaches to deliver personalized, data-driven support, design a comprehensive Power Platform page layout with distinct, interactive UI components that align with their core responsibilities. The top section should feature an employee well-being dashboard with Power BI tiles showing sentiment trends, submission volume, and risk indicators, along with dropdown filters for department, time range, or wellness category, and an alert banner highlighting employees flagged for urgent follow-up based on AI-driven thresholds. The left column should include a submission response panel with a scrollable list of employee entries marked by urgency badges, expandable cards showing submission summaries and tags, a rich text editor for composing personalized advice using AI-suggested templates, a status toggle to mark responses, and a history tab with a timeline of past interactions and feedback scores. The right column should host a resource recommendation engine with a search bar and filters, resource cards displaying title, source, snippet, and credibility score, a drag-and-drop panel to attach resources to responses, and a smart suggest button that recommends resources based on submission content and history. The bottom section should be a workshop management hub with a calendar view for scheduling, workshop cards with details like title, description, audience, and registration count, a feedback summary modal for post-session ratings and comments, and a clone workshop button to duplicate successful formats. A persistent top navigation bar should include global search, quick action buttons like 'New Submission,' 'Schedule Workshop,' and 'Generate Report,' and a notification center for real-time alerts. Additional features should include role-based access controls to manage visibility and permissions, an embedded Co-Pilot agent to assist with drafting responses and suggesting interventions, and an offline mode toggle to support data entry and review during connectivity gaps.", "website_name": "coach"}
{"first_URL": "https://5844045e-chess-game-can-frontend.pages.dev", "second_URL": "https://62db9c20-chess-game-can-frontend.pages.dev", "third_URL": "https://bbd5cb41-chess-game-can-frontend.pages.dev", "fourth_URL": "https://2ab02dce-chess-game-can-frontend.pages.dev", "fifth_URL": "https://f9d45404-chess-game-can-frontend.pages.dev", "prompt": "Chess Game: Can you make a chess game?", "website_name": "chess"}
{"first_URL": "https://9023d7cf-build-the-hackernews-frontend.pages.dev", "second_URL": "https://4711122b-build-the-hackernews-frontend.pages.dev", "third_URL": "https://fa8230d0-build-the-hackernews-frontend.pages.dev", "fourth_URL": "https://072043c3-build-the-hackernews-frontend.pages.dev", "fifth_URL": "https://b5f337c0-build-the-hackernews-frontend.pages.dev", "prompt": "Build the HackerNews like website but in the style of the New York Times newspaper layout (typography, fonts, etc)", "website_name": "build"}
{"first_URL": "https://d0410815-ascii-art-image-frontend.pages.dev", "second_URL": "https://4849e3c5-ascii-art-image-frontend.pages.dev", "third_URL": "https://2fb2c719-ascii-art-image-frontend.pages.dev", "fourth_URL": "https://6f03277f-ascii-art-image-frontend.pages.dev", "fifth_URL": "https://bd54f7c7-ascii-art-image-frontend.pages.dev", "prompt": "ASCII Art Image Generator: Create an image generator that takes a word as input and produces an ASCII art image.", "website_name": "ascii"}
{"first_URL": "https://dda82964-anxiety-reduction-ap-frontend.pages.dev", "second_URL": "https://639f9d54-anxiety-reduction-ap-frontend.pages.dev", "third_URL": "https://ef947fa4-anxiety-reduction-ap-frontend.pages.dev", "fourth_URL": "https://939648ab-anxiety-reduction-ap-frontend.pages.dev", "fifth_URL": "https://f498715c-anxiety-reduction-ap-frontend.pages.dev", "prompt": "Anxiety Reduction App: Create an app that helps lessen anxiety while out and about.", "website_name": "anxiety"}
{"first_URL": "https://6eed1d2e-accountcards-build-a-frontend.pages.dev", "second_URL": "https://3c92f127-accountcards-build-a-frontend.pages.dev", "third_URL": "https://51910bd2-accountcards-build-a-frontend.pages.dev", "fourth_URL": "https://eb2baa8d-accountcards-build-a-frontend.pages.dev", "fifth_URL": "https://27e51581-accountcards-build-a-frontend.pages.dev", "prompt": "AccountCards: Build a page showing accounts as a gallery of cards. All cards should have fixed size and tall enough to fit 2 lines of titles. Include name, image, website, email, phone number. Make each card clickable to open the account record in a new window. Include a horizontal control bar under the header section that contains a search box and Buttons to switch between card view and grid view of this page (labeled \"Card\" and \"Grid\").", "website_name": "accountcards"}
{"first_URL": "https://e9852290-wait-staff-table-frontend.pages.dev", "second_URL": "https://1dbcc804-wait-staff-table-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://fbedb859-wait-staff-table-frontend.pages.dev", "fifth_URL": "https://cf5d76ca-wait-staff-table-frontend.pages.dev", "prompt": "Wait Staff Table Management: To optimize real-time seating coordination, design a highly interactive Power Platform page tailored for waitstaff, integrating dynamic visualizations and actionable controls. The layout should feature a three-column structure: Left Panel – Interactive Floor Map: Display a scalable, color-coded map of the restaurant floor with table icons representing real-time statuses—green (Available), red (Occupied), yellow (Needs Cleaning), and blue (Reserved). Each icon should be tappable to open a modal with table details (e.g., server assigned, party size, time seated, and notes). Include a 'Quick Update' dropdown to change status instantly and a 'Seat from Waitlist' button to assign a waiting party. Center Panel – Waitlist Management Console: Present a vertically scrollable list of waitlist entries with expandable cards showing customer name, party size, arrival time, estimated wait, and seating preferences (e.g., booth, outdoor). Each card should include action buttons to 'Assign Table,' 'Update ETA,' or 'Remove.' A real-time timer should track how long each party has been waiting, with color-coded urgency indicators. Include a 'Smart Suggest' feature that recommends optimal table matches based on party size and preferences. Right Panel – Status & Notification Feed: Display a live activity feed showing recent updates (e.g., table cleared, party seated, waitlist updated) with timestamps and staff initials. Include a notification banner for high-priority alerts like long-waiting parties or table turnover delays. A collapsible filter bar at the top should allow filtering by server, table zone, or party size. Global features should include a persistent top bar with a search box, view toggles (Map/List), and a 'New Entry' button, role-based access to restrict certain actions (e.g., table reassignment) to leads, and offline sync capability for mobile use during connectivity drops.", "website_name": "waitstaff"}
{"first_URL": null, "second_URL": "https://903fe592-titanic-dataset-jupy-frontend.pages.dev", "third_URL": "https://a1c1fc28-titanic-dataset-jupy-frontend.pages.dev", "fourth_URL": "https://a16f1cea-titanic-dataset-jupy-frontend.pages.dev", "fifth_URL": "https://e9e7a19a-titanic-dataset-jupy-frontend.pages.dev", "prompt": "Titanic Dataset Jupyter Starter: Help me get started with the Titanic dataset using a Jupyter notebook.", "website_name": "waitstaff"}
{"first_URL": "https://3d4a50d8-team-planner-weekly-frontend.pages.dev", "second_URL": "https://abf6dea1-team-planner-weekly-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://9f1832ff-team-planner-weekly-frontend.pages.dev", "fifth_URL": "https://7479f91f-team-planner-weekly-frontend.pages.dev", "prompt": "Team Planner: Weekly team planning app - create an app that makes weekly team planning simple. allow the user to create a list of team members then plan out multiple weeks, one week at a time, for all teammembers. allow me to add OOO date ranges for specific team members. when a teammember is out of office, block out the ability to assign work for them during the associated weeks.", "website_name": "waitstaff"}
{"first_URL": "https://3a240894-san-francisco-boredo-frontend.pages.dev", "second_URL": "https://eaf478a5-san-francisco-boredo-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://a2d198e7-san-francisco-boredo-frontend.pages.dev", "fifth_URL": "https://c2221804-san-francisco-boredo-frontend.pages.dev", "prompt": "San Francisco Boredom PWA: Create a PWA that gives suggestions for what to do when bored in San Francisco.", "website_name": "francisco"}
{"first_URL": "https://d2c6ee80-restaurant-manager-t-frontend.pages.dev", "second_URL": "https://73e8f69e-restaurant-manager-t-frontend.pages.dev", "third_URL": "https://0232cb50-restaurant-manager-t-frontend.pages.dev", "fourth_URL": "https://da97faf2-restaurant-manager-t-frontend.pages.dev", "fifth_URL": null, "prompt": "Restaurant Manager: To streamline restaurant operations, I propose designing a responsive Power Platform page layout tailored for managers to oversee sales, shifts, inventory, maintenance, and feedback. The homepage should feature a dashboard with KPIs like daily sales, inventory alerts, and customer satisfaction scores. Below, use a tabbed layout with sections for Sales, Shifts, Inventory, Maintenance, and Feedback. Sales Tab: Display a line chart of daily sales with filters for date and menu category, and a table of transactions. Shifts Tab: Use a calendar view to assign and view staff shifts, with color-coded roles and drag-and-drop scheduling. Inventory Tab: Present a searchable list of food items with stock levels, reorder indicators, and export options. Maintenance Tab: Show a list of equipment with status badges and a reminder panel for overdue tasks. Feedback Tab: Include a sentiment chart and a scrollable list of customer comments with filters by date and rating. A persistent top bar should include global filters (e.g., date range, location), a search box, and a 'Create' button for adding new records. The layout ensures managers can monitor and act on key operational areas in one unified, data-rich interface.", "website_name": "calendar"}
{"first_URL": "https://fc22b763-rakuten-style-websit-frontend.pages.dev", "second_URL": null, "third_URL": "https://8ca64a54-rakuten-style-websit-frontend.pages.dev", "fourth_URL": "https://e3b6925c-rakuten-style-websit-frontend.pages.dev", "fifth_URL": "https://a932ceaa-rakuten-style-websit-frontend.pages.dev", "prompt": "Rakuten-style Website: Create a website that looks like rakuten.com.", "website_name": "waitstaff"}
{"first_URL": "https://a6b81ef7-power-of-attorney-frontend.pages.dev", "second_URL": "https://1cc08cc3-power-of-attorney-frontend.pages.dev", "third_URL": "https://99b847f7-power-of-attorney-frontend.pages.dev", "fourth_URL": null, "fifth_URL": "https://b1cd8546-power-of-attorney-frontend.pages.dev", "prompt": "Power Of Attorney: Create wizard for power of attorney revocation for bank employees processing revocations that includes the 5 steps below. Ensure each step is collecting the appropriate information. 1. User can Search & Select POA record and press next. 2. Revocation Documentation Upload – User upload proof of revocation (customer request, legal notice) and press next. 3. Account Access Removal – Remove POA authority from designated accounts. 4. Compliance & Legal Review – Confirm compliance with legal requirements. 5. Confirmation & Notifications – Send confirmation to relevant parties and update audit logs. Use PowerOfAttorney and BankAccount tables.", "website_name": "waitstaff"}
{"first_URL": null, "second_URL": "https://086b5775-multilingual-llm-tra-frontend.pages.dev", "third_URL": "https://8a822383-multilingual-llm-tra-frontend.pages.dev", "fourth_URL": "https://549ba690-multilingual-llm-tra-frontend.pages.dev", "fifth_URL": "https://da9ca2a3-multilingual-llm-tra-frontend.pages.dev", "prompt": "Multilingual LLM Translator App: Create an app that translates using an LLM and allows you to pick between 5 languages of your choice.", "website_name": "multilingual"}
{"first_URL": "https://0672b911-make-a-simple-frontend.pages.dev", "second_URL": "https://2d80d630-make-a-simple-frontend.pages.dev", "third_URL": "https://2ee864c4-make-a-simple-frontend.pages.dev", "fourth_URL": null, "fifth_URL": "https://56e7d991-make-a-simple-frontend.pages.dev", "prompt": "Make a simple React app for creating and viewing slideshows. The app should be titled “Slides, by Appberry”\n\nAll slideshows should be publicly viewable and editable. Do not add authentication\nEach slide can have a title + a description OR a title + image. This is to keep it as simple as possible. Descriptions should support markdown formatting. No need to add wyiwyg editing, just make it so that when the slide is displayed the markdown properly renders\nYou should be able to add, delete, and re-arrange slides (drag and drop)\nYou should also be able to change the BG color and font color of each slide\nThe home page should list all slideshows that have been created\nSlideshows must be able to be presented. When presenting a slideshow, there should be an option to exit the presentation and go back to editing. But the presentation UI itself should be very minimal, and the focus should be on the slides. The slides should take up most of the screen\nMake the app look like an app from the classic Mac operating system.\nMake the logo a berry.", "website_name": "slideshow"}
{"first_URL": "https://eacbf807-make-a-calendar-frontend.pages.dev", "second_URL": "https://2f9abc5b-make-a-calendar-frontend.pages.dev", "third_URL": "https://8f19618e-make-a-calendar-frontend.pages.dev", "fourth_URL": "https://d877ed5a-make-a-calendar-frontend.pages.dev", "fifth_URL": null, "prompt": "Make a calendar app in the style of Apple HIG design principles. should be able to schedule new events with other people, notify on new events.\n\nOne-tap event creation Speed.\nRecurring events with custom rules\n    Handles real-life patterns (e.g., “2nd Tuesday, every 3 months”)\nMultiple calendar layers\n    Separate work, personal, family, etc. without clutter\nColour-coding and quick toggles are essential.\nNatural-language input “Dinner with Sam next Fri 7-9 pm” creates an event.", "website_name": "calendar"}
{"first_URL": "https://e37b1197-loan-application-por-frontend.pages.dev", "second_URL": "https://ae86f124-loan-application-por-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://29e8e8d3-loan-application-por-frontend.pages.dev", "fifth_URL": "https://4fdf96cd-loan-application-por-frontend.pages.dev", "prompt": "Loan Application Portal: Create a page showing loan applications as cards with loan type, date, and status. Each card links to a detail view for document upload or updates. A control bar offers search and toggles between 'Card' and 'List' views. The 'List' view presents applications in a sortable table. Copilot, Power Automate, and Model-Driven Apps support internal actions and notifications.", "website_name": "waitstaff"}
{"first_URL": "https://1adc640d-influencer-portal-cr-frontend.pages.dev", "second_URL": "https://088f9055-influencer-portal-cr-frontend.pages.dev", "third_URL": "https://c3579f75-influencer-portal-cr-frontend.pages.dev", "fourth_URL": "https://36dd981a-influencer-portal-cr-frontend.pages.dev", "fifth_URL": null, "prompt": "Influencer Portal: Create a page where influencers can view active campaigns through vertically stacked cards, each showing the campaign name, brand logo, content type, due date, and status tag such as 'In Review' or 'Approved.' Cards include a quick-access link to submit content or review feedback, and tapping one opens a detailed brief in a mobile-optimized window. A control bar under the header includes a search box and view toggles labeled 'Card' and 'List.' The 'List' view presents campaigns in a table with columns for name, brand, deadline, and status. This streamlined layout enables easy content tracking and efficient campaign coordination on mobile.", "website_name": "waitstaff"}
{"first_URL": "https://15b727c9-fitness-tracker-app-frontend.pages.dev", "second_URL": "https://5ad9dc22-fitness-tracker-app-frontend.pages.dev", "third_URL": "https://ee0951ea-fitness-tracker-app-frontend.pages.dev", "fourth_URL": null, "fifth_URL": "https://6c6d005d-fitness-tracker-app-frontend.pages.dev", "prompt": "Fitness Tracker App (React/Django/MongoDB): Build a fitness tracker app using ReactJS for the frontend, Django for the backend, and MongoDB as the database.", "website_name": "calendar"}
{"first_URL": "https://ed494ccb-employee-anniversary-frontend.pages.dev", "second_URL": "https://9e254f67-employee-anniversary-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://eb687d5c-employee-anniversary-frontend.pages.dev", "fifth_URL": "https://8624882d-employee-anniversary-frontend.pages.dev", "prompt": "Employee Anniversary: Create a gallery card list for employee Anniversary table. For each card show the name, team, title, and years, emphasize the name and years of service. Include a Photo on top of each card, pull the value from the Photo column and give the top half of the card a light blue background. Include a horizontal tool bar under the Header section which includes: A search box, Small filter drop-down for team name, providing options for all the values in the team field, and another option for 'All' which is the default selection, A slider to filter anniversaries based on minimum number of years of experience ranging from 0 to 30 years. Add labels e.g. Years of service: 5+ years.", "website_name": "gallery"}
{"first_URL": "https://eb4d29f7-create-a-website-frontend.pages.dev", "second_URL": "https://2ecf0e72-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://976eb1a2-create-a-website-frontend.pages.dev", "fifth_URL": "https://4564f9e7-create-a-website-frontend.pages.dev", "prompt": "Create a website that provides a simple private space for families – visitors can sign up and log in with a display name, email, password and a shared family code that either ties them into an existing group or creates a new one, then see a shared list of recipes for their code. Give authenticated users a form to add and edit their own entries with fields for title, story, ingredients, steps, an optional heritage note and image/video attachments, show recipe cards with thumbnails and detail views with all the content, allow filtering the list by contributor or heritage, and include basic logout functionality.", "website_name": "gallery"}
{"first_URL": "https://20c8269d-create-a-website-frontend.pages.dev", "second_URL": "https://038dee06-create-a-website-frontend.pages.dev", "third_URL": "https://faaf4858-create-a-website-frontend.pages.dev", "fourth_URL": "https://3167430d-create-a-website-frontend.pages.dev", "fifth_URL": null, "prompt": "Create a website that lets visitors pick any name to start, draw a Pokémon‑inspired creature on an interactive canvas with brushes, shapes and colours and stores that image along with base stats and unlocked abilities keyed to the name. Then take that custom creature into turn‑based combat – choosing a boss from a gallery or creating/joining a short code multiplayer match – with HP bars, ability buttons, evolving stats, rewards and unlocks all handled behind the scenes and persisting between sessions.", "website_name": "slideshow"}
{"first_URL": "https://2e2ce2e4-create-a-website-frontend.pages.dev", "second_URL": "https://079c00db-create-a-website-frontend.pages.dev", "third_URL": "https://edb2b6c6-create-a-website-frontend.pages.dev", "fourth_URL": "https://91965d0b-create-a-website-frontend.pages.dev", "fifth_URL": null, "prompt": "Create a website that lets visitors paste base64‑encoded text, strips any data prefix, decodes it into a PNG on the server, stores it keyed by a generated unique identifier and then displays the picture with copyable direct link and embed code. Build a simple, neutral UI with a header and “Home”/“My Images” views that keep track of created IDs in browser storage, separate view pages at `/view/<id>`, and backend API endpoints for decoding and serving the images with proper validation, error messaging and persistent server storage.", "website_name": "base64"}
{"first_URL": "https://f27ba43a-create-a-website-frontend.pages.dev", "second_URL": "https://370c72c2-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://f560bda6-create-a-website-frontend.pages.dev", "fifth_URL": "https://fa7c0ca7-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users sign up and log in, form reading groups with a name, invited members and start/end dates for a shared book, carry on threaded conversations with text and image attachments, and propose new titles on the group page with simple voting to endorse them.", "website_name": "create"}
{"first_URL": "https://fe887b04-create-a-website-frontend.pages.dev", "second_URL": "https://398eda5f-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://fa9a4ff8-create-a-website-frontend.pages.dev", "fifth_URL": "https://c249c205-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users register and sign in, create and manage screenwriting projects with scenes, characters, plotlines and timeline views, save version snapshots and invite collaborators. The tool should feature an intuitive two‑pane, tabbed editing interface with drag‑drop ordering and operate on a secure RESTful backend storing everything internally with proper authentication and access control.", "website_name": "waitstaff"}
{"first_URL": "https://f01b1c22-create-a-website-frontend.pages.dev", "second_URL": "https://39698aa1-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://8ed7c811-create-a-website-frontend.pages.dev", "fifth_URL": "https://aaf55da4-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets users register and log in, compose short messages with a simple privacy choice of public or friends‑only and view a chronological feed of all public posts alongside the posts from people they’ve friended. Include friend management via sending and accepting requests, personal profile pages, persistent sessions and a clean, minimal UI with navigational links for feed, friends, profile and logout. Implement a RESTful backend with secure storage for users, posts, friend relations and session tokens.", "website_name": "waitstaff"}
{"first_URL": "https://03fe3ddc-create-a-website-frontend.pages.dev", "second_URL": "https://73b987c6-create-a-website-frontend.pages.dev", "third_URL": "https://e819def5-create-a-website-frontend.pages.dev", "fourth_URL": "https://24ca9cd6-create-a-website-frontend.pages.dev", "fifth_URL": null, "prompt": "Create a website that lets storytellers sign up, log in/out and view a clean dashboard listing all their storyboards, with controls to create, rename or delete them. Build an editor for each storyboard where they can upload images, type text and notes into card objects, drag cards to reorder them and delete them, with all of it persisted safely to your own server, database and file store.", "website_name": "waitstaff"}
{"first_URL": "https://fac60399-create-a-website-frontend.pages.dev", "second_URL": "https://15d74862-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://728dd343-create-a-website-frontend.pages.dev", "fifth_URL": "https://b486a17b-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets friends play an online adaptation of the secret‑role Hidden Leader game: visitors pick a nickname, create or join a coded session, the server deals secret roles, shuffles policy cards and drives rounds of nomination, voting, legislation and executive powers with real‑time updates via websockets or polling. Include lobby management, persistent chat, clear game boards and trackers, modals for hidden information, mobile‑friendly design, a rules reference, rejoin support and endgame screens, backed by a database enforcing the game rules and win conditions.", "website_name": "waitstaff"}
{"first_URL": "https://fb6ff5ca-create-a-website-frontend.pages.dev", "second_URL": "https://c218e822-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://197cc31d-create-a-website-frontend.pages.dev", "fifth_URL": "https://5869a16f-create-a-website-frontend.pages.dev", "prompt": "Create a website that immerses players in a real‑time 2D mining and exploration game – visitors type in a name, create or join a code‑protected session and enter a shared, procedurally generated underground world where they dig through stratified materials with the proper tools, gather and manage resources, craft and upgrade gear, build bases, fight off creatures with depth‑scaled difficulty and chat with teammates. Provide an intuitive UI with a lobby, world canvas, inventory/crafting/build panels, help overlays and exit controls, and implement a simple cloud back end that persists session state, runs the simulation tick loop, handles procedural generation on demand and manages multiplayer API calls.", "website_name": "slideshow"}
{"first_URL": "https://217194d0-create-a-website-frontend.pages.dev", "second_URL": "https://3164d084-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://c12a1434-create-a-website-frontend.pages.dev", "fifth_URL": "https://2d200df5-create-a-website-frontend.pages.dev", "prompt": "Create a website that faithfully recreates Codenames online, allowing players to anonymously create or join rooms via short codes, choose sides and spymasters in a lobby, and play on a random 5×5 codename grid with the key visible only to spymasters while enforcing the official clue/guess rules, timers, turn management and win/loss conditions. Build real‑time state syncing with persistent server‑side logic, internal team chat, a responsive colour‑coded UI including rules display and reconnection support, all on your own backend with no external dependencies.", "website_name": "waitstaff"}
{"first_URL": "https://bd4dbf80-create-a-website-frontend.pages.dev", "second_URL": "https://88d5c6b9-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://75661e33-create-a-website-frontend.pages.dev", "fifth_URL": "https://ebe0c3bb-create-a-website-frontend.pages.dev", "prompt": "Create a website that drops players into a 3D voxel valley where, by simply choosing a name and a world code, they explore, gather resources, craft items, build cube‑based structures and automation with conveyors and logic gates, and fulfil quests from friendly villagers. It should feature avatar and block customization, a persistent shared world with day/night cycles, weather effects, real‑time co‑op multiplayer and chat, intuitive inventory/crafting/placement UIs and simple session management.", "website_name": "slideshow"}
{"first_URL": "https://02ca4852-create-a-website-frontend.pages.dev", "second_URL": "https://996ef4c0-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://c49dc3c2-create-a-website-frontend.pages.dev", "fifth_URL": "https://7a3cabb6-create-a-website-frontend.pages.dev", "prompt": "Create a website that delivers a browser‑accessible multiplayer, persistent, top‑down, turn‑based farm and factory automation world where players set a name, create or enter a game code, explore a fog‑of‑war map to gather resources, build conveyors, machines and decorations, interact with NPCs and quests, and cope with changing seasons. Provide an intuitive canvas UI with inventory and build panels, turn and season status, dialogs and notifications, and build a backend that holds authoritative state, runs simulation ticks on End Turn, enforces the game rules, persists sessions and exposes simple JSON APIs for syncing and rejoining.", "website_name": "waitstaff"}
{"first_URL": "https://438083a5-create-a-website-frontend.pages.dev", "second_URL": "https://388158a9-create-a-website-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://62088e1e-create-a-website-frontend.pages.dev", "fifth_URL": "https://54d2dc90-create-a-website-frontend.pages.dev", "prompt": "Create a website that allows users to register and log in, upload pictures and short video clips with captions, and view a reverse‑chronological global feed of all these posts. Include the ability to like and comment on posts and show simple profile pages with a gallery of each user’s posts. Securely handle sessions and store media in your own blob storage with structured data in your own database.", "website_name": "gallery"}
{"first_URL": "https://21db3847-create-a-minimal-frontend.pages.dev", "second_URL": "https://c48578c0-create-a-minimal-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://740628a6-create-a-minimal-frontend.pages.dev", "fifth_URL": "https://fa8c782c-create-a-minimal-frontend.pages.dev", "prompt": "Create a minimal Slack clone with the following features:\n\nEmail/password login (no email verification)\nA shared workspace with multiple channels\nUsers can send and receive real-time messages in each channel\nMessages can include text or images.\nEmojis should be supported.\nSupport threaded replies to individual messages\nMessages should display sender, timestamp, and content\nSimple UI with a channel list and chat view\nMake sure it looks clean and polished.\nCall it BerrySlack, and make the logo a berry.", "website_name": "slack"}
{"first_URL": "https://2981c1e8-calendar-app-create-frontend.pages.dev", "second_URL": "https://51eb5688-calendar-app-create-frontend.pages.dev", "third_URL": "https://41b9c634-calendar-app-create-frontend.pages.dev", "fourth_URL": null, "fifth_URL": "https://72e55af9-calendar-app-create-frontend.pages.dev", "prompt": "Calendar App: Create a calendar app.", "website_name": "calendar"}
{"first_URL": "https://542a4e3b-build-the-vscode-frontend.pages.dev", "second_URL": "https://ac440860-build-the-vscode-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://3d6005cf-build-the-vscode-frontend.pages.dev", "fifth_URL": "https://78776fb2-build-the-vscode-frontend.pages.dev", "prompt": "Build the VSCode IDE clone in the style of Apple HIG, use the image as a reference, make sure you implement all the core features. make sure there is a way to log in etc.\n\nVSCode IDE Clone - MVP Feature Set:\n\nCode Editing Core Syntax highlighting for major languages (JavaScript, Python, HTML/CSS, etc.)\nLine numbers, bracket matching, and auto-indentation\n\nBasic autocompletion (based on file contents and language keywords)\n\nMulti-tab editor support\n\nBasic file tree panel to browse and open project files\n\nFile & Project Management Open/save files locally\nBasic workspace folder support\n\nFile operations: create, delete, rename\n\nRecent file history\n\nSearch & Navigation Global search across open files or project directory\nGo to line / go to file\n\nSimple in-file search with highlighting\n\nTerminal Integration Embedded terminal (basic shell access)\nSimple split view between terminal and editor\n\nThemes & Customization Light & Dark mode support\nFont size, tab size, line spacing customization", "website_name": "slideshow"}
{"first_URL": "https://d64a8fad-banner-user-manageme-frontend.pages.dev", "second_URL": "https://62b258e9-banner-user-manageme-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://dcee2da2-banner-user-manageme-frontend.pages.dev", "fifth_URL": "https://6ea36b0c-banner-user-manageme-frontend.pages.dev", "prompt": "Banner User Management App: Make an app for managing banned users. A ban can be by IP, user, or email domain. It will have a reason, moderator or automod, and an expiration date or 'permanent' for permanent bans.", "website_name": "waitstaff"}
{"first_URL": "https://f9feb77b-b2b-saas-create-frontend.pages.dev", "second_URL": "https://16e76c4f-b2b-saas-create-frontend.pages.dev", "third_URL": "https://717e3ae1-b2b-saas-create-frontend.pages.dev", "fourth_URL": null, "fifth_URL": "https://1750b346-b2b-saas-create-frontend.pages.dev", "prompt": "B2B SaaS: Create a modern B2B SaaS marketing page for my AI Widget Factory", "website_name": "waitstaff"}
{"first_URL": "https://1570f066-atomic-design-calcul-frontend.pages.dev", "second_URL": "https://174190cd-atomic-design-calcul-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://9367182f-atomic-design-calcul-frontend.pages.dev", "fifth_URL": "https://531e29ac-atomic-design-calcul-frontend.pages.dev", "prompt": "Atomic Design Calculator (React): Create a calculator app using the React framework, following atomic design principles.", "website_name": "atomic"}
{"first_URL": "https://f88c1f94-assistant-task-track-frontend.pages.dev", "second_URL": "https://c7bac323-assistant-task-track-frontend.pages.dev", "third_URL": "https://787c245c-assistant-task-track-frontend.pages.dev", "fourth_URL": "https://32b02632-assistant-task-track-frontend.pages.dev", "fifth_URL": null, "prompt": "Assistant Task Tracker: A streamlined app for legal assistants to manage case tasks and track updates. Design a mobile-friendly layout for legal assistants to view tasks, update statuses, and track changes efficiently. As a Legal Assistant, I need to update case files with new information so that records are accurate. As a Legal Assistant, I need to upload and organize case-related documents so that they can be found quickly. As a Legal Assistant, I need to track the status of tasks assigned to me so that I can prioritize my work. As a Legal Assistant, I need to track the history of case changes so that I can see what updates have been made. Include tables for Case File, Case Task, and Task History.", "website_name": "waitstaff"}
{"first_URL": "https://0401f944-anniversarygallery-s-frontend.pages.dev", "second_URL": "https://87e9f70f-anniversarygallery-s-frontend.pages.dev", "third_URL": "https://84e0a99c-anniversarygallery-s-frontend.pages.dev", "fourth_URL": "https://e8ef5ce9-anniversarygallery-s-frontend.pages.dev", "fifth_URL": null, "prompt": "AnniversaryGallery: Show a wrapping card list that takes up the available horizontal width and is vertically scrollable, of employee anniversaries from the table. For each card show the name, team, title, and years, with minimal spacing between the lines of text. All cards should be 200px tall and 150px wide. The text should be appropriately styled to emphasize the name and years of service. Include a photo on top of each card, pull the value from the photo column and give the top half of the card a light blue background.", "website_name": "gallery"}
{"first_URL": "https://dc45eea0-allowance-tracker-cr-frontend.pages.dev", "second_URL": "https://ea61a955-allowance-tracker-cr-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://3de552e8-allowance-tracker-cr-frontend.pages.dev", "fifth_URL": "https://4e6cadbd-allowance-tracker-cr-frontend.pages.dev", "prompt": "Allowance tracker: Create a simple allowance tracker for families. It should optimize for quick transactions and multiple family members.", "website_name": "allowance"}
{"first_URL": "https://91f78ac5-workout-planner-buil-frontend.pages.dev", "second_URL": null, "third_URL": null, "fourth_URL": "https://d047ffb7-workout-planner-buil-frontend.pages.dev", "fifth_URL": "https://6ba5daca-workout-planner-buil-frontend.pages.dev", "prompt": "Workout Planner: Build a workout planner app.", "website_name": "waitstaff"}
{"first_URL": "https://e142ae49-travel-cities-tracke-frontend.pages.dev", "second_URL": "https://1abd0e10-travel-cities-tracke-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://7ec66c3c-travel-cities-tracke-frontend.pages.dev", "fifth_URL": null, "prompt": "Travel Cities Tracker & Reviewer: Create an app to track all the cities I have visited, allowing reviews and star ratings.", "website_name": "waitstaff"}
{"first_URL": "https://dda15d5b-golf-score-tracker-frontend.pages.dev", "second_URL": null, "third_URL": null, "fourth_URL": "https://6e72638b-golf-score-tracker-frontend.pages.dev", "fifth_URL": "https://2c1c0f99-golf-score-tracker-frontend.pages.dev", "prompt": "Golf Score Tracker: Develop a golf score tracker app.", "website_name": "waitstaff"}
{"first_URL": "https://45794921-create-an-online-frontend.pages.dev", "second_URL": null, "third_URL": null, "fourth_URL": "https://275ed12d-create-an-online-frontend.pages.dev", "fifth_URL": "https://f3b58972-create-an-online-frontend.pages.dev", "prompt": "Create an online multiplayer pictionary game\n\nNo login required. Users can create a new game, or join an existing one.\nShow a lobby of existing games.\nUse durable objects for realtime drawing and guessing\nThere must be one player who is the drawer. They get a random word and need to draw it. They must be able to change colors when drawing. All other players guess what they draw by entering guesses in a text box. The first player to guess correctly wins the round. Then the drawer rotates around. First player to 5 points wins\nMake it look fun and colorful. Name the app BerryPictionary and make the logo a berry.", "website_name": "waitstaff"}
{"first_URL": "https://9f572948-create-a-website-frontend.pages.dev", "second_URL": null, "third_URL": null, "fourth_URL": "https://8c3001a2-create-a-website-frontend.pages.dev", "fifth_URL": "https://56fef549-create-a-website-frontend.pages.dev", "prompt": "Create a website that lets anyone pick a display name, then create or join a Texas Hold’em table via a short code with no registration required, choosing player count and betting style as they go and waiting in a lobby until enough people arrive. Build an in‑browser poker interface with a virtual table where cards are dealt in real time, blinds are posted and rotated, chips are tracked, turns are highlighted and you can check/call/bet/raise/fold through the pre‑flop, flop, turn and river, finish with a showdown and award the pot. Include a persistent rules overlay so newcomers can read the objective, flow of play and hand rankings at any time.", "website_name": "slideshow"}
{"first_URL": "https://1d5edbe8-create-a-website-frontend.pages.dev", "second_URL": null, "third_URL": null, "fourth_URL": "https://2ef0c37e-create-a-website-frontend.pages.dev", "fifth_URL": "https://e168cce2-create-a-website-frontend.pages.dev", "prompt": "Create a website that allows users to register and log in to a secure backend and then create, edit, delete and save little web pages in plain HTML to their own personal gallery, with hashed‑password authentication and persistent storage. Build a responsive gallery with rendered thumbnail previews, a full‑screen view mode and simple sharing via unique slug URLs that anyone can open without needing to log in.", "website_name": "gallery"}
{"first_URL": "https://e0799bbb-create-a-minimal-frontend.pages.dev", "second_URL": null, "third_URL": "https://58cac93c-create-a-minimal-frontend.pages.dev", "fourth_URL": "https://f2a02560-create-a-minimal-frontend.pages.dev", "fifth_URL": null, "prompt": "Create a minimal Twitter clone with the following features:\n\nEmail/password login (no email verification)\nUsers can create profiles with a username, profile photo, and description\nUsers can create tweets up to 280 characters and attach up to 4 photos to each tweet\nThere should be a feed with all tweets from all users in chronological order\nUsers can like and comment on tweets\nUsers can view other people's profiles, which shows their profile info and all their tweets\nMake it look clean and polished. Make it have the same color scheme as twitter. Name it BerryTwitter. Create a custom SVG in code for the app icon that looks like Twitter's icon, and use it in the header.\nCall it BerryTwitter, and make the logo a berry.", "website_name": "twitter"}
{"first_URL": "https://48d1456e-amazon-purchase-list-frontend.pages.dev", "second_URL": "https://538ca253-amazon-purchase-list-frontend.pages.dev", "third_URL": null, "fourth_URL": "https://a02bcd60-amazon-purchase-list-frontend.pages.dev", "fifth_URL": null, "prompt": "Amazon Purchase List Replica: Create a replica of the Amazon purchase list.", "website_name": "waitstaff"}
{"first_URL": null, "second_URL": null, "third_URL": "https://6a1d0821-create-a-website-frontend.pages.dev", "fourth_URL": null, "fifth_URL": null, "prompt": "Create a website that allows users to register and log in and then opens a clean distraction‑free text editor with a minimal header and a settings panel for picking background environments, ambient audio loops and starting a countdown timer. Ensure the site saves the work to your own backend, recording session time and word count, storing user preferences in a database, static assets in blob storage and session tokens in a key/value store.", "website_name": "slideshow"}
{"first_URL": null, "second_URL": null, "third_URL": null, "fourth_URL": null, "fifth_URL": null, "prompt": "Developer Portfolio: Create a beautiful, modern, sleek and futuristic developer resume website for an engineer working on GitHub Copilot.", "website_name": "slideshow"}
