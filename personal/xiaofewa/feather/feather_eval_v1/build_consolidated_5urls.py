#!/usr/bin/env python3
"""
Consolidate 5 merged JSONL result files into a single JSONL with exactly one row
per base prompt (expected 104). Prompts are matched across files via normalized
prompt text. Each row contains up to 5 URL fields (first_URL .. fifth_URL) in the
order the files are supplied.

Usage:
  python build_consolidated_5urls.py \
    --files appberry-0424-imagegen-merged.jsonl \
            gpt5d64-noimagegen-merged.jsonl \
            gpt5mini-imagegen_new-merged.jsonl \
            shishito-noimagegen-merged.jsonl \
            gpt5d64-imagegen-merged.jsonl \
    --base feather_v0_long_prompt.jsonl \
    --output feather_consolidated_104.jsonl

If --base is omitted, the first merged file is used as the base (so its prompt
list defines the 104 expected entries).
"""

from __future__ import annotations
import argparse
import json
import re
import sys
from difflib import SequenceMatcher
from pathlib import Path
from typing import Dict, List, Optional, Tuple, DefaultDict
from collections import defaultdict


def log(msg: str):
    print(msg, file=sys.stderr)


def normalize_prompt(text: str) -> str:
    """Strong normalization for stable matching.

    Collapses whitespace, lowercases, converts common unicode punctuation to ASCII,
    strips repeated dashes/underscores, and trims trailing boilerplate reference sections.
    """
    if not text:
        return ''
    # Remove known trailing reference UI section which varies across files
    split_marker = '\n## Reference UI Design'
    if split_marker in text:
        text = text.split(split_marker, 1)[0]
    # Unicode normalization replacements
    replacements = {
        '\u2011': '-', '\u2012': '-', '\u2013': '-', '\u2014': '-', '\u2015': '-',
        '\u202f': ' ', '\u2003': ' ', '\u2002': ' ', '\u2009': ' ', '\u00a0': ' ',
        '\u2018': "'", '\u2019': "'", '\u201c': '"', '\u201d': '"'
    }
    for k, v in replacements.items():
        text = text.replace(k, v)
    # Collapse whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    # Collapse multiple dashes/spaces combos
    text = re.sub(r'[-_]{2,}', '-', text)
    return text.lower()


def fingerprint_prompt(text: str) -> str:
    """Generate an order-insensitive-ish fingerprint based on first 120 alnum chars.

    Removes non-alphanumeric characters, lowercases, then takes first 120 chars.
    Trims trailing reference section similarly.
    """
    if not text:
        return ''
    split_marker = '\n## Reference UI Design'
    if split_marker in text:
        text = text.split(split_marker, 1)[0]
    core = re.sub(r'[^0-9a-zA-Z]+', '', text.lower())
    return core[:120]


def first_line_key(text: str) -> str:
    if not text:
        return ''
    first = text.strip().split('\n', 1)[0].lower()
    return re.sub(r'\s+', ' ', first)[:160]


def extract_frontend_url(obj: dict) -> Optional[str]:
    d = obj.get('deployment') or {}
    urls = d.get('urls') or {}
    url = urls.get('frontend')
    if url:
        return url
    # Fallbacks if frontend missing
    for key in ('app', 'backend', 'api'):
        if urls.get(key):
            return urls[key]
    return None


def load_jsonl(path: Path) -> List[dict]:
    data = []
    with path.open('r', encoding='utf-8') as f:
        for ln, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
            try:
                data.append(json.loads(line))
            except Exception as e:
                log(f"WARN: {path.name}: line {ln} JSON parse error: {e}")
    return data


def choose_best(existing: dict, candidate: dict) -> dict:
    """If duplicate normalized prompt appears, prefer one with deployment status 'completed'."""
    def score(obj):
        status = (obj.get('deployment') or {}).get('status') or ''
        url = extract_frontend_url(obj)
        return (1 if status == 'completed' else 0, 1 if url else 0)
    if existing is None:
        return candidate
    return candidate if score(candidate) > score(existing) else existing


def build_lookup(records: List[dict]) -> Dict[str, dict]:
    """Primary lookup by normalized full prompt."""
    lookup: Dict[str, dict] = {}
    for r in records:
        prompt = r.get('prompt') or ''
        if not prompt:
            continue
        norm = normalize_prompt(prompt)
        if norm not in lookup:
            lookup[norm] = r
        else:
            lookup[norm] = choose_best(lookup[norm], r)
    return lookup


def build_aux_indices(records: List[dict]) -> Tuple[DefaultDict[str, List[dict]], DefaultDict[str, List[dict]]]:
    """Auxiliary indices: fingerprint -> [records], first_line_key -> [records]."""
    fp_index: DefaultDict[str, List[dict]] = defaultdict(list)
    fl_index: DefaultDict[str, List[dict]] = defaultdict(list)
    for r in records:
        p = r.get('prompt') or ''
        if not p:
            continue
        fp_index[fingerprint_prompt(p)].append(r)
        fl_index[first_line_key(p)].append(r)
    return fp_index, fl_index


def fuzzy_resolve(norm_prompt: str, lookups: List[Dict[str, dict]], threshold: float = 0.985) -> List[Tuple[int, dict]]:
    """Attempt fuzzy matching across lookups if exact key is missing in some.

    Returns list of (file_index, record) where a high-similarity match is found.
    Only used for missing matches; keeps exact matches untouched.
    """
    results: List[Tuple[int, dict]] = []
    for idx, lu in enumerate(lookups):
        if norm_prompt in lu:
            continue  # already exact
        best_key = None
        best_score = 0.0
        for k in lu.keys():
            s = SequenceMatcher(None, norm_prompt, k).ratio()
            if s > best_score:
                best_score = s
                best_key = k
        if best_key and best_score >= threshold:
            results.append((idx, lu[best_key]))
    return results


def derive_website_name(prompt: str) -> str:
    p = prompt.lower()
    keywords = [
        ('twitter', 'twitter'), ('slack', 'slack'), ('calendar', 'calendar'),
        ('blackjack', 'blackjack'), ('hacker news', 'hackernews'), ('hackernews', 'hackernews'),
        ('maintenance', 'maintenance'), ('slides', 'slideshow'), ('slideshow', 'slideshow'),
        ('supercharger', 'supercharger'), ('tesla', 'supercharger'), ('cipher', 'cipher'),
        ('base64', 'base64'), ('gallery', 'gallery'), ('waitlist', 'waitstaff'),
        ('table', 'waitstaff'), ('leaderboard', 'leaderboard')
    ]
    for needle, name in keywords:
        if needle in p:
            return name
    # Fallback: first word >3 alnum chars
    for w in re.split(r'\s+', prompt):
        cw = re.sub(r'[^\w]', '', w)
        if len(cw) > 3:
            return cw.lower()
    return 'unknown'


def main():
    parser = argparse.ArgumentParser(description="Consolidate 5 merged JSONL files into 104-row unified JSONL.")
    parser.add_argument('--files', nargs=5, required=True, metavar='MERGED_JSONL', help='Exactly 5 merged jsonl files (order maps to first..fifth_URL)')
    parser.add_argument('--base', help='Base JSONL file containing the canonical 104 prompts. If omitted, first merged file is base.')
    parser.add_argument('--output', required=True, help='Output JSONL path')
    parser.add_argument('--allow-fuzzy', action='store_true', help='Enable fuzzy fallback for missing prompt matches (high threshold)')
    parser.add_argument('--union', action='store_true', help='Use union of prompts across all files instead of limiting to base (ensures every link represented).')
    args = parser.parse_args()

    file_paths = [Path(p) for p in args.files]
    for p in file_paths:
        if not p.is_file():
            log(f"ERROR: File not found: {p}")
            return 2

    base_path = Path(args.base) if args.base else file_paths[0]
    if not base_path.is_file():
        log(f"ERROR: Base file not found: {base_path}")
        return 2

    if not args.union:
        log(f"Loading base prompts from: {base_path}")
        base_records = load_jsonl(base_path)
        # Build ordered list of (original_prompt, normalized)
        seen_norm = set()
        base_prompts: List[Tuple[str, str]] = []
        for r in base_records:
            prompt = r.get('prompt') or ''
            norm = normalize_prompt(prompt)
            if not norm or norm in seen_norm:
                continue
            seen_norm.add(norm)
            base_prompts.append((prompt, norm))

        log(f"Base unique prompts: {len(base_prompts)}")
        if len(base_prompts) != 104:
            log(f"WARN: Expected 104 base prompts, got {len(base_prompts)}")
    else:
        base_prompts = []  # will fill after loading all files

    # Load merged files & build lookups
    lookups: List[Dict[str, dict]] = []
    aux_fp: List[DefaultDict[str, List[dict]]] = []
    aux_fl: List[DefaultDict[str, List[dict]]] = []
    file_stats = []
    all_records_for_union: List[dict] = []
    for p in file_paths:
        recs = load_jsonl(p)
        lu = build_lookup(recs)
        fp_idx, fl_idx = build_aux_indices(recs)
        lookups.append(lu)
        aux_fp.append(fp_idx)
        aux_fl.append(fl_idx)
        file_stats.append((p.name, len(recs), len(lu)))
        if args.union:
            all_records_for_union.extend(recs)

    for name, raw_count, unique_count in file_stats:
        log(f"Loaded {name}: {raw_count} lines -> {unique_count} unique prompts")

    # If union mode, derive union prompt set
    if args.union:
        log("Deriving union of prompts across all files (union mode)...")
        union_map: Dict[str, str] = {}
        for r in all_records_for_union:
            ptxt = r.get('prompt') or ''
            if not ptxt:
                continue
            norm = normalize_prompt(ptxt)
            if norm and norm not in union_map:
                union_map[norm] = ptxt
        base_prompts = [(orig, norm) for norm, orig in ((n, union_map[n]) for n in union_map)]
        # Preserve deterministic order (sort by first line)
        base_prompts.sort(key=lambda t: t[0].split('\n',1)[0][:120].lower())
        log(f"Union prompts: {len(base_prompts)} (will output this many rows)")

    # Build consolidated rows
    output_rows = []
    missing_counts = [0]*5  # exact normalized misses
    recovered_by_fp = [0]*5
    recovered_by_fl = [0]*5
    fuzzy_matches_used = 0

    for original_prompt, norm in base_prompts:
        urls = [None]*5
        fp_key = fingerprint_prompt(original_prompt)
        fl_key = first_line_key(original_prompt)

        for idx, lu in enumerate(lookups):
            rec = lu.get(norm)
            if rec is None:
                missing_counts[idx] += 1
            else:
                urls[idx] = extract_frontend_url(rec)

        # Auxiliary recovery (fingerprint)
        for idx in range(len(lookups)):
            if urls[idx] is None and fp_key in aux_fp[idx]:
                # choose best candidate among fingerprint matches
                cand = aux_fp[idx][fp_key][0]
                urls[idx] = extract_frontend_url(cand)
                if urls[idx]:
                    recovered_by_fp[idx] += 1

        # Auxiliary recovery (first line)
        for idx in range(len(lookups)):
            if urls[idx] is None and fl_key in aux_fl[idx]:
                cand = aux_fl[idx][fl_key][0]
                urls[idx] = extract_frontend_url(cand)
                if urls[idx]:
                    recovered_by_fl[idx] += 1

        # Fuzzy fallback only for positions still missing and if enabled
        if args.allow_fuzzy and any(u is None for u in urls):
            fuzzy_found = fuzzy_resolve(norm, lookups)
            for idx, rec in fuzzy_found:
                if urls[idx] is None:  # don't overwrite existing exact
                    urls[idx] = extract_frontend_url(rec)
                    fuzzy_matches_used += 1

        row = {
            'first_URL': urls[0],
            'second_URL': urls[1],
            'third_URL': urls[2],
            'fourth_URL': urls[3],
            'fifth_URL': urls[4],
            'prompt': original_prompt,
            'website_name': derive_website_name(original_prompt)
        }
        output_rows.append(row)

    log("Building statistics...")
    # Stats
    def url_count(r):
        return sum(1 for k in ('first_URL','second_URL','third_URL','fourth_URL','fifth_URL') if r[k])
    distribution = {}
    for r in output_rows:
        c = url_count(r)
        distribution[c] = distribution.get(c, 0) + 1

    # Write output preserving base order
    out_path = Path(args.output)
    with out_path.open('w', encoding='utf-8') as f:
        for r in output_rows:
            f.write(json.dumps(r, ensure_ascii=False) + '\n')

    log(f"Wrote {len(output_rows)} rows to {out_path}")
    log("URL count distribution (rows with N URLs):")
    for n in sorted(distribution):
        log(f"  {n}: {distribution[n]}")
    log("Missing counts per file index (exact misses before aux recovery):")
    for i, m in enumerate(missing_counts):
        log(f"  File {i+1}: {m} exact misses | fp_recovered={recovered_by_fp[i]} | fl_recovered={recovered_by_fl[i]}")
    if args.allow_fuzzy:
        log(f"Fuzzy matches adopted: {fuzzy_matches_used}")

    # Emit first 3 examples to stdout (not stderr) for quick glance
    print("First 3 rows (preview):")
    for r in output_rows[:3]:
        print(json.dumps(r, ensure_ascii=False)[:300] + ('...' if len(json.dumps(r, ensure_ascii=False))>300 else ''))

    if not args.union and len(output_rows) != 104:
        log("NOTE: Output row count != 104; verify your base file.")

    # Coverage check: ensure every input URL appears at least once
    input_urls = set()
    for p in file_paths:
        for rec in load_jsonl(p):
            url = extract_frontend_url(rec)
            if url:
                input_urls.add(url)
    output_urls = set()
    for r in output_rows:
        for k in ('first_URL','second_URL','third_URL','fourth_URL','fifth_URL'):
            if r.get(k):
                output_urls.add(r[k])
    missing = input_urls - output_urls
    log(f"Input frontend URLs: {len(input_urls)} unique | Output URLs present: {len(output_urls)}")
    if missing:
        log(f"WARNING: {len(missing)} input URLs missing from output (union={args.union}). Writing missing URLs list to stderr sample (first 5):")
        for i, m in enumerate(list(missing)[:5]):
            log(f"  MISSING[{i}]: {m}")
    else:
        log("All input frontend URLs are represented in output.")

    return 0


if __name__ == '__main__':
    raise SystemExit(main())
