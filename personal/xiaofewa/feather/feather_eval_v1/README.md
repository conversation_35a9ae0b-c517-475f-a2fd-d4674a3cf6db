# Feather Consolidated Dataset (104 Full Extended)

This folder contains the finalized consolidated evaluation dataset:

- **Primary file:** `feather_consolidated_104_full_extended.jsonl`
  - 104 prompts (base prompt set)
  - 5 URL columns (`first_URL` ... `fifth_URL`)
  - Rows ordered by completeness (first those with 5 URLs)

## Source Inputs (Merged JSONL Files)
The 5 upstream model result files whose frontend deployment URLs were merged:

| Column in Consolidated | Source File | Notes |
|------------------------|-------------|-------|
| `first_URL`            | `appberry-0424-imagegen-merged.jsonl`          | Image-enabled variant |
| `second_URL`           | `gpt5d64-noimagegen-merged.jsonl`              | Non image-gen variant |
| `third_URL`            | `gpt5mini-imagegen_new-merged.jsonl`           | Mini image-gen new build |
| `fourth_URL`           | `shishito-noimagegen-merged.jsonl`             | Shishito non image-gen |
| `fifth_URL`            | `gpt5d64-imagegen-merged.jsonl`                | Image-gen variant |

Each column consistently maps to exactly ONE source file (verified; no column mixes sources). All five columns map to distinct sources.

## Validation Summary
All validation checks were executed against the final file.

- Unique frontend URLs across the 5 source merged files: **459**
- Non-null URL cells in consolidated file: **459**
- Unique URLs in consolidated file: **459**
- Missing URLs (present in sources but absent in consolidated): **0**
- Extra URLs (appear in consolidated but not in sources): **0**
- Duplicate URLs in consolidated (appearing more than once): **0**

Result: Full 1:1 coverage with perfect uniqueness.

## Usage
Consume `feather_consolidated_104_full_extended.jsonl` for evaluation; rely on this README for provenance and integrity guarantees.
