#!/usr/bin/env python3
"""
Convert CSV file to JSONL format for feather evaluation data.

Input: CSV file with columns EXPERIMENT_ID, SAMPLE_ID, PROMPT
Output: JSONL file with format matching feather_v0_short_prompt.jsonl
"""

import csv
import json
import argparse
import sys
from pathlib import Path


def convert_csv_to_jsonl(input_csv_path, output_jsonl_path):
    """
    Convert CSV file to JSONL format.
    
    Args:
        input_csv_path (str): Path to input CSV file
        output_jsonl_path (str): Path to output JSONL file
    """
    
    try:
        with open(input_csv_path, 'r', encoding='utf-8') as csv_file:
            # Use csv.DictReader to handle quoted fields properly
            csv_reader = csv.DictReader(csv_file)
            
            with open(output_jsonl_path, 'w', encoding='utf-8') as jsonl_file:
                for row in csv_reader:
                    # Create JSON object matching the target format
                    json_obj = {
                        "experiment_id": row["EXPERIMENT_ID"],
                        "sample_id": row["SAMPLE_ID"], 
                        "is_sample_correct": "unknown",
                        "prompt": row["PROMPT"],
                        "cua_graded_rubric": {}
                    }
                    
                    # Write JSON object as single line
                    jsonl_file.write(json.dumps(json_obj, ensure_ascii=False) + '\n')
                    
        print(f"Successfully converted {input_csv_path} to {output_jsonl_path}")
        
    except FileNotFoundError:
        print(f"Error: Input file {input_csv_path} not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error during conversion: {str(e)}")
        sys.exit(1)


def main():
    """Main function to handle command line arguments and execute conversion."""
    
    parser = argparse.ArgumentParser(
        description="Convert CSV file to JSONL format for feather evaluation data"
    )
    
    parser.add_argument(
        "input_csv", 
        help="Path to input CSV file"
    )
    
    parser.add_argument(
        "-o", "--output",
        help="Path to output JSONL file (optional, defaults to input filename with .jsonl extension)"
    )
    
    args = parser.parse_args()
    
    # Determine output filename
    if args.output:
        output_path = args.output
    else:
        input_path = Path(args.input_csv)
        output_path = input_path.with_suffix('.jsonl')
    
    # Perform conversion
    convert_csv_to_jsonl(args.input_csv, output_path)


if __name__ == "__main__":
    main()
