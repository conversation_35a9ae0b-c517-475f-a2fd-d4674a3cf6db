#!/usr/bin/env bash
set -euo pipefail

############################################################
# Script to create PR with dynamically generated JSON content
# Usage: ./publish.sh <snapshot_name> <openai_user> <snapshot>
# Example: ./publish.sh swe-vsc-m15-s200-0724-3 lixiaoli snapshot.51cc18a442289f303f63a8978a74514879ffd9c4c69fd89f731a184f92fe8a63.json
############################################################

# Check if correct number of arguments provided
if [ $# -ne 3 ]; then
    echo "❌ Error: Expected 3 arguments"
    echo "Usage: $0 <snapshot_name> <openai_user> <snapshot>"
    echo "Example: $0 swe-vsc-m15-s200-0724-3 lixiaoli snapshot.51cc18a442289f303f63a8978a74514879ffd9c4c69fd89f731a184f92fe8a63.json"
    exit 1
fi

# Parse command line arguments
SNAPSHOT_NAME="$1"
OPENAI_USER="$2"
SNAPSHOT="$3"

# Extract snapshot_id from snapshot filename (remove "snapshot." prefix and ".json" suffix)
SNAPSHOT_ID="${SNAPSHOT#snapshot.}"
SNAPSHOT_ID="${SNAPSHOT_ID%.json}"

ORG="project-argos"
PROJECT="Mumford"
REPO="oai-engine-configs"

BASE_BRANCH=${BASE_BRANCH:-main}
NEW_BRANCH=${NEW_BRANCH:-add-${SNAPSHOT_NAME}-$(date +%s)}

############################################################
# Step 0: Get Azure DevOps Bearer Token (valid for 1 hour)
# Note: 499b84ac-1321-427f-aa17-267ca6975798 is the Azure DevOps
#       resource ID in Entra ID.
############################################################
TOKEN=$(az account get-access-token \
          --resource 499b84ac-1321-427f-aa17-267ca6975798 \
          --query accessToken -o tsv)

API="https://dev.azure.com/${ORG}/${PROJECT}/_apis"

############################################################
# Step 1: Get current HEAD commit ID of target branch
############################################################
API_RESPONSE=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  "${API}/git/repositories/${REPO}/refs/heads/${BASE_BRANCH}?api-version=7.1")

HEAD=$(echo "$API_RESPONSE" | jq -r '.value[0].objectId // empty')

echo "Base branch HEAD: $HEAD"

# Check if HEAD commit ID was successfully retrieved
if [ "$HEAD" = "null" ] || [ -z "$HEAD" ]; then
  echo "❌ Error: Unable to get HEAD commit ID for branch '${BASE_BRANCH}'"
  
  # Check if it's an authentication/authorization error
  ERROR_MESSAGE=$(echo "$API_RESPONSE" | jq -r '.message // empty')
  if [[ "$ERROR_MESSAGE" == *"not authorized"* ]] || [[ "$ERROR_MESSAGE" == *"TF400813"* ]]; then
    echo ""
    echo "🔍 Authentication/Authorization Error Detected:"
    echo "   Message: $ERROR_MESSAGE"
    
    # Get current user info to display user-friendly account name
    CURRENT_USER=$(az account show --query 'user.name' -o tsv 2>/dev/null || echo "unknown")
    echo "   Current account: $CURRENT_USER"
    echo ""
    echo "💡 Required solution:"
    echo "   You must log in with your Microsoft corporate account (<EMAIL>):"
    echo "      az login"
    echo ""
    echo "📋 Additional info:"
    echo "   Target Azure DevOps project:"
    echo "   • Organization: ${ORG}"
    echo "   • Project: ${PROJECT}"
    echo "   • Repository: ${REPO}"
  else
    echo "Please check if the branch name is correct or if the branch exists"
    if [ -n "$ERROR_MESSAGE" ]; then
      echo "API Error: $ERROR_MESSAGE"
    fi
  fi
  exit 1
fi

############################################################
# Step 2: Create new branch and commit JSON file in one push
############################################################
FILE_PATH="/models/ledgers/${SNAPSHOT_NAME}.json"

# Generate JSON content dynamically
JSON_CONTENT=$(cat <<EOF
{
  "snapshot_name": "${SNAPSHOT_NAME}",
  "snapshot_id": "${SNAPSHOT_ID}",
  "snapshot_dir": "${OPENAI_USER}/${SNAPSHOT_NAME}",
  "snapshots": {
    "msft": "${SNAPSHOT}"
  },
  "description": "Test onboarding ${SNAPSHOT_NAME} model from ledger",
  "msft_tent": "orange",
  "asset_type": "IXF",
  "registry": {
    "registry_name": "openai-devault",
    "registry_primary_region": "centralus",
    "resource_group": "registry-openai-devault",
    "subscription_id": "699a8a35-07fd-475c-8586-4a145fd6a011"
  }
}
EOF
)

FILE_CONTENT=$(echo "$JSON_CONTENT" | jq -Rs .)     # Convert to JSON string

PUSH_RESULT=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -X POST "${API}/git/repositories/${REPO}/pushes?api-version=7.1" \
  -d @- <<EOF
{
  "refUpdates": [
    { "name": "refs/heads/${NEW_BRANCH}", "oldObjectId": "${HEAD}" }
  ],
  "commits": [
    {
      "comment": "Add ${SNAPSHOT_NAME} ledger",
      "changes": [
        {
          "changeType": "add",
          "item": { "path": "${FILE_PATH}" },
          "newContent": {
            "content": ${FILE_CONTENT},
            "contentType": "rawtext"
          }
        }
      ]
    }
  ]
}
EOF
)

# Check if push operation was successful
if echo "$PUSH_RESULT" | jq -e '.message' > /dev/null 2>&1; then
  echo "❌ Push operation failed:"
  echo "$PUSH_RESULT" | jq -r '.message'
  exit 1
fi

echo "✅ Pushed ${FILE_PATH} to ${NEW_BRANCH}"

############################################################
# Step 3: Create Pull Request
############################################################
PR_JSON=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -X POST "${API}/git/repositories/${REPO}/pullrequests?api-version=7.1" \
  -d @- <<EOF
{
  "sourceRefName": "refs/heads/${NEW_BRANCH}",
  "targetRefName": "refs/heads/${BASE_BRANCH}",
  "title": "Add ${SNAPSHOT_NAME} ledger",
  "description": "Auto-generated via script",
  "completionOptions": {
    "deleteSourceBranch": true
  }
}
EOF)

# Check if PR creation was successful
if echo "$PR_JSON" | jq -e '.message' > /dev/null 2>&1; then
  echo "❌ Failed to create Pull Request:"
  echo "$PR_JSON" | jq -r '.message'
  exit 1
fi

PR_ID=$(echo "$PR_JSON" | jq -r '.pullRequestId')

# Check if PR information was successfully retrieved
if [ "$PR_ID" = "null" ] || [ -z "$PR_ID" ]; then
  echo "❌ Unable to get Pull Request ID, creation may have failed"
  echo "API response:"
  echo "$PR_JSON" | jq .
  exit 1
fi

echo "✅ Pull Request #${PR_ID} created successfully!"
echo "🔗 https://dev.azure.com/${ORG}/${PROJECT}/_git/${REPO}/pullrequest/${PR_ID}"

############################################################
# Step 4: Link PR to work item
############################################################

WORK_ITEM_ID=31416

PROJECT_ID=$(echo "$PR_JSON" | jq -r '.repository.project.id')
REPO_ID=$(echo "$PR_JSON"    | jq -r '.repository.id')
PR_ID=$(echo "$PR_JSON"      | jq -r '.pullRequestId')

PR_ARTIFACT="vstfs:///Git/PullRequestId/${PROJECT_ID}%2F${REPO_ID}%2F${PR_ID}"

LINK_RESULT=$(curl -s \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json-patch+json" \
  -X PATCH \
  "https://dev.azure.com/${ORG}/${PROJECT}/_apis/wit/workitems/${WORK_ITEM_ID}?api-version=7.1" \
  -d @- <<EOF
[
  {
    "op": "add",
    "path": "/relations/-",
    "value": {
      "rel": "ArtifactLink",
      "url": "${PR_ARTIFACT}",
      "attributes": {
        "name": "Pull Request",        
        "comment": "Auto-link PR #${PR_ID}"
      }
    }
  }
]
EOF
)

if echo "$LINK_RESULT" | jq -e '.relations[]? | select(.url=="'${PR_ARTIFACT}'")' >/dev/null; then
  echo "✅ Work Item ${WORK_ITEM_ID} linked to PR #${PR_ID}"
else
  echo "❌ Failed to link Work Item — response:"
  echo "$LINK_RESULT" | jq .
  exit 1
fi
