#!/usr/bin/env bash

# Parse command line arguments
MODEL_NAME=""
FALCON_PATH=""
EXPORT_DIR=""
OVERRIDE=""
NEW_MODEL="false"

while [[ $# -gt 0 ]]; do
  case $1 in
    --model_name=*)
      MODEL_NAME="${1#*=}"
      shift
      ;;
    --falcon_path=*)
      FALCON_PATH="${1#*=}"
      shift
      ;;
    --export_dir=*)
      EXPORT_DIR="${1#*=}"
      shift
      ;;
    --override)
      shift
      OVERRIDE="$1"
      shift
      ;;
    --new_model)
      NEW_MODEL="true"
      shift
      ;;
    --new_model=*)
      NEW_MODEL="${1#*=}"
      shift
      ;;
    *)
      echo "Unknown parameter: $1"
      exit 1
      ;;
  esac
done

# Validate required parameters
if [[ -z "$MODEL_NAME" || -z "$FALCON_PATH" || -z "$EXPORT_DIR" ]]; then
  echo "Usage: $0 --model_name=<model_name> --falcon_path=<falcon_path> --export_dir=<export_dir> [--override \"<override_string>\"] [--new_model]"
  echo "Example: $0 --model_name=\"falcon.multimodal.runs.scallion-d36-s64-lpe\" --falcon_path=\"az://orngcresco/path/to/tc/weights\" --export_dir=\"export-dir\" --override \"model=falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False\""
  exit 1
fi

echo "MODEL_NAME: $MODEL_NAME"
echo "FALCON_PATH: $FALCON_PATH"
echo "EXPORT_DIR: $EXPORT_DIR"
echo "OVERRIDE: $OVERRIDE"

# Build the command array
CMD_ARGS=(
  "python"
  "/root/code/glass/personal/lixiaoli/mopub/prepare_snapshot.py"
  "--model_name=$MODEL_NAME"
  "--falcon_path=$FALCON_PATH"
  "--export_dir=$EXPORT_DIR"
)

# Add override parameters if provided
if [[ -n "$OVERRIDE" ]]; then
  # Add --override with the complete override string as a single argument
  CMD_ARGS+=("--override" "$OVERRIDE")
fi

# Add new_model parameter if provided
if [[ "$NEW_MODEL" == "true" ]]; then
  CMD_ARGS+=("--new_model" "True")
fi

{
  echo "Running command: ${CMD_ARGS[*]}"
  "${CMD_ARGS[@]}"
} > prepare_snapshot.log 2>&1

python /root/code/glass/personal/lixiaoli/mopub/notify.py \
  "$(tail -n 1 prepare_snapshot.log)"
