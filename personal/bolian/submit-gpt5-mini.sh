for i in 7 8 9 15; do
    python cloud-bus-shooter.py \
        --quota-team "team-bus" \
        --engine "start-bus-gpt5-mini.sh" \
        --user "swe-main-run" \
        --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
        --start_num 1 --end_num 150 \
        --num_pods 1 \
        --nowait \
        --cluster "prod-uksouth-${i}"
done

for i in 2 3 4 5; do
    python cloud-bus-shooter.py \
        --quota-team "team-bus" \
        --engine "start-bus-gpt5-mini.sh" \
        --user "swe-main-run" \
        --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
        --start_num 1 --end_num 150 \
        --num_pods 1 \
        --nowait \
        --cluster "prod-southcentralus-hpe-${i}"
done

python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "start-bus-gpt5-mini.sh" \
    --user "swe-main-run" \
    --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
    --start_num 1 --end_num 10 \
    --num_pods 1 \
    --nowait \
    --cluster stage-southcentralus-hpe-1
    
 python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "start-bus-gpt5-mini.sh" \
    --user "swe-main-run" \
    --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
    --start_num 1 --end_num 10 \
    --num_pods 1 \
    --nowait \
    --cluster prod-eastus2-30

python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "start-bus-gpt5-mini.sh" \
    --user "swe-main-run" \
    --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
    --start_num 1 --end_num 150 \
    --num_pods 1 \
    --nowait \
    --cluster prod-westus2-19   


python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "start-bus-gpt5-mini.sh" \
    --user "swe-main-run" \
    --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
    --start_num 1 --end_num 150 \
    --num_pods 1 \
    --nowait \
    --cluster  prod-westus2-cw-8

python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "start-bus-gpt5-mini.sh" \
    --user "swe-main-run" \
    --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
    --start_num 1 --end_num 150 \
    --num_pods 1 \
    --nowait \
    --cluster  prod-eastus2-36 

python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "start-bus-gpt5-mini.sh" \
    --user "swe-main-run" \
    --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
    --start_num 1 --end_num 150 \
    --num_pods 1 \
    --nowait \
    --cluster  prod-australiaeast-8 

python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "start-bus-gpt5-mini-gb200.sh" \
    --user "swe-main-run" \
    --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
    --start_num 1 --end_num 20 \
    --num_pods 1 \
    --num_gpu 4 \
    --use_beam True \
    --target_sku gpu11 \
    --add_nvlink_host_mount True \
    --nowait \
    --cluster prod-westus3-22
    
python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "start-bus-gpt5-mini-gb200.sh" \
    --user "swe-main-run" \
    --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
    --start_num 1 --end_num 20 \
    --num_pods 1 \
    --num_gpu 4 \
    --use_beam True \
    --target_sku gpu11 \
    --add_nvlink_host_mount True \
    --nowait \
    --cluster prod-westus3-23
        