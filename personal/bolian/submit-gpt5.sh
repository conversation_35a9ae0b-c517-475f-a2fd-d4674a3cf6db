python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "./start-bus-gpt5-gb200.sh" \
    --user "gpt-oss-rkld" \
    --snapshot_path "az://orngcresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas" \
    --start_num 1 --end_num 9 \
    --num_pods 2 \
    --num_gpu 4 \
    --use_beam True \
    --target_sku gpu11 \
    --add_nvlink_host_mount True \
    --nowait \
    --cluster prod-westus3-22

python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "./start-bus-gpt5-gb200.sh" \
    --user "gpt-oss-rkld" \
    --snapshot_path "az://orngcresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas" \
    --start_num 1 --end_num 9 \
    --num_pods 2 \
    --num_gpu 4 \
    --use_beam True \
    --target_sku gpu11 \
    --add_nvlink_host_mount True \
    --nowait \
    --cluster prod-westus3-23    


python cloud-bus-shooter.py \
    --quota-team "team-bus" \
    --engine "start-bus-gpt5.sh" \
    --user "gpt-oss-rkld" \
    --snapshot_path "az://orngcresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas" \
    --start_num 1 --end_num 150 \
    --num_pods 1 \
    --nowait \
    --cluster  prod-eastus2-36     