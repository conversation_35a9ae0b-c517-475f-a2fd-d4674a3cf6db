SNAPSHOT_PATH=$1
USER_OR_TOPIC=$2

# ev2
RUST_BACKTRACE=1 nohup python -m harmony_scripts.engines.start_engine \
--name=gpt5-engine \
--mode=optimal \
--model_config=falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal \
--snapshot_path=$SNAPSHOT_PATH \
--is_multimodal=False \
--gpu_kind=H100 \
--renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2" \
--restartable \
--extra_config_string="tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={(0,200166):0} allow_embedding_prefix_loading=True ixf_max_cache_list_length=4096" \
--cluster=local \
--bus_enable_qos=True \
--bus_topic_mode_or_user=$USER_OR_TOPIC \
--n_replicas=1 \
--bus_rate_limiter=KV_UTIL \
--enable_healthcheck=True \
--use_bus_v2=True 
