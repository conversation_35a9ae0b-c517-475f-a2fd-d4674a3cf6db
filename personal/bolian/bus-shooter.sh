#!/bin/bash
set -e

while [[ $# -gt 0 ]]; do
    case "$1" in
        --engine)
            engine="$2"
            shift 2
            ;;
        --user)
            user="$2"
            shift 2
            ;;
        --snapshot_path)
            snapshot_path="$2"
            shift 2
            ;;
        --base_job_name)
            base_job_name="$2"
            shift 2
            ;;
        --start_num)
            start_num="$2"
            shift 2
            ;;
        --end_num)
            end_num="$2"
            shift 2
            ;;
        --cluster)
            cluster="$2"
            shift 2
            ;;
        --priority_class)
            priority_class="$2"
            shift 2
            ;;
        --coasting_dog_delay)
            coasting_dog_delay="$2"
            shift 2
            ;;
        --num_pods)
            num_pods="$2"
            shift 2
            ;;
        --num_gpu)
            num_gpu="$2"
            shift 2
            ;;
        --use_beam)
            use_beam="$2"
            shift 2
            ;;
        --target_sku)
            target_sku="$2"
            shift 2
            ;;
        --add_nvlink_host_mount)
            add_nvlink_host_mount="$2"
            shift 2
            ;;
        --help|-h)
            echo "Usage: $0 [--engine <script>] [--user <user>] [--model_path <path>]
            [--base_job_name <name>] [--start_num <num>] [--end_num <num>]
            [--cluster <cluster>] [--priority_class <class>]
            [--coasting_dog_delay <delay>] [--num_pods <pods>] [--num_gpu <gpus>]"
            echo "Default values:"
            echo "  engine: start-bus-o3.sh"
            echo "  user: msft"
            echo "  snapshot_path: az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted"
            echo "  base_job_name: bus-rm"
            echo "  start_num: 1"
            echo "  end_num: 1"
            echo "  cluster: (not set)"
            echo "  priority_class: low-priority"
            echo "  coasting_dog_delay: 3600"
            echo "  num_pods: 2"
            echo "  num_gpu: 8"
            exit 0
            ;;
        *)
            echo "Unknown argument: $1"
            exit 1
            ;;
    esac
done

# Set defaults if not provided
# engine="${engine:-start-bus-o3.sh}"
# snapshot_path="${snapshot_path:-az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted}"
# user="${user:-msft}"
# base_job_name="${base_job_name:-bus-rm}"
start_num="${start_num:-1}"
end_num="${end_num:-1}"
cluster="${cluster:-$BRIX_CLUSTER}"
priority_class="${priority_class:-low-priority}"
coasting_dog_delay="${coasting_dog_delay:-3600}"
num_pods="${num_pods:-2}"
num_gpu="${num_gpu:-8}"
use_beam="${use_beam:-False}"
target_sku="${target_sku:-gpu1}"
add_nvlink_host_mount="${add_nvlink_host_mount:-False}"

topic=$(python -c "from bus.utils.topics import get_redis_topic; print(get_redis_topic(snapshot_path='$snapshot_path', topic_mode_or_user='$user'))")

engine_script=$(base64 -w0 $engine)

wait_ray=$(echo '
CLUSTER_FILE="/tmp/ray/ray_current_cluster"

# Wait for the file to appear
echo "Waiting for $CLUSTER_FILE to appear..."
while [ ! -f "$CLUSTER_FILE" ]; do
  sleep 1
done

# Read IP and port
read -r addr < "$CLUSTER_FILE"
host=${addr%%:*}
port=${addr##*:}

echo "File found: $addr"
echo "Waiting for $host:$port to become available..."

# Wait until the port is reachable
while ! nc -z "$host" "$port" 2>/dev/null; do
  sleep 1
done

echo "Connected to $host:$port"
' | base64 -w0)


wait_beam=$(echo '
until beam wait; do
echo "beam wait failed, retrying in 10s..."
sleep 10
done
' | base64 -w0)

init_script="
#!/bin/bash
set -ex
"

wait_script=$wait_ray

if [ "$use_beam" = "True" ]; then
    wait_script=$wait_beam
fi

trap_script=$(echo '
trap "echo 1 > /tmp/watchdogs/bus_exit" EXIT
' | base64 -w0)

init_script+='
case "$HOSTNAME" in
'
init_script+="
*-0)
echo \"yes\" > ~/is_head_pod.txt
oaipkg install bus > /root/bus_install.log 2>&1

export OPENAI_API_KEY="dummy_key"
export TWDEV_LAUNCH_CACHE_SVC_ENGINE=1 && brix run --env TWDEV_LAUNCH_CACHE_SVC_ENGINE=1 --env CACHE_SVC_NAMESPACE=orange --env CACHE_SVC_ENABLE_ENCRYPTION=0 -- python /root/code/openai/torchflow/run-torchflow-setup.py > cache_svc.log 2>&1
export BUS_CLOSE_IDLE_CONNECTION_THRESHOLD_MINUTES=2

echo '' >> /tmp/start-engine.sh

echo $trap_script | base64 -d >>  /tmp/start-engine.sh

echo $wait_script | base64 -d >>  /tmp/start-engine.sh

echo $engine_script | base64 -d >> /tmp/start-engine.sh
chmod +x /tmp/start-engine.sh
nohup /tmp/start-engine.sh $snapshot_path $user >> /root/bus.log 2>&1 &
"
init_script+='
ln -s /root/bus.log $RCALL_LOGDIR/rcall-output/bus.$HOSTNAME.$(date '+%Y-%m-%d_%H-%M-%S').txt
'

coastingdog_script=$(base64 -w0 coasting-dog.sh)

init_script+="
echo $coastingdog_script | base64 -d > /tmp/coastingdog.sh
chmod +x /tmp/coastingdog.sh
nohup /tmp/coastingdog.sh ${topic} $coasting_dog_delay >> /root/coastingdog.log 2>&1 &
"

init_script+='
ln -s /root/coastingdog.log $RCALL_LOGDIR/rcall-output/coastingdog.$HOSTNAME.$(date '+%Y-%m-%d_%H-%M-%S').txt
ln -s /var/log/api-services.log $RCALL_LOGDIR/rcall-output/api-services.$HOSTNAME.$(date '+%Y-%m-%d_%H-%M-%S').txt
'

init_script+='
;;
*)
echo \"no\" > ~/is_head_pod.txt
;;
esac

ln -s /tmp/tw $RCALL_LOGDIR/rcall-output/tw.$HOSTNAME.$(date '+%Y-%m-%d_%H-%M-%S')
'

tempfile=$(mktemp --suffix .py)
echo "EXTRA_SETUP += \"\"\"${init_script}\"\"\""  > "$tempfile"

echo "Creating jobs $base_job_name $engine with $snapshot_path, user=$user from $start_num to $end_num"

# Loop through and create multiple jobs with different suffixes
for i in $(seq $start_num $end_num); do
    job_name="${base_job_name}${i}"
    echo "Creating job: $job_name"
    
    OAIPKG_OVERRIDE_WHEEL=unsafe_skip \
    twdev create-ray-devbox \
    ${cluster:+cluster=$cluster} \
    num_pods=$num_pods \
    setup_twapi=True \
    use_beam=$use_beam \
    num_gpu=$num_gpu \
    target_sku=$target_sku \
    use_infiniband=True \
    add_nvlink_host_mount=$add_nvlink_host_mount \
    job_name=$job_name \
    priority_class=$priority_class \
    extra_rcall_configs=$tempfile
    
    echo "Job $job_name submitted"
    # Optional: add a small delay between job submissions
    sleep 5
done

