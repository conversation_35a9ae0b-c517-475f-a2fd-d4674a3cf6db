# export TEACHER_CKPT=az://orngscuscresco/models/snapshots/sweberry-v3-tbv3-20250304-decrypted
# student 4.1mini: az://orngscuscresco/models/haoranxu/haoranxu-4.1mini-midtrained-glansfted-decrypted/
# student o4-mini az://orngscuscresco/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted/
export TEACHER_CKPT_0=az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted
export USER_0=haoranxu
export TEACHER_CKPT_1=az://orngscuscresco/models/snapshots/4o-tt-v5-transferred-2025-04-09-decrypted
export USER_1=codechat
export STUDENT=az://orngscuscresco/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted/
LR=1e-5
N_CTX=272144
BZ=512
N_SAMPLE=1

oaipkg run --beam qstar.run_experiment nostrict \
    name=nv4-fkld-mix8-flight-5-o30402-${BZ}x${N_SAMPLE}-$(date +%m%d-%H%M) \
    batcher.curriculum.training_datasets.0.dataset.dataset_id=data.haoranxu.rkld.mix.mix8 \
    batcher.curriculum.training_datasets.0.dataset.dataset_container=orngscuscresco \
    batcher.curriculum.training_datasets.0.dataset.grader=deepchat_msft.graders.jsd_grader:JSDGrader \
    batcher.curriculum.training_datasets.0.dataset.grader.topic=$TEACHER_CKPT_0 \
    batcher.curriculum.training_datasets.0.dataset.grader.line=bus \
    batcher.curriculum.training_datasets.0.dataset.grader.topic_mode_or_user=${USER_0} \
    ...teacher_top_k=32 \
    \
    defaults.sampler.return_token_alternatives=None \
    batch_completer.n_batches_in_flight=5 \
    \
    defaults.instance_completer=qstar.instance_completers:SimpleInstanceCompleter \
    defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer \
    defaults.instance_completer.instance_optimizer.reinforce_strategy.sample_selector=qstar.sample_selectors:SimpleSampleSelector \
    defaults.instance_completer.instance_optimizer.reinforce_strategy.reward_reinforcer=qstar.optimizers.strategies.reward_reinforcers:ZeroReinforcer \
    defaults.target_samples_per_instance=${N_SAMPLE} \
    optimizer.rkld_alpha=0.0 \
    optimizer.fkld_alpha=1.0 \
    optimizer.kl_penalty_alpha=0.02 \
    optimizer.hparam_scaler.lr_per_instance_d16=$LR \
    ...renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe \
    \
    :berry_models.scallion_lpe:d36_80g_mbg16_bf16 \
    policy.n_gpus=80 \
    policy.initial_checkpoint=${STUDENT} \
    policy.n_ctx=$N_CTX \
    defaults.sampler.n_ctx=$N_CTX \
    defaults.max_tokens=$N_CTX \
    defaults.sampler.stop_tokens="<|im_end|>,<|fim_suffix|>" \
    \
    root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False' \
    policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True' \
    policy.ml_config='ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22' \
    \
    peashooter.num_sampling_processes=8 \
    peashooter.sampling_concurrency=24 \
    defaults.instances_per_batch=${BZ} \
    \
    security_profile=msft-orng \
    github_upload=False \
    wandb_enable=True \
    kafka_enable=False \
    enable_slackbot=False \
    ...save_every=50 \
    skip_validate_config=True
   