import os
import json
import re
import asyncio
import argparse
from typing import List, <PERSON><PERSON>, Dict, Any
from tqdm import tqdm
from anthropic import AsyncAnthropic

USER_PROMPT = """You will be given a competitive programming problem.
Analyze the maximum input constraints and identify the optimal algorithmic approach and data structures needed to process the largest possible test cases within the time and memory limits, then explain why your chosen implementation strategy is the most efficient solution. Please reason step by step about your solution approach, then provide a complete implementation in Python 3 that is thoroughly optimized for both speed and memory usage.

Your solution must read input from standard input (input()), write output to standard output (print()).
Do not include any debug prints or additional output.

Put your final solution within a single code block which looks like
```python
{starter_code}<your code here>
```

Problem:
{question}
"""

def extract_code_from_last_block(text: str) -> Tuple[str, bool]:
    """
    Extracts the code content from the last triple-backtick block.
    
    Args:
        text: Input text containing potential code blocks
        
    Returns:
        tuple: (extracted_text, match_found) where match_found is True if a 
               triple-backtick block was found, otherwise False.
    """
    pattern = r'```[^\n]*\n([\s\S]*?)```'
    matches = re.findall(pattern, text)
    return (matches[-1], True) if matches else (text, False)

def setup_anthropic_client():
    """Initialize and return the Anthropic client."""
    api_key = "xxxx"
    return AsyncAnthropic(api_key=api_key)

def count_tokens(text: str) -> int:
    """
    Simple token counting function that approximates tokens by splitting on whitespace.
    For more accurate counting, you could use a proper tokenizer.
    """
    return len(text.split())

async def process_question(question_data, client, max_tokens: int = 32768) -> Dict[str, Any]:
    """Process a single question and return the result."""
    question = question_data["question_content"].strip()
    question_id = question_data["question_id"]
    starter_code = question_data["starter_code"]
    difficulty = question_data["difficulty"]

    formatted_prompt = USER_PROMPT.format(question=question, starter_code=starter_code)
    
    for attempt in range(5):
        try:
            async with client.messages.stream(
                max_tokens=max_tokens,
                temperature=1.0,
                messages=[
                    {
                        "role": "user",
                        "content": formatted_prompt,
                    }
                ],
                model="claude-3-7-sonnet-20250219",
            ) as stream:
                # Collect streaming text
                full_response = ""
                async for text in stream.text_stream:
                    full_response += text
                
                # Get final message with metadata
                message = await stream.get_final_message()
                
                # Extract the actual response text
                if message.content and len(message.content) > 0:
                    choice_output = message.content[0].text
                else:
                    choice_output = full_response
                
                break
        except Exception as e:
            print(f"Error on attempt {attempt + 1} for question {question_id}: {e}")
            if attempt == 4:  # Last attempt
                choice_output = None
                message = None

    if not choice_output:
        print(f"Error processing question {question_id}")
        return {
            "question_id": question_id, 
            "code_list": [""], 
            "matched": False, 
            "question_content": question, 
            "difficulty": difficulty, 
            "starter_code": starter_code, 
            "full_output": "", 
            "token_count": 0,
            "usage": None
        }

    extracted_code, code_matched = extract_code_from_last_block(choice_output)
    token_count = count_tokens(choice_output)
    
    # Extract usage information if available
    usage = None
    if message and hasattr(message, 'usage'):
        usage = {
            "input_tokens": message.usage.input_tokens,
            "output_tokens": message.usage.output_tokens,
        }
    
    return {
        "question_id": question_id,
        "code_list": [extracted_code],
        "matched": code_matched,
        "question_content": question,
        "full_output": choice_output,
        "token_count": token_count,
        "difficulty": difficulty,
        "starter_code": starter_code,
        "usage": usage,
    }

async def process_question_worker(question_data: Dict[str, str], max_tokens: int = 32768) -> Dict[str, Any]:
    """Worker function to process a single question. Creates its own client."""
    client = setup_anthropic_client()
    return await process_question(question_data, client, max_tokens)

def load_questions_from_file(file_path: str) -> List[Dict[str, str]]:
    """Load all questions from a file into memory."""
    questions = []
    with open(file_path, "r") as file:
        for line in file:
            if not line.strip():
                continue
            data = json.loads(line)
            questions.append({
                "question_content": data["question_content"],
                "question_id": data["question_id"],
                "starter_code": data["starter_code"],
                "difficulty": data["difficulty"],
            })
    return questions

async def process_input_file(file_path: str, concurrency_limit: int = 10, max_tokens: int = 32768) -> Tuple[List[Dict[str, Any]], int]:
    """Process all questions in a single input file using asyncio with semaphore."""
    questions = load_questions_from_file(file_path)
    
    # Lower concurrency for Anthropic API to avoid rate limits
    semaphore = asyncio.Semaphore(concurrency_limit)
    results = []

    async def process_with_semaphore(question_data):
        async with semaphore:
            return await process_question_worker(question_data, max_tokens)

    tasks = [process_with_semaphore(question_data) for question_data in questions]
    
    # Use asyncio.as_completed to show progress
    for task in tqdm(asyncio.as_completed(tasks), 
                     total=len(questions),
                     desc=f"Processing {os.path.basename(file_path)}",
                     unit="questions"):
        result = await task
        results.append(result)
    
    non_match_count = sum(1 for result in results if not result["matched"])
    return results, non_match_count

def ensure_output_directory(directory: str = "./outputs") -> None:
    """Create output directory if it doesn't exist."""
    os.makedirs(directory, exist_ok=True)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run LiveCodeBench with Anthropic Claude")
    parser.add_argument(
        "--output", 
        type=str, 
        default="./outputs",
        help="Output directory for results (default: ./outputs)"
    )
    parser.add_argument(
        "--num_tests",
        type=int,
        default=5,
        help="Number of test runs (default: 5)"
    )
    parser.add_argument(
        "--concurrency",
        type=int,
        default=10,
        help="Maximum number of concurrent requests (default: 10, lower for API limits)"
    )
    parser.add_argument(
        "--max_tokens",
        type=int,
        default=32768,
        help="Maximum number of tokens for completion (default: 32768)"
    )
    return parser.parse_args()

async def main():
    """Main execution function."""
    args = parse_arguments()
    
    input_files = [
        # "livecodebench/code_generation_lite/test.jsonl",
        # "livecodebench/code_generation_lite/test2.jsonl",
        # "livecodebench/code_generation_lite/test3.jsonl",
        # "livecodebench/code_generation_lite/test4.jsonl",
        # "livecodebench/code_generation_lite/test5.jsonl",
        # "livecodebench/code_generation_lite/test6.jsonl",
        "livecodebench/code_generation_lite/test5_6.jsonl"
        # "livecodebench/code_generation_lite/tmp.jsonl"
    ]
    
    ensure_output_directory(args.output)
    
    for test_idx in range(args.num_tests):
        all_results = []
        total_non_matches = 0
        
        for input_file in input_files:
            file_results, file_non_matches = await process_input_file(
                input_file, args.concurrency, args.max_tokens
            )
            all_results.extend(file_results)
            total_non_matches += file_non_matches
        
        # Calculate mean token count
        valid_token_counts = [result["token_count"] for result in all_results if result["token_count"] > 0]
        mean_token_count = sum(valid_token_counts) / len(valid_token_counts) if valid_token_counts else 0
        
        # Calculate API usage statistics if available
        total_input_tokens = sum(result["usage"]["input_tokens"] for result in all_results if result["usage"])
        total_output_tokens = sum(result["usage"]["output_tokens"] for result in all_results if result["usage"])
        
        output_file = f"{args.output}/anthropic_test_{test_idx}.json"
        with open(output_file, "w") as file:
            json.dump(all_results, file, indent=4)
        
        print(f"Test {test_idx}: Invalid answers: {total_non_matches}")
        print(f"Test {test_idx}: Mean token count: {mean_token_count:.2f}")
        if total_input_tokens > 0:
            print(f"Test {test_idx}: Total input tokens: {total_input_tokens}")
            print(f"Test {test_idx}: Total output tokens: {total_output_tokens}")

if __name__ == "__main__":
    asyncio.run(main())