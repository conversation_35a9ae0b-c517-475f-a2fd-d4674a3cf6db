# export TEACHER_CKPT=az://orngscuscresco/models/snapshots/sweberry-v3-tbv3-20250304-decrypted
export TEACHER_CKPT=az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted
LR=1e-5
BZ=8
N_SAMPLE=1

oaipkg run --beam qstar.run_experiment nostrict \
    name=d16-debug-0611-2126 \
    batcher.curriculum.training_datasets.0.dataset.dataset_id=data.haoranxu.rkld.mix.mix0 \
    batcher.curriculum.training_datasets.0.dataset.dataset_container=orngscuscresco \
    batcher.curriculum.training_datasets.0.dataset.grader=deepchat_msft.graders.rkld_grader:RkldGrader \
    batcher.curriculum.training_datasets.0.dataset.grader.topic=$TEACHER_CKPT \
    batcher.curriculum.training_datasets.0.dataset.grader.line=bus \
    batcher.curriculum.training_datasets.0.dataset.grader.topic_mode_or_user=haoranxu \
    \
    defaults.sampler.return_token_alternatives=None \
    batch_completer.n_batches_in_flight=16 \
    \
    :qstar.presets.rkld:base_preset \
    defaults.target_samples_per_instance=${N_SAMPLE} \
    optimizer.rkld_alpha=1.0 \
    optimizer.kl_penalty_alpha=0.02 \
    optimizer.hparam_scaler.lr_per_instance_d16=$LR \
    \
    policy.model_config_name="falcon.orion.d16-s32-k4-fp16rgs-scallion-trimodal" \
    policy.layout="finetune-80g" \
    policy.encoding_name="orion_200k" \
    policy.initial_checkpoint=az://orngscuscresco/models/snapshots/models.tc.small/scallion-qstar-prior-d16-transferred-20241118/ \
    \
    peashooter.num_sampling_processes=8 \
    peashooter.sampling_concurrency=24 \
    defaults.instances_per_batch=${BZ} \
    \
    security_profile=msft-orng \
    github_upload=False \
    wandb_enable=False \
    kafka_enable=False \
    enable_slackbot=False \
    ...save_every=20000
   