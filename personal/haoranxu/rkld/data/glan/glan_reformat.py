import json, glob, uuid, os, pathlib, sys

# Paths
input_pattern = '/root/workspace/glan/part_*.jsonl'
output_path = '/root/workspace/glan_reformat.jsonl'

# Ensure output directory exists
os.makedirs(os.path.dirname(output_path), exist_ok=True)

# Process files line by line and write reformatted lines
matched_files = sorted(glob.glob(input_pattern))

processed_count = 0

with open(output_path, 'w', encoding='utf-8') as fout:
    for fname in matched_files:
        try:
            with open(fname, 'r', encoding='utf-8') as fin:
                for line in fin:
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        obj = json.loads(line)
                    except json.JSONDecodeError:
                        # Skip malformed JSON lines
                        continue

                    # Extract the first user message content
                    user_msg = None
                    for msg in obj.get('messages', []):
                        if msg.get('role') == 'user':
                            user_msg = msg.get('content', '')
                            break

                    if user_msg is None:
                        # No user message found; skip this entry
                        continue

                    new_obj = {
                        "problem": user_msg,
                        "solution": None,
                        "answer": None,
                        "subject": "no_name",
                        "answer_only": True,
                        "unique_id": f"glan_raw_{processed_count}",
                        "metadata": {}
                    }

                    fout.write(json.dumps(new_obj, ensure_ascii=False) + '\n')
                    processed_count += 1
        except FileNotFoundError:
            # Input file not found; skip
            continue

# Provide a brief summary as output for visibility
print(f"Processed {processed_count} entries from {len(matched_files)} input files.")

