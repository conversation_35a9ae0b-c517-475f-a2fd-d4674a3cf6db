import io
from PIL import Image
from chat import chat
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter
from message_completer import TokenMessageCompleter
from chat.render.renderer_registry import get_renderer
from bus_token_completer import BusTokenCompleter, QoSType
import chat
import json
from copy import deepcopy
import asyncio
from tqdm import tqdm
from legacy_rest_token_completer import LegacyRestTokenCompleter
from token_completer import CompleterBackend
from message_completer.message_completer import ParseErrorMode

user_prompt = """You are a coding problem evaluator.

Your task is to determine:

1. The target programming language of a given coding problem. You must select exactly one from the following list, using the exact capitalization:

    - Python
    - Go
    - C#
    - R
    - C++
    - C
    - Java
    - JavaScript
    - TypeScript
    - SQL
    - Others

    Use "Others" only when:
    - The problem clearly targets a language not in the list (e.g., Rust, Ruby), or
    - The problem is language-agnostic and does not imply any specific programming language.

2. The difficulty level of the coding problem. You must select exactly one from the following list, using the exact capitalization:

    - Easy
    - Medium
    - Hard
    - Very Hard
    - Invalid

    Use "Invalid" only when:
    - The problem cannot be solved due to missing essential context or incomplete description, or
    - It is not a coding problem at all.

Your response must be in the following format and nothing else:

## Language: <language>
## Difficulty: <difficulty>

Coding problem: {problem}
"""

def setup_completer_and_renderer():
    """Initialize and return the message completer and renderer."""
    token_completer_config = LegacyRestTokenCompleter.Config(
            api_base="http://127.0.0.1:5122/v1/inference",
            backend=CompleterBackend.FALCON_MM_BACKEND,
    )

    renderer_name = "harmony_v4.0.16_berry_v3_1mil_orion_lpe"

    message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=token_completer_config,
            renderer=renderer_name,
            completion_params={"model": "model", "temperature": 1, "max_tokens": 8192},
    )
    
    return message_completer_config.build(), get_renderer(renderer_name)

async def process_line(line_data, retries):
    """Process a single line of data"""
    line, line_index = line_data
    
    # Create message completer for this process
    message_completer, renderer = setup_completer_and_renderer()
    cot_juice = 128
    
    for _ in range(retries):
        try:
            messages = []
            system_message = chat.Message.system(
                model_identity_desc="You should carefully adhere to all formatting instructions.",
                    tools_section=None,  # explicit set in developer prompt
                    channel_config=chat.SystemChannelConfig(
                        valid_channels=("analysis", "final"), channel_required=True
                    ),
                    metadata=chat.SystemContentMetadata(reward_multiplier=cot_juice),
                )
            messages.append(system_message)

            problem = line["problem"]
            parts = [user_prompt.format(problem=problem)]
            messages.append(chat.Message.user(content=chat.MultimodalText(parts=parts)))
            convo = chat.Conversation(
                messages=messages,
            )
            
            completion = await message_completer.async_completion(
                conversations=[convo], n=1,
            )
            
            output = None
            for choice in completion.choices:
                new_messages = choice.get_messages(
                    parse_error_mode=ParseErrorMode.SYSTEM_ERROR
                )
                all_parts = [p for m in new_messages for p in m.content.parts]
                output = '\n\n'.join(all_parts)
                break

            if not output:
                raise ValueError("No output generated")

            new_data = deepcopy(line)
            language = output.split("## Language: ")[-1].split("\n")[0].strip()
            difficulty = output.split("## Difficulty: ")[-1].split("\n")[0].strip()
            if language not in [
                "Python", "Go", "C#", "R", "C++", "C", "Java", 
                "JavaScript", "TypeScript", "SQL", "Others"
            ]:
                raise ValueError(f"Invalid language: {language}")
            if difficulty not in ["Easy", "Medium", "Hard", "Very Hard", "Invalid"]:
                raise ValueError(f"Invalid difficulty: {difficulty}")

            new_data["metadata"]["language"] = language
            new_data["metadata"]["difficulty"] = difficulty
            new_data["metadata"]["analysis_output"] = output
            return (line_index, new_data)
        except Exception as e:
            # print(f"Error processing line {line_index}:\nproblem: {problem}, \nerror: {e}, \n retrying...")
            continue
    
    # If all retries failed, return None
    # print(f"Failed to process line {line_index} after {retries} retries.")
    return (line_index, None)

async def process_line_worker(line_data, retries):
    """Worker function to process a single line."""
    return await process_line(line_data, retries)

async def process_file_async(input_file, output_file, retries=10, concurrency_limit=64):
    """Process file using async/await pattern."""
    # Read all lines first
    lines = []
    with open(input_file, "r") as f:
        for i, line in enumerate(f):
            lines.append((json.loads(line), i))
    
    print(f"Processing {len(lines)} lines with concurrency limit {concurrency_limit}...")
    
    semaphore = asyncio.Semaphore(concurrency_limit)
    results_buffer = {}  # Buffer to maintain order: {line_index: data}
    successful_count = 0

    async def process_with_semaphore(line_data):
        async with semaphore:
            return await process_line_worker(line_data, retries)

    # Open output file for writing
    with open(output_file, "w") as f_out:
        tasks = [process_with_semaphore(line_data) for line_data in lines]
        
        # Process tasks and write results as they complete, maintaining order
        next_expected_index = 0
        completed_count = 0
        
        for task in tqdm(asyncio.as_completed(tasks), 
                         total=len(lines),
                         desc="Processing lines",
                         unit="lines"):
            result = await task
            line_index, processed_data = result
            completed_count += 1
            
            if processed_data is not None:
                results_buffer[line_index] = processed_data
                successful_count += 1
                
                # Write all consecutive results starting from next_expected_index
                while next_expected_index in results_buffer:
                    f_out.write(json.dumps(results_buffer[next_expected_index]) + "\n")
                    f_out.flush()  # Ensure data is written immediately
                    del results_buffer[next_expected_index]
                    next_expected_index += 1
            else:
                # Failed result, just increment the expected index
                if line_index == next_expected_index:
                    next_expected_index += 1
                    # Check if we can write any buffered results now
                    while next_expected_index in results_buffer:
                        f_out.write(json.dumps(results_buffer[next_expected_index]) + "\n")
                        f_out.flush()
                        del results_buffer[next_expected_index]
                        next_expected_index += 1
    
    print(f"Completed processing. {successful_count} successful results written to {output_file}")

async def main():
    """Main execution function."""
    input_file = "/root/workspace/glan/glan_reformat.jsonl"
    output_file = "/root/workspace/glan/glan_reformat_with_lg_and_difficulty_o4mini.jsonl"
    retries = 10
    concurrency_limit = 64
    
    await process_file_async(input_file, output_file, retries, concurrency_limit)

if __name__ == "__main__":
    asyncio.run(main())