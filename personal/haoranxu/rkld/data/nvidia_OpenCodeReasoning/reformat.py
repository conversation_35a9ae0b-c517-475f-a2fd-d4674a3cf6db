from datasets import load_dataset
import json
from tqdm import tqdm


PROMPT="""You will be given a competitive programming problem. Please reason step by step about the solution, then provide a complete implementation in one of the languages below: C++17, Python3, Java, JavaScript, Go.

Your solution must read input from standard input (cin), write output to standard output (cout).
Do not include any debug prints or additional output.

Put your final solution within a single code block if using C++17:
```cpp
<your code here>
```

If using Python:
```python
<your code here>
```

If using Java:
```java
<your code here>
```

If using JavaScript:
```javascript
<your code here>
```

If using Go:
```go
<your code here>
```

{problem}
"""

input_data = load_dataset('nvidia/OpenCodeReasoning', 'split_0')['split_0']
output_data = "./opencodereasoning_reformat.jsonl"

# Filter for specific difficulty levels and transform data
filtered_data = []
difficulty_stats = {}

for item in tqdm(input_data, desc="Processing items"):
    # Count difficulty levels
    difficulty = item["difficulty"]
    difficulty_stats[difficulty] = difficulty_stats.get(difficulty, 0) + 1
    
    if item["difficulty"] in ["competition", "HARD", "VERY_HARD"]:
        # Create metadata with all fields except id, input, output, solution
        metadata = {}
        for key, value in item.items():
            if key not in ["id", "input", "output", "solution"]:
                metadata[key] = value
        
        # Add hint to metadata
        metadata["hint"] = item["solution"]
        
        # Create new format
        new_data = {
            "problem": PROMPT.format(problem=item["input"]),
            "solution": None,
            "answer": None,
            "subject": "no_name",
            "answer_only": True,
            "unique_id": item["id"],
            "metadata": metadata
        }
        
        filtered_data.append(new_data)

# Write to JSONL file
with open(output_data, 'w') as f:
    for item in tqdm(filtered_data, desc="Writing to file"):
        f.write(json.dumps(item) + '\n')

print(f"Total original items: {len(input_data)}")
print(f"Filtered items: {len(filtered_data)}")
print(f"Difficulty level statistics:")
for difficulty, count in sorted(difficulty_stats.items()):
    print(f"  {difficulty}: {count}")
print(f"Saved to {output_data}")

