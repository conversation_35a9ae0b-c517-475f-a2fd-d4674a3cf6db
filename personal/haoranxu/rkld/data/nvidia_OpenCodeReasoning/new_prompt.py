import json
import re
import sys

def update_problem_description(problem_text):
    """
    Extract the actual problem content and wrap it with the new format.
    """
    
    # Define the old template pattern
    new_template = """Please reslove the following coding problem.

Put your final solution within a single python code block:

```python
<your code here>
```

"""
    
    # New template
    old_template_start = """You will be given a competitive programming problem. Please reason step by step about the solution.

Your solution must read input from standard input, write output to standard output.
Do not include any debug prints or additional output.

Put your final solution within a single code block by using one of the following languages: C++17, Python3, Java, JavaScript, Go. Different languages have different code block syntax, so make sure to use the correct one.

If using C++17:
```cpp
<your code here>
```

If using Python:
```python
<your code here>
```

If using Java:
```java
<your code here>
```

If using JavaScript:
```javascript
<your code here>
```

If using Go:
```go
<your code here>
```

"""
    
    # Extract the actual problem content by removing the old template
    if problem_text.startswith(old_template_start):
        actual_problem = problem_text[len(old_template_start):]
    else:
        ValueError("Problem text does not start with the expected old template format.")
        # If the format doesn't match exactly, try to find the problem content
        # Look for the end of the Go code block which should be the last template part
        # go_block_end = "```\n\n"
        # if go_block_end in problem_text:
        #     parts = problem_text.split(go_block_end)
        #     if len(parts) > 1:
        #         actual_problem = go_block_end.join(parts[1:])
        #     else:
        #         actual_problem = problem_text
        # else:
        #     actual_problem = problem_text
    
    # Combine new template with actual problem
    return new_template + actual_problem

def process_jsonl_file(input_file, output_file):
    """
    Process the JSONL file and update problem descriptions.
    """
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8') as outfile:
        
        for line_num, line in enumerate(infile, 1):
            try:
                # Parse JSON line
                data = json.loads(line.strip())
                
                # Update the problem field if it exists
                if 'problem' in data:
                    data['problem'] = update_problem_description(data['problem'])
                
                # Write updated line to output file
                outfile.write(json.dumps(data, ensure_ascii=False) + '\n')
                
            except json.JSONDecodeError as e:
                print(f"Error parsing line {line_num}: {e}")
                continue
            except Exception as e:
                print(f"Error processing line {line_num}: {e}")
                continue

# Usage
if __name__ == "__main__":
    # Example usage:
    # $ python new_prompt.py input.jsonl output.jsonl
    # Successfully processed input.jsonl -> output.jsonl
    
    if len(sys.argv) != 3:
        print("Usage: python new_prompt.py <input_file.jsonl> <output_file.jsonl>")
        print("Example: python new_prompt.py train_data.jsonl train_data_updated.jsonl")
        sys.exit(1)
    
    input_filename = sys.argv[1]
    output_filename = sys.argv[2]
    
    try:
        process_jsonl_file(input_filename, output_filename)
        print(f"Successfully processed {input_filename} -> {output_filename}")
    except FileNotFoundError:
        print(f"Error: File {input_filename} not found.")
    except Exception as e:
        print(f"Error: {e}")