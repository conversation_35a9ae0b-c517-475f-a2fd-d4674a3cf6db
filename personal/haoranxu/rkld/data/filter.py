import json
import argparse
import os
from collections import Counter

def filter_hard_problems(input_file, output_dir="hard_only"):
    """Filter problems with Hard and Very Hard difficulty levels"""
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate output filename based on input filename
    input_basename = os.path.basename(input_file)
    name_without_ext = os.path.splitext(input_basename)[0]
    output_file = os.path.join(output_dir, f"{name_without_ext}_hard_only.jsonl")
    
    difficulty_counts = Counter()
    language_counts = Counter()
    hard_difficulty_counts = Counter()
    hard_language_counts = Counter()
    total_lines = 0
    hard_lines = 0
    
    try:
        with open(input_file, 'r') as f_in, open(output_file, 'w') as f_out:
            for line in f_in:
                total_lines += 1
                try:
                    data = json.loads(line.strip())
                    
                    # Count overall statistics
                    if 'metadata' in data and 'difficulty' in data['metadata']:
                        difficulty = data['metadata']['difficulty']
                        difficulty_counts[difficulty] += 1
                        
                        # Filter for Hard and Very Hard
                        if difficulty in ["Hard", "Very Hard"]:
                            f_out.write(line)
                            hard_lines += 1
                            hard_difficulty_counts[difficulty] += 1
                            
                            # Count language for hard problems
                            if 'language' in data['metadata']:
                                language = data['metadata']['language']
                                hard_language_counts[language] += 1
                    
                    # Count overall language statistics
                    if 'metadata' in data and 'language' in data['metadata']:
                        language = data['metadata']['language']
                        language_counts[language] += 1
                        
                except json.JSONDecodeError:
                    print(f"Warning: Skipping invalid JSON line")
                    continue
    
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found")
        return
    except Exception as e:
        print(f"Error processing file: {e}")
        return
    
    # Print statistics
    print(f"=== Filtering Results for {input_file} ===")
    print(f"Total lines processed: {total_lines}")
    print(f"Hard problems filtered: {hard_lines}")
    print(f"Output file: {output_file}")
    print()
    
    print("=== Original Dataset Difficulty Distribution ===")
    difficulty_order = ["Easy", "Medium", "Hard", "Very Hard", "Invalid"]
    for difficulty in difficulty_order:
        count = difficulty_counts.get(difficulty, 0)
        percentage = (count / sum(difficulty_counts.values()) * 100) if sum(difficulty_counts.values()) > 0 else 0
        print(f"  {difficulty}: {count} ({percentage:.1f}%)")
    
    # Show any unexpected difficulty values
    unexpected = set(difficulty_counts.keys()) - set(difficulty_order)
    if unexpected:
        print("\nUnexpected difficulty values:")
        for diff in unexpected:
            count = difficulty_counts[diff]
            print(f"  {diff}: {count}")
    
    print("\n=== Filtered Dataset (Hard + Very Hard) Difficulty Distribution ===")
    for difficulty in ["Hard", "Very Hard"]:
        count = hard_difficulty_counts.get(difficulty, 0)
        percentage = (count / sum(hard_difficulty_counts.values()) * 100) if sum(hard_difficulty_counts.values()) > 0 else 0
        print(f"  {difficulty}: {count} ({percentage:.1f}%)")
    
    print("\n=== Original Dataset Language Distribution ===")
    for language, count in language_counts.most_common():
        percentage = (count / sum(language_counts.values()) * 100) if sum(language_counts.values()) > 0 else 0
        print(f"  {language}: {count} ({percentage:.1f}%)")
    
    print("\n=== Filtered Dataset Language Distribution ===")
    for language, count in hard_language_counts.most_common():
        percentage = (count / sum(hard_language_counts.values()) * 100) if sum(hard_language_counts.values()) > 0 else 0
        print(f"  {language}: {count} ({percentage:.1f}%)")
    
    print(f"\n=== Summary ===")
    print(f"Filtered {hard_lines} hard problems out of {total_lines} total problems")
    print(f"Filtering rate: {(hard_lines / total_lines * 100):.1f}%")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Filter Hard and Very Hard problems from JSONL file")
    parser.add_argument("--input", required=True, help="Path to the input JSONL file")
    parser.add_argument("--output_dir", default="hard_only", help="Output directory (default: hard_only)")
    
    args = parser.parse_args()
    filter_hard_problems(args.input, args.output_dir)