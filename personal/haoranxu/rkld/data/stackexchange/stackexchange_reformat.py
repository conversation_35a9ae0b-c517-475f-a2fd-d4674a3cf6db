import json
import os
from tqdm import tqdm

def reformat_stackexchange_data(input_file="stackexchange_conversation_raw_100k.jsonl"):
    """
    Reformat StackExchange JSONL data to the required format.
    
    Args:
        input_file (str): Path to the input JSONL file
    """
    # Create output filename by adding "reformat" to the original name
    base_name = os.path.splitext(input_file)[0]
    output_file = f"{base_name}_reformat.jsonl"
    
    # Count total lines for progress bar
    with open(input_file, 'r', encoding='utf-8') as infile:
        total_lines = sum(1 for _ in infile)
    
    processed_count = 0
    
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8') as outfile:
        
        for line in tqdm(infile, total=total_lines, desc="Processing"):
            # Parse the JSON line
            item = json.loads(line.strip())
            
            # Extract metadata
            metadata = item.get("metadata", {})
            
            # Create new data structure
            new_data = {
                "problem": metadata.get("question", ""),
                "solution": None,
                "answer": None,
                "subject": "no_name",
                "answer_only": True,
                "unique_id": f"stackexchange_{metadata.get('id', '')}",
                "metadata": {}
            }
            
            # Build new metadata
            new_metadata = {}
            
            # Add hint from first answer
            answers = metadata.get("answers", [])
            if answers and len(answers) > 0:
                new_metadata["hint"] = answers[0].get("text", None)
            
            # Add messages from original data
            new_metadata["messages"] = item.get("messages", [])
            
            # Copy all other metadata fields except answers, id, and question
            for key, value in metadata.items():
                if key not in ["answers", "id", "question"]:
                    new_metadata[key] = value
            
            new_data["metadata"] = new_metadata
            
            # Write the reformatted line to output file
            outfile.write(json.dumps(new_data, ensure_ascii=False) + '\n')
            processed_count += 1
    
    print(f"Reformatted data written to: {output_file}")
    print(f"Total entries reformatted: {processed_count}")

if __name__ == "__main__":
    reformat_stackexchange_data()
