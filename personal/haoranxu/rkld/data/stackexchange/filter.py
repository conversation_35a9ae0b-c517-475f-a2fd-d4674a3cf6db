import io
from PIL import Image
from chat import chat
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter
from message_completer import TokenMessageCompleter
from chat.render.renderer_registry import get_renderer
from bus_token_completer import BusTokenCompleter, QoSType
import chat
import json
from copy import deepcopy
import multiprocessing as mp
from functools import partial
from tqdm import tqdm
from legacy_rest_token_completer import LegacyRestTokenCompleter
from token_completer import CompleterBackend

user_prompt ="""You are an expert in software engineering, computer science, mathematics, and STEM fields. Your task is to evaluate the **difficulty of answering a given technical question** from platforms like StackOverflow or other Q&A sites. You should analyze the question and label it with one of the following five categories based on how challenging it would be to provide a high-quality, accurate, and comprehensive answer:

- **Easy**: The question is straightforward and can be answered with basic knowledge or simple explanations. Examples include:
  - Basic syntax questions (e.g., "How to declare a variable in Python?")
  - Simple debugging issues with clear error messages
  - Elementary math or science concepts
  - Questions about well-documented library functions

- **Medium**: The question requires moderate expertise and understanding. Examples include:
  - Implementation of standard algorithms or data structures
  - Debugging issues requiring some investigation
  - Questions about design patterns or best practices
  - Mathematical problems requiring multiple steps
  - Integration of multiple technologies or concepts

- **Hard**: The question is complex and requires deep understanding or specialized knowledge. Examples include:
  - Performance optimization problems
  - Complex architectural decisions
  - Advanced algorithm design or analysis
  - Questions involving multiple interacting systems
  - Advanced mathematical proofs or derivations
  - Domain-specific problems requiring significant expertise

- **Very Hard**: The question is extremely challenging, even for experts. Examples include:
  - Cutting-edge research problems
  - Questions at the intersection of multiple advanced fields
  - Problems with no clear or established solutions
  - Highly specialized domain knowledge (e.g., quantum computing, advanced cryptography)
  - Questions requiring extensive experimentation or research
  - Problems involving poorly documented or obscure technologies

- **Invalid**: The question is:
  - Too vague or unclear to answer meaningfully
  - Missing critical information or context
  - Not actually a question
  - Contains contradictory requirements
  - Spam or off-topic content

When evaluating, consider:
1. The depth of knowledge required
2. The complexity of concepts involved
3. Whether the question requires original problem-solving vs. recalling known information
4. The amount of context and explanation needed for a complete answer
5. Whether multiple valid approaches exist
6. The likelihood that even experts might disagree on the answer

For each question, provide your reasoning followed by the difficulty label in this format:

## Difficulty: <One of the five labels>

Here is the question to evaluate:

```
{question}
```
""".strip()


# token_completer_config = BusTokenCompleter.Config(
#     topic_mode_or_user="haoranxu",
#     topic_or_snapshot="az://orngscuscresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted",
#     bus_line="bus",
#     qos_type=QoSType.FIFO,
# )
  
# renderer_name = "harmony_v4.0.15_berry_v3_16k_orion_text" #"harmony_v4.0.16_berry_v3_1mil_orion_lpe"

# berry_turn_completer_config = BerryMultiMessageTurnCompleter.Config(
#         token_completer_config=token_completer_config,
#         completion_params={"temperature": 1, "max_tokens": 8192},
#         renderer=renderer_name,
# )

token_completer_config = LegacyRestTokenCompleter.Config(
        api_base="http://127.0.0.1:5122/v1/inference",
        backend=CompleterBackend.FALCON_MM_BACKEND,
)

renderer_name = "harmony_v4.0.16_berry_v3_1mil_orion_lpe"

berry_turn_completer_config = BerryMultiMessageTurnCompleter.Config(
        token_completer_config=token_completer_config,
        completion_params={"model": "model", "temperature": 1, "max_tokens": 32768},
        renderer=renderer_name,
)

def process_line(line_data, user_prompt, token_completer_config, berry_turn_completer_config, retries):
    """Process a single line of data"""
    line, line_index = line_data
    
    # Create turn completer for this process
    turn_completer = berry_turn_completer_config.build()
    cot_juice = 128
    for _ in range(retries):
        try:
            messages = []
            system_message = chat.Message.system(
                model_identity_desc="You should carefully adhere to all formatting instructions.",
                    tools_section=None,  # explicit set in developer prompt
                    channel_config=chat.SystemChannelConfig(
                        valid_channels=("analysis", "final"), channel_required=True
                    ),
                    metadata=chat.SystemContentMetadata(reward_multiplier=cot_juice),
                )
            messages.append(system_message)

            problem = line["problem"]
            new_data = deepcopy(line)

            parts = [user_prompt.format(question=problem)]
            messages.append(chat.Message.user(content=chat.MultimodalText(parts=parts)))
            convo = chat.Conversation(
                messages=messages,
            )
            output = []
            for i, msg in enumerate(
                    turn_completer.completion(convo, reward_multiplier=cot_juice).output_messages
            ):  
                # if msg.end_turn:
                #     output = msg.content.parts[0]
                output.append(msg.content.parts[0])
            output = "\n\n".join(output)
            # if output is None:
            #     output = msg.content.parts[0]
            difficulty = output.split("## Difficulty: ")[-1].split("\n")[0].strip()
            if difficulty not in ["Easy", "Medium", "Hard", "Very Hard", "Invalid"]:
                raise ValueError(f"Invalid difficulty: {difficulty}")
            new_data["metadata"]["difficulty"] = difficulty
            new_data["metadata"]["analysis_output"] = output
            return (line_index, new_data)
        except Exception as e:
            # print(f"Error processing line {line_index}:\nproblem: {problem}, \nerror: {e}, \n retrying...")
            continue
    
    # If all retries failed, return None
    # print(f"Failed to process line {line_index} after {retries} retries. message: {msg}")
    return (line_index, None)

def writer_process(queue, output_file, total_lines):
    """Writer process that continuously writes results to file"""
    written_count = 0
    results_buffer = {}  # Buffer to maintain order: {line_index: data}
    next_expected_index = 0
    
    with open(output_file, "w") as f_out:
        while written_count < total_lines:
            try:
                line_index, processed_data = queue.get(timeout=30000)
                
                if processed_data is not None:
                    results_buffer[line_index] = processed_data
                    
                    # Write all consecutive results starting from next_expected_index
                    while next_expected_index in results_buffer:
                        f_out.write(json.dumps(results_buffer[next_expected_index]) + "\n")
                        f_out.flush()  # Ensure data is written immediately
                        del results_buffer[next_expected_index]
                        written_count += 1
                        next_expected_index += 1
                else:
                    # Failed result, just increment the expected index
                    next_expected_index += 1
                    
            except Exception as e:
                print(f"Writer process error: {e}")
                break
    
    print(f"Writer process completed. Wrote {written_count} results to {output_file}")

if __name__ == "__main__":
    input_file = "reformat_stackexchange_conversation_raw_100k_reformat.jsonl"
    output_file = "labeled_stackexchange_conversation_raw_100k_reformat.jsonl"
    retries = 10
    num_processes = 64
    
    # Read all lines first
    lines = []
    with open(input_file, "r") as f:
        for i, line in enumerate(f):
            lines.append((json.loads(line), i))
    
    print(f"Processing {len(lines)} lines with {num_processes} processes...")
    
    # Create queue for communication with writer process
    result_queue = mp.Queue(maxsize=500)  # Limit queue size to prevent memory issues
    
    # Start writer process
    writer_proc = mp.Process(target=writer_process, args=(result_queue, output_file, len(lines)))
    writer_proc.start()
    
    # Create partial function with fixed arguments
    process_func = partial(
        process_line, 
        user_prompt=user_prompt,
        token_completer_config=token_completer_config,
        berry_turn_completer_config=berry_turn_completer_config,
        retries=retries
    )
    
    # Process with multiprocessing and send results to writer
    successful_count = 0
    with mp.Pool(processes=num_processes) as pool:
        for result in tqdm(pool.imap_unordered(process_func, lines), total=len(lines), desc="Processing lines"):
            result_queue.put(result)
            if result[1] is not None:
                successful_count += 1
    
    # Wait for writer process to finish
    writer_proc.join()
    
    print(f"Completed processing. {successful_count} successful results written to {output_file}")