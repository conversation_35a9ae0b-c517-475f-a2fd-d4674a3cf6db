import datasets
import json
import random
from tqdm import tqdm

# Load the datasets
codeforce_test_cases = datasets.load_dataset("open-r1/codeforces")["train"]
# codeforce_data = datasets.load_dataset("open-r1/codeforces-cots", "solutions")["train"]
# output_file = "./codeforces.c++.jsonl"
codeforce_data = datasets.load_dataset("open-r1/codeforces-cots", "solutions_py")["train"]
output_file = "./codeforces.py.jsonl"

# Create a mapping of test cases by ID for efficient lookup
test_cases_by_id = {}
for item in tqdm(codeforce_test_cases, desc="Building test cases mapping"):
    test_cases_by_id[item["id"]] = item.get("official_tests", [])

# Process the data
processed_data = []
seen_ids = set()

for item in tqdm(codeforce_data, desc="Processing CodeForces data"):
    # Skip if we've already processed this ID
    if item["id"] in seen_ids:
        continue
    
    seen_ids.add(item["id"])
    
    # Check if we have test cases for this ID
    if item["id"] not in test_cases_by_id or not test_cases_by_id[item["id"]]:
        continue
    
    # Get test cases and sample up to 50 if needed
    tests = test_cases_by_id[item["id"]]
    if len(tests) > 50:
        tests = random.sample(tests, 50)
    
    # Create the new data format
    new_data = {
        "problem": item["prompt"],
        "solution": None,
        "answer": None,
        "subject": "no_name",
        "answer_only": True,
        "unique_id": item["id"],
        "metadata": {
            "tests": tests,
            # Include all other metadata except excluded fields
            "id": item.get("id"),
            "aliases": item.get("aliases"),
            "contest_id": item.get("contest_id"),
            "contest_name": item.get("contest_name"),
            "contest_type": item.get("contest_type"),
            "contest_start": item.get("contest_start"),
            "contest_start_year": item.get("contest_start_year"),
            "index": item.get("index"),
            "time_limit": item.get("time_limit"),
            "memory_limit": item.get("memory_limit"),
            "title": item.get("title"),
            "description": item.get("description"),
            "input_format": item.get("input_format"),
            "output_format": item.get("output_format"),
            "examples": item.get("examples"),
            "note": item.get("note"),
            "hint": item.get("editorial"),  # Renamed from 'editorial' to 'hint'
            "api_metadata": item.get("api_metadata"),
            "interaction_format": item.get("interaction_format")
        }
    }
    
    processed_data.append(new_data)

# Write to JSONL file
with open(output_file, 'w', encoding='utf-8') as f:
    for item in processed_data:
        f.write(json.dumps(item, ensure_ascii=False) + '\n')

print(f"Processed {len(processed_data)} items and saved to {output_file}")
