import datasets
import json
import random
from tqdm import tqdm
import re

# Load the datasets
dataset = datasets.load_dataset("open-r1/verifiable-coding-problems-python")["train"]
codeforce_data = datasets.load_dataset("open-r1/codeforces-cots", "solutions_py")["train"]

# Create a set of existing codeforces problem IDs for quick lookup
codeforces_ids = set()
for item in tqdm(codeforce_data, desc="Building codeforces IDs set"):
    codeforces_ids.add(item["id"])

# Process the data
processed_data = []

for item in tqdm(dataset, desc="Processing verifiable coding problems"):
    # Check if this problem is already in codeforces data
    skip_item = False
    
    # Extract problem URL from metadata and check if it's a codeforces problem
    if item.get("metadata") and isinstance(item["metadata"], dict):
        problem_url = item["metadata"].get("problem_url", "")
        if problem_url and "codeforces.com" in problem_url:
            # Extract problem ID from URL (e.g., "1259/D" from "https://codeforces.com/problemset/problem/1259/D")
            match = re.search(r'/problem/(\d+)/([A-Z]\d*)', problem_url)
            if match:
                problem_id = f"{match.group(1)}/{match.group(2)}"
                if problem_id in codeforces_ids:
                    skip_item = True
    
    if skip_item:
        continue
    
    # Extract test cases from verification_info
    tests = []
    if item.get("verification_info") and isinstance(item["verification_info"], dict):
        tests = item["verification_info"].get("test_cases", [])
    
    # Skip if no test cases
    if len(tests) == 0:
        continue
    
    # Create the new data format
    new_data = {
        "problem": item["problem_statement"],
        "solution": None,
        "answer": None,
        "subject": "no_name",
        "answer_only": True,
        "unique_id": item["problem_id"],
        "metadata": {
            "tests": tests,
            # Include all fields from the original metadata
            **(item.get("metadata", {}) if isinstance(item.get("metadata"), dict) else {}),
            # Include other fields from the dataset
            "source": item.get("source"),
            "task_type": item.get("task_type"),
            "in_source_id": item.get("in_source_id"),
            "hint": item.get("gold_standard_solution"),
            "verification_info": item.get("verification_info")
        }
    }
    
    processed_data.append(new_data)

# Write to JSONL file
output_file = "./verifiable_coding_problems.py.jsonl"
with open(output_file, 'w', encoding='utf-8') as f:
    for item in processed_data:
        f.write(json.dumps(item, ensure_ascii=False) + '\n')

print(f"Processed {len(processed_data)} items and saved to {output_file}")
