import json
import argparse
from collections import Counter

def analyze_difficulty_stats(file_path):
    """Analyze difficulty statistics from a JSONL file"""
    difficulty_counts = Counter()
    language_counts = Counter()
    total_lines = 0
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                total_lines += 1
                try:
                    data = json.loads(line.strip())
                    if 'metadata' in data and 'difficulty' in data['metadata']:
                        difficulty = data['metadata']['difficulty']
                        difficulty_counts[difficulty] += 1
                    
                    if 'metadata' in data and 'language' in data['metadata']:
                        language = data['metadata']['language']
                        language_counts[language] += 1
                        
                except json.JSONDecodeError:
                    print(f"Warning: Skipping invalid JSON line")
                    continue
    
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found")
        return
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    print(f"=== Difficulty Statistics for {file_path} ===")
    print(f"Total lines processed: {total_lines}")
    print(f"Lines with difficulty data: {sum(difficulty_counts.values())}")
    print()
    
    print("Difficulty Distribution:")
    difficulty_order = ["Easy", "Medium", "Hard", "Very Hard", "Invalid"]
    for difficulty in difficulty_order:
        count = difficulty_counts.get(difficulty, 0)
        percentage = (count / sum(difficulty_counts.values()) * 100) if sum(difficulty_counts.values()) > 0 else 0
        print(f"  {difficulty}: {count} ({percentage:.1f}%)")
    
    # Show any unexpected difficulty values
    unexpected = set(difficulty_counts.keys()) - set(difficulty_order)
    if unexpected:
        print("\nUnexpected difficulty values:")
        for diff in unexpected:
            count = difficulty_counts[diff]
            print(f"  {diff}: {count}")
    
    print("\n" + "="*50)
    print("Language Distribution:")
    for language, count in language_counts.most_common():
        percentage = (count / sum(language_counts.values()) * 100) if sum(language_counts.values()) > 0 else 0
        print(f"  {language}: {count} ({percentage:.1f}%)")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Analyze difficulty and language statistics from JSONL file")
    parser.add_argument("--file", required=True, help="Path to the target JSONL file")
    
    args = parser.parse_args()
    analyze_difficulty_stats(args.file)