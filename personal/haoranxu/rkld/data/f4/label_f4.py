import io
from PIL import Image
from chat import chat
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter
from message_completer import TokenMessageCompleter
from chat.render.renderer_registry import get_renderer
from bus_token_completer import BusTokenCompleter, QoSType
import chat
import json
from copy import deepcopy
import multiprocessing as mp
from functools import partial
from tqdm import tqdm
import glob
from legacy_rest_token_completer import LegacyRestTokenCompleter
from token_completer import CompleterBackend
import signal
import os

user_prompt = """You are a coding problem evaluator.

Your task is to determine:

1. The target programming language of a given coding problem. You must select exactly one from the following list, using the exact capitalization:

    - Python
    - Go
    - C#
    - R
    - C++
    - C
    - Java
    - JavaScript
    - TypeScript
    - SQL
    - Others

    Use "Others" only when:
    - The problem clearly targets a language not in the list (e.g., Rust, Ruby), or
    - The problem is language-agnostic and does not imply any specific programming language.

2. The difficulty level of the coding problem. You must select exactly one from the following list, using the exact capitalization:

    - Easy
    - Medium
    - Hard
    - Very Hard
    - Invalid

    Use "Invalid" only when:
    - The problem cannot be solved due to missing essential context or incomplete description, or
    - It is not a coding problem at all.

Your response must be in the following format and nothing else:

## Language: <language>
## Difficulty: <difficulty>

Coding problem: {problem}
"""


# token_completer_config = BusTokenCompleter.Config(
#     topic_mode_or_user="haoranxu",
#     topic_or_snapshot="az://orngscuscresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted",
#     bus_line="bus",
#     qos_type=QoSType.FIFO,
# )
  
# renderer_name = "harmony_v4.0.15_berry_v3_16k_orion_text" #"harmony_v4.0.16_berry_v3_1mil_orion_lpe"

# berry_turn_completer_config = BerryMultiMessageTurnCompleter.Config(
#         token_completer_config=token_completer_config,
#         completion_params={"temperature": 1, "max_tokens": 8192},
#         renderer=renderer_name,
# )

token_completer_config = LegacyRestTokenCompleter.Config(
        api_base="http://127.0.0.1:5122/v1/inference",
        backend=CompleterBackend.FALCON_MM_BACKEND,
)

renderer_name = "harmony_v4.0.16_berry_v3_1mil_orion_lpe" #"harmony_v4.0.15_berry_v3_16k_orion_text" #"harmony_v4.0.16_berry_v3_1mil_orion_lpe"

berry_turn_completer_config = BerryMultiMessageTurnCompleter.Config(
        token_completer_config=token_completer_config,
        completion_params={"model": "model", "temperature": 1, "max_tokens": 131072},
        renderer=renderer_name,
)
token_completer = berry_turn_completer_config.build()
renderer = get_renderer(renderer_name)


def process_line(line_data, user_prompt, token_completer_config, berry_turn_completer_config, retries, filename):
    """Process a single line of data"""
    line, line_index = line_data
    cot_juice = 128
    # Create turn completer for this process
    turn_completer = berry_turn_completer_config.build()
    
    for _ in range(retries):
        try:
            messages = []
            system_message = chat.Message.system(
                model_identity_desc="You should carefully adhere to all formatting instructions.",
                    tools_section=None,  # explicit set in developer prompt
                    channel_config=chat.SystemChannelConfig(
                        valid_channels=("analysis", "final"), channel_required=True
                    ),
                    metadata=chat.SystemContentMetadata(reward_multiplier=cot_juice),
                )
            messages.append(system_message)

            problem = line["messages"][0]["content"]
            parts = [user_prompt.format(problem=problem)]
            messages.append(chat.Message.user(content=chat.MultimodalText(parts=parts)))
            convo = chat.Conversation(
                messages=messages,
            )
            output = []
            for i, msg in enumerate(
                    turn_completer.completion(convo, reward_multiplier=cot_juice).output_messages
            ):  
                output.append(msg.content.parts[0])
                # if msg.end_turn:
                #     output = msg.content.parts[0]
            output = "\n\n".join(output)
            # if output is None:
            #     output = msg.content.parts[0]

            language = output.split("## Language: ")[-1].split("\n")[0].strip()
            difficulty = output.split("## Difficulty: ")[-1].split("\n")[0].strip()
            if language not in [
                "Python", "Go", "C#", "R", "C++", "C", "Java", 
                "JavaScript", "TypeScript", "SQL", "Others"
            ]:
                raise ValueError(f"Invalid language: {language}")
            if difficulty not in ["Easy", "Medium", "Hard", "Very Hard", "Invalid"]:
                raise ValueError(f"Invalid difficulty: {difficulty}")

            new_data = {
                "problem": problem,
                "solution": None,
                "answer": None,
                "subject": "no_name",
                "answer_only": True,
                "unique_id": f"{filename}_{line_index}",
                "metadata": {
                    "tests": line["metadata"]["tests"],
                    "language": language,
                    "difficulty": difficulty,
                    "analysis_output": output, 
                }
            }
            # new_data["metadata"]["language"] = language
            # new_data["metadata"]["difficulty"] = difficulty
            return (line_index, new_data)
        except Exception as e:
            # print(f"Error processing line {line_index}:\nproblem: {problem}, \nerror: {e}, \n retrying...")
            continue
    
    # If all retries failed, return None
    # print(f"Failed to process line {line_index} after {retries} retries. message: {msg}")
    return (line_index, None)

def writer_process(queue, output_file, total_lines):
    """Writer process that continuously writes results to file"""
    written_count = 0
    results_buffer = {}  # Buffer to maintain order: {line_index: data}
    next_expected_index = 0
    
    with open(output_file, "w") as f_out:
        while written_count < total_lines:
            try:
                line_index, processed_data = queue.get(timeout=30000)
                
                if processed_data is not None:
                    results_buffer[line_index] = processed_data
                    
                    # Write all consecutive results starting from next_expected_index
                    while next_expected_index in results_buffer:
                        f_out.write(json.dumps(results_buffer[next_expected_index]) + "\n")
                        f_out.flush()  # Ensure data is written immediately
                        del results_buffer[next_expected_index]
                        written_count += 1
                        next_expected_index += 1
                else:
                    # Failed result, just increment the expected index
                    next_expected_index += 1
                    
            except Exception as e:
                print(f"Writer process error: {e}")
                break
    
    print(f"Writer process completed. Wrote {written_count} results to {output_file}")

class TimeoutError(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutError("Processing timed out after 2 hours")

if __name__ == "__main__":
    # input_file = "/root/workspace/glan_reformat_6.jsonl"
    # output_file = "/root/workspace/glan_reformat_with_lg_and_difficulty_o3mini_6.jsonl"
    retries = 10
    num_processes = 64
    

    pattern = f"/root/workspace/f4_data/*.jsonl"
    input_files = glob.glob(pattern)
    input_files = [f for f in input_files if "python" in f and "difficulty" not in f]
    output_files = [
        f"{'/'.join(file.split('/')[:-1])}/juice_128_difficulty_lg_o4mini_{file.split('/')[-1]}"
        for file in input_files
    ]

    for idx, input_file in tqdm(enumerate(input_files)):
        output_file = output_files[idx]
        print(f"processing input file {input_file}")
        
        # Set up timeout alarm for 2 hours (7200 seconds)
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(3600*4)
        
        try:
            # Read all lines first
            lines = []
            with open(input_file, "r") as f:
                for i, line in enumerate(f):
                    lines.append((json.loads(line), i))
            
            print(f"Processing {len(lines)} lines with {num_processes} processes...")
            
            # Create queue for communication with writer process
            result_queue = mp.Queue(maxsize=100)
            
            # Start writer process
            writer_proc = mp.Process(target=writer_process, args=(result_queue, output_file, len(lines)))
            writer_proc.start()
            
            # Create partial function with fixed arguments
            filename = ".".join(input_file.split('/')[-1].split(".")[:-1])
            process_func = partial(
                process_line, 
                user_prompt=user_prompt,
                token_completer_config=token_completer_config,
                berry_turn_completer_config=berry_turn_completer_config,
                retries=retries,
                filename=filename,
            )
            
            # Process with multiprocessing and send results to writer
            successful_count = 0
            with mp.Pool(processes=num_processes) as pool:
                for result in tqdm(pool.imap_unordered(process_func, lines), total=len(lines), desc="Processing lines"):
                    result_queue.put(result)
                    if result[1] is not None:
                        successful_count += 1
            
            # Wait for writer process to finish
            writer_proc.join()
            
            print(f"Completed processing. {successful_count} successful results written to {output_file}")
            
        except (TimeoutError, Exception) as e:
            print(f"Error or timeout processing file {input_file}: {e}")
            print("Moving to next file...")
            
            # Clean up processes if they exist
            try:
                if 'writer_proc' in locals() and writer_proc.is_alive():
                    writer_proc.terminate()
                    writer_proc.join(timeout=5)
                if 'pool' in locals():
                    pool.terminate()
                    pool.join()
            except:
                pass
                
        finally:
            # Cancel the alarm
            signal.alarm(0)