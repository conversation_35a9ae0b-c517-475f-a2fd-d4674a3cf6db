ITC=768
JUICE=128
EXPERIMENT_NAME="vsc-mix16-arm2-stage2-noread-itc-${ITC}-juice-${JUICE}-32x32$(date +%m%d-%H%M)"
export CAAS_CONNECTION_POOL_SIZE=800

CMD=(
beam python --use-cwd -m qstar.run_experiment nostrict
name=$EXPERIMENT_NAME
# name=pdw2-test-sbh-run1
seed=20250727

skip_validate_config=True

# Policy settings
:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
#policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True'

# nv4
# policy.initial_checkpoint=az://orngscuscresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted/
# o4-mini
# policy.initial_checkpoint=az://orngscuscresco/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted
# codex-mini
# policy.initial_checkpoint=az://orngscuscresco/models/nv4-codex-tc-may11-s200-decrypted
# gpt5-mini 0710
# policy.initial_checkpoint=az://orngscuscresco/models/snapshots/zen-os4-nv4-cbv6-hh-rkld-4jul-orca-2-2025-07-10-00-04-decrypted/
# gpt5-mini 0804
# policy.initial_checkpoint=az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted/
# gpt5mini sft
# policy.initial_checkpoint=az://orngscuscresco/twapi/mini/e/chenyifang-swe_vsc_workflow_sft_rrb_v3_gpt5mini_b128_lr1e-5-run4/043bde0c-3371-4d9f-a0a3-8cef27b92f4f/checkpoint/model1/000000000030/
# gpt5mini prerun1
# policy.initial_checkpoint=az://orngscuscresco/twapi/mini/e/haoranxu-prerun1-000-mix16-5mini0804-itc-768-juice-128-32x32-sbt_rrb_rcr0808-0926/policy/step_000080/250808200256J6TQEQNP-0/
# gpt5mini 0804 - preprun -v2
policy.initial_checkpoint=az://orngscuscresco/twapi/mini/e/haoranxu-vsc-mix16-arm2-prerun-itc-768-juice-128-32x320820-0903/policy/step_000070/250821092713KTLR7IJU-0/
policy.initial_checkpoint_mode='resume'

policy.n_gpus=128
policy.is_multimodal=True
# ...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2

# Systems settings
:qstar.presets.common:longer_timeout
peashooter.sampling_use_ev3=True
peashooter.timeout_seconds.stalled_datapoint=3600
timeout.default=None

# Model settings
policy.n_ctx=131072
defaults.n_ctx=131072
...harmony_constrained_sampling=True
optimizer.hparam_scaler.lr_per_instance_d16=5e-6
...save_every=5

# Dataset configs!
:deep_swe_msft.vsc_presets:train_vsc_mix16
...dataset_container=orngscuscresco
...max_num_yields=256
# ...override_pass_rate_minimum=0.01

...prefill_token_cost_multiplier=0.025
...dataset.variant_producer=CompositeVariantProducer
...dataset.variant_producer.variant_producers.0=VarDiscountingVariantProducer
...dataset.variant_producer.variant_producers.0.override_reward_multiplier=${JUICE}
...conversation_start_date=$(date +"%Y-%m-%d")
...tools_format_version=v2
...instance_objective.aux_objective_0=caas_autograding.tool_penalty_objective:ToolPenaltyObjective

berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=3

# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
defaults.inverse_token_cost=${ITC}
defaults.instances_per_batch=32
defaults.target_samples_per_instance=32
defaults.channel_config.channels=analysis,commentary,final
defaults.sampler.interleave_channels.0=analysis
defaults.sampler.interleave_channels.1=commentary

batch_completer.n_batches_in_flight=30 # number of batches in flight
peashooter.num_sampling_processes=32 # number of sampling processes per instance worker
peashooter.sampling_concurrency=16 # concurrency sampling threads per process
peashooter.num_instance_workers=64 # number of instance workers

## Dataset configs
defaults.instance_completer=qstar.instance_completers:VariantsInstanceCompleter
defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer
# swe-bench-train
batcher.curriculum.training_datasets.0.dataset.instance_completer="deep_swe_msft.swe_bench_train_v2_vsc.dataset_config:SWEBenchV2InstanceCompleter"
batcher.curriculum.training_datasets.0.dataset.inverse_token_cost_multiplier=16
# swe-bench-hard
batcher.curriculum.training_datasets.1.dataset.inverse_token_cost_multiplier=32
# rrb_py
batcher.curriculum.training_datasets.2.dataset.inverse_token_cost_multiplier=8
# rrb_ts
batcher.curriculum.training_datasets.3.dataset.inverse_token_cost_multiplier=8
# rrb_java
batcher.curriculum.training_datasets.4.dataset.inverse_token_cost_multiplier=8
# rrb_cs
batcher.curriculum.training_datasets.5.dataset.inverse_token_cost_multiplier=8
# rrb_js
batcher.curriculum.training_datasets.6.dataset.inverse_token_cost_multiplier=8
# repo_qa + file_qa
batcher.curriculum.training_datasets.7.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.8.dataset.inverse_token_cost_multiplier=8
# if
batcher.curriculum.training_datasets.8.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.9.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.10.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.11.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.12.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.13.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.14.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.15.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.16.dataset.inverse_token_cost_multiplier=8
# batcher.curriculum.training_datasets.17.dataset.inverse_token_cost_multiplier=8

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=swe-main-run-2
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}" 2>&1 | tee -a swe-nv4.log
