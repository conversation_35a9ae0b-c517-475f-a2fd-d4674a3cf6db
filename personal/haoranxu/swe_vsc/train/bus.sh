cp ~/.rcall_config.py ~/.rcall_config.py.bak

sed -i '/""" + EXTRA_SETUP/{
r /dev/stdin
d
}' ~/.rcall_config.py <<'EOF'
""" + EXTRA_SETUP + """
case "$HOSTNAME" in
*-0)
echo "yes" > ~/is_head_pod.txt
export OPENAI_API_KEY="dummy_key"
oaipkg install bus > ~/bus_install.log 2>&1
export TWDEV_LAUNCH_CACHE_SVC_ENGINE=1 && brix run --env TWDEV_LAUNCH_CACHE_SVC_ENGINE=1 -- python /root/code/openai/torchflow/run-torchflow-setup.py > cache_svc.log 2>&1
RUST_BACKTRACE=1 nohup python -m harmony_scripts.engines.start_engine \
--name=gpt5r \
--mode=optimal \
--model_config=falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal \
--snapshot_path="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas" \
--is_multimodal=False \
--gpu_kind=H100 \
--renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_tools_v2" \
--restartable \
--extra_config_string="load_inference_snapshots_with_tcv2=False raise_on_load_for_missing_tensor=False ixf_sampling_extension_gpu_to_cpu_async=False twapi_cpu_comm_backend=gloo ixf_batcher_response_loop_timeout=600 allow_embedding_prefix_loading=True tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0} ixf_max_cache_list_length=4096" \
--cluster=local \
--bus_enable_qos=True \
--bus_topic_mode_or_user="gpt-oss-rkld" \
--n_replicas=1 \
--bus_rate_limiter=KV_UTIL \
--use_bus_v2=True \
> ~/bus.log 2>&1 & ;;
*)
echo "no" > ~/is_head_pod.txt ;;
esac
"""
EOF


# Base job name
base_job_name="swe-sft-data"
GROUP="team-moonfire-genaicore"

# Default values
start_num=60
end_num=70
cluster="prod-southcentralus-hpe-5"
priority_class="team-critical"
# priority_class="team-high"

# Parse command line arguments if provided
if [ $# -ge 2 ]; then
    start_num=$1
    end_num=$2
fi

echo "Creating jobs from $start_num to $end_num"

# Loop through and create multiple jobs with different suffixes
for i in $(seq $start_num $end_num); do
    job_name="${base_job_name}${i}"
    echo "Creating job: $job_name"
    
    OAIPKG_OVERRIDE_WHEEL=unsafe_skip \
    BRIX_QUOTA=${GROUP} \
    twdev create-ray-devbox \
    cluster=$cluster \
    num_pods=2 \
    setup_twapi=True \
    num_gpu=8 \
    job_name=$job_name \
    priority_class=$priority_class
    
    echo "Job $job_name submitted"
    # Optional: add a small delay between job submissions
    sleep 5
done

# Restore the original rcall_config.py if backup exists
if [ -f ~/.rcall_config.py.bak ]; then
    mv ~/.rcall_config.py.bak ~/.rcall_config.py
    echo "Restored original ~/.rcall_config.py"
else
    echo "Warning: ~/.rcall_config.py.bak not found, skipping restore"
fi
