dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="vsc-mix16-arm2-2-derisk-sbhv2-rewrite-prerun-$dt-peaval"
RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"

CMD=(
beam python --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}
root_config="mini.root.dev init_actors_rpc_timeout=600"
experiment_id=haoranxu-vsc-mix16-derisk-sbhv2-rewrite-itc-768-juice-128-32x320901-0850
...conversation_start_date=$(date +"%Y-%m-%d")
#zhendongwang-mix9-pdw2-ivt-mixed-32x32-o4mini-tef05-7-tpm2-rm-lr1e-5-run9
#zhendongwang-mix9-pdw2-ivt-mixed-32x32-o4mini-tef05-7-tpm2-rm-lr1e-5-run10
#zhendongwang-mix9-pdw2-ivt-mixed-32x32-o4mini-tef05-7-tpm2-rm-lr1e-5-run9
#zhendongwang-mix7-pdw2-ivt16k-32x32-o4mini-tef05-tpm1-rm-lr1e-5-run3
#zhendongwang-mix7-pdw2-ivt16k-32x32-o4mini-tef05-tpm2-rm-lr1e-5-run2

#zhendongwang-mix3-vsc-ivt16k-32x32-nv4-tpm3-efc05-lr1e-5-rm-run4
#zhendongwang-mix1-pdw2-ivt16k-32x32-o4mini-efc05-tef1-tpm1-tc-rm-run2
#chenliang1-mix2-pdw2-ivt16k-32x32-o4mini-efc05-tef1-tpm1-run7
#zhendongwang-mix1-pdw2-ivt16k-32x32-o4mini-efc05-tef1-tpm1-tc-rm-run2
#swang-swe-data-derisk-sbt_passgraderonly

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=1               # swang concurrent thread in one process
peashooter.num_sampling_processes=100            # swang concurrent process in one worker
#peashooter.tool_pool_config.num_tool_workers=64 # swang concurrent processes in tool pool
peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
peashooter.sampling_use_ev3=True

# swang, not support
#timeout.evaluate=80000
#timeout.rollout=3600

# Set this if you want to eval all checkpoints at once in parallel (Not needed if your training run just started.)
# max_workers_per_step=4 # swang nodes per step, cannot exceed total sample nodes
max_active_steps=8    # swang active steps

eval_settings.eval_every=5
eval_settings.min_step=0
eval_settings.max_step=300
eval_settings.exit_on_no_new_checkpoint=False

# Inherit model params from training experiment!
auto_inherit_training_args=True

# data
:deep_swe_eval_msft.swe_bench.peaval.vsc.presets:ev_sm_bench_vsc
eval_settings.evals.0.dataset.override_target_samples_per_instance=1
eval_settings.evals.0.dataset.max_num_yields=256
...conversation_start_date=$(date +"%Y-%m-%d")
...tools_format_version=v2
# SWV with prompt file
# eval_settings.evals.1.dataset.override_target_samples_per_instance=1
# eval_settings.evals.1.dataset.max_num_yields=100

# ':deep_swe_eval_msft.msweb.peaval.vsc.presets:ev_sm_bench_juice_vsc(juice=768 override_target_samples_per_instance=1 max_num_yields=100)'

# SWE-Bench Hard data
# :deep_swe_eval_msft.swebench_hard:swebenchhard_12878_repair_msft_hq_vsc_testval
# :deep_swe_eval_msft.swebench_hard:o3_hpe_cotograder_bus

metrics_collector=deep_swe_eval_msft.metrics.vsc_swe_metrics_collector:SWEMetricsCollector

#policy.n_ctx=524288
#defaults.n_ctx=524288
policy.n_ctx=262144
defaults.n_ctx=262144
defaults.sampler.max_num_yields=256
# defaults.sampler.max_num_yields=100
defaults.sampler.harmony_constrained_sampling=True
...harmony_renderer_name=${RENDERER_NAME}

# Set to True if you want to evaluate step0 (your prior)
eval_settings.eval_initial_policy=True

# msft special
load.restore_from_all_clusters=False

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=swe-pevals
kafka_enable=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log
