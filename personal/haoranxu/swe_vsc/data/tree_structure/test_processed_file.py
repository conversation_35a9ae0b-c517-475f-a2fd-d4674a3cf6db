import json
import sys
import blobfile as bf
from tqdm import tqdm

def check_project_structure_field(input_file):
    """
    Check if all rows in a JSONL file have the field sample["metadata"]["project_structure"]
    and that it's non-empty
    
    Args:
        input_file: Path to the input JSONL file (can be Azure blob path)
    """
    missing_field_count = 0
    empty_field_count = 0
    total_count = 0
    missing_ids = []
    empty_ids = []
    missing_repos = []
    empty_repos = []
    
    print(f"Checking file: {input_file}")
    
    with bf.BlobFile(input_file, "r") as f:
        lines = f.readlines()
        
    for i, line in enumerate(tqdm(lines, desc="Checking rows")):
        try:
            sample = json.loads(line)
            total_count += 1
            
            # Check if the nested field exists
            if "metadata" not in sample or "project_structure" not in sample["metadata"]:
                missing_field_count += 1
                unique_id = sample.get("unique_id", f"row_{i}")
                missing_ids.append(unique_id)
                repo_name = sample.get("metadata", {}).get("repo_name", "Unknown")
                missing_repos.append(repo_name)
            elif not sample["metadata"]["project_structure"]:  # Check if empty
                empty_field_count += 1
                unique_id = sample.get("unique_id", f"row_{i}")
                empty_ids.append(unique_id)
                repo_name = sample["metadata"].get("repo_name", "Unknown")
                empty_repos.append(repo_name)
                
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON at line {i+1}: {e}")
            continue
    
    # Print results
    print(f"\nTotal rows: {total_count}")
    print(f"Rows with non-empty project_structure field: {total_count - missing_field_count - empty_field_count}")
    print(f"Rows missing project_structure field: {missing_field_count}")
    print(f"Rows with empty project_structure field: {empty_field_count}")
    
    if missing_field_count > 0:
        print(f"\n--- Missing field samples ---")
        print(f"First 10 missing unique_ids: {missing_ids[:10]}")
        print(f"First 10 missing repo_names: {missing_repos[:10]}")
        if missing_field_count > 10:
            print(f"... and {missing_field_count - 10} more")
    
    if empty_field_count > 0:
        print(f"\n--- Empty field samples ---")
        print(f"First 10 empty unique_ids: {empty_ids[:10]}")
        print(f"First 10 empty repo_names: {empty_repos[:10]}")
        if empty_field_count > 10:
            print(f"... and {empty_field_count - 10} more")
    
    if missing_field_count == 0 and empty_field_count == 0:
        print("\n✅ All rows have non-empty project_structure field!")
    else:
        total_problematic = missing_field_count + empty_field_count
        print(f"\nPercentage with valid field: {((total_count - total_problematic) / total_count * 100):.2f}%")

    return total_count - missing_field_count - empty_field_count, total_count, missing_ids, empty_ids

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python check_project_structure_field.py <input_file>")
        print("Example: python check_project_structure_field.py az://orngscuscresco/data/haoranxu/swe/swe_vsc/swb_train_rewritten_4x/swe_bench_train_updated/rewritten2.jsonl")
        sys.exit(1)
    
    input_file = sys.argv[1]
    check_project_structure_field(input_file)