import asyncio
import json
import time
import re

import blobfile as bf
import structlog
from caas.commands import BashScript, RawBashScript
from caas.terminal.api import TerminalSession
from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn
from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.mrfs_setup import mrfs_setup_fn_coreutils
from deep_swe_msft.qa_vsc.datasets.setup import qa_setup_fn_coreutils
from deep_swe_msft.tools.vscode_copilot_tool import (
    exec,
    new_container,
    prepare_vsc_tool,
)
from mini.metrics import metrics_init
from tqdm import tqdm

from smokey import Smokey
from deep_swe_msft.rfs_rcs_bb_ml_vsc.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK
import random
import string

def generate_random_string():
    length = random.randint(5, 8)  # Choose a random length between 5 and 8
    letters = string.ascii_letters  # Includes both uppercase and lowercase letters
    return "".join(random.choices(letters, k=length))

def main():
    with bf.BlobFile(
        "az://orngscuscresco/data/haoranxu/swe/swe_vsc/repo_qa/repo_loc_ml_data_merge.jsonl",
        # "az://orngscuscresco/data/haoranxu/swe/swe_vsc/repo_qa/repo_qa_ml_data_merge.jsonl",
        # "az://orngscuscresco/data/haoranxu/swe/swe_vsc/file_qa/file_qa_ml_data_merge.jsonl",

        "r",
    ) as f:
        samples = [json.loads(line) for line in f]

    OUTPUT_DIR = "az://orngscuscresco/data/haoranxu/swe/swe_vsc/repo_qa_v2/repo_loc_ml_data_merge.jsonl"
    # OUTPUT_DIR = "az://orngscuscresco/data/haoranxu/swe/swe_vsc/repo_qa_v2/repo_qa_ml_data_merge.jsonl"
    # OUTPUT_DIR = "az://orngscuscresco/data/haoranxu/swe/swe_vsc/file_qa_v2/file_qa_ml_data_merge.jsonl"

    with bf.BlobFile(
        OUTPUT_DIR,
        "a",
    ) as f:
        for sample in tqdm(samples):
            try:
                repo_name = sample["metadata"]["repo_name"].split("/")[-1]
                repo_root = random.choice(
                    [
                        f"{repo_name}",
                        f"/workspace/{repo_name}",
                        f"/{repo_name}",
                        f"/root/{repo_name}",
                        f"/root/repos/{repo_name}",
                        f"/root/code/{repo_name}",
                        f"/root/{generate_random_string()}/{repo_name}",
                    ]
                )
                sample["metadata"]["cwd"] = repo_root
                f.write(json.dumps(sample) + "\n")
            except Exception as e:
                print(f"error processing sample {sample['unique_id']}: {e}")
                failure_cnt += 1


if __name__ == "__main__":
    main()
