import asyncio
import json
import time
import re

import blobfile as bf
import structlog
from caas.commands import BashScript, RawBashScript
from caas.terminal.api import TerminalSession
from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn
from deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets.mrfs_setup import mrfs_setup_fn_coreutils
from deep_swe_msft.tools.vscode_copilot_tool import setup_vscutils
from deep_swe_msft.tools.vscode_copilot_tool import (
    exec,
    new_container,
    prepare_vsc_tool,
)
from mini.metrics import metrics_init
from tqdm.asyncio import tqdm

from smokey import Smokey
from deep_swe_msft.rfs_rcs_bb_ml_vsc.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK


metrics_init(config=None, intial_step=0)
logger = structlog.stdlib.get_logger(component=__name__)


def extract_code_from_last_block(text: str):
    """
    Extracts the code content from the last triple-backtick block.
    
    Args:
        text: Input text containing potential code blocks
        
    Returns:
        tuple: (extracted_text, match_found) where match_found is True if a 
               triple-backtick block was found, otherwise False.
    """
    pattern = r'```[^\n]*\n([\s\S]*?)```'
    matches = re.findall(pattern, text)
    return (matches[-1], True) if matches else (text, False)

async def extract_project_structure(endpoint, sample, semaphore):
    async with semaphore:
        try:
            caas_container = await new_container(endpoint, sample["metadata"]["image_name"])
            caas_session = caas_container.caas_session
            # await prepare_vsc_tool(caas_session, "/root/code")
            # terminal_session = TerminalSession(caas_session, endpoint=endpoint)
            await setup_vscutils(datapoint=sample, session=caas_session, workdir="/testbed")
            project_structure = await exec(caas_session, "read_project_structure", {})
            return project_structure
        except Exception as e:
            import traceback

            tb_str = traceback.format_exc()
            print(tb_str)
            print(sample["unique_id"])

            # import trainflow.utils; trainflow.utils.debugpy_breakpoint(remote=True)
            raise e


async def main():
    endpoint: str = "https://eastus2.caas.azure.com"
    MAX_WORKERS = 64  # Adjust this number based on your system capacity
    semaphore = asyncio.Semaphore(MAX_WORKERS)
    BATCH_SIZE = 200
    
    while True:
        with bf.BlobFile(
            "az://orngscuscresco/data/damajercak/swe/upload05202025/rcr_12878/train_hq/rcr_12878_train_hq_junit.jsonl",
            "r",
        ) as f:
            samples = [json.loads(line) for line in f]

        OUTPUT_DIR = "az://orngscuscresco/data/haoranxu/swe/swe_vsc/rcr_12878/train_hq/rcr_12878_train_hq_junit.jsonl"

        try:
            with bf.BlobFile(
                OUTPUT_DIR,
                "r",
            ) as f:
                seen_ids = set([json.loads(line)["unique_id"] for line in f])
        except:
            seen_ids = set()

        print(f"There are total unprocessed samples: {len(samples) - len(seen_ids)}")
        samples = [sample for sample in samples if sample["unique_id"] not in seen_ids]
        samples = samples[:BATCH_SIZE]
        
        if not samples:
            print("No more samples to process.")
            break
        # samples = [sample for sample in samples if sample["unique_id"] in failed]
        tasks = {
            sample["unique_id"]: asyncio.create_task(extract_project_structure(endpoint, sample, semaphore))
            for sample in samples
        }

        # Use tqdm to track progress
        results = []
        with tqdm(total=len(tasks), desc="Processing samples") as pbar:
            for task in asyncio.as_completed(tasks.values()):
                try:
                    result = await task
                    results.append(result)
                except Exception as e:
                    results.append(e)
                pbar.update(1)
        
        # Map results back to unique_ids
        completed_tasks = await asyncio.gather(*tasks.values(), return_exceptions=True)
        read_project_structure_map = {uid: res for uid, res in zip(tasks.keys(), completed_tasks)}

        failure_cnt = 0
        with bf.BlobFile(
            OUTPUT_DIR,
            "a",
        ) as f:
            for sample in samples:
                try:
                    project_structure = read_project_structure_map[sample["unique_id"]][0]
                    project_structure = extract_code_from_last_block(project_structure)[0]
                    # print(f"{project_structure=}", type(project_structure))
                    if isinstance(project_structure, str):
                        sample["metadata"]["project_structure"] = project_structure
                        sample["problem"] = sample["problem"].replace(
                            " by running `bash run_tests.sh`",
                            ""
                        )
                        print(sample["problem"])
                        f.write(json.dumps(sample) + "\n")
                except Exception as e:
                    print(f"error processing sample {sample['unique_id']}: {e}")
                    failure_cnt += 1

        print(f"Failed to extract project structure for {failure_cnt} samples out of {len(samples)}")


if __name__ == "__main__":
    asyncio.run(Smokey(main))
