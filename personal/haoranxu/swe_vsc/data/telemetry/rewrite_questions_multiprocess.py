import blobfile as bf
from bus_token_completer.bus_token_completer import BusTokenCompleter
from bus.qos_type import QoSType
from chat.render import get_renderer
import chat
import json
import argparse
import random
import re
from tqdm import tqdm
import multiprocessing as mp
from functools import partial

# Global variables for multiprocessing
bus_token_completer = None
renderer = None

def init_worker():
    """Initialize global resources for each worker process"""
    global bus_token_completer, renderer
    bus_token_completer = BusTokenCompleter(
        topic_or_snapshot="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas",
        topic_mode_or_user="gpt-oss-rkld",
        bus_line="bus",
        qos_type=QoSType.ROUND_ROBIN_BY_POD,
    )
    renderer = get_renderer("harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2")

def load_all_style_examples(style_file_path):
    """Load all style examples from the style file"""
    with bf.BlobFile(style_file_path, "r") as f:
        style_data = [json.loads(line) for line in f]
    return [example["question"] for example in style_data]

def sample_style_examples(all_examples, num_examples=20):
    """Randomly sample style examples"""
    return random.sample(all_examples, min(num_examples, len(all_examples)))

def create_rewrite_prompt(original_problem, style_examples):
    """Create a prompt for rewriting the problem statement"""
    style_examples_text = "\n".join([f"Example {i+1}: {example}" for i, example in enumerate(style_examples)])

    prompt = f"""
Your task is to take a formal or official-sounding technical problem statement and rewrite it so that it matches 
the style of real user questions asked in VS Code to an LLM assistant.  

Rules:
1. Match the style of the example questions below.
2. You must preserve the original technical goal and meaning.
3. Make the wording natural and consistent with how users typically ask in VS Code.
4. Use the same language as the original question (e.g., English in → English out, Chinese in → Chinese out).
5. You may rewrite the question using just a few words or sentences. Add more details only if you think 
   the model would not be able to solve the problem without them.
6. You do not need to include hints from the original problem (such as examples, action items, or step-by-step notes) 
   in the rewritten version, unless the model would not be able to solve the problem without them.
7. You should NOT carefully format the rewritten question (for example, listing items with "-"). Keep it casual.
8. You MUST NOT list items! e.g.,
    - Look at file A
    - Modify file B
    - Run command C
    Instead, summarize them in a short sentence.

Below are example questions in the target style:
{style_examples_text}

Please rewrite the following problem statement so it follows the rules above.

Original problem statement:
{original_problem}

Again, You MUST NOT list items!

Rewritten problem statement:
"""

    
    return prompt

def rewrite_problem_with_llm(original_problem, style_examples):
    """Use LLM to rewrite the problem statement"""
    global bus_token_completer, renderer
    
    prompt = create_rewrite_prompt(original_problem, style_examples)
    
    messages = [chat.Message.user(content=prompt)]
    convo = chat.Conversation(messages=messages)
    tokens = renderer.render_for_completion_multimodal_toklist(convo, role=chat.Role.ASSISTANT)

    for attempt in range(5):
        try:
            # Retry logic to handle potential issues with the completion
            completion = bus_token_completer.completion(
                [tokens],
                max_tokens=32768,
                stop=[[200002]],
                temperature=1.0,
            )
            
            response_tokens = completion.choices[0].toklist.spans[0].tokens
            output_messages = renderer.decode(response_tokens)
            # Extract the rewritten text
            if output_messages:                
                # Extract content between delimiters using regex
                pattern = r'<\|meta_sep\|>final<\|im_sep\|>(.*)'
                match = re.search(pattern, output_messages, re.DOTALL)

                if match:
                    extracted_content = match.group(1).strip()
                    
                    # Further extract everything before <|im_end|> if it exists
                    if '<|im_end|>' in extracted_content:
                        extracted_content = extracted_content.split('<|im_end|>')[0].strip()
                    
                    return extracted_content
                else:
                    # Fallback to original content if delimiters not found
                    return ""

        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {e}")
            if attempt == 4:  # Last attempt
                return original_problem  # Return original if all attempts fail
    
    return original_problem

def process_single_datapoint(args_tuple):
    """Process a single datapoint - designed for multiprocessing"""
    datapoint, all_style_examples, num_style_examples, sbhv2 = args_tuple
    
    try:
        # Extract original problem
        if sbhv2:
            original_problem = datapoint.get("metadata", {}).get("problem_statement", "")
        else:
            original_problem = datapoint.get("problem", "")
        
        if not original_problem:
            print(f"Warning: No problem statement found for datapoint")
            return None
        
        # Sample different style examples for each problem
        style_examples = sample_style_examples(all_style_examples, num_style_examples)
        
        # Rewrite the problem
        rewritten_problem = rewrite_problem_with_llm(original_problem, style_examples)

        if not rewritten_problem:
            return None
        
        # Update the datapoint
        updated_datapoint = datapoint.copy()
        # Replace unique_id with original_unique_id + "rewrite" + 10 random letters
        original_uid = datapoint.get("unique_id", "")
        rand_suffix = "".join(random.choices("abcdefghijklmnopqrstuvwxyz", k=10))
        if original_uid:
            updated_datapoint["unique_id"] = f"{original_uid}rewrite{rand_suffix}"
        else:
            updated_datapoint["unique_id"] = f"rewrite{rand_suffix}"
        if "metadata" not in updated_datapoint:
            updated_datapoint["metadata"] = {}
        
        if sbhv2:
            updated_datapoint["metadata"]["problem_statement"] = rewritten_problem
            updated_datapoint["metadata"]["original_problem_statement"] = original_problem
        else:
            updated_datapoint["problem"] = rewritten_problem
            updated_datapoint["metadata"]["original_problem"] = original_problem
        
        # Return both the updated datapoint and the example for display
        return {
            'datapoint': updated_datapoint,
            'example': {
                'original': original_problem,
                'rewritten': rewritten_problem
            }
        }
        
    except Exception as e:
        print(f"Error processing datapoint: {e}")
        return None  # Skip datapoint on error

def main():
    parser = argparse.ArgumentParser(description="Rewrite questions using LLM based on style examples")
    parser.add_argument(
        "--input_file", 
        default="az://orngscuscresco/data/jadhuang/swe/upload08182025/sbhv2/vsc/train/train.jsonl",
        help="Input JSONL file path"
    )
    parser.add_argument(
        "--output_file",
        default="az://orngscuscresco/data/haoranxu/swe/swe_vsc/sbhv2/telemetry_rewrite/train.jsonl",
        help="Output JSONL file path"
    )
    parser.add_argument(
        "--style_file",
        default="az://orngscuscresco/data/haoranxu/swe/swe_vsc/telemetry/user_questions/filtered_self_contained_prompts.jsonl",
        help="Style examples file path"
    )
    parser.add_argument(
        "--num_style_examples",
        type=int,
        default=50,
        help="Number of style examples to use"
    )
    parser.add_argument(
        "--sbhv2",
        action="store_true",
        help="Whether to use SBHV2 style"
    )
    parser.add_argument(
        "--num_processes",
        type=int,
        default=mp.cpu_count(),
        help="Number of processes to use for multiprocessing"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Debug mode: process only first 10 questions"
    )
    
    args = parser.parse_args()
    
    # Load all style examples once
    print("Loading style examples...")
    all_style_examples = load_all_style_examples(args.style_file)
    print(f"Loaded {len(all_style_examples)} total style examples")
    
    # Process input file
    print("Loading input data...")
    with bf.BlobFile(args.input_file, "r") as f:
        input_data = [json.loads(line) for line in f]
    
    # Debug mode: limit to first 10 questions
    if args.debug:
        input_data = input_data[:10]
        print(f"Debug mode: processing only first {len(input_data)} datapoints")
    
    print(f"Processing {len(input_data)} datapoints with {args.num_processes} processes...")
    
    # Prepare arguments for multiprocessing
    process_args = [(datapoint, all_style_examples, args.num_style_examples, args.sbhv2) 
                   for datapoint in input_data]
    
    # Process with multiprocessing
    processed_data = []
    rewrite_examples = []
    with mp.Pool(processes=args.num_processes, initializer=init_worker) as pool:
        results = list(tqdm(
            pool.imap(process_single_datapoint, process_args),
            total=len(process_args),
            desc="Rewriting problems"
        ))
        
        # Filter out None results and separate datapoints from examples
        for result in results:
            if result is not None:
                processed_data.append(result['datapoint'])
                rewrite_examples.append(result['example'])
    
    # Save results
    print(f"Saving {len(processed_data)} results to {args.output_file}...")
    with bf.BlobFile(args.output_file, "w") as f:
        for datapoint in processed_data:
            f.write(json.dumps(datapoint) + "\n")
    
    # Print 10 random examples
    if rewrite_examples:
        print("\n" + "="*80)
        print("RANDOM REWRITE EXAMPLES:")
        print("="*80)
        
        num_examples_to_show = min(10, len(rewrite_examples))
        random_examples = random.sample(rewrite_examples, num_examples_to_show)
        
        for i, example in enumerate(random_examples, 1):
            print(f"\nExample {i}:")
            print("-" * 40)
            print("BEFORE:")
            print(example['original'][:1500] + ("..." if len(example['original']) > 1500 else ""))
            print("\nAFTER:")
            print(example['rewritten'][:1500] + ("..." if len(example['rewritten']) > 1500 else ""))
            print("-" * 40)
    
    print("Done!")

if __name__ == "__main__":
    main()


