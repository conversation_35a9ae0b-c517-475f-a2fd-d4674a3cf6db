#!/usr/bin/env bash
set -euo pipefail

INPUTS=(data.haoranxu.swe.swe_vsc.rcr_12878.train_hq)
OUTPUTS=(data.haoranxu.swe.swe_vsc.telemetry.rewrite.sbhv1)

for i in "${!INPUTS[@]}"; do
    INPUT="${INPUTS[i]}"
    OUTPUT="${OUTPUTS[i]}"

    echo "Processing dataset:"
    echo "  Input:  $INPUT"
    echo "  Output: $OUTPUT"


    AZ_BASE="az://orngscuscresco"

    # Convert dotted logical names to az:// path (dots -> slashes)
    input_az="${AZ_BASE}/$(echo "$INPUT" | tr '.' '/')"
    output_az="${AZ_BASE}/$(echo "$OUTPUT" | tr '.' '/')"

    echo "Input folder: $input_az"
    echo "Output folder: $output_az"

    # Script to run for each file
    SCRIPT="$HOME/code/glass/personal/haoranxu/swe_vsc/data/telemetry/rewrite_questions_multiprocess.py"
    NUM_PROCS=64

    # Debug mode: enabled if DEBUG env var is set to 1/true, or if --debug is passed on the command line
    debug=0
    if [[ "${DEBUG:-}" == "1" || "${DEBUG:-}" == "true" ]]; then
    debug=1
    fi
    for arg in "$@"; do
    if [[ "$arg" == "--debug" ]]; then
        debug=1
    fi
    done
    if [ "$debug" -eq 1 ]; then
    echo "*** DEBUG MODE enabled: commands will only be printed, not executed"
    fi

    # Get list of jsonl files under the input az folder. Keep only lines that end with .jsonl
    # Use awk to pick the last column in case `bbb ls` prints other columns
    mapfile -t files < <(bbb ls "$input_az" | awk '{print $NF}' | grep -E '\.jsonl$' || true)

    if [ ${#files[@]} -eq 0 ]; then
    echo "No .jsonl files found under $input_az" >&2
    exit 1
    fi

    for f in "${files[@]}"; do
    # Normalize to full az path if the listing returned a basename
    input_file="$f"
    if [[ "$input_file" != az://* ]]; then
        input_file="${input_az%/}/$input_file"
    fi

    base="$(basename "$input_file")"
    output_file="${output_az%/}/$base"

    # Build the command as an array so we can safely print it (fully quoted) in debug mode
    cmd=(python "$SCRIPT" --input_file "$input_file" --output_file "$output_file" --num_processes "$NUM_PROCS")

    if [ "$debug" -eq 1 ]; then
        printf 'DEBUG: '
        printf '%q ' "${cmd[@]}"
        echo
    else
        echo "Running: ${cmd[*]}"
        "${cmd[@]}"
    fi

    done
done
exit 0


