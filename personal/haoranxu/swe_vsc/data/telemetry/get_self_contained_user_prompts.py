import json
import os

def filter_self_contained_prompts(input_file_path, output_file_path):
    """
    Filter datapoints based on word count and self-contained score criteria.
    
    Args:
        input_file_path: Path to the input JSONL file
        output_file_path: Path to save the filtered results
    """
    filtered_data = []
    
    with open(input_file_path, 'r') as file:
        for line_num, line in enumerate(file, 1):
            try:
                dt = json.loads(line.strip())
                
                # Extract self-contained score
                assistant_content = dt["messages"][-1]["content"]
                score_data = json.loads(assistant_content[7:-4])
                self_contained_score = score_data['user_request_selfcontained']
                
                # Extract user question
                user_question = dt["metadata"]["chat_records"][0]['prompt']
                
                # Filter by criteria
                word_count = len(user_question.split())
                if word_count > 5 and self_contained_score >= 10 and word_count < 300:
                    # Extract required fields
                    filtered_entry = {
                        "id": dt["metadata"]["chat_records"][0]["id"],
                        "question": user_question,
                        "self_contained_score": self_contained_score
                    }
                    filtered_data.append(filtered_entry)
                    
            except (<PERSON><PERSON><PERSON><PERSON>, IndexError, json.JSONDecodeError) as e:
                print(f"Error processing line {line_num}: {e}")
                continue
    
    # Save filtered results
    with open(output_file_path, 'w', encoding='utf-8') as output_file:
        for entry in filtered_data:
            json.dump(entry, output_file)
            output_file.write('\n')
    
    print(f"Filtered {len(filtered_data)} entries from the input file")
    print(f"Results saved to: {output_file_path}")

if __name__ == "__main__":
    # Set file paths
    input_file = "/root/code/glass/project/deep_swe_msft/3p_20k_rewrited_scored.jsonl"
    output_file = "/root/filtered_self_contained_prompts.jsonl"
    
    # Run the filtering
    filter_self_contained_prompts(input_file, output_file)
