from caas.api import caas_api
import asyncio
from caas.commands import RawExec
from caas.commands import UploadFile
from smokey import Smokey
from berry_rfs.mrfs_setup import get_strawberry_ace_token
from caas.terminal.api import TerminalSession
from deep_swe_msft.tools.vscode_copilot_tool import (
    exec,
)
import json
from deep_swe_msft.tools.utils import get_strawberry_ace_token
from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.mrfs_setup import mrfs_setup_fn_coreutils
import blobfile as bf
from mini.metrics import metrics_init
from deep_swe_msft.tools.vscode_copilot_tool import (
    exec,
    new_container,
    prepare_vsc_tool,
)
from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn
"""
Example usage:
time python caas_verify.py --endpoint=https://eastus2.caas.azure.com --image=aio
"""

# sbt-v2 image
# acrcommitcaaseastus2ame.azurecr.io/sweb.train.x86_64.networkx__networkx:c732e4
# sbv image:
# "acrbuiltincaasglobalame.azurecr.io/caas-swe-bench:20250115a-scikit-learn__scikit-learn-14141" 
HTTP_FILE_PATH = "https://codemodeldata.blob.core.windows.net/swe-model-training-data/github_repos_v2_training/wave1_2/wave1_1_old_rfs_default_rcs/merged_upload_repos"
metrics_init(config=None)
 
def print_stdout(a):
    print("=" * 100)
    print("stdout: ", a[1].decode())
    print("exit code: ", a[0])
    print("=" * 100)
    print("\n")
 
async def main(
        endpoint: str | None = None,
        image: str | None = None):
    endpoint="https://southcentralus.caas.azure.com"
    # image = "aio"
    # print(f"endpoint: {endpoint}")
    # print(f"image: {image}")
    # caas = caas_api(endpoint=endpoint)
    # caas_session = await caas.new_session(
    #     cpu_limit="50.0",
    #     memory_limit="200g",
    #     image="aio",
    #     disk_limit="32g",
    #     enable_network=True)
 
    try:
 
        # print("---> run basic test...")
        # a = await caas_session.run(
        #     RawExec(["bash", "-c", f'rm -rf /root/blueice; mkdir -p /root/blueice'], timeout=1200, enable_public_logging=True))
        # print_stdout(a)
        with bf.BlobFile(
            # "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/rrb_py_diverse_repo/merged_diverse_dataset_rrb_py.jsonl",
            # "az://orngscuscresco/data/qingruzhang/swe/rrb_if/rrb_py_v3/rrb_if_py_merged_diverse_dataset_allv3.jsonl",
            "az://orngscuscresco/data/zhendongw/swe_train/data_mix_v1/swb_train_rewritten_4x/swe_bench_train_updated.rewritten1.jsonl",
            "r",
        ) as f:
            samples = [json.loads(line) for line in f]
        sample = samples[0]
        caas_container = await new_container(endpoint, sample["metadata"]["docker_image"])
        caas_session = caas_container.caas_session
        await prepare_vsc_tool(caas_session, sample["metadata"]["cwd"])
        terminal_session = TerminalSession(caas_session, endpoint=endpoint)
        await swe_bench_v2_setup_fn(datapoint=sample, terminal_session=terminal_session)
        cmd = {
            # "command": f"cd $WORKSPACEFOLDER; (if [ -f run_tests.sh ]; then timeout 1800 bash run_tests.sh; else timeout 1800 pytest {files_str}; fi) || echo '{failure_msg}'",
            "command": f"cd {sample['metadata']['cwd']}; timeout 10 bash run_tests.sh || timeout 10 pytest",
            "explanation": f"Run tests in specific files",
            "isBackground": False,
        }
        out = await exec(caas_session, "run_in_terminal", cmd)
        print(out)
  
        # print("---> zip..")
        # a = await caas_session.run(
        #     RawExec(["bash", "-c", 'tar -czf testbed.tar.gz /testbed'], timeout=1200, enable_public_logging=True))
        # print_stdout(a)


        # print("---> run azcopy test...")
        # a = await caas_session.run(
        #     RawExec(["bash", "-c", f'curl -X PUT -T testbed.tar.gz -H "x-ms-blob-type: BlockBlob" "https:///orngcaas.blob.core.windows.net/data/haoranxu/swe/testbed/testbed.tar.gz?se=2025-07-30T08%3A53%3A30Z&sp=r&sv=2025-05-05&sr=c&skoid=11df29bd-b2df-45eb-b2a6-ba03b8c23f83&sktid=8b9ebe14-d942-49e7-ace9-14496d0caff0&skt=2025-07-30T07%3A53%3A30Z&ske=2025-07-30T08%3A53%3A30Z&sks=b&skv=2025-05-05&sig=jnacpMCzcGrlgscF/dFs4BMpPpYnY7BVLctXHxoXfuI%3D"'], timeout=1200, enable_public_logging=True))
        # print_stdout(a)
 
        # a = await caas_session.run(
        #     RawExec(["bash", "-c", f'cd /root/code; ls -al'], timeout=1200, enable_public_logging=True))
        # print_stdout(a)

        # project_structure = await exec(caas_session, "read_project_structure", {})
        # print_stdout(project_structure)
 
        # print("---> run setup test...")
        # a = await caas_session.run(
        #     RawExec(["bash", "-c", f'cd /root/blueice; bash run_tests.sh'], timeout=1200, enable_public_logging=True))
        # print_stdout(a)
 
    except Exception as e:
        print("Error: ", e)
    finally:
        await caas_session.close()
        print("caas_session closed")
 
 
if __name__ == "__main__":
    asyncio.run(Smokey(main))


# swt-v2
from caas.commands import HttpGet, HttpPost, RawBashScript, UploadFile
from caas.protocol import NetworkMode, Tmpfs, VolumeMount
from caas_tool.caas_container import CaasContainer
endpoint="https://eastus2.caas.azure.com"
mnt = [VolumeMount(host=f"/mnt/azure_blob/certs/mitmproxy-ca-cert.crt", container=f"/usr/local/share/ca-certificates/my_custom_certificate.crt")]
image="acrbuiltincaasglobalame.azurecr.io/caas-swe-bench:20250115a-scikit-learn__scikit-learn-14141"
caas_container = await CaasContainer.new(
    caas_endpoint=endpoint,
    image_name=image,
    # volume_mounts=mnt,
    disk_limit="32gb",
    cpu_limit="16.0",
    network=NetworkMode.BRIDGE,
)

# caas_container = await new_container(endpoint, "acrcommitcaaseastus2ame.azurecr.io/sweb.train.x86_64.networkx__networkx:c732e4", volume_mounts=mnt)
caas_session = caas_container.caas_session

code, message = await caas_session.run(
    RawBashScript(
        f"""
ls /usr/local/share/ca-certificates
""",
        timeout=600,
        enable_public_logging=True,
    ),
)

# code, message = await caas_session.run(
#     RawBashScript(
#         f"""
# apt install -y libnss3-tools

# NSSDB_DIR="$HOME/.pki/nssdb"
# rm -rf "$NSSDB_DIR"
# mkdir -p "$NSSDB_DIR"

# if [ -z "$(ls -A $NSSDB_DIR)" ]; then
# stderr "NSS database directory is empty. Initializing…"
# certutil -d "$NSSDB_DIR" -N --empty-password
# else
# stderr "NSS database already initialized."
# fi

# # this section adds all the certificates that exist in the directory
# for cert_file in "/usr/local/share/ca-certificates"/*.crt; do
# stderr "Adding certificate [$cert_file] to NSS database…"
# cert_name=$(basename "$cert_file" .crt)
# certutil -d "$NSSDB_DIR" -A -t "C,," -n "CaaS-$cert_name" -i "$cert_file"
# done
# """,
#         timeout=600,
#         enable_public_logging=True,
#     ),
# )
print(code)
print(message.decode("utf-8"))




## Debug runTests
import asyncio
import json

import blobfile as bf
import pytest
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from caas.commands import UploadFile, RawBashScript

from deep_swe_msft.tests.vsc_helpers import get_tool, invoke_tool, exist_text
from deep_swe_msft.tools.utils import CRESCO_STORAGE_NAME
from deep_swe_msft.tools.vscode_copilot_tool import (
    LANGUAGE_UNIFORMS,
    new_container,
    debug_startup,
)
from mini.metrics import metrics_init
from deep_swe_msft.tools.utils import CAAS_ENDPOINT

pytestmark = pytest.mark.asyncio

def _get_samples() -> dict:
    print("Loading sample data")
    with bf.BlobFile(
        f"az://{CRESCO_STORAGE_NAME}/data/luw/rfs_data/train_data/mrfs_caas/latest/python/train/rfs/3p/processed_train.jsonl",
        "r",
    ) as f:
        samples = [json.loads(line) for line in f]
    print(f"Loaded sample data, num_samples={len(samples)}")
    return samples

async def _get_container(sample: dict) -> CaasContainer:
    print("Creating CAAS container")
    container = await new_container(CAAS_ENDPOINT, "aio")
    print("CAAS container created, setting up VSC utils")

    language = "python"
    if "metadata" in sample:
        metadata = sample["metadata"]
        language = metadata.get("lang", None) or metadata.get("top_language", None) or "python"
    language = language.lower()
    if language in LANGUAGE_UNIFORMS:
        language = LANGUAGE_UNIFORMS[language]
    if language == "python":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "javascript":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.javascript.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "java":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.java.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "csharp":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "typescript":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.typescript.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    else:
        raise ValueError(f"Unsupported language: {language}")

    await mrfs_setup_fn_coreutils(
        datapoint=sample,
        terminal_session=TerminalSession(container.caas_session, endpoint=CAAS_ENDPOINT),
    )

    print("CAAS container created")
    return container

metrics_init(config=dict())
samples = _get_samples()
python_sample = [s for s in samples if "metadata" in s and s["metadata"]["repo_id"] == "0ddfell0w__cp_rfs_51c48082-ae27-4902-8b16-88e9a74673a6"][0]
python_container = await _get_container(python_sample)
python_tool = get_tool(python_container, 6000)
print(f"Tool is ready.")

run_result = await invoke_tool(
    python_tool, "runTests", { "files": [] }
)


###
import asyncio
import json

import blobfile as bf
import pytest
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from caas.commands import UploadFile, RawBashScript

from deep_swe_msft.tests.vsc_helpers import get_tool, invoke_tool, exist_text
from deep_swe_msft.tools.utils import CRESCO_STORAGE_NAME
from deep_swe_msft.tools.vscode_copilot_tool import (
    LANGUAGE_UNIFORMS,
    new_container,
    debug_startup,
)
from mini.metrics import metrics_init
from deep_swe_msft.tools.utils import CAAS_ENDPOINT

pytestmark = pytest.mark.asyncio

def _get_samples() -> dict:
    print("Loading sample data")
    with bf.BlobFile(
        f"az://{CRESCO_STORAGE_NAME}/data/luw/rfs_data/train_data/mrfs_caas/latest/python/train/rfs/3p/processed_train.jsonl",
        "r",
    ) as f:
        samples = [json.loads(line) for line in f]
    print(f"Loaded sample data, num_samples={len(samples)}")
    return samples

async def _get_container(sample: dict) -> CaasContainer:
    print("Creating CAAS container")
    container = await new_container(CAAS_ENDPOINT, "aio")
    print("CAAS container created, setting up VSC utils")

    language = "python"
    if "metadata" in sample:
        metadata = sample["metadata"]
        language = metadata.get("lang", None) or metadata.get("top_language", None) or "python"
    language = language.lower()
    if language in LANGUAGE_UNIFORMS:
        language = LANGUAGE_UNIFORMS[language]
    if language == "python":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "javascript":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.javascript.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "java":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.java.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "csharp":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "typescript":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.typescript.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    else:
        raise ValueError(f"Unsupported language: {language}")

    await mrfs_setup_fn_coreutils(
        datapoint=sample,
        terminal_session=TerminalSession(container.caas_session, endpoint=CAAS_ENDPOINT),
    )

    print("CAAS container created")
    return container


async def test_run_tests():
    metrics_init(config=dict())
    samples = _get_samples()
    python_sample = [s for s in samples if "metadata" in s and s["metadata"]["repo_id"] == "0ddfell0w__cp_rfs_51c48082-ae27-4902-8b16-88e9a74673a6"][0]
    python_container = await _get_container(python_sample)
    python_tool = get_tool(python_container, 10000)
    print(f"Tool is ready.")

    # Run tests
    run_result = await invoke_tool(
        python_tool, "runTests", { "files": [] }
    )
    assert exist_text("be/test/test_pokerMove.py::PokerMoveTest::test_move_strength_ordering failed:", run_result), f"Expected test failure not found in {run_result=}"

    run_result = await invoke_tool(
        python_tool, "runTests", { "files": ["/root/code/be/test/test_kindMove.py"] }
    )
    assert exist_text("No test failures were found.", run_result), f"Expected no test failures in {run_result=}"

    run_result = await invoke_tool(
        python_tool, "runTests", { "files": ["/root/code/be/test/test_round.py"] }
    )
    assert exist_text("be/test/test_round.py::RoundTest::test_append_first_move_kind_move failed:", run_result), f"Expected test failures not found in {run_result=}"

    # Create a long-running test to ensure the timeout works
    long_test = """import unittest
import time
from ..poker_move import PokerMove
from .utils import CustomAssertions
INVALID = "3S 4S 5S 6S 2H"
STRAIGHT = "3S 4C 5D 6S 7H"
FLUSH = "3S JS QS KS 7S"
FULL_HOUSE = "3S 3H 3C 7D 7C"
FOUR_OF_A_KIND = "3S 3H 3C 3D 7S"
STRAIGHT_FLUSH = "7H 6H 5H 4H 3H"
class PokerMoveTest(unittest.TestCase, CustomAssertions):
  def test_move_strength_ordering(self):
    time.sleep(20)
    self.assert_in_order_from_string(PokerMove, [
      INVALID,
      STRAIGHT,
      FLUSH,
      FULL_HOUSE,
      FOUR_OF_A_KIND,
      STRAIGHT_FLUSH,
    ])
if __name__ == '__main__':
  unittest.main()
"""

    await python_container.caas_session.run(UploadFile(
        path="/root/code/be/test/test_long_run.py",
        data=long_test.encode(),
    ))

    long_run_result = await invoke_tool(
        python_tool, "runTests", { "files": ["/root/code/be/test/test_long_run.py"] }
    )
    assert exist_text("VSCode Copilot tool error: Command failed because it timed out.", long_run_result), f"Expected long-running test failure not found in {long_run_result=}"

    await python_container.caas_session.run(RawBashScript(f"rm /root/code/be/test/test_long_run.py"))
    run_all_result = await invoke_tool(
        python_tool, "runTests", { "files": [] }
    )
    assert exist_text("be/test/test_deck.py::DefaultDeckTest::test_get_hands_3_players_3D_excluded failed:", run_all_result), f"Expected test failures not found in: {run_all_result=}"
    print(f"Run all runTests passed.")


async def test_run_tests_for_all():
    metrics_init(config=dict())
    tasks = [asyncio.create_task(_run_single_test(sample)) for sample in _get_samples()[:5]]
    run_results = await asyncio.gather(*tasks, return_exceptions=True)
    for run_result in run_results:
        assert not isinstance(run_result, Exception), f"Expected run_tests to succeed, but got exception: {run_result}"
        assert not exist_text("No test failures were found.", run_result), f"Expected test failures in {run_result=}"
        assert not exist_text("VSCode Copilot tool error", run_result), f"Expected no tool errors in {run_result=}"
        assert exist_text("<testResult passed=false>", run_result), f"Expected test failures in {run_result=}"

async def _run_single_test(sample):
    python_container = await _get_container(sample)
    python_tool = get_tool(python_container, 10000)
    print(f"Tool is ready.")
    return await invoke_tool(
        python_tool, "runTests", { "files": [] }
    )


async def test_manage_todo_list_features():
    """Integration-style test for mock manage_todo_list tool using a CAAS container & sample.

    Steps:
    1. Acquire one python sample & container (mirrors test_run_tests).
    2. Invoke manage_todo_list write with an initial list (one in-progress, one completed).
    3. Read back and verify markdown structure & status markers.
    4. Write with multiple in-progress + non-sequential IDs to trigger auto-corrections and warnings.
    5. Read back and verify only first in-progress, IDs sequential, warning presence in write response.
    6. Perform a large change write to trigger bulk change warning (>3 changes).
    7. Validate invalid status error path.
    """
metrics_init(config=dict())
samples = _get_samples()
python_sample = [s for s in samples if "metadata" in s and s["metadata"].get("repo_id") == "0ddfell0w__cp_rfs_51c48082-ae27-4902-8b16-88e9a74673a6"][0]
container = await new_container(CAAS_ENDPOINT, "aio")
tool = get_tool(container, 10000)  # VSCodeTool instance

    # Helper to invoke manage_todo_list and just get the string output
async def invoke_manage(params):
    return await invoke_tool(tool, "manage_todo_list", params)

    # 1. Initial write
initial_payload = [
    {"id": 1, "title": "Read file", "description": "Open source", "status": "not-started"},
    {"id": 2, "title": "Implement feature", "description": "Do work", "status": "in-progress"},
    {"id": 3, "title": "Review", "description": "Check logic", "status": "completed"},
]
    write_result = await invoke_manage({"operation": "write", "todoList": initial_payload})
    assert "Successfully wrote todo list" in write_result

    # 2. Read and verify markdown structure
    read_result = await invoke_manage({"operation": "read"})
    assert read_result.startswith("# Task List"), f"Unexpected heading: {read_result[:40]}"
    assert "- [ ] Read file" in read_result
    assert "- [-] Implement feature" in read_result
    assert "- [x] Review" in read_result

    # 3. Write with multiple in-progress + non-sequential IDs
    multi_payload = [
        {"id": 10, "title": "First", "description": "a", "status": "in-progress"},
        {"id": 11, "title": "Second", "description": "b", "status": "in-progress"},
        {"id": 30, "title": "Third", "description": "c", "status": "not-started"},
    ]
    multi_write = await invoke_manage({"operation": "write", "todoList": multi_payload})
    assert "Multiple in-progress items" in multi_write
    assert "reindexed" in multi_write  # IDs adjusted

    multi_read = await invoke_manage({"operation": "read"})
    # Only first should remain in-progress after reindexing => becomes id=1 line order preserved
    assert "- [-] First" in multi_read
    # Second demoted
    assert "- [ ] Second" in multi_read

    # 4. Bulk change warning (>3 changes)
    bulk_payload = [
        {"id": 1, "title": "F1", "description": "a*", "status": "completed"},
        {"id": 2, "title": "F2", "description": "b*", "status": "not-started"},
        {"id": 3, "title": "F3", "description": "c*", "status": "in-progress"},
        {"id": 4, "title": "NEW", "description": "new", "status": "not-started"},
    ]
    bulk_write = await invoke_manage({"operation": "write", "todoList": bulk_payload})
    assert "update so many todos" in bulk_write

    # 5. Validation error (invalid status)
    invalid_payload = [
        {"id": 1, "title": "Bad", "description": "", "status": "weird"},
    ]
    invalid_write = await invoke_manage({"operation": "write", "todoList": invalid_payload})
    assert "Invalid status" in invalid_write

    # 6. Validation error (empty title)
    empty_title_payload = [
        {"id": 1, "title": " ", "description": "", "status": "not-started"},
    ]
    empty_title_write = await invoke_manage({"operation": "write", "todoList": empty_title_payload})
    assert "Empty title" in empty_title_write
