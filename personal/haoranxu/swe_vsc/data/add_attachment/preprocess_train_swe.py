import asyncio
import json
import time
import re

import blobfile as bf
import structlog
from caas.commands import BashScript, RawBashScript
from caas.terminal.api import TerminalSession
from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn
from deep_swe_msft.tools.vscode_copilot_tool import (
    exec,
    new_container,
    prepare_vsc_tool,
)
from mini.metrics import metrics_init
from tqdm.asyncio import tqdm

from smokey import Smokey
from deep_swe_msft.rfs_rcs_bb_ml_vsc.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK


metrics_init(config=None, intial_step=0)
logger = structlog.stdlib.get_logger(component=__name__)


async def extract_attachement(endpoint, sample, semaphore):
    async with semaphore:
        try:
            caas_container = await new_container(endpoint, sample["metadata"]["docker_image"])
            caas_session = caas_container.caas_session
            await prepare_vsc_tool(caas_session, sample["metadata"]["cwd"])
            terminal_session = TerminalSession(caas_session, endpoint=endpoint)
            await swe_bench_v2_setup_fn(datapoint=sample, terminal_session=terminal_session)


            command = f"""cd {sample["metadata"]["cwd"]} && \\
            echo "===PROJECT_STRUCTURE_START===" && \\
            find . -type f \\( -name "*.py" -o -name "*.cs" -o -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.java" -o -name "*.md" -o -name "*.txt" -o -name "*.yml" -o -name "*.yaml" -o -name "*.json" -o -name "*.toml" -o -name "*.cfg" -o -name "*.ini" -o -name "*.csproj" -o -name "*.sln" -o -name "package.json" -o -name "tsconfig.json" -o -name "pom.xml" -o -name "build.gradle" \\) | grep -v -E "__pycache__|node_modules|bin|obj|target|dist|build|.git" | sort && \\
            echo "===PROJECT_STRUCTURE_END===" && \\
            echo "===FILE_CONTENTS_START===" && \\
            files=$(find . -type f \\( -name "*.py" -o -name "*.cs" -o -name "*.ts" -o -name "*.js" -o -name "*.java" -o -name "README*" -o -name "setup.py" -o -name "*.cfg" -o -name "*.ini" -o -name "package.json" -o -name "tsconfig.json" -o -name "pom.xml" -o -name "*.csproj" \\) | grep -v -E "__pycache__|test_|spec\\.|Test\\.|node_modules|bin|obj|target|dist|build|.git" | head -30 | shuf | head -3) && \\
            for file in $files; do \\
                echo "===FILE_START:$file===" && \\
                head -500 "$file" 2>/dev/null && \\
                echo "===FILE_END:$file==="; \\
            done && \\
            echo "===FILE_CONTENTS_END===" """

            cmd = {
                "command": command,
                "explanation": "get attachment files",
                "isBackground": False,
            }
            project_structure = await exec(caas_session, "read_project_structure", {})
            output = await exec(caas_session, "run_in_terminal", cmd)
            print(output)
            exit(0)
            return output
        except Exception as e:
            import traceback

            tb_str = traceback.format_exc()
            print(tb_str)
            print(sample["unique_id"])

            # import trainflow.utils; trainflow.utils.debugpy_breakpoint(remote=True)
            raise e


async def main():
    endpoint: str = "https://eastus2.caas.azure.com"
    MAX_WORKERS = 64  # Adjust this number based on your system capacity
    semaphore = asyncio.Semaphore(MAX_WORKERS)
    BATCH_SIZE = 100
    
    for ind in range(1, 2):
        while True:
            with bf.BlobFile(
                f"az://orngscuscresco/data/haoranxu/swe/swe_vsc/swb_train_rewritten_4x/swe_bench_train_updated/rewritten{ind}.jsonl",
                "r",
            ) as f:
                samples = [json.loads(line) for line in f]

            OUTPUT_DIR = f"az://orngscuscresco/data/haoranxu/swe/swe_vsc/swb_train_rewritten_4x/swe_bench_train_updated_v2_0729/rewritten{ind}.jsonl"

            try:
                with bf.BlobFile(
                    OUTPUT_DIR,
                    "r",
                ) as f:
                    seen_ids = set([json.loads(line)["unique_id"] for line in f])
            except:
                seen_ids = set()

            print(f"There are total unprocessed samples: {len(samples) - len(seen_ids)}")
            samples = [sample for sample in samples if sample["unique_id"] not in seen_ids]
            samples = samples[:BATCH_SIZE]
            
            if not samples:
                print("No more samples to process.")
                break
            # samples = [sample for sample in samples if sample["unique_id"] in failed]
            tasks = {
                sample["unique_id"]: asyncio.create_task(extract_attachement(endpoint, sample, semaphore))
                for sample in samples
            }

            # Use tqdm to track progress
            results = []
            with tqdm(total=len(tasks), desc="Processing samples") as pbar:
                for task in asyncio.as_completed(tasks.values()):
                    try:
                        result = await task
                        results.append(result)
                    except Exception as e:
                        results.append(e)
                    pbar.update(1)
            
            # Map results back to unique_ids
            completed_tasks = await asyncio.gather(*tasks.values(), return_exceptions=True)
            read_attachment_map = {uid: res for uid, res in zip(tasks.keys(), completed_tasks)}

            failure_cnt = 0
            with bf.BlobFile(
                OUTPUT_DIR,
                "a",
            ) as f:
                for sample in samples:
                    try:
                        result = read_attachment_map[sample["unique_id"]]
                        if isinstance(result, tuple) and len(result) > 0:
                            raw_output = result[0]
                            
                            # Initialize attachments dict
                            attachments = {}
                            
                            # Extract file contents from the output
                            if raw_output and isinstance(raw_output, str):
                                # Extract file contents between markers
                                file_pattern = r'===FILE_START:(.*?)===(.*?)===FILE_END:.*?==='
                                file_matches = re.findall(file_pattern, raw_output, re.DOTALL)
                                
                                for filepath, content in file_matches:
                                    filepath = filepath.strip()
                                    content = content.strip()
                                    if filepath and content:
                                        # Remove leading ./ if present
                                        clean_path = filepath.lstrip('./')
                                        attachments[clean_path] = content
                            
                            # Add attachments to sample metadata
                            if "metadata" not in sample:
                                sample["metadata"] = {}
                            sample["metadata"]["attachments"] = attachments
                            
                            # Write the updated sample
                            f.write(json.dumps(sample) + "\n")
                        else:
                            raise Exception(f"Invalid result format: {result}")
                        
                    except Exception as e:
                        print(f"error processing sample {sample['unique_id']}: {e}")
                        failure_cnt += 1

            print(f"Failed to extract project structure for {failure_cnt} samples out of {len(samples)}")


if __name__ == "__main__":
    asyncio.run(Smokey(main))
