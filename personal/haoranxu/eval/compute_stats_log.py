import os
import json
import argparse
from collections import defaultdict
import re
from statistics import median


_pattern_title = re.compile(r'<pr_title>\s*(.*?)\s*</pr_title>', re.DOTALL)
_pattern_desc = re.compile(r'<pr_description>\s*(.*?)\s*</pr_description>', re.DOTALL)

def is_valid_pr(text: str) -> bool:
    """
    Return True if `text` contains non-empty content between
    <pr_title>...</pr_title> and <pr_description>...</pr_description>.
    """
    title_match = _pattern_title.search(text)
    desc_match = _pattern_desc.search(text)
    return bool(
        title_match and title_match.group(1).strip() and
        desc_match and desc_match.group(1).strip()
    )

# Set up argument parsing
parser = argparse.ArgumentParser(description="Compute statistics from log folders")
parser.add_argument("--log-folder", default="./gpt-log", 
                    help="Path to the folder containing log subfolders (default: ./gpt-log)")
args = parser.parse_args()

# Use the provided log folder path
log_folder_path = args.log_folder

# Variables to store data for calculations
subfolder_stats = []  # List of (rounds, token_count) for each subfolder
package_stats = defaultdict(lambda: {"rounds": 0, "token_count": 0, "subfolder_count": 0, "progress_reported": 0, "valid_pr": 0, "invalid_recipient": 0})

# Track subfolder with most progress reports
max_progress_subfolder = ""
max_progress_count = 0

# Iterate over each subfolder in the log folder
count_files_with_all_episodes = 0
good_bash_tool_calls = 0
all_bash_tool_calls = 0
good_str_tool_calls = 0
all_str_tool_calls = 0
report_progress_in_all_episodes = 0
for subfolder in os.listdir(log_folder_path):
    subfolder_path = os.path.join(log_folder_path, subfolder)
    
    # Skip if it's not a directory
    if not os.path.isdir(subfolder_path):
        continue

    rounds = 0
    token_count = 0
    progress_reported = 0
    valid_pr = 0
    valid_recipient = 0
    invalid_recipient = 0

    # Process all episode files in the subfolder
    for file in os.listdir(subfolder_path):
        if file.startswith("episode_") and file.endswith(".jsonl"):
            file_path = os.path.join(subfolder_path, file)
            rounds += 1
            with open(file_path, 'r') as f:
                for line in f:
                    log_entry = json.loads(line.strip())
                    token_count += log_entry.get("metadata", {}).get("message_content_token_count", 0)
                    progress_reported += 1 if log_entry.get("recipient", {}) == "functions.report_progress" else 0
                    valid_pr += 1 if is_valid_pr(log_entry.get("content", {}).get("parts", [""])[0]) else 0
                    invalid_recipient += 1 if log_entry.get("recipient", {}) == "container.exec" else 0
                    if log_entry.get("author", {}).get("role", "") == "tool" and log_entry.get("author", {}).get("name", "") == "functions.bash":
                        all_bash_tool_calls += 1
                        good_bash_tool_calls += 1 if log_entry.get("metadata", {}).get("exec_status_code", "") == 0 else 0
                    # "functions.str_replace_editor"
                    elif log_entry.get("author", {}).get("role", "") == "tool" and log_entry.get("author", {}).get("name", "") == "functions.str_replace_editor":
                        all_str_tool_calls += 1
                        good_str_tool_calls += 1 if log_entry.get("metadata", {}).get("exec_status_code", "") == 0 else 0

    if os.path.exists(os.path.join(subfolder_path, "all_episodes.jsonl")):
        # If all_episodes.jsonl exists, process it
        valid_pr = 0
        progress_reported = 0
        all_episodes_path = os.path.join(subfolder_path, "all_episodes.jsonl")
        count_files_with_all_episodes += 1
        
        with open(all_episodes_path, 'r') as f:
            for line in f:
                log_entry = json.loads(line.strip())
                progress_reported += 1 if log_entry.get("recipient", {}) == "functions.report_progress" else 0
                report_progress_in_all_episodes += 1 if log_entry.get("recipient", {}) == "functions.report_progress" else 0
            valid_pr += 1 if is_valid_pr(log_entry.get("content", {}).get("parts", [""])[0]) else 0
        # if valid_pr > 1:
        #     more_than_one_pr.append(subfolder)
    # Update max progress tracking
    if progress_reported > max_progress_count:
        max_progress_count = progress_reported
        max_progress_subfolder = subfolder
                    
    # Record stats for the subfolder
    subfolder_stats.append((rounds, token_count, progress_reported, valid_pr, valid_recipient, invalid_recipient))
    
    # Extract package name (string before "__")
    package_name = subfolder.split("__")[0]
    package_stats[package_name]["rounds"] += rounds
    package_stats[package_name]["token_count"] += token_count
    package_stats[package_name]["subfolder_count"] += 1
    package_stats[package_name]["progress_reported"] += progress_reported
    package_stats[package_name]["valid_pr"] += valid_pr
    package_stats[package_name]["invalid_recipient"] += invalid_recipient

# Calculate mean rounds and token_count across all subfolders
total_rounds = sum(r for r, t, p, v, vr, ir in subfolder_stats)
total_token_count = sum(t for r, t, p, v, vr, ir in subfolder_stats)
total_progress_reported = sum(p for r, t, p, v, vr, ir in subfolder_stats)
total_valid_pr = sum(v for r, t, p, v, vr, ir in subfolder_stats)
total_invalid_recipient = sum(ir for r, t, p, v, vr, ir in subfolder_stats)
mean_rounds_all = total_rounds / len(subfolder_stats) if subfolder_stats else 0
mean_token_count_all = total_token_count / len(subfolder_stats) if subfolder_stats else 0
mean_progress_reported_all = total_progress_reported / len(subfolder_stats) if subfolder_stats else 0
median_progress_reported_all = median([p for r, t, p, v, vr, ir in subfolder_stats])
mean_valid_pr_all = total_valid_pr / len(subfolder_stats) if subfolder_stats else 0
mean_invalid_recipient_all = total_invalid_recipient / len(subfolder_stats) if subfolder_stats else 0


# Calculate mean rounds and token_count across packages
package_means = {}
for package, stats in package_stats.items():
    if stats["subfolder_count"] > 0:
        package_means[package] = {
            "mean_rounds": stats["rounds"] / stats["subfolder_count"],
            "mean_token_count": stats["token_count"] / stats["subfolder_count"],
            "progress_reported": stats["progress_reported"] / stats["subfolder_count"],
            "valid_pr": stats["valid_pr"] / stats["subfolder_count"],
            "invalid_recipient": stats["invalid_recipient"] / stats["subfolder_count"]
        }

# Report the results
print(f"Mean number of rounds across all subfolders: {mean_rounds_all}")
print(f"Mean token count across all subfolders: {mean_token_count_all}")
# print(f"Mean progress reported across all subfolders: {mean_progress_reported_all}")
# print(f"Median progress reported across all subfolders: {median_progress_reported_all}")
## mean, median, min, max of progress reported in one line:
print(f"Mean progress reported across all subfolders: {mean_progress_reported_all}, in all episodes: {report_progress_in_all_episodes/count_files_with_all_episodes if count_files_with_all_episodes > 0 else 0} Median: {median_progress_reported_all}, Min: {min(p for r, t, p, v, vr, ir in subfolder_stats)}, Max: {max(p for r, t, p, v, vr, ir in subfolder_stats)}")
print(f"Mean valid PR across all subfolders: {mean_valid_pr_all}")
print(f"Mean invalid recipient across all subfolders: {mean_invalid_recipient_all}")
print(f"Valid PR in all_episodes.jsonl: {0 if count_files_with_all_episodes==0 else total_valid_pr/count_files_with_all_episodes}")
print(f"Good bash tool calls: {good_bash_tool_calls}, All tool calls: {all_bash_tool_calls}, Ratio: {good_bash_tool_calls/all_bash_tool_calls}")
print(f"Good str tool calls: {good_str_tool_calls}, All tool calls: {all_str_tool_calls}, Ratio: {good_str_tool_calls/all_str_tool_calls}")


print("\nMean stats by package:")
for package, means in package_means.items():
    print(f"- {package}: Mean Rounds = {means['mean_rounds']}, Mean Token Count = {means['mean_token_count']}, Progress Reported = {means['progress_reported']}, Valid PR = {means['valid_pr']}, Invalid Recipient = {means['invalid_recipient']}")

# Print the subfolder with highest progress reports
print(f"\nSubfolder with most progress reports: {max_progress_subfolder} (count: {max_progress_count})")
# print(f"Subfolders with more than one PR: {more_than_one_pr}")
