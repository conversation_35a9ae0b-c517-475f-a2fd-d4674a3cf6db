import os
import json
from collections import defaultdict
import argparse

# Path to the 'outputs' folder
parser = argparse.ArgumentParser()
parser.add_argument(
    "--root-output-dir",
    type=str,
    default="./outputs",
    help="Path to the outputs folder containing JSON reports.",
)
args = parser.parse_args()

# Set the path to the outputs folder
outputs_folder = args.root_output_dir

# Initialize variables
total_resolved = 0
total_count = 0
package_resolved_counts = defaultdict(lambda: [0, 0])  # {package: [resolved_count, total_count]}

# Iterate through subfolders
for subfolder in os.listdir(outputs_folder):
    subfolder_path = os.path.join(outputs_folder, subfolder)
    if os.path.isdir(subfolder_path):  # Ensure it's a directory
        report_file = os.path.join(subfolder_path, "report.json")
        if os.path.exists(report_file):
            # Load the JSON file
            with open(report_file, "r") as f:
                report_data = json.load(f)
            
            # Extract package name and resolved status
            package_name = subfolder.split("__")[0]  # Extract string before `__`
            if subfolder in report_data and 'resolved' in report_data[subfolder]:
                resolved = report_data[subfolder]['resolved']
                total_resolved += int(resolved)
                total_count += 1
                package_resolved_counts[package_name][0] += int(resolved)
                package_resolved_counts[package_name][1] += 1

# Calculate overall mean resolved rate
mean_resolved_rate = total_resolved / total_count if total_count > 0 else 0

# Calculate mean resolved rate per package
package_resolved_rates = {
    package: counts[0] / counts[1] if counts[1] > 0 else 0
    for package, counts in package_resolved_counts.items()
}

# Output results

print("Resolved Rate and Counts Per Package:")
for package, counts in package_resolved_counts.items():
    rate = counts[0] / counts[1] if counts[1] > 0 else 0
    print(f"  {package}:")
    print(f"    Resolved Rate: {rate:.2f}")
    print(f"    Count: {counts[1]}")

# Print total resolved and count for all subfolders
print("\nSummary:")
print(f"Total Resolved Count: {total_resolved}")
print(f"Total Instance Count: {total_count}")
print(f"Overall Mean Resolved Rate: {mean_resolved_rate}\n")
# print(f"  Total Subfolder Count: {total_count}")
