import os
import json
from collections import defaultdict
import argparse

# Path to the 'outputs' folder
parser = argparse.ArgumentParser()
parser.add_argument(
    "--root-output-dir",
    type=str,
    default="./",
    help="Path to the root output folder",
)
args = parser.parse_args()

# Set the path to the gpt log folder
gpt_log_folder = args.root_output_dir + '/gpt-log'

# Iterate through subfolders
subfolder_to_episodes = defaultdict(list)
for subfolder in os.listdir(gpt_log_folder):
    subfolder_path = os.path.join(gpt_log_folder, subfolder)
    if os.path.isdir(subfolder_path):  # Ensure it's a directory
        for episode_file in os.listdir(subfolder_path):
            if episode_file.endswith(".jsonl"):
                episode_file_path = os.path.join(subfolder_path, episode_file)
                episode_data = []
                with open(episode_file_path, "r") as f:
                    for line in f:
                        episode_dict = json.loads(line)
                        episode_data.append(episode_dict)
                episode = {"episode_file": episode_file, "episode_data": episode_data}
                subfolder_to_episodes[subfolder].append(episode)

# Calculate statistics
subfolder_avg_num_episodes = sum(
    len(episodes) 
    for episodes in subfolder_to_episodes.values()
) / len(subfolder_to_episodes)
subfolder_min_num_episodes = min(len(episodes) for episodes in subfolder_to_episodes.values())
subfolder_max_num_episodes = max(len(episodes) for episodes in subfolder_to_episodes.values())
subfolder_avg_num_messages = sum(
    len(episode["episode_data"]) 
    for episodes in subfolder_to_episodes.values() 
    for episode in episodes
) / len(subfolder_to_episodes)
subfolder_min_num_messages = min(
    sum(len(episode["episode_data"]) for episode in episodes)
    for episodes in subfolder_to_episodes.values()
)
subfolder_max_num_messages = max(
    sum(len(episode["episode_data"]) for episode in episodes)
    for episodes in subfolder_to_episodes.values()
)


def get_message_content_length(message):
    # Ignore system and user messages
    if message['author']['role'] in ('system', 'user', 'tool'):
        return 0
    assert message['author']['role'] in ('assistant')

    content_type = message['content']['content_type']
    if content_type == 'text':
        return len(''.join(message['content']['parts']))
    elif content_type == 'code':
        return len(message['content']['text'])
    elif content_type == 'system_error':
        return 0
    else:
        raise ValueError(f"Unknown content type: {content_type}")
    
subfolder_avg_message_content_len = sum(
    get_message_content_length(message)
    for episodes in subfolder_to_episodes.values()
    for episode in episodes
    for message in episode["episode_data"]
) / len(subfolder_to_episodes)

print(f"Total number of subfolder: {len(subfolder_to_episodes)}")
print(f"Episodes - Avg: {subfolder_avg_num_episodes:.2f}, Min: {subfolder_min_num_episodes}, Max: {subfolder_max_num_episodes}")
print(f"Messages - Avg: {subfolder_avg_num_messages:.2f}, Min: {subfolder_min_num_messages}, Max: {subfolder_max_num_messages}")
print(f"Average chs: {subfolder_avg_message_content_len:.2f}")
