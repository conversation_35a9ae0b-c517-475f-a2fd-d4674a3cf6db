cd ~/code/openai/project/distill_reasoning_model/eval/Padawan-swe-local
oaipkg installpip ghapi

until curl -sf http://localhost:5122/v1/engines/dummy/inference/completions \
        -H 'Content-Type: application/json' \
        -H 'Authorization: Bearer dummy' \
        -H 'OpenAI-Organization: openai' \
        -d '{"prompt":"Fun fact is fun because","max_tokens":64}'; do
    echo "Request failed — retrying in 300 seconds…" >&2
    sleep 300
done

# Safely delete the “outputs” directory only if the user confirms
rm -rf outputs; rm -rf gpt-log


OPENAI_API_KEY='fake' CAAS_CLIENT_ID='haoranxu' python ~/code/openai/project/distill_reasoning_model/eval/Padawan-swe-local/run_nv4_padawan_tool_report_progress_pr.py

ckpt=$(ps -eo cmd= |
grep -E '^python -m harmony_scripts\.engines\.start_engine' |
grep -oP '(?<=--snapshot_path=)\S+'
)

bbb cpr gpt-log $ckpt/gpt-log
bbb cpr outputs $ckpt/outputs
echo "gpt log and outputs has been saved at the path: $ckpt"

python ~/code/openai/personal/haoranxu/eval/conclude_results.py; python ~/code/openai/personal/haoranxu/eval/compute_stats_log.py

echo "$ckpt eval done"