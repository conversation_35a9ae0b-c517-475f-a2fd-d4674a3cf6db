export WANDB_BASE_URL="https://msaip.wandb.io"
export WANDB_API_KEY="****************************************"

# Timestamp for unique wandb name
DATE_ID=$(date +%m-%d-%H-%M-%Y)

## Model list:
# nv4: "az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted"
# nv4 step500: az://orngcresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted

## Dataset list:
## v1:
    # az://orngtransfer/data/xuga/output/swe-rollout/xidai-nv4-swe-rfs-rcs-32x32-15_10conversations.jsonl 83,829
    # az://orngtransfer/data/xuga/output/swe-rollout/xidai-nv4-swe-rfs-rcs-32x32-15_8conversations.jsonl  38,758
    # az://orngtransfer/data/xuga/output/swe-rollout/xidai-nv4-swe-rfs-rcs-32x32-15_6conversations.jsonl  5,191
    # backup:  az://orngcresco/data/haoranxu/swe/data/distill/v1/

### NV4 d36


NCTX=131072
BATCH_SIZE=64
LR=$(awk 'BEGIN{print 3.73e-5 * 0.0125}')

## UKS:
# NAME="nv4-full-filtered-rcsrfsbb"
# BASE_CKPT="az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted" # NV4
# # BASE_CKPT="az://orngcresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted" # NV4 step500
# DATASET="az://orngcresco/data/haoranxu/swe/data/distill/v1/tbv2-train0414-full-*-filtered.jsonl.gz"
# EMA_HORIZON=1511   # IMPORTANT: this should be 10% of the total number of rows in the dataset!!
# LAYOUT="finetune-80g-sft"
# SECURITY="msft"

## HPE:
NAME="report_progress_2K"
# BASE_CKPT="az://orngscuscresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted" # NV4
# BASE_CKPT="az://orngscuscresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted" # NV4 step500
BASE_CKPT="az://orngscuscresco/twapi/mini/e/zhendongwang-data-mix-padawan-ivt16k-64x32-rm-run2/policy/step_000040/250427030407TTIPMSWH-0" # NV4
# DATASET="az://orngscuscresco/data/haoranxu/swe/data/distill/v1/tbv2-train0414-full-*-filtered.jsonl.gz"
EMA_HORIZON=200  # IMPORTANT: this should be 10% of the total number of rows in the dataset!!
LAYOUT="finetune-h100-80g-sft"
SECURITY="msft-orng" 


python -m mini.finetune.finetune2 \
    name="${NAME}-bz${BATCH_SIZE}-lr${LR}-date${DATE_ID}" \
    dataset=mini.finetune.datasets.glob_dataset:BerryHarmonyGlobDataset \
    dataset.globs.0.path="az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/correct_downsampled_progress_report.jsonl.gz" \
    dataset.max_n_datapoints=2000 \
    dataset.batch_size=${BATCH_SIZE} \
    dataset.bypass_exceptions=True \
    dataset.shuffle=True \
    ...renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe \
    ...is_multimodal=True \
    base_model.ml_config="ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=False enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false enable_slow_replica_detection=False" \
    base_model.sampling_ml_config="ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True" \
    base_model.initial_checkpoint=${BASE_CKPT} \
    base_model.snapshot="scallion_lpe:d36" \
    base_model.layout=${LAYOUT} \
    base_model.local_cache_base=/tmp/tensorcache \
    ...n_ctx=${NCTX} \
    base_model.n_replicas=1 \
    ...encoding_name="orion_200k" \
    hyperparam_manager=const_hyperparam_manager.ConstLRHyperparamManager \
    hyperparam_manager.opt_config.lr_per_sample_d16=${LR} \
    hyperparam_manager.opt_config.ema_horizon=${EMA_HORIZON} \
    between_saves="1 epochs" \
    duration="1 epochs" \
    security_profile=${SECURITY} \
    github_upload=False \
    git_guard=False \
    wandb_enable=True \
    kafka_enable=False \
    use_shmsnap=False \
    initialize_timeout=999999 \
    timeout.default=999999 \
    load.restore_from_all_clusters=False \
    ...postprocessors.0=mini.finetune.datasets.postprocessors.postprocess:MaybeReformatDataPoint \
    ...postprocessors.1=mini.finetune.datasets.postprocessors.postprocess:LoadConvoPostprocess \
    ...postprocessors.2=mini.finetune.datasets.postprocessors.postprocess:WeightNonAssistantMessagesPostprocess \
    ...postprocessors.3=mini.finetune.datasets.postprocessors.postprocess:RemoveConfidencePostprocess \
    ...postprocessors.4=mini.finetune.datasets.postprocessors.postprocess:EnsureEndTurn \
    ...postprocessors.5=mini.finetune.datasets.postprocessors.postprocess:AdjustBudgetPostprocess \
    ...postprocessors.5.max_num_yields=384 \
    ...postprocessors.5.max_token_budget=131072
