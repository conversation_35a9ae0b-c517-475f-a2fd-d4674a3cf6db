DATASET=train_filter150_restrict
GPUNUM=128 #56 or 128
MAX_YIELDs=384 # 256 or 512
NCTX=131072
ICT=65536
# CKPT=az://orngscuscresco/twapi/mini/e/haoranxu-nv4-full-sb-v2-450-bz32-lr1.865e-06-date04-26-09-25-2025/ad575930-b0b3-4a06-a692-2ac1ffb903c0/checkpoint/model1/000000000008/
# CKPT=az://orngscuscresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted
CKPT="az://orngscuscresco/twapi/mini/e/haoranxu-nv4-200-rcr-cold-start-bz48-lr2.33125e-07-date05-03-04-24-2025/3e9d951e-1d66-49c1-80e9-b87ef274200d/checkpoint/model1/000000000017"
# CKPT="az://orngscuscresco/twapi/mini/e/haoranxu-nv4-all-cold-start-bz64-lr2.33125e-07-date05-05-03-46-2025/b26236ea-2fa3-40f5-b13c-e3f1b4029991/checkpoint/model1/000000000143"
NAME=rcr-cold-start-nv4-swe-${DATASET}-${GPUNUM}gpu-${MAX_YIELDs}yields-hpe3-${NCTX}ctx-${ICT}ict-full-sb


#?sv=2023-01-03&st=2025-05-02T21%3A19%3A18Z&se=2025-05-09T21%3A19%3A00Z&skoid=bd7a8bde-daa0-4393-aa4b-5c65c06b444b&sktid=72f988bf-86f1-41af-91ab-2d7cd011db47&skt=2025-05-02T21%3A19%3A18Z&ske=2025-05-09T21%3A19%3A00Z&sks=b&skv=2023-01-03&sr=c&sp=racwdxltf&sig=mDXPlLtv2X3aev6YRaFgpQa%2F3fmfeIadMl85qFEVPGo%3D
WANDB_PROJECT_NAME=haoran-swe

CMD=(
beam python --use-cwd -m qstar.run_experiment nostrict
name=${NAME}
seed=20250501

skip_validate_config=True

# Policy settings
:berry_models.scallion_lpe:d36_80g_mbg16_bf16
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True'

policy.initial_checkpoint=${CKPT}
policy.n_gpus=${GPUNUM}
policy.is_multimodal=True
...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe

# Systems settings
:qstar.presets.common:longer_timeout
peashooter.timeout_seconds.stalled_datapoint=3600
timeout.default=None

# Model settings
policy.n_ctx=${NCTX}
defaults.n_ctx=${NCTX}
defaults.sampler.harmony_constrained_sampling=True
optimizer.hparam_scaler.lr_per_instance_d16=1e-5
...save_every=5

# Dataset configs!
:deep_swe.sweberry_msft.swe_bench_train_v2_padawan.presets:${DATASET}
# :deep_swe_msft.sweberry_v2.presets:${DATASET}
...dataset_container=orngscuscresco
...max_num_yields=${MAX_YIELDs}
...tool_penalty_multiplier=0

berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=10

# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
# Recommend 32768/16384 to get rid of reducing in solution length
defaults.inverse_token_cost=${ICT} 
defaults.instances_per_batch=32
defaults.target_samples_per_instance=32
batch_completer.n_batches_in_flight=10

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=${WANDB_PROJECT_NAME}
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}"
