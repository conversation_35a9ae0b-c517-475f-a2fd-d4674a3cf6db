export WANDB_BASE_URL="https://msaip.wandb.io"
export WANDB_API_KEY="****************************************"


RANDOM_ID=$(LC_CTYPE=C tr -dc 'A-Za-z' </dev/urandom | head -c 3)

### NV4 d36
NCTX=8192
python -m mini.finetune.finetune2 \
    name="minisft_nv4_example_${RANDOM_ID}" \
    dataset=mini.finetune.datasets.glob_dataset:BerryHarmonyGlobDataset \
    dataset.globs.0.path="az://orngcresco/data/haoranxu/swe/data/distill/math_debug/debug*.jsonl" \
    dataset.batch_size=64 \
    dataset.bypass_exceptions=True \
    dataset.shuffle=True \
    ...renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe \
    ...is_multimodal=True \
    base_model.ml_config="ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=False enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false enable_slow_replica_detection=False" \
    base_model.sampling_ml_config="ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True" \
    base_model.initial_checkpoint="az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted" \
    base_model.snapshot="scallion_lpe:d36" \
    base_model.layout="finetune-80g-sft" \
    ...n_ctx=${NCTX} \
    base_model.n_replicas=1 \
    ...encoding_name="orion_200k" \
    hyperparam_manager=const_hyperparam_manager.ConstLRHyperparamManager \
    hyperparam_manager.opt_config.lr_per_sample_d16=3.72666070e-05 \
    hyperparam_manager.opt_config.ema_horizon=50 \
    hyperparam_manager.warmup_samples=32 \
    between_saves="10 epochs" \
    duration="10 epochs" \
    security_profile="msft" \
    github_upload=False \
    git_guard=False \
    wandb_enable=False \
    kafka_enable=False \
    use_shmsnap=False \
    initialize_timeout=36000 \
    load.restore_from_all_clusters=False \
    ...postprocessors.0=mini.finetune.datasets.postprocessors.postprocess:MaybeReformatDataPoint \
    ...postprocessors.0=mini.finetune.datasets.postprocessors.postprocess:LoadConvoPostprocess \
    ...postprocessors.1=mini.finetune.datasets.postprocessors.postprocess:WeightNonAssistantMessagesPostprocess \
    ...postprocessors.2=mini.finetune.datasets.postprocessors.postprocess:RemoveConfidencePostprocess \
    ...postprocessors.3=mini.finetune.datasets.postprocessors.postprocess:EnsureEndTurn \
    ...postprocessors.4=mini.finetune.datasets.postprocessors.postprocess:AdjustBudgetPostprocess \
    ...postprocessors.4.max_num_yields=256 \
    ...postprocessors.4.max_token_budget=131072 


    
    
### D16
NCTX=2048
python -m mini.finetune.finetune2 \
    name="minisft_d16_example_${RANDOM_ID}" \
    dataset.globs.0.path="az://orngcresco/data/haoranxu/swe/data/distill/math_debug/debug*.jsonl" \
    dataset.batch_size=8 \
    base_model.ml_config="rollbackv2_use_delayed_copy=False dp_collective_ops_use_tree=False enable_slow_replica_detection=False" \
    base_model.sampling_ml_config=f"mini.tw.common chunk_lengths=${NCTX} n_replicas={1} enable_shmsnap=False twppo.sampling_only staging=linear mode=passthrough has_optim=False step_optim_inline=False sync_grads_inline=False enable_shmsnap=False dedicated_optim=False fetch_params_inline=False dedicated_embed=False dedicated_unembed=False create_sampling_comm=True rollbackv2_use_delayed_copy=False sequence_parallel_transformer_pipe=False dp_collective_ops_use_tree=False" \
    base_model.initial_checkpoint="az://orngcresco/models/snapshots/models.tc.small/scallion-qstar-prior-d16-transferred-20241118" \
    ...n_ctx=${NCTX} \
    ...is_multimodal=True \
    base_model.snapshot=None \
    base_model.config="falcon.orion.d16-s32-k4-fp16rgs-scallion-trimodal" \
    base_model.layout="test-finetune-80g" \
    base_model.n_replicas=1 \
    ...encoding_name="orion_200k_mmgen" \
    hyperparam_manager=const_hyperparam_manager.ConstLRHyperparamManager \
    hyperparam_manager.opt_config.lr_per_sample_d16=3.72666070e-05 \
    hyperparam_manager.warmup_samples=32 \
    between_saves="5 epochs" \
    duration="5 epochs" \
    security_profile="msft" \
    github_upload=False \
    git_guard=False \
    wandb_enable=True \
    kafka_enable=False \
    use_shmsnap=False \
    load.restore_from_all_clusters=False \
    ...postprocessors.0=mini.finetune.datasets.postprocessors.postprocess:MaybeReformatDataPoint \
    ...postprocessors.1=mini.finetune.datasets.postprocessors.postprocess:LoadConvoPostprocess \
    ...postprocessors.2=mini.finetune.datasets.postprocessors.postprocess:WeightNonAssistantMessagesPostprocess \
    ...postprocessors.3=mini.finetune.datasets.postprocessors.postprocess:RemoveConfidencePostprocess \
    ...postprocessors.4=mini.finetune.datasets.postprocessors.postprocess:EnsureEndTurn \
    ...postprocessors.5=mini.finetune.datasets.postprocessors.postprocess:AdjustBudgetPostprocess \
    ...postprocessors.5.max_num_yields=256 \
    ...postprocessors.5.max_token_budget=131072 \
    dataset.bypass_exceptions=True \
    dataset.shuffle=True \
    dataset=mini.finetune.datasets.glob_dataset:BerryHarmonyGlobDataset \
    hyperparam_manager.opt_config.ema_horizon=170 \
    ...renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe \
    initialize_timeout=36000


############################################ Input format is context-completion ############################################
### NV4 d36
# DATA="az://orngcresco/data/haoranxu/swe/data/distill/math_debug/qq8933-MATH500.jsonl"
# WARMUP=30
# EPOCHS=3
# SAVE_INTERVAL=1
# NCTX=131072

# python -m mini.finetune.finetune2 \
#     name="minisft_nv4_example" \
#     dataset=mini.finetune.datasets.glob_dataset:GlobDataset \
#     dataset.globs.0.path=${DATA} \
#     dataset.batch_size=8 \
#     base_model.ml_config="ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=False enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false enable_slow_replica_detection=False" \
#     base_model.sampling_ml_config="ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True" \
#     base_model.initial_checkpoint="az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted" \
#     base_model.snapshot="scallion_lpe:d36" \
#     base_model.layout="finetune-80g-mbg16-sft" \
#     ...n_ctx=${NCTX} \
#     base_model.n_replicas=1 \
#     ...encoding_name="orion_200k" \
#     hyperparam_manager=const_hyperparam_manager.ConstLRHyperparamManager \
#     hyperparam_manager.opt_config.lr_per_sample_d16=1e-5 \
#     hyperparam_manager.warmup_samples=${WARMUP} \
#     between_saves="${SAVE_INTERVAL} epochs" \
#     duration="${EPOCHS} epochs" \
#     security_profile="msft" \
#     github_upload=False \
#     git_guard=False \
#     wandb_enable=True \
#     kafka_enable=False \
#     use_shmsnap=False \
#     initialize_timeout=999999 \
#     load.restore_from_all_clusters=False



# NCTX=8192
# NCTX=8192
# python -m mini.finetune.finetune2 \
#     name="minisft_d16_example" \
#     dataset=mini.finetune.datasets.glob_dataset:GlobDataset \
#     dataset.globs.0.path="az://orngcresco/data/haoranxu/swe/data/distill/math_debug/qq8933-MATH500.jsonl" \
#     dataset.batch_size=8 \
#     base_model.ml_config="rollbackv2_use_delayed_copy=False dp_collective_ops_use_tree=False enable_slow_replica_detection=False" \
#     base_model.sampling_ml_config=f"mini.tw.common chunk_lengths=${NCTX} n_replicas={1} enable_shmsnap=False twppo.sampling_only staging=linear mode=passthrough has_optim=False step_optim_inline=False sync_grads_inline=False enable_shmsnap=False dedicated_optim=False fetch_params_inline=False dedicated_embed=False dedicated_unembed=False create_sampling_comm=True rollbackv2_use_delayed_copy=False sequence_parallel_transformer_pipe=False dp_collective_ops_use_tree=False" \
#     base_model.initial_checkpoint="az://orngcresco/models/snapshots/models.tc.small/scallion-qstar-prior-d16-transferred-20241118" \
#     ...n_ctx=${NCTX} \
#     base_model.config="falcon.orion.d16-s32-k4-fp16rgs-scallion-trimodal" \
#     base_model.layout="test-finetune-80g" \
#     base_model.snapshot=None \
#     base_model.n_replicas=1 \
#     ...encoding_name="orion_200k_mmgen" \
#     hyperparam_manager=const_hyperparam_manager.ConstLRHyperparamManager \
#     hyperparam_manager.opt_config.lr_per_sample_d16=1e-5 \
#     hyperparam_manager.warmup_samples=32 \
#     between_saves="1 epochs" \
#     duration="1 epochs" \
#     security_profile="msft" \
#     github_upload=False \
#     git_guard=False \
#     wandb_enable=True \
#     kafka_enable=False \
#     use_shmsnap=False \
#     initialize_timeout=36000 \
#     load.restore_from_all_clusters=False


## Deprecated command
## D36 nv4
# python /root/code/openai/personal/haoranxu/minisft_deprecated.py \
#     name=minisft_nv4_test1 \
#     dataset=mini.finetune.datasets.glob_dataset:GlobDataset \
#     dataset.globs.0.path="az://orngcresco/data/haoranxu/swe/data/distill/math_debug/qq8933-MATH500.jsonl" \
#     dataset.batch_size=64 \
#     base_model=MSFTFinetuneDefaultModelConfigd36 \
#     base_model.initial_checkpoint="az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted" \
#     base_model.ml_config="ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=False enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false enable_slow_replica_detection=False" \
#     base_model.sampling_ml_config="ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True" \
#     hyperparam_manager=const_hyperparam_manager.ConstLRHyperparamManager \
#     hyperparam_manager.opt_config.lr_per_sample_d16=1e-5 \
#     hyperparam_manager.warmup_samples=128 \
#     between_saves="2 epochs" \
#     duration="2 epochs" \
#     security_profile="msft" \
#     github_upload=False \
#     git_guard=False \
#     wandb_enable=True \
#     kafka_enable=False \
#     use_shmsnap=False \
#     load.restore_from_all_clusters=False \
#     base_model.layout="finetune-80g-mbg16" \
#     base_model.snapshot="scallion_lpe:d36"

## D16
# python /root/code/openai/personal/haoranxu/minisft_deprecated.py \
#     name=minisft_example5 \
#     dataset=mini.finetune.datasets.glob_dataset:GlobDataset \
#     dataset.globs.0.path="az://orngcresco/data/haoranxu/swe/data/distill/math_debug/qq8933-MATH500.jsonl" \
#     dataset.batch_size=8 \
#     base_model=MSFTFinetuneDefaultModelConfigd16 \
#     base_model.initial_checkpoint="az://orngcresco/models/snapshots/models.tc.small/scallion-qstar-prior-d16-transferred-20241118" \
#     base_model.layout="finetune-80g" \
#     hyperparam_manager=const_hyperparam_manager.ConstLRHyperparamManager \
#     hyperparam_manager.opt_config.lr_per_sample_d16=1e-5 \
#     hyperparam_manager.warmup_samples=32 \
#     between_saves="1 epochs" \
#     duration="1 epochs" \
#     security_profile="msft" \
#     github_upload=False \
#     git_guard=False \
#     wandb_enable=True \
#     kafka_enable=False \
#     use_shmsnap=False \
#     load.restore_from_all_clusters=False