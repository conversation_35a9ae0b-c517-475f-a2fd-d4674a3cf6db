from mini.finetune.datasets.jsonl import read_jsonl, write_jsonl
from collections import defaultdict


def filter_data(data_pair, threshold_min, threshold_max):
    sample_data = read_jsonl(data_pair[0])
    output_data = []
    qualified_data_id = set()
    all_data_id = set()
    for sample in sample_data:
        if len(sample["conversation"]["messages"]) >= threshold_min and len(sample["conversation"]["messages"]) < threshold_max:
            qualified_data_id.add(sample["unique_id"])
        all_data_id.add(sample["unique_id"])


    count = defaultdict(int)
    for orig in sample_data:
        if orig["unique_id"] in qualified_data_id and count[orig["unique_id"]] < 2:
            output_data.append(orig)
            count[orig["unique_id"]] += 1
    
    print(f"Data filtering for {data_pair[0]}: {len(sample_data)} -> {len(output_data)}, {len(all_data_id)} -> {len(qualified_data_id)}")
    output_dir = f"az://orngscuscresco/data/haoranxu/swe/data/distill_cold_start/{data_pair[2]}_threshold_{threshold_min}_{threshold_max}_{len(output_data)}_0504/train.jsonl"
    write_jsonl(output_dir, output_data)

if __name__ == "__main__":
    data_pair = [
        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-full-swebenchtrain-pdw.jsonl.gz", #swebenchtrain
            "swebenchtrain",
        ),

        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-train0414-full-rcr-pdw.jsonl.gz", #rcr
            "rcr",
        ),

        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-bb-pdw.jsonl.gz", #bb
            "bb",
        ),

        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-rfs-pdw.jsonl.gz", #rfs
            "rfs",
        ),

        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-rcs-pdw.jsonl.gz", #rcs
            "rcs",
        ),
    ]

    for threshold in [(100, 150)]:
        output_data = []
        # output_dir = f"az://orngscuscresco/data/haoranxu/swe/data/rl/sb_rcr_bb_rfs_rcs_{threshold}.jsonl"
        for pair in data_pair:
            filter_data(pair, threshold[0], threshold[1])
        
        # write_jsonl(output_dir, output_data)
        # print(f"Data filtering for {output_dir}: {len(output_data)}")
        