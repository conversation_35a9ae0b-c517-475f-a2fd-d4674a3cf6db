import os
import time
import sys
import subprocess
from mini import progress
from mini.constrained_model import ConstrainedModel
from mini.metrics import metrics
from mini.mini_config import mini
from mini.utils import duration_v2
from mini.utils.crasher import crasher
from mini.utils.misc import concat_lists
from mini.utils.scope import scope
from mini.finetune.finetune2 import main, FinetuneArgs, FinetuneDefaultModelConfig, FinetuneDefaultWandbConfig
import chz
from mini import mini_config
from layout_store.layout import Layout
from mini import progress, timeout_config

@chz.chz
class MSFTFinetuneDefaultModelConfigd16(FinetuneDefaultModelConfig):
    config: str | None = "falcon.orion.d16-s32-k4-fp16rgs-scallion-trimodal"
    snapshot: str | None = None
    encoding_name: str = "orion_200k_mmgen"
    X_layout: str | None = None
    X_sampling_layout: str | None = None
    X_n_ctx: int = 16384
    X_ml_config: str = "rollbackv2_use_delayed_copy=False dp_collective_ops_use_tree=False enable_slow_replica_detection=False"
    X_n_replicas: int | None = 1
    X_sampling_config: str = " ".join([
        f"{config} mini.tw.common chunk_lengths={X_n_ctx} n_replicas={1} enable_shmsnap=False",
        "twppo.sampling_only",
        "staging=linear",
        "mode=passthrough",
        "has_optim=False",
        "step_optim_inline=False",
        "sync_grads_inline=False",
        "enable_shmsnap=False",
        "dedicated_optim=False",
        "fetch_params_inline=False",
        "dedicated_embed=False",
        "dedicated_unembed=False",
        "create_sampling_comm=True",
        "rollbackv2_use_delayed_copy=False",
        "sequence_parallel_transformer_pipe=False",
        "dp_collective_ops_use_tree=False",
    ])
    def get_layout_obj(self):
        return Layout(
            pipe_depth=1,
            n_op_shards=2,
            optim_pipe_depth=None,
            microbatch_tokens=16384,
            dedicated_optim=False,
            gpu="a100-80g",
            dedicated_embed=False,
            dedicated_unembed=False,
            enable_shmsnap=False,
        )
    @property
    def layout(self) -> str:
        return self.get_layout_obj().to_config(n_ctx=16384, n_replicas=1)


@chz.chz
class MSFTFinetuneDefaultModelConfigd36(FinetuneDefaultModelConfig):
    config: str | None = "falcon.multimodal.runs.scallion-d36-s64-lpe"
    snapshot: str | None = None
    encoding_name: str = "orion_200k_mmgen"
    X_layout: str | None = None
    X_sampling_layout: str | None = None
    X_n_ctx: int = 16384
    X_ml_config: str = "rollbackv2_use_delayed_copy=False dp_collective_ops_use_tree=False enable_slow_replica_detection=False"
    X_n_replicas: int | None = 1
    X_sampling_config: str = " ".join([
        f"{config} mini.tw.common chunk_lengths={X_n_ctx} n_replicas={1} enable_shmsnap=False",
        "twppo.sampling_only",
        "staging=linear",
        "mode=passthrough",
        "has_optim=False",
        "step_optim_inline=False",
        "sync_grads_inline=False",
        "enable_shmsnap=False",
        "dedicated_optim=False",
        "fetch_params_inline=False",
        "dedicated_embed=False",
        "dedicated_unembed=False",
        "create_sampling_comm=True",
        "rollbackv2_use_delayed_copy=False",
        "sequence_parallel_transformer_pipe=False",
        "dp_collective_ops_use_tree=False",
    ])
    
    # def get_layout_obj(self):
    #     return Layout(
    #         pipe_depth=1,
    #         n_op_shards=8,
    #         optim_pipe_depth=6,
    #         microbatch_tokens=16384,
    #         max_n_microbatches_per_group=16,
    #         dedicated_optim=True,
    #         gpu="a100-80g",
    #     )
    # @property
    # def layout(self) -> str:
    #     return self.get_layout_obj().to_config(n_ctx=16384, n_replicas=1)

@chz.chz
class MSFTFinetuneDefaultModelConfigNV4(FinetuneDefaultModelConfig):
    config: str | None = None #"falcon.multimodal.runs.scallion-d36-s64-lpe"
    snapshot: str | None = None
    encoding_name: str = "orion_200k"
    X_layout: str | None = None
    X_sampling_layout: str | None = None
    X_n_ctx: int = 2**17
    X_ml_config: str = "ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=False enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false enable_slow_replica_detection=False"
    X_n_replicas: int | None = 1
    X_sampling_ml_config: str = "ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True"
    mini_initial_load_skip_regex: str | None = "block_expected|block_lg_scale.*"


    # def get_layout_obj(self):
    #     return Layout(
    #         pipe_depth=1,
    #         n_op_shards=8,
    #         optim_pipe_depth=6, ## 8*6 + 8
    #         microbatch_tokens=2**17,
    #         max_n_microbatches_per_group=16,
    #         dedicated_optim=True,
    #         gpu="a100-80g",
    #     )
    # @property
    # def layout(self) -> str:
    #     return self.get_layout_obj().to_config(n_ctx=2**17, n_replicas=1)


@chz.chz
class AMLFinetuneArgs(FinetuneArgs):
    base_model: mini_config.ModelConfig = chz.field(
        doc="The model config of the model we will finetune.",
        meta_factory=chz.factories.subclass(
            base_cls=mini_config.ModelConfig,
            default_cls=MSFTFinetuneDefaultModelConfigNV4,
        ),
    )
    X_use_shmsnap: bool = False
    initialize_timeout: int | None = 3600

def aml_main(ft_args: AMLFinetuneArgs):
    main(ft_args)

if __name__ == "__main__":
    gpu_name = subprocess.check_output(['nvidia-smi', '--query-gpu=gpu_name', '--format=csv,noheader', '--id=0']).decode().strip()
    print(f'GPU: {gpu_name}')
    mini(aml_main)
