## Turn Completer Evaluation
import io
from PIL import Image
from chat import chat
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter
from message_completer import TokenMessageCompleter
from chat.render.renderer_registry import get_renderer
from bus_token_completer import <PERSON><PERSON><PERSON><PERSON><PERSON>pleter, QoSType
from mini.finetune.datasets.jsonl import read_jsonl, write_jsonl
import chat
import random
from tqdm import tqdm
import multiprocessing
from functools import partial

TEMPLATE = """
Rewrite the following task description as a natural GitHub Pull Request (PR) description, as if submitted by a real contributor. Use a tone and structure commonly found in open-source PRs — it can include a brief summary, steps taken, rationale if relevant, and any important notes. Avoid sounding too formal or templated. Keep the task exactly the same. End with this important instruction as a final note:
Please do NOT modify the run_tests.sh file.


The problem you need to rewrite:
{problem}


Some examples of PR descriptions, where you can see the tone and structure and take them as references:
Example 1:
{example_1}
Example 2:
{example_2}
Example 3:
{example_3}
"""

def process_data_item(data, template_data, turn_completer_config, renderer_name):
    # Initialize turn completer in each worker process
    berry_turn_completer_config = BerryMultiMessageTurnCompleter.Config(
        token_completer_config=turn_completer_config,
        completion_params={"temperature": 1, "max_tokens": 16384},
        renderer=renderer_name,
    )
    turn_completer = berry_turn_completer_config.build()
    
    problem = data["problem"]
    examples = random.sample(template_data, 3)
    input_prompt = TEMPLATE.format(
        problem=problem,
        example_1=examples[0]["problem"],
        example_2=examples[1]["problem"],
        example_3=examples[2]["problem"],
    )
    convo = chat.Conversation(
        messages=[chat.Message.user(content=chat.MultimodalText(parts=[input_prompt]))],
    )
    
    rewrite_problem = problem
    while True:
        try:
            for i, msg in enumerate(
                    turn_completer.completion(convo, reward_multiplier=random.randint(32, 64)).output_messages
            ):  
                if msg.end_turn:
                    rewrite_problem = msg.content.parts[0]
                    
            break
        except Exception as e:
            pass

    new_data = data.copy()
    new_data["problem"] = rewrite_problem
    print(f"Rewriting {problem} to {rewrite_problem}")
    return new_data

def main():
    token_completer_config = BusTokenCompleter.Config(
        topic_mode_or_user="msft",
        topic_or_snapshot="az://orngcresco/models/snapshots/models.tc/nv4-strawberry-step-320-transferred-20250122-decrypted",
        bus_line="bus",
        qos_type=QoSType.FIFO,
    )
      
    renderer_name = "harmony_v4.0.16_berry_v3_1mil_orion_lpe"

    input_files = [
        # "az://orngscuscresco/data/haoranxu/swe/data/rl/bb_threshold_150_761_0427/train.jsonl",
        "az://orngscuscresco/data/haoranxu/swe/data/rl/rcs_threshold_150_1399_0427/train.jsonl",
        "az://orngscuscresco/data/haoranxu/swe/data/rl/rfs_threshold_150_2136_0427/train.jsonl",
    ]

    output_files =[
        # "az://orngscuscresco/data/haoranxu/swe/data/rl/bb_threshold_150_761_0427_rewrite/train.jsonl",
        "az://orngscuscresco/data/haoranxu/swe/data/rl/rcs_threshold_150_1399_0427_rewrite/train.jsonl",
        "az://orngscuscresco/data/haoranxu/swe/data/rl/rfs_threshold_150_2136_0427_rewrite/train.jsonl",
    ]

    template_data = read_jsonl("az://orngscuscresco/data/haoranxu/swe/data/rl/swebenchtrain_threshold_150_635_0427/train.jsonl")
    
    # Determine the number of processes to use (adjust as needed)
    num_processes = min(multiprocessing.cpu_count(), 8)
    
    for idx, input_file in enumerate(input_files):
        input_data = read_jsonl(input_file)
        
        # Create a process pool
        with multiprocessing.Pool(processes=num_processes) as pool:
            # Create a partial function with fixed arguments
            process_func = partial(
                process_data_item, 
                template_data=template_data, 
                turn_completer_config=token_completer_config, 
                renderer_name=renderer_name
            )
            
            # Process data items in parallel while maintaining order
            all_new_data = list(tqdm(
                pool.imap(process_func, input_data),
                total=len(input_data),
                desc=f"Processing {input_file}"
            ))
            
        write_jsonl(output_files[idx], all_new_data)

if __name__ == "__main__":
    # Required for multiprocessing on some platforms
    multiprocessing.set_start_method('spawn', force=True)
    main()
