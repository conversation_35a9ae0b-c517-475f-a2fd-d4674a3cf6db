from mini.finetune.datasets.jsonl import read_jsonl, write_jsonl



def filter_data(data_pair, threshold=150):
    sample_data = read_jsonl(data_pair[0])
    orig_data = read_jsonl(data_pair[1])
    output_data = []
    qualified_data_id = set()
    all_data_id = set()
    for sample in sample_data:
        if len(sample["conversation"]["messages"]) >= threshold:
            qualified_data_id.add(sample["unique_id"].replace("_remove_test","").replace("_no_test",""))
        all_data_id.add(sample["unique_id"].replace("_remove_test","").replace("_no_test",""))

    for orig in orig_data:
        if orig["unique_id"] in qualified_data_id:
            output_data.append(orig)
    
    print(f"Data filtering for {data_pair[0]}: {len(orig_data)} -> {len(output_data)}, {len(all_data_id)} -> {len(qualified_data_id)}")
    output_dir = f"az://orngscuscresco/data/haoranxu/swe/data/rl/{data_pair[2]}_threshold_{threshold}_{len(output_data)}_0427/train.jsonl"
    write_jsonl(output_dir, output_data)

if __name__ == "__main__":
    data_pair = [
        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-full-swebenchtrain-pdw.jsonl.gz", #swebenchtrain
            "az://orngscuscresco/data/zhendongw/swe-bench-train/test_train/swe_bench_train_updated.jsonl",
            "swebenchtrain",
        ),

        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-train0414-full-rcr-pdw.jsonl.gz", #rcr
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/rcr_5636_train.jsonl",
            "rcr",
        ),

        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-bb-pdw.jsonl.gz", #bb
            "az://orngscuscresco/data/swang/swe/u20250423/bb_hard/train_1930/processed_train_1930.jsonl",
            "bb",
        ),

        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-rfs-pdw.jsonl.gz", #rfs
            "az://orngscuscresco/data/swang/swe/u20250423/rfs_7func/train_4093/processed_train_4093.jsonl",
            "rfs",
        ),

        (
            "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-rcs-pdw.jsonl.gz", #rcs
            "az://orngscuscresco/data/swang/swe/u20250423/rcs/train_7562/processed_train_7562.jsonl",
            "rcs",
        ),
    ]

    for threshold in [100, 150]:
        output_data = []
        # output_dir = f"az://orngscuscresco/data/haoranxu/swe/data/rl/sb_rcr_bb_rfs_rcs_{threshold}.jsonl"
        for pair in data_pair:
            filter_data(pair, threshold)
        
        # write_jsonl(output_dir, output_data)
        # print(f"Data filtering for {output_dir}: {len(output_data)}")
        