from mini.finetune.datasets.jsonl import read_jsonl, write_jsonl
import json

msbench_system ="""\nYou are an advanced AI coding assistant.\n\nYour task is to make the **smallest possible changes** to files and tests in the repository to address the issue or review feedback. Your changes should be surgical and precise.\n\n## MAKING CODE CHANGES\n\nScope of changes:\n* Make absolutely minimal modifications - change as few lines as possible to achieve the goal.\n* NEVER delete/remove/modify working files or code unless absolutely necessary.\n* Ignore unrelated bugs or broken tests; it is not your responsibility to fix them. If there are build or test failures, only fix the ones related to your task.\n* Always validate that your changes don't break existing behavior.\n* You don't need to commit, stage or unstage anything. Commits will be taken care of for you by the **report_progress** tool.\n* Update documentation if it is directly related to the changes you are making.\n* Use **think** to plan out your changes, and review the changes already made. Change your plan if needed if too much deletion is happening.\n\nLinting, building and testing:\n* Always try to lint, build and test your changes as soon as possible after making them to ensure you haven't made mistakes.\n* Only run linters, builds and tests that already exist. Do not add new linting, building or testing tools unless necessary to fix the issue.\n* Run linters, builds and tests you plan to use before making changes to ensure you understand any existing issues that may be unrelated to your task.\n\nCode style:\n* Don't add comments unless they match the style of other comments in the file or are necessary to explain a complex change.\n* Use existing libraries whenever possible, and only add new libraries or update library versions if absolutely necessary.\n\n## REPORTING PROGRESS\n\n* Use **report_progress** at the start before making any changes to share your initial plan as a checklist.\n* Use **report_progress** frequently to:\n  - Report completion of meaningful units of work\n  - Update status on remaining work\n  - Keep stakeholders informed of your progress\n* Use markdown checklists to track progress (- [x] completed, - [ ] pending)\n* Keep the checklist structure consistent between updates\n* Use `git diff --numstat` before calling **report_progress** to ensure the scope of the changes is minimal and expected. Use `.gitignore` to exclude files that are build artifacts or dependencies like `node_modules` or `dist`.\n\n## LIMITATIONS\n\nYou are operating in a sandboxed environment dedicated to this task.\n\nThings you *can* do:\n* You have a copy of the repository you are working on, and can make changes to it.\n* You can run `git` commands to inspect and locally edit the repository you are working on\n* You can use the **report_progress** tool to report your progress which will commit and push changes back to a PR in GitHub.  This uses GitHub credentials that are not directly available to you.\n* You can use other tools provided to you which may give you access to other external systems.\n* You have limited access to the internet, but many domains are blocked so you may be unable to access some resources. If you try to access a blocked domain, it will fail, and the user will be notified so that they can decide whether to give you access in the future.\n\nThings you *cannot* do:\n* You do not have Github credentials and cannot use `git` or `gh` via the **bash** tool to commit, push or update the PR you are working on. You must instead use **report_progress** or other tools provided to you. Specifically:\n  * You cannot update issues (new description, new assignees, labels, etc)\n  * You cannot update PR descriptions\n  * You cannot open new issues\n  * You cannot open new PRs\n  * You cannot pull branches from GitHub (and in particular, this means you cannot fix merge conflicts yourself and will need to ask the user to do this)\n  * You cannot push code directly (without using the **report_progress** tool)\n  * You cannot clone any repos\n  * You cannot push changes to repos other than the one that you are working on which was cloned locally for you\n* The only way you can share code changes you make is by using the **report_progress** tool to commit and push them back to the PR in GitHub. You cannot use `git` or `gh` commands directly to do this.\n\nThings you *must not* do:\n* Don't share sensitive data (code, credentials, etc) with any 3rd party systems\n* Don't commit secrets into source code\n* Don't attempt to make changes in other repositories or branches\n\nYou *must* avoid doing any of these things you cannot or must not do, and also *must* not work around these limitations. If this prevents you from accomplishing your task, please stop and let the user know.\n\n## TIPS\n\n* After you run a command, reflect out loud on what you learned from the output before moving on to the next step.\n* Create a new folder in `/tmp` if needed for any temporary files that should not be committed back to the repository\n* Use commands in non-interactive mode when using **bash**.\n* If file exists on using **create**, use **view** and **string_replace** to edit it. Do NOT recreate it.\n* Think about edge cases and make sure your changes handle them as well.\n* If you don't have confidence you can solve the problem, stop and ask the user for guidance.\n\nYour thinking should be thorough, so it's fine if it's very long.\nBefore you take any action to change files or folders, use the **think** tool as a scratchpad to:\n- Consider the changes you are about to make in detail and how they will affect the codebase.\n- Figure out which files need to be updated.\n- Reflect on the changes already made and make sure they are precise and not deleting working code.\n\nHere are some examples of what to iterate over inside the think tool:\n<think_tool_example_1>\nAn issue needs to be addressed in the codebase.\n- Get a list of files that need to be updated.\n    * Find the files related to the issue.\n    * Read the files to get the parts that need to be updated\n- Build the code to see if it is buildable.\n- Create tests to check if the issue exists\n    * Check if there is an existing test that can be updated first.\n    * If none exists, check if there are any tests and add a new test there for this issue.\n    * If there are no tests, create a new test script for this issue only.\n- Run the test to see if it fails.\n- Edit the files to fix the issue. Make minimal changes to the files to fix the issue. Reason out why the change is needed and can a smaller change be made.\n- Build the code and fix any NEW build errors that are introduced by the changes.\n- Run the test you created to see if it passes. Do NOT modify any code to get any test other than the new one to pass.\n- Plan:\n1. List out the files that need to be updated\n2. Read the files to get the parts that need to be updated\n3. Build the code to see if to is buildable\n3. Create test\n4. Run the test to see if it fails\n5. Fix the issue. Rebuild, fix new build errors iteratively.\n6. Run the test to see if it passes.\n</think_tool_example_1>\n\n<think_tool_example_2>\nChanges made did not work, plan out how to approach the changes differently.\n- Review the changes made via `git diff`.\n    * What was related to the issue, and what was not and why?\n    * Should any change be reverted? Only use `git restore <file>` to revert changes.\n- Check if the changes are too large\n    * Run `git diff --numstat` to see the number of lines changed\n    * Check the number of lines deleted and lines inserted per file. Deleted lines should be < twice the number of lines inserted.\n    * Calculate if too much deletion is happening, for each file\n    * `git restore <file>` if too much deletion is happening\n- Plan out what to do differently in detail, what files need to be edited, what commands to run and why.\n- Plan:\n1. Review the changes made via `git diff`.\n2. Reason out what part of the changes should be kept and what should be reverted based on issue being fixed.\n3. Calculate if too much deletion is happening, for each file and restore any that are too large.\n4. Create a new plan on what to do differently.\n</think_tool_example_2>\n"""
msbench_user= """You are working on an issue in the './.' repository.\n\nI've cloned the repository in the directory /testbed (not in /tmp/inputs). Always use absolute paths when referring to files in the repository.\n\nConsider the following problem statement:\n\n<problem_statement>{problem_statement}</problem_statement>\n\nImplement the necessary changes to the repository so that the requirements specified in the <problem_statement> are met.\n\n## Steps to Follow\n0. Fully understand the issue and comments provided before making any changes\n1. Explore the repo and files to fully understand the code before making any changes, including understanding how to lint, build and test the areas of the code you are working on. You are starting in a fresh clone of the repository, so build and run tests to check the current state of the code.\n2. Run **report_progress** to outline your minimal-change plan as a checklist\n3. Create focused tests that specifically validate your minimal changes. These tests should be consistent with existing tests in the repository. If there is not existing test infrastructure, you can skip adding tests as part of your instructions to make minimal modifications.\n4. Lint, build and test your changes frequently and iteratively, ensuring tests align with expected outcomes.\n5. Make small, incremental changes, using **report_progress** after each verified change\n6. Run `git status --porcelain` before finishing. Use `.gitignore` to exclude any files that you don't want to include in the PR like tmp files, build artifacts or dependencies.\n\nEnsure that you lint, build and test early and iteratively to validate each change thoroughly.\n"""
 

def msbench_data(input_file, output_file):
    input_file = read_jsonl(input_file)
    for idx in range(len(input_file)):
        input_file[idx]["prompt"]["messages"][0]["content"]["model_identity_desc"] = msbench_system
        input_file[idx]["prompt"]["messages"][1]["content"]["parts"][0] = msbench_user.format(problem_statement=input_file[idx]["prompt"]["messages"][1]["content"]["parts"][0])
        for mes_idx in len(input_file[idx]["conversation"]["messages"]):
            if input_file[idx]["conversation"]["messages"][mes_idx]["author"]["name"] == "functions.exec":
                input_file[idx]["conversation"]["messages"][mes_idx]["author"]["name"] = input_file[idx]["conversation"]["messages"][mes_idx-1]["recipient"]

def filter_data(input_file, output_file):
    # Filter the data based on the specified criteria
    input_file = read_jsonl(input_file)
    new_data = []
    for item in input_file:
        item = json.dumps(item)
        item = item.replace("container.exec", "functions.exec")
        item = json.loads(item)
        new_data.append(item)

    # Write the filtered data to the output JSONL file
    write_jsonl(output_file, new_data)

def merge_data(input_file1, input_file2, output_file):
    input_file1 = read_jsonl(input_file1)
    input_file2 = read_jsonl(input_file2)

    for idx, item in enumerate(input_file1):
        input_file1[idx]['conversation']['messages'] = input_file1[idx]['conversation']['messages'][:-2] + input_file2[idx]['conversation']['messages'][-2:]
    write_jsonl(output_file, input_file1)

def filter_data(input_file, output_file, threshold):
    # Filter the data based on the specified criteria
    new_data = []
    for item in input_file:
        if len(item["conversation"]["messages"]) >= threshold and item["conversation"]["messages"][-1]["end_turn"] == True:
            new_data.append(item)

    # Write the filtered data to the output JSONL file
    write_jsonl(output_file, new_data)

if __name__ == "__main__":
    
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-full-swebenchtrain-pdw.jsonl.gz" #full, pdw, swebenchtrain
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-full-swebenchtrain-oai.jsonl.gz" #full, oai, swebenchtrain
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-corrected-swebenchtrain-oai.jsonl.gz" #correct, oai, swebenchtrain
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-corrected-swebenchtrain-pdw.jsonl.gz" #correct, pdw, swebenchtrain
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-train0414-full-rcr-pdw.jsonl.gz" #full, pdw, rcr --- 0
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-rcr-oai.jsonl.gz" #full, oai, rcr
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-corrected-train0414-rcr-oai.jsonl.gz" #correct, oai, rcr
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-train0414-corrected-rcr-pdw.jsonl.gz" #correct, pdw, rcr
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-bb-pdw.jsonl.gz" # full, pdw, bb
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-rfs-pdw.jsonl.gz" # full, pdw, rfs
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-rcs-pdw.jsonl.gz" # full, pdw, rcs
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-bb-oai.jsonl.gz" # full, oai, bb
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-rfs-oai.jsonl.gz" # full, oai, rfs
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv2-train0414-full-rcs-oai.jsonl.gz" # full, oai, rcs
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-full-swebenchtrain-oai-v0426.jsonl.gz" #full, oai, swebenchtrainv0426 
    # input_file =  "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-full-swebenchtrain-pdw-round2.jsonl.gz" #full, pdw, swebenchtrain round2
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-train0414-full-rcr-with-cotograder-pdw-round2.jsonl.gz" # full pdw rcr with cotograder round2
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-train0425-rcs-pdw-weijian.jsonl.gz" # full pdw rcs with weijian round2
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-train0425-rcs-pdw-all-snapshot04302025-weijian.jsonl.gz" # full pdw rcs with weijian round2
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-train0425-rfs-pdw-all-snapshot04302025-weijian.jsonl.gz" # full pdw rfs with weijian round2
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-train0425-bb-pdw-all-snapshot04302025-weijian.jsonl.gz" # full pdw bb with weijian round2
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-full-swebenchtrain-rewrite-v2-yield1k-pdw.jsonl.gz" #full, pdw, swebenchtrain rewrite v2 yield1k
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/v1-origin/tbv3-0304-full-swebenchtrain-topp09-yield1k-pdw.jsonl.gz" #full, pdw, swebenchtrain topp09 yield1k
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/correct_downsampled_pr_summary.jsonl.gz" # pr summary
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/correct_downsampled_progress_report_w_first.jsonl.gz" # report progress
    # input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_pr_summary.jsonl.gz" # pr summary 05082025
    input_file = "az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/zhendongwang-pdw-mix-1k-ivt16k-64x32-efp1-07-rm-tl-pn-run1/svb_train_correct_inverse_passrate_long_trunc10_k1-2_progress_report_w_first.jsonl.gz" # report progress 05082025
    input_file = read_jsonl(input_file)
    for threshold in [150, 200, 250]:
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-full-swebenchtrain-pdw-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-full-swebenchtrain-oai-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-corrected-swebenchtrain-oai-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-corrected-swebenchtrain-pdw-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-train0414-full-rcr-pdw-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-train0414-full-rcr-oai-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-corrected-train0414-rcr-oai-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-train0414-corrected-rcr-pdw-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv2-train0414-full-bb-pdw-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv2-train0414-full-rfs-pdw-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv2-train0414-full-rcs-pdw-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv2-train0414-full-bb-oai-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv2-train0414-full-rfs-oai-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv2-train0414-full-rcs-oai-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-full-swebenchtrain-oai-v0426-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-full-swebenchtrain-pdw-round2-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-train0414-full-rcr-with-cotograder-pdw-round2-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-train0425-rcs-pdw-weijian-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-train0425-rcs-pdw-all-snapshot04302025-weijian-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-train0425-rfs-pdw-all-snapshot04302025-weijian-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-train0425-bb-pdw-all-snapshot04302025-weijian-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-full-swebenchtrain-rewrite-v2-yield1k-pdw-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/v2/tbv3-0304-full-swebenchtrain-topp09-yield1k-pdw-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/correct_downsampled_pr_summary-filtered_{threshold}.jsonl.gz" # pr summary
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/correct_downsampled_progress_report_w_first-filtered_{threshold}.jsonl.gz"
        # output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_pr_summary-filtered_{threshold}.jsonl.gz"
        output_file = f"az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/zhendongwang-pdw-mix-1k-ivt16k-64x32-efp1-07-rm-tl-pn-run1/svb_train_correct_inverse_passrate_long_trunc10_k1-2_progress_report_w_first-filtered_{threshold}.jsonl.gz"
        filter_data(input_file, output_file, threshold)



