export WANDB_BASE_URL="https://msaip.wandb.io"
export WANDB_API_KEY="****************************************"

# Timestamp for unique wandb name
DATE_ID=$(date +%m-%d-%H-%M-%Y)

## Model list:
# nv4: "az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted"
# nv4 step500: az://orngcresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted

    # dataset.globs.0.path="az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_progress_report_w_first_mid6_haoran.jsonl.gz" \
    # dataset.globs.1.path="az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_pr_summary_sysfix_haoran.jsonl.gz" \



## UKS:
# NAME="nv4-full-filtered-rcsrfsbb"
# BASE_CKPT="az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted" # NV4
# # BASE_CKPT="az://orngcresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted" # NV4 step500
# DATASET="az://orngcresco/data/haoranxu/swe/data/distill/v1/tbv2-train0414-full-*-filtered.jsonl.gz"
# EMA_HORIZON=1511   # IMPORTANT: this should be 10% of the total number of rows in the dataset!!
# LAYOUT="finetune-80g-sft"
# SECURITY="msft"

# important data:
# 1. merged report progress + PR data: az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_progress_report_w_first_mid6_and_pr_description_haoran.jsonl.gz
# 2. PR summary only 150 data: az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/correct_downsampled_pr_summary-filtered_150.jsonl.gz
# 3. Chen's data 0512, merge+fix: az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_merge_mid6_v3.jsonl.gz
# 4. Chen's data 0513， merge+fix+issuenumber+rollback: az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_merge_mixpr_rollback.jsonl.gz
# 5. Chen's data 0515, msbench style data: v2: az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_merge_mixpr_rollback_v2.jsonl.gz v3: az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_merge_mixpr_rollback_v3.jsonl.gz
# 6. Yang's data 0515 az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_merge_mixpr_rollback_v3.jsonl

# report progress fix + pr summary 0.4 * lr : az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_all_merged_haoran_150_step2-bz32-lr1.492e-05-date05-11-23-46-2025/e523fd8a-6cfc-4317-9a8d-de07c6af69e0/checkpoint/model1/000000000057
# report progress fix + pr summary lr : az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_all_merged_haoran_150_step2-bz32-lr3.73e-05-date05-11-23-46-2025/4877271b-410a-4265-837b-1b0210e2334d/checkpoint/model1/000000000057
# Base + pr summary fix lr*0.4: hpe2  az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_prs_only-bz32-lr1.492e-05-date05-12-02-35-2025/2b57ab0c-9012-4319-9964-b314f24852d1/checkpoint/model1/000000000057
# Base + pr summary fix lr*0.05: hpe3 az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_prs_only-bz32-lr1.865e-06-date05-12-02-38-2025/5cdb9ffd-5467-48aa-bad3-7278522e706a/checkpoint/model1/000000000057
# Base + pr summary fix lr*0.4 -> merged data lr 0.4 spec weight 1: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_prs_then_report_progress-bz32-lr1.492e-05-date05-12-07-22-2025/f56edb26-b737-4338-b4ce-d2e2cc7ad99b/checkpoint/model1/000000000020
# Report progress fixed model -> all PR summary data lr * 0.05: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_reported_fix_and_prs-bz32-lr1.865e-06-date05-12-06-23-2025/37d42c00-629f-4a3a-87db-6bbaaf6bb787/checkpoint/model1/000000000079
# Report progress fixed model -> all PR summary data lr * 0.4 bz64: hpe 5 az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_reported_fix_and_prs-bz64-lr1.492e-05-date05-12-06-24-2025/b6388bfc-d8e2-41ed-84c2-d9c029424e87/checkpoint/model1/000000000040
# SFT no masking on 0512 data, lr*0.4, spec weight 1: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_prs_then_report_progress-bz32-lr1.492e-05-date05-12-08-28-2025/e64f077a-cb35-425f-b4e5-ea683a304d38/checkpoint/model1/000000000020
# SFT no masking on 0512 data, lr*0.15, spec weight 1: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_prs_then_report_progress-bz32-lr5.595e-06-date05-12-09-04-2025/a5b85a76-5898-487c-8c04-ed7f6b66e5c9/checkpoint/model1/000000000020
# 68%ckpt SFT no masking on 0512 data, lr*0.4, spec weight 1: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_prs_then_report_progress-bz32-lr1.492e-05-date05-12-09-25-2025/f113bf20-6365-440a-9a12-9c1a867c03a0/checkpoint/model1/000000000020
# SFT, masking, weight 10 for PR des, 1 for others, lr * 0.4: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr1.492e-05-date05-12-16-44-2025/5b07b5c1-1d9f-4307-933d-d667bb80dc33/checkpoint/model1/000000000020  too good in report progress, ~40% in PR, low performace
# SFT non masking, weight 10 for PR des, 1 for others, lr * 0.05, 0512 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_non_mask_PRS_weight10-bz32-lr1.865e-06-date05-12-16-54-2025/591987b1-ef52-4221-aa83-905e6f1b0591/checkpoint/model1/000000000020  PR works, the others not, moderate performance
# SFT non masking,weight 10 for PR des, 1 for others, lr * 0.2, 0512 data:az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_non_mask_PRS_weight10-bz32-lr7.46e-06-date05-12-20-58-2025/eb4b507f-c151-41b0-a386-fd384af37c59/checkpoint/model1/000000000020
# SFT non masking,weight 10 for PR des 1 for others lr * 0.15, v3 data, more PR exposure: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_non_mask_PRS_weight10-bz32-lr5.595e-06-date05-13-00-07-2025/318d6e4c-3b85-40de-a365-568a3b3273bb/checkpoint/model1/000000000020
# SFT masking, weight 10 for PR, lr*0.2, v3 data, more PR exposure: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr7.46e-06-date05-13-04-23-2025/514fc954-6809-4238-9f92-be2506f61d60/checkpoint/model1/000000000020 # no report progress, good PRS, long output
# SFT masking, weight 10 for PR, lr*0.2, v3 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr7.46e-06-date05-13-04-22-2025/3ba50a7f-cb90-45fb-9c2a-362d24d43f36/checkpoint/model1/000000000020 # looks good so far, testing performance
# SFT masking, weight 10 for PR and speical tok, lr*0.2, v3 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr7.46e-06-date05-13-05-42-2025/3256578a-b2b3-48f2-9c40-e54cd1112be1/checkpoint/model1/000000000020 too much report progress, low performance
# SFT masking, weight 10 for PR and speical tok, lr*0.3, v3 data:  az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr1.119e-05-date05-13-05-59-2025/57f4b74d-bad0-4ed8-8afe-bb5405324f4c/checkpoint/model1/000000000020 too much report progress, low performance
# SFT masking, weight 10 for PR and speical tok, lr*0.1, v3 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr3.73e-06-date05-13-07-06-2025/f29d417c-5d37-4ca2-8abe-f3ffc08a0ef8/checkpoint/model1/000000000020 68.09-1.20-0.68
# SFT masking, weight 10 for PR and speical tok, lr*0.05, v3 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr1.865e-06-date05-13-08-12-2025/7825a900-b397-4fb9-bb63-2e6131fd2783/checkpoint/model1/000000000020 Middle: 65.7-0.01-96.53
# SFT masking, weight 10 for PR and speical tok, lr*0.3, 0514 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr1.119e-05-date05-13-07-38-2025/911be5ea-ad8d-4de9-b0c3-2e4b4990c3da/checkpoint/model1/000000000020 0.67-18-0.62 
# SFT masking, weight 10 for PR and speical tok, lr*0.15, 0514 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr5.595e-06-date05-13-08-20-2025/3572bda9-7741-4232-8753-377dc8f62e96/checkpoint/model1/000000000020 65.51-5.52-77.76 Candidate 1
# SFT masking, weight 15 for PR and 10 special, lr * 0.1, 0514 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight15-bz32-lr3.73e-06-date05-13-16-56-2025/1291f56b-5ee8-4c12-ab3f-7ccd15a1843f/checkpoint/model1/000000000020 bad report progress, good PR
# SFT masking, weight 20 for PR and 10 special, lr * 0.1, 0514 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight20-bz32-lr3.73e-06-date05-13-17-17-2025/2d0d6019-de57-4cd5-95fd-870944fcac20/checkpoint/model1/000000000020 bad report progress, good PR
# SFT masking, weight 20 for PR and 10 special, lr * 0.15, 0514 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight20-bz32-lr5.595e-06-date05-13-17-19-2025/a9566f2d-72e2-4774-9565-72098cbbe754/checkpoint/model1/000000000020 E2-8, new E2-4
# SFT masking, weight 10 for PR and 10 special, lr * 0.125, 0514 data: az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_mask_PRS_weight10-bz32-lr4.6625e-06-date05-13-22-58-2025/de3f71da-d44c-421c-b52b-f29b053e2b0c/checkpoint/model1/000000000020 E2-6 constraint, analysis, final
# SFT masking, weight 5 for PR and 5 special, lr * 0.11 0514 data: az://orngscuscresco/twapi/mini/e/haoranxu-0514-hotfix-bz32-lr4.103e-06-date05-15-02-14-2025/8d472263-1274-4659-b931-824c1e632e69/checkpoint/model1/000000000020
# SFT masking, weight 5 for PR and 5 special, lr * 0.1 MSbench data: az://orngscuscresco/twapi/mini/e/haoranxu-0515-msbench-hotfix-bz32-lr3.73e-06-date05-15-07-21-2025/bacdc57b-ffcb-4726-87a4-f6befcd2aff8/checkpoint/model1/000000000020 E-1 low report progress, good PR
# SFT masking, weight 10 for PR and 5 special, lr * 0.125 MSbench data: hpe3v2 az://orngscuscresco/twapi/mini/e/haoranxu-0515-msbench-hotfix-bz32-lr4.6625e-06-date05-15-09-10-2025/4dd08c20-58fe-4d73-bc55-3c7ce14c1c87/checkpoint/model1/000000000020 0515 candidate 2
# SFT masking, weight 5 for PR and 5 special, lr * 0.125 MSbench data: hpe3  az://orngscuscresco/twapi/mini/e/haoranxu-0515-msbench-hotfix-bz32-lr4.6625e-06-date05-15-09-10-2025/4dd08c20-58fe-4d73-bc55-3c7ce14c1c87/checkpoint/model1/000000000020 E-4 loos ok so far
# SFT masking, weight 5 for PR and 5 special, lr * 0.15 MSbench data: hpe2v2 az://orngscuscresco/twapi/mini/e/haoranxu-0515-msbench-hotfix-bz32-lr5.595e-06-date05-15-08-59-2025/f90be3bf-46b6-48ff-9120-900165c78f2c/checkpoint/model1/000000000020 a little more report progress 6, a little lower performance, 61
# SFT masking, weight 5 for PR and 5 special, lr * 0.11 MSbench data: hpe2v2 az://orngscuscresco/twapi/mini/e/haoranxu-0515-msbench-hotfix-bz32-lr4.103e-06-date05-15-17-04-2025/5e55edf9-4229-4cf8-bd0e-ec037c7aad7a/checkpoint/model1/000000000020 a litle low report progress, good PR
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.1： hpe2v2 az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr3.73e-06-date05-15-22-12-2025/988cb6eb-7eb5-4fc1-a19b-239c4091758a/checkpoint/model1/000000000020 E-6 207/307 2.97 mean report progress, 1 PR
# rlckpt SFT masking, Yang's data, weight 0.2 PR, weight 1 for all, lr * 0.1: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr3.73e-06-date05-15-23-25-2025/99de7ead-b903-4fca-8fd0-2f7af8f1369a/checkpoint/model1/000000000015 
# rlckpt SFT masking, Yang's data, weight 0.2 PR, weight 1 for all, lr * 0.2: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr7.46e-06-date05-15-23-44-2025/5e39640f-504b-4b37-9316-4f060b0ef9c3/checkpoint/model1/000000000015 Evaluate?
# rlckpt SFT masking, Yang's data, weight 0.2 PR, weight 1 for all, lr * 0.05 + previous unmask: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr1.865e-06-date05-16-00-40-2025/1cbba2b0-9c3a-4c1b-91c4-76af67d16d5f/checkpoint/model1/000000000015 too low report progress
# rlckpt SFT masking, Yang's data, weight 0.2 PR, weight 1 for all, lr * 0.2 + previous unmask: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr7.46e-06-date05-16-00-44-2025/93b8bfb2-7b32-4bec-b659-e4bc2b66020e/checkpoint/model1/000000000015  too low report progress
# rlckpt SFT masking, Yang's data, weight 0.2 PR, weight 1 for all, lr * 0.3 + previous unmask: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr1.119e-05-date05-16-04-19-2025/89f7aad5-fabc-4cb2-a5d7-7b44ea882e38/checkpoint/model1/000000000015
# rlckpt SFT masking, Yang's data, weight 0.2 PR, weight 1 for all, lr * 0.4 + previous unmask: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr1.492e-05-date05-16-03-46-2025/0da312bf-8613-46f5-a099-b1b6fdd829d2/checkpoint/model1/000000000015 E (learn to use text not code)
# rlckpt SFT masking, Yang's data, weight 0.2 PR, weight 1 for all, lr * 0.5 + previous unmask: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr1.865e-05-date05-16-04-49-2025/ca8478d0-ec49-45dc-8961-5847e64fb70d/checkpoint/model1/000000000015  
# rlckpt SFT masking, Yang's data, weight 0.2 PR, weight 1 for all, lr * 1.0 + previous unmask: No!
# rlckpt SFT masking, Yang's data, OnlyReportProgressAndRandom, lr*0.4, theshold 0.1 : az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr1.492e-05-date05-16-06-42-2025/d7c7a39f-560e-4ff3-9031-4900de5e7f93/checkpoint/model1/000000000015 
# rlckpt SFT masking, Yang's data, OnlyReportProgressAndRandom, lr*0.3 theshold 0.1: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr1.119e-05-date05-16-07-13-2025/105e9cdb-25ab-4994-8126-d32a6c5d7457/checkpoint/model1/000000000015
# rlckpt SFT masking, Yang's data, OnlyReportProgressAndRandom, lr*1, theshold 0.1 : az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr3.73e-05-date05-16-07-51-2025/4355720b-27d0-4137-a017-b337eb559349/checkpoint/model1/000000000015 
# rlckpt SFT masking, Yang's data, OnlyReportProgressAndRandom, lr*0.7, theshold 0.1 : az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr2.611e-05-date05-16-08-03-2025/1f54cb65-f0f0-479c-98de-84601580e4b9/checkpoint/model1/000000000015
# rlckpt SFT masking, Yang's data, OnlyReportProgressAndRandom, lr*0.55, theshold 0.1 : az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr2.0515e-05-date05-16-09-34-2025/aa131761-91f5-40a5-af11-9fe9d0b4d9ce/checkpoint/model1/000000000015 E-1
# rlckpt SFT masking, Yang's data, OnlyReportProgressAndRandom, lr*0.5, theshold 0.1 : az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr1.865e-05-date05-16-09-50-2025/99865f1b-2da9-4ad6-b978-01b3cd3b2c1c/checkpoint/model1/000000000015 E-4
# rlckpt SFT masking, Yang's data, OnlyReportProgressAndRandom, lr*0.6, theshold 0.1 : az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0515-msbench-hotfix-bz32-lr2.238e-05-date05-16-10-28-2025/b8fef610-417d-45ad-b9c7-e0a98345495e/checkpoint/model1/000000000015
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.05： az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr1.865e-06-date05-16-16-55-2025/88c06654-88cb-43c8-b318-3271db3d436a/checkpoint/model1/000000000020
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.075： az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr2.7975e-06-date05-16-16-57-2025/9789b91e-fa83-4c84-80c4-479cde51e860/checkpoint/model1/000000000020 
# rlckpt SFT masking, Yang's data, OnlyReportProgressAndRandom, lr*0.55, theshold 0.1 only cmd: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr2.0515e-05-date05-16-18-10-2025/61dc9c22-e2c4-4fac-a898-865f4d47b282/checkpoint/model1/000000000015 E-8
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.075 fixed: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr2.7975e-06-date05-16-18-14-2025/ef3c94f1-c262-4c4d-9f2c-90637508047d/checkpoint/model1/000000000020 E-4
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.1 fixedd: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr3.73e-06-date05-16-20-05-2025/f1efe6ed-2d66-4f85-af05-f873f51895fd/checkpoint/model1/000000000020 E-8
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.085 fixed: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr3.1705e-06-date05-16-19-40-2025/64a5d995-56b0-4188-8b84-0ad3b8e48b04/checkpoint/model1/000000000020 E-1
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.092 fixed: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr3.4316e-06-date05-16-20-48-2025/6a81426c-20a8-4f06-b9e7-53b6542ed392/checkpoint/model1/000000000020
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.11 fixed: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr4.103e-06-date05-16-21-23-2025/9102278c-abfa-42f8-afbd-3cfff041cd2c/checkpoint/model1/000000000020 E-6
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.125 fixed: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr4.6625e-06-date05-16-22-01-2025/f36e7ce7-f8bf-4187-ac8b-9f3acc220939/checkpoint/model1/000000000020
# rlckpt SFT masking, weight 0.2 PR, weight 1 for all, lr * 0.105 fixed: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr3.9165e-06-date05-16-23-02-2025/63b06289-eecb-405b-a4aa-012d6a05bbcd/checkpoint/model1/000000000020
# rlckpt SFT masking, Yang's data, OnlyReportProgressAndRandom, lr*0.105, theshold 1. only cmd: az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr3.9165e-06-date05-16-23-07-2025/703435c4-eced-4450-ba0f-32021f4b37d6/checkpoint/model1/000000000015 E-1



# python -m harmony_scripts.engines.start_engine \
# --name sweberry-v2-mini-engine \
# --mode=optimal \
# --snapshot_path="az://orngscuscresco/twapi/mini/e/haoranxu-rlckpt-0516-msbench-hotfix-bz32-lr4.103e-06-date05-16-21-23-2025/9102278c-abfa-42f8-afbd-3cfff041cd2c/checkpoint/model1/000000000020" \
# --is_multimodal=False \
# --gpu_kind=H100 \
# --renderer_name="harmony_v4.0.15_berry_v3_1mil_orion_lpe" \
# --restartable \
# --extra_config_string="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=2 pipe_depth=4 n_replicas=1" \
# --use_bus_v2=False \
# --n_replicas=1


NCTX=131072
BATCH_SIZE=32
LR=$(awk 'BEGIN{print 3.73e-5 * 0.105}')

## HPE:
NAME="rlckpt-0516-msbench-hotfix"
# BASE_CKPT="az://orngscuscresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted" # NV4
# BASE_CKPT="az://orngscuscresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted" # NV4 step500
# BASE_CKPT="az://orngscuscresco/twapi/mini/e/zhendongwang-data-mix-padawan-ivt16k-64x32-rm-run2/policy/step_000040/250427030407TTIPMSWH-0" # 68% NV4
# BASE_CKPT="az://orngscuscresco/twapi/mini/e/haoranxu-hotfix_all_newcmd_pr_prs_new-bz32-lr1.492e-05-date05-10-18-39-2025/90e3abc4-82b7-4545-8b6e-dd0dd8578e9f/checkpoint/model1/000000000040"
# BASE_CKPT="az://orngscuscresco/twapi/mini/e/haoranxu-cold-start-nv4-swe-train_filter150-128gpu-384yields-hpe5-131072ctx-65536ict-full-sb/policy/step_000020/250504030837KQ5KGYNX-0/" #71% NV4
# BASE_CKPT="az://orngscuscresco/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted/" # o4-mini
BASE_CKPT="az://orngscuscresco/twapi/mini/e/zhendongwang-mix-pdw2-ivt16k-32x32-o4mini-funcf-1-01-0-efc07-tpm1-run2/policy/step_000020/"

POSTPROCESS=OnlyReportProgressAndPRDescription #OnlyReportProgressAndPRDescription WeightNonAssistantMessagesPostprocess OnlyReportProgressAndRandom
EMA_HORIZON=64  # IMPORTANT: this should be 10% of the total number of rows in the dataset!!
LAYOUT="finetune-h100-80g-sft"
SECURITY="msft-orng" 

python -m mini.finetune.finetune2 \
    name="${NAME}-bz${BATCH_SIZE}-lr${LR}-date${DATE_ID}" \
    dataset=mini.finetune.datasets.glob_dataset:BerryHarmonyGlobDataset \
    dataset.globs.0.path="az://orngscuscresco/data/haoranxu/swe/data/distill/report_progress/svb_train_correct_inverse_passrate_long_trunc10_k1-2_merge_mixpr_rollback_v2.jsonl.gz" \
    dataset.batch_size=${BATCH_SIZE} \
    dataset.bypass_exceptions=True \
    dataset.shuffle=True \
    ...renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe \
    ...is_multimodal=True \
    base_model.ml_config="ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=False enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false enable_slow_replica_detection=False" \
    base_model.sampling_ml_config="ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True" \
    base_model.initial_checkpoint=${BASE_CKPT} \
    base_model.snapshot="scallion_lpe:d36" \
    base_model.layout=${LAYOUT} \
    base_model.local_cache_base=/tmp/tensorcache \
    ...n_ctx=${NCTX} \
    base_model.n_replicas=1 \
    ...encoding_name="orion_200k" \
    hyperparam_manager=const_hyperparam_manager.ConstLRHyperparamManager \
    hyperparam_manager.opt_config.lr_per_sample_d16=${LR} \
    hyperparam_manager.opt_config.ema_horizon=${EMA_HORIZON} \
    between_saves="1 epochs" \
    duration="1 epochs" \
    security_profile=${SECURITY} \
    github_upload=False \
    git_guard=False \
    wandb_enable=True \
    kafka_enable=False \
    use_shmsnap=False \
    initialize_timeout=999999 \
    timeout.default=999999 \
    load.restore_from_all_clusters=False \
    ...special_tokens_weight=5.0 \
    ...postprocessors.0=mini.finetune.datasets.postprocessors.postprocess:MaybeReformatDataPoint \
    ...postprocessors.1=mini.finetune.datasets.postprocessors.postprocess:LoadConvoPostprocess \
    ...postprocessors.2=mini.finetune.datasets.postprocessors.postprocess:${POSTPROCESS} \
    ...postprocessors.3=mini.finetune.datasets.postprocessors.postprocess:EnsureEndTurn
    # ...postprocessors.4=mini.finetune.datasets.postprocessors.postprocess:FixBudgetPostprocess \
    # ...postprocessors.4.max_num_yields=256 \
    # ...postprocessors.4.max_token_budget=131072
    
