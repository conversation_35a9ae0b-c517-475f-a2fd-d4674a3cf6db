import os
import requests
import json
import base64
import json
import io
from azure.identity import DefaultAzureCredential, get_bearer_token_provider

# Code snippet to execute, the input to test, and the expected output
# code = """print("Hello World")
# """
# testInput = ""
# expectedOutput = "Hello World\n"

code = """x = 1
print(x+2)
print(x+4)
"""
testInput = ""
expectedOutput = "3\n5\n"

# Prepare the data payload
url = 'https://ces.azurewebsites.net/api/ces/executeCode'

credential = DefaultAzureCredential()
bearer_token_provider = get_bearer_token_provider(credential, "api://52c57900-7d62-4551-b4e9-bde729180e86/.default")

data = {
    "code": base64.b64encode(code.encode("utf-8")).decode("utf-8"),
    "testInput": testInput,
    "expectedOutput": expectedOutput,
    "language": "python3",
    # "dependencies": ["torch"]
}

# Convert data to JSON format
headers = {
    'Content-Type': 'application/json',
    "Authorization": "Bearer " + bearer_token_provider()
}
# Send POST request
response = requests.post(url, headers=headers, data=json.dumps(data))

# Check if the request was successful
if response.status_code == 200:
    # Parse the JSON response
    json_response = response.json()

    # Print the JSON response
    print(json.dumps(json_response, indent=2))
else:
    # Print the error message
    print(f"Error: {response.status_code}, {response.text}")