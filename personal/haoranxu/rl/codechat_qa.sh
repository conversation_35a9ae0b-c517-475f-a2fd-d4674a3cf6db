LR=5e-6
IPB=64
SPI=32
ICT=24576
NAME=codechat_nv4_qa_stackexchangehard_gt_BZ${IPB}xSPI${SPI}_ICT_${ICT}_$(date +%m%d-%H%M)
PASSRATE_THRESHOLD=0.05
PASSRATE_MAX_THRESHOLD=0.95
WANDB_PROJECT_NAME=codechat
WANDB_ENTITY=genaicore
SAVE_FREQ=20
N_CTX=131072
GPUNUM=80
# o4mini: CKPT=az://orngscuscresco/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted
# rkld-ed CKPT=az://orngscuscresco/twapi/mini/e/haoranxu-nv4-mix7-o30402-512x1-0620-1728/policy/step_000500/250620172849UUO3TBGE-0/
# rkld + rl: CKPT=az://orngscuscresco/twapi/mini/e/haoranxu-codechat_nv4_rlmix1_BZ64xSPI32_ICT_24576_0704-0834/policy/step_000140/250704083519FTBOTFBM-0/
CKPT=az://orngscuscresco/twapi/mini/e/haoranxu-codechat_nv4_rlmix1_BZ64xSPI32_ICT_24576_0704-0834/policy/step_000140/250704083519FTBOTFBM-0/
TEACHER_CKPT=az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted
USER=deepchat

beam start
CMD=(
beam python --use-cwd -m qstar.run_experiment nostrict 
    name=$NAME

    ...dataset=HarmonyCompletionDatasetConfig
    batcher.curriculum.training_datasets.0.dataset.dataset_id=data.haoranxu.rkld.stackexchange.hard_only
    batcher.curriculum.training_datasets.0.dataset.grader=deepchat_msft.graders.code_qa_grader:CodeQALLMGrader
    batcher.curriculum.training_datasets.0.dataset.instance_completer=qstar.instance_completers.variants_instance_completer:VariantsInstanceCompleter
    batcher.curriculum.training_datasets.0.dataset.grader.topic=${TEACHER_CKPT}
    batcher.curriculum.training_datasets.0.dataset.grader.line=bus
    batcher.curriculum.training_datasets.0.dataset.grader.topic_mode_or_user=${USER}
    batcher.curriculum.training_datasets.0.dataset.grader.prompt_template=gt_codeqa
    batcher.curriculum.training_datasets.0.dataset.grader.rollback_prompt_template=codeqa

    ...dataset_container=orngscuscresco
    ...datapoint_converters.0.name=deepchat_msft.common.datapoint_converters:msft_postprocess_deepchat_qa_harmony_data
    defaults.instance_completer.instance_optimizer=qstar.instance_optimizers.variants_instance_optimizer:VariantsInstanceOptimizer
    
    :berry_models.scallion_lpe:d36_80g_mbg16_bf16
    policy.initial_checkpoint=${CKPT}
    policy.n_gpus=${GPUNUM}
    policy.is_multimodal=True
    policy.n_ctx=${N_CTX}
    root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
    policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
    policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True'


    timeout.evaluate=10000
    timeout.rollout=50000
    timeout.teach=10000
    timeout.default=None

    defaults.instances_per_batch=$IPB
    defaults.target_samples_per_instance=$SPI 
    ...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe
    ...renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe

    berry_curriculum=berry_curriculums.MultiEpochCurriculum
    berry_curriculum.max_epochs=1

    
    optimizer.hparam_scaler.lr_per_instance_d16=$LR
    ...save_every=$SAVE_FREQ

    defaults.max_tokens=$N_CTX
	defaults.n_ctx=$N_CTX
	defaults.instance_completer.pass_rate_minimum=$PASSRATE_THRESHOLD 
	defaults.instance_completer.pass_rate_maximum=$PASSRATE_MAX_THRESHOLD
	defaults.channel_config=None
    defaults.sampler.harmony_constrained_sampling=True
    defaults.inverse_token_cost=${ICT}


	peashooter.timeout_seconds.stalled_datapoint=3600
    peashooter.sampling_concurrency=64
	peashooter.num_sampling_processes=16
    defaults.sampler.n_ctx=$N_CTX
    batch_completer.n_batches_in_flight=10
    
    defaults.instance_completer.instance_optimizer.sample_preprocessor=TokenCostPreprocessor

    seed=20250624
    security_profile=msft-orng
    github_upload=False
    wandb_enable=True
    kafka_enable=False
    enable_slackbot=False
    wandb.wandb_project=${WANDB_PROJECT_NAME}

)

"${CMD[@]}"