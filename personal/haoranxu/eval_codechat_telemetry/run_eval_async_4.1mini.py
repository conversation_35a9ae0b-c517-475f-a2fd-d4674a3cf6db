import os
import json
import asyncio
import argparse
from typing import List, <PERSON><PERSON>, Dict, Any
from tqdm import tqdm

from bus_token_completer import BusTokenCompleter
from chat import chat
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter
from legacy_rest_token_completer import LegacyRestTokenCompleter
from token_completer import CompleterBackend
from message_completer import TokenMessageCompleter
from chat.render.renderer_registry import get_renderer
from message_completer.message_completer import ParseErrorMode
from bus_token_completer import BusTokenCompleter, QoSType
import re

def extract_after_final(text: str) -> str:
    """
    Extracts content in the following order:
      1. Everything after the literal "[final]" marker.
      2. If no "[final]" is found, everything between a ```final fence and the next ```.
    Strips leading/trailing whitespace. If neither is found, returns the original text.
    """
    # 1) Try "[final]" marker
    marker_pattern = re.compile(r'(?s)\[final\](.*)')
    marker_match = marker_pattern.search(text)
    if marker_match:
        return marker_match.group(1).strip()
    
    # 2) Try fenced block ```final ... ```
    fence_pattern = re.compile(r'(?s)```final\s*(.*?)```')
    fence_match = fence_pattern.search(text)
    if fence_match:
        return fence_match.group(1).strip()
    
    # 3) Fallback to original text
    return text

def setup_completer_and_renderer(max_tokens: int = 65535):
    """Initialize and return the token completer and renderer."""
    token_completer_config = LegacyRestTokenCompleter.Config(
        api_base="http://127.0.0.1:5122/v1/inference",
        backend=CompleterBackend.FALCON_MM_BACKEND,
    )
    
    ## Use Bus:
    # token_completer_config = BusTokenCompleter.Config(
    #     topic_mode_or_user="haoranxu",
    #     topic_or_snapshot="az://orngscuscresco/models/snapshots/sweberry-v3-tbv3-20250304-decrypted",
    #     bus_line="bus",
    #     qos_type=QoSType.FIFO,
    # )

    #renderer_name = "harmony_v4.0.16_berry_v3_1mil_orion_lpe"
    renderer_name = "harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs"
    message_completer_config = TokenMessageCompleter.Config(
        token_completer_config=token_completer_config,
        renderer=renderer_name,
        completion_params={"model": "model", "temperature": 1, "max_tokens": max_tokens},
    )
    
    return message_completer_config.build(), get_renderer(renderer_name)

def count_tokens(text: str) -> int:
    """
    Simple token counting function that approximates tokens by splitting on whitespace.
    For more accurate counting, you could use a proper tokenizer like tiktoken.
    """
    return len(text.split())

async def process_question(question_data: Dict[str, Any], message_completer, renderer) -> Dict[str, Any]:
    """Process a single question and return the result."""
    user_request = question_data["user_request"]
    metadata = question_data["metadata"]
    original_messages = question_data["messages"]

    messages: list[chat.Message] = []
    system_message = chat.Message.system(
        model_identity_desc="You should carefully adhere to all formatting instructions.",
            tools_section=None,
            channel_config=chat.SystemChannelConfig(
                valid_channels=("analysis", "final"), channel_required=True
            ),
            metadata=chat.SystemContentMetadata(reward_multiplier=256),
        )
    messages.append(system_message)

    # Use the user request directly without any template
    messages.append(chat.Message.user(content=user_request))
    conversation = chat.Conversation(messages=messages)

    for _ in range(10):
        try:
            completion = await message_completer.async_completion(
                conversations=[conversation], n=1,
            )
            choice_output = None
            for choice in completion.choices:
                new_messages = choice.get_messages(
                    parse_error_mode=ParseErrorMode.SYSTEM_ERROR
                )
                all_parts = []
                final_output = []
                for m in new_messages:
                    for p in m.content.parts:
                        all_parts.append(p)
                choice_output = '\n\n'.join(all_parts)
                final_output = extract_after_final(choice_output)
                if not final_output:
                    print("warning, no final output, try again")
                    continue
            break
        except Exception as e:
            completion = None
            choice_output = None

    if not choice_output:
        return {
            "user_request": user_request,
            "full_answer": "",
            "matched": False,
            "metadata": metadata,
            "messages": original_messages,
            "token_count": 0,
            "final_answer": final_output,
        }

    token_count = count_tokens(choice_output)
    
    return {
        "user_request": user_request,
        "full_answer": choice_output,
        "matched": True,
        "metadata": metadata,
        "messages": original_messages,
        "token_count": token_count,
        "final_answer": final_output,
    }

async def process_question_worker(question_data: Dict[str, Any], max_tokens: int = 65535) -> Dict[str, Any]:
    """Worker function to process a single question. Creates its own completer and renderer."""
    message_completer, renderer = setup_completer_and_renderer(max_tokens)
    
    return await process_question(question_data, message_completer, renderer)

def load_questions_from_file(file_path: str) -> List[Dict[str, Any]]:
    """Load all questions from a file into memory."""
    questions = []
    with open(file_path, "r") as file:
        for line in file:
            if not line.strip():
                continue
            data = json.loads(line)
            # Extract user request from messages
            user_request = ""
            messages = data.get("messages", [])
            for message in messages:
                if message.get("role") == "user":
                    user_request = message.get("content", "")
                    break
            
            questions.append({
                "user_request": user_request,
                "metadata": data.get("metadata", {}),
                "messages": messages
            })
    return questions

async def process_input_file(file_path: str, concurrency_limit: int = 64, max_tokens: int = 65535) -> Tuple[List[Dict[str, Any]], int]:
    """Process all questions in a single input file using asyncio with semaphore."""
    questions = load_questions_from_file(file_path)
    
    semaphore = asyncio.Semaphore(concurrency_limit)
    results = []

    async def process_with_semaphore(question_data):
        async with semaphore:
            return await process_question_worker(question_data, max_tokens)

    tasks = [process_with_semaphore(question_data) for question_data in questions]
    
    # Use asyncio.as_completed to show progress
    for task in tqdm(asyncio.as_completed(tasks), 
                     total=len(questions),
                     desc=f"Processing {os.path.basename(file_path)}",
                     unit="questions"):
        result = await task
        results.append(result)
    
    non_match_count = sum(1 for result in results if not result["matched"])
    return results, non_match_count

def ensure_output_directory(directory: str = "./outputs") -> None:
    """Create output directory if it doesn't exist."""
    os.makedirs(directory, exist_ok=True)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run eval async processing")
    parser.add_argument(
        "--output", 
        type=str, 
        default="./outputs",
        help="Output directory for results (default: ./outputs)"
    )
    parser.add_argument(
        "--num_tests",
        type=int,
        default=5,
        help="Number of test runs (default: 5)"
    )
    parser.add_argument(
        "--concurrency",
        type=int,
        default=64,
        help="Maximum number of concurrent requests (default: 64)"
    )
    parser.add_argument(
        "--max_tokens",
        type=int,
        default=32768,
        help="Maximum number of tokens for completion (default: 32768)"
    )
    return parser.parse_args()

async def main():
    """Main execution function."""
    args = parse_arguments()
    
    input_files = [
        "eval_telemetry_v.0.0.jsonl"
    ]
    
    ensure_output_directory(args.output)
    
    for test_idx in range(args.num_tests):
        all_results = []
        total_non_matches = 0
        
        for input_file in input_files:
            try:
                file_results, file_non_matches = await process_input_file(
                    input_file, args.concurrency, args.max_tokens
                )
                all_results.extend(file_results)
                total_non_matches += file_non_matches
            except Exception as e:
                print(f"Error processing file {input_file}: {e}")
                continue
        
        # Calculate mean token count
        valid_token_counts = [result["token_count"] for result in all_results if result["token_count"] > 0]
        mean_token_count = sum(valid_token_counts) / len(valid_token_counts) if valid_token_counts else 0
        
        output_file = f"{args.output}/test_{test_idx}.jsonl"
        with open(output_file, "w") as file:
            for result in all_results:
                file.write(json.dumps(result) + "\n")
        
        print(f"Test {test_idx}: Failed answers: {total_non_matches}")
        print(f"Test {test_idx}: Mean token count: {mean_token_count:.2f} tokens")

if __name__ == "__main__":
    asyncio.run(main())
