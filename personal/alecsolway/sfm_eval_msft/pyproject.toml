[project]
name = "sfm_eval_msft"
version = "0.1.0"
dependencies = ["pydantic", "blobfile"]

[tool.oaipkg]
monorepo-dependencies = [
    "chat", 
    "token_completer",
    "bus_token_completer", 
    "message_completer",
    "turn_completer"
]
ruff_full_check = true

[project.scripts]
sfm_eval_orng = "sfm_eval_msft.main:main"
as_bus_orng = "sfm_eval_msft.bus:main"

[tool.buildkite]
name = "sfm_eval_msft"
pipeline = "buildkite/pipeline.yml"
owner = "@alecsolway"

[tool.ruff]
extend = "../../pyproject.toml"

[tool.ruff.lint]
extend-select = ["ANN2"]

[tool.setuptools.packages.find]
include = ["sfm_eval_msft*"]

[build-system]
requires = ["setuptools>=64.0"]
build-backend = "setuptools.build_meta"

[tool.mypy]
strict = true
local_partial_types = true

# These two make life easier if you use a lot of untyped libraries:
warn_return_any = false
allow_untyped_calls = true

# Do not use `ignore_missing_imports`, instead use:
# disable_error_code = ["import-untyped"]

# If you don't want to force use of type annotations:
# allow_incomplete_defs = true
# allow_untyped_defs = true
# allow_any_generics = true
