from types import MappingProxyType

from pydantic import BaseModel


class SnapshotConfig(BaseModel):
    snapshot_path: str
    renderer_name: str
    extra_engine_args: str | None = None
    num_nodes: int | None = None
    model_config_name: str | None = None
    training_extra_config_string: str | None = None


_SNAPSHOTS = {
    "gpt-5-reasoning": SnapshotConfig(
        snapshot_path="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas/",
        renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_tools_v2",
        extra_engine_args=' --model_config=falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal \\\n --is_multimodal=True \\\n --extra_config_string="load_inference_snapshots_with_tcv2=False raise_on_load_for_missing_tensor=False ixf_sampling_extension_gpu_to_cpu_async=False twapi_cpu_comm_backend=gloo ixf_batcher_response_loop_timeout=600 allow_embedding_prefix_loading=True tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0} ixf_max_cache_list_length=4096" \\\n --encoder_decoder_snapshots \'{"clip":"az://orngcresco/models/snapshots/mm/oaiomni/rcall/crow-export/scallion2b/omni/model_final_ts.pt"}\'',
        num_nodes=2,
        model_config_name="scallion-d64-fp16rgs",
    ),
    "gpt-5-chat": SnapshotConfig(
        snapshot_path="az://orngscuscresco/models/snapshots/scallion_d64-mm_arm2a-1900-8shard-zen-fp8-comm-2025-08-13-22-52-decrypted/",
        renderer_name="harmony_v4.0.16_128k_orion_berry_mm_no_asr_16k_action",
        extra_engine_args=' --model_config=falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal \\\n --is_multimodal=True \\\n --extra_config_string="load_inference_snapshots_with_tcv2=False raise_on_load_for_missing_tensor=False ixf_sampling_extension_gpu_to_cpu_async=False twapi_cpu_comm_backend=gloo ixf_batcher_response_loop_timeout=600 allow_embedding_prefix_loading=True tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={None:0,(0,200166):0}" \\\n  --encoder_decoder_snapshots \'{"clip":"az://orngscuscresco/models/snapshots/mm/oaiomni/rcall/crow-export/scallion2b/omni/model_final_ts.pt"}\'',
        num_nodes=2,
        model_config_name="scallion-d64-fp16rgs",
    ),
    "gpt-5-nano": SnapshotConfig(
        snapshot_path="az://orngscuscresco/models/snapshots/gpt5n-t7-resume2-s170-2025-08-04-21-13-decrypted",
        renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2",
        extra_engine_args=' --is_multimodal=False \\\n --extra_config_string="n_op_shards=8 pipe_depth=1" \\\n  --use_ev3=True',  # --model_config=falcon.orion.d16ar120-s128-k4-bf16-clover-mm-nown \\\n
        num_nodes=1,
        model_config_name="clover-d16-nown",
        training_extra_config_string="twppo.leek.common twppo.clover.common twppo.training n_expert_shards=1 n_replicas=1 rollbackv2_use_delayed_copy=False wt_available_during_forward=False dp_collective_ops_use_tree=False enable_tensorcache_v2=True use_chicken=False combo_chicken_scallion_next=False softmax_xent_implementation=triton_sharded moe_implementation=triton replicate_kv_in_kernel=True dynamic_esharding_enabled=False minimal_dynamic_esharding_enabled=False dynamic_esharding_softmax_after_dropping=False dynamic_esharding_use_2stage_rescaling_scheme=False use_applied_comms_for_opsharding=False dynamic_esharding_fuse_opsharding_comm=False attn_weight_compute_dtype=torch.bfloat16 moe_weight_compute_dtype=torch.float8e5",
    ),
    "gpt-4.1-nano": SnapshotConfig(
        snapshot_path="az://orngscuscresco/models/snapshots/4o-tt-v5-nano-transferred-2025-04-10-decrypted",
        renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc",
        extra_engine_args=" --is_multimodal=False \\\n --use_ev3=True",  # --model_config=falcon.orion.d16ar120-s128-k4-bf16-clover-mm-nown \\\n
        num_nodes=1,
        model_config_name="clover-d16-nown",
    ),
    "gpt-4.1-mini": SnapshotConfig(
        snapshot_path="az://orngscuscresco/models/snapshots/4o-tt-v5-mini-transferred-2025-04-11-decrypted",
        renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc",
        extra_engine_args=' --model_config=falcon.multimodal.runs.scallion-d36-s64-lpe \\\n --is_multimodal=False \\\n --extra_config_string="enable_tensorcache_v2=False"',
        num_nodes=1,
        model_config_name="scallion-lpe-s64-d36",
    ),
}
SNAPSHOTS = MappingProxyType(_SNAPSHOTS)
