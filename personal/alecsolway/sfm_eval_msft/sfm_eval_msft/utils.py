import argparse
import json

import blobfile as bf
import sfm_eval_msft.models as eval_models
from bus_token_completer import BusTokenCompleter, QoSType
from message_completer.token_message_completer import TokenMessageCompleter
from token_completer import TokenCompleter
from turn_completer.simple_turn_completer import <PERSON><PERSON>urnCompleter
from turn_completer.turn_completer import TurnCompleter


def argparse_add_model_args(parser: argparse.ArgumentParser) -> None:
    parser.add_argument("-m", "--model", type=str, choices=eval_models.SNAPSHOTS.keys())  # fmt: skip
    parser.add_argument("--snapshot", type=str, help="Override or set (if no model specified) snapshot path.")  # fmt: skip
    parser.add_argument("--renderer", type=str, help="Override or set (if no model specified) renderer.")  # fmt: skip


def argparse_check_model_args(parser: argparse.ArgumentParser, args: argparse.Namespace) -> None:
    if not args.model and not args.snapshot:
        parser.error("You must specify either a model or a snapshot.")
    if not args.model and not args.renderer:
        parser.error("You must specify either a model or a renderer.")


def argparse_get_label(args: argparse.Namespace) -> str:
    return args.label if args.label else args.model


def load_jsonl(file_path: str) -> list[dict[str, str]]:
    data = []
    with bf.BlobFile(file_path, "r") as file:
        for idx, line in enumerate(file):
            try:
                data.append(json.loads(line))
            except json.JSONDecodeError as e:
                raise ValueError(f"Error decoding JSON at line {idx} in {file_path}: {e}") from e
    return data


def get_input_file_paths(input_dir: str) -> list[str]:
    return [bf.join(input_dir, file) for file in bf.listdir(input_dir) if file.endswith(".jsonl")]


def get_output_file_path(output_dir_base: str, label: str, input_file_path: str) -> str:
    return bf.join(output_dir_base, label, bf.basename(input_file_path))


def datum_to_prompt(datum: dict[str, str]) -> str:
    if "instruction" not in datum or "input" not in datum:
        raise ValueError("Datum must contain 'instruction' and 'input' keys.")
    prompt = datum["instruction"]
    if datum["input"] and datum["input"].strip():
        prompt += "\n\n" + datum["input"]
    return prompt


def datum_to_output(datum: dict[str, str]) -> str:
    if "output" not in datum:
        raise ValueError("Datum must contain 'output' key.")
    return datum["output"]


def get_snapshot_config(
    model: str | None, snapshot_path: str | None, renderer_name: str | None
) -> eval_models.SnapshotConfig:
    if model:
        config = eval_models.SNAPSHOTS[model]
        if snapshot_path:
            config.snapshot_path = snapshot_path
        if renderer_name:
            config.renderer_name = renderer_name
    else:
        config = eval_models.SnapshotConfig(
            snapshot_path=snapshot_path, renderer_name=renderer_name
        )
    return config


def get_turn_completer(
    snapshot_config: eval_models.SnapshotConfig, bus_topic: str, temperature: float = 0
) -> TurnCompleter:
    bus_token_completer_config = BusTokenCompleter.Config(
        topic_mode_or_user=bus_topic,
        topic_or_snapshot=snapshot_config.snapshot_path,
        bus_line="bus",
        qos_type=QoSType.ROUND_ROBIN_BY_USER,
    )
    completion_params: TokenCompleter.Params = {"temperature": temperature}
    token_message_completer = TokenMessageCompleter.Config(
        token_completer_config=bus_token_completer_config,
        renderer=snapshot_config.renderer_name,
        completion_params=completion_params,
    )
    simple_turn_completer = SimpleTurnCompleter.Config(
        message_completer=token_message_completer,
    )
    return simple_turn_completer.build()


def extract_answer(s: str) -> str:
    start_index = s.lower().find("answer:")
    return s[start_index:] if start_index != -1 else s


def remove_quotes(s: str) -> str:
    if (s.startswith('"') and s.endswith('"')) or (s.startswith("'") and s.endswith("'")):
        return s[1:-1]
    return s
