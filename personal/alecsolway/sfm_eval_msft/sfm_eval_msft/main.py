import argparse
import asyncio
import hashlib
import json
import logging
import random
import sys

import blobfile as bf
import sfm_eval_msft.utils as eval_utils
import tenacity
from chat import chat
from sfm_eval_msft.special_model_output_preprocessing import SPECIAL_MODEL_OUTPUT_PREPROCESSING
from tqdm.asyncio import tqdm
from turn_completer.turn_completer import TurnCompleter

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)
stream_handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter(
    "%(asctime)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
)
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument("-i", "--input", type=str, default="az://orngscuscresco/data/sfm_eval_input", help="Path to the input directory containing jsonl data files.")  # fmt: skip
    parser.add_argument("-o", "--output", type=str, default="az://orngscuscresco/data/sfm_eval_output", help="Path to the base output directory where results will be saved.")  # fmt: skip
    parser.add_argument("-l", "--label", type=str, required=True, help="Label to append to the output directory name for versioning.")  # fmt: skip
    parser.add_argument("-b", "--bus_topic", type=str, required=True)
    parser.add_argument("-n", "--num_datum_per_source", type=int, default=1000, help="Number of data points to process per input source file. These are randomized with a seed based on each source filename.")  # fmt: skip
    parser.add_argument("-t", "--temperature", type=float, default=0)
    parser.add_argument("-w", "--num_workers", type=int, default=60)
    parser.add_argument("--operation", type=str, choices=["inference", "tabulate", "all"], default="all")  # fmt: skip
    parser.add_argument("--overwrite", action="store_true", help="Overwrite existing output files, otherwise only process input files that do not have corresponding output files.")  # fmt: skip
    parser.add_argument("--seed_prefix", type=str, default="", help="Base16 prefix to add to the random seed based on input filename.")  # fmt: skip
    eval_utils.argparse_add_model_args(parser)
    args = parser.parse_args()
    eval_utils.argparse_check_model_args(parser, args)

    return args


@tenacity.retry(
    wait=tenacity.wait_random_exponential(min=1, max=30),
    before_sleep=tenacity.before_sleep_log(logger, logging.WARNING),
)
async def run_completer(
    turn_completer: TurnCompleter, convo: chat.Conversation
) -> TurnCompleter.Completion:
    return await turn_completer.async_completion(convo)


def truncate_data(
    args: argparse.Namespace, input_file: str, data: list[dict[str, str]]
) -> list[dict[str, str]]:
    new_data = data
    if args.num_datum_per_source < len(data):
        random.seed(
            int(args.seed_prefix + hashlib.md5(bf.basename(input_file).encode()).hexdigest(), 16)
        )
        new_data = random.sample(data, args.num_datum_per_source)
    return new_data


async def inference_worker(
    args: argparse.Namespace,
    input_file: str,
    data: asyncio.Queue[tuple[int, dict[str, str]] | None],
    results: list[str | None],
    pbar: tqdm,
) -> None:
    turn_completer = eval_utils.get_turn_completer(
        eval_utils.get_snapshot_config(args.model, args.snapshot, args.renderer),
        args.bus_topic,
        args.temperature,
    )
    while True:
        result = await data.get()
        if result is None:
            break
        idx, datum = result
        try:
            convo = chat.Conversation(
                messages=[chat.Message.user(eval_utils.datum_to_prompt(datum))]
            )
            completion = await run_completer(
                turn_completer, turn_completer.initialize_conversation(convo)
            )
            output_messages = completion.output_messages
            if not output_messages:
                logger.warning(
                    f"No output messages returned from the turn completer for datum {idx} in {bf.basename(input_file)}."
                )
            else:
                results[idx] = str(output_messages[-1].content)
            pbar.update(1)
        except Exception as e:
            logger.error(f"Error processing datum {idx} in {bf.basename(input_file)}: {e}")
            raise


async def inference(args: argparse.Namespace) -> None:
    input_files = eval_utils.get_input_file_paths(args.input)
    for input_file in input_files:
        # Prepare and load.
        output_file = eval_utils.get_output_file_path(args.output, args.label, input_file)
        if bf.exists(output_file) and not args.overwrite:
            logger.info(
                f"Skipping {bf.basename(input_file)} as output file {output_file} already exists."
            )
            continue
        data_list = eval_utils.load_jsonl(input_file)
        data_list = truncate_data(args, input_file, data_list)
        data = asyncio.Queue()
        for idx, datum in enumerate(data_list):
            data.put_nowait((idx, datum))
        results: list[str | None] = [None] * data.qsize()
        pbar = tqdm(total=data.qsize(), desc=f"Processing {bf.basename(input_file)}")
        for _ in range(args.num_workers):
            data.put_nowait(None)
        # Run.
        workers = [
            asyncio.create_task(inference_worker(args, input_file, data, results, pbar))
            for _ in range(args.num_workers)
        ]
        await asyncio.gather(*workers)
        pbar.close()
        # Save.
        with bf.BlobFile(output_file, "w") as file:
            for result in results:
                file.write(json.dumps({"output": result}) + "\n")


def tabulate(args: argparse.Namespace) -> dict[str, float]:
    accuracy = {}
    input_files = eval_utils.get_input_file_paths(args.input)
    for input_file in input_files:
        output_file = eval_utils.get_output_file_path(args.output, args.label, input_file)
        if not bf.exists(output_file):
            logger.info(
                f"Skipping {bf.basename(input_file)} as output file {output_file} does not exist."
            )
            continue
        data = eval_utils.load_jsonl(input_file)
        data = truncate_data(args, input_file, data)
        results = eval_utils.load_jsonl(output_file)
        if len(data) != len(results):
            raise ValueError(
                f"Data and results length mismatch for {bf.basename(input_file)}: {len(data)} vs {len(results)}"
            )
        accuracy[bf.basename(input_file)] = 0
        for idx, (datum, result) in enumerate(zip(data, results)):
            if result["output"] is None:
                continue
            try:
                model_output = (
                    eval_utils.extract_answer(eval_utils.remove_quotes(result["output"]))
                    .strip()
                    .lower()
                )
                if bf.basename(input_file) in SPECIAL_MODEL_OUTPUT_PREPROCESSING:
                    model_output = SPECIAL_MODEL_OUTPUT_PREPROCESSING[bf.basename(input_file)](model_output)  # fmt: skip
                if model_output == eval_utils.datum_to_output(datum).strip().lower():
                    accuracy[bf.basename(input_file)] += 1
            except Exception as e:
                logger.error(f"Error processing datum {idx} in {bf.basename(input_file)}: {e}")
                raise
        accuracy[bf.basename(input_file)] /= len(data)
    return accuracy


def main() -> None:
    args = parse_args()
    if args.operation == "inference" or args.operation == "all":
        asyncio.run(inference(args))
    if args.operation == "tabulate" or args.operation == "all":
        accuracy = tabulate(args)
        print("Accuracy results:")
        file_pad = max(len(file) for file in accuracy.keys()) if accuracy else 0
        for file, acc in accuracy.items():
            print(f"{file:<{file_pad}} {acc:>10.2f}")
