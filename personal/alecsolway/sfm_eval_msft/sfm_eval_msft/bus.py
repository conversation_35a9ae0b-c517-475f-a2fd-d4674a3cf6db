import argparse
import getpass
import os
import random
import re
import string
import subprocess
import tempfile
from pathlib import Path
from typing import TextIO

import sfm_eval_msft.utils as eval_utils

START_ENGINE_SCRIPT = """
export OPENAI_API_KEY="fake"

beam start

RUST_BACKTRACE=1 nohup python -m harmony_scripts.engines.start_engine \\
--mode=optimal \\
--snapshot_path="{snapshot_path}" \\
--gpu_kind=H100 \\
--renderer_name="{renderer}" \\
--cluster=local \\
--bus_enable_qos=True \\
--bus_rate_limiter=KV_UTIL \\
--bus_topic_mode_or_user="{user_or_topic}" \\
--n_replicas=1 \\
--restartable {extra_args}
"""


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument("-n", "--num_instances", type=int, default=3)
    parser.add_argument("-b", "--bus_topic", type=str, default=getpass.getuser())
    parser.add_argument("--extra_engine_args", type=str, default="")
    parser.add_argument("-p", "--num_nodes", type=int)
    eval_utils.argparse_add_model_args(parser)
    args = parser.parse_args()
    eval_utils.argparse_check_model_args(parser, args)

    snapshot_config = eval_utils.get_snapshot_config(args.model, args.snapshot, args.renderer)
    if snapshot_config.num_nodes is None and args.num_nodes is None:
        parser.error("You must specify --num_nodes if pre-defined model name is not given.")

    return args


def start_engine_tempfile() -> tuple[TextIO, str]:
    dir = tempfile.gettempdir()
    while True:
        chars = string.ascii_lowercase + string.digits
        name = "".join(random.choices(chars, k=15))
        path = os.path.join(dir, name)
        try:
            fd = os.open(path, os.O_RDWR | os.O_CREAT | os.O_EXCL)
            return os.fdopen(fd, "w+t"), path
        except FileExistsError:
            continue


def main() -> None:
    args = parse_args()
    snapshot_config = eval_utils.get_snapshot_config(args.model, args.snapshot, args.renderer)
    extra_engine_args = (
        snapshot_config.extra_engine_args if snapshot_config.extra_engine_args else ""
    )
    if args.extra_engine_args:
        if extra_engine_args:
            extra_engine_args += " \\\n"
        extra_engine_args += args.extra_engine_args
    extra_engine_args = "\\\n" + extra_engine_args if extra_engine_args else ""
    temp_f, temp_file_path = start_engine_tempfile()
    temp_f.write(
        START_ENGINE_SCRIPT.format(
            snapshot_path=snapshot_config.snapshot_path,
            renderer=snapshot_config.renderer_name,
            user_or_topic=args.bus_topic,
            extra_args=extra_engine_args,
        )
    )
    temp_f.close()
    output = subprocess.run(
        [
            "python",
            Path("~/code/glass/personal/bolian/cloud-bus-shooter.py").expanduser(),
            "--quota-team",
            "team-moonfire-security",
            "--cluster",
            "prod-southcentralus-hpe-2",
            "--priority_class",
            "team-high",
            "--num_pods",
            str(args.num_nodes) if args.num_nodes is not None else str(snapshot_config.num_nodes),
            "--engine",
            temp_file_path,
            "--user",
            getpass.getuser(),
            "--snapshot_path",
            snapshot_config.snapshot_path,
            "--start_num",
            "1",
            "--end_num",
            str(args.num_instances),
        ],
        capture_output=True,
        text=True,
    )
    # This is hacky, we should just re-implement cloud-bus-shooter.py for our purposes, but works for now.
    # job_names = re.findall(r"Creating job:\s*(.+)", output.stdout)
    # if not job_names:
    #    raise RuntimeError(
    #        f"Failed to detect created jobs for b sync:\n{output.stdout}\n{output.stderr}"
    #    )
    # subprocess.run(["rcall-brix", "sync"] + job_names)
    # print(f"\nCreated jobs: {', '.join(job_names)}")
