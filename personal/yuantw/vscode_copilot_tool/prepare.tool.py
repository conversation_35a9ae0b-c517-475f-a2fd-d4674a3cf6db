import asyncio
import argparse
import subprocess
import time
from caas.api import caas_api
from caas.protocol import Tmpfs, NetworkMode
from caas.commands import DownloadFileToUrl, RawBashScript
from caas import CaasSession

import structlog
logger = structlog.stdlib.get_logger(component=__name__)

# so we don't need to install nodejs
IMAGE="aio"

def parse_args():
    parser = argparse.ArgumentParser(description="Prepare deb files.")
    parser.add_argument(
        "--caas-endpoint", type=str, help="CAAS endpoint (e.g., 'https://southcentralus.caas.azure.com')."
    )
    parser.add_argument(
        "--src", type=str, help="vscode copilot source code on github (e.g., 'https://github.com/wangyuantao/vscode-copilot')."
    )
    parser.add_argument(
        "--mode", type=str, help="clone_only,skip_clone,all"
    )
    parser.add_argument(
        "--branch", type=str, help="vscode copilot source code branch name (e.g., 'main')."
    )
    parser.add_argument(
        "--storage-account-name", type=str, help="Azure storage account name."
    )
    parser.add_argument(
        "--container-name", type=str, help="Azure storage container name."
    )
    parser.add_argument(
        "--output-tool-blob-name", type=str, help="Blob name (e.g., 'tools/vscode-copilot-tools/vscode-copilot-tools.v3.tar')."
    )
    return parser.parse_args()

async def get_sas(storage_account_name: str, container_name: str, blob_name: str) -> str:
    # like 2024-12-16T00:00Z
    import datetime
    expire=datetime.datetime.now() + datetime.timedelta(days=2)
    expire=expire.strftime("%Y-%m-%dT%H:%MZ")
    cmd=f"""
az storage blob generate-sas \
    --account-name {storage_account_name} \
    --container-name {container_name} \
    --name {blob_name} \
    --permissions w \
    --expiry {expire} \
    --auth-mode login \
    --as-user \
    --https-only \
    --output tsv
"""
    logger.info(f"Running command: {cmd}")
    t0 = time.time()
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        raise Exception(f"Command failed: {result.stderr}")
    logger.info(f"Command output: {result.stdout.strip()} elapsed: {time.time() - t0:.2f} seconds")
    return result.stdout.strip()

async def run(container: CaasSession, cmd: str) -> str:
    """
    Run a command in the container.
    :param container: The container to run the command in.
    :param cmd: The command to run.
    """
    logger.info(f"Running command: {cmd}")
    t0 = time.time()
    output = await container.run(RawBashScript(cmd, login=True, timeout=600))
    logger.info(f"Exit Code: {output[0]} stdout: {output[1].decode()} elapsed: {time.time() - t0:.2f} seconds")
    if output[0] != 0:
        raise Exception(f"Command failed with exit code {output[0]}: {output[1].decode()}")
    return output[1].decode()

async def prepare_src(src: str, branch: str) -> str:
    """
    Prepare the source code for vscode copilot.
    :param src: The source code path or url.
    :return: The prepared source code path.
    """
    if src.startswith("http"):
        tmp = "/tmp/vscode-copilot"        
        cmd=f"""
rm -rf {tmp}
mkdir -p {tmp}
git clone {src} --depth 1 --single-branch --branch {branch} {tmp}
    """
        logger.info(f"Running command: {cmd}")
        t0 = time.time()
        import subprocess
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"Command failed: {result.stderr}")
        logger.info(f"Command output: {result.stdout.strip()} elapsed: {time.time() - t0:.2f} seconds")
        return tmp
    else:
        # use the local source code
        return src

async def tar(src: str) -> str:
    """
    Tar the source code.
    :param src: The source code path.
    :return: The tar file path.
    """
    tar_file = f"/tmp/vsc.tar"
    cmd=f"""
rm -rf {tar_file}
tar -cf {tar_file} -C {src} .
ls -lh {tar_file}
    """
    logger.info(f"Running command: {cmd}")
    t0 = time.time()
    import subprocess
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        raise Exception(f"Command failed: {result.stderr}")
    logger.info(f"Command output: {result.stdout.strip()} elapsed: {time.time() - t0:.2f} seconds")
    return tar_file

async def main():
    args = parse_args()
    caas_endpoint = args.caas_endpoint
    output_blob = args.output_tool_blob_name
    storage_account_name = args.storage_account_name
    container_name = args.container_name
    mode = args.mode
    if mode not in ["clone_only", "skip_clone", "all"]:
        raise ValueError(f"Invalid mode: {mode}. Must be one of 'clone_only', 'skip_clone', 'all'.")
    logger.info(f"using mode: {mode}")

    if mode == "skip_clone":
        src_tar = args.src
        logger.info(f"Using src tar file: {src_tar} from args.src")
    else:
        logger.info(f"Output tool blob name: {output_blob}")
        src = await prepare_src(args.src, args.branch)
        logger.info(f"Prepared src: {src} from {args.src} branch: {args.branch}")
        src_tar = await tar(src)
        logger.info(f"Created src tar file: {src_tar}")
        if mode == "clone_only":
            logger.info("Mode is clone_only, exiting after cloning.")
            return

    tar_bytes = open(src_tar, "rb").read()
    logger.info(f"Got src tar file size: {len(tar_bytes)}")
    
    caas = caas_api(endpoint=caas_endpoint)
    wdir = "/app"
    memory_limit: str = "32g"
    tmpfs = [Tmpfs(container=wdir, size=memory_limit)]
    async with caas.use_session(image=IMAGE,
                                #log_mode="full",
                                tmpfs=tmpfs,
                                memory_limit=memory_limit,
                                cpu_limit="32",
                                disk_limit="32gb",
                                pids_limit=1024,
                                network=NetworkMode.BRIDGE,
                                ) as container:
        logger.info("Container created", id=container.id)
        # upload src tar file
        await container.container.write("/app/vscode-copilot.tar", tar_bytes)
        logger.info("Uploaded src tar file") # 3s

        # untar src tar file
        await run(container, "rm -rf /app/vscode-copilot")
        await run(container, "mkdir -p /app/vscode-copilot")
        await run(container, "tar -xf /app/vscode-copilot.tar -C /app/vscode-copilot") # 1s
        await run(container, "rm -rf /app/vscode-copilot.tar")
        logger.info("Untarred src tar file")

        # install deb packages
        await run(container, f"""
apt install -y libnss3 libdbus-1-3 libatk1.0-0 xvfb dbus-x11 libatk-bridge2.0-0 libgtk-3-0 libgbm1 libasound2 x11-xkb-utils xfonts-100dpi xfonts-75dpi xfonts-scalable xauth
""") # 10s
        
        # Resolve the certificate issue for vscode.
        await run(container, """
apt install -y libnss3-tools

NSSDB_DIR="$HOME/.pki/nssdb"
rm -rf "$NSSDB_DIR"
mkdir -p "$NSSDB_DIR"

if [ -z "$(ls -A $NSSDB_DIR)" ]; then
    stderr "NSS database directory is empty. Initializing…"
    certutil -d "$NSSDB_DIR" -N --empty-password
else
    stderr "NSS database already initialized."
fi

# this section adds all the certificates that exist in the directory
for cert_file in "/usr/local/share/ca-certificates"/*.crt; do
    stderr "Adding certificate [$cert_file] to NSS database…"
    cert_name=$(basename "$cert_file" .crt)
    certutil -d "$NSSDB_DIR" -A -t "C,," -n "CaaS-$cert_name" -i "$cert_file"
done
""")

        # install vsc tool dependencies
        await run(container, f"""
npm config set registry $CAAS_ARTIFACTORY_NPM_REGISTRY
git config --global url.$CAAS_GIT_MIRROR_URL/github.com/.insteadOf https://github.com/
export GITHUB_PAT=1
export WORKSPACEFOLDER=/app/vscode-copilot
cd $WORKSPACEFOLDER
npm install""")
        await run(container, f"""
export GITHUB_PAT=1
export WORKSPACEFOLDER=/app/vscode-copilot
cd $WORKSPACEFOLDER
npm run compile""")
        await run(container, f"""
export GITHUB_PAT=1
export WORKSPACEFOLDER=/app/vscode-copilot
cd $WORKSPACEFOLDER
npm run test:extension""")
        
        await run(container, "tar -cf /app/vscode-copilot.tar -C /app/vscode-copilot .")
        await run(container, "ls -lh /app/vscode-copilot.tar")

        sas = await get_sas(storage_account_name, container_name, output_blob)
        logger.info(f"Got SAS: {sas}")
        url = f"https://{storage_account_name}.blob.core.windows.net/{container_name}/{output_blob}?{sas}"
        t0 = time.time()
        await container.run(DownloadFileToUrl("/app/vscode-copilot.tar", url, timeout=600))
        logger.info(f"DownloadFileToUrl elapsed: {time.time() - t0:.2f} seconds")

if __name__ == "__main__":
    asyncio.run(main())