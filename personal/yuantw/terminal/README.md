# New

If you use caas_tool from OAI after 8/15 FI, no need to bake terminal server.py or executable into the user image, because `terminal_session.wait_until_ready` is [removed](https://dev.azure.com/project-argos/Mimco/_git/torchflow-mirror/commit/2338ff213d28d07720313d9c0737811c6151edab?refName=refs/heads/openai/msft/rlhf-master&path=/lib/harmony_tools/caas_tool/caas_tool/caas_container.py&_a=compare&tab=details).
See `new.py` as example.

# Legacy

If you use orange/main today (8/28) which is based on July FI, you can mount `terminal_tool_server` executable directly to avoid python package dependency conflict, like pydantic.

1. Build your image from `Dockerfile` as example. If you already have an image that have conflict python package dependency (Similar to `Dockerfile.conflict`), you can based on that existing image, simplely add the ENTRYPOINT as `terminal_tool_server` like `Dockerfile.fix`, then all layers are reused. Note that you even don't have to bake `terminal_tool_server` executable into your image!
2. When `new_container`, mount the `terminal_tool_server` executable from CaaS regional shared system storage account host `/mnt/azure_blob/...` to user container `/usr/bin/`.
3. When container start, it will execute docker defined entrypoint `terminal_tool_server` found at `/usr/bin/`, and terminal_session.wait_until_ready will curl healthcheck from that local serivce in user container.
