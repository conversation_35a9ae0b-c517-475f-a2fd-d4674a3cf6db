az acr login -n acrcommitcaaseastus2dev
BASE_IMAGE_TAG=********-1342042-official
docker build \
    -f Dockerfile.fix . \
    -t acrcommitcaaseastus2dev.azurecr.io/yuantw/conflict_example:829a_fix \
    --push

# DO NOT try to run
# docker run -it acrcommitcaaseastus2dev.azurecr.io/yuantw/conflict_example:829a_fix
# Because you will need CaaS to mount terminal_tool_server from storage account
# otherwise you will see error
# docker: Error response from daemon: failed to create task for container: failed to create shim task: OCI runtime create failed: runc create failed: unable to start container process: error during container init: exec: "terminal_tool_server": executable file not found in $PATH: unknown