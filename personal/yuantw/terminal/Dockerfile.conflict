
ARG BASE_IMAGE_TAG=grab_one_from_pipeline
FROM acrbuiltincaasglobaldev.azurecr.io/aio-venv:${BASE_IMAGE_TAG}
#FROM acrbuiltincaasglobaldev.azurecr.io/terminal-light:${BASE_IMAGE_TAG}

# terminal server uses 1.10.15
RUN pip install pydantic==2.11.7

# when run it:
# docker run -it acrcommitcaaseastus2dev.azurecr.io/yuantw/conflict_example:829a
# you should see 
# ImportError: cannot import name 'Undefined' from 'pydantic.fields' 