from smokey import Smokey
import logging, asyncio
from caas.protocol import VolumeMount
from caas_tool.caas_container import CaasContainer
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main(endpoint: str = "https://southcentralus.caas.azure.com",
               image: str = "acrcommitcaassouthcentralusame.azurecr.io/yuantw/conflict_example:829a_fix",
               mount_path: str = "/mnt/azure_blob/tools/terminal/20250828/terminal_tool_server"
               ):
    mnt = [VolumeMount(host=mount_path, container="/usr/bin/terminal_tool_server", deprecated_use_blobfuse=True)]
    
    container: CaasContainer = await CaasContainer.new(caas_endpoint=endpoint, image_name=image, volume_mounts=mnt)
    try:
        res = await container.exec(["ls", "-lh", "/"], None, None, None)
        logger.info(f"Output: [{res[0]}] {res[1].decode()}")
        res = await container.exec(["ps", "aux"], None, None, None)
        logger.info(f"Output: [{res[0]}] {res[1].decode()}")
        # res = await container.exec(["pkill", "-f", "terminal_tool_server"], None, None, None)
        # logger.info(f"Output: [{res[0]}] {res[1].decode()}")
    finally:
        await container.teardown()
if __name__ == "__main__":
    import asyncio
    asyncio.run(Smokey(main))