from smokey import Smokey
import logging, asyncio
from caas_tool.caas_container import CaasContainer
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main(endpoint: str = "https://eastus2.caas.azure.com",
               image: str = "acrbuiltincaasglobalame.azurecr.io/ubuntu:22.04"
               ):
    # new caas_tool doesn't depends on terminal tool server, so you can use any image! Even there is no curl!
    container: CaasContainer = await CaasContainer.new(caas_endpoint=endpoint, image_name=image)
    try:
        res = await container.exec(["ls", "-lh", "/"], None, None, None)
        logger.info(f"Output: [{res[0]}] {res[1].decode()}")
        res = await container.exec(["ps", "aux"], None, None, None)
        logger.info(f"Output: [{res[0]}] {res[1].decode()}")
    finally:
        await container.teardown()

if __name__ == "__main__":
    import asyncio
    asyncio.run(Smoke<PERSON>(main))