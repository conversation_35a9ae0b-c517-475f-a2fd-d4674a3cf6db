import asyncio
from caas.api import caas_api
from caas.protocol import SandboxRuntime
from caas.commands import RawBashScript
from smokey import Smokey
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEST_SCRIPT = """
git config --global url.$CAAS_GIT_MIRROR_URL/github.com/.insteadOf https://github.com/
git clone https://github.com/docker-library/busybox
cd busybox
git checkout dist-amd64
cd latest/glibc
docker build -t test .
docker run --rm -i test echo 'Hello from Docker!'
"""

async def main(
    endpoint: str = "https://eastus2.caas.azure.com",
    image: str = "acrbuiltincaasglobalame.azurecr.io/test-dind:20250812",
):
    caas = caas_api(endpoint=endpoint)
    print("Creating container session...")
    async with caas.use_session(
        image=image,
        sandbox_runtime=SandboxRuntime.KATA_PRIVILEGED_V2
    ) as caas_session:
        logger.info("Running hello-world as a dind container...")
        output = await caas_session.run(RawBashScript(TEST_SCRIPT, timeout=300))
        logger.info(f"Exit code: {output[0]}\nOutput:\n{output[1].decode()}")

if __name__ == "__main__":
    asyncio.run(Smokey(main))
