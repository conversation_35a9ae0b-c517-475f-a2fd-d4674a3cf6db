#!/bin/zsh
set -euo pipefail

OUTPUT_FILE="$HOME/.cherry_picked_commits"
: > "$OUTPUT_FILE"

CURRENT_DIR=$(pwd)
trap 'cd "$CURRENT_DIR"' EXIT

record_commit() {
  local repo_path=$1
  shift
  local commits=("$@")

  echo "==> Entering: $repo_path"
  cd "$repo_path"

  for commit in "${commits[@]}"; do
    git fetch origin "$commit"
    git cherry-pick "$commit"

    new_commit=$(git rev-parse HEAD)
    echo "$repo_path $new_commit" >> "$OUTPUT_FILE"
    echo "Recorded: $repo_path $new_commit"
  done
}

# openai repo
record_commit "$HOME/code/openai" \
  feb942c48498fb65f3e3842b36c919ddfb250d38 \
  ab54bd9a7a929dd7359b1871595053d573b7af1d

# glass repo
record_commit "$HOME/code/glass" \
  c339c5999dcdf5a96f7283c3191030d44ae1e723

echo "✅ All cherry-picks done. New commits recorded in $OUTPUT_FILE"