#!/bin/zsh
set -euo pipefail

INPUT_FILE="$HOME/.cherry_picked_commits"
if [[ ! -f "$INPUT_FILE" ]]; then
  echo "❌ Error: No recorded commits file found at $INPUT_FILE"
  exit 1
fi

CURRENT_DIR=$(pwd)
trap 'cd "$CURRENT_DIR"' EXIT

while read -r repo_path commit_id; do
  echo "==> Entering: $repo_path"
  cd "$repo_path"

  # revert 这个 commit
  git revert --no-edit "$commit_id"
  echo "Reverted $commit_id in $repo_path"
done < "$INPUT_FILE"

echo "✅ All recorded commits reverted. Returned to $CURRENT_DIR"