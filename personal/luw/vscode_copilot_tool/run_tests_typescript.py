import asyncio
import json
import traceback

import blobfile as bf
import pytest
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from caas.commands import UploadFile, RawBashScript

from deep_swe_msft.tests.vsc_helpers import get_tool, invoke_tool, exist_text
from deep_swe_msft.tools.utils import CRESCO_STORAGE_NAME
from deep_swe_msft.tools.vscode_copilot_tool import (
    LANGUAGE_UNIFORMS,
    new_container,
    debug_startup,
)
from mini.metrics import metrics_init
from deep_swe_msft.tools.utils import CAAS_ENDPOINT

pytestmark = pytest.mark.asyncio

def _get_samples(path: str) -> dict:
    print("Loading sample data")
    with bf.BlobFile(
        path,
        "r",
    ) as f:
        samples = [json.loads(line) for line in f]
    print(f"Loaded sample data, num_samples={len(samples)}")
    return samples

async def _get_container(sample: dict) -> CaasContainer:
    print("Creating CAAS container")
    container = await new_container(CAAS_ENDPOINT, "aio")
    container.terminal_session.session.start_keepalive_task(keepalive_interval=30)
    print("CAAS container created, setting up VSC utils")

    language = "python"
    if "metadata" in sample:
        metadata = sample["metadata"]
        language = metadata.get("lang", None) or metadata.get("top_language", None) or "python"
    language = language.lower()
    if language in LANGUAGE_UNIFORMS:
        language = LANGUAGE_UNIFORMS[language]
    if language == "python":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.python.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "javascript":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.javascript.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "java":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.java.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "csharp":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.csharp.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    elif language == "typescript":
        from deep_swe_msft.rfs_rcs_bb_ml_vsc.typescript.datasets.mrfs_setup import mrfs_setup_fn_coreutils
    else:
        raise ValueError(f"Unsupported language: {language}")

    res = await mrfs_setup_fn_coreutils(
        datapoint=sample,
        terminal_session=TerminalSession(container.caas_session, endpoint=CAAS_ENDPOINT),
    )

    assert res["setup_done"] is True, f"Expected setup to complete successfully, but got {res=}"

    print("CAAS container created")
    return container

BATCH_SIZE = 64

async def test_run_tests_for_all(dataset_path: str):
    metrics_init(config=dict())
    samples = [s for s in _get_samples(dataset_path) if s["metadata"]["vscode_extensions"]]

    with open("run_tests_failures.log", "w") as failure_log:
        for i in range(0, len(samples), BATCH_SIZE):
            batch_samples = samples[i:i + BATCH_SIZE]
            print(f"Running tests for batch {i // BATCH_SIZE + 1} with {len(batch_samples)} samples.")
            tasks = [asyncio.create_task(_run_single_test(sample)) for sample in batch_samples]
            run_results = await asyncio.gather(*tasks, return_exceptions=True)
            run_results_by_id = {s["unique_id"]: r for s, r in zip(samples, run_results)}
            failures = []
            for unique_id, run_result in run_results_by_id.items():
                if isinstance(run_result, Exception):
                    failures.append((unique_id, run_result))
                    continue
                if exist_text("No test failures were found.", run_result):
                    failures.append((unique_id, run_result))
                    continue
                if exist_text("VSCode Copilot tool error", run_result):
                    failures.append((unique_id, run_result))
                    continue
                if not exist_text("<testResult passed=false>", run_result):
                    failures.append((unique_id, run_result))
                    continue
            print(f"Batch {i // BATCH_SIZE + 1} completed. Failures: {len(failures)} out of {len(batch_samples)} samples.")
            if failures:
                print(f"Failures in batch {i // BATCH_SIZE + 1}:\n{failures}")
                for id, result in failures:
                    failure_log.write(f"{id}\t{result}\n")
                failure_log.flush()


async def _run_single_test(sample):
    container = await _get_container(sample)
    python_tool = get_tool(container, 120000)
    print(f"Tool is ready.")
    res = await invoke_tool(
        python_tool, "runTests", { "files": [] }
    )
    await container.terminal_session.session.stop_keepalive_task()
    await container.terminal_session.close()
    return res

if __name__ == "__main__":
    python_dataset = "az://orngcaas/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/python/train/rfs/3p/processed_train.jsonl"
    javascript_dataset = "az://orngcaas/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/javascript/train/rfs/3p/processed_train_add_configs.jsonl"
    typescript_dataset = "az://orngcaas/data/swe-model-training-data/rfs_data/train_data/mrfs_caas/latest/typescript/train/rfs/3p/processed_train_add_configs.jsonl"
    #asyncio.run(test_run_tests_for_all(python_dataset))
    #asyncio.run(test_run_tests_for_all(javascript_dataset))
    asyncio.run(test_run_tests_for_all(typescript_dataset))

