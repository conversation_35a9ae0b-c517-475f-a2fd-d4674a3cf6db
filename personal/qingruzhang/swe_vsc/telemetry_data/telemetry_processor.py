#!/usr/bin/env python3
"""
VS Code Copilot Telemetry Data Processor

This script processes VS Code Copilot telemetry data to extract comprehensive conversation information
including user queries, model responses, context files, and tool usage.
"""

import json
import re
import argparse
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path


@dataclass
class ConversationTurn:
    """Represents a single turn in a conversation (user query + model response)"""
    turn_number: int
    timestamp: str
    request_id: str
    conversation_id: str
    
    # User input
    user_query: str
    intent: str = ""
    context_types: str = ""
    
    # Model response 
    model_response: str = ""
    base_model: str = ""
    
    # Context information
    attached_files: List[Dict] = field(default_factory=list)
    workspace_context: List[str] = field(default_factory=list)
    available_tools: List[str] = field(default_factory=list)
    tools_used: Dict[str, int] = field(default_factory=dict)
    
    # Raw message data sent to model
    full_prompt: str = ""
    prompt_token_length: int = 0
    
    # Metadata
    chat_location: str = ""  # panel, inline, etc.
    language_id: str = ""


@dataclass
class ConversationSession:
    """Represents a complete conversation session"""
    session_id: str
    start_time: str
    end_time: str = ""
    turns: List[ConversationTurn] = field(default_factory=list)
    total_turns: int = 0


class TelemetryProcessor:
    """Processes VS Code Copilot telemetry data to extract conversation information"""
    
    def __init__(self):
        self.sessions: Dict[str, ConversationSession] = {}
        self.pending_turns: Dict[str, Dict] = {}  # request_id -> partial turn data
        
    def process_telemetry_file(self, file_path: str) -> Dict[str, ConversationSession]:
        """Process a telemetry JSONL file and return conversation sessions"""
        
        with open(file_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    if 'vscodesession' in data:
                        for event in data['vscodesession']:
                            self._process_event(event)
                except Exception as e:
                    print(f"Error processing line {line_num}: {e}")
                    continue
        
        # Finalize any pending turns
        self._finalize_pending_turns()
        
        return self.sessions
    
    def _process_event(self, event: Dict):
        """Process a single telemetry event"""
        event_name = event.get('name', '')
        
        if event_name == 'GitHub.copilot-chat/interactiveSessionMessage':
            self._process_user_message(event)
        elif event_name == 'GitHub.copilot-chat/interactiveSessionResponse':
            self._process_model_response(event)
        elif event_name == 'GitHub.copilot.chat/engine.messages':
            self._process_engine_messages(event)
        elif event_name == 'GitHub.copilot-chat/toolCallDetailsInternal':
            self._process_tool_usage(event)
        elif event_name == 'GitHub.copilot-chat/languagemodelrequest':
            self._process_language_model_request(event)
        elif event_name == 'GitHub.copilot-chat/conversation.messageText':
            self._process_conversation_message(event)
    
    def _process_user_message(self, event: Dict):
        """Process user message events"""
        if 'data' not in event or 'baseData' not in event['data']:
            return
            
        props = event['data']['baseData'].get('properties', {})
        measurements = event['data']['baseData'].get('measurements', {})
        
        request_id = props.get('requestId', '')
        session_id = props.get('sessionId', props.get('conversationId', ''))
        
        if not request_id or not session_id:
            return
        
        # Initialize session if not exists
        if session_id not in self.sessions:
            self.sessions[session_id] = ConversationSession(
                session_id=session_id,
                start_time=event.get('time', ''),
            )
        
        # Create or update turn
        if request_id not in self.pending_turns:
            self.pending_turns[request_id] = {}
            
        turn_data = self.pending_turns[request_id]
        turn_data.update({
            'session_id': session_id,
            'request_id': request_id,
            'timestamp': event.get('time', ''),
            'turn_number': measurements.get('turnNumber', 0),
            'user_query': props.get('query', ''),
            'intent': props.get('intent', ''),
            'context_types': props.get('contextTypes', ''),
            'chat_location': props.get('chatLocation', ''),
            'base_model': props.get('baseModel', ''),
        })
    
    def _process_model_response(self, event: Dict):
        """Process model response events"""
        if 'data' not in event or 'baseData' not in event['data']:
            return
            
        props = event['data']['baseData'].get('properties', {})
        request_id = props.get('requestId', '')
        
        if request_id in self.pending_turns:
            turn_data = self.pending_turns[request_id]
            turn_data.update({
                'model_response': props.get('response', ''),
                'user_query': props.get('request', turn_data.get('user_query', '')),  # Backup
                'base_model': props.get('baseModel', turn_data.get('base_model', '')),
            })
    
    def _process_engine_messages(self, event: Dict):
        """Process engine messages to extract full conversation context"""
        if 'data' not in event or 'baseData' not in event['data']:
            return
            
        props = event['data']['baseData'].get('properties', {})
        request_id = props.get('headerRequestId', '')
        
        if request_id in self.pending_turns:
            turn_data = self.pending_turns[request_id]
            
            # Reconstruct full messagesJson from multiple parts
            messages_json = self._reconstruct_messages_json(props)
            turn_data['full_prompt'] = messages_json
            
            # Extract context files and workspace information from the prompt
            context_info = self._extract_context_from_messages(messages_json)
            turn_data.update(context_info)
    
    def _process_tool_usage(self, event: Dict):
        """Process tool usage information"""
        if 'data' not in event or 'baseData' not in event['data']:
            return
            
        props = event['data']['baseData'].get('properties', {})
        conversation_id = props.get('conversationId', '')
        
        # Find the most recent turn in this conversation
        if conversation_id in self.sessions:
            session = self.sessions[conversation_id]
            if session.turns:
                latest_turn = session.turns[-1]
                latest_turn.available_tools = json.loads(props.get('availableTools', '[]'))
                
                tool_counts = props.get('toolCounts', '{}')
                if isinstance(tool_counts, str):
                    latest_turn.tools_used = json.loads(tool_counts)
                else:
                    latest_turn.tools_used = tool_counts
    
    def _process_language_model_request(self, event: Dict):
        """Process language model request for additional context"""
        if 'data' not in event or 'baseData' not in event['data']:
            return
            
        props = event['data']['baseData'].get('properties', {})
        measurements = event['data']['baseData'].get('measurements', {})
        
        request_id = props.get('requestid', '')
        if request_id in self.pending_turns:
            turn_data = self.pending_turns[request_id]
            turn_data.update({
                'prompt_token_length': measurements.get('tokenCount', 0),
                'model_name': props.get('model', ''),
                'full_query': props.get('query', ''),
            })
    
    def _process_conversation_message(self, event: Dict):
        """Process conversation message events as backup for missing data"""
        if 'data' not in event or 'baseData' not in event['data']:
            return
            
        props = event['data']['baseData'].get('properties', {})
        source = props.get('source', '')
        message_text = props.get('messageText', '')
        request_id = props.get('headerRequestId', '')
        
        if request_id in self.pending_turns:
            turn_data = self.pending_turns[request_id]
            if source == 'user' and not turn_data.get('user_query'):
                turn_data['user_query'] = message_text
            elif source == 'model' and not turn_data.get('model_response'):
                turn_data['model_response'] = message_text
    
    def _reconstruct_messages_json(self, props: Dict) -> str:
        """Reconstruct the full messagesJson from multiple parts"""
        messages_parts = []
        
        # Get the base messagesJson
        base_messages = props.get('messagesJson', '')
        if base_messages:
            messages_parts.append(base_messages)
        
        # Get all the additional parts (messagesJson_02, messagesJson_03, etc.)
        i = 2
        while f'messagesJson_{i:02d}' in props:
            messages_parts.append(props[f'messagesJson_{i:02d}'])
            i += 1
        
        return ''.join(messages_parts)
    
    def _extract_context_from_messages(self, messages_json: str) -> Dict:
        """Extract context files and workspace information from the full prompt"""
        context_info = {
            'attached_files': [],
            'workspace_context': [],
        }
        
        if not messages_json:
            return context_info
        
        try:
            # Try to parse as JSON to extract structured context
            if messages_json.startswith('['):
                messages = json.loads(messages_json)
                for message in messages:
                    if isinstance(message, dict) and 'content' in message:
                        content = message['content']
                        # Look for file references
                        file_matches = re.findall(r'filepath[\'"]?\s*:\s*[\'"]([^\'\"]+)[\'"]', content, re.IGNORECASE)
                        for file_path in file_matches:
                            context_info['attached_files'].append({
                                'path': file_path,
                                'type': 'attached'
                            })
                        
                        # Look for workspace references
                        workspace_matches = re.findall(r'workspace|current file|editor context', content, re.IGNORECASE)
                        context_info['workspace_context'].extend(workspace_matches)
                        
        except json.JSONDecodeError:
            # If not valid JSON, do text-based extraction
            file_matches = re.findall(r'filepath[\'"]?\s*:\s*[\'"]([^\'\"]+)[\'"]', messages_json, re.IGNORECASE)
            for file_path in file_matches:
                context_info['attached_files'].append({
                    'path': file_path,
                    'type': 'attached'
                })
        
        return context_info
    
    def _finalize_pending_turns(self):
        """Convert pending turn data to ConversationTurn objects and add to sessions"""
        for request_id, turn_data in self.pending_turns.items():
            session_id = turn_data.get('session_id')
            if session_id and session_id in self.sessions:
                turn = ConversationTurn(
                    turn_number=turn_data.get('turn_number', 0),
                    timestamp=turn_data.get('timestamp', ''),
                    request_id=request_id,
                    conversation_id=session_id,
                    user_query=turn_data.get('user_query', ''),
                    intent=turn_data.get('intent', ''),
                    context_types=turn_data.get('context_types', ''),
                    model_response=turn_data.get('model_response', ''),
                    base_model=turn_data.get('base_model', ''),
                    attached_files=turn_data.get('attached_files', []),
                    workspace_context=turn_data.get('workspace_context', []),
                    available_tools=turn_data.get('available_tools', []),
                    tools_used=turn_data.get('tools_used', {}),
                    full_prompt=turn_data.get('full_prompt', ''),
                    prompt_token_length=turn_data.get('prompt_token_length', 0),
                    chat_location=turn_data.get('chat_location', ''),
                    language_id=turn_data.get('language_id', ''),
                )
                
                self.sessions[session_id].turns.append(turn)
                self.sessions[session_id].total_turns += 1
        
        # Sort turns by turn number within each session
        for session in self.sessions.values():
            session.turns.sort(key=lambda t: (t.turn_number, t.timestamp))
            if session.turns:
                session.end_time = session.turns[-1].timestamp


def export_conversations_to_jsonl(sessions: Dict[str, ConversationSession], output_path: str):
    """Export processed conversations to JSONL format for analysis"""
    
    with open(output_path, 'w') as f:
        for session_id, session in sessions.items():
            for turn in session.turns:
                # Convert to dictionary format similar to your existing analysis format
                conversation_record = {
                    'session_id': session_id,
                    'turn_number': turn.turn_number,
                    'request_id': turn.request_id,
                    'timestamp': turn.timestamp,
                    'user_query': turn.user_query,
                    'model_response': turn.model_response,
                    'intent': turn.intent,
                    'base_model': turn.base_model,
                    'context_info': {
                        'attached_files': turn.attached_files,
                        'workspace_context': turn.workspace_context,
                        'context_types': turn.context_types,
                        'available_tools': turn.available_tools,
                        'tools_used': turn.tools_used,
                    },
                    'prompt_info': {
                        'full_prompt': turn.full_prompt,
                        'token_length': turn.prompt_token_length,
                    },
                    'metadata': {
                        'chat_location': turn.chat_location,
                        'language_id': turn.language_id,
                    }
                }
                
                f.write(json.dumps(conversation_record) + '\n')


def main():
    """Main processing function"""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(
        description="Process VS Code Copilot telemetry data to extract comprehensive conversation information"
    )
    parser.add_argument(
        "--input_file",
        help="Path to the input telemetry JSONL file"
    )
    parser.add_argument(
        "--output_file",
        help="Path to the output processed conversations JSONL file"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output with sample statistics"
    )
    
    args = parser.parse_args()
    
    # Validate input file exists
    input_path = Path(args.input_file)
    if not input_path.exists():
        print(f"Error: Input file '{args.input_file}' does not exist")
        return 1
    
    # Create output directory if it doesn't exist
    output_path = Path(args.output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    processor = TelemetryProcessor()
    
    # Process the telemetry file
    print(f"Processing telemetry file: {args.input_file}")
    sessions = processor.process_telemetry_file(args.input_file)
    
    print(f"Processed {len(sessions)} conversation sessions")
    
    total_turns = sum(session.total_turns for session in sessions.values())
    print(f"Total conversation turns: {total_turns}")
    
    # Export to structured format
    export_conversations_to_jsonl(sessions, args.output_file)
    print(f"Exported conversations to: {args.output_file}")
    
    # Print sample statistics if verbose mode is enabled
    if args.verbose:
        print("\n=== Sample Statistics ===")
        for i, (session_id, session) in enumerate(sessions.items()):
            if i >= 3:  # Show first 3 sessions as examples
                break
            print(f"\n=== Session {session_id[:8]}... ===")
            print(f"Turns: {session.total_turns}")
            print(f"Duration: {session.start_time} -> {session.end_time}")
            
            for j, turn in enumerate(session.turns[:2]):  # Show first 2 turns
                print(f"  Turn {turn.turn_number}:")
                print(f"    Query: {turn.user_query[:100]}...")
                print(f"    Response: {turn.model_response[:100]}...")
                print(f"    Model: {turn.base_model}")
                print(f"    Files: {len(turn.attached_files)}")
                print(f"    Tools: {list(turn.tools_used.keys())}")
    
    return 0


if __name__ == "__main__":
    main()
