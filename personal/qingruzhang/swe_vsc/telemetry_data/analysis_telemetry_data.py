#!/usr/bin/env python3
"""
Conversation Analysis Script

This script loads conversation data, processes them in batches using the INSTRUCTION_PROMPT 
template, and evaluates them using bus_message_completer similar to swe_workflow_grader.
"""

import argparse
import asyncio
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import chat
from bus_token_completer import BusTokenCompleter
from chat import Conversation, Message, Role
from chat.render.renderer_registry import get_renderer
# from deep_swe_sft_msft.data_generation.constants import BUS_TOPIC, BUS_USER, TEACHER_RENDERER
from message_completer.message_completer import MessageCompleter, ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter
from mini.finetune.datasets.jsonl import read_jsonl, write_jsonl

from prompts import INSTRUCTION_PROMPT

BUS_USER = "gpt5-data-bus"
BUS_TOPIC = "az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"
BUS_RENDERER = "harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs_cross_msg_msc_tools_v2"

def reconstruct_sample(raw_sample: Dict[str, Any]) -> Dict[str, Any]:
    """
    Reconstruct a sample from the raw input format to the expected analysis format.
    
    Args:
        raw_sample: Raw sample from input file with format:
            {
                'thread_id': str,
                'chat_records': [{'id': str, 'prompt': str, 'message': str, ...}],
                'conversation': [{'user': str, 'assistant': str}],
                ... other fields
            }
            
    Returns:
        Reconstructed sample with format:
            {
                'sample_id': str (thread_id),
                'conversation': [{'id': str, 'user': str, 'assistant': str}],
                'metadata': {'raw_data': original_sample}
            }
    """
    thread_id = raw_sample.get('thread_id', 'unknown')
    chat_records = raw_sample.get('chat_records', [])

    # Reconstruct conversation from chat_records
    conversation = []
    for record in chat_records:
        message_id = record.get('id', '')
        user_query = record.get('prompt', '')
        assistant_answer = record.get('message', '')

        conversation.append({
            'id': message_id,
            'user': user_query,
            'assistant': assistant_answer
        })

    # Create reconstructed sample
    new_sample = {
        'sample_id': thread_id,
        'conversation': conversation,
        'metadata': {
            'raw_data': raw_sample
        }
    }

    return new_sample


class ConversationAnalyzer:
    """
    Analyzer that evaluates conversation samples using the INSTRUCTION_PROMPT template.
    
    Follows the pattern from SWEWorkFlowGrader for consistency.
    """

    def __init__(
        self,
        renderer_name: str = BUS_RENDERER,
        retries: int = 3,
        reward_multiplier: int = 32,
        temperature: float = 0.1
    ):
        self.renderer_name = renderer_name
        self.renderer = get_renderer(renderer_name)
        self.retries = retries
        self.reward_multiplier = reward_multiplier

        # Configure bus token completer (same pattern as SWEWorkFlowGrader)
        bus_tc_config = BusTokenCompleter.Config(
            topic_mode_or_user=BUS_USER,
            topic_or_snapshot=BUS_TOPIC,
        )

        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=bus_tc_config,
            completion_params={"temperature": temperature},
            renderer=self.renderer,
        )

        self.message_completer = message_completer_config.build()

    def format_conversations_for_analysis(self, samples: List[Dict[str, Any]]) -> str:
        """
        Format conversation samples for the INSTRUCTION_PROMPT template.
        
        Args:
            samples: List of reconstructed samples, each containing a "conversation" field and "sample_id"
            
        Returns:
            Formatted string with sample_id, user, and assistant messages
        """
        formatted_samples = []

        for sample in samples:
            conversation = sample.get("conversation", [])
            sample_id = sample.get("sample_id", "unknown")
            sample_lines = [f"sample_id: {sample_id}"]

            for msg in conversation:
                user_content = msg.get("user", "")
                assistant_content = msg.get("assistant", "")

                if user_content:
                    sample_lines.append(f"user: {user_content}")
                if assistant_content:
                    sample_lines.append(f"assistant: {assistant_content}")

            formatted_samples.append("\n".join(sample_lines))

        # Join samples with separator
        return "\n###\n".join(formatted_samples)

    async def analyze_conversations(self, samples: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze a batch of conversation samples using the INSTRUCTION_PROMPT.
        
        Args:
            samples: List of conversation samples to analyze
            
        Returns:
            Analysis results as a dictionary
        """
        # Format conversations for the prompt
        formatted_conversations = self.format_conversations_for_analysis(samples)

        # Create the analysis prompt by replacing placeholder in INSTRUCTION_PROMPT
        # Note: The INSTRUCTION_PROMPT expects the samples to be provided as input
        analysis_prompt = f"{INSTRUCTION_PROMPT}\n\n## Conversation Samples to Analyze\n{formatted_conversations}"

        print(f"Analyzing {len(samples)} conversation samples...")
        print(f"Prompt length: {len(analysis_prompt)} characters")

        # Create conversation for the analyzer
        analyzer_convo = Conversation(
            messages=[
                Message.system(
                    "You are an expert SWE Agent Judge. Analyze the provided conversation samples "
                    "according to the detailed instructions and return a complete JSON evaluation.",
                    metadata=chat.SystemContentMetadata(
                        reward_multiplier=self.reward_multiplier),
                ),
                Message.user(analysis_prompt),
            ]
        )

        # Get analysis with retries
        for attempt in range(self.retries):
            try:
                completion = await self.message_completer.async_completion(
                    conversations=[analyzer_convo],
                    n=1,
                    seed=0,
                    end_header=False,
                )
                choice = completion.choices[0]
                messages = choice.get_messages(
                    parse_error_mode=ParseErrorMode.SYSTEM_ERROR)

                # Parse the analyzer's response
                response_text = str(messages[-1].content)
                print(
                    f"Analyzer response length: {len(response_text)} characters")
                print(f"Response preview: {response_text[:500]}...")

                # Try to parse as JSON
                try:
                    analysis_result = self._parse_evaluation_response(
                        response_text)
                    return {
                        "success": True,
                        "analysis": analysis_result,
                        "raw_response": response_text,
                        "samples_analyzed": len(samples),
                        "attempt": attempt + 1
                    }
                except json.JSONDecodeError as json_error:
                    print(
                        f"JSON parsing failed on attempt {attempt + 1}: {json_error}")
                    if attempt < self.retries - 1:
                        continue
                    else:
                        return {
                            "success": False,
                            "error": f"JSON parsing failed: {json_error}",
                            "raw_response": response_text,
                            "samples_analyzed": len(samples),
                            "attempt": attempt + 1
                        }

            except Exception as e:
                if attempt < self.retries - 1:
                    print(f"Analysis attempt {attempt + 1} failed: {e}")
                    await asyncio.sleep(1)
                    continue
                else:
                    return {
                        "success": False,
                        "error": f"Analysis failed after {self.retries} attempts: {e}",
                        "samples_analyzed": len(samples),
                        "attempt": attempt + 1
                    }

        # This should never be reached, but just in case
        return {
            "success": False,
            "error": "Unexpected end of retry loop",
            "samples_analyzed": len(samples)
        }

    def _parse_evaluation_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse the analyzer's evaluation response to extract JSON results.
        Similar to _parse_evaluation_response in swe_workflow_grader.
        
        Args:
            response_text: The full response text from the analyzer
            
        Returns:
            Parsed evaluation results as a dictionary
        """
        import re

        # Try to find JSON content in the response
        # Look for the last occurrence of a JSON object (which should be the evaluation)
        json_matches = list(re.finditer(r'\{.*?\}', response_text, re.DOTALL))

        if not json_matches:
            raise json.JSONDecodeError(
                "No JSON found in response", response_text, 0)

        # Try parsing from the last (and hopefully largest) JSON match
        for match in reversed(json_matches):
            json_text = match.group()
            try:
                parsed_result = json.loads(json_text)
                # Validate that this looks like our expected evaluation format
                if "samples" in parsed_result:
                    return parsed_result
            except json.JSONDecodeError:
                continue

        # If no valid JSON with "samples" found, try to extract JSON from end of response
        # Look for JSON starting with { and ending with } at the end
        match = re.search(r'\{[^{}]*"samples"[^{}]*\}$',
                          response_text, re.DOTALL)
        if match:
            try:
                return json.loads(match.group())
            except json.JSONDecodeError:
                pass

        # Final attempt: try to parse the entire response as JSON
        try:
            return json.loads(response_text)
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(
                f"Could not parse evaluation JSON: {e}", response_text, e.pos)


async def process_with_semaphore(
    semaphore: asyncio.Semaphore,
    batch_info: Dict[str, Any],
    analyzer: ConversationAnalyzer,
    output_dir: str
) -> Dict[str, Any]:
    """
    Process a single batch with semaphore for concurrency control.
    
    Args:
        semaphore: Semaphore for concurrency control
        batch_info: Dictionary containing batch_idx, batch_samples, start_idx, end_idx
        analyzer: ConversationAnalyzer instance
        output_dir: Directory to save results
        
    Returns:
        Batch result dictionary
    """
    async with semaphore:
        batch_idx = batch_info["batch_idx"]
        raw_batch_samples = batch_info["batch_samples"]
        start_idx = batch_info["start_idx"]
        end_idx = batch_info["end_idx"]
        total_batches = batch_info["total_batches"]

        # print(f"\n{'='*80}")
        # print(f"Processing batch {batch_idx + 1}/{total_batches}")
        print(f"Processing {start_idx} to {end_idx - 1} ({len(raw_batch_samples)} samples)")
        # print(f"{'='*80}")

        try:
            # Reconstruct samples to the expected format
            new_samples = []
            for raw_sample in raw_batch_samples:
                new_sample = reconstruct_sample(raw_sample)
                new_samples.append(new_sample)

            print(f"Reconstructed {len(new_samples)} samples for analysis")

            # Analyze the batch
            batch_result = await analyzer.analyze_conversations(new_samples)

            # Map evaluation results back to samples
            evaluated_samples = []
            clusters = []
            taxonomy_updates = []

            if batch_result.get("success") and "analysis" in batch_result:
                analysis = batch_result["analysis"]

                # Extract evaluation results for each sample
                sample_evaluations = analysis.get("samples", [])

                # Create a mapping of sample_id to evaluation
                eval_map = {}
                for eval_result in sample_evaluations:
                    sample_id = eval_result.get("sample_id")
                    if sample_id:
                        eval_map[sample_id] = eval_result

                # Add evaluation results to reconstructed samples
                for new_sample in new_samples:
                    sample_id = new_sample.get("sample_id")
                    sample_copy = new_sample.copy()

                    if sample_id in eval_map:
                        sample_copy["eval_result"] = eval_map[sample_id]
                    else:
                        # If no evaluation found, add a placeholder
                        sample_copy["eval_result"] = {
                            "error": "No evaluation found for this sample_id",
                            "sample_id": sample_id
                        }

                    evaluated_samples.append(sample_copy)

                # Extract clusters and taxonomy updates
                clusters = analysis.get("clusters", [])
                taxonomy_updates = analysis.get("taxonomy_updates", [])

            batch_result.update({
                "batch_idx": batch_idx,
                "start_idx": start_idx,
                "end_idx": end_idx,
                "timestamp": datetime.now().isoformat(),
                "new_samples_count": len(new_samples),
                "evaluated_samples": evaluated_samples,
                "clusters": clusters,
                "taxonomy_updates": taxonomy_updates
            })

            print(
                f"Batch {batch_idx + 1} completed: {'SUCCESS' if batch_result['success'] else 'FAILED'}")
            return batch_result

        except Exception as e:
            error_result = {
                "success": False,
                "error": f"Batch processing error: {e}",
                "batch_idx": batch_idx,
                "start_idx": start_idx,
                "end_idx": end_idx,
                "timestamp": datetime.now().isoformat()
            }
            print(f"Batch {batch_idx + 1} failed with error: {e}")
            return error_result


async def process_all_samples(
    data: List[Dict[str, Any]],
    num_shots: int,
    output_dir: str,
    analyzer: ConversationAnalyzer,
    max_batches: Optional[int] = None,
    concurrency_limit: int = 5
) -> List[Dict[str, Any]]:
    """
    Process conversation data in batches using asyncio.as_completed with semaphore.
    
    Args:
        data: List of conversation samples
        num_shots: Number of samples per batch
        output_dir: Directory to save results
        analyzer: ConversationAnalyzer instance
        max_batches: Maximum number of batches to process (for testing)
        concurrency_limit: Maximum number of concurrent batch processing
        
    Returns:
        List of batch results
    """
    results = []
    total_batches = (len(data) + num_shots - 1) // num_shots

    if max_batches:
        total_batches = min(total_batches, max_batches)
        print(f"Limiting processing to {max_batches} batches for testing")

    print(
        f"Processing {len(data)} samples in {total_batches} batches of {num_shots}")
    print(f"Concurrency limit: {concurrency_limit}")

    # Create output directory
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # Progress tracking
    progress_file = os.path.join(output_dir, "progress.txt")
    results_file = os.path.join(output_dir, "analysis_results.jsonl")

    # Create semaphore for concurrency control
    semaphore = asyncio.Semaphore(concurrency_limit)

    # Prepare batch information
    batch_infos = []
    for batch_idx in range(total_batches):
        start_idx = batch_idx * num_shots
        end_idx = min(start_idx + num_shots, len(data))
        batch_samples = data[start_idx:end_idx]

        batch_info = {
            "batch_idx": batch_idx,
            "batch_samples": batch_samples,
            "start_idx": start_idx,
            "end_idx": end_idx,
            "total_batches": total_batches
        }
        batch_infos.append(batch_info)

    # Create tasks for concurrent processing
    tasks = [
        process_with_semaphore(semaphore, batch_info, analyzer, output_dir)
        for batch_info in batch_infos
    ]

    completed_count = 0

    # Process tasks as they complete
    for task in asyncio.as_completed(tasks):
        try:
            result = await task
            results.append(result)
            completed_count += 1

            # Save result incrementally
            with open(results_file, "a") as f:
                f.write(json.dumps(result, default=str) + "\n")

            # Update progress
            with open(progress_file, "w") as f:
                f.write(
                    f"Completed {completed_count}/{total_batches} batches\n")
                f.write(f"Last update: {datetime.now().isoformat()}\n")
                if result["success"]:
                    f.write(f"Batch {result['batch_idx'] + 1}: SUCCESS\n")
                else:
                    f.write(
                        f"Batch {result['batch_idx'] + 1}: FAILED - {result.get('error', 'Unknown error')}\n")

            print(
                f"Overall progress: {completed_count}/{total_batches} batches completed")

        except Exception as e:
            print(f"Task completion error: {e}")
            # Still increment count to avoid hanging
            completed_count += 1

    # Aggregate results and save evaluated samples
    all_evaluated_samples = []
    aggregated_clusters = []
    aggregated_taxonomy_updates = []

    for result in results:
        if result.get("success") and "evaluated_samples" in result:
            all_evaluated_samples.extend(result["evaluated_samples"])
        if result.get("success") and "clusters" in result:
            aggregated_clusters.extend(result["clusters"])
        if result.get("success") and "taxonomy_updates" in result:
            aggregated_taxonomy_updates.extend(result["taxonomy_updates"])

    # Save all evaluated samples
    evaluated_samples_file = os.path.join(
        output_dir, "evaluated_samples.jsonl")
    with open(evaluated_samples_file, "w") as f:
        for sample in all_evaluated_samples:
            f.write(json.dumps(sample, default=str) + "\n")

    # Save aggregated clusters and taxonomy updates
    aggregated_results = {
        "clusters": aggregated_clusters,
        "taxonomy_updates": aggregated_taxonomy_updates,
        "total_samples": len(all_evaluated_samples),
        "timestamp": datetime.now().isoformat()
    }

    aggregated_file = os.path.join(output_dir, "aggregated_results.json")
    with open(aggregated_file, "w") as f:
        json.dump(aggregated_results, f, indent=2, default=str)

    print(
        f"Saved {len(all_evaluated_samples)} evaluated samples to: {evaluated_samples_file}")
    print(f"Saved aggregated results to: {aggregated_file}")
    print(f"Total clusters found: {len(aggregated_clusters)}")
    print(f"Total taxonomy updates: {len(aggregated_taxonomy_updates)}")

    return results


async def main():
    parser = argparse.ArgumentParser(
        description="Analyze conversation samples using INSTRUCTION_PROMPT")
    parser.add_argument(
        "--input_file",
        type=str,
        required=True,
        help="Input JSONL file containing conversation samples"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="Output directory for analysis results"
    )
    parser.add_argument(
        "--num_shots",
        type=int,
        default=5,
        help="Number of samples to process in each batch (default: 5)"
    )
    parser.add_argument(
        "--max_batches",
        type=int,
        default=None,
        help="Maximum number of batches to process (for testing)"
    )
    parser.add_argument(
        "--retries",
        type=int,
        default=3,
        help="Number of retries for failed analysis attempts"
    )
    parser.add_argument(
        "--temperature",
        type=float,
        default=0.1,
        help="Temperature for the analysis model"
    )
    parser.add_argument(
        "--concurrency",
        "-c",
        type=int,
        default=5,
        help="Concurrency limit for parallel batch processing"
    )

    args = parser.parse_args()

    print(f"Starting conversation analysis with:")
    print(f"  Input file: {args.input_file}")
    print(f"  Output directory: {args.output_dir}")
    print(f"  Batch size (num_shots): {args.num_shots}")
    print(f"  Max batches: {args.max_batches or 'unlimited'}")
    print(f"  Concurrency limit: {args.concurrency}")
    print(f"  Retries: {args.retries}")
    print(f"  Temperature: {args.temperature}")

    # Load data
    try:
        data = read_jsonl(args.input_file)
        print(f"Loaded {len(data)} samples from {args.input_file}")
    except Exception as e:
        print(f"Error loading data from {args.input_file}: {e}")
        sys.exit(1)

    # Create output directory with timestamp
    output_dir = Path(args.output_dir) / \
        datetime.now().strftime("%Y%m%d-%H%M%S")

    # Initialize analyzer
    analyzer = ConversationAnalyzer(
        retries=args.retries,
        temperature=args.temperature
    )

    # Process batches
    try:
        results = await process_all_samples(
            data=data,
            num_shots=args.num_shots,
            output_dir=str(output_dir),
            analyzer=analyzer,
            max_batches=args.max_batches,
            concurrency_limit=args.concurrency
        )

        # Save final summary
        summary = {
            "total_samples": len(data),
            "total_batches": len(results),
            "successful_batches": sum(1 for r in results if r.get("success", False)),
            "failed_batches": sum(1 for r in results if not r.get("success", False)),
            "timestamp": datetime.now().isoformat(),
            "args": vars(args)
        }

        summary_file = output_dir / "summary.json"
        with open(summary_file, "w") as f:
            json.dump(summary, f, indent=2, default=str)

        print(f"\n{'='*80}")
        print("ANALYSIS COMPLETED")
        print(f"{'='*80}")
        print(f"Total samples processed: {len(data)}")
        print(f"Total batches: {len(results)}")
        print(f"Successful batches: {summary['successful_batches']}")
        print(f"Failed batches: {summary['failed_batches']}")
        print(f"Results saved to: {output_dir}")
        print(f"Individual results: {output_dir}/analysis_results.jsonl")
        print(f"Summary: {summary_file}")
        print(f"Progress log: {output_dir}/progress.txt")

    except Exception as e:
        print(f"Error during processing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
