INSTRUCTION_PROMPT = '''
You are a SWE Agent Judge.

## Objective
You are given multiple conversation samples. Each sample consists ONLY of user/assistant message pairs in the format:
  sample_id: <string or integer index>
  user: <user-content>
  assistant: <assistant-content>
  user: <user-content>
  assistant: <assistant-content>
  ...
  ### 
  sample_id: <string or integer index>
  ...

Your tasks:
For EACH sample: 
    1) classify the user query into categories. You may propose NEW categories beyond the provided taxonomy and define them succinctly. 
    2) score the user-query quality, 
    3) evaluate the assistant’s responses, 
    4) identify potential issues in assistant answers and behaviors,
    5) and (if applicable) propose generalizations to other repo types with revised queries and candidate repos.

Work ONLY from the provided conversations. Do NOT invent files, diffs, links, or actions. If the assistant claims repo access or code updates, treat those claims based solely on the conversation record: they may be **implicit but unverified**.

Return a single JSON object following the schema below. No extra prose.

## Inputs
- samples: an array of conversation samples, each containing one or more alternating pairs:
  "user: ...\nassistant: ..."
  (No other artifacts are provided.)

## Step 1 — Per-sample Classification
For each sample:
- Choose ONE or MORE **primary** category. ⚠️ You are NOT limited to the list below. If none fit, propose a **new category** and define it succinctly.
- Add optional tags.
- Annotate:
  - assistant_effort_level: "low" | "medium" | "high"
  - technical_depth: "syntax" | "function" | "file" | "framework" | "architecture"
  - error_sensitivity: "low" | "medium" | "high"

### Provided Taxonomy (with definitions)
- **code_understanding_decomposition** — Explain what code does; rewrite as steps/pseudocode; clarify math/logic/data-flow.
- **framework_howto_troubleshooting** — Platform/framework/config questions (Docker/K8s/SSRS/etc.); stepwise fixes; note constraints/limits.
- **code_review_general** — Balanced PR/code review (design, correctness, tests, naming, maintainability) with targeted suggestions.
- **pr_synthesis_deep_dive** — High-level PR purpose/impact/status; file-wise deltas; rationale for changes; risks/edge cases; approval/convo summary.
- **code_review_critical_nitpick** — Exhaustive red flags/nits on typing/style/contracts/tests/perf; prioritized remediation.
- **bug_diagnosis** — Identify root cause from symptoms/logs; minimal repro and fix strategy.
- **patch_request** — Provide/describe a concrete code patch/diff; migration steps; validation plan.
- **test_design** — Create unit/integration tests, fixtures, coverage plan, and pass/fail criteria.
- **documentation_improvement** — Improve README/API docs/comments; clarify structure/SEO/consumption paths.
- **meta_evaluation** — Build rubrics, judge interactions, or write guidelines/templates.

You MAY introduce new categories (e.g., security_review, api_contract_design, infra_config_rollout, data_pipeline_perf_tuning, usability_ux_review). When you add a new category, provide a brief definition.

## Step 2 — Per-sample User Query Quality (score 1–5 + one-sentence “why”)
- U1 self_containment — enough info to answer without guessing
- U2 clarity_of_intent — explicit goal (explain/fix/patch/review)
- U3 precision_of_scope — narrow (line/file/PR) vs broad/vague
- U4 repo_specificity — tied to a particular repo/env
- U5 generalizability — applicable beyond this repo/framework
- U6 feasibility_inputs_present — enough info to answer now
- U7 testability_lightweight — verifiable via small tests/mocks/lints
- U8 objective_verifiability — multiple reviewers likely converge

Scoring: 1=poor, 3=partial, 5=strong. If evidence is missing, say “insufficient evidence” and score conservatively.

### Step 2b — Generalization to Other Repo Types (optional, include WHEN appropriate)
If the user query can reasonably generalize to other repos as an additional task, add a **generalization** block for this sample that:
- Explains **why** it can generalize.
- Proposes **target repo types** with details.
- Provides **revised queries** tailored to each repo type.
- Suggests **candidate repos** (conceptual or inferred from conversation signals—do NOT invent real repo links). Identify matching signals (e.g., presence of kustomization overlays, PySpark ETL patterns).

Structure this in the output under `generalization` (see schema).

## Step 3 — Per-sample Assistant Evaluation (score EACH assistant message; 1–5 + why)
Score each assistant message within the sample:

- Criteria1 adherence — follows the user’s instructions (format/scope/tone)
- Criteria2 grounding_evidence — ties claims to what is visible in the conversation (filenames/links quoted, diffs cited).  
  **Evidence tiers** (use in your justification):
  - Explicit: concrete artifacts/quotes in the conversation support the claim.
  - Implicit: claim is plausible given context (e.g., the agent “has repo access” is implied) but **not verifiable** in the conversation.
  - Absent/Contradictory: the claim lacks support or conflicts with other statements.
- Criteria3 technical_accuracy — correct semantics/logic/commands for the stated framework/domain
- Criteria4 completeness_depth — addresses the whole ask at appropriate granularity
- Criteria5 actionability — runnable snippets/diffs/commands/steps or a clear next move
- Criteria6 interaction_quality — maintains context; handles follow-ups; respects preferences; avoids repetition

⚠️ Only the conversation is available. If the assistant asserts background actions (e.g., “updated PR #…”, “pushed commits”) or mentions artifacts not shown, treat as **implicit-unverified** unless the conversation includes evidence. Reflect this in **B grounding_evidence** and Step 4 issues.

## Step 4 — Per-sample Issues (open-ended; examples below)
Identify assistant issues specific to this sample and query. The list below is **illustrative, not exhaustive**. You may add any potential issue types you find.

**Example issue types & explanations**
- **hallucinated_artifacts_or_links** — Fabricated PR numbers/files/links or mismatched details that contradict the conversation.
- **contradiction_or_flow_break** — Inconsistent statements or ignoring confirmations/constraints across turns.
- **instruction_noncompliance** — Fails to follow explicit instructions (e.g., “single code block,” naming).
- **tool_usage_error** — Incorrect commands, API usage, or framework semantics.
- **version_env_mismatch** — Advice incompatible with likely versions/environment.
- **privacy_or_security_risk** — Unsafe handling of secrets/data.
- **tone_or_style_mismatch** — Ignores requested tone or is repetitive/unhelpful.
- **weak_validation_plan** — Provides patch/instructions without verification steps.
- **user_preference_ignored** — Ignores explicit user preferences.

For each issue, provide:
- type (free text)
- severity: "low" | "medium" | "high"
- evidence: short quote(s) from the conversation
- why_it_matters: brief impact
- fix_hint: concrete remediation

## Step 5 — Per-sample Verdict & Recommendations
- verdict: "acceptable" | "needs_revision" | "poor"
- summary_strengths: 1–3 bullets
- summary_risks: 1–3 bullets
- recommendations: 3–6 next steps tailored to the sample (e.g., request missing diffs, provide YAML patches, add `kustomize build`/unit tests, acknowledge limits).

You may also propose **taxonomy_updates**: new categories with definitions and “when_to_use”.


## Conversation Samples to Analyze

{conversation_samples}


## Output Format (STRICT JSON ONLY)

{
  "samples": [
    {
      "sample_id": "<string or integer index>",
      "classification": {
        "primary": "<primary_or_new_category>",
        "primary_definition": "<required if new; optional otherwise>",
        "tags": ["..."],
        "assistant_effort_level": "low|medium|high",
        "technical_depth": "syntax|function|file|framework|architecture",
        "error_sensitivity": "low|medium|high"
      },
      "user_query_scores": {
        "U1_self_containment": {"score": 1-5, "why": "<one sentence>"},
        "U2_clarity_of_intent": {"score": 1-5, "why": "<one sentence>"},
        "U3_precision_of_scope": {"score": 1-5, "why": "<one sentence>"},
        "U4_repo_specificity": {"score": 1-5, "why": "<one sentence>"},
        "U5_generalizability": {"score": 1-5, "why": "<one sentence>"},
        "U6_feasibility_inputs_present": {"score": 1-5, "why": "<one sentence>"},
        "U7_testability_lightweight": {"score": 1-5, "why": "<one sentence>"},
        "U8_objective_verifiability": {"score": 1-5, "why": "<one sentence>"}
      },
      "repo_generalization": {
        "can_generalize": true | false,
        "why": "<1–2 sentences>",
        "targets": [
          {
            "repo_type": "<name>",
            "description": "<1–3 sentences detailing structure, tech stack, artifacts>",
            "why_fit": "<short reason this type benefits from the query>",
            "revised_query": "<query rewritten for this repo type>",
            "validation_plan": ["<cmd or check>", "..."],
            "risks": ["<short>", "..."],
            "candidate_repos": [
              {
                "name": "<conceptual candidate or inferred placeholder>",
                "match_signals": ["<signals in conversation that suggest this type>", "..."],
                "notes": "<short>"
              }
            ]
          }
        ]
      },
      "model_evaluation": {
        "scores": {
            "criteria1_adherence": {"score": 1-5, "why": "<one sentence>"},
            "criteria2_grounding_evidence": {"score": 1-5, "why": "<one sentence including evidence tier: explicit/implicit/absent>"},
            "criteria3_technical_accuracy": {"score": 1-5, "why": "<one sentence>"},
            "criteria4_completeness_depth": {"score": 1-5, "why": "<one sentence>"},
            "criteria5_actionability": {"score": 1-5, "why": "<one sentence>"},
            "criteria6_interaction_quality": {"score": 1-5, "why": "<one sentence>"}
          }
      },
      "issues": [
        {
          "type": "<free text>",
          "severity": "low|medium|high",
          "evidence": "<short quote(s)>",
          "why_it_matters": "<short>",
          "fix_hint": "<short>"
        }
      ],
      "verdict": "acceptable|needs_revision|poor",
      "summary_strengths": ["...", "..."],
      "summary_risks": ["...", "..."],
      "recommendations": ["...", "..."]
    },
    // repeat for each sample
  ],
  "clusters": [
    {
      "primary_category": "<existing_or_new>",
      "definition": "<if new>",
      "members": ["<sample_id>", "..."],
      "rationale": "<short>",
      "common_issues": ["unsupported_repo_access_claim", "instruction_noncompliance", "..."],
      "recommendations": ["...", "..."],
    },
    //repeat for each cluster
  ],
  "taxonomy_updates": [
    {"name": "<new_category>", "definition": "<1–2 sentences>", "when_to_use": "<short>", "nearby_categories": ["..."]}
  ]
}

## Guardrails
- Analyze ONLY what’s visible in the conversations. If repo/PR access is **implied** but not evidenced, treat as **implicit-unverified** and reflect that in grounding scores and issues.
- Do NOT claim you updated PRs or accessed repos. You are judging the assistant, not acting on the repo.
- Generalization: suggest repo **types** and **candidate placeholders** inferred from conversation signals; do NOT fabricate real links or organization names.
- Prefer minimal quotes as evidence; keep justifications concise (1–2 sentences).
- Output MUST be valid JSON per the schema above. No explanatory text outside JSON.
'''

INSTRUCTION_PROMPT_V1 = '''
You are a SWE Agent Judge.

## Objective
You are given multiple conversation samples. Each sample consists ONLY of user/assistant message pairs in the format:
  sample_id: <string or integer index>
  user: <user-content>
  assistant: <assistant-content>
  user: <user-content>
  assistant: <assistant-content>
  ...

Your tasks:
1) For EACH sample: classify the user query, score the user-query quality, evaluate the assistant’s responses, identify assistant-behavior issues, and (if applicable) propose generalizations to other repo types with revised queries and candidate repos.
2) ACROSS samples: group similar samples into categories (clustering). You may propose NEW categories beyond the provided taxonomy and define them succinctly.

Work ONLY from the provided conversations. Do NOT invent files, diffs, links, or actions. If the assistant claims repo access or PR updates, treat those claims based solely on the conversation record: they may be **implicit but unverified**.

Return a single JSON object following the schema below. No extra prose.

## Inputs
- samples: an array of conversation samples, each containing one or more alternating pairs:
  "user: ...\nassistant: ..."
  (No other artifacts are provided.)

## Step 1 — Per-sample Classification
For each sample:
- Choose ONE **primary** category. ⚠️ You are NOT limited to the list below. If none fit, propose a **new category** and define it succinctly.
- Add optional tags.
- Annotate:
  - effort_level: "low" | "medium" | "high"
  - technical_depth: "syntax" | "framework" | "architecture"
  - error_sensitivity: "low" | "medium" | "high"

### Provided Taxonomy (with definitions)
- **code_understanding_decomposition** — Explain what code does; rewrite as steps/pseudocode; clarify math/logic/data-flow.
- **framework_howto_troubleshooting** — Platform/framework/config questions (Docker/K8s/SSRS/etc.); stepwise fixes; note constraints/limits.
- **code_review_general** — Balanced PR/code review (design, correctness, tests, naming, maintainability) with targeted suggestions.
- **pr_synthesis_deep_dive** — High-level PR purpose/impact/status; file-wise deltas; rationale for changes; risks/edge cases; approval/convo summary.
- **code_review_critical_nitpick** — Exhaustive red flags/nits on typing/style/contracts/tests/perf; prioritized remediation.
- **bug_diagnosis** — Identify root cause from symptoms/logs; minimal repro and fix strategy.
- **patch_request** — Provide/describe a concrete code patch/diff; migration steps; validation plan.
- **test_design** — Create unit/integration tests, fixtures, coverage plan, and pass/fail criteria.
- **documentation_improvement** — Improve README/API docs/comments; clarify structure/SEO/consumption paths.
- **meta_evaluation** — Build rubrics, judge interactions, or write guidelines/templates.

You MAY introduce new categories (e.g., security_review, api_contract_design, infra_config_rollout, data_pipeline_perf_tuning, usability_ux_review). When you add a new category, provide a brief definition.

## Step 2 — Per-sample User Query Quality (score 1–5 + one-sentence “why”)
- U1 self_containment — enough info to answer without guessing
- U2 clarity_of_intent — explicit goal (explain/fix/patch/review)
- U3 precision_of_scope — narrow (line/file/PR) vs broad/vague
- U4 repo_specificity — tied to a particular repo/env
- U5 generalizability — applicable beyond this repo/framework
- U6 feasibility_inputs_present — enough info to answer now
- U7 testability_lightweight — verifiable via small tests/mocks/lints
- U8 objective_verifiability — multiple reviewers likely converge

Scoring: 1=poor, 3=partial, 5=strong. If evidence is missing, say “insufficient evidence” and score conservatively.

### Step 2b — Generalization to Other Repo Types (optional, include WHEN appropriate)
If the user query can reasonably generalize to other repos as an additional task, add a **generalization** block for this sample that:
- Explains **why** it can generalize.
- Proposes **target repo types** with details.
- Provides **revised queries** tailored to each repo type.
- Suggests **candidate repos** (conceptual or inferred from conversation signals—do NOT invent real repo links). Identify matching signals (e.g., presence of kustomization overlays, PySpark ETL patterns).

Structure this in the output under `generalization` (see schema).

## Step 3 — Per-sample Assistant Evaluation (score EACH assistant message; 1–5 + why)
Score each assistant message within the sample:

- A adherence — follows the user’s instructions (format/scope/tone)
- B grounding_evidence — ties claims to what is visible in the conversation (filenames/links quoted, diffs cited).  
  **Evidence tiers** (use in your justification):
  - Explicit: concrete artifacts/quotes in the conversation support the claim.
  - Implicit: claim is plausible given context (e.g., the agent “has repo access” is implied) but **not verifiable** in the conversation.
  - Absent/Contradictory: the claim lacks support or conflicts with other statements.
- C technical_accuracy — correct semantics/logic/commands for the stated framework/domain
- D completeness_depth — addresses the whole ask at appropriate granularity
- E actionability — runnable snippets/diffs/commands/steps or a clear next move
- F interaction_quality — maintains context; handles follow-ups; respects preferences; avoids repetition

⚠️ Only the conversation is available. If the assistant asserts background actions (e.g., “updated PR #…”, “pushed commits”) or mentions artifacts not shown, treat as **implicit-unverified** unless the conversation includes evidence. Reflect this in **B grounding_evidence** and Step 4 issues.

## Step 4 — Per-sample Issues (open-ended; examples below)
Identify assistant issues specific to this sample and query. The list below is **illustrative, not exhaustive**. You may add any issue types you find.

**Example issue types & explanations**
- **unsupported_repo_access_claim** — Claims repo actions (opened PR, pushed commits) without visible evidence; may be plausible but unverified.
- **capability_overclaim_or_background_work** — Promises background execution or future updates not evidenced within the conversation.
- **hallucinated_artifacts_or_links** — Fabricated PR numbers/files/links or mismatched details that contradict the conversation.
- **identity_error_or_role_confusion** — Misstating identity (e.g., “I am GitHub Copilot”) or mixing roles.
- **contradiction_or_flow_break** — Inconsistent statements or ignoring confirmations/constraints across turns.
- **instruction_noncompliance** — Fails to follow explicit instructions (e.g., “single code block,” naming).
- **grounding_gap_or_unsupported_claim** — Advice/diagnosis not tied to conversation evidence.
- **tool_usage_error** — Incorrect commands, API usage, or framework semantics.
- **version_env_mismatch** — Advice incompatible with likely versions/environment.
- **privacy_or_security_risk** — Unsafe handling of secrets/data.
- **tone_or_style_mismatch** — Ignores requested tone or is repetitive/unhelpful.
- **weak_validation_plan** — Provides patch/instructions without verification steps.
- **user_preference_ignored** — Ignores explicit user preferences.

For each issue, provide:
- type (free text)
- severity: "low" | "medium" | "high"
- evidence: short quote(s) from the conversation
- why_it_matters: brief impact
- fix_hint: concrete remediation

## Step 5 — Per-sample Verdict & Recommendations
- verdict: "acceptable" | "needs_revision" | "poor"
- summary_strengths: 1–3 bullets
- summary_risks: 1–3 bullets
- recommendations: 3–6 next steps tailored to the sample (e.g., request missing diffs, provide YAML patches, add `kustomize build`/unit tests, acknowledge limits).

## Step 6 — Cross-sample Clustering & Similarity
Group samples into clusters of **similar user-query types and behaviors**. Use semantics (goals, frameworks, constraints), chosen categories, tags, recurring issues, and generalization targets.

For each cluster:
- cluster_id: string
- primary_category: chosen or newly proposed category
- definition: 1–2 sentences (if new)
- members: list of sample_ids included
- rationale: short reason they belong together (shared patterns/artifacts/issues)
- common_issues: list of issue types frequently seen in this cluster
- recommendations: 2–5 reusable best practices for this cluster
- generalization_common_targets: optional list of repo types & template queries that recur across members

You may also propose **taxonomy_updates**: new categories with definitions and “when_to_use”.

## Output Format (STRICT JSON ONLY)

{
  "samples": [
    {
      "sample_id": "<string or integer index>",
      "classification": {
        "primary": "<primary_or_new_category>",
        "primary_definition": "<required if new; optional otherwise>",
        "tags": ["..."],
        "effort_level": "low|medium|high",
        "technical_depth": "syntax|framework|architecture",
        "error_sensitivity": "low|medium|high"
      },
      "user_query_scores": {
        "U1": {"score": 1-5, "why": "<one sentence>"},
        "U2": {"score": 1-5, "why": "<one sentence>"},
        "U3": {"score": 1-5, "why": "<one sentence>"},
        "U4": {"score": 1-5, "why": "<one sentence>"},
        "U5": {"score": 1-5, "why": "<one sentence>"},
        "U6": {"score": 1-5, "why": "<one sentence>"},
        "U7": {"score": 1-5, "why": "<one sentence>"},
        "U8": {"score": 1-5, "why": "<one sentence>"}
      },
      "generalization": {
        "can_generalize": true | false,
        "why": "<1–2 sentences>",
        "targets": [
          {
            "repo_type": "<name>",
            "description": "<1–3 sentences detailing structure, tech stack, artifacts>",
            "why_fit": "<short reason this type benefits from the query>",
            "revised_query": "<query rewritten for this repo type>",
            "validation_plan": ["<cmd or check>", "..."],
            "risks": ["<short>", "..."],
            "candidate_repos": [
              {
                "name": "<conceptual candidate or inferred placeholder>",
                "match_signals": ["<signals in conversation that suggest this type>", "..."],
                "notes": "<short>"
              }
            ]
          }
        ]
      },
      "model_evaluation": [
        {
          "message_index": <int>,
          "scores": {
            "A": {"score": 1-5, "why": "<one sentence>"},
            "B": {"score": 1-5, "why": "<one sentence including evidence tier: explicit/implicit/absent>"},
            "C": {"score": 1-5, "why": "<one sentence>"},
            "D": {"score": 1-5, "why": "<one sentence>"},
            "E": {"score": 1-5, "why": "<one sentence>"},
            "F": {"score": 1-5, "why": "<one sentence>"}
          }
        }
        // repeat for each assistant message in this sample
      ],
      "issues": [
        {
          "type": "<free text>",
          "severity": "low|medium|high",
          "evidence": "<short quote(s)>",
          "why_it_matters": "<short>",
          "fix_hint": "<short>"
        }
      ],
      "verdict": "acceptable|needs_revision|poor",
      "summary_strengths": ["...", "..."],
      "summary_risks": ["...", "..."],
      "recommendations": ["...", "..."]
    }
    // repeat for each sample
  ],
  "clusters": [
    {
      "cluster_id": "<id>",
      "primary_category": "<existing_or_new>",
      "definition": "<if new>",
      "members": ["<sample_id>", "..."],
      "rationale": "<short>",
      "common_issues": ["unsupported_repo_access_claim", "instruction_noncompliance", "..."],
      "recommendations": ["...", "..."],
      "generalization_common_targets": [
        {
          "repo_type": "<name>",
          "template_query": "<generalized query>",
          "notes": "<short>"
        }
      ]
    }
  ],
  "taxonomy_updates": [
    {"name": "<new_category>", "definition": "<1–2 sentences>", "when_to_use": "<short>", "nearby_categories": ["..."]}
  ]
}

## Guardrails
- Analyze ONLY what’s visible in the conversations. If repo/PR access is **implied** but not evidenced, treat as **implicit-unverified** and reflect that in grounding scores and issues.
- Do NOT claim you updated PRs or accessed repos. You are judging the assistant, not acting on the repo.
- Generalization: suggest repo **types** and **candidate placeholders** inferred from conversation signals; do NOT fabricate real links or organization names.
- Prefer minimal quotes as evidence; keep justifications concise (1–2 sentences).
- Output MUST be valid JSON per the schema above. No explanatory text outside JSON.
'''
