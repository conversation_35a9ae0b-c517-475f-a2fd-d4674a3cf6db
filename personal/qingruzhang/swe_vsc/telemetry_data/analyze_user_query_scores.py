#!/usr/bin/env python3
"""
Conversation Analysis Score Plotting Script

This script reads the analysis results from conversation_analysis_script.py,
extracts user_query_scores or model_evaluation scores from evaluation results, 
calculates averages, and creates histogram plots of the average scores.
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Dict, List, Any
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict

def load_analysis_results(results_file: str) -> List[Dict[str, Any]]:
    """
    Load analysis results from the JSONL file created by conversation_analysis_script.py
    
    Args:
        results_file: Path to the analysis_results.jsonl file
        
    Returns:
        List of batch results
    """
    results = []
    try:
        with open(results_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    result = json.loads(line)
                    results.append(result)
        return results
    except Exception as e:
        print(f"Error loading results from {results_file}: {e}")
        return []

def extract_scores(results: List[Dict[str, Any]], score_field: str) -> List[Dict[str, Any]]:
    """
    Extract scores from all successful batch results
    
    Args:
        results: List of batch results from conversation_analysis_script.py
        score_field: Field to extract scores from ('user_query_scores' or 'model_evaluation')
        
    Returns:
        List of scores, each containing sample_id and scores
    """
    all_scores = []
    
    for batch_result in results:
        if not batch_result.get("success"):
            continue
            
        evaluated_samples = batch_result.get("evaluated_samples", [])
        
        for sample in evaluated_samples:
            eval_result = sample.get("eval_result", {})
            if "error" in eval_result:
                continue
                
            # Extract the specified score field
            if score_field == "user_query_scores":
                scores_data = eval_result.get("user_query_scores", {})
            elif score_field == "model_evaluation":
                # model_evaluation has a nested "scores" field
                model_eval = eval_result.get("model_evaluation", {})
                scores_data = model_eval.get("scores", {})
            else:
                print(f"Warning: Unknown score field '{score_field}', skipping sample")
                continue
                
            if not scores_data:
                continue
                
            sample_id = sample.get("sample_id", "unknown")
            
            # Extract numeric scores from each criterion
            scores_dict = {"sample_id": sample_id}
            for criterion, score_data in scores_data.items():
                if isinstance(score_data, dict) and "score" in score_data:
                    scores_dict[criterion] = score_data["score"]
                    
            if len(scores_dict) > 1:  # More than just sample_id
                all_scores.append(scores_dict)
    
    return all_scores

def calculate_average_scores(scores: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Calculate average scores for each sample
    
    Args:
        scores: List of scores per sample
        
    Returns:
        List of samples with their average scores and individual criterion scores
    """
    averaged_scores = []
    
    for sample_scores in scores:
        sample_id = sample_scores["sample_id"]
        
        # Get all numeric scores (exclude sample_id)
        numeric_scores = []
        criterion_scores = {}
        
        for key, value in sample_scores.items():
            if key != "sample_id" and isinstance(value, (int, float)):
                numeric_scores.append(value)
                criterion_scores[key] = value
        
        if numeric_scores:
            avg_score = np.mean(numeric_scores)
            averaged_scores.append({
                "sample_id": sample_id,
                "average_score": avg_score,
                "individual_scores": criterion_scores,
                "num_criteria": len(numeric_scores)
            })
    
    return averaged_scores

def plot_score_histograms(averaged_scores: List[Dict[str, Any]], output_dir: str, score_field: str):
    """
    Create histogram plots for scores
    
    Args:
        averaged_scores: List of samples with average scores
        output_dir: Directory to save plots
        score_field: Type of scores being plotted ('user_query_scores' or 'model_evaluation')
    """
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Extract average scores for plotting
    avg_scores = [sample["average_score"] for sample in averaged_scores]
    
    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Create figure with subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Customize title based on score field
    if score_field == "user_query_scores":
        title = 'User Query Scores Analysis'
        score_label = 'User Query Score'
    elif score_field == "model_evaluation":
        title = 'Model Evaluation Scores Analysis'
        score_label = 'Model Evaluation Score'
    else:
        title = f'{score_field.replace("_", " ").title()} Analysis'
        score_label = score_field.replace("_", " ").title()
    
    fig.suptitle(title, fontsize=16, fontweight='bold')
    
    # 1. Histogram of average scores
    axes[0, 0].hist(avg_scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title(f'Distribution of Average {score_label}s')
    axes[0, 0].set_xlabel('Average Score (1-5)')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Add statistics text
    mean_score = np.mean(avg_scores)
    std_score = np.std(avg_scores)
    axes[0, 0].axvline(mean_score, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_score:.2f}')
    axes[0, 0].legend()
    
    # 2. Box plot of average scores
    axes[0, 1].boxplot(avg_scores, patch_artist=True, 
                       boxprops=dict(facecolor='lightgreen', alpha=0.7))
    axes[0, 1].set_title(f'Box Plot of Average {score_label}s')
    axes[0, 1].set_ylabel('Average Score (1-5)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Individual criterion scores (if we have them)
    if averaged_scores:
        # Get all unique criteria
        all_criteria = set()
        for sample in averaged_scores:
            all_criteria.update(sample["individual_scores"].keys())
        
        all_criteria = sorted(all_criteria)
        
        # Create data for each criterion
        criterion_data = defaultdict(list)
        for sample in averaged_scores:
            for criterion in all_criteria:
                if criterion in sample["individual_scores"]:
                    criterion_data[criterion].append(sample["individual_scores"][criterion])
        
        # Plot individual criteria as a violin plot (if we have multiple criteria)
        if len(all_criteria) > 1:
            data_for_plot = [criterion_data[criterion] for criterion in all_criteria]
            parts = axes[1, 0].violinplot(data_for_plot, positions=range(len(all_criteria)), 
                                         showmeans=True, showmedians=True)
            axes[1, 0].set_title('Distribution by Individual Criteria')
            axes[1, 0].set_xlabel('Criteria')
            axes[1, 0].set_ylabel('Score (1-5)')
            axes[1, 0].set_xticks(range(len(all_criteria)))
            
            # Customize labels based on score field
            if score_field == "user_query_scores":
                # User query criteria: U1, U2, etc.
                short_labels = [criterion.replace('_', '\n').replace('U', '').replace('self', 'self\n') 
                               for criterion in all_criteria]
            elif score_field == "model_evaluation":
                # Model evaluation criteria: criteria1, criteria2, etc.
                short_labels = [criterion.replace('criteria', 'C').replace('_', '\n') 
                               for criterion in all_criteria]
            else:
                short_labels = [criterion.replace('_', '\n') for criterion in all_criteria]
                
            axes[1, 0].set_xticklabels(short_labels, rotation=45, ha='right', fontsize=8)
            axes[1, 0].grid(True, alpha=0.3)
        else:
            axes[1, 0].text(0.5, 0.5, 'Not enough criteria for comparison', 
                           ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('Individual Criteria Distribution')
    
    # 4. Score distribution by quality ranges
    score_ranges = {
        'Poor (1-2)': [s for s in avg_scores if s <= 2],
        'Fair (2-3)': [s for s in avg_scores if 2 < s <= 3],
        'Good (3-4)': [s for s in avg_scores if 3 < s <= 4],
        'Excellent (4-5)': [s for s in avg_scores if s > 4]
    }
    
    range_counts = [len(scores) for scores in score_ranges.values()]
    range_labels = list(score_ranges.keys())
    
    colors = ['red', 'orange', 'lightblue', 'green']
    wedges, texts, autotexts = axes[1, 1].pie(range_counts, labels=range_labels, colors=colors, 
                                             autopct='%1.1f%%', startangle=90)
    axes[1, 1].set_title('Score Quality Distribution')
    
    plt.tight_layout()
    
    # Save the plot
    plot_file = output_path / f"{score_field}_analysis.png"
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"Histogram plot saved to: {plot_file}")
    
    # Also save as PDF
    plot_file_pdf = output_path / f"{score_field}_analysis.pdf"
    plt.savefig(plot_file_pdf, bbox_inches='tight')
    print(f"PDF plot saved to: {plot_file_pdf}")
    
    plt.show()

def generate_summary_statistics(averaged_scores: List[Dict[str, Any]], output_dir: str, score_field: str):
    """
    Generate and save summary statistics
    
    Args:
        averaged_scores: List of samples with average scores
        output_dir: Directory to save summary
        score_field: Type of scores being analyzed ('user_query_scores' or 'model_evaluation')
    """
    if not averaged_scores:
        print("No scores to analyze")
        return
    
    output_path = Path(output_dir)
    
    avg_scores = [sample["average_score"] for sample in averaged_scores]
    
    # Calculate statistics
    stats = {
        "score_field": score_field,
        "total_samples": len(averaged_scores),
        "mean_score": float(np.mean(avg_scores)),
        "median_score": float(np.median(avg_scores)),
        "std_score": float(np.std(avg_scores)),
        "min_score": float(np.min(avg_scores)),
        "max_score": float(np.max(avg_scores)),
        "q25_score": float(np.percentile(avg_scores, 25)),
        "q75_score": float(np.percentile(avg_scores, 75))
    }
    
    # Score distribution by ranges
    score_ranges = {
        'poor_1_2': len([s for s in avg_scores if s <= 2]),
        'fair_2_3': len([s for s in avg_scores if 2 < s <= 3]),
        'good_3_4': len([s for s in avg_scores if 3 < s <= 4]),
        'excellent_4_5': len([s for s in avg_scores if s > 4])
    }
    
    stats["score_distribution"] = score_ranges
    
    # Individual criteria statistics (if available)
    if averaged_scores and averaged_scores[0]["individual_scores"]:
        criteria_stats = {}
        all_criteria = set()
        for sample in averaged_scores:
            all_criteria.update(sample["individual_scores"].keys())
        
        for criterion in all_criteria:
            criterion_scores = []
            for sample in averaged_scores:
                if criterion in sample["individual_scores"]:
                    criterion_scores.append(sample["individual_scores"][criterion])
            
            if criterion_scores:
                criteria_stats[criterion] = {
                    "mean": float(np.mean(criterion_scores)),
                    "std": float(np.std(criterion_scores)),
                    "count": len(criterion_scores)
                }
        
        stats["criteria_breakdown"] = criteria_stats
    
    # Save statistics
    stats_file = output_path / f"{score_field}_statistics.json"
    with open(stats_file, 'w') as f:
        json.dump(stats, f, indent=2)
    
    print(f"Summary statistics saved to: {stats_file}")
    
    # Print key statistics
    score_display_name = score_field.replace('_', ' ').title()
    print(f"\n{'='*60}")
    print(f"{score_display_name} SUMMARY")
    print(f"{'='*60}")
    print(f"Total samples analyzed: {stats['total_samples']}")
    print(f"Mean score: {stats['mean_score']:.2f}")
    print(f"Median score: {stats['median_score']:.2f}")
    print(f"Standard deviation: {stats['std_score']:.2f}")
    print(f"Score range: {stats['min_score']:.2f} - {stats['max_score']:.2f}")
    print(f"\nScore Distribution:")
    print(f"  Poor (1-2): {score_ranges['poor_1_2']} ({score_ranges['poor_1_2']/len(avg_scores)*100:.1f}%)")
    print(f"  Fair (2-3): {score_ranges['fair_2_3']} ({score_ranges['fair_2_3']/len(avg_scores)*100:.1f}%)")
    print(f"  Good (3-4): {score_ranges['good_3_4']} ({score_ranges['good_3_4']/len(avg_scores)*100:.1f}%)")
    print(f"  Excellent (4-5): {score_ranges['excellent_4_5']} ({score_ranges['excellent_4_5']/len(avg_scores)*100:.1f}%)")

def main():
    parser = argparse.ArgumentParser(description="Analyze user query scores from evaluation results")
    parser.add_argument("--results_file", required=True, help="Path to JSONL results file")
    parser.add_argument("--output_dir", required=True, help="Directory to save analysis results")
    parser.add_argument("--score_field", choices=["user_query_scores", "model_evaluation"], default="user_query_scores",
                        help="Type of scores to analyze (default: user_query_scores)")
    
    args = parser.parse_args()
    
    print(f"Analyzing user query scores from: {args.results_file}")
    print(f"Output directory: {args.output_dir}")
    
    # Load analysis results
    results = load_analysis_results(args.results_file)
    if not results:
        print("No results loaded. Exiting.")
        sys.exit(1)
    
    print(f"Loaded {len(results)} batch results")
    
    # Extract scores
    user_scores = extract_scores(results, args.score_field)
    if not user_scores:
        print(f"No {args.score_field} found in results. Exiting.")
        sys.exit(1)
    
    print(f"Extracted {args.score_field} from {len(user_scores)} samples")
    
    # Calculate average scores
    averaged_scores = calculate_average_scores(user_scores)
    if not averaged_scores:
        print("No valid average scores calculated. Exiting.")
        sys.exit(1)
    
    print(f"Calculated average scores for {len(averaged_scores)} samples")
    
    # Generate plots
    try:
        plot_score_histograms(averaged_scores, args.output_dir, args.score_field)
    except Exception as e:
        print(f"Error creating plots: {e}")
        print("Continuing with statistics generation...")
    
    # Generate summary statistics
    generate_summary_statistics(averaged_scores, args.output_dir, args.score_field)
    
    print(f"\nAnalysis complete! Check {args.output_dir} for results.")

if __name__ == "__main__":
    main()
