schedules:
  - cron: "0 */4 * * *" # Once every 4 hours
    displayName: Sync every 4 hours
    branches:
      include:
        - main
    batch: true # only run one at a time
    always: true # always run on schedule, even if the "tools" repo hasn't changed
trigger: none
pr: none

resources:
  repositories:
    - repository: torchflow-mirror-openai-msft-rlhf-master
      type: git
      name: "Mimco/torchflow-mirror"
      ref: "refs/heads/openai/msft/rlhf-master"

pool:
  vmImage: ubuntu-latest

variables:
  - name: days-back-to-cache
    value: "7"
  - name: skipNugetSecurityAnalysis
    value: "true"
  - name: Codeql.Enabled
    value: false
  - name: Codeql.AnalyzeInPipeline
    value: true

steps:
  - checkout: self
    path: repo/glass
    sparseCheckoutDirectories: personal/project-coeus/tldr_reports # only checkout the tldr_reports directory to speed things up
    workspaceRepo: true # sets working directory to tools repo
  - checkout: torchflow-mirror-openai-msft-rlhf-master
    path: repo/torchflow-mirror
  - script: |
      set -e
      curl -LsSf https://astral.sh/uv/install.sh | sh
      cd personal/project-coeus/tldr_reports
      uv run tldr_reports/commit_cache.py \
        --local-repo-path=$(Pipeline.Workspace)/repo/torchflow-mirror \
        --remote-name=origin \
        --branch-name=openai/msft/rlhf-master \
        --start-date=$(date -d "$(days-back-to-cache) days ago" +"%Y-%m-%d") \
        --commit-storage.settings.storage-type=filesystem \
        --commit-storage.settings.path-prefix=$(Pipeline.Workspace)/.cache
    displayName: "cache diffs to disk"
  - task: AzureCli@2
    displayName: "upload commit cache to blob storage"
    inputs:
      azureSubscription: coeus-azure-resource-manager-connection # azure resource manager service connection
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: |
        set -e
        az storage blob upload-batch --auth-mode login --source $(Pipeline.Workspace)/.cache --destination https://changelogstorageprod.blob.core.windows.net/commits --overwrite
