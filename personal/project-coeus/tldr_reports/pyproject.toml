[project]
name = "tldr-reports"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiohttp>=3.12.14",
    "azure-identity>=1.23.1",
    "azure-storage-blob>=12.26.0",
    "openai>=1.98.0",
    "pydantic-settings>=2.10.1",
    "pyyaml>=6.0.2",
    "requests>=2.32.4",
    "rich>=14.1.0",
    "tiktoken>=0.9.0",
]

[tool.pyright]
venvPath = "."
venv = ".venv"

[tool.ruff]
line-length = 88

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
