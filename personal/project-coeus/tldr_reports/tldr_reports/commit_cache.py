import asyncio
import datetime
from typing import Self

from pydantic import BaseModel, Field, model_validator
from pydantic_settings import (
    BaseSettings,
    CliApp,
    CliImplicitFlag,
    SettingsConfigDict,
)
from rich.columns import Columns
from rich.console import Console, Group
from rich.panel import Panel
from rich.pretty import Pretty
from rich.progress import Progress
from tldr_reports.git import CommitContent, Git, LogEntry
from tldr_reports.storage import (
    FileSystemStorageSettings,
    StorageProtocol,
    StorageSettings,
    storage_factory,
)

console = Console()


class CommitContentPath(BaseModel):
    """
    Represents the path to the content of a commit.
    """

    commit_sha: str = Field(
        description="The SHA of the commit.",
    )

    path: str = Field(
        description="The path where the commit content is stored.",
    )


class CommitList(BaseModel):
    refresh_date: datetime.datetime | None = Field(
        default=None,
        description="The date when the commit list was last refreshed.",
    )

    date: datetime.date = Field(
        description="The date for which the commit set is valid.",
    )

    commits: list[CommitContentPath] = Field(
        description="A list of commit content paths for the date.",
    )


async def read_cached_commit_list(
    storage: StorageProtocol, repo_cache_key: str, date: datetime.date
) -> CommitList | None:
    """
    Fetches commit list for a specific date from the cache for a specific date.

    Args:
        storage (AzureStorage): The AzureStorage instance to use for fetching.
        repo_cache_key (str): The cache key for the repository.
        date (datetime.date): The date for which to fetch commits.

    Returns:
        CommitList: A CommitList object containing commit SHAs and their paths.
    """

    path = _path_for_commit_list_for_date(repo_cache_key, date)
    content = await storage.read(path)
    if content is None:
        return None
    commit_set = CommitList.model_validate_json(content.decode("utf-8"))
    return commit_set


async def _write_cached_commit_list(
    storage: StorageProtocol,
    repo_cache_key: str,
    date: datetime.date,
    commit_list: CommitList,
) -> None:
    path = _path_for_commit_list_for_date(repo_cache_key, date)
    await storage.write(path, commit_list.model_dump_json().encode("utf-8"))


async def read_cached_commit_content(
    storage: StorageProtocol, repo_cache_key: str, commit_sha: str
) -> CommitContent | None:
    """
    Fetches the content of a commit by its SHA.

    Args:
        storage (AzureStorage): The AzureStorage instance to use for fetching.
        repo_cache_key (str): The cache key for the repository.
        commit_sha (str): The SHA of the commit to fetch.

    Returns:
        str: The content of the commit.
    """

    path = _path_for_commit_content(repo_cache_key, commit_sha)
    content = await storage.read(path)
    if content is None:
        return None
    return CommitContent.model_validate_json(content.decode("utf-8"))


def _path_prefix_for_repo(repo_cache_key: str) -> str:
    return f"commit-cache/{repo_cache_key}"


def _path_for_commit_list_for_date(repo_cache_key: str, date: datetime.date) -> str:
    return f"{_path_prefix_for_repo(repo_cache_key)}/commits-by-date/{date.isoformat()}.json"


def _path_for_commit_content(repo_cache_key: str, commit_sha: str) -> str:
    return f"{_path_prefix_for_repo(repo_cache_key)}/commit-contents/{commit_sha}.txt"


async def cache_commits_for_date(
    storage: StorageProtocol,
    repo_cache_key: str,
    commits_by_author_date: list[LogEntry],
    git: Git,
    date: datetime.date,
    overwrite: bool,
    concurrency: int,
    progress: Progress,
) -> int:
    """
    Caches the commits for a specific date.

    Args:
        storage (AzureStorage): The AzureStorage instance to use for caching.
        repo_cache_key (str): The cache key for the repository.
        local_repo_path (str): The local path to the repository.
        date (datetime.date): The date for which to cache commits.
    """

    progress_task = progress.add_task("Caching commits ...")
    try:
        start_date = date
        end_date = start_date + datetime.timedelta(days=1)

        commit_shas_for_date = [
            commit_log.sha
            for commit_log in commits_by_author_date
            if start_date <= commit_log.author_date.date() < end_date
        ]

        commit_list = CommitList(
            refresh_date=datetime.datetime.now(tz=datetime.timezone.utc),
            date=date,
            commits=[
                CommitContentPath(
                    commit_sha=sha, path=_path_for_commit_content(repo_cache_key, sha)
                )
                for sha in commit_shas_for_date
            ],
        )

        progress.update(
            progress_task,
            description=f"Caching commits ({len(commit_list.commits)}) ...",
            total=len(commit_list.commits),
        )

        existing_commit_list = await read_cached_commit_list(
            storage=storage, repo_cache_key=repo_cache_key, date=date
        )

        if not overwrite and existing_commit_list is not None:
            current_shas = [c.commit_sha for c in commit_list.commits]
            existing_shas = [c.commit_sha for c in existing_commit_list.commits]
            if existing_shas == current_shas:
                progress.log(
                    f"Commit list for {date} already up to date: "
                    f"{existing_shas[0] if existing_shas else ''} ... {existing_shas[-1] if existing_shas else ''} ({len(existing_shas)})."
                    " Skipping ..."
                )
                return len(existing_shas)

            progress.log(
                f"Commit list for {date} exists, but is out of date: "
                f"{existing_shas[0] if existing_shas else '-'} ... {existing_shas[-1] if existing_shas else '-'} ({len(existing_shas)})"
                f" vs {current_shas[0] if current_shas else ''} ... {current_shas[-1] if current_shas else ''} ({len(current_shas)})."
                " Overwriting ..."
            )

        semaphore = asyncio.Semaphore(concurrency)

        async def _process_commit(commit: CommitContentPath) -> None:
            async with semaphore:
                content = await git.commit_details(commit.commit_sha)

                await storage.write(
                    commit.path,
                    content.model_dump_json().encode("utf-8"),
                )
                # progress.log(f"Cached commit {commit.commit_sha}")
            progress.update(progress_task, advance=1, description=commit.commit_sha)

        # cache the contents of all commits
        tasks = []
        for commit in commit_list.commits:
            task = asyncio.create_task(_process_commit(commit))
            tasks.append(task)

        await asyncio.gather(*tasks)

        # cache the dictionary of commit hashes and their paths for the date
        await _write_cached_commit_list(
            storage=storage,
            repo_cache_key=repo_cache_key,
            date=date,
            commit_list=commit_list,
        )

        progress.log(
            f"Cached {len(commit_list.commits)} commits for {date.isoformat()}"
        )

        return len(commit_list.commits)

    finally:
        progress.update(progress_task, visible=False)


class CommitCacheCliApp(BaseSettings):
    """
    Caches commits and their content for a specific date.
    """

    model_config = SettingsConfigDict(
        cli_parse_args=True,
        cli_kebab_case=True,
        cli_enforce_required=True,
        env_file=".env",
        env_file_encoding="utf-8",
        cli_avoid_json=True,
    )

    local_repo_path: str = Field(
        description="Path to the local git repository read from.",
    )

    remote_name: str = Field(
        default="origin",
        description="Name of the remote to use for fetching commits.",
    )

    branch_name: str = Field(
        description="Name of the branch to pull in the local repository.",
    )

    fetch_and_reset: CliImplicitFlag[bool] = Field(
        default=False,
        description="Whether to fetch and reset the local branch to match the remote, before caching commits.",
    )

    start_date: datetime.date = Field(
        description="Start date of date range for which to cache commits.",
    )

    end_date: datetime.date = Field(
        default_factory=datetime.date.today,
        description="End date of date range for which to cache commits. Defaults to today.",
    )

    repo_cache_key: str = Field(
        default="",
        description="Cache key for the repository to use for commit caching. If not specified, uses the last path segment of the repository path, plus remote and branch name.",
    )

    concurrency: int = Field(
        default=10,
        description="Number of concurrent tasks to run when caching commits.",
    )

    overwrite: CliImplicitFlag[bool] = Field(
        default=False,
        description="Whether to overwrite existing cached commits.",
    )

    ignored_paths: list[str] = Field(
        default=list(),
        description="List of repo-relative paths to ignore when caching commits.",
    )

    commit_storage: StorageSettings = Field(
        default=StorageSettings(
            settings=FileSystemStorageSettings(path_prefix="./.cache/")
        ),
        description="Settings for storage for commit caching.",
    )

    @model_validator(mode="after")
    def set_repo_cache_key_if_empty(self) -> Self:
        if self.repo_cache_key == "":
            self.repo_cache_key = "/".join(
                (
                    self.local_repo_path.strip("/").split("/")[-1],
                    self.remote_name,
                    self.branch_name,
                )
            )
        return self

    async def cli_cmd(self) -> None:
        console.log("Running update-cache command")
        console.print(
            Panel(
                renderable=Group(
                    Columns(
                        (
                            Group(
                                "Repo:",
                                "Remote:",
                                "Branch:",
                                "Repo cache key:",
                                "Storage:",
                                "Start date:",
                                "End date:",
                            ),
                            Group(
                                Pretty(self.local_repo_path),
                                Pretty(self.remote_name),
                                Pretty(self.branch_name),
                                Pretty(self.repo_cache_key),
                                Pretty(self.commit_storage),
                                Pretty(str(self.start_date)),
                                Pretty(str(self.end_date or self.start_date)),
                            ),
                        )
                    ),
                ),
            )
        )

        git = Git(
            repo_path=self.local_repo_path,
            remote_name=self.remote_name,
            branch_name=self.branch_name,
        )

        if self.fetch_and_reset:
            with console.status(
                f"Fetching repo: {self.local_repo_path} ({self.remote_name})"
            ):
                output = await git.fetch()
                console.log(
                    f"Fetched '{self.remote_name}' for repo {self.local_repo_path}\n{output.strip()}"
                )

            with console.status(
                f"Resetting repo: {self.local_repo_path} ({self.remote_name})"
            ):
                output = await git.reset_hard()
                console.log(
                    f"Reset hard '{self.remote_name}/{self.branch_name}' for repo {self.local_repo_path}\n{output.strip()}"
                )

        with console.status("Getting complete commit log ..."):
            commits_by_author_date = await git.log()
            console.log(
                f"Commit log retrieved; commit count: {len(commits_by_author_date)}"
            )

        self.end_date = self.end_date or self.start_date
        date_range = [
            self.start_date + datetime.timedelta(days=x)
            for x in range((self.end_date - self.start_date).days + 1)
        ]

        commit_count = 0

        with Progress(transient=True) as progress:
            date_progress_task = progress.add_task(
                f"Checking commits for dates ({len(date_range)}) ...",
                total=len(date_range),
            )

            async with storage_factory(self.commit_storage) as storage:
                for date in date_range:
                    progress.update(
                        date_progress_task,
                        description=f"Checking commits for date: {date.isoformat()}",
                    )
                    commit_count += await cache_commits_for_date(
                        storage=storage,
                        repo_cache_key=self.repo_cache_key,
                        commits_by_author_date=commits_by_author_date,
                        git=git,
                        date=date,
                        overwrite=self.overwrite,
                        concurrency=self.concurrency,
                        progress=progress,
                    )

                    progress.update(date_progress_task, advance=1)

        console.log(f"Run complete. Total commits cached: {commit_count}")


if __name__ == "__main__":
    CliApp.run(CommitCacheCliApp)
