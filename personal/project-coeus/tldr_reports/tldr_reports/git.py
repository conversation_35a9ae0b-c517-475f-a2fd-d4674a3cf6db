import asyncio
import datetime
import re
import subprocess
import textwrap
from typing import Literal, NamedTuple

import rich
from pydantic import BaseModel, Field


class CommitContent(BaseModel):
    """
    Represents the content of a commit.
    """

    commit_sha: str = Field(
        description="The SHA of the commit.",
    )

    author_date: datetime.datetime = Field(
        description="The author date of the commit.",
    )

    commit_date: datetime.datetime = Field(
        description="The commit date of the commit.",
    )

    author: str = <PERSON>(
        description="The author of the commit.",
    )

    author_email: str = Field(
        default="",
        description="The email of the author of the commit.",
    )

    committer: str = Field(
        description="The committer of the commit.",
    )

    committer_email: str = Field(
        default="",
        description="The email of the committer of the commit.",
    )

    stat: str = Field(
        description="The stat of the commit.",
    )

    title: str = Field(
        description="The title of the commit.",
    )

    body: str = Field(
        description="The body of the commit.",
    )

    patch: str = Field(
        description="The patch of the commit.",
    )

    @property
    def show(self) -> str:
        header = f"commit {self.commit_sha}\nAuthor: {self.author}\nDate: {self.author_date.isoformat()}"
        message = textwrap.indent(f"{self.title}\n\n{self.body}", " " * 4)

        return f"{header}\n\n{message}\n\n{self.patch}"

    @property
    def original_commit_sha(self) -> str | None:
        # torchflow-mirror pattern
        match = re.search(r"Original-commit-id: ([a-f0-9]+)", self.body)
        if match is not None:
            return match.group(1)

        # export-model-runner pattern
        match = re.search(
            r"\"_monorepoCommitMetadata\": \{\"hash\": \"([a-f0-9]+)\"", self.body
        )
        if match is not None:
            return match.group(1)

        return None

    @property
    def original_author_date(self) -> datetime.datetime | None:
        # export-model-runner pattern
        match = re.search(
            r"\"_monorepoCommitMetadata\": \{\"hash\": \"[a-f0-9]+\", \"ts\": \"([0-9]+)\"",
            self.body,
        )
        if match is not None:
            return datetime.datetime.fromtimestamp(int(match.group(1)))

        return None


LogEntry = NamedTuple("LogEntry", [("author_date", datetime.datetime), ("sha", str)])


class Git:
    arg_date_format = "--date=iso-strict-local"
    """iso-strict formats as ISO, "-local" in combination with TZ=UTC prints dates in UTC (versus author timezone)"""
    arg_author_date_order = "--author-date-order"
    env_tz = {"TZ": "UTC"}

    def __init__(self, repo_path: str, remote_name: str, branch_name: str):
        self.repo_path = repo_path
        self.remote_name = remote_name
        self.branch_name = branch_name

    async def fetch(self) -> str:
        await _exec_git_command(
            local_repo_path=self.repo_path,
            args=["remote", "prune", self.remote_name],
        )
        return await _exec_git_command(
            local_repo_path=self.repo_path,
            args=["fetch", self.remote_name],
        )

    async def reset_hard(self) -> str:
        await _exec_git_command(
            local_repo_path=self.repo_path,
            args=["switch", self.branch_name],
        )
        return await _exec_git_command(
            local_repo_path=self.repo_path,
            args=["reset", "--hard", f"{self.remote_name}/{self.branch_name}"],
        )

    async def log(
        self,
        path: str = ".",
        grep_pattern: str | None = None,
        order_by_author_date: bool = True,
        path_exclusions: list[str] | None = None,
        skip: int | None = None,
        max_count: int | None = None,
        reverse: Literal[True] | None = None,
    ) -> list[LogEntry]:
        args: list[str] = [
            "log",
            Git.arg_date_format,
            "--pretty=format:%ad %H",  # print the author date and commit hash
        ]
        if order_by_author_date:
            args.append(Git.arg_author_date_order)
        if grep_pattern is not None:
            args.extend(["--grep", grep_pattern])
        if skip is not None:
            args.extend(["--skip", str(skip)])
        if max_count is not None:
            args.extend(["--max-count", str(max_count)])
        if reverse:
            args.append("--reverse")

        args.extend(["--", path])

        if path_exclusions:
            for path_exclusion in path_exclusions:
                args.append(f"':(exclude){path_exclusion}'")

        output = await _exec_git_command(
            local_repo_path=self.repo_path,
            args=args,
            env=Git.env_tz,
        )

        split_lines = [line.split(" ") for line in output.strip().splitlines()]

        # parse datetimes
        return [
            LogEntry(
                author_date=datetime.datetime.fromisoformat(parts[0]), sha=parts[1]
            )
            for parts in split_lines
        ]

    async def commit_details(
        self, commit_sha: str, include_stat: bool = True, include_patch: bool = True
    ) -> CommitContent:
        output = await _exec_git_command(
            local_repo_path=self.repo_path,
            args=[
                "show",
                Git.arg_date_format,
                "--format=%ad%n%cd%n%an%n%ae%n%cn%n%ce%n%s%n%b",
                "--no-patch",
                commit_sha,
            ],
            env=Git.env_tz,
        )

        metadata_parts = output.strip().splitlines()

        stat = ""
        if include_stat:
            stat = await _exec_git_command(
                local_repo_path=self.repo_path,
                args=[
                    "show",
                    "--format=",  # no header
                    "--stat",
                    commit_sha,
                ],
            )

        patch = ""
        if include_patch:
            patch = await _exec_git_command(
                local_repo_path=self.repo_path,
                args=[
                    "show",
                    "--format=",  # no header
                    "--patch",
                    commit_sha,
                ],
            )

        return CommitContent(
            commit_sha=commit_sha,
            author_date=datetime.datetime.fromisoformat(metadata_parts[0]),
            commit_date=datetime.datetime.fromisoformat(metadata_parts[1]),
            author=metadata_parts[2],
            author_email=metadata_parts[3],
            committer=metadata_parts[4],
            committer_email=metadata_parts[5],
            title=metadata_parts[6],
            body="\n".join(metadata_parts[7:]),
            stat=stat,
            patch=patch,
        )


async def _exec_git_command(
    local_repo_path: str, args: list[str], env: dict[str, str] | None = None
) -> str:
    """Executes a git command in the specified local repository path."""
    # console.log(f"shell: git {' '.join(args)}")
    result = await asyncio.to_thread(
        subprocess.run,
        ["git", *args],
        cwd=local_repo_path,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        encoding="utf-8",
        errors="replace",
        env=env,
    )
    if result.returncode != 0:
        rich.get_console().log(
            "git command failed:", result.stderr.strip(), "cmd: git", *args
        )
        exit(result.returncode)

    return result.stdout
