import pathlib
from collections.abc import AsyncGenerator, AsyncIterable
from contextlib import asynccontextmanager
from typing import Literal, Protocol

from azure.core.exceptions import ResourceNotFoundError
from azure.identity.aio import DefaultAzureCredential
from azure.storage.blob.aio import BlobServiceClient, ContainerClient
from pydantic import BaseModel, Field


class AzureStorageSettings(BaseModel):
    """
    Settings for Azure Blob Storage.
    """

    storage_type: Literal["azure"] = "azure"

    storage_account_name: str = Field(
        description="Azure Storage account name",
    )

    storage_container_name: str = Field(
        description="Azure Blob Storage container name",
    )

    path_prefix: str = Field(
        default="",
        description="Path prefix within the container",
    )


class FileSystemStorageSettings(BaseModel):
    """
    Settings for file system storage.
    """

    storage_type: Literal["filesystem"] = "filesystem"

    path_prefix: str = Field(
        description="Path prefix within the file system",
    )


class StorageSettings(BaseModel):
    settings: FileSystemStorageSettings | AzureStorageSettings = Field(
        discriminator="storage_type"
    )


class StorageProtocol(Protocol):
    async def exists(self, file_path: str) -> bool: ...

    async def write(self, file_path: str, data: bytes) -> None: ...

    async def read(self, file_path: str) -> bytes | None: ...

    def list(self, prefix: str | None = None) -> AsyncIterable[str]: ...

    @property
    def base_path(self) -> str: ...


class AzureStorage:
    """
    Manages interactions with Azure Blob Storage.
    """

    def __init__(self, container_client: ContainerClient, path_prefix: str):
        self._path_prefix = path_prefix + (
            "/" if path_prefix and not path_prefix.endswith("/") else ""
        )
        self._container_client = container_client

    @property
    def base_path(self) -> str:
        return (
            self._container_client.url
            + ("/" if not self._container_client.url.endswith("/") else "")
            + self._path_prefix
        )

    async def list(self, prefix: str | None = None) -> AsyncIterable[str]:
        """
        Lists all files in the specified container, optionally matching a prefix.
        """
        async for blob_name in self._container_client.list_blob_names(
            name_starts_with=self._path_prefix + (prefix or "")
        ):
            yield blob_name

    async def exists(self, file_path: str) -> bool:
        """
        Checks if a file exists in the specified container.
        """
        async for blob_name in self.list(prefix=self._path_prefix + file_path):
            if blob_name == self._path_prefix + file_path:
                return True

        return False

    async def write(self, file_path: str, data: bytes) -> None:
        """
        Uploads a file to the specified container.
        """

        await self._container_client.upload_blob(
            name=self._path_prefix + file_path, data=data, overwrite=True
        )

    async def read(self, file_path: str) -> bytes | None:
        """
        Downloads a file from the specified container.
        """
        try:
            result = await self._container_client.download_blob(
                blob=self._path_prefix + file_path
            )
            return await result.readall()

        except ResourceNotFoundError:
            return None


class LocalStorage:
    """
    Manages interactions with the local file system.
    """

    def __init__(self, path_prefix: pathlib.Path):
        self._path_prefix = path_prefix

    @property
    def base_path(self) -> str:
        return str(self._path_prefix.absolute()) + "/"

    async def exists(self, file_path: str) -> bool:
        """
        Checks if a file exists in the local file system.
        """
        return (self._path_prefix / file_path).exists()

    async def write(self, file_path: str, data: bytes) -> None:
        """
        Writes a file to the local file system.
        """
        (self._path_prefix / file_path).parent.mkdir(parents=True, exist_ok=True)
        with open(self._path_prefix / file_path, "wb") as f:
            f.write(data)

    async def list(self, prefix: str | None = None) -> AsyncIterable[str]:
        """
        Lists all files in the local file system, optionally matching a prefix.
        """
        base = self._path_prefix / (prefix or "")
        if base.is_file():
            yield str(base.relative_to(self._path_prefix))
            return

        for path in base.rglob("*"):
            async for sub_path in self.list(str(path.relative_to(self._path_prefix))):
                yield sub_path

    async def read(self, file_path: str) -> bytes | None:
        """
        Reads a file from the local file system.
        """
        try:
            with open(self._path_prefix / file_path, "rb") as f:
                return f.read()
        except FileNotFoundError:
            return None


@asynccontextmanager
async def build_container_client(
    settings: AzureStorageSettings,
) -> AsyncGenerator[ContainerClient]:
    """
    Returns a ContainerClient instance using the settings.
    """
    async with (
        DefaultAzureCredential() as credential,
        BlobServiceClient(
            account_url=f"https://{settings.storage_account_name}.blob.core.windows.net",
            credential=credential,
        ) as blob_service_client,
        blob_service_client.get_container_client(
            settings.storage_container_name
        ) as container_client,
    ):
        yield container_client


@asynccontextmanager
async def storage_factory(
    storage_settings: StorageSettings,
) -> AsyncGenerator[StorageProtocol]:
    """
    Factory to create a storage client based on the settings.
    """
    match storage_settings.settings:
        case AzureStorageSettings():
            async with build_container_client(
                storage_settings.settings
            ) as container_client:
                yield AzureStorage(
                    container_client=container_client,
                    path_prefix=storage_settings.settings.path_prefix,
                )

        case FileSystemStorageSettings():
            base_path = pathlib.Path(storage_settings.settings.path_prefix)
            base_path.mkdir(parents=True, exist_ok=True)
            yield LocalStorage(path_prefix=base_path)

        case _:
            raise ValueError("Unsupported storage type in settings.")
