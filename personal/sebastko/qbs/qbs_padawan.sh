#!/bin/bash

: <<'COMMENT'

Useful for debugging datasets/graders OR for sampling rollouts.

Can be executed from a CPU-only devbox against a bus.

If you have snowflake configured, the samples will appear at gosb
(https://gosb.int.prod-uksouth-7.dev.openai.org/) after a couple of minutes.

Sample command to create a CPU-only devbox (as of 8/22/2025, our clusters
have only a max 4-core CPU SKU):
```
OAIPKG_OVERRIDE_WHEEL=unsafe_skip \
  twdev create-ray-devbox \
    num_pods=1 \
    num_cpu=4 \
    num_gpu=0 \
    job_name=<job name> \
    team=<your team> \
    cluster=<your cluster> \
    priority_class=<your priority>
```
COMMENT


# GPT5-mini low-priority bus
BUS_TOPIC="bus:snap:orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted:user:swe-main-run"

# Check if bus is up
bup engines -t $BUS_TOPIC

# Some of the SWEAgent code requires existence of this dir for writing logs.
mkdir -p /var/log/supervisor/

DEBUG=""
# Uncomment if you want to attach a debugger
#DEBUG="-m debugpy --wait-for-client --listen 0.0.0.0:5678"

CMD=(
python $DEBUG -m qstar.bin.sample
experiment_name=${OPENAI_USER}-qbs-$(date +%Y%m%d%H%M%S)
  
# For quick testing and easy one-thread debugging:
sampling_concurrency=1
samples_per_instance=1
...max_n_datapoints=1 # One datapoint from each dataset

# Bus to sample policy completions from:
token_completer=bus_token_completer:BusTokenCompleter.Config
token_completer.topic_or_snapshot="$BUS_TOPIC"

# Model configs (keep consistent with training/evaluation)
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc

# Model settings
defaults.n_ctx=131072
...container_tool_config="mix_tool"
...sampler='deep_swe_msft.padawan_data.sampler:PadawanSampler'
...harmony_constrained_sampling=True  

# Dataset configs!
:deep_swe_msft.presets:debug_run
...dataset_container=orngscuscresco
...max_num_yields=256
...average_reward_minimum=0.01
  
# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
defaults.inverse_token_cost=1024
defaults.instances_per_batch=32
defaults.target_samples_per_instance=32

# Dataset configs
defaults.instance_completer=qstar.instance_completers:VariantsInstanceCompleter
defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer
  
batcher.curriculum.training_datasets.0.dataset.inverse_token_cost_multiplier=16

# Logging and misc
security_profile=msft-orng

# Either configure Snowflake or enable dry_run to prevent kafka errors:
# dry_run=True
)
  

"${CMD[@]}"