"""
Schedules peashooter, automatically starts Q* training on MATH and automatically
shuts down the cluster after 1 step of training.

Usage from the laptop:
```
pychz math_train.chz.py -e launch_cluster \
    cluster=<your-cluster>
```
See README.md for more details.
"""

import datetime
import os

# TODO: expose in a separate library.
CLUSTER_TO_STORAGE_MAP = {
    "prod-uksouth-7": "orngcresco",
    "prod-uksouth-8": "orngcresco",
    "prod-southcentralus-hpe-2": "orngscuscresco",
    "prod-southcentralus-hpe-3": "orngscuscresco",
    "prod-southcentralus-hpe-5": "orngscuscresco",
    "stage-southcentralus-hpe-1": "orngscuscresco",
}


def launch_cluster(
    cluster,
    priority="team-low",
    team=None,
    queue=None,
    cpu_controller=True,
    rapid_id=None,
    experiment_name=None,
    prefix="auto",
    base_storage=None,
    autostart=True,
):
    if experiment_name is None:
        experiment_name = f"{prefix}-{datetime.datetime.now():%Y%m%d%H%M%S}"
    if rapid_id is None:
        rapid_id = experiment_name
    if base_storage is None:
        base_storage = CLUSTER_TO_STORAGE_MAP[cluster]

    chunks = ["python -m qstar.peashooter.run_peashooter"]
    if team:
        chunks.append(f"rapid.cluster.team={team}")
    chunks.append(f"""
rapid.cluster.name={cluster}
rapid.cluster.priority={priority}
rapid.rapid_id={rapid_id}
security_profile=msft-orng

rapid.pull_git_on_restart="False"
num_controller_nodes="1"
n_gpus_per_sampler="8"
sampling_jobs="2"
n_train_gpus="224"
use_beam_object_store="True"
cpu_controller="False"
strict="True"
""")

    if autostart:
        chunks.append(f"""
autostart_file={__file__}
autostart_args="experiment_name={experiment_name} base_storage={base_storage}"
autostart_max_attempts=3

# Local validation doesn't work as of Sept 2025, so skip it.
# See Teams thread in Project Orange (CoreAI only): https://aka.ms/AAxxib4
autostart_skip_validation=True
# Starting from August FI, use the following flag instead:
# autostart_skip_local_test=True
""")

    return "\n".join(chunks) + "\n"


def run_experiment(experiment_name, base_storage="orngcresco"):
    return f"""
python3 -m qstar.run_experiment
name="{experiment_name}"
skip_validate_config=True
seed=20250807 nostrict

# ## Policy settings
:berry_models.scallion:d64_chicken_80g_bf16

root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='tensorcache_v2_load_allow_missing_tensor=True decoupled_attention_token_mapping={{None:0,(0,200166):0}}'

# gpt-5
policy.initial_checkpoint="az://{base_storage}/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas"

policy.n_gpus=224
policy.is_multimodal=True 
...encoding_name=orion_200k
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_tools_v2

# Systems settings
:qstar.presets.common:longer_timeout
peashooter.sampling_use_ev3=True
peashooter.timeout_seconds.stalled_datapoint=7200
peashooter.timeout_seconds.lost_datapoint=360 
timeout.default=None

# Model settings
# max_steps=20 
policy.n_ctx=4096
defaults.n_ctx=4096
defaults.sampler.stop_tokens="<|im_end|>,<|fim_suffix|>" 
...harmony_constrained_sampling=True
optimizer.hparam_scaler.lr_per_instance_d16=1e-5
...save_every=5

# Dataset configs!
:qstar.presets.mathgen:tr_mathd_v2 
batcher.curriculum.training_datasets.0.dataset.dataset_container=orngcresco 
batcher.curriculum.training_datasets.0.dataset.dataset_id=data.mathd-msft.train 
batcher.curriculum.training_datasets.0.dataset=HarmonyCompletionDatasetConfig

berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=10

# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
defaults.inverse_token_cost=1024
defaults.instances_per_batch=32
defaults.target_samples_per_instance=32

# Enable the COMMENTARY channel
defaults.channel_config.channels="analysis,final" #  The channels that the policy can use, i.e. the channels that appear in the system prompt.

# Sampling and timeout 
batch_completer.n_batches_in_flight=30 # number of batches in flight
peashooter.num_sampling_processes=16 # number of sampling processes per instance worker
peashooter.sampling_concurrency=12 # concurrency sampling threads per process
peashooter.num_instance_workers=48 # number of instance workers


# Dataset configs
defaults.instance_completer=qstar.instance_completers:VariantsInstanceCompleter
defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
"""


_CMD = run_experiment
