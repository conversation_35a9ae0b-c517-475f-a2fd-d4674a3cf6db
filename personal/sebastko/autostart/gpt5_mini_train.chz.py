"""
Schedules peashooter, automatically starts Q* training on MATH and automatically
shuts down the cluster after 1 step of training.

Usage from the laptop:
```
pychz math_train.chz.py -e launch_cluster \
    cluster=<your-cluster>
```
See README.md for more details.
"""

import datetime
import os

# TODO: expose in a separate library.
CLUSTER_TO_STORAGE_MAP = {
    "prod-uksouth-7": "orngcresco",
    "prod-uksouth-8": "orngcresco",
    "prod-southcentralus-hpe-2": "orngscuscresco",
    "prod-southcentralus-hpe-3": "orngscuscresco",
    "prod-southcentralus-hpe-5": "orngscuscresco",
    "stage-southcentralus-hpe-1": "orngscuscresco",
}


def launch_cluster(
    cluster,
    priority="team-low",
    team=None,
    queue=None,
    cpu_controller=True,
    rapid_id=None,
    experiment_name=None,
    prefix="auto",
    base_storage=None,
    autostart=True,
):
    if experiment_name is None:
        experiment_name = f"{prefix}-{datetime.datetime.now():%Y%m%d%H%M%S}"
    if rapid_id is None:
        rapid_id = experiment_name
    if base_storage is None:
        base_storage = CLUSTER_TO_STORAGE_MAP[cluster]

    chunks = ["python -m qstar.peashooter.run_peashooter"]
    if team:
        chunks.append(f"rapid.cluster.team={team}")
    chunks.append(f"""
rapid.cluster.name={cluster}
rapid.cluster.priority={priority}
rapid.rapid_id={rapid_id}
security_profile=msft-orng

num_controller_nodes=1
n_gpus_per_sampler=8
sampling_jobs=1
n_train_gpus=56
cpu_controller=False
strict=True
""")

    if autostart:
        chunks.append(f"""
autostart_file={__file__}
autostart_args="experiment_name={experiment_name} base_storage={base_storage}"
autostart_max_attempts=3

# Local validation doesn't work as of Sept 2025, so skip it.
# See Teams thread in Project Orange (CoreAI only): https://aka.ms/AAxxib4
autostart_skip_validation=True
# Starting from August FI, use the following flag instead:
# autostart_skip_local_test=True
""")

    return "\n".join(chunks) + "\n"


def run_experiment(experiment_name, base_storage="orngcresco"):
    return f"""
python3 -m qstar.run_experiment
name="{experiment_name}"
:qstar.presets.common:preamble
seed=20250616 nostrict
:qstar.presets.mathgen:tr_mathd_v2
batcher.curriculum.training_datasets.0.dataset.dataset_container={base_storage}
batcher.curriculum.training_datasets.0.dataset.dataset_id=data.mathd-msft.train
:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
defaults.inverse_token_cost=1024
defaults.instances_per_batch=32
defaults.target_samples_per_instance=32
peashooter.num_sampling_processes=32
peashooter.sampling_concurrency=16
peashooter.num_instance_workers=64
peashooter.timeout_seconds.stalled_datapoint=3600
peashooter.sampling_use_ev3=True
enable_slackbot=False
security_profile=msft-orng
github_upload=False
wandb_enable=True
kafka_enable=False
max_steps=10
policy.n_ctx=4096
defaults.n_ctx=4096
...save_every=5
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
policy.initial_checkpoint=az://{base_storage}/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted
"""


_CMD = run_experiment
