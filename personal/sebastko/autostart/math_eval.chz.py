"""
Schedules peashooter, automatically starts Q* training on MATH and automatically
shuts down the cluster after 1 step of training.

Usage from the laptop:
```
pychz math_eval.chz.py -e launch_cluster \
    cluster=<your-cluster>
```
See README.md for more details.
"""

import datetime
import os

# TODO: expose in a separate library.
CLUSTER_TO_STORAGE_MAP = {
    "prod-uksouth-7": "orngcresco",
    "prod-uksouth-8": "orngcresco",
    "prod-southcentralus-hpe-2": "orngscuscresco",
    "prod-southcentralus-hpe-3": "orngscuscresco",
    "prod-southcentralus-hpe-5": "orngscuscresco",
    "stage-southcentralus-hpe-1": "orngscuscresco",
}


def launch_cluster(
    cluster,
    rapid_id,
    experiment_id,
    priority="low-priority",
    team=None,
    queue=None,
    cpu_controller=True,
    base_storage=None,
    autostart=True,
):
    if base_storage is None:
        base_storage = CLUSTER_TO_STORAGE_MAP[cluster]

    chunks = ["python -m qstar.peashooter.run_peashooter"]
    if team:
        chunks.append(f"rapid.cluster.team={team}")
    chunks.append(f"""
rapid.cluster.name={cluster}
rapid.cluster.priority={priority}
rapid.rapid_id={rapid_id}
security_profile=msft-orng

n_train_gpus=0
n_sampling_gpus=8
eval_only=True
""")

    if cpu_controller:
        # Our clusters have 4 CPU SKUs.
        chunks.append(f"""
cpu_controller=True
n_cpu_cores_for_controller=4
""")

    if autostart:
        chunks.append(f"""
autostart_file={__file__}
autostart_args="experiment_id={experiment_id} base_storage={base_storage}"
autostart_max_attempts=3

# Local validation doesn't work as of Sept 2025, so skip it.
# See Teams thread in Project Orange (CoreAI only): https://aka.ms/AAxxib4
autostart_skip_validation=True
# Starting from August FI, use the following flag instead:
# autostart_skip_local_test=True
""")

    return "\n".join(chunks) + "\n"


def run_eval(experiment_id, base_storage="orngcresco"):
    return f"""
python3 -m qstar.run_eval
nostrict
name="{experiment_id}-peaval"
experiment_id={experiment_id}

:berry_models.scallion:d16_80g
peashooter.timeout_seconds.stalled_datapoint=3600
peashooter.timeout_seconds.lost_datapoint=1800
peashooter.use_stale_kv_cache=False
load.restore_from_all_clusters=False
 
defaults.override_target_samples_per_instance=64
eval_settings.evals.0.dataset.dataset_id=data.mathd-msft.test
eval_settings.evals.0.dataset.grader=qstar.graders.mathgen_grader:MathgenGrader
...dataset_container={base_storage}
 
security_profile=msft-orng
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
"""


_CMD = run_eval
