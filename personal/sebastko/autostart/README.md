# Peashooter autostart

The feature allows to schedule a peashooter cluster with an auto-start script
which will be executed once the cluster is up and running. The cluster will
be automatically deleted after the job is finished. If there are failures
in the job, by default it will be retried 3 times (can be changed with the `autostart_max_attempts` argument).

It also introduces `pychz` which has become popular recently within OAI to
prepare and submit jobs (even without auto-start).

## Pre-requisites
Update: As of 8/19/2025 autostart is merged into the `orange/main` branch of `torchflow-mirror`

Also make sure `pychz` is installed on your laptop:
```
oaipkg install pychz
```

## Usage
Minimal example from the laptop:

```
pychz math_train.chz.py -e launch_cluster \
    team=team-moonfire-genaicore \
    cluster=prod-southcentralus-hpe-3 \
    priority=team-low
```

Afer a while, you can also spawn an evaluation job passing the training job's experiment_id:
```
pychz math_eval.chz.py -e launch_cluster \
    rapid_id=auto-peaval \
    experiment_id=sebastko-auto-20250813163039 \
    team=team-moonfire-genaicore \
    cluster=prod-southcentralus-hpe-3 \
    priority=team-low
```
NOTE: evaluation script doesn't currently work, needs more investigation...

The training/eval is run within a `tmux` background session, so if you'd like to view logs in the real-time (before the cluster is self-deleted), you can SSH into the pod and run:
```
tmux attach -t 0
```

## autostart + brix queues
I had high hopes for using this feature together with brix queues (see: `b --help | grep queue`), but I couldn't get it to work, i.e., the peashooters don't seem get scheduled correctly. Needs more investigation.
