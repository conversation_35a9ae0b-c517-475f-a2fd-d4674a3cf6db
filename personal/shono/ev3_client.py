import argparse
import async<PERSON>

import tiktoken
from multimodal_token.toklist import Tok<PERSON><PERSON>
from twapi.engine import batcher_requestor


def get_prompt(args):
    """Get the prompt from command line arguments or file."""
    if args.prompt:
        return args.prompt
    elif args.prompt_file:
        try:
            with open(args.prompt_file, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except FileNotFoundError:
            print(f"Error: Prompt file '{args.prompt_file}' not found.")
            return None
        except Exception as e:
            print(f"Error reading prompt file: {e}")
            return None
    else:
        return None


async def main():
    parser = argparse.ArgumentParser(description="EV3 Client for batch processing.")
    parser.add_argument("--host", type=str, default="localhost", help="Host address for the EV3 server.")
    parser.add_argument('--port', type=int, default=5201, help="Port number for the EV3 server.")
    parser.add_argument("--prompt", type=str, help="The prompt to send to the model.")
    parser.add_argument("--prompt-file", type=str, help="Path to a file containing the prompt.")
    parser.add_argument("--max-tokens", type=int, default=100, help="Maximum number of tokens to generate.")
    parser.add_argument("--n", type=int, default=1, help="Number of completions to generate.")
    parser.add_argument("--model-name", type=str, default="model", help="Name of the model to use.")
    args = parser.parse_args()

    # Get the prompt from either command line argument or file
    prompt_text = get_prompt(args)
    if not prompt_text:
        print("Error: Please provide a prompt using --prompt or --prompt-file")
        return

    host = f"{args.host}:{args.port}"
    print(f"Connecting to EV3 server at {host}")
    print(f"Using prompt: {prompt_text[:100]}{'...' if len(prompt_text) > 100 else ''}")

    requestor = batcher_requestor.BatcherRequestor.from_hosts(
        hosts=[host],
        min_replicas=1,
        pipereplica_group_id="default",
        default_mode="threaded",
        ev3=True,
        model_name=args.model_name
    )

    kwargs = {'max_tokens': args.max_tokens, 'n': args.n}
    encoder = tiktoken.get_encoding('orion_200k_mmgen')
    tokens = encoder.encode(prompt_text)
    toklist = TokList.from_ints(tokens)

    response = await asyncio.wrap_future(requestor.create_completion(prompt=[toklist], **kwargs))
    print("\nResponse received:")
    print(f"Full response: {response}")

    if 'choices' in response and len(response['choices']) > 0:
        for i, choice in enumerate(response['choices']):
            print(f"\n--- Choice {i+1} ---")
            if 'sample_tokens' in choice:
                sample_tokens = choice['sample_tokens']
                print(f"Sample tokens: {sample_tokens}")
                decoded_text = encoder.decode(sample_tokens)
                print(f"Decoded text: {decoded_text}")
            else:
                print("No sample_tokens found in choice")
    else:
        print("No choices found in response")


if __name__ == "__main__":
    asyncio.run(main())
