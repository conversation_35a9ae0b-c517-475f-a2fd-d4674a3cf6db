#!/usr/bin/env python3
"""
Convert JSONL OpenTelemetry data to Chrome tracing format.
Processes spans and metrics data.
"""
import collections
import json
import argparse
import sys
from typing import Dict, List, Any, Optional
from pathlib import Path


class OTelToChromeTracingConverter:
    """Converts OpenTelemetry JSONL data to Chrome tracing format."""
    
    def __init__(self):
        self.spans_events: List[Dict[str, Any]] = []
        self.metrics_events: List[Dict[str, Any]] = []
        self.metrics_descriptions: Dict[str, str] = {}
        self.process_names: Dict[str, str] = {}
        self.thread_names: Dict[str, str] = {}
        self.trace_id_to_process_id: Dict[str, int] = {}
        self.scope_to_process_id: Dict[str, int] = {}
        self.next_process_id: int = 1
        # Thread ID mappings per process
        self.process_thread_mappings: Dict[int, Dict[str, int]] = {}
        self.process_next_thread_id: Dict[int, int] = {}
    
    def convert_timestamp_to_microseconds(self, timestamp_ns: int) -> int:
        """Convert nanosecond timestamp to microseconds for Chrome tracing."""
        return timestamp_ns // 1000
    
    def convert_duration_to_microseconds(self, duration_ns: int) -> int:
        """Convert nanosecond duration to microseconds for Chrome tracing."""
        return duration_ns // 1000
    
    def get_process_id_for_trace(self, trace_id: str) -> int:
        """Get or assign a process ID for a trace ID."""
        if trace_id not in self.trace_id_to_process_id:
            self.trace_id_to_process_id[trace_id] = self.next_process_id
            self.next_process_id += 1
        return self.trace_id_to_process_id[trace_id]
    
    def get_process_id_for_scope(self, scope_name: str, scope_version: str = "") -> int:
        """Get or assign a process ID for a metrics scope."""
        scope_key = f"{scope_name}:{scope_version}" if scope_version else scope_name
        if scope_key not in self.scope_to_process_id:
            self.scope_to_process_id[scope_key] = self.next_process_id
            self.next_process_id += 1
        return self.scope_to_process_id[scope_key]
    
    def extract_span_kind(self, span: Dict[str, Any]) -> str:
        """Extract and convert span kind to Chrome tracing category."""
        kind = span.get('kind', 'SPAN_KIND_UNSPECIFIED')
        kind_mapping = {
            'SPAN_KIND_INTERNAL': 'internal',
            'SPAN_KIND_SERVER': 'server',
            'SPAN_KIND_CLIENT': 'client',
            'SPAN_KIND_PRODUCER': 'producer',
            'SPAN_KIND_CONSUMER': 'consumer',
            'SPAN_KIND_UNSPECIFIED': 'unspecified'
        }
        return kind_mapping.get(kind, 'unknown')
    
    def extract_attributes(self, span: Dict[str, Any]) -> Dict[str, Any]:
        """Extract attributes from span for Chrome tracing args."""
        attributes = span.get('attributes', {})
        args = {}
        
        # Add span attributes
        for attr in attributes:
            key = attr.get('key', '')
            value = attr.get('value', {})
            
            # Handle different value types
            if 'stringValue' in value:
                args[key] = value['stringValue']
            elif 'intValue' in value:
                args[key] = int(value['intValue'])
            elif 'doubleValue' in value:
                args[key] = float(value['doubleValue'])
            elif 'boolValue' in value:
                args[key] = value['boolValue']
            elif 'arrayValue' in value:
                args[key] = str(value['arrayValue'])
            elif 'kvlistValue' in value:
                args[key] = str(value['kvlistValue'])
        
        return args
    
    def extract_events(self, span: Dict[str, Any], trace_id: str) -> List[Dict[str, Any]]:
        """Extract events from span and convert to Chrome tracing instant events."""
        events = []
        span_events = span.get('events', [])
        
        for event in span_events:
            timestamp_ns = int(event.get('timeUnixNano', 0))
            if timestamp_ns == 0:
                continue
            
            # Get process ID for this trace
            process_id = self.get_process_id_for_trace(trace_id)
            
            # Get integer thread ID for this span
            span_id = span.get('spanId', 'unknown_thread')
            thread_id = self.get_thread_id_for_process(process_id, span_id)
            
            # Create instant event for span event
            chrome_event = {
                'name': event.get('name', 'span_event'),
                'cat': 'span_event',
                'ph': 'i',  # Instant event
                'ts': self.convert_timestamp_to_microseconds(timestamp_ns),
                'pid': process_id,
                'tid': thread_id,
                's': 't',  # Thread scope
                'args': {}
            }
            
            # Add event attributes
            event_attributes = event.get('attributes', [])
            for attr in event_attributes:
                key = attr.get('key', '')
                value = attr.get('value', {})
                
                if 'stringValue' in value:
                    chrome_event['args'][key] = value['stringValue']
                elif 'intValue' in value:
                    chrome_event['args'][key] = int(value['intValue'])
                elif 'doubleValue' in value:
                    chrome_event['args'][key] = float(value['doubleValue'])
                elif 'boolValue' in value:
                    chrome_event['args'][key] = value['boolValue']
            
            events.append(chrome_event)
        
        return events
    
    def convert_span_to_chrome_event(self, span: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Convert a single OpenTelemetry span to Chrome tracing event."""
        try:
            start_time_ns = int(span.get('startTimeUnixNano', 0))
            end_time_ns = int(span.get('endTimeUnixNano', 0))
            
            if start_time_ns == 0:
                return None
            
            # Calculate duration
            if end_time_ns > start_time_ns:
                duration_ns = end_time_ns - start_time_ns
            else:
                duration_ns = 0
            
            # Get trace ID and assign process ID
            trace_id = span.get('traceId', '')
            process_id = self.get_process_id_for_trace(trace_id)
            
            # Get integer thread ID for this span
            span_id = span.get('spanId', 'unknown_thread')
            thread_id = self.get_thread_id_for_process(process_id, span_id)
            
            # Create Chrome tracing event
            chrome_event = {
                'name': span.get('name', 'unnamed_span'),
                'cat': self.extract_span_kind(span),
                'ph': 'X',  # Complete event (duration event)
                'ts': self.convert_timestamp_to_microseconds(start_time_ns),
                'dur': self.convert_duration_to_microseconds(duration_ns),
                'pid': process_id,
                'tid': thread_id,
                'args': self.extract_attributes(span)
            }
            
            # Add trace and span IDs
            chrome_event['args']['trace_id'] = span.get('traceId', '')
            chrome_event['args']['span_id'] = span.get('spanId', '')
            chrome_event['args']['parent_span_id'] = span.get('parentSpanId', '')
            
            # Add status if present
            status = span.get('status', {})
            if status:
                chrome_event['args']['status_code'] = status.get('code', 'UNSET')
                if 'message' in status:
                    chrome_event['args']['status_message'] = status['message']
            
            # Store process and thread names for metadata
            self.process_names[process_id] = f"Trace {process_id} ({trace_id[:8]}...)"
            self.thread_names[f"{process_id}:{thread_id}"] = span_id
            
            return chrome_event
            
        except (ValueError, KeyError) as e:
            print(f"Error converting span: {e}", file=sys.stderr)
            return None

    def process_jsonl_line(self, line: str, metrics_only: bool, traces_only: bool) -> None:
        """Process a single JSONL line."""
        try:
            data = json.loads(line.strip())
            
            # Process resource spans
            if 'resourceSpans' in data and not metrics_only:
                for resource_span in data['resourceSpans']:
                    scope_spans = resource_span.get('scopeSpans', [])
                    
                    for scope_span in scope_spans:
                        spans = scope_span.get('spans', [])
                        
                        for span in spans:
                            # Get trace ID
                            trace_id = span.get('traceId', '')
                            
                            # Convert span to Chrome event
                            chrome_event = self.convert_span_to_chrome_event(span)
                            if chrome_event:
                                self.spans_events.append(chrome_event)

                            # Add span events as instant events
                            span_events = self.extract_events(span, trace_id)
                            self.spans_events.extend(span_events)

            # Process resource metrics
            if 'resourceMetrics' in data and not traces_only:
                for resource_metric in data['resourceMetrics']:
                    scope_metrics = resource_metric.get('scopeMetrics', [])
                    
                    for scope_metric in scope_metrics:
                        scope = scope_metric.get('scope', {})
                        scope_name = scope.get('name', 'unknown_scope')
                        scope_version = scope.get('version', '')
                        
                        metrics = scope_metric.get('metrics', [])
                        
                        for metric in metrics:
                            # Convert metric to Chrome events
                            metric_events = self.convert_metric_to_chrome_event(metric, scope_name, scope_version)
                            self.metrics_events.extend(metric_events)

        except json.JSONDecodeError as e:
            print(f"Error parsing JSON line: {e}", file=sys.stderr)
        except Exception as e:
            print(f"Error processing line: {e}", file=sys.stderr)
    
    def extract_metric_attributes(self, data_point: Dict[str, Any]) -> Dict[str, Any]:
        """Extract attributes from metric data point for Chrome tracing args."""
        attributes = data_point.get('attributes', [])
        args = {}
        
        for attr in attributes:
            key = attr.get('key', '')
            value = attr.get('value', {})
            
            # Handle different value types
            if 'stringValue' in value:
                args[key] = value['stringValue']
            elif 'intValue' in value:
                args[key] = int(value['intValue'])
            elif 'doubleValue' in value:
                args[key] = float(value['doubleValue'])
            elif 'boolValue' in value:
                args[key] = value['boolValue']
            elif 'arrayValue' in value:
                args[key] = str(value['arrayValue'])
            elif 'kvlistValue' in value:
                args[key] = str(value['kvlistValue'])
        
        return args
    
    def convert_metric_to_chrome_event(self, metric: Dict[str, Any], scope_name: str, scope_version: str = "") -> List[Dict[str, Any]]:
        """Convert a single OpenTelemetry metric to Chrome tracing events."""
        events = []
        
        try:
            metric_name = metric.get('name', 'unnamed_metric')
            metric_description = metric.get('description', '')
            metric_unit = metric.get('unit', '')
            if metric_name not in self.metrics_descriptions:
                self.metrics_descriptions[metric_name] = metric_description

            # Get process ID for this scope
            process_id = self.get_process_id_for_scope(scope_name, scope_version)
            
            # Store process name for metadata
            scope_key = f"{scope_name}:{scope_version}" if scope_version else scope_name
            self.process_names[process_id] = f"Metrics: {scope_key}"
            
            # Handle different metric types
            data_points = []
            metric_type = ""
            
            if 'gauge' in metric:
                data_points = metric['gauge'].get('dataPoints', [])
                metric_type = "gauge"
            elif 'sum' in metric:
                data_points = metric['sum'].get('dataPoints', [])
                metric_type = "sum"
            elif 'histogram' in metric:
                data_points = metric['histogram'].get('dataPoints', [])
                metric_type = "histogram"
            elif 'exponentialHistogram' in metric:
                data_points = metric['exponentialHistogram'].get('dataPoints', [])
                metric_type = "exponentialHistogram"
            elif 'summary' in metric:
                data_points = metric['summary'].get('dataPoints', [])
                metric_type = "summary"
            
            # Convert each data point to a Chrome tracing event
            for i, data_point in enumerate(data_points):
                timestamp_ns = int(data_point.get('timeUnixNano', 0))
                if timestamp_ns == 0:
                    continue
                
                # Create thread ID from metric name and attributes
                attributes = self.extract_metric_attributes(data_point)
                attr_string = "_".join(f"{k}={v}" for k, v in sorted(attributes.items()))
                thread_key = f"{metric_name}_{attr_string}" if attr_string else metric_name
                
                # Get integer thread ID for this metric thread
                thread_id = self.get_thread_id_for_process(process_id, thread_key)
                
                # Create more readable thread name for metadata
                readable_thread_name = f"{metric_name}"
                if attributes:
                    key_attrs = []
                    for k, v in sorted(attributes.items()):
                        # Truncate long values for readability
                        str_v = str(v)
                        if len(str_v) > 20:
                            str_v = str_v[:17] + "..."
                        key_attrs.append(f"{k}={str_v}")
                    readable_thread_name += f" ({', '.join(key_attrs)})"
                
                # Create Chrome tracing event
                chrome_event = {
                    'name': metric_name,
                    'cat': f'metric_{metric_type}',
                    'ph': 'C',  # Counter event for metrics
                    'ts': self.convert_timestamp_to_microseconds(timestamp_ns),
                    'pid': process_id,
                    'args': {
                        #'metric_type': metric_type,
                        #'description': metric_description,
                        #'unit': metric_unit,
                        **attributes
                    }
                }
                
                # Add metric value(s)
                if metric_type == "gauge" or metric_type == "sum":
                    if 'asDouble' in data_point:
                        chrome_event['args']['value'] = data_point['asDouble']
                    elif 'asInt' in data_point:
                        chrome_event['args']['value'] = int(data_point['asInt'])
                elif metric_type == "histogram":
                    count = int(data_point.get('count', 0))
                    sum = float(data_point.get('sum', 0))
                    chrome_event['args']['cnt'] = count
                    # chrome_event['args']['sum'] = sum
                    chrome_event['args']['avg'] = sum / count if count > 0 else 0
                    chrome_event['args']['max'] = data_point.get('max', 0)
                    # bucketCounts, explicitBounds are optional
                elif metric_type == "summary":
                    chrome_event['args']['cnt'] = data_point.get('count', 0)
                    chrome_event['args']['sum'] = data_point.get('sum', 0)
                    if 'quantileValues' in data_point:
                        chrome_event['args']['quantile_values'] = data_point['quantileValues']
                
                # Store thread name for metadata
                self.thread_names[f"{process_id}:{thread_id}"] = readable_thread_name
                
                events.append(chrome_event)
                
        except Exception as e:
            print(f"Error converting metric {metric.get('name', 'unknown')}: {e}", file=sys.stderr)
        
        return events
    
    def optimize_thread_names(self) -> None:
        """
        Analyze all thread names to find common prefixes and remove them.
        Add the shared prefix to the process name to reduce redundancy.
        """
        # Group thread names by process ID
        process_threads = {}
        for thread_key, thread_name in self.thread_names.items():
            process_id_str, thread_id_str = thread_key.split(':', 1)
            process_id = int(process_id_str)
            thread_id = int(thread_id_str)  # Now thread_id is an integer
            
            if process_id not in process_threads:
                process_threads[process_id] = []
            process_threads[process_id].append((thread_key, thread_name, thread_id))
        
        # Process each process group separately
        for process_id, threads in process_threads.items():
            if len(threads) <= 1:
                continue  # Skip if only one thread
                
            thread_names = [thread_name for _, thread_name, _ in threads]
            
            # Find common prefix among all thread names in this process
            common_prefix = self._find_common_prefix(thread_names)
            
            # Only proceed if we found a meaningful common prefix (at least 3 characters)
            if len(common_prefix) >= 3:
                # Remove common prefix from thread names
                updated_threads = []
                for thread_key, thread_name, thread_id in threads:
                    if thread_name.startswith(common_prefix):
                        # Remove the prefix and any trailing separator characters
                        new_name = thread_name[len(common_prefix):].lstrip('_-. ')
                        if not new_name:  # If nothing left, use a default
                            new_name = "main"
                        self.thread_names[thread_key] = new_name
                        updated_threads.append(new_name)
                
                # Update process name to include the common prefix
                current_process_name = self.process_names.get(process_id, f"Process {process_id}")
                # Clean up the common prefix for process name
                clean_prefix = common_prefix.rstrip('_-. ')
                self.process_names[process_id] = f"{current_process_name} [{clean_prefix}]"
    
    def _find_common_prefix(self, strings: List[str]) -> str:
        """Find the longest common prefix among a list of strings."""
        if not strings:
            return ""
        
        if len(strings) == 1:
            return ""
        
        # Start with the first string as the potential prefix
        prefix = strings[0]
        
        # Compare with each subsequent string
        for string in strings[1:]:
            # Reduce prefix until it matches the start of current string
            while prefix and not string.startswith(prefix):
                prefix = prefix[:-1]
            
            if not prefix:
                break
        
        return prefix

    def generate_metadata_events(self) -> List[Dict[str, Any]]:
        """Generate metadata events for Chrome tracing (process names, thread names)."""
        metadata_events = []
        
        # Generate process name metadata events
        for process_id, process_name in self.process_names.items():
            metadata_event = {
                'name': 'process_name',
                'ph': 'M',  # Metadata event
                'pid': process_id,
                'args': {
                    'name': process_name
                }
            }
            metadata_events.append(metadata_event)
        
        # Generate thread name metadata events
        for thread_key, thread_name in self.thread_names.items():
            process_id_str, thread_id_str = thread_key.split(':', 1)
            thread_id = int(thread_id_str)  # Convert to integer
            metadata_event = {
                'name': 'thread_name',
                'ph': 'M',  # Metadata event
                'pid': int(process_id_str),
                'tid': thread_id,
                'args': {
                    'name': thread_name
                }
            }
            metadata_events.append(metadata_event)
        
        return metadata_events

    def convert_file(self, input_file: Path, output_file: Path, metrics_only: bool, traces_only: bool) -> None:
        """Convert JSONL OpenTelemetry file to Chrome tracing format."""
        print(f"Converting {input_file} to {output_file}")
        
        # Process input file
        with open(input_file, 'r', encoding='utf-8') as f:
            for line_no, line in enumerate(f, 1):
                if line.strip():
                    self.process_jsonl_line(line, metrics_only, traces_only)
                
                if line_no % 1000 == 0:
                    print(f"Processed {line_no} lines...")
        
        # Print trace ID to process ID mapping
        if self.trace_id_to_process_id:
            print(f"Total unique traces: {len(self.trace_id_to_process_id)}")
        
        # Optimize thread names by removing common prefixes
        self.optimize_thread_names()
        
        # Optimize metric names by removing common prefixes
        self.optimize_metric_names()
        
        # Generate metadata events for process and thread names
        metadata_events = self.generate_metadata_events()
        
        # Combine all events
        all_events = metadata_events + self.metrics_events + self.spans_events
        
        # Sort events by timestamp for better visualization (metadata events don't have timestamps)
        #all_events.sort(key=lambda x: x.get('ts', 0))
        
        # Write Chrome tracing format
        chrome_trace = {
            'traceEvents': all_events,
            'displayTimeUnit': 'ms',
            'otherData': {
                'version': '1.0',
                'converter': 'otel_to_chrome_tracing'
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(chrome_trace, f, indent=2)
        
        print("Metrics descriptions:")
        for name, description in self.metrics_descriptions.items():
            print(f"{name}: {description}")

        print(f"Converted {len(all_events)} events to {output_file}")

    def get_thread_id_for_process(self, process_id: int, thread_key: str) -> int:
        """Get or assign an integer thread ID for a thread key within a specific process."""
        if process_id not in self.process_thread_mappings:
            self.process_thread_mappings[process_id] = {}
            self.process_next_thread_id[process_id] = 1
        
        if thread_key not in self.process_thread_mappings[process_id]:
            self.process_thread_mappings[process_id][thread_key] = self.process_next_thread_id[process_id]
            self.process_next_thread_id[process_id] += 1
        
        return self.process_thread_mappings[process_id][thread_key]

    def optimize_metric_names(self) -> None:
        """
        Analyze all metric names to find common prefixes and remove them.
        This makes metric names more readable in the Chrome tracing viewer.
        """
        # Collect all unique metric names from the metrics events
        metric_names_per_process = collections.defaultdict(set)
        for event in self.metrics_events:
            if event.get('name'):
                process_id = event.get('pid', 0)
                metric_names_per_process[process_id].add(event['name'])

        for process_id, metric_names in metric_names_per_process.items():
            if len(metric_names) <= 1:
                continue  # Skip if only one metric
        
            metric_names_list = list(metric_names)
        
            # Find common prefix among all metric names
            common_prefix = self._find_common_prefix(metric_names_list)
        
            # Only proceed if we found a meaningful common prefix (at least 3 characters)
            if len(common_prefix) >= 3:
                # Create mapping of old names to new names
                name_mapping = {}
                for name in metric_names_list:
                    if name.startswith(common_prefix):
                        # Remove the prefix and any trailing separator characters
                        new_name = name[len(common_prefix):].lstrip('_-. ')
                        if not new_name:  # If nothing left, use original name
                            new_name = name
                        name_mapping[name] = new_name
            
                # Update all metric events with optimized names
                for event in self.metrics_events:
                    if event.get('pid') == process_id:
                        old_name = event.get('name')
                        if old_name in name_mapping:
                            event['name'] = name_mapping[old_name]
            
                # Update process name to include the common prefix
                current_process_name = self.process_names.get(process_id, f"Process {process_id}")
                # Clean up the common prefix for process name
                clean_prefix = common_prefix.rstrip('_-. ')
                self.process_names[process_id] = f"{current_process_name} [{clean_prefix}]" 

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description='Convert JSONL OpenTelemetry data to Chrome tracing format'
    )
    parser.add_argument(
        'input_file',
        type=Path,
        help='Input JSONL file containing OpenTelemetry data'
    )
    parser.add_argument(
        'output_file',
        type=Path,
        help='Output JSON file in Chrome tracing format'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )

    parser.add_argument(
        '--metrics_only', '-m',
        action='store_true',
        help='Only process metrics, ignore spans'
    )

    parser.add_argument(
        '--traces_only', '-t',
        action='store_true',
        help='Only process spans, ignore metrics'
    )

    args = parser.parse_args()
    
    if not args.input_file.exists():
        print(f"Error: Input file {args.input_file} does not exist", file=sys.stderr)
        sys.exit(1)
    
    # Create output directory if it doesn't exist
    args.output_file.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        converter = OTelToChromeTracingConverter()
        converter.convert_file(args.input_file, args.output_file, args.metrics_only, args.traces_only)
        print("Conversion completed successfully!")
        print(f"You can now open {args.output_file} in Chrome by navigating to chrome://tracing/")
        
    except Exception as e:
        print(f"Error during conversion: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()