"""Extract Peashooter tracing from kubectl logs of a pod."""
import argparse
import collections
import json
import logging
import pathlib
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


def main():
    parser = argparse.ArgumentParser(description="Extract Peashooter telemetry data from pod logs")
    parser.add_argument('log_file', type=str, help='Path to the log file to extract telemetry data from')
    parser.add_argument('--output', '-o', type=str, help='Output file for the telemetry trace events')

    args = parser.parse_args()

    log_filepath = pathlib.Path(args.log_file)
    if not log_filepath.exists():
        logger.error(f"Log file {log_filepath} does not exist.")
        return

    output_filepath = pathlib.Path(args.output or f"{log_filepath.stem}_telemetry_trace.json")
    logger.info(f"Output file will be: {output_filepath}")
    logger.info(f"Processing log file: {log_filepath}")

    logger.info(f"Extracting telemetry trace events from {log_filepath}")
    trace_events = extract_trace_events_from_logs(log_filepath)
    logger.info(f"Extracted {len(trace_events)} trace events.")
    with open(output_filepath, 'w') as f:
        json.dump({"traceEvents": trace_events}, f, indent=2)


def extract_trace_events_from_logs(log_filepath):
    sample_id_mapping = SampleIdMapping()
    handlers = {'appberry.graders.cua_rubric.core_grader_logic': [CuaMessageLogHandler(sample_id_mapping)],
                'peashooter.sampling.tracker': [RequestSpanLogHandler(sample_id_mapping)]}

    # This is a very naive implementation, assuming the log output is in JSON format.
    # You might want to improve this based on your actual log format.
    trace_events = []
    with open(log_filepath, 'r') as f:
        for line in f:
            try:
                # Try to find JSON content in the line, ignoring any prefix
                json_start = line.find('{')
                if json_start != -1:
                    json_content = line[json_start:]
                    event = json.loads(json_content)
                    if 'component' in event and event['component'] in handlers:
                        for handler in handlers[event['component']]:
                            result = handler(event)
                            if result is not None:
                                trace_events.append(result)
            except json.JSONDecodeError:
                pass
    return trace_events


def read_timestamp(time_str):
    """Read time from a string in the format 'YYYY-MM-DDTHH:MM:SS.ssssssZ' or 'YYYY-MM-DDTHH:MM:SSZ'."""
    try:
        dt = datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%S.%fZ")
    except ValueError:
        dt = datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%SZ")
    return dt.replace(tzinfo=timezone.utc).timestamp() * 1_000_000  # Convert to microseconds


class SampleIdMapping:
    def __init__(self):
        self._sample_id_mapping = {}

    def __getitem__(self, sample_id):
        if sample_id not in self._sample_id_mapping:
            # Create a new sample ID mapping
            self._sample_id_mapping[sample_id] = len(self._sample_id_mapping)
        return self._sample_id_mapping[sample_id]


class CuaMessageLogHandler:
    def __init__(self, sample_id_mapping):
        self._sample_id_mapping = sample_id_mapping
        self._rubric_hash_per_sample_id_mapping = collections.defaultdict(dict)
        self._prev_rollout_tokens = collections.defaultdict(int)

    def __call__(self, item):
        if item['log'] == 'Got message in CUA rollout':
            timestamp = read_timestamp(item['time'])
            duration = item['elapsed_this_message'] * 1_000_000  # Convert seconds to microseconds

            author = item['author']
            if author['role'] == 'assistant':
                name = 'sample'
            elif author['role'] == 'tool':
                name = author['name']

            rubric_chunk_hash = item['grade_functionality']['rubric_chunk_hash']
            if rubric_chunk_hash not in self._rubric_hash_per_sample_id_mapping[item['sample_id']]:
                self._rubric_hash_per_sample_id_mapping[item['sample_id']][item['grade_functionality']['rubric_chunk_hash']] = len(self._rubric_hash_per_sample_id_mapping[item['sample_id']])

            pid = self._sample_id_mapping[item['sample_id']]
            tid = self._rubric_hash_per_sample_id_mapping[item['sample_id']][rubric_chunk_hash]
            num_rollout_tokens = item['num_rollout_tokens']
            delta_rollout_tokens = num_rollout_tokens - self._prev_rollout_tokens[(pid, tid)]
            self._prev_rollout_tokens[(pid, tid)] = num_rollout_tokens

            return {
                'name': name,
                'ph': 'X',
                'ts': timestamp - duration,
                'pid': pid,
                'tid': tid,
                'dur': duration,
                'args': {'sample_id': item['sample_id'], 'content': item['content'], 'num_rollout_tokens': num_rollout_tokens, 'delta_rollout_tokens': delta_rollout_tokens}
            }


class RequestSpanLogHandler:
    def __init__(self, sample_id_mapping):
        self._sample_id_mapping = sample_id_mapping

    def __call__(self, item):
        if item['log'] == 'Finished request state span':
            duration = item['duration_ns'] / 1_000  # Convert nanoseconds to microseconds
            timestamp = read_timestamp(item['time'])
            return {
                'name': item['state'],
                'ph': 'X',
                'ts': timestamp - duration,
                'dur': duration,
                'pid': self._sample_id_mapping[item['sample_id']],
                'tid': 0,
                'args': {
                    'sample_id': item['sample_id']
                }
            }


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    main()
