import argparse
import json
import logging

from peashooter.telemetry_store.telemetry_store_reader import TelemetryStoreReader
from peashooter.trackers.tracker_utils import get_tracker_redis_storage

logger = logging.getLogger(__name__)

def main():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    parser = argparse.ArgumentParser(description="Export Peashooter telemetry data")
    parser.add_argument('experiment_name', type=str, help='Name of the experiment to export telemetry data for')
    parser.add_argument('--output', '-o', type=str, help='Output file for the telemetry trace events')
    args = parser.parse_args()

    reader = TelemetryStoreReader(experiment_name=args.experiment_name, storage=get_tracker_redis_storage())
    redis_storage = reader._storage

    result = redis_storage.search_entity_ids(reader._samples_storage_prefix)
    sample_entity_ids = result.entity_ids
    logger.info(f"Found {len(sample_entity_ids)} entity IDs in the telemetry store for experiment '{args.experiment_name}'.")

    histories = [reader.get_sample_history(entity_id) for entity_id in sample_entity_ids]
    trace_events = [convert_history_to_trace_events(history, index) for index, history in enumerate(histories)]
    logger.info(f"Converted {len(histories)} histories to trace events.")

    trace_events = [event for sublist in trace_events for event in sublist]
    logger.info(f"Total trace events: {len(trace_events)}")

    output_filename = args.output if args.output else f"{args.experiment_name}_telemetry_trace.json"
    with open(output_filename, 'w') as f:
        json.dump({"traceEvents": trace_events}, f, indent=2)


def convert_history_to_trace_events(history, index):
    # Convert the sample history to trace events.
    # We use Chrome's trace event format.
    # The sample history is a list of ditionaries, each containing a 'active_state' and 'state_ts' key.
    # Available active_state: ['in_queue', 'worker_accepted', 'sample', 'tool', 'grade', 'invalid', 'unhandled_error', 'complete']

    state_set = {event['active_state'] for event in history}
    KNOWN_STATES = {'in_queue', 'worker_accepted', 'sample', 'sample_more', 'tool', 'grade', 'invalid', 'complete', 'unhandled_error'}
    if state_set - KNOWN_STATES:
        logger.warning(f"Unexpected active states found in history for index {index}: {state_set - KNOWN_STATES}")
        return []

    trace_events = []
    if len(history) < 2:
        # This happens when the sample is in the queue and has not been accepted by a worker yet.
        return trace_events

    start_time = history[0]['state_ts'] * 1000000
    end_time = history[-1]['state_ts'] * 1000000
    trace_events.append({
        'name': 'request',
        'ph': 'X',
        'ts': start_time,
        'dur': end_time - start_time,
        'pid': index,
        'tid': 0,
        'args': {
            'datapoint_id': history[0]['datapoint_id'],
            'final_state': history[-1]['active_state'],
        }
    })

    worker_accepted_event = next((event for event in history if event['active_state'] == 'worker_accepted'), None)
    if not worker_accepted_event:
        logger.warning(f"No 'worker_accepted' event found in history for index {index}.")
        return trace_events

    trace_events.append({
        'name': 'worker',
        'ph': 'X',
        'ts': worker_accepted_event['state_ts'] * 1000000,
        'dur': end_time - worker_accepted_event['state_ts'] * 1000000,
        'pid': index,
        'tid': 0,
        'args': {
            'worker': worker_accepted_event['worker'],
        }
    })

    for i in range(len(history) - 1):
        duration = history[i + 1]['state_ts'] * 1000000 - history[i]['state_ts'] * 1000000
        if history[i]['active_state'] in ('sample', 'sample_more'):
            trace_events.append({
                'name': 'sample',
                'ph': 'X',
                'ts': history[i]['state_ts'] * 1000000,
                'dur': duration,
                'pid': index,
                'tid': 0,
            })

        elif history[i]['active_state'] == 'tool':
            trace_events.append({
                'name': 'tool',
                'ph': 'X',
                'ts': history[i]['state_ts'] * 1000000,
                'dur': duration,
                'pid': index,
                'tid': 0,
                'args': {
                    'tool_names': history[i]['tool_names'],
                }
            })
        elif history[i]['active_state'] == 'grade':
            if history[i]['grader_name'] == 'multi-stage-grader':
                trace_events.append({
                    'name': history[i]['grader_name'],
                    'ph': 'X',
                    'ts': history[i]['state_ts'] * 1000000,
                    'dur': end_time - history[i]['state_ts'] * 1000000,
                    'pid': index,
                    'tid': 0,
                })
            else:
                trace_events.append({
                    'name': history[i]['grader_name'],
                    'ph': 'X',
                    'ts': history[i]['state_ts'] * 1000000,
                    'dur': duration,
                    'pid': index,
                    'tid': 0,
                })

    for i in range(len(history)):
        if history[i]['active_state'] == 'invalid':
            trace_events.append({
                'name': 'invalid',
                'ph': 'I',
                'ts': history[i]['state_ts'] * 1000000,
                'pid': index,
                'tid': 0,
                'args': {
                    'error_traceback': history[i].get('error_traceback'),
                }
            })
        elif history[i]['active_state'] == 'unhandled_error':
            trace_events.append({
                'name': 'unhandled_error',
                'ph': 'I',
                'ts': history[i]['state_ts'] * 1000000,
                'pid': index,
                'tid': 0,
                'args': {
                    'error': history[i]['unhandled_error'],
                }
            })
    return trace_events

if __name__ == "__main__":
    main()
