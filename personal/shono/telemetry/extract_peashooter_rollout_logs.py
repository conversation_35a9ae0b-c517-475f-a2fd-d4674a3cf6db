import argparse
import json
import logging
import random
import typing

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

from peashooter.telemetry_store.telemetry_store_reader import TelemetryStoreReader
from peashooter.trackers.sample_tracker import SampleAttributes
from peashooter.trackers.tracker_utils import get_tracker_redis_storage

logger = logging.getLogger(__name__)

def main():
    parser = argparse.ArgumentParser(description="Export history of a single sample from Peashooter telemetry data")
    parser.add_argument('experiment_name', type=str, help='Name of the experiment to export sample history for')
    parser.add_argument('--output', '-o', type=str, help='Output file for the sample history')
    parser.add_argument('--entity-id', type=str, help='Specific entity ID to export. If not provided, a random one will be selected.')
    parser.add_argument('--seed', type=int, help='Random seed for reproducible sample selection')
    args = parser.parse_args()

    if args.seed is not None:
        random.seed(args.seed)
        logger.info(f"Set random seed to {args.seed}")

    reader = TelemetryStoreReader(experiment_name=args.experiment_name, storage=get_tracker_redis_storage())
    redis_storage = reader._storage

    result = redis_storage.search_entity_ids(reader._samples_storage_prefix)
    sample_entity_ids = result.entity_ids
    logger.info(f"Found {len(sample_entity_ids)} entity IDs in the telemetry store for experiment '{args.experiment_name}'.")

    if not sample_entity_ids:
        logger.error("No sample entity IDs found in the experiment.")
        return

    # Select entity ID
    if args.entity_id:
        if args.entity_id not in sample_entity_ids:
            logger.error(f"Specified entity ID '{args.entity_id}' not found in experiment '{args.experiment_name}'.")
            logger.info(f"Available entity IDs: {sample_entity_ids[:10]}...")  # Show first 10
            return
        selected_entity_id = args.entity_id
        logger.info(f"Using specified entity ID: {selected_entity_id}")
    else:
        selected_entity_id = random.choice(sample_entity_ids)
        logger.info(f"Randomly selected entity ID: {selected_entity_id}")

    # Get the sample history
    history = [record for record in reader._storage.get_history(reader._samples_storage_prefix, selected_entity_id)]
    for rec in history:
        attrs = typing.cast(SampleAttributes, rec.attributes)
        print(attrs)

    history = reader.get_sample_history(selected_entity_id)

    logger.info(f"Retrieved history with {len(history)} events for entity ID: {selected_entity_id}")

    # Prepare output data
    output_data = {
        "experiment_name": args.experiment_name,
        "entity_id": selected_entity_id,
        "total_samples_in_experiment": len(sample_entity_ids),
        "history_length": len(history),
        "history": history
    }

    # Add summary information
    if history:
        states = [event['active_state'] for event in history]
        output_data["summary"] = {
            "start_time": history[0]['state_ts'],
            "end_time": history[-1]['state_ts'],
            "duration_seconds": history[-1]['state_ts'] - history[0]['state_ts'],
            "final_state": history[-1]['active_state'],
            "states_sequence": states,
            "unique_states": list(set(states))
        }

        # Add datapoint ID if available
        if 'datapoint_id' in history[0]:
            output_data["datapoint_id"] = history[0]['datapoint_id']

        # Add worker information if available
        worker_events = [event for event in history if 'worker' in event]
        if worker_events:
            output_data["summary"]["worker"] = worker_events[0]['worker']

    # Write to output file
    output_filename = args.output if args.output else f"{args.experiment_name}_sample_{selected_entity_id}_history.json"
    with open(output_filename, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    logger.info(f"Sample history exported to: {output_filename}")

    # Print summary to console
    if history:
        print(f"\nSample Summary:")
        print(f"  Entity ID: {selected_entity_id}")
        print(f"  Datapoint ID: {output_data.get('datapoint_id', 'N/A')}")
        print(f"  Duration: {output_data['summary']['duration_seconds']:.2f} seconds")
        print(f"  Final State: {output_data['summary']['final_state']}")
        print(f"  States: {' -> '.join(output_data['summary']['states_sequence'])}")
        if 'worker' in output_data['summary']:
            print(f"  Worker: {output_data['summary']['worker']}")

if __name__ == "__main__":
    main()