"""
Kubernetes Log Downloader with Resume Capability

This script downloads logs from a Kubernetes pod using kubectl logs.
If the download fails or is interrupted, it can resume from the last
successfully downloaded timestamp.
"""

import argparse
import os
import re
import subprocess
import sys
import time
from datetime import datetime


class KubeLogDownloader:
    def __init__(self, pod_name: str, namespace: str | None = None, container: str | None = None, output_file: str | None = None, since_time: str | None = None):
        self.pod_name = pod_name
        self.namespace = namespace
        self.container = container
        self.since_time = since_time
        self.output_file = output_file or self._generate_default_filename()
        self.timestamp_pattern = re.compile(r'^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z?)')

    def get_last_timestamp_from_file(self) -> str | None:
        """Extract the last timestamp from the existing log file."""
        if not os.path.exists(self.output_file):
            return None

        try:
            with open(self.output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Search from the end for the last valid timestamp
            for line in reversed(lines):
                match = self.timestamp_pattern.match(line.strip())
                if match:
                    timestamp = match.group(1)
                    print(f"Found last timestamp in file: {timestamp}")
                    return timestamp

        except Exception as e:
            print(f"Error reading existing log file: {e}")

        return None

    def build_kubectl_command(self, since_time: str | None = None) -> list:
        """Build the kubectl logs command with appropriate parameters."""
        cmd = ["kubectl", "logs", self.pod_name]

        if self.namespace:
            cmd.extend(["-n", self.namespace])

        if self.container:
            cmd.extend(["-c", self.container])

        if since_time:
            cmd.extend(["--since-time", since_time])

        # Always add timestamps to parse log timestamps
        cmd.append("--timestamps")

        return cmd

    def download_logs(self, since_time: str | None = None, append_mode: bool = False) -> tuple[bool, str]:
        """Download logs using kubectl and save to file."""
        cmd = self.build_kubectl_command(since_time)

        print(f"Executing: {' '.join(cmd)}")

        try:
            # Open file in appropriate mode
            mode = 'a' if append_mode else 'w'

            with open(self.output_file, mode, encoding='utf-8') as f:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )

                line_count = 0
                byte_count = 0
                last_timestamp = None
                start_time = time.time()

                # Read output line by line
                for line in process.stdout:
                    f.write(line)
                    f.flush()  # Ensure data is written immediately

                    # Extract timestamp from current line
                    match = self.timestamp_pattern.match(line.strip())
                    if match:
                        last_timestamp = match.group(1)

                    line_count += 1
                    byte_count += len(line.encode('utf-8'))

                    # Print progress every 1000 lines
                    if line_count % 1000 == 0:
                        elapsed_time = time.time() - start_time
                        if elapsed_time > 0:
                            lines_per_second = line_count / elapsed_time
                            bytes_per_second = byte_count / elapsed_time
                            print(f"Downloaded {line_count} lines, {byte_count:,} bytes, {last_timestamp} | "
                                  f"Speed: {lines_per_second:.1f} lines/s, {bytes_per_second:,.0f} bytes/s")
                        else:
                            print(f"Downloaded {line_count} lines, {byte_count:,} bytes, {last_timestamp}")
                # Wait for process to complete
                return_code = process.wait()
                stderr_output = process.stderr.read()

                if return_code == 0:
                    elapsed_time = time.time() - start_time
                    if elapsed_time > 0 and line_count > 0:
                        lines_per_second = line_count / elapsed_time
                        bytes_per_second = byte_count / elapsed_time
                        print(f"Successfully downloaded {line_count} lines ({byte_count:,} bytes) to {self.output_file}")
                        print(f"Final speed: {lines_per_second:.1f} lines/s, {bytes_per_second:,.0f} bytes/s")
                        print(f"Total time: {elapsed_time:.1f} seconds")
                    else:
                        print(f"Successfully downloaded {line_count} lines to {self.output_file}")
                    return True, last_timestamp
                else:
                    print(f"kubectl command failed with return code {return_code}")
                    if stderr_output:
                        print(f"Error output: {stderr_output}")
                    return False, last_timestamp

        except KeyboardInterrupt:
            print("\nDownload interrupted by user")
            return False, None
        except Exception as e:
            print(f"Error during download: {e}")
            return False, None

    def download_with_retry(self, max_retries: int = 3, retry_delay: int = 5):
        """Download logs with automatic retry on failure."""
        retry_count = 0

        while retry_count <= max_retries:
            if retry_count == 0:
                print(f"Starting initial download for pod '{self.pod_name}' in namespace '{self.namespace}'")
                since_time = None
                append_mode = False
            else:
                print(f"Retry attempt {retry_count}/{max_retries}")
                since_time = self.get_last_timestamp_from_file()
                if since_time:
                    print(f"Resuming from timestamp: {since_time}")
                    append_mode = True
                else:
                    print("No valid timestamp found, starting fresh download")
                    append_mode = False

            success, last_timestamp = self.download_logs(since_time, append_mode)

            if success:
                print("Download completed successfully!")
                return True
            else:
                retry_count += 1
                if retry_count <= max_retries:
                    print(f"Download failed. Waiting {retry_delay} seconds before retry...")
                    time.sleep(retry_delay)
                else:
                    print("Maximum retries reached. Download failed.")
                    return False

        return False

    def _generate_default_filename(self) -> str:
        """Generate default filename with pod name and optional since-time."""
        # Get current time for filename
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.pod_name}_logs_{current_time}"

        if self.since_time:
            # Convert timestamp to filename-safe format
            # Replace special characters that are not allowed in filenames
            timestamp_safe = self.since_time.replace(":", "-").replace(".", "-")
            filename += f"_since_{timestamp_safe}"

        return f"{filename}.txt"

def main():
    parser = argparse.ArgumentParser(description="Download Kubernetes pod logs with resume capability")
    parser.add_argument("pod_name", help="Name of the pod to download logs from")
    parser.add_argument("-n", "--namespace", help="Kubernetes namespace")
    parser.add_argument("-c", "--container", help="Container name (for multi-container pods)")
    parser.add_argument("-o", "--output", help="Output file path (default: {pod_name}_logs[_since_{timestamp}].txt)")
    parser.add_argument("--max-retries", type=int, default=5, help="Maximum number of retry attempts (default: 3)")
    parser.add_argument("--retry-delay", type=int, default=5, help="Delay between retries in seconds (default: 5)")
    parser.add_argument("--resume", action="store_true", help="Resume from existing log file")
    parser.add_argument("--since-time", help="Start downloading logs from this timestamp (RFC3339 format, e.g., 2023-01-01T10:00:00Z)")
    args = parser.parse_args()

    # Create downloader instance
    downloader = KubeLogDownloader(
        pod_name=args.pod_name,
        namespace=args.namespace,
        container=args.container,
        output_file=args.output,
        since_time=args.since_time
    )

    if args.resume:
        print("Resume mode: checking for existing log file...")
        last_timestamp = downloader.get_last_timestamp_from_file()
        if last_timestamp:
            success, _ = downloader.download_logs(since_time=last_timestamp, append_mode=True)
        else:
            print("No existing log file found or no valid timestamp. Starting fresh download.")
            success, _ = downloader.download_logs()
    elif args.since_time:
        print(f"Starting download from specified timestamp: {args.since_time}")
        success, _ = downloader.download_logs(since_time=args.since_time, append_mode=False)
    else:
        # Use retry mechanism
        success = downloader.download_with_retry(
            max_retries=args.max_retries,
            retry_delay=args.retry_delay
        )

    if success:
        print(f"Logs saved to: {downloader.output_file}")
        sys.exit(0)
    else:
        print("Failed to download logs")
        sys.exit(1)

if __name__ == "__main__":
    main()
