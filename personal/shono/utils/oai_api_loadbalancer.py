#!/usr/bin/env bash
# Launch vLLM servers on ports 8001–8008 inside tmux with venv + GPU pinning
# uvicorn oai_api_loadbalancer:app --host 0.0.0.0 --port 8000 --workers 4

MODEL="facebook/opt-125m"   # change this to your model
SESSION="vllm_cluster"
VENV_PATH="./venv/bin/activate"   # adjust path to your venv

# Create a new tmux session (detached)
tmux new-session -d -s $SESSION

for i in {0..7}; do
    PORT=$((8001 + i))
    GPU=$i
    CMD="source $VENV_PATH && CUDA_VISIBLE_DEVICES=$GPU vllm serve $MODEL --host 0.0.0.0 --port $PORT"
    
    if [ $i -eq 0 ]; then
        # First one in the first window
        tmux send-keys -t $SESSION "$CMD" C-m
    else
        # Create a new window per instance
        tmux new-window -t $SESSION -n "vllm-$PORT" "$CMD"
    fi

    echo "Launched vllm on port $PORT using GPU $GPU"
done

echo "✅ All vLLM servers launched in tmux session: $SESSION"
echo "Run: tmux attach -t $SESSION"

