#!/usr/bin/env python3
"""
SafeTensors Statistics Analyzer

This script loads safetensors files and computes comprehensive statistics
for each tensor including shape, dtype, memory usage, and numerical statistics.

Supports multiple input formats:
1. Single safetensors file: python get_safetensors_stats.py model.safetensors
2. Directory with safetensors files: python get_safetensors_stats.py /path/to/model/
3. Index file: python get_safetensors_stats.py model.safetensors.index.json

Examples:
  # Analyze a single file
  python get_safetensors_stats.py model.safetensors
  
  # Analyze all safetensors files in a directory
  python get_safetensors_stats.py /path/to/model_directory/
  
  # Analyze using index file (for sharded models)
  python get_safetensors_stats.py model.safetensors.index.json
  
  # Show only summary
  python get_safetensors_stats.py model.safetensors --summary-only
  
  # Limit analysis to first 100 tensors per file
  python get_safetensors_stats.py model.safetensors --max-tensors 100
  
  # Save results to JSON
  python get_safetensors_stats.py model.safetensors --output results.json
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
import glob

import torch
from safetensors import safe_open


def load_index_file(index_path: str) -> Dict[str, str]:
    """
    Load and parse a model.safetensors.index.json file.
    
    Args:
        index_path: Path to the index.json file
        
    Returns:
        Dictionary mapping tensor names to their corresponding safetensors files
    """
    with open(index_path, 'r') as f:
        index_data = json.load(f)
    
    if 'weight_map' in index_data:
        return index_data['weight_map']
    else:
        raise ValueError("Invalid index file format: missing 'weight_map' key")


def find_safetensors_files(path: str) -> List[str]:
    """
    Find all safetensors files in a directory or return the single file if path is a file.
    
    Args:
        path: Path to a directory or a single safetensors file
        
    Returns:
        List of safetensors file paths
    """
    path_obj = Path(path)
    
    if path_obj.is_file():
        if path_obj.suffix == '.safetensors':
            return [str(path_obj)]
        elif path_obj.name.endswith('.index.json'):
            # If it's an index file, find the corresponding safetensors files
            base_dir = path_obj.parent
            index_data = load_index_file(str(path_obj))
            safetensors_files = set(index_data.values())
            return [str(base_dir / filename) for filename in safetensors_files]
        else:
            raise ValueError(f"Unsupported file type: {path_obj}")
    
    elif path_obj.is_dir():
        # Look for safetensors files in the directory
        safetensors_files = list(path_obj.glob("*.safetensors"))
        if not safetensors_files:
            raise ValueError(f"No safetensors files found in directory: {path_obj}")
        return [str(f) for f in sorted(safetensors_files)]
    
    else:
        raise FileNotFoundError(f"Path not found: {path_obj}")


def analyze_multiple_safetensors_files(
    file_paths: List[str], 
    max_tensors: Optional[int] = None
) -> Dict[str, Any]:
    """
    Analyze multiple safetensors files and aggregate statistics.
    
    Args:
        file_paths: List of paths to safetensors files
        max_tensors: Maximum number of tensors to analyze per file
        
    Returns:
        Dictionary containing aggregated file metadata and tensor statistics
    """
    all_results = {
        "files": [],
        "total_files": len(file_paths),
        "total_tensor_count": 0,
        "tensors": [],
        "summary": {
            "total_parameters": 0,
            "total_memory_bytes": 0,
            "total_memory_mb": 0,
            "dtypes": {},
            "shapes_summary": {},
            "files_summary": {},
        }
    }
    
    for i, file_path in enumerate(file_paths):
        print(f"Processing file {i+1}/{len(file_paths)}: {file_path}")
        
        try:
            # Analyze individual file
            file_results = analyze_safetensors_file(file_path, max_tensors)
            
            # Add file info to results
            all_results["files"].append({
                "path": file_path,
                "tensor_count": file_results["tensor_count"],
                "file_size_mb": file_results["file_size_mb"],
                "memory_mb": file_results["summary"]["total_memory_mb"]
            })
            
            # Add tensors with file prefix
            file_name = Path(file_path).name
            for tensor in file_results["tensors"]:
                tensor_copy = tensor.copy()
                tensor_copy["file"] = file_name
                tensor_copy["full_name"] = f"{file_name}::{tensor['name']}"
                all_results["tensors"].append(tensor_copy)
            
            # Update aggregate statistics
            all_results["total_tensor_count"] += file_results["tensor_count"]
            all_results["summary"]["total_parameters"] += file_results["summary"]["total_parameters"]
            all_results["summary"]["total_memory_bytes"] += file_results["summary"]["total_memory_bytes"]
            
            # Aggregate dtypes
            for dtype, count in file_results["summary"]["dtypes"].items():
                if dtype in all_results["summary"]["dtypes"]:
                    all_results["summary"]["dtypes"][dtype] += count
                else:
                    all_results["summary"]["dtypes"][dtype] = count
            
            # Aggregate shapes
            for shape, count in file_results["summary"]["shapes_summary"].items():
                if shape in all_results["summary"]["shapes_summary"]:
                    all_results["summary"]["shapes_summary"][shape] += count
                else:
                    all_results["summary"]["shapes_summary"][shape] = count
            
            # Track file summary
            all_results["summary"]["files_summary"][file_name] = {
                "tensor_count": file_results["tensor_count"],
                "memory_mb": file_results["summary"]["total_memory_mb"]
            }
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            continue
    
    # Finalize summary
    all_results["summary"]["total_memory_mb"] = round(
        all_results["summary"]["total_memory_bytes"] / (1024 * 1024), 4
    )
    
    return all_results


def compute_tensor_stats(tensor: torch.Tensor, name: str) -> Dict[str, Any]:
    """
    Compute comprehensive statistics for a single tensor.
    
    Args:
        tensor: PyTorch tensor
        name: Name of the tensor
        
    Returns:
        Dictionary containing various statistics
    """
    stats = {
        "name": name,
        "shape": list(tensor.shape),
        "dtype": str(tensor.dtype),
        "size": tensor.numel(),
        "ndim": tensor.ndim,
        "memory_bytes": tensor.numel() * tensor.element_size(),
        "memory_mb": round((tensor.numel() * tensor.element_size()) / (1024 * 1024), 4),
    }
    
    # Only compute numerical stats for numeric dtypes
    if tensor.dtype.is_floating_point or tensor.dtype.is_complex or tensor.is_signed() or tensor.dtype == torch.uint8:
        # Convert to float for statistical computations to avoid overflow
        tensor_float = tensor.float() if tensor.dtype != torch.float32 else tensor
        
        # Move to CPU if on GPU for statistics computation
        if tensor_float.is_cuda:
            tensor_float = tensor_float.cpu()
        
        # Flatten tensor for percentile computations
        tensor_flat = tensor_float.flatten()
        
        stats.update({
            "min": float(torch.min(tensor_float)),
            "max": float(torch.max(tensor_float)),
            "mean": float(torch.mean(tensor_float)),
            "std": float(torch.std(tensor_float)),
            "median": float(torch.median(tensor_flat)),
            "var": float(torch.var(tensor_float)),
        })
        
        # Additional numerical statistics
        zeros_mask = (tensor == 0)
        stats.update({
            "zeros_count": int(torch.sum(zeros_mask)),
            "nonzeros_count": int(torch.count_nonzero(tensor)),
            "zeros_ratio": float(torch.sum(zeros_mask)) / tensor.numel(),
            "abs_mean": float(torch.mean(torch.abs(tensor_float))),
            "l1_norm": float(torch.norm(tensor_float.flatten(), p=1)),
            "l2_norm": float(torch.norm(tensor_float.flatten(), p=2)),
        })
        
        # Check for special values (only for floating point)
        if tensor.dtype.is_floating_point:
            stats.update({
                "nan_count": int(torch.sum(torch.isnan(tensor))),
                "inf_count": int(torch.sum(torch.isinf(tensor))),
                "finite_count": int(torch.sum(torch.isfinite(tensor))),
            })
    
    return stats


def analyze_safetensors_file(file_path: str, max_tensors: Optional[int] = None) -> Dict[str, Any]:
    """
    Analyze a safetensors file and compute statistics for all tensors.
    
    Args:
        file_path: Path to the safetensors file
        max_tensors: Maximum number of tensors to analyze (None for all)
        
    Returns:
        Dictionary containing file metadata and tensor statistics
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    if not file_path.suffix == '.safetensors':
        print(f"Warning: File {file_path} doesn't have .safetensors extension")
    
    # Initialize results
    results = {
        "file_path": str(file_path),
        "file_size_bytes": file_path.stat().st_size,
        "file_size_mb": round(file_path.stat().st_size / (1024 * 1024), 4),
        "tensor_count": 0,
        "tensors": [],
        "summary": {
            "total_parameters": 0,
            "total_memory_bytes": 0,
            "total_memory_mb": 0,
            "dtypes": {},
            "shapes_summary": {},
        }
    }
    
    # Load and analyze tensors
    with safe_open(file_path, framework="pt") as f:
        tensor_names = list(f.keys())
        results["tensor_count"] = len(tensor_names)
        
        # Limit number of tensors if specified
        if max_tensors and max_tensors < len(tensor_names):
            tensor_names = tensor_names[:max_tensors]
            print(f"Analyzing first {max_tensors} tensors out of {len(f.keys())}")
        
        for i, name in enumerate(tensor_names):
            print(f"Processing tensor {i+1}/{len(tensor_names)}: {name}")
            
            # Load tensor
            tensor = f.get_tensor(name)
            
            # Compute statistics
            stats = compute_tensor_stats(tensor, name)
            results["tensors"].append(stats)
            
            # Update summary statistics
            results["summary"]["total_parameters"] += stats["size"]
            results["summary"]["total_memory_bytes"] += stats["memory_bytes"]
            
            # Track dtypes
            dtype = stats["dtype"]
            if dtype in results["summary"]["dtypes"]:
                results["summary"]["dtypes"][dtype] += 1
            else:
                results["summary"]["dtypes"][dtype] = 1
            
            # Track shape patterns
            shape_str = str(stats["shape"])
            if shape_str in results["summary"]["shapes_summary"]:
                results["summary"]["shapes_summary"][shape_str] += 1
            else:
                results["summary"]["shapes_summary"][shape_str] = 1
    
    # Finalize summary
    results["summary"]["total_memory_mb"] = round(
        results["summary"]["total_memory_bytes"] / (1024 * 1024), 4
    )
    
    return results


def print_tensor_details(results: Dict[str, Any]) -> None:
    """Print detailed information for each tensor, one line per tensor."""
    print("\n" + "="*200)
    print("TENSOR DETAILS (sorted by name)")
    print("="*200)
    
    # Print header
    if 'total_files' in results and results['total_files'] > 1:
        header = f"{'File':<25} {'Name':<60} {'Shape':<20} {'DType':<15} {'Size':<12} {'Memory(MB)':<12} {'Mean':<12} {'Std':<12} {'Min':<12} {'Max':<12} {'Zeros%':<8}"
    else:
        header = f"{'Name':<80} {'Shape':<25} {'DType':<15} {'Size':<12} {'Memory(MB)':<12} {'Mean':<12} {'Std':<12} {'Min':<12} {'Max':<12} {'Zeros%':<8}"
    
    print(header)
    print("-" * len(header))
    
    # Sort tensors by name
    sorted_tensors = sorted(results['tensors'], key=lambda x: x['name'])
    
    for tensor in sorted_tensors:
        name = tensor['name']
        shape = str(tensor['shape'])
        dtype = tensor['dtype']
        size = f"{tensor['size']:,}"
        memory_mb = f"{tensor['memory_mb']:.3f}"
        
        # Handle numerical stats
        if 'mean' in tensor:
            mean = f"{tensor['mean']:.6f}"
            std = f"{tensor['std']:.6f}"
            min_val = f"{tensor['min']:.6f}"
            max_val = f"{tensor['max']:.6f}"
            zeros_pct = f"{tensor['zeros_ratio']*100:.2f}%"
        else:
            mean = std = min_val = max_val = zeros_pct = "N/A"
        
        if 'total_files' in results and results['total_files'] > 1:
            file_name = tensor.get('file', 'unknown')[:24]
            name = name[:59]
            shape = shape[:19]
            dtype = dtype[:14]
            line = f"{file_name:<25} {name:<60} {shape:<20} {dtype:<15} {size:<12} {memory_mb:<12} {mean:<12} {std:<12} {min_val:<12} {max_val:<12} {zeros_pct:<8}"
        else:
            name = name[:79]
            shape = shape[:24]
            dtype = dtype[:14]
            line = f"{name:<80} {shape:<25} {dtype:<15} {size:<12} {memory_mb:<12} {mean:<12} {std:<12} {min_val:<12} {max_val:<12} {zeros_pct:<8}"
        
        print(line)
    
    # Print summary at the end
    print("\n" + "="*80)
    print("SUMMARY")
    print("="*80)
    
    if 'total_files' in results and results['total_files'] > 1:
        print(f"Total Files: {results['total_files']}")
        print(f"Total Tensors: {results['total_tensor_count']}")
        print(f"Total Parameters: {results['summary']['total_parameters']:,}")
        print(f"Total Memory: {results['summary']['total_memory_mb']} MB")
        
        print(f"\nPer-file breakdown:")
        for file_name, file_info in results['summary']['files_summary'].items():
            print(f"  {file_name}: {file_info['tensor_count']} tensors, {file_info['memory_mb']} MB")
    else:
        print(f"File: {results.get('file_path', 'Unknown')}")
        print(f"Total Tensors: {results['tensor_count']}")
        print(f"Total Parameters: {results['summary']['total_parameters']:,}")
        print(f"Total Memory: {results['summary']['total_memory_mb']} MB")


def print_summary_only(results: Dict[str, Any]) -> None:
    """Print only a summary of the analysis results."""
    print("\n" + "="*80)
    print("SAFETENSORS ANALYSIS SUMMARY")
    print("="*80)
    
    if 'total_files' in results and results['total_files'] > 1:
        print(f"Total Files: {results['total_files']}")
        print(f"Total Tensors: {results['total_tensor_count']}")
        print(f"Total Parameters: {results['summary']['total_parameters']:,}")
        print(f"Total Memory: {results['summary']['total_memory_mb']} MB")
        
        print(f"\nFiles:")
        for file_info in results['files']:
            print(f"  {file_info['path']}: {file_info['tensor_count']} tensors, {file_info['memory_mb']} MB")
    else:
        print(f"File: {results.get('file_path', 'Unknown')}")
        print(f"File Size: {results.get('file_size_mb', 0)} MB ({results.get('file_size_bytes', 0)} bytes)")
        print(f"Number of Tensors: {results['tensor_count']}")
        print(f"Total Parameters: {results['summary']['total_parameters']:,}")
        print(f"Total Memory: {results['summary']['total_memory_mb']} MB")
    
    print(f"\nData Types Distribution:")
    for dtype, count in results['summary']['dtypes'].items():
        print(f"  {dtype}: {count} tensors")
    
    print(f"\nTop 10 Most Common Shapes:")
    sorted_shapes = sorted(results['summary']['shapes_summary'].items(), 
                          key=lambda x: x[1], reverse=True)
    for shape, count in sorted_shapes[:10]:
        print(f"  {shape}: {count} tensors")
    
    if results['tensors']:
        print(f"\nLargest Tensors by Memory:")
        sorted_tensors = sorted(results['tensors'], 
                              key=lambda x: x['memory_bytes'], reverse=True)
        for tensor in sorted_tensors[:5]:
            file_prefix = f"{tensor.get('file', '')}::" if 'file' in tensor else ""
            print(f"  {file_prefix}{tensor['name']}: {tensor['memory_mb']} MB, shape {tensor['shape']}")


def main():
    """Main function to handle command line arguments and run analysis."""
    parser = argparse.ArgumentParser(
        description="Analyze safetensors files and compute tensor statistics. "
                    "Supports single files, multiple files, directories, or index.json files."
    )
    parser.add_argument(
        "path", 
        help="Path to a safetensors file, directory containing safetensors files, or model.safetensors.index.json file"
    )
    parser.add_argument(
        "--output", "-o",
        help="Output JSON file to save detailed results"
    )
    parser.add_argument(
        "--max-tensors", "-m",
        type=int,
        help="Maximum number of tensors to analyze per file (useful for large files)"
    )
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress progress output"
    )
    parser.add_argument(
        "--summary-only", "-s",
        action="store_true",
        help="Show only summary instead of detailed tensor information"
    )
    
    args = parser.parse_args()
    
    try:
        # Find all safetensors files to process
        if not args.quiet:
            print(f"Finding safetensors files from: {args.path}")
        
        safetensors_files = find_safetensors_files(args.path)
        
        if not args.quiet:
            print(f"Found {len(safetensors_files)} safetensors file(s)")
            for f in safetensors_files:
                print(f"  - {f}")
        
        # Analyze the files
        if len(safetensors_files) == 1:
            # Single file analysis
            results = analyze_safetensors_file(safetensors_files[0], args.max_tensors)
        else:
            # Multiple files analysis
            results = analyze_multiple_safetensors_files(safetensors_files, args.max_tensors)
        
        # Print tensor information
        if not args.quiet:
            if args.summary_only:
                print_summary_only(results)
            else:
                print_tensor_details(results)
        
        # Save detailed results if requested
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"\nDetailed results saved to: {args.output}")
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()