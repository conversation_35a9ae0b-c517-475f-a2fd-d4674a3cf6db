import argparse
import json
import tensorcache.v2.local as tc
import torch
import numpy as np


def get_tensor_stats(tensor: torch.Tensor) -> dict:
    """
    Returns a dict containing base statistics for a tensor.
    
    Args:
        tensor: PyTorch tensor to analyze
        
    Returns:
        Dictionary containing basic tensor statistics
    """
    stats = {
        "shape": list(tensor.shape),
        "dtype": str(tensor.dtype),
        "size": tensor.numel(),
        "ndim": tensor.ndim,
        "memory_bytes": tensor.numel() * tensor.element_size(),
        "memory_mb": round((tensor.numel() * tensor.element_size()) / (1024 * 1024), 4),
    }
    
    # Only compute numerical stats for numeric dtypes
    if tensor.dtype.is_floating_point or tensor.dtype.is_complex or tensor.is_signed() or tensor.dtype == torch.uint8:
        # Convert to float for statistical computations to avoid overflow
        tensor_float = tensor.float() if tensor.dtype != torch.float32 else tensor
        
        # Move to CPU if on GPU for statistics computation
        if tensor_float.is_cuda:
            tensor_float = tensor_float.cpu()
        
        stats.update({
            "min": float(torch.min(tensor_float)),
            "max": float(torch.max(tensor_float)),
            "mean": float(torch.mean(tensor_float)),
            "std": float(torch.std(tensor_float)),
        })
        
        # Count zeros and non-zeros
        zeros_count = int(torch.sum(tensor == 0))
        stats.update({
            "zeros_count": zeros_count,
            "nonzeros_count": int(torch.count_nonzero(tensor)),
            "zeros_ratio": zeros_count / tensor.numel(),
        })
        
        # Check for special values (only for floating point)
        if tensor.dtype.is_floating_point:
            stats.update({
                "nan_count": int(torch.sum(torch.isnan(tensor))),
                "inf_count": int(torch.sum(torch.isinf(tensor))),
            })
            
            # Analyze mantissa distribution to detect fp4/fp8 quantization
            mantissa_stats = analyze_mantissa_distribution(tensor)
            stats.update(mantissa_stats)
    
    return stats


def analyze_mantissa_distribution(tensor: torch.Tensor) -> dict:
    """
    Analyze mantissa distribution to detect if tensor was quantized from fp4/fp8.
    
    Args:
        tensor: PyTorch floating-point tensor
        
    Returns:
        Dictionary with mantissa analysis results
    """
    if not tensor.dtype.is_floating_point:
        return {}
    
    # Move to CPU and flatten for analysis
    if tensor.is_cuda:
        tensor = tensor.cpu()
    tensor_flat = tensor.flatten()
    
    # Remove zeros, infinities, and NaNs for mantissa analysis
    finite_mask = torch.isfinite(tensor_flat) & (tensor_flat != 0)
    if not torch.any(finite_mask):
        return {"mantissa_analysis": "no_finite_nonzero_values"}
    
    finite_values = tensor_flat[finite_mask]
    
    # Convert to numpy for bit manipulation
    
    if tensor.dtype == torch.float32:
        # IEEE 754 single precision: 1 sign + 8 exponent + 23 mantissa
        np_values = finite_values.numpy().astype(np.float32)
        int_view = np_values.view(np.uint32)
        mantissa_bits = int_view & 0x7FFFFF  # Extract 23-bit mantissa
        mantissa_shift = 23
    elif tensor.dtype == torch.float16:
        # IEEE 754 half precision: 1 sign + 5 exponent + 10 mantissa
        np_values = finite_values.numpy().astype(np.float16)
        int_view = np_values.view(np.uint16)
        mantissa_bits = int_view & 0x3FF  # Extract 10-bit mantissa
        mantissa_shift = 10
    elif tensor.dtype == torch.bfloat16:
        # bfloat16: 1 sign + 8 exponent + 7 mantissa
        # Convert to float32 first, then extract
        np_values = finite_values.float().numpy().astype(np.float32)
        int_view = np_values.view(np.uint32)
        mantissa_bits = (int_view & 0x7FFFFF) >> 16  # Extract top 7 bits of mantissa
        mantissa_shift = 7
    else:
        return {"mantissa_analysis": f"unsupported_dtype_{tensor.dtype}"}
    
    # Analyze mantissa patterns
    unique_mantissas, counts = np.unique(mantissa_bits, return_counts=True)
    total_values = len(mantissa_bits)
    
    # Calculate statistics
    num_unique_mantissas = len(unique_mantissas)
    max_possible_mantissas = 2 ** mantissa_shift
    mantissa_utilization = num_unique_mantissas / max_possible_mantissas
    
    # Check for fp4/fp8 quantization patterns
    # FP4: ~16 unique values total (including signs/exponents)
    # FP8: ~256 unique values total
    
    # For mantissa analysis, we look at reduced precision patterns
    fp4_threshold = 0.01  # Very few unique mantissa values
    fp8_threshold = 0.05  # Limited unique mantissa values
    
    # Check if mantissa bits show quantization patterns
    # Count how many mantissa values use only lower precision bits
    
    # For FP4 detection: check if mantissa uses only ~1-2 bits effectively
    fp4_pattern_count = 0
    fp8_pattern_count = 0
    
    if mantissa_shift >= 4:
        # Check if values align to 4-bit boundaries (for fp4)
        fp4_aligned = mantissa_bits % (2 ** (mantissa_shift - 1)) == 0
        fp4_pattern_count = np.sum(fp4_aligned)
        
        # Check if values align to 8-bit boundaries (for fp8)
        if mantissa_shift >= 8:
            fp8_aligned = mantissa_bits % (2 ** (mantissa_shift - 3)) == 0
            fp8_pattern_count = np.sum(fp8_aligned)
    
    fp4_alignment_ratio = fp4_pattern_count / total_values if total_values > 0 else 0
    fp8_alignment_ratio = fp8_pattern_count / total_values if total_values > 0 else 0
    
    # Determine likely quantization source
    quantization_likelihood = "none"
    if mantissa_utilization < fp4_threshold or fp4_alignment_ratio > 0.8:
        quantization_likelihood = "likely_fp4_origin"
    elif mantissa_utilization < fp8_threshold or fp8_alignment_ratio > 0.6:
        quantization_likelihood = "likely_fp8_origin"
    elif mantissa_utilization < 0.1:
        quantization_likelihood = "possible_quantized_origin"
    
    return {
        "mantissa_unique_count": int(num_unique_mantissas),
        "mantissa_max_possible": int(max_possible_mantissas),
        "mantissa_utilization": round(mantissa_utilization, 6),
        "fp4_alignment_ratio": round(fp4_alignment_ratio, 4),
        "fp8_alignment_ratio": round(fp8_alignment_ratio, 4),
        "quantization_likelihood": quantization_likelihood,
        "mantissa_bits_analyzed": mantissa_shift
    }


def print_detailed_stats(stats):
    """Print detailed statistics for a single tensor."""
    print(f"\nDetailed Statistics for '{stats['name']}':")
    print(f"  Shape: {stats['shape']}")
    print(f"  DType: {stats['dtype']}")
    print(f"  Size: {stats['size']:,} elements")
    print(f"  Memory: {stats['memory_mb']:.2f} MB")
    
    if 'mean' in stats:
        print(f"  Mean: {stats['mean']:.6f}")
        print(f"  Std: {stats['std']:.6f}")
        print(f"  Min: {stats['min']:.6f}")
        print(f"  Max: {stats['max']:.6f}")
        print(f"  Zeros: {stats['zeros_count']:,} ({stats['zeros_ratio']*100:.2f}%)")
        print(f"  Non-zeros: {stats['nonzeros_count']:,}")
    
    if 'quantization_likelihood' in stats:
        print(f"  Quantization Analysis:")
        print(f"    Likelihood: {stats['quantization_likelihood']}")
        if 'mantissa_utilization' in stats:
            print(f"    Mantissa Utilization: {stats['mantissa_utilization']:.6f}")
        if 'fp4_alignment_ratio' in stats:
            print(f"    FP4 Alignment: {stats['fp4_alignment_ratio']:.4f}")
        if 'fp8_alignment_ratio' in stats:
            print(f"    FP8 Alignment: {stats['fp8_alignment_ratio']:.4f}")
        if 'mantissa_unique_count' in stats:
            print(f"    Unique Mantissas: {stats['mantissa_unique_count']}/{stats.get('mantissa_max_possible', 'N/A')}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("checkpoint", help="Path to the model checkpoint")
    parser.add_argument("--detailed", "-d", nargs="*", help="Show detailed stats for specific tensor names (or all if no names given)")
    parser.add_argument("--summary-only", "-s", action="store_true", help="Show only the summary table")
    parser.add_argument("--output", "-o", help="Output results to JSON file")
    args = parser.parse_args()

    reader = tc.CheckpointReader(args.checkpoint)
    tensor_map = reader.get_checkpoint_tensors()
    
    # Collect all tensor stats first
    all_stats = []
    for key, value in tensor_map.items():
        tensor = value.load_data()
        stats = get_tensor_stats(tensor)
        stats['name'] = key.name
        all_stats.append(stats)
    
    # Print summary table unless detailed mode is requested
    if not args.detailed or not args.summary_only:
        print(f"{'Name':<30} {'Shape':<20} {'DType':<10} {'Size':<12} {'MB':<8} {'Mean':<10} {'Std':<10} {'Zeros%':<8} {'Quant':<15}")
        print("-" * 130)
        
        # Print each tensor in one line
        for stats in all_stats:
            name = stats['name'][:29]  # Truncate long names
            shape_str = str(stats['shape'])[:19]  # Truncate long shapes
            dtype = str(stats['dtype'])
            size = f"{stats['size']:,}"[:11]  # Format with commas
            memory_mb = f"{stats['memory_mb']:.2f}"
            
            # Get numerical stats if available
            mean_str = f"{stats['mean']:.3f}" if 'mean' in stats else "N/A"
            std_str = f"{stats['std']:.3f}" if 'std' in stats else "N/A"
            zeros_pct = f"{stats['zeros_ratio']*100:.1f}%" if 'zeros_ratio' in stats else "N/A"
            
            # Get quantization likelihood
            quant = stats.get('quantization_likelihood', 'N/A')
            if quant == 'likely_fp4_origin':
                quant = 'FP4'
            elif quant == 'likely_fp8_origin':
                quant = 'FP8'
            elif quant == 'possible_quantized_origin':
                quant = 'Possible'
            elif quant == 'none':
                quant = 'None'
            
            print(f"{name:<30} {shape_str:<20} {dtype:<10} {size:<12} {memory_mb:<8} {mean_str:<10} {std_str:<10} {zeros_pct:<8} {quant:<15}")
        
        # Print summary
        total_tensors = len(all_stats)
        total_memory = sum(stats['memory_mb'] for stats in all_stats)
        fp4_count = sum(1 for stats in all_stats if stats.get('quantization_likelihood') == 'likely_fp4_origin')
        fp8_count = sum(1 for stats in all_stats if stats.get('quantization_likelihood') == 'likely_fp8_origin')
        
        print("-" * 130)
        print(f"Summary: {total_tensors} tensors, {total_memory:.2f} MB total, {fp4_count} likely FP4, {fp8_count} likely FP8")
    
    # Show detailed stats if requested
    if args.detailed is not None:
        if len(args.detailed) == 0:
            # Show detailed stats for all tensors
            for stats in all_stats:
                print_detailed_stats(stats)
        else:
            # Show detailed stats for specific tensors
            for tensor_name in args.detailed:
                matching_stats = [s for s in all_stats if tensor_name in s['name']]
                if matching_stats:
                    for stats in matching_stats:
                        print_detailed_stats(stats)
                else:
                    print(f"No tensor found matching '{tensor_name}'")
    
    # Export to JSON if requested
    if args.output:
        # Prepare JSON output with summary and tensor details
        total_tensors = len(all_stats)
        total_memory = sum(stats['memory_mb'] for stats in all_stats)
        fp4_count = sum(1 for stats in all_stats if stats.get('quantization_likelihood') == 'likely_fp4_origin')
        fp8_count = sum(1 for stats in all_stats if stats.get('quantization_likelihood') == 'likely_fp8_origin')
        possible_count = sum(1 for stats in all_stats if stats.get('quantization_likelihood') == 'possible_quantized_origin')
        
        json_output = {
            "checkpoint_path": args.checkpoint,
            "summary": {
                "total_tensors": total_tensors,
                "total_memory_mb": round(total_memory, 2),
                "likely_fp4_count": fp4_count,
                "likely_fp8_count": fp8_count,
                "possible_quantized_count": possible_count,
                "none_quantized_count": total_tensors - fp4_count - fp8_count - possible_count
            },
            "tensors": all_stats
        }
        
        with open(args.output, 'w') as f:
            json.dump(json_output, f, indent=2)
        print(f"\nResults exported to {args.output}")


if __name__ == '__main__':
    main()