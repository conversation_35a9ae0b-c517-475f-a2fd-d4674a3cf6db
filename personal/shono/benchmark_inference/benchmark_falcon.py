"""
Simple benchmark script for Falcon API using ShareGPT dataset.

Usage:
python benchmark_falcon.py --dataset-path /path/to/sharegpt.json --api-base http://localhost:5122/v1/inference --num-requests 100
"""

import argparse
import logging
from typing import Dict, Any

from benchmark_base import TokenMessageCompleterBenchmark
from legacy_rest_token_completer import LegacyRestTokenCompleter
from token_completer import CompleterBackend
from message_completer.token_message_completer import TokenMessageCompleter

logger = logging.getLogger(__name__)


class FalconBenchmark(TokenMessageCompleterBenchmark):
    def __init__(self, api_base: str, temperature: float = 1.0, renderer: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"):
        super().__init__(api_base, temperature=temperature, renderer=renderer)
        self.temperature = temperature
        self.renderer = renderer
        self.create_token_message_completer()

    def create_token_message_completer(self) -> None:
        """Create and initialize the Falcon TokenMessageCompleter."""
        # Initialize the token completer
        token_completer_config = LegacyRestTokenCompleter.Config(
            api_base=self.api_base,
            backend=CompleterBackend.FALCON_MM_BACKEND
        )

        token_message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=token_completer_config,
            completion_params={"model": "model", "temperature": self.temperature},
            renderer=self.renderer,
        )
        
        self.token_message_completer = token_message_completer_config.build()

    def print_config(self) -> None:
        """Print Falcon-specific configuration."""
        print(f"Temperature: {self.temperature}")
        print(f"Renderer: {self.renderer}")

    def get_request_kwargs_from_args(self, args: argparse.Namespace) -> Dict[str, Any]:
        """Extract additional keyword arguments for make_request from command line args."""
        return {
            "n": args.n,
            "temperature": self.temperature  # Use the instance temperature instead of args
        }


def main():
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    # Setup argument parser with base arguments
    parser = FalconBenchmark.setup_argument_parser()
    parser.description = "Benchmark Falcon API with ShareGPT dataset"
    parser.add_argument("--api-base", default="http://127.0.0.1:5122/v1/inference", help="Base URL for Falcon inference API")
    parser.add_argument("--temperature", type=float, default=1.0, help="Temperature for sampling")
    parser.add_argument("--renderer", default="harmony_v4.0.15_berry_v3_1mil_orion_lpe", help="Renderer to use")
    parser.add_argument("--n", type=int, default=1, help="Number of completions to generate for each prompt (OpenAI API parameter)")
    
    args = parser.parse_args()
    
    # Create benchmark instance
    print("Initializing Falcon benchmark...")
    benchmark = FalconBenchmark(
        api_base=args.api_base,
        temperature=args.temperature,
        renderer=args.renderer
    )
    
    # Use base class main method
    benchmark.run_benchmark_main(args)


if __name__ == "__main__":
    main()
