# Examples
## Chicken Engine EV3
### GPT-OSS-120B

```
beam python -m chicken_inference_scripts.start_chicken_engine \
    snapshot_family=osmini \
    snapshot_name=d36 \
    layout.n_expert_shards=1 layout.n_replicas=1 layout.n_op_shards=8 layout.pipe_depth=1 \
    snapshot_path="az://orngscuscresco/models/snapshots/osmini-unrl3-safetify-tc-20250815" \
    config_overrides="simple_attn_sink=False dp_collective_ops_use_tree=False dataset_encoding=orion_200k attention_implementation=ki replicate_kv_in_kernel=True eval_only=True use_overlapped_schedule=True microbatch_size=1 combo_chicken_osmini=True moe_implementation=ki dynamic_esharding_enabled=True minimal_dynamic_esharding_enabled=True dynamic_esharding_softmax_after_dropping=True route_masked_tokens_dust_decoder=False dynamic_esharding_use_2stage_rescaling_scheme=True dynamic_esharding_fuse_opsharding_comm=False ixf_max_batch_size=2048 ixf_batch_runner_memory_buffer=10_000_000_000 ixf_max_cache_list_length=8192 use_attn_global_scale=False use_moe_global_scale=False experts_output_scale=1.0 logit_multiplier=1.0 force_embedding_rmsnorm=False rotary_pos_embeddings_scaling_factor=32.0" \
    name=osmini-engine \
    cluster=local
```

```
bbb cp az://oaiorangeshono/tmp/ShareGPT_V3_unfiltered_cleaned_split.json .
python benchmark_ev3.py --dataset-path ShareGPT_V3_unfiltered_cleaned_split.json --num-requests 1024 --max-tokens 1024 --n 5
```

## Twapi EV2
### o4-mini
```
beam python -m harmony_scripts.engines.start_engine --name sweberry-v2-mini-engine --mode=optimal --snapshot_path="az://orngscuscresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted" --is_multimodal=False --gpu_kind=H100 --renderer_name="harmony_v4.0.15_berry_v3_1mil_orion_lpe" --restartable --extra_config_string="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=2 pipe_depth=1 n_replicas=1" --use_bus=False --use_bus_v2=False --n_replicas=1 --use_beam=True
```

```
bbb cp az://oaiorangeshono/tmp/ShareGPT_V3_unfiltered_cleaned_split.json .
python benchmark_falcon.py --dataset-path ShareGPT_V3_unfiltered_cleaned_split.json --num-requests 1024 --max-tokens 1024 --n 5
```

