"""
Simple benchmark script for EV3 API using ShareGPT dataset.

Usage:
python benchmark_ev3.py --dataset-path /path/to/sharegpt.json --api-base http://localhost:5122/v1 --num-requests 100
"""

import argparse
import asyncio
import logging
import time
import traceback
from typing import Dict, Any

from benchmark_base import TokenMessageCompleterBenchmark, BenchmarkRequest, BenchmarkResult
from message_completer.token_message_completer import TokenMessageCompleter
from batcher_requestor_token_completer import BatcherRequestorTokenCompleter
from twapi.engine.batcher_requestor import BatcherRequestor
import chat

logger = logging.getLogger(__name__)


class EV3Benchmark(TokenMessageCompleterBenchmark):
    def __init__(self, host: str, port: int = 5201, model_name: str = "model", 
                 renderer: str = "harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only",
                 pipereplica_group_id: str = "default"):
        # For EV3, we use host:port format instead of http URL
        api_base = f"{host}:{port}"
        super().__init__(api_base, host=host, port=port, model_name=model_name, 
                        renderer=renderer, pipereplica_group_id=pipereplica_group_id)
        self.host = host
        self.port = port
        self.model_name = model_name
        self.renderer = renderer
        self.pipereplica_group_id = pipereplica_group_id
        self.create_token_message_completer()

    def create_token_message_completer(self) -> None:
        """Create and initialize the EV3 TokenMessageCompleter."""
        # Initialize the EV3 batcher requestor
        batcher_requestor = BatcherRequestor.from_hosts(
            hosts=[self.api_base],
            min_replicas=1,
            pipereplica_group_id=self.pipereplica_group_id,
            ev3=True,
            model_name=self.model_name
        )
        
        # Initialize the token completer using batcher requestor
        token_completer = BatcherRequestorTokenCompleter(batcher_requestor)
        
        # Initialize the token message completer
        self.token_message_completer = TokenMessageCompleter(
            token_completer=token_completer,
            renderer=self.renderer,
        )

    def print_config(self) -> None:
        """Print EV3-specific configuration."""
        print(f"Host: {self.host}")
        print(f"Port: {self.port}")
        print(f"Model Name: {self.model_name}")
        print(f"Renderer: {self.renderer}")
        print(f"Pipereplica Group ID: {self.pipereplica_group_id}")

    def get_request_kwargs_from_args(self, args: argparse.Namespace) -> Dict[str, Any]:
        """Extract additional keyword arguments for make_request from command line args."""
        return {
            "n": args.n,
            "temperature": getattr(args, 'temperature', 0.7)
        }


def main():
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    # Setup custom argument parser for EV3
    parser = argparse.ArgumentParser(description="Benchmark EV3 API with ShareGPT dataset")
    parser.add_argument("--dataset-path", required=True, help="Path to ShareGPT JSON dataset")
    parser.add_argument("--host", default="localhost", help="Host address for the EV3 server")
    parser.add_argument("--port", type=int, default=5201, help="Port number for the EV3 server")
    parser.add_argument("--model-name", default="model", help="Name of the model to use")
    parser.add_argument("--renderer", default="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only", help="Renderer to use")
    parser.add_argument("--pipereplica-group-id", default="default", help="Pipereplica group ID")
    parser.add_argument("--temperature", type=float, default=0.7, help="Temperature for sampling")
    parser.add_argument("--num-requests", type=int, default=100, help="Number of requests to benchmark")
    parser.add_argument("--concurrency", type=int, default=None, help="Number of concurrent requests (default: same as num-requests)")
    parser.add_argument("--max-tokens", type=int, default=512, help="Maximum tokens per completion")
    parser.add_argument("--n", type=int, default=1, help="Number of completions to generate for each prompt")
    
    args = parser.parse_args()
    
    # Create benchmark instance
    print("Initializing EV3 benchmark...")
    benchmark = EV3Benchmark(
        host=args.host,
        port=args.port,
        model_name=args.model_name,
        renderer=args.renderer,
        pipereplica_group_id=args.pipereplica_group_id
    )
    
    # Use base class main method
    benchmark.run_benchmark_main(args)


if __name__ == "__main__":
    main()
