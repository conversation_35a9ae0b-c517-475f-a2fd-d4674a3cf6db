"""
Base benchmark framework for inference APIs using ShareGPT dataset.
"""

import argparse
import asyncio
import json
import logging
import time
import sys
import traceback
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path
from tqdm.asyncio import tqdm
from abc import ABC, abstractmethod
import chat

logger = logging.getLogger(__name__)


@dataclass
class BenchmarkRequest:
    prompt: str
    expected_tokens: int


@dataclass
class BenchmarkResult:
    success: bool
    latency: float
    tokens_generated: int
    prompt_tokens: int
    error: str = ""


class BaseBenchmark(ABC):
    """Abstract base class for API benchmarks."""
    
    def __init__(self, api_base: str, **kwargs):
        self.api_base = api_base
        # Store additional config parameters
        self.config = kwargs
    
    def load_sharegpt_dataset(self, dataset_path: str, num_requests: int) -> List[BenchmarkRequest]:
        """Load ShareGPT dataset and prepare benchmark requests."""
        if not Path(dataset_path).exists():
            raise FileNotFoundError(f"Dataset file not found: {dataset_path}")
        
        with open(dataset_path, 'r') as f:
            data = json.load(f)
        
        # Filter conversations with at least 2 turns
        filtered_data = []
        for item in data:
            conversations = item.get("conversations", item.get("conversation", []))
            if len(conversations) >= 2:
                # Use first turn as prompt, second as expected completion
                user_msg = conversations[0]["value"]
                assistant_msg = conversations[1]["value"] 
                filtered_data.append((user_msg, assistant_msg))
        
        # Take only the number of requests we need
        requests = []
        
        for i, (prompt, completion) in enumerate(filtered_data[:num_requests]):
            # Rough estimate of completion tokens
            completion_tokens = len(completion.split()) * 1.3  # Rough estimation
            requests.append(BenchmarkRequest(
                prompt=prompt,
                expected_tokens=int(completion_tokens)
            ))
        
        return requests

    @abstractmethod
    async def make_request(self, request: BenchmarkRequest, max_tokens: int = 512, **kwargs) -> BenchmarkResult:
        """Make a single request to the API. Must be implemented by subclasses."""
        pass

    async def run_benchmark(self, requests: List[BenchmarkRequest], concurrency: int = 10, max_tokens: int = 512, **kwargs) -> Dict[str, Any]:
        """Run the benchmark with specified concurrency."""
        
        # Create progress bar
        pbar = tqdm(total=len(requests), desc="Benchmark Progress", unit="req")
        
        async def process_request(request: BenchmarkRequest) -> BenchmarkResult:
            result = await self.make_request(request, max_tokens, **kwargs)
            pbar.update(1)
            return result
        
        # Run requests with concurrency limit
        semaphore = asyncio.Semaphore(concurrency)
        
        async def bounded_request(request):
            async with semaphore:
                return await process_request(request)
        
        start_time = time.perf_counter()
        try:
            results = await asyncio.gather(*[bounded_request(req) for req in requests])
        finally:
            pbar.close()
        end_time = time.perf_counter()
        
        # Analyze results
        successful_results = [r for r in results if r.success]
        failed_results = [r for r in results if not r.success]
        
        if not successful_results:
            return {
                "error": "All requests failed",
                "total_requests": len(requests),
                "successful_requests": 0,
                "failed_requests": len(failed_results),
                "errors": [r.error for r in failed_results[:5]]  # Show first 5 errors
            }
        
        latencies = [r.latency for r in successful_results]
        completion_tokens = [r.tokens_generated for r in successful_results]
        prompt_tokens = [r.prompt_tokens for r in successful_results]
        
        total_time = end_time - start_time
        total_completion_tokens = sum(completion_tokens)
        total_prompt_tokens = sum(prompt_tokens)
        
        return {
            "total_requests": len(requests),
            "successful_requests": len(successful_results),
            "failed_requests": len(failed_results),
            "total_time": total_time,
            "throughput_req_per_sec": len(successful_results) / total_time,
            "throughput_tokens_per_sec": total_completion_tokens / total_time,
            "avg_latency": sum(latencies) / len(latencies),
            "p50_latency": sorted(latencies)[len(latencies) // 2],
            "p90_latency": sorted(latencies)[int(len(latencies) * 0.9)],
            "p99_latency": sorted(latencies)[int(len(latencies) * 0.99)],
            "total_completion_tokens": total_completion_tokens,
            "total_prompt_tokens": total_prompt_tokens,
            "avg_completion_tokens_per_request": total_completion_tokens / len(successful_results),
            "avg_prompt_tokens_per_request": total_prompt_tokens / len(successful_results),
        }

    def print_results(self, results: Dict[str, Any]) -> None:
        """Print benchmark results in a formatted way."""
        print("\n" + "="*50)
        print("BENCHMARK RESULTS")
        print("="*50)
        
        if "error" in results:
            print(f"❌ Benchmark failed: {results['error']}")
            if "errors" in results:
                print("Sample errors:")
                for error in results["errors"]:
                    print(f"  - {error}")
            sys.exit(1)
        
        print(f"📊 Total Requests: {results['total_requests']}")
        print(f"✅ Successful: {results['successful_requests']}")
        print(f"❌ Failed: {results['failed_requests']}")
        print(f"⏱️  Total Time: {results['total_time']:.2f}s")
        print()
        print(f"🚀 Throughput: {results['throughput_req_per_sec']:.2f} req/s")
        print(f"🎯 Token Throughput: {results['throughput_tokens_per_sec']:.2f} tokens/s")
        print()
        print(f"📈 Average Latency: {results['avg_latency']:.3f}s")
        print(f"📊 P50 Latency: {results['p50_latency']:.3f}s")
        print(f"📊 P90 Latency: {results['p90_latency']:.3f}s")
        print(f"📊 P99 Latency: {results['p99_latency']:.3f}s")
        print()
        print(f"🔤 Total Completion Tokens: {results['total_completion_tokens']}")
        print(f"🔤 Total Prompt Tokens: {results['total_prompt_tokens']}")
        print(f"📝 Avg Completion Tokens/Request: {results['avg_completion_tokens_per_request']:.1f}")
        print(f"📝 Avg Prompt Tokens/Request: {results['avg_prompt_tokens_per_request']:.1f}")
        
        # Print JSON summary for programmatic use
        print("\n" + "="*50)
        print("JSON SUMMARY")
        print("="*50)
        print(json.dumps(results, indent=2))

    @classmethod
    def setup_argument_parser(cls) -> argparse.ArgumentParser:
        """Setup common command line arguments. Can be extended by subclasses."""
        parser = argparse.ArgumentParser(description="Benchmark API with ShareGPT dataset")
        parser.add_argument("--dataset-path", required=True, help="Path to ShareGPT JSON dataset")
        parser.add_argument("--num-requests", type=int, default=1000, help="Number of requests to benchmark")
        parser.add_argument("--concurrency", type=int, default=None, help="Number of concurrent requests (default: same as num-requests)")
        parser.add_argument("--max-tokens", type=int, default=1000, help="Maximum tokens per completion")
        return parser

    def run_benchmark_main(self, args: argparse.Namespace) -> None:
        """Main benchmark execution logic."""
        # Set default concurrency to num_requests if not specified
        if args.concurrency is None:
            args.concurrency = args.num_requests
        
        try:
            # Load dataset
            print(f"Loading dataset from {args.dataset_path}...")
            requests = self.load_sharegpt_dataset(args.dataset_path, args.num_requests)
            print(f"Loaded {len(requests)} requests from dataset")
            
            # Run benchmark
            print(f"Starting benchmark with {args.concurrency} concurrent requests...")
            print(f"Max tokens: {args.max_tokens}")
            
            # Print implementation-specific config
            self.print_config()
            print()
            
            # Get additional kwargs for make_request from args
            request_kwargs = self.get_request_kwargs_from_args(args)
            
            results = asyncio.run(self.run_benchmark(
                requests, 
                args.concurrency, 
                args.max_tokens,
                **request_kwargs
            ))
            
            # Print results
            self.print_results(results)
            
        except FileNotFoundError as e:
            print(f"❌ Error: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

    def print_config(self) -> None:
        """Print implementation-specific configuration. Override in subclasses."""
        pass

    def get_request_kwargs_from_args(self, args: argparse.Namespace) -> Dict[str, Any]:
        """Extract additional keyword arguments for make_request from command line args. Override in subclasses."""
        return {}


class TokenMessageCompleterBenchmark(BaseBenchmark):
    """Base class for benchmarks using TokenMessageCompleter."""
    
    def __init__(self, api_base: str, **kwargs):
        super().__init__(api_base, **kwargs)
        self.token_message_completer = None
    
    @abstractmethod 
    def create_token_message_completer(self) -> None:
        """Create and initialize the TokenMessageCompleter. Must be implemented by subclasses."""
        pass
   
    def create_conversation(self, prompt: str):
        """Create a conversation object from a prompt."""
        return chat.Conversation(messages=[
            chat.Message.system(
                model_identity_desc="You are ChatGPT, a large language model trained by OpenAI.", 
                channel_config=chat.SystemChannelConfig(
                    valid_channels=("analysis", "commentary", "final"), 
                    channel_required=True
                )
            ),
            chat.Message.user(prompt)
        ])
   
    async def make_request(self, request: BenchmarkRequest, max_tokens: int = 512, **kwargs) -> BenchmarkResult:
        """Make a single request using TokenMessageCompleter."""
        start_time = time.perf_counter()
        
        try:
            # Create conversation with user message
            conversation = self.create_conversation(request.prompt)
            
            # Extract parameters
            n = kwargs.get('n', 1)
            temperature = kwargs.get('temperature', self.config.get('temperature', 1.0))
            
            # Make the completion request
            completion = await self.token_message_completer.async_completion(
                [conversation], 
                n=n, 
                max_tokens=min(max_tokens, request.expected_tokens),
                completion_params={'temperature': temperature}
            )
            
            end_time = time.perf_counter()
            latency = end_time - start_time

            # Extract token counts from the first choice
            choice = completion.choices[0]
            num_prompt_tokens = len(choice.prompt_tokens)
            num_completion_tokens = sum(len(c.completion_tokens) for c in completion.choices)

            return BenchmarkResult(
                success=True,
                latency=latency,
                tokens_generated=num_completion_tokens,
                prompt_tokens=num_prompt_tokens
            )
                    
        except Exception as e:
            logger.error(f"Request failed: {traceback.format_exc()}")
            end_time = time.perf_counter()
            return BenchmarkResult(
                success=False,
                latency=end_time - start_time,
                tokens_generated=0,
                prompt_tokens=0,
                error=str(e)
            )
