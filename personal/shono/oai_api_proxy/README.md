# OAI API Proxy

This is a proxy server that translates OpenAI-compatible REST API calls into Bus or EV3 protocol.

## Usage with Bus backend

1. Start the proxy server:
   ```bash
   python bus_oai_api_proxy.py -r <renderer_name> bus_topic_name
   ```

2. Make a request to the proxy server:
   ```bash
   curl -X POST "http://localhost:8000/v1/chat/completions" -H "Content-Type: application/json" -d '{
       "model": "bus-model",
       "messages": [
           {"role": "user", "content": "Hello, how are you?"}
       ]
   }'
   ```

3. The proxy server will translate the request into the appropriate Bus protocol and forward it to the Bus backend.

## Usage with EV3 backend

1. Start the EV3 server. Use harmony_scripts or chicken_engine.

2. Start the EV3 proxy server:
```bash
python ev3_oai_api_proxy.py -r <renderer_name>
```

3. Make a request to the proxy server:
```bash
curl -X POST "http://localhost:8000/v1/chat/completions" -H "Content-Type: application/json" -d '{
    "model": "ev3-model",
    "messages": [
        {"role": "user", "content": "Hello, how are you?"}
    ]
}'
```

4. The proxy server will translate the request into the appropriate EV3 protocol and forward it to the EV3 backend.