
"""OpenAI-compatible HTTP server using MessageCompleter as backend.

This server provides OpenAI-compatible chat completion endpoints that internally
use the MessageCompleter class to generate responses.
"""

import argparse
import asyncio
import logging


from bus_token_completer.bus_token_completer import BusTokenCompleter
from message_completer.token_message_completer import TokenMessageCompleter
from chat.render import get_renderer
from base_oai_api_proxy import OpenAICompatibleServer, MessageCompleter


logger = logging.getLogger(__name__)


class BusMessageCompleter(MessageCompleter):
    def __init__(self, bus_topic: str, renderer_name: str):
        """Initialize the MessageCompleter.
        
        Args:
            bus_topic: The bus topic to use for token completion
            renderer_name: The name of the renderer to use for tokenization
        """
        super().__init__()
        # Initialize the bus token completer
        bus_token_completer = BusTokenCompleter(topic_or_snapshot=bus_topic)
        
        # Initialize the token message completer
        self.token_message_completer = TokenMessageCompleter(
            token_completer=bus_token_completer,
            renderer=renderer_name,
        )
        self._renderer = get_renderer(renderer_name)


async def main():
    """Main entry point for running the server."""

    parser = argparse.ArgumentParser(description="OpenAI-compatible HTTP server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--model-name", help="Model name to report")
    parser.add_argument("--renderer-name", "-r", default="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_tools_v2", help="Name of the renderer to use")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    parser.add_argument("bus_topic", help="Bus topic to use for MessageCompleter")
    
    args = parser.parse_args()
    logging.basicConfig(level=logging.DEBUG if args.verbose else logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    model_name = args.model_name
    if not model_name:
        logger.info(f"Model name not provided, using bus topic as model name: {args.bus_topic}")
        model_name = args.bus_topic

    message_completer = BusMessageCompleter(bus_topic=args.bus_topic, renderer_name=args.renderer_name)
    server = OpenAICompatibleServer(message_completer, model_name=model_name)
    
    logger.info(f"Model name: {model_name}")
    logger.info(f"Bus topic: {args.bus_topic}")
    logger.info(f"Renderer: {args.renderer_name}")
   
    await server.start_server(host=args.host, port=args.port)


if __name__ == "__main__":
    asyncio.run(main())