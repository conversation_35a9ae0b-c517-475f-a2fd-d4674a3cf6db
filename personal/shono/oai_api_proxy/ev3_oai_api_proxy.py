
"""OpenAI-compatible HTTP server using MessageCompleter as backend.

This server provides OpenAI-compatible chat completion endpoints that internally
use the MessageCompleter class to generate responses.
"""

import argparse
import asyncio
import logging

from message_completer.token_message_completer import TokenMessageCompleter
from chat.render import get_renderer
from batcher_requestor_token_completer import Batcher<PERSON>equestorTokenCompleter
from twapi.engine.batcher_requestor import BatcherRequestor
from base_oai_api_proxy import MessageCompleter, OpenAICompatibleServer

logger = logging.getLogger(__name__)

class Ev3MessageCompleter(MessageCompleter):
    def __init__(self, ev3_hosts: list[str], renderer_name: str, pipereplica_group_id: str):
        """Initialize the MessageCompleter.
        
        Args:
            ev3_hosts: List of EV3 host addresses (e.g., ["localhost:5201"])
            renderer_name: The name of the renderer to use for tokenization
            pipereplica_group_id: The pipereplica group ID to use
        """
        super().__init__()
        batcher_requestor = BatcherRequestor.from_hosts(
            hosts=ev3_hosts, 
            min_replicas=1, 
            ev3=True, 
            pipereplica_group_id=pipereplica_group_id
        )
        
        token_completer = BatcherRequestorTokenCompleter(batcher_requestor)
        
        self.token_message_completer = TokenMessageCompleter(
            token_completer=token_completer,
            renderer=renderer_name,
        )
        self._renderer = get_renderer(renderer_name)


async def main():
    """Main entry point for running the server."""

    parser = argparse.ArgumentParser(description="OpenAI-compatible HTTP server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--model-name", default="default", help="Model name to report")
    parser.add_argument("--renderer-name", "-r", default="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only", help="Name of the renderer to use")
    parser.add_argument("--ev3-hosts", default="localhost:5201", help="Comma-separated list of EV3 host addresses")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    logging.basicConfig(level=logging.DEBUG if args.verbose else logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    # Parse the EV3 hosts
    ev3_hosts = [host.strip() for host in args.ev3_hosts.split(",")]
    
    message_completer = Ev3MessageCompleter(
        ev3_hosts=ev3_hosts, 
        renderer_name=args.renderer_name,
        pipereplica_group_id="default"
    )
    server = OpenAICompatibleServer(message_completer, model_name=args.model_name)

    logger.info(f"Model name: {args.model_name}")
    logger.info(f"EV3 hosts: {ev3_hosts}")
    logger.info(f"Renderer: {args.renderer_name}")
    
    await server.start_server(host=args.host, port=args.port)


if __name__ == "__main__":
    asyncio.run(main())