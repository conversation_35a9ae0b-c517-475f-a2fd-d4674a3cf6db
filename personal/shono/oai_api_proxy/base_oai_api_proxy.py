
"""OpenAI-compatible HTTP server using MessageCompleter as backend.

This server provides OpenAI-compatible chat completion endpoints that internally
use the MessageCompleter class to generate responses.
"""

import argparse
import asyncio
import logging
import threading
import time
import uuid
from typing import List, Optional, Dict, Any, Union

import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel

import chat
from chat import Conversation, Message, Role
from message_completer.token_message_completer import TokenMessageCompleter
import traceback


logger = logging.getLogger(__name__)


class ChatMessage(BaseModel):
    """OpenAI-compatible chat message."""
    role: str
    content: str
    name: Optional[str] = None


class ChatCompletionRequest(BaseModel):
    """OpenAI-compatible chat completion request."""
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 1.0
    top_p: Optional[float] = 1.0
    n: Optional[int] = 1
    stream: Optional[bool] = False
    stop: Optional[Union[str, List[str]]] = None
    max_tokens: int = 100000
    presence_penalty: Optional[float] = 0.0
    frequency_penalty: Optional[float] = 0.0
    logit_bias: Optional[Dict[str, float]] = None
    user: Optional[str] = None
    reasoning_effort: Optional[str] = None


class ChatCompletionChoice(BaseModel):
    """OpenAI-compatible chat completion choice."""
    index: int
    message: ChatMessage
    finish_reason: str


class ChatCompletionUsage(BaseModel):
    """OpenAI-compatible usage statistics."""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponse(BaseModel):
    """OpenAI-compatible chat completion response."""
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
    usage: ChatCompletionUsage


class ModelInfo(BaseModel):
    """OpenAI-compatible model information."""
    id: str
    object: str = "model"
    created: int
    owned_by: str = "openai"


class ModelsResponse(BaseModel):
    """OpenAI-compatible models list response."""
    object: str = "list"
    data: List[ModelInfo]


ROLE_MAP = {
    "system": Role.SYSTEM,
    "user": Role.USER,
    "assistant": Role.ASSISTANT
}
class RequestTracker:
    def __init__(self):
        self._lock = threading.Lock()
        self._num_running_reqs = 0
        self._total_requests = 0
        self._total_prompt_tokens = 0
        self._total_completion_tokens = 0
        self._status_task = None
        self._running = False
    
    # async context manager to track the request count
    async def __aenter__(self):
        with self._lock:
            self._num_running_reqs += 1
            self._total_requests += 1
        
        # Create a request-specific tracker to avoid race conditions
        request_tracker = RequestInstance(self)
        return request_tracker

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        with self._lock:
            self._num_running_reqs -= 1

    def _add_tokens(self, prompt_tokens: int, completion_tokens: int):
        """Thread-safe method to add tokens to the totals."""
        with self._lock:
            self._total_prompt_tokens += prompt_tokens
            self._total_completion_tokens += completion_tokens

    def print_status(self):
        with self._lock:
            num_running = self._num_running_reqs
            total_reqs = self._total_requests
            total_prompt = self._total_prompt_tokens
            total_completion = self._total_completion_tokens
            total_tokens = total_prompt + total_completion
        
        logger.info(f"RequestTracker: running={num_running}, total_reqs={total_reqs}, prompt_tokens={total_prompt}, completion_tokens={total_completion}, total_tokens={total_tokens}")

    async def _status_loop(self):
        """Periodically print status information."""
        while self._running:
            try:
                await asyncio.sleep(30)  # Print status every 30 seconds
                if self._running:  # Check again in case we were stopped during sleep
                    self.print_status()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in status loop: {e}")

    def start(self):
        """Start the periodic status reporting."""
        if not self._running:
            self._running = True
            self._status_task = asyncio.create_task(self._status_loop())
            logger.info("RequestTracker started")

    def stop(self):
        """Stop the periodic status reporting."""
        if self._running:
            self._running = False
            if self._status_task:
                self._status_task.cancel()
            logger.info("RequestTracker stopped")


class RequestInstance:
    """Per-request tracker to avoid race conditions when setting token counts."""
    
    def __init__(self, parent_tracker: RequestTracker):
        self._parent = parent_tracker
        self._prompt_tokens = 0
        self._completion_tokens = 0
        self._tokens_set = False
    
    def set_status(self, prompt_tokens: int, completion_tokens: int):
        """Set the token counts for this specific request."""
        self._prompt_tokens = prompt_tokens
        self._completion_tokens = completion_tokens
        if not self._tokens_set:
            self._tokens_set = True
            # Add tokens to parent tracker in a thread-safe way
            self._parent._add_tokens(prompt_tokens, completion_tokens)

class MessageCompleter:
    def __init__(self):
        self._tracker = RequestTracker()
        self._tracker.start()

    async def complete(self, conversation: Conversation, n: int, temperature: float | None, max_tokens: int) -> tuple[list[list[Message]], int, int]:
        """Complete a conversation with new messages.
        
        Args:
            conversation: The conversation to complete
            n: The number of completions to generate
            temperature: The temperature to use for sampling
        Returns:
            List of new messages (typically containing one assistant message) and the number of prompt tokens and completion tokens used.
        """
        if logger.isEnabledFor(logging.DEBUG):
            prompt_tokens = self._renderer.render_for_completion_multimodal_toklist(conversation, Role.ASSISTANT).get_ints()
            decoded_prompt = self._renderer.decode(prompt_tokens)
            logger.debug(f"Decoded prompt for completion: {repr(decoded_prompt)}. ({len(prompt_tokens)} tokens)")
            logger.debug(f"Raw prompt: {prompt_tokens}")

        try:
            # Make the completion request
            start_time = time.time()
            async with self._tracker as t:
                completion: TokenMessageCompleter.Completion = await self.token_message_completer.async_completion([conversation], n=n, max_tokens=max_tokens, completion_params={'temperature': temperature})
                processing_time = time.time() - start_time
                num_prompt_tokens = len(completion.choices[0].prompt_tokens)
                messages_list = [choice.get_messages() for choice in completion.choices]
                completion_tokens_count = sum(len(c.completion_tokens) for c in completion.choices)
                t.set_status(num_prompt_tokens, completion_tokens_count)

            logger.info(f"Generated {[len(messages) for messages in messages_list]} completion messages. prompt={num_prompt_tokens} completion={completion_tokens_count}. Took {processing_time}s. {(num_prompt_tokens+completion_tokens_count)/processing_time} tokens/s.")
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"Completions: {[self._renderer.decode(c.completion_tokens) for c in completion.choices]}")
            return messages_list, num_prompt_tokens, completion_tokens_count

        except Exception as e:
            logger.error(f"Error in MessageCompleter.complete: {e}\n{traceback.format_exc()}")
            # Return a fallback message
            return [[Message.assistant(f"I apologize, but I encountered an error while processing your request: {str(e)}")] for _ in range(n)]


class OpenAICompatibleServer:
    """OpenAI-compatible HTTP server using MessageCompleter backend."""
    
    def __init__(self, message_completer: MessageCompleter, model_name: str = "model"):
        """Initialize the server.
        
        Args:
            message_completer: The MessageCompleter instance to use for generating responses
            model_name: The name of the model to report in API responses
        """
        self.message_completer = message_completer
        self.model_name = model_name
        self.app = FastAPI(
            title="OpenAI-Compatible Chat API",
            description="OpenAI-compatible API server using MessageCompleter backend",
            version="1.0.0"
        )
        self._setup_routes()

    def _setup_routes(self):
        """Set up HTTP routes."""
        
        @self.app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
        async def chat_completions(request: ChatCompletionRequest) -> ChatCompletionResponse:
            """Generate chat completions."""
            try:
                logger.info(f"Received chat completion request with {len(request.messages)} messages")
                
                # Convert OpenAI messages to internal conversation format
                conversation = self._convert_to_internal_conversation(request.messages, request.reasoning_effort)
                
                # Generate completion using MessageCompleter
                completion_messages_list, num_prompt_tokens, num_completion_tokens = await self.message_completer.complete(conversation, request.n, request.temperature, request.max_tokens)
                
                # Convert back to OpenAI format
                choices = []
                
                for i, completion_messages in enumerate(completion_messages_list):
                    # Find the last assistant message in the completion
                    assistant_message = None
                    for msg in reversed(completion_messages):
                        if msg.role == Role.ASSISTANT:
                            assistant_message = msg
                            break
                    
                    if assistant_message is None:
                        # Fallback if no assistant message found
                        assistant_content = "I apologize, but I couldn't generate a response."
                        finish_reason = "error"
                    else:
                        assistant_content = str(assistant_message.content)
                        finish_reason = "stop"  # Could be enhanced to detect other finish reasons
                    
                    choice = ChatCompletionChoice(
                        index=i,
                        message=ChatMessage(
                            role="assistant",
                            content=assistant_content
                        ),
                        finish_reason=finish_reason
                    )
                    choices.append(choice)
                
                return ChatCompletionResponse(
                    id=f"chatcmpl-{uuid.uuid4().hex}",
                    created=int(time.time()),
                    model=request.model,
                    choices=choices,
                    usage=ChatCompletionUsage(
                        prompt_tokens=num_prompt_tokens,
                        completion_tokens=num_completion_tokens,
                        total_tokens=num_prompt_tokens + num_completion_tokens
                    )
                )
                
            except Exception as e:
                logger.error(f"Error generating chat completion: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/v1/models", response_model=ModelsResponse)
        async def models() -> ModelsResponse:
            """List available models."""
            return ModelsResponse(
                object="list",
                data=[
                    ModelInfo(
                        id=self.model_name,
                        created=int(time.time()),
                        owned_by="openai"
                    )
                ]
            )

        @self.app.get("/health")
        async def health():
            """Health check endpoint."""
            return {"status": "healthy", "model": self.model_name}

    def _convert_to_internal_conversation(self, messages: List[ChatMessage], reasoning_effort) -> Conversation:
        """Convert OpenAI messages to internal Conversation format."""
        internal_messages = []
 
        if not any(ROLE_MAP.get(msg.role.lower()) == Role.SYSTEM for msg in messages):
            # If all messages are system messages, we can use a simpler format
            instruction = f"Reasoning: {reasoning_effort}" if reasoning_effort else None
            system_message = Message.system(instruction, model_identity_desc="You are ChatGPT, a large language model trained by OpenAI.", channel_config=chat.SystemChannelConfig(valid_channels=("analysis", "commentary", "final"), channel_required=True))
            internal_messages.append(system_message)

        for msg in messages:
            role = ROLE_MAP.get(msg.role.lower())
            if role is None:
                raise HTTPException(status_code=400, detail=f"Unsupported role: {msg.role}")
            
            # Create internal message based on role
            if role == Role.SYSTEM:
                instructions = msg.content
                if reasoning_effort:
                    instructions += f"\nReasoning: {reasoning_effort}\n\n"
                internal_message = Message.system(instructions)
            elif role == Role.USER:
                internal_message = Message.user(msg.content)
            elif role == Role.ASSISTANT:
                internal_message = Message.assistant(msg.content)
            
            internal_messages.append(internal_message)
        
        return Conversation(messages=internal_messages)

    def get_app(self) -> FastAPI:
        """Get the FastAPI application."""
        return self.app

    async def start_server(self, host: str = "0.0.0.0", port: int = 8000):
        """Start the HTTP server."""

        logger.info(f"Starting OpenAI-compatible server on {host}:{port}")
        logger.info("API endpoints:")
        logger.info("  POST /v1/chat/completions")
        logger.info("  GET /v1/models")
        logger.info("  GET /health")

        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()