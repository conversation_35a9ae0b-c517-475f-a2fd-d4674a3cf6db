#!/usr/bin/env python3
"""
Simple client script for sending requests to EV3 MessageCompleter.

This script creates a conversation from a prompt and sends it to an EV3-backed
MessageCompleter for completion.
"""

import argparse
import asyncio
import logging
import time

from message_completer.token_message_completer import TokenMessageCompleter
from chat.render import get_renderer
from batcher_requestor_token_completer import BatcherRequestorTokenCompleter
from twapi.engine.batcher_requestor import BatcherRequestor
import chat

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class MessageCompleter:
    def __init__(self, renderer_name, ev3_hosts=None, pipereplica_group_id="default"):
        """Initialize the MessageCompleter.
        
        Args:
            renderer_name: The name of the renderer to use for tokenization
            ev3_hosts: List of EV3 host addresses (e.g., ["localhost:5201"])
            pipereplica_group_id: The pipereplica group ID to use
        """
        if ev3_hosts is None:
            ev3_hosts = ["localhost:5201"]
            
        # Initialize the batcher requestor
        batcher_requestor = BatcherRequestor.from_hosts(
            hosts=ev3_hosts, 
            min_replicas=1, 
            ev3=True, 
            pipereplica_group_id=pipereplica_group_id
        )
        
        # Initialize the token completer using batcher requestor
        token_completer = BatcherRequestorTokenCompleter(batcher_requestor)
        
        # Initialize the token message completer
        self.token_message_completer = TokenMessageCompleter(
            token_completer=token_completer,
            renderer=renderer_name,
        )
        self._renderer = get_renderer(renderer_name)

    async def complete(self, prompt, n, max_tokens, temperature):
        """Make a conversation object. Then send a request.
        
        Args:
            prompt: The user prompt to complete
            n: Number of completions to generate
            max_tokens: Maximum tokens to generate (not directly used, depends on model)
            temperature: Sampling temperature
            
        Returns:
            List of completion messages
        """
        # Create conversation object with system message and user message
        conversation = chat.Conversation(
            messages=[
                chat.Message.system("You are a helpful AI assistant. Please provide clear, accurate, and thoughtful responses to user questions."),
                chat.Message.user(prompt),
            ],
        )
        
        logger.info(f"Sending request with prompt: {repr(prompt)}")
        logger.info(f"Parameters: n={n}, max_tokens={max_tokens}, temperature={temperature}")
        
        try:
            # Make the completion request
            completion: TokenMessageCompleter.Completion = await self.token_message_completer.async_completion(
                [conversation], 
                n=n, 
                completion_params={'temperature': temperature}
            )
            
            # Extract messages from all choices
            messages_list = [choice.get_messages() for choice in completion.choices]
            prompt_tokens_count = sum(len(c.prompt_tokens) for c in completion.choices)
            completion_tokens_count = sum(len(c.completion_tokens) for c in completion.choices) 
            logger.info(f"Generated {len(messages_list)} completion(s)")
            return messages_list, prompt_tokens_count, completion_tokens_count
            
        except Exception as e:
            logger.error(f"Error in MessageCompleter.complete: {e}")
            raise


async def main():
    parser = argparse.ArgumentParser(description="Send a completion request to EV3")
    parser.add_argument('--prompt', default="Hello, world!", help="The prompt to send")
    parser.add_argument('--num_completions', '-n', default=1, type=int, help="Number of completions to generate")
    parser.add_argument('--max_tokens', default=2**15, type=int, help="Maximum tokens to generate")
    parser.add_argument('--temperature', default=0.7, type=float, help="Sampling temperature")
    parser.add_argument('--renderer-name', '-r', 
                       default="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc_allow_refusal_text_only", 
                       help="Name of the renderer to use")
    parser.add_argument('--ev3-hosts', default="localhost:5201", 
                       help="Comma-separated list of EV3 host addresses")
    parser.add_argument('--verbose', '-v', action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Parse the EV3 hosts
    ev3_hosts = [host.strip() for host in args.ev3_hosts.split(",")]
    
    # Initialize the message completer
    message_completer = MessageCompleter(
        renderer_name=args.renderer_name,
        ev3_hosts=ev3_hosts,
        pipereplica_group_id="default"
    )
    
    try:
        start_time = time.time()

        # Send the completion request
        messages_list, prompt_tokens, completion_tokens = await message_completer.complete(
            prompt=args.prompt,
            n=args.num_completions,
            max_tokens=args.max_tokens,
            temperature=args.temperature
        )
        end_time = time.time()

        # Print the results
        print("\n" + "=" * 60)
        print(f"Request took {end_time - start_time:.2f} seconds")
        print(f"Prompt tokens: {prompt_tokens}, {prompt_tokens/(end_time - start_time):.2f} tokens/second")
        print(f"Completion tokens: {completion_tokens}, {completion_tokens/(end_time - start_time):.2f} tokens/second")
        print("COMPLETION RESULTS:")
        print("=" * 60)
        
        for i, messages in enumerate(messages_list):
            print(f"\n--- Completion {i+1} ---")
            for j, message in enumerate(messages):
                print(f"Message {j+1} ({message.role.name}):")
                print(message.content)
                print()
        
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"Failed to get completion: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

