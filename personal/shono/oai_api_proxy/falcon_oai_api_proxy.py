"""OpenAI-compatible HTTP server using TWAPI BerryMultiMessageTurnCompleter as backend.

This server provides OpenAI-compatible chat completion endpoints that internally
use the BerryMultiMessageTurnCompleter class to generate responses.
"""

import argparse
import asyncio
import logging
import os

from legacy_rest_token_completer import LegacyRest<PERSON>okenCompleter
from token_completer import CompleterBackend
from message_completer.token_message_completer import TokenMessageCompleter
from base_oai_api_proxy import MessageCompleter, OpenAICompatibleServer

logger = logging.getLogger(__name__)


class FalconMessageCompleter(MessageCompleter):
    def __init__(self, api_base: str, temperature: float, renderer: str):
        """Initialize the TWAPI MessageCompleter.
        
        Args:
            api_base: The base URL for the TWAPI inference endpoint
            model: The model name to use for completions
            temperature: The temperature to use for sampling
            renderer: The renderer name to use for tokenization
        """
        super().__init__()

        token_completer_config = LegacyRestTokenCompleter.Config(
            api_base=api_base,
            backend=CompleterBackend.FALCON_MM_BACKEND
        )

        token_message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=token_completer_config,
            completion_params={"model": "model", "temperature": temperature},
            renderer=renderer,
        )
        
        self.token_message_completer = token_message_completer_config.build()
        self._renderer = self.token_message_completer.renderer


async def main():
    """Main entry point for running the server."""

    parser = argparse.ArgumentParser(description="OpenAI-compatible HTTP server using TWAPI")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--model-name", default="model", help="Model name to report and use for completions")
    parser.add_argument("--api-base", default="http://127.0.0.1:5122/v1/inference", help="Base URL for the TWAPI inference endpoint")
    parser.add_argument("--temperature", type=float, default=1.0, help="Default temperature for completions")
    parser.add_argument("--renderer", "-r", default="harmony_v4.0.15_berry_v3_1mil_orion_lpe", help="Name of the renderer to use")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    logging.basicConfig(level=logging.DEBUG if args.verbose else logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    message_completer = FalconMessageCompleter(
        api_base=args.api_base,
        temperature=args.temperature,
        renderer=args.renderer,
    )
    
    server = OpenAICompatibleServer(message_completer, model_name=args.model_name)

    logger.info(f"Model name: {args.model_name}")
    logger.info(f"API base: {args.api_base}")
    logger.info(f"Temperature: {args.temperature}")
    logger.info(f"Renderer: {args.renderer}")
    
    await server.start_server(host=args.host, port=args.port)


if __name__ == "__main__":
    asyncio.run(main())
