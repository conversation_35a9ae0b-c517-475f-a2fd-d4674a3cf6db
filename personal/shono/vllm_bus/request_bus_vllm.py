
import sys
import argparse
from typing import Sequence, Any
import uuid

from opentelemetry.metrics import set_meter_provider, NoOpMeterProvider
from opentelemetry.trace import set_tracer_provider
from opentelemetry.sdk.trace import TracerProvider

# transformers library sets up OpenTelemetry and crashes lol bad, bad, bad
# We need to set a fake provider to avoid it
set_meter_provider(NoOpMeterProvider())
set_tracer_provider(TracerProvider())

from transformers import AutoTokenizer
import pydantic

from bus_token_completer.bus_token_completer import BusTokenCompleter
from message_completer.token_message_completer import TokenMessageCompleter

from chat import chat
from chat_enums import Role
from chat._src.render.formatter import Formatter
from chat.render.common import render_content
from chat.render.renderer_base import BaseRenderer, ensure_parse_error
from chat.render.parse import Parse<PERSON>enerator, GetNextToken, BeginMessage, UpdateMessage, EndMessage, OutOfTokens, ParseError
from vllm.entrypoints.chat_utils import ConversationMessage, apply_hf_chat_template
from openai.types.chat.chat_completion_message_tool_call_param import ChatCompletionMessageToolCallParam, Function


def convert_chat_to_vllm_format(convo: chat.Conversation) -> list[ConversationMessage]:
    """Convert a chat conversation to vLLM's expected format.
    
    class ConversationMessage(TypedDict):
        role: Required[str]
        content: Union[Optional[str], list[dict[str, str]]]
        tool_call_id: Optional[str]
        name: Optional[str]
        tool_calls: Optional[Iterable[ChatCompletionMessageToolCallParam]]
        
    """
    # Filter out system messages. The Chat template already handles system message.
    messages = [msg for msg in convo.messages if msg.role != Role.SYSTEM]
    return [
        ConversationMessage(
            role=msg.role.value,
            content=str(msg.content),
            tool_call_id=msg.id,
            name=msg.author.name,
            tool_calls=[ChatCompletionMessageToolCallParam(id=msg.id, function=Function(name=msg.recipient, arguments=msg.content.split(' ')), type='function')] if msg.recipient != 'all' else None,
        )
        for msg in messages
    ]

class VLLMCustomRenderer(BaseRenderer):
    """Custom renderer for vLLM API with local tokenizer files.

    Note that this renderer has minimum functionality to make our workflow work. 
    """
    # Required BaseRenderer fields
    n_ctx: int = 128000
    max_action_length: int = 32000 
    name: str = "vllm_custom_renderer"
    encoding_name: str = "orion_200k_mmgen"  # Fake name

    # Custom fields for local tokenizer
    local_tokenizer_dir: str = pydantic.Field(description="Path to local HF tokenizer directory")
    
    def __init__(self, *, tools=None, **kwargs):
        formatter = Formatter(supports_multi_message_action=True)
        super().__init__(**kwargs, formatter=formatter)
        self._hf_tokenizer = None
        self._tools = tools
        self._load_local_tokenizer()
    
    def _load_local_tokenizer(self):
        """Load the Hugging Face tokenizer from local directory."""
        
        self._hf_tokenizer = AutoTokenizer.from_pretrained(
            self.local_tokenizer_dir,
            trust_remote_code=True,
            local_files_only=True
        )
        print(f"Successfully loaded custom tokenizer. Vocab size: {self._hf_tokenizer.vocab_size}")

        self._chat_template = self._hf_tokenizer.get_chat_template(None, tools=None)

    @property
    def encoding(self):
        return self._hf_tokenizer

    def encode(self, text: str, allow_special_tokens: bool = False) -> list[int]:
        """Encode text to token IDs using the custom tokenizer."""
        return self._hf_tokenizer.encode(
            text, 
            add_special_tokens=allow_special_tokens,
            truncation=False,
            padding=False
        )
    
    def decode(self, tokens: Sequence[int]) -> str:
        """Decode token IDs to text using the custom tokenizer."""
        return self._hf_tokenizer.decode(tokens, skip_special_tokens=False)
    
    def render_for_display(self, convo: chat.Conversation) -> str:
        """Render conversation for human-readable display."""
        result = []
        for msg in convo.messages:
            role_str = msg.role.value.upper()
            content_str = render_content(msg, 0)
            result.append(f"[{role_str}]: {content_str}")
        return "\n\n".join(result)
    
    def render_for_completion(
        self,
        convo: chat.Conversation,
        role: chat.Role,
        username: str | None = None,
        recipient: str | None = None,
        require_recipient: bool = False,
        end_header: bool = False,
        completion_content_type: Any = None,  # chat.MessageContent | None
    ) -> list[int]:
        """Render conversation for model completion with vLLM-compatible format."""
        # Build conversation text with simple format
        result_text = ""
        vllm_convo = convert_chat_to_vllm_format(convo)
        # TODO: Convert the tools section to vLLM's JSON schema format.
        # Note that model_identity_desc in the system message is ignored. vLLM has hard-coded message in the chat template.
        result_text = apply_hf_chat_template(self._hf_tokenizer, vllm_convo, self._chat_template, tools=self._tools, model_config=None, add_generation_prompt=True)
        tokens = self._hf_tokenizer.encode(result_text, add_special_tokens=False)

        # Ensure we don't exceed context length
        if len(tokens) > (self.n_ctx - self.max_action_length):
            # Simple truncation from the beginning (keeping recent context)
            max_prompt_tokens = self.n_ctx - self.max_action_length
            tokens = tokens[-max_prompt_tokens:]
            print(f"Warning: Conversation truncated to {len(tokens)} tokens")
        
        return tokens

    def extract_tool_call_info(self, tool_call_rsp: str):
        """Extract tool call information from special tokens in the response."""
        if '<|tool_calls_section_begin|>' not in tool_call_rsp:
            # No tool calls
            return []
        import re
        pattern = r"<\|tool_calls_section_begin\|>(.*?)<\|tool_calls_section_end\|>"
        
        tool_calls_sections = re.findall(pattern, tool_call_rsp, re.DOTALL)
        
        # Extract multiple tool calls
        func_call_pattern = r"<\|tool_call_begin\|>\s*(?P<tool_call_id>[\w\.]+:\d+)\s*<\|tool_call_argument_begin\|>\s*(?P<function_arguments>.*?)\s*<\|tool_call_end\|>"
        tool_calls = []
        for match in re.findall(func_call_pattern, tool_calls_sections[0], re.DOTALL):
            function_id, function_args = match
            # function_id: functions.get_weather:0
            function_name = function_id.split('.')[1].split(':')[0]
            tool_calls.append(
                {
                    "id": function_id,
                    "type": "function",
                    "function": {
                        "name": function_name,
                        "arguments": function_args
                    }
                }
            )  
        return tool_calls

    @ensure_parse_error
    def parse_generator(
        self,
        role: chat.Role,
        recipient: str | None = None,
        skip_header: bool = False,
        require_recipient: bool = False,
        allow_multiple_messages: bool = False,
        in_place: bool = False,
        return_token_count: bool = False,
    ) -> ParseGenerator:
        """Parse generator implementation for vLLM custom renderer.
        
        This is a simple implementation that accumulates tokens and builds a text message.
        It handles basic text parsing without complex header parsing since vLLM models
        typically generate simple text completions.
        """
        from chat.chat import Text, MessageMetadataKeys
        from chat_enums import Status
        
        # Initialize message components
        all_tokens = []
        decoded_content = ""
        
        # Use default recipient if not provided
        if recipient is None:
            recipient = "all"
        
        # Start parsing - first yield GetNextToken to request the first token
        t = yield GetNextToken
        
        # Begin message with initial empty content
        yield BeginMessage(
            id=uuid.uuid4(),
            role=role,
            recipient=recipient,
            content=Text.from_string(""),
            tokens=[],
            decoded_tokens="",
            metadata={"first_content_token_index": 0}
        )
        
        # Parse tokens until we hit OutOfTokens or stop strings
        while t is not OutOfTokens:
            if t is None:
                t = yield GetNextToken
                continue
                
            # Add token to our accumulator
            all_tokens.append(t)
            
            # Decode the accumulated tokens to get current content
            try:
                current_decoded = self.decode(all_tokens)
                
                # Check if we've hit a stop string
                should_stop = False
                for stop_str in self.stop_strings():
                    if stop_str in current_decoded:
                        # Remove the stop string from the content
                        current_decoded = current_decoded.replace(stop_str, "")
                        should_stop = True
                        break
                
                # Update message content if it changed
                if current_decoded != decoded_content:
                    decoded_content = current_decoded
                    yield UpdateMessage(
                        content=Text.from_string(decoded_content),
                        tokens=all_tokens.copy(),
                        decoded_tokens=current_decoded
                    )
                
                if should_stop:
                    break
                    
            except Exception:
                # If decoding fails, continue accumulating tokens
                pass
            
            # Get next token
            t = yield GetNextToken
        
        # Check for tool calls in the decoded content
        tool_calls = self.extract_tool_call_info(decoded_content)
        
        if tool_calls:
            # Remove tool call sections from the main message content first
            import re
            # Remove the entire tool calls section from decoded content
            pattern = r"<\|tool_calls_section_begin\|>.*?<\|tool_calls_section_end\|>"
            cleaned_content = re.sub(pattern, "", decoded_content, flags=re.DOTALL).strip()
            
            # Update the main message with cleaned content
            if cleaned_content != decoded_content:
                yield UpdateMessage(
                    content=Text.from_string(cleaned_content),
                    tokens=all_tokens.copy(),
                    decoded_tokens=cleaned_content
                )
            
            # End the main message first
            main_metadata = {}
            if return_token_count:
                main_metadata[MessageMetadataKeys.MESSAGE_CONTENT_TOKEN_COUNT] = len(all_tokens)
            
            yield EndMessage(
                end_turn=False,  # Don't end turn yet, we have tool calls
                tokens=[],
                decoded_tokens="",
                status=Status.FINISHED_SUCCESSFULLY,
                metadata=main_metadata
            )
            
            # Now process each tool call as a separate message
            for tool_call in tool_calls:
                tool_name = tool_call["function"]["name"]
                tool_arguments = tool_call["function"]["arguments"]
                tool_id = tool_call["id"]
                
                # Create a new message for the tool call
                yield BeginMessage(
                    id=uuid.uuid4(),
                    role=role,
                    recipient=tool_name,  # Set recipient to tool name
                    content=Text.from_string(tool_arguments),
                    tokens=[],
                    decoded_tokens=tool_arguments,
                    metadata={
                        "tool_call_id": tool_id,
                        "tool_call_type": tool_call["type"]
                    }
                )
                
                # End the tool call message
                yield EndMessage(
                    end_turn=False,  # Tool calls don't end the turn
                    tokens=[],
                    decoded_tokens="",
                    status=Status.FINISHED_SUCCESSFULLY,
                    metadata={}
                )
        else:
            # No tool calls - prepare final metadata and end the main message
            final_metadata = {}
            if return_token_count:
                final_metadata[MessageMetadataKeys.MESSAGE_CONTENT_TOKEN_COUNT] = len(all_tokens)
            
            # End the main message
            yield EndMessage(
                end_turn=True,  # Main message ends turn
                tokens=[],
                decoded_tokens="",
                status=Status.FINISHED_SUCCESSFULLY,
                metadata=final_metadata
            )
        
        # Clean finish - expect OutOfTokens
        if t is not OutOfTokens:
            t = yield GetNextToken
            if t is not OutOfTokens:
                raise ParseError(f"Expected OutOfTokens but got {t}")

    def stop_strings(self) -> list[str]:
        """Return stop strings for generation."""
        return [self._hf_tokenizer.eos_token]
    
    def special_tokens(self) -> list[int]:
        """Get special tokens for this renderer."""
        if self._hf_tokenizer and hasattr(self._hf_tokenizer, 'all_special_tokens'):
            special_tokens = []
            for token in self._hf_tokenizer.all_special_tokens:
                try:
                    token_id = self._hf_tokenizer.convert_tokens_to_ids(token)
                    if isinstance(token_id, int):
                        special_tokens.append(token_id)
                    elif isinstance(token_id, list):
                        special_tokens.extend(token_id)
                except Exception:
                    continue
            return special_tokens
        return []
   
def curl_bus_engine(
    snapshot_path: str, 
    custom_tokenizer_dir: str,
    prompt: str
):
    """
    Test the bus engine with optional custom renderer for vLLM.
    
    Args:
        snapshot_path: Path to the bus snapshot
        custom_tokenizer_dir: Path to local directory with HF tokenizer files
        prompt: Custom prompt to use
    """
    
    bus_token_completer = BusTokenCompleter(
        topic_or_snapshot=snapshot_path,
    )
   
    tools = [{
    "type": "function",
    "function": {
        "name": "get_weather",
        "description": "Retrieve current weather information. Call this when the user asks about the weather.",
        "parameters": {
            "type": "object",
            "required": ["city"],
            "properties": {
                "city": {
                    "type": "string",
                    "description": "Name of the city"
                }
            }
        }
    }
    }]

    custom_renderer = VLLMCustomRenderer(local_tokenizer_dir=custom_tokenizer_dir, tools=tools)
   
    token_message_completer = TokenMessageCompleter(
        token_completer=bus_token_completer,
        renderer=custom_renderer,
    )
    # Create test conversation
    convo = chat.Conversation(
        messages=[
            chat.make_text_message(prompt, role=Role.USER),
        ],
    )

    print(f"\nQuerying with prompt: {prompt}")
    
    # Show rendered conversation for debugging if using custom renderer
    if custom_tokenizer_dir:
        print("\n" + "=" * 40)
        print("RENDERED CONVERSATION:")
        print("=" * 40)
        print(custom_renderer.render_for_display(convo))
        
        print("\n" + "=" * 40)
        print("TOKENIZATION INFO:")
        print("=" * 40)
        completion_tokens = custom_renderer.render_for_completion(convo, Role.ASSISTANT)
        print(f"Total tokens for completion: {len(completion_tokens)}")
        print(f"First 10 tokens: {completion_tokens[:10]}")
        
        # Show decoded version of first part
        if len(completion_tokens) > 0:
            preview_tokens = completion_tokens[:min(200, len(completion_tokens))]
            decoded_preview = custom_renderer.decode(preview_tokens)
            print(f"Decoded preview (first {len(preview_tokens)} tokens):")
            print(repr(decoded_preview))
        
        print("=" * 40)
    
    # Make the completion request
    print("\nSending completion request...")
    try:
        output_obj: TokenMessageCompleter.Completion = token_message_completer.completion([convo])
        messages = output_obj.singleton.get_messages()
        
        print("\n" + "=" * 40)
        print("RESPONSE:")
        print("=" * 40)
        
        for i, message in enumerate(messages):
            print(f"Message {i+1}:")
            print(f"  Role: {message.role.value}")
            print(f"  ID: {message.id}")
            print(f"  Recipient: {message.recipient}")
            print(f"  Content: {repr(str(message.content))}")
            if hasattr(message, 'metadata') and message.metadata:
                print(f"  Metadata: {message.metadata}")
            print()
        
        print("=" * 40)
        
    except Exception as e:
        print(f"Error during completion: {e}")
        import traceback
        traceback.print_exc()


def main():
    parser = argparse.ArgumentParser(
        description="Test bus engine with optional custom vLLM renderer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage with default renderer
  python request_bus_vllm.py kimi-k2-instruct

  # Use existing local tokenizer directory
  python request_bus_vllm.py --custom_tokenizer_dir="/path/to/tokenizer" kimi-k2-instruct

  # Use with piped input
  echo "What is 2+2?" | python request_bus_vllm.py kimi-k2-instruct --custom_tokenizer_dir="/path/to/tokenizer"
        """
    )
    
    parser.add_argument("bus_topic", help="Path to the bus snapshot or topic")
    parser.add_argument(
        "--custom_tokenizer_dir", 
        required=True,
        help="Path to local directory containing Hugging Face tokenizer files"
    )
    
    args = parser.parse_args()
    
    # Check if input is coming from stdin (pipe)
    prompt = "Find the sum of all integer bases $b>9$ for which $17_{b}$ is a divisor of $97_{b}$."
    if not sys.stdin.isatty():
        # Read from stdin
        prompt = sys.stdin.read().strip()
        if prompt:
            print(f"Using prompt from stdin: {prompt}")
    
    curl_bus_engine(
        snapshot_path=args.bus_topic,
        custom_tokenizer_dir=args.custom_tokenizer_dir,
        prompt=prompt
    )


if __name__ == "__main__":
    main()