"""Script for running a proxy between the bus and vLLM's HTTP server.

This proxy listens to bus requests containing token IDs and forwards them to a vLLM 
HTTP server that provides completion and health endpoints. The proxy works with tokens
directly - no tokenization is performed.

The proxy connects to an existing vLLM HTTP server.

Example usage:
    # Connect to existing vLLM server:
    python run_vllm_proxy.py kimi-k2-instruct --topic="kimi-k2-instruct"  --endpoint="http://localhost:8000"

Expected bus request format:
    # Simple flat format (recommended):
    {
        "prompt": [15339],
        "format": "tokens",
        "max_tokens": 1,
        "temperature": 1.0,
        "n": 1,
        "stop": [[200002], [200007]],
        "echo": False,
        "echo_stop": True
    }
    
    # Legacy nested format (still supported):
    {
        "engine_data": {
            "format": "tokens",
            "temperature": 1.0,
            "n": 1,
            "max_tokens": 2048,
            "stop": [[200002], [200007]],
            "echo": False,
            "echo_stop": True,
            "prompt": [[200006, 17360, 200008, ...]]  # List of token ID lists
        },
        "type": "inline"
    }
"""

import argparse
import asyncio
import logging
import uuid
import time
import threading
from dataclasses import dataclass
from typing import Any, List, Dict, Optional
from queue import Queue, Empty

import aiohttp
import bus
import bus.utils.aio.syncio as syncio
from bus.redis.proxy import BusProxy
from bus.redis.proxy.rate_limit import NoRateLimiter, RateLimiter
from bus.redis.request import IncomingRequest

# Set up logging
_logger = logging.getLogger(__name__)

DEFAULT_BATCH_SIZE = 100
DEFAULT_BATCH_TIMEOUT_MS = 1000  # 100ms timeout for batching


class VLLMHttpClient:
    """HTTP client for communicating with vLLM server."""
    
    def __init__(self, endpoint: str, model: str):
        """Initialize the HTTP client.
        
        Args:
            endpoint: The vLLM endpoint URL (e.g., "http://localhost:8000")
            model: The vLLM model name
        """
        self.endpoint = endpoint
        self.model = model
        self.completion_url = f"{self.endpoint.rstrip('/')}/v1/completions"
        self.health_url = f"{self.endpoint.rstrip('/')}/health"
        self.models_url = f"{self.endpoint.rstrip('/')}/v1/models"
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create the aiohttp session."""
        timeout = aiohttp.ClientTimeout(total=300.0)  # 5 minute timeout
        return aiohttp.ClientSession(timeout=timeout)
    
    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
    
    async def completions(self, batched_completion_request: Dict[str, Any]) -> Dict[str, Any]:
        """Send a completion request to vLLM server.
        
        Args:
            batched_completion_request: The batched completion request
            
        Returns:
            The response from vLLM server
            
        Raises:
            Exception: If the request fails
        """
        session = await self._get_session()
        
        async with session.post(
            self.completion_url,
            json=batched_completion_request
        ) as response:
            if response.status != 200:
                text = await response.text()
                raise Exception(f"vLLM server error (status {response.status}): {text}")
            
            return await response.json()
    
    async def check_readiness(self) -> bool:
        """Check if the vLLM server is ready.
        
        Returns:
            True if server is ready, False otherwise
        """
        try:
            session = await self._get_session()
            
            # Check health endpoint
            async with session.get(self.health_url) as response:
                if response.status != 200:
                    return False
            
            # Check models endpoint to ensure it's fully ready
            async with session.get(self.models_url) as models_response:
                if models_response.status == 200:
                    models_data = await models_response.json()
                    _logger.info(f"vLLM server ready with models: {[m['id'] for m in models_data.get('data', [])]}")
                    return True
            
            return False
        except Exception as e:
            _logger.debug(f"Readiness check failed: {e}")
            return False
    
    def format_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Format the vLLM response into the expected bus response format."""
        choices = []
        for i, choice in enumerate(result.get("choices", [])):
            choices.append({
                "index": i,
                "tokens": choice.get("tokens", []),
                "finish_details": {"type": "stop"}
            })
        
        return {
            "id": result.get("id", str(uuid.uuid4())),
            "object": "text_completion",
            "created": int(time.time()),
            "model": self.model,
            "choices": choices,
        }


class VLLMFakeClient:
    """Fake client for testing without vLLM server."""
    
    def __init__(self, endpoint: str, model: str):
        """Initialize the fake client.
        
        Args:
            endpoint: The vLLM endpoint URL (ignored in fake mode)
            model: The vLLM model name
        """
        self.endpoint = endpoint
        self.model = model
    
    async def close(self):
        """No-op for fake client."""
        pass
    
    async def completions(self, batched_completion_request: Dict[str, Any]) -> Dict[str, Any]:
        """Return a fake completion response.
        
        Args:
            batched_completion_request: The batched completion request
            
        Returns:
            A fake response that mimics vLLM format
        """
        prompt_token_ids = batched_completion_request.get("prompt_token_ids", [])
        
        # Create fake choices for each prompt
        choices = []
        for i, prompt in enumerate(prompt_token_ids):
            choices.append({
                "index": i,
                "tokens": prompt,  # Echo the input tokens
                "finish_details": {"type": "stop"}
            })
        
        return {
            "id": str(uuid.uuid4()),
            "object": "text_completion",
            "created": int(time.time()),
            "model": self.model,
            "choices": choices,
        }
    
    async def check_readiness(self) -> bool:
        """Always return True for fake client."""
        return True
    
    def format_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Format the response (same as real client)."""
        choices = []
        for i, choice in enumerate(result.get("choices", [])):
            choices.append({
                "index": i,
                "tokens": choice.get("tokens", []),
                "finish_details": {"type": "stop"}
            })
        
        return {
            "id": result.get("id", str(uuid.uuid4())),
            "object": "text_completion",
            "created": int(time.time()),
            "model": self.model,
            "choices": choices,
        }


@dataclass
class BatchedRequest:
    """A request that's been batched for processing."""
    request: IncomingRequest
    future: asyncio.Future
    request_id: str
    completion_request: Dict[str, Any]


class BatchProcessor:
    """A simple class that batches requests and sends HTTP requests via a background thread."""
    
    def __init__(
        self,
        endpoint: str,
        model: str,
        batch_size: int = DEFAULT_BATCH_SIZE,
        batch_timeout_ms: int = DEFAULT_BATCH_TIMEOUT_MS,
        no_vllm: bool = False,
    ):
        """Initialize the batch processor.
        
        Args:
            endpoint: The vLLM endpoint URL (e.g., "http://localhost:8000") 
            model: The vLLM model name
            batch_size: Maximum number of requests to batch together
            batch_timeout_ms: Timeout in milliseconds to wait for batching
            no_vllm: If True, run in testing mode without vLLM server connection
        """
        self.endpoint = endpoint
        self.model = model
        self.batch_size = batch_size
        self.batch_timeout_ms = batch_timeout_ms
        
        # Create the appropriate client based on no_vllm flag
        if no_vllm:
            self.client = VLLMFakeClient(endpoint, model)
            _logger.info(f"Initialized BatchProcessor with FakeClient for endpoint '{endpoint}' (model: {model})")
        else:
            self.client = VLLMHttpClient(endpoint, model)
            _logger.info(f"Initialized BatchProcessor with HttpClient for endpoint '{endpoint}' (model: {model})")
        
        # Thread-safe queue for incoming requests
        self._request_queue = Queue()
        
        # Thread for batch processing
        self._batch_thread = None
        self._stop_event = threading.Event()
        
        _logger.info(f"Initialized BatchProcessor for endpoint '{endpoint}' (model: {model})")
    
    def start(self):
        """Start the background batch processing thread."""
        if self._batch_thread is None or not self._batch_thread.is_alive():
            self._stop_event.clear()
            self._batch_thread = threading.Thread(target=self._batch_worker, daemon=True)
            self._batch_thread.start()
            _logger.info("Started batch processing thread")
    
    def stop(self):
        """Stop the background batch processing thread."""
        self._stop_event.set()
        if self._batch_thread and self._batch_thread.is_alive():
            self._batch_thread.join(timeout=5.0)
            _logger.info("Stopped batch processing thread")
    
    async def close(self):
        """Close the client session."""
        if hasattr(self.client, 'close'):
            await self.client.close()
    
    def add_request(self, completion_request: Dict[str, Any]) -> asyncio.Future:
        """Add a request to the batch queue and return a future for the result.
        
        Args:
            completion_request: The parsed completion request
            
        Returns:
            A future that will contain the response when processing is complete
        """
        future = asyncio.Future()
        request_id = str(uuid.uuid4())
        
        # Create a batched request item
        request_item = {
            'future': future,
            'request_id': request_id,
            'completion_request': completion_request
        }
        
        # Add to queue
        self._request_queue.put(request_item)
        _logger.debug(f"Added request {request_id} to batch queue")
        
        return future
    
    def _batch_worker(self):
        """Background thread worker that processes batches."""
        current_batch = []
        last_batch_time = time.time()
        
        while not self._stop_event.is_set():
            try:
                # Try to get a request with a short timeout
                timeout_seconds = self.batch_timeout_ms / 1000.0
                try:
                    request_item = self._request_queue.get(timeout=0.01)  # 10ms timeout
                    current_batch.append(request_item)
                except Empty:
                    pass
                
                current_time = time.time()
                time_since_last_batch = (current_time - last_batch_time) * 1000  # Convert to ms
                
                # Process batch if we have enough requests or timeout exceeded
                should_process = (
                    len(current_batch) >= self.batch_size or
                    (current_batch and time_since_last_batch >= self.batch_timeout_ms)
                )
                
                if should_process and current_batch:
                    self._process_batch(current_batch)
                    current_batch = []
                    last_batch_time = current_time
                    
            except Exception as e:
                _logger.error(f"Error in batch worker: {e}")
                # Set error for all requests in current batch
                if current_batch:
                    self._set_batch_error(current_batch, str(e))
                    current_batch = []
        
        # Process any remaining requests when stopping
        if current_batch:
            self._process_batch(current_batch)
    
    def _process_batch(self, batch: List[Dict[str, Any]]):
        """Process a batch of requests by sending HTTP request via client."""
        if not batch:
            return
            
        _logger.info(f"Processing batch of {len(batch)} requests")
        
        try:
            # Run the async method in the thread's event loop
            asyncio.run(self._handle_batch_with_client(batch))
        except Exception as e:
            _logger.error(f"Error processing batch: {e}")
            self._set_batch_error(batch, str(e))
    
    async def _handle_batch_with_client(self, batch: List[Dict[str, Any]]):
        """Handle a batch using the client (real or fake)."""
        # Combine all prompts into a single batched request
        all_prompt_token_ids = []
        request_indices = []  # Track which response belongs to which request
        
        for i, request_item in enumerate(batch):
            req_prompts = request_item['completion_request']["prompt_token_ids"]
            for prompt in req_prompts:
                all_prompt_token_ids.append(prompt)
                request_indices.append(i)
        
        # Use the parameters from the first request
        first_request = batch[0]['completion_request']
        
        # Create the batched completion request
        batched_completion_request = {
            "prompt_token_ids": all_prompt_token_ids,
            "max_tokens": first_request["max_tokens"],
            "temperature": first_request["temperature"],
            "top_p": first_request["top_p"],
            "n": first_request["n"],
            "stop_token_ids": first_request["stop_token_ids"],
            "echo": first_request["echo"],
            "echo_stop": first_request["echo_stop"],
        }
        
        _logger.info(f"Batched completion request with {len(all_prompt_token_ids)} prompts")
        
        # Send request via client
        result = await self.client.completions(batched_completion_request)
        _logger.debug(f"Got batched response from client")
        
        # Distribute responses back to individual requests
        self._distribute_batch_responses(batch, result, request_indices)
    
    def _distribute_batch_responses(self, batch: List[Dict[str, Any]], result: Dict[str, Any], request_indices: List[int]):
        """Distribute the batched response back to individual requests."""
        choices = result.get("choices", [])

        assert len(choices) == len(batch) 
        
        # Create individual responses for each request using client formatting
        for request_idx, request_item in enumerate(batch):
            individual_choices = choices[request_idx]
            
            # Format response for this individual request using client
            individual_response = self.client.format_response({
                "id": result.get("id", request_item['request_id']),
                "object": "text_completion",
                "created": result.get("created", int(time.time())),
                "choices": individual_choices
            })
            
            # Set result directly - futures are thread-safe for setting results
            if not request_item['future'].done():
                request_item['future'].set_result(individual_response)
    
    def _set_batch_error(self, batch: List[Dict[str, Any]], error_msg: str):
        """Set error results for all requests in the batch."""
        error_response = {
            "id": str(uuid.uuid4()),
            "object": "text_completion", 
            "created": int(time.time()),
            "model": self.model,
            "choices": [],
            "error": {
                "message": error_msg,
                "type": "internal_error",
                "code": "internal_error",
            }
        }
        for request_item in batch:
            if not request_item['future'].done():
                request_item['future'].set_result(error_response)
    
class VLLMProxy(BusProxy):
    """A BusProxy that forwards bus requests to a vLLM HTTP server.
    
    This proxy forwards requests containing token IDs to a vLLM HTTP server
    that provides completion and health endpoints. No tokenization is performed.
    This implementation uses a BatchProcessor for efficient request batching.
    """
    
    def __init__(
        self,
        topic: str,
        endpoint: str,
        model: str,
        batch_size: int = DEFAULT_BATCH_SIZE,
        batch_timeout_ms: int = DEFAULT_BATCH_TIMEOUT_MS,
        rate_limiter: RateLimiter | None = None,
        no_vllm: bool = False,
    ) -> None:
        """Initialize the vLLM proxy.
        
        Args:
            topic: The bus topic to listen to
            endpoint: The vLLM endpoint URL (e.g., "http://localhost:8000") 
            model: The vLLM model name
            batch_size: Maximum number of requests to batch together
            batch_timeout_ms: Timeout in milliseconds to wait for batching
            rate_limiter: Optional rate limiter for controlling request flow
            no_vllm: If True, run in testing mode without vLLM server connection
        """
        super().__init__(topic, batch_size=batch_size)
        self.endpoint = endpoint
        self.model = model
        self.rate_limiter = rate_limiter or NoRateLimiter()
        self.no_vllm = no_vllm
        
        # Initialize the batch processor
        self.batch_processor = BatchProcessor(
            endpoint=endpoint,
            model=model,
            batch_size=batch_size,
            batch_timeout_ms=batch_timeout_ms,
            no_vllm=no_vllm
        )
        
        if self.no_vllm:
            _logger.info(f"Initialized vLLM proxy in TEST MODE for topic '{topic}' (model: {model})")
        else:
            _logger.info(f"Initialized vLLM proxy for topic '{topic}' -> endpoint '{endpoint}' (model: {model})")
    
    async def _close_session(self):
        """Clean up the batch processor."""
        if hasattr(self, 'batch_processor'):
            self.batch_processor.stop()
            await self.batch_processor.close()
    
    @syncio.run_in_global_loop
    async def handle_request(self, request: IncomingRequest) -> Any:
        """Handle an incoming bus request by adding it to the batch processor.
        
        This method processes each request individually but batches them internally
        for efficient processing via the vLLM HTTP server.
        """
        request_data = request.request_data
        _logger.debug(f"Processing request: {request_data}")
        
        try:
            # Parse and validate the request first
            completion_request = self._parse_request(request_data)
            
            # Ensure batch processor is started
            if not hasattr(self, '_batch_processor_started'):
                self.batch_processor.start()
                self._batch_processor_started = True
            
            # Add request to batch processor and wait for result
            future = self.batch_processor.add_request(completion_request)
            return await future

        except Exception as e:
            _logger.error(f"Error processing request: {e}")
            # Return error response in expected format
            return {
                "id": str(uuid.uuid4()),
                "object": "text_completion",
                "created": int(time.time()),
                "model": self.model,
                "choices": [],
                "error": {
                    "message": str(e),
                    "type": "internal_error",
                    "code": "internal_error",
                }
            }

    def _parse_request(self, request_data: Any) -> Dict[str, Any]:
        """Parse and validate a single request into a completion request format."""
        if not isinstance(request_data, dict):
            raise ValueError(f"Expected dict request data, got {type(request_data)}")
        
        # Check if this is a flat format or nested format
        if "engine_data" in request_data:
            # Legacy nested format
            engine_data = request_data.get("engine_data", {})
            if not engine_data:
                raise ValueError("Missing 'engine_data' in request")
            params_source = engine_data
        else:
            # New flat format
            params_source = request_data
        
        # Get prompt token IDs
        prompt_token_ids = params_source.get("prompt")
        if not prompt_token_ids:
            raise ValueError("Missing 'prompt' in request")
        
        # Handle both single list [15339] and list of lists [[1, 2, 3, ...]] formats
        if isinstance(prompt_token_ids, list):
            if prompt_token_ids and isinstance(prompt_token_ids[0], int):
                # Single list format: [15339] -> convert to list of lists
                prompt_token_ids = [prompt_token_ids]
            elif not all(isinstance(p, list) for p in prompt_token_ids):
                raise ValueError("'prompt' must be a list of integers or a list of lists of integers")
        else:
            raise ValueError("'prompt' must be a list of integers or a list of lists of integers")
        
        # Process stop tokens
        stop_tokens = params_source.get("stop")
        processed_stop = None
        if stop_tokens:
            if not isinstance(stop_tokens, list):
                raise ValueError("'stop' must be a list of lists of integers")
            # Check if it's a list of lists (token ID format)
            if not all(isinstance(s, list) for s in stop_tokens):
                raise ValueError("'stop' must be a list of lists of integers")
            if not all(len(s) == 1 for s in stop_tokens):
                raise ValueError("Each stop token list must contain exactly 1 token")
            # Convert to List[int] format
            processed_stop = [s[0] for s in stop_tokens]
        
        # Extract sampling parameters with defaults - note this is for a single prompt
        return {
            "prompt_token_ids": prompt_token_ids,
            "max_tokens": params_source.get("max_tokens", 100),
            "temperature": params_source.get("temperature", 1.0),
            "top_p": params_source.get("top_p", 1.0),
            "n": params_source.get("n", 1),
            "stop_token_ids": processed_stop,
            "echo": params_source.get("echo", False),
            "echo_stop": params_source.get("echo_stop", False),
        }

    @syncio.run_in_global_loop
    async def _wait_for_target_aliveness(self) -> None:
        """Function that blocks for the target to be alive."""
        if hasattr(self, 'batch_processor') and isinstance(self.batch_processor.client, VLLMFakeClient):
            _logger.info("TEST MODE: Skipping vLLM server health check")
            return
            
        _logger.info(f"Checking if {self.endpoint} is alive")
        
        max_attempts = 30  # Try for up to 5 minutes (30 * 10 seconds)
        attempt = 0
        
        while attempt < max_attempts:
            try:
                # Use client's check_readiness method
                is_ready = await self.batch_processor.client.check_readiness()
                
                if is_ready:
                    _logger.info(f"{self.endpoint} is ready")
                    return
                else:
                    _logger.debug(f"Readiness check attempt {attempt + 1} failed")
                    
            except Exception as e:
                _logger.debug(f"Health check attempt {attempt + 1} failed: {e}")
            
            attempt += 1
            if attempt < max_attempts:
                _logger.info(f"Waiting for vLLM server to be ready... (attempt {attempt}/{max_attempts})")
                await asyncio.sleep(10)
        
        error_msg = f"vLLM server at {self.endpoint} did not become ready after {max_attempts} attempts"
        _logger.error(error_msg)
        raise Exception(error_msg)

async def _amain(*, topic: str, endpoint: str, model: str, batch_size: int = DEFAULT_BATCH_SIZE, batch_timeout_ms: int = DEFAULT_BATCH_TIMEOUT_MS, no_vllm: bool = False) -> None:
    """Async main function for the vLLM proxy.
    
    Args:
        topic: The bus topic to subscribe to
        endpoint: The vLLM endpoint URL
        model: The vLLM model name
        batch_size: Maximum number of requests to batch
        batch_timeout_ms: Timeout in milliseconds to wait for batching
        no_vllm: If True, run in testing mode without vLLM server connection
    """
    if no_vllm:
        _logger.info(f"Starting vLLM proxy in TEST MODE for topic '{topic}' (model: {model})")
    else:
        _logger.info(f"Starting vLLM proxy for topic '{topic}' -> endpoint '{endpoint}' (model: {model})")
    
    proxy = VLLMProxy(
        topic=topic,
        endpoint=endpoint,
        model=model,
        batch_size=batch_size,
        batch_timeout_ms=batch_timeout_ms,
        no_vllm=no_vllm,
    )
    
    try:
        await proxy.arun()
    finally:
        # Clean up (no session to close since we're using requests)
        await proxy._close_session()


def main():
    logging.basicConfig(level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    parser = argparse.ArgumentParser(
        description="Run a proxy between the bus and vLLM's HTTP server interface"
    )
    parser.add_argument('model', type=str, help="The vLLM model to run (e.g., 'kimi-k2-instruct')")
    parser.add_argument(
        "--topic",
        required=True,
        help="Bus topic to subscribe to (e.g., 'bus:embedding:my-topic')"
    )
    parser.add_argument(
        "--endpoint", 
        default="http://localhost:8000",
        help="vLLM endpoint URL (e.g., 'http://localhost:8000')"
    )
    parser.add_argument(
        "--batch_size",
        type=int,
        default=DEFAULT_BATCH_SIZE,
        help=f"Maximum batch size (default: {DEFAULT_BATCH_SIZE})"
    )
    parser.add_argument(
        "--batch_timeout_ms",
        type=int,
        default=DEFAULT_BATCH_TIMEOUT_MS,
        help=f"Batch timeout in milliseconds (default: {DEFAULT_BATCH_TIMEOUT_MS})"
    )
    parser.add_argument('--no-vllm', action='store_true', help="Disable vLLM server connection (for testing)")
    
    args = parser.parse_args()
     # Initialize bus
    bus.init()
    
    # Run the async main function
    syncio.run_in_global_loop(
        _amain(
        topic=args.topic,
        endpoint=args.endpoint,
        model=args.model,
        batch_size=args.batch_size,
        batch_timeout_ms=args.batch_timeout_ms,
        no_vllm=args.no_vllm
        )
    )


if __name__ == "__main__":
    main()