
import sys
import argparse

from bus_token_completer.bus_token_completer import Bus<PERSON>oken<PERSON>ompleter
from message_completer.token_message_completer import TokenMessageCompleter

from chat import chat
from chat.render import get_renderer

MODEL_IDENTITY_DESC = "You are <PERSON><PERSON>, a helpful AI assistant that can interact with a computer to solve tasks."
  
def request_bus(
    topic_or_snapshot: str, 
    renderer_name: str,
    prompt: str
):
    """
    Test the bus engine with optional custom renderer for vLLM.
    
    Args:
        snapshot_path: Path to the bus snapshot
        custom_tokenizer_dir: Path to local directory with HF tokenizer files
        prompt: Custom prompt to use
    """
    
    bus_token_completer = BusTokenCompleter(topic_or_snapshot=topic_or_snapshot)
   
    tools = {
        "get_weather": '{"description": "Retrieve current weather information. Call this when the user asks about the weather.", "parameters": {"type": "object", "required": ["city"], "properties": {"city": {"type": "string", "description": "Name of the city"}}}}'
    }

    renderer = get_renderer(renderer_name)
    token_message_completer = TokenMessageCompleter(
        token_completer=bus_token_completer,
        renderer=renderer_name,
    )
    # Create test conversation
    convo = chat.Conversation(
        messages=[
            chat.Message.system(model_identity_desc=MODEL_IDENTITY_DESC, tools_section=tools),
            chat.make_text_message(prompt, role=chat.Role.USER),
            # chat.Message.assistant('{"city": "Tokyo"}', recipient='get_weather'),
            # chat.Message.tool("Sunny, 25°C", author_name="get_weather"),
            # chat.Message.user("Is it hot in Tokyo?"),
        ],
    )

    completion_toklist = renderer.render_for_completion_multimodal_toklist(convo, chat.Role.ASSISTANT)
    completion_tokens = completion_toklist.get_ints()

    print("\n" + "=" * 20 + " REQUEST " + "=" * 20 + "\n") 
    print(convo)
    print(f"Raw tokens (len={len(completion_tokens)}): {completion_tokens}")
    print(f"Decoded tokens: {repr(renderer.decode(completion_tokens))}")
    
    print("=" * 40)

    # Make the completion request
    print("\nSending completion request...")
    output_obj: TokenMessageCompleter.Completion = token_message_completer.completion([convo])
    messages = output_obj.singleton.get_messages()
        
    print("\n" + "=" * 20 + " RESPONSE " + "=" * 20 + "\n") 
    for message in messages:
        print(message)

    tokens = output_obj.singleton.completion_tokens
    print(f"Raw completion tokens (len={len(tokens)}): {tokens}")
    print(f"Decoded completion: {repr(renderer.decode(tokens))}")


def main():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage with default renderer
  python request_bus_vllm.py kimi-k2-instruct

  # Use with piped input
  echo "What is 2+2?" | python request_bus_vllm.py kimi-k2-instruct --custom_tokenizer_dir="/path/to/tokenizer"
        """
    )
    
    parser.add_argument("bus_topic_or_snapshot", help="Path to the bus snapshot or topic")
    parser.add_argument(
        "--renderer_name", "-r", 
        required=True,
        help="Name of the renderer to use"
    )
    
    args = parser.parse_args()
    
    # Check if input is coming from stdin (pipe)
    prompt = "Find the sum of all integer bases $b>9$ for which $17_{b}$ is a divisor of $97_{b}$."
    if not sys.stdin.isatty():
        # Read from stdin
        prompt = sys.stdin.read().strip()
        if prompt:
            print(f"Using prompt from stdin: {prompt}")
    
    request_bus(
        topic_or_snapshot=args.bus_topic_or_snapshot,
        renderer_name=args.renderer_name,
        prompt=prompt
    )


if __name__ == "__main__":
    main()