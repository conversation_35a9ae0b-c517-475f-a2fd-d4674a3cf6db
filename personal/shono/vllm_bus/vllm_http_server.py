"""HTTP server for vLLM that provides completion and health endpoints.

This server exposes vLLM functionality through HTTP endpoints and works with tokens directly.
No tokenization is performed - the server expects and returns token IDs.

Example usage:
    python vllm_http_server.py kimi-k2-instruct --tensor_parallel_size=8 --pipeline_parallel_size=2
"""

import argparse
import asyncio
import logging
import time
import traceback
import uuid
from typing import List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from vllm.engine.async_llm_engine import AsyncLLMEngine
from vllm.engine.arg_utils import AsyncEngineArgs
from vllm.sampling_params import SamplingParams


# Set up logging
_logger = logging.getLogger(__name__)


class CompletionRequest(BaseModel):
    """Request model for completion endpoint."""
    prompt_token_ids: List[List[int]]
    max_tokens: int = 100
    temperature: float = 1.0
    top_p: float = 1.0
    n: int = 1
    stop_token_ids: Optional[List[int]] = None
    echo: bool = False


class CompletionChoice(BaseModel):
    """Single completion choice."""
    index: int
    tokens: List[int]
    finish_reason: str


class CompletionResponse(BaseModel):
    """Response model for completion endpoint."""
    id: str
    object: str = "text_completion"
    created: int
    model: str
    choices: List[List[CompletionChoice]]  # 2D list: [prompt_idx][completion_idx]
    usage: dict


class HealthResponse(BaseModel):
    """Response model for health endpoint."""
    status: str = "healthy"


class VLLMServer:
    """HTTP server for vLLM with token-based interface."""
    
    def __init__(self, model: str, tensor_parallel_size: int = 1, pipeline_parallel_size: int = 1, data_parallel_size: int = 1, max_model_len: Optional[int] = None, max_num_seqs: Optional[int] = None, enable_expert_parallel: bool = False):
        """Initialize the vLLM server.
        
        Args:
            model: The name of the vLLM model to load
            tensor_parallel_size: Number of tensor parallel workers
            pipeline_parallel_size: Number of pipeline parallel stages
            data_parallel_size: Number of data parallel workers
            max_model_len: Maximum model sequence length
            max_num_seqs: Maximum number of sequences to process in parallel
            enable_expert_parallel: Enable expert parallelism
        """
        self.model_name = model
        self.tensor_parallel_size = tensor_parallel_size
        self.pipeline_parallel_size = pipeline_parallel_size
        self.data_parallel_size = data_parallel_size
        self.max_model_len = max_model_len
        self.max_num_seqs = max_num_seqs
        self.enable_expert_parallel = enable_expert_parallel
        self.engine = None
        self.app = FastAPI(title="vLLM HTTP Server", version="1.0.0")
        self._setup_routes()
        
    def _setup_routes(self):
        """Set up HTTP routes."""
        
        @self.app.post("/v1/completions", response_model=CompletionResponse)
        async def completions(request: CompletionRequest) -> CompletionResponse:
            """Generate completions from token IDs."""
            if self.engine is None:
                raise HTTPException(status_code=503, detail="Model not loaded")
            
            # Validate request
            if not request.prompt_token_ids:
                raise HTTPException(status_code=400, detail="prompt_token_ids cannot be empty")
            
            # Ensure all prompts are valid
            for i, prompt in enumerate(request.prompt_token_ids):
                if not isinstance(prompt, list):
                    raise HTTPException(status_code=400, detail=f"prompt_token_ids[{i}] must be a list of integers")
                if not prompt:
                    raise HTTPException(status_code=400, detail=f"prompt_token_ids[{i}] cannot be empty")
                if not all(isinstance(token_id, int) for token_id in prompt):
                    raise HTTPException(status_code=400, detail=f"prompt_token_ids[{i}] must contain only integers")
            
            try:
                # Create sampling parameters
                sampling_params = SamplingParams(
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    top_p=request.top_p,
                    n=request.n,
                    stop_token_ids=request.stop_token_ids,
                    detokenize=False,  # Important: work with tokens directly
                )
                
                # Generate completions for each prompt using AsyncLLMEngine
                _logger.info(f"Processing batch of {len(request.prompt_token_ids)} prompts")
                _logger.debug(f"Prompt token IDs: {request.prompt_token_ids}")
                _logger.debug(f"Sampling params: {sampling_params}")

                # Generate tasks for all prompts
                completion_tasks = []
                for prompt_idx, prompt_tokens in enumerate(request.prompt_token_ids):
                    # Create a unique request ID for each prompt
                    request_id = f"req-{uuid.uuid4().hex}-{prompt_idx}"
                    
                    # Create a task for this prompt
                    task = asyncio.create_task(
                        self._generate_single_prompt(
                            prompt_tokens, 
                            sampling_params, 
                            request_id,
                            prompt_idx
                        )
                    )
                    completion_tasks.append(task)
                
                # Wait for all completions
                all_prompt_results = await asyncio.gather(*completion_tasks)
                
                _logger.info(f"Generated results for {len(all_prompt_results)} prompts")
                
                # Format response as 2D list: choices[prompt_idx][completion_idx]
                choices = []
                total_completion_tokens = 0
                
                for prompt_idx, prompt_results in enumerate(all_prompt_results):
                    _logger.debug(f"Processing results for prompt {prompt_idx} with {len(prompt_results)} completions")
                    prompt_choices = []
                    
                    for completion_idx, result in enumerate(prompt_results):
                        choice = CompletionChoice(
                            index=completion_idx,  # Index within this prompt's completions
                            tokens=result['tokens'],
                            finish_reason=result['finish_reason']
                        )
                        prompt_choices.append(choice)
                        total_completion_tokens += len(result['tokens'])
                    
                    choices.append(prompt_choices)
                
                _logger.info(f"Created {len(choices)} prompt groups with {sum(len(pc) for pc in choices)} total choices")
                
                # Calculate usage (simplified)
                total_prompt_tokens = sum(len(prompt) for prompt in request.prompt_token_ids)
                
                return CompletionResponse(
                    id=f"cmpl-{uuid.uuid4().hex}",
                    created=int(time.time()),
                    model=self.model_name,
                    choices=choices,
                    usage={
                        "prompt_tokens": total_prompt_tokens,
                        "completion_tokens": total_completion_tokens,
                        "total_tokens": total_prompt_tokens + total_completion_tokens,
                    }
                )
                
            except Exception as e:
                error_traceback = traceback.format_exc()
                _logger.error(f"Error generating completion: {e}\n{error_traceback}")
                raise HTTPException(status_code=500, detail=f"{str(e)}\n\nTraceback:\n{error_traceback}")
        
        @self.app.get("/health", response_model=HealthResponse)
        async def health() -> HealthResponse:
            """Health check endpoint."""
            if self.engine is None:
                raise HTTPException(status_code=503, detail="Model not loaded")
            return HealthResponse()
        
        @self.app.get("/v1/models")
        async def models() -> dict:
            """List available models."""
            return {
                "object": "list",
                "data": [
                    {
                        "id": self.model_name,
                        "object": "model",
                        "created": int(time.time()),
                        "owned_by": "openai",
                    }
                ]
            }
    
    async def _generate_single_prompt(self, prompt_tokens: List[int], sampling_params: SamplingParams, request_id: str, prompt_idx: int) -> List[dict]:
        """Generate completions for a single prompt using AsyncLLMEngine."""
        results = []
        
        # Create the prompt input format expected by AsyncLLMEngine
        prompt_input = {"prompt_token_ids": prompt_tokens}
        
        try:
            # Generate using AsyncLLMEngine - this returns an async generator
            async for request_output in self.engine.generate(
                prompt=prompt_input,
                sampling_params=sampling_params,
                request_id=request_id
            ):
                # Process the final outputs when generation is complete
                if request_output.finished:
                    for output in request_output.outputs:
                        # Get token IDs from the completion output
                        token_ids = output.token_ids if output.token_ids else []
                        
                        result = {
                            'tokens': token_ids,
                            'finish_reason': output.finish_reason or "stop"
                        }
                        results.append(result)
                    break  # We got our final result
            
        except Exception as e:
            _logger.error(f"Error generating completion for prompt {prompt_idx}: {e}")
            # Return an empty result on error
            results.append({
                'tokens': [],
                'finish_reason': 'error'
            })
        
        return results
    
    async def load_model(self):
        """Load the vLLM model."""
        try:
            _logger.info(f"Loading AsyncLLMEngine for model '{self.model_name}' with TP={self.tensor_parallel_size}, PP={self.pipeline_parallel_size}, DP={self.data_parallel_size}, EP={self.enable_expert_parallel}")
            
            # Create AsyncEngineArgs
            engine_args = AsyncEngineArgs(
                model=self.model_name,
                tensor_parallel_size=self.tensor_parallel_size,
                pipeline_parallel_size=self.pipeline_parallel_size,
                data_parallel_size=self.data_parallel_size,
                enable_expert_parallel=self.enable_expert_parallel,
                max_model_len=self.max_model_len,
                max_num_seqs=self.max_num_seqs,
                # skip_tokenizer_init=True  # Work with tokens directly  # Skipping tokenizer init causes an exception in some case.
            )
            
            # Create AsyncLLMEngine from args
            self.engine = AsyncLLMEngine.from_engine_args(engine_args)
            
            _logger.info(f"Successfully loaded AsyncLLMEngine for model '{self.model_name}'")
            
        except Exception as e:
            _logger.error(f"Failed to load AsyncLLMEngine for model '{self.model_name}': {e}")
            raise
    
    def get_app(self) -> FastAPI:
        """Get the FastAPI application."""
        return self.app


async def create_server(model: str, tensor_parallel_size: int = 1, pipeline_parallel_size: int = 1, data_parallel_size: int = 1, max_model_len: Optional[int] = None, max_num_seqs: Optional[int] = None, enable_expert_parallel: bool = False) -> VLLMServer:
    """Create and initialize vLLM server."""
    server = VLLMServer(model, tensor_parallel_size, pipeline_parallel_size, data_parallel_size, max_model_len, max_num_seqs, enable_expert_parallel)
    await server.load_model()
    return server


def main():
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    """Main function to run the vLLM HTTP server."""
    parser = argparse.ArgumentParser(
        description="Run a vLLM HTTP server with token-based interface using AsyncLLMEngine"
    )
    parser.add_argument(
        "model", 
        type=str, 
        help="The vLLM model to run (e.g., 'kimi-k2-instruct')"
    )
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to bind the server to (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to (default: 8000)"
    )
    parser.add_argument(
        "--tensor-parallel-size",
        type=int,
        default=1,
        help="Number of tensor parallel workers (default: 1)"
    )
    parser.add_argument(
        "--pipeline-parallel-size",
        type=int,
        default=1,
        help="Number of pipeline parallel stages (default: 1)"
    )
    parser.add_argument(
        "--max-model-len",
        type=int,
        default=None,
        help="Maximum model sequence length (default: None, uses model's default)"
    )
    parser.add_argument(
        "--max-num-seqs",
        type=int,
        default=None,
        help="Maximum number of sequences to process in parallel (default: None, uses vLLM's default)"
    )
    parser.add_argument(
        "--data-parallel-size",
        type=int,
        default=1,
        help="Number of data parallel workers (default: 1)"
    )
    parser.add_argument(
        "--enable-expert-parallel",
        action="store_true",
        help="Enable expert parallelism (default: False)"
    )
    
    args = parser.parse_args()

    # Remove trailing slashes from model name
    model = args.model.rstrip('/')

    # Create server with model loading
    async def run_server():
        server = await create_server(
            model=model,
            tensor_parallel_size=args.tensor_parallel_size,
            pipeline_parallel_size=args.pipeline_parallel_size,
            data_parallel_size=args.data_parallel_size,
            max_model_len=args.max_model_len,
            max_num_seqs=args.max_num_seqs,
            enable_expert_parallel=args.enable_expert_parallel
        )
        
        config = uvicorn.Config(
            app=server.get_app(),
            host=args.host,
            port=args.port,
            log_level="info"
        )
        server_instance = uvicorn.Server(config)
        await server_instance.serve()
    
    # Run the server
    asyncio.run(run_server())


if __name__ == "__main__":
    main()
