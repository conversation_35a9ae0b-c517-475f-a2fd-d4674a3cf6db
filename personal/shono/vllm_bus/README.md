# vLLM Bus System

A three-component system for running vLLM models through a message bus architecture. This system provides token-based completion services where requests are sent through a bus proxy to a vLLM HTTP server.

## Architecture Overview

```
[request_bus_vllm.py] --> [Bus] --> [run_vllm_bus_proxy.py] --> [vllm_http_server.py]
     (Client)                           (Bus Proxy)              (vLLM Server)
```

The system consists of three main components:

1. **vLLM HTTP Server** (`vllm_http_server.py`) - Runs the actual vLLM model and provides HTTP endpoints
2. **Bus Proxy** (`run_vllm_bus_proxy.py`) - Bridges between the message bus and the vLLM HTTP server
3. **Request Client** (`request_bus_vllm.py`) - Sends completion requests through the bus system

## Quick Start

### Step 1: Start the vLLM HTTP Server

First, start the vLLM HTTP server with your desired model:

```bash
python vllm_http_server.py kimi-k2-instruct \
    --host 0.0.0.0 \
    --port 8000 \
    --tensor-parallel-size 1
```

**Available options:**
- `model` (required): The vLLM model to run (e.g., 'kimi-k2-instruct')
- `--host`: Host to bind the server to (default: 0.0.0.0)
- `--port`: Port to bind the server to (default: 8000)
- `--tensor-parallel-size`: Number of tensor parallel workers (default: 1)
- `--pipeline-parallel-size`: Number of pipeline parallel stages (default: 1)
- `--max-model-len`: Maximum model sequence length (default: model's default)
- `--max-num-seqs`: Maximum number of sequences to process in parallel (default: vLLM's default)

### Step 2: Start the Bus Proxy

In a new terminal, start the bus proxy that connects the message bus to your vLLM server:

```bash
python run_vllm_bus_proxy.py kimi-k2-instruct \
    --topic "kimi-k2-instruct" \
    --endpoint "http://localhost:8000"
```

**Required arguments:**
- `model`: The vLLM model name (must match the server model)
- `--topic`: Bus topic to subscribe to (e.g., 'bus:completion:my-topic')
- `--endpoint`: vLLM server URL (e.g., 'http://localhost:8000')

**Optional arguments:**
- `--batch_size`: Maximum batch size for processing requests (default: 8)

### Step 3: Send Requests

Now you can send completion requests using the client:

```bash
# Basic usage
python request_bus_vllm.py snapshot_path

# With custom tokenizer directory
python request_bus_vllm.py --custom_tokenizer_dir="/path/to/tokenizer" snapshot_path

# With piped input
echo "What is 2+2?" | python request_bus_vllm.py snapshot_path
```

**Arguments:**
- `snapshot_path` (required): Path to the bus snapshot or topic
- `--custom_tokenizer_dir`: Path to local directory containing Hugging Face tokenizer files

## Request Format

The bus proxy accepts requests in two formats:

### Simple Format (Recommended)
```json
{
    "prompt": [15339],
    "format": "tokens",
    "max_tokens": 1,
    "temperature": 1.0,
    "n": 1,
    "stop": [[200002], [200007]],
    "echo": false,
    "echo_stop": true
}
```

### Legacy Format (Still Supported)
```json
{
    "engine_data": {
        "format": "tokens",
        "temperature": 1.0,
        "n": 1,
        "max_tokens": 2048,
        "stop": [[200002], [200007]],
        "echo": false,
        "echo_stop": true,
        "prompt": [[200006, 17360, 200008]]
    },
    "type": "inline"
}
```

## HTTP API Endpoints

The vLLM HTTP server provides the following endpoints:

### POST /v1/completions
Generate completions from token IDs.

**Request Body:**
```json
{
    "prompt_token_ids": [[200006, 17360, 200008]],
    "max_tokens": 100,
    "temperature": 1.0,
    "top_p": 1.0,
    "n": 1,
    "stop_token_ids": [200002, 200007],
    "echo": false
}
```

**Response:**
```json
{
    "id": "completion-uuid",
    "object": "text_completion",
    "created": **********,
    "model": "kimi-k2-instruct",
    "choices": [
        {
            "index": 0,
            "tokens": [12345, 67890],
            "finish_reason": "stop"
        }
    ]
}
```

### GET /health
Health check endpoint.

**Response:**
```json
{
    "status": "healthy",
    "model": "kimi-k2-instruct"
}
```
# Troubleshooting

### Common Issues

1. **Server fails to start**: Check if the model path is correct and the model is compatible with vLLM
2. **Bus proxy can't connect**: Verify the endpoint URL and ensure the HTTP server is running
3. **Token format errors**: Ensure requests use proper token ID arrays, not strings
4. **Memory issues**: Adjust `--max-model-len` and `--max-num-seqs` parameters

## Example Workflow

Here's a complete example of setting up and using the system:

```bash
# Terminal 1: Start vLLM server
python vllm_http_server.py kimi-k2-instruct --port 8000

# Terminal 2: Start bus proxy
python run_vllm_bus_proxy.py kimi-k2-instruct \
    --topic "bus:completion:test" \
    --endpoint "http://localhost:8000"

# Terminal 3: Send a request
echo "Hello, how are you?" | python request_bus_vllm.py test_snapshot
```

This will process the input through the entire pipeline and return the model's completion.
