# mopub - Model Publishing Tool

mopub is a command-line tool that automates the model checkpoint publishing process, enabling a streamlined one-command workflow for publishing model checkpoints to Nimbus.

## Overview

The mopub tool simplifies the complex process of converting model checkpoints from TensorCache (TC) format to Inference Exchange Format (IXF) and publishing them through the proper channels. It handles the entire pipeline from checkpoint conversion to Pull Request creation, making model deployment accessible through a single command.

### Usage

Execute the publishing command with the required parameters:

```bash
./run.sh \
  --model_name="<model_name>" \
  --falcon_path="<checkpoint_path>" \
  --export_dir="<export_directory>" \
  --override "<space-separated key=value pairs>"
```

```
BRIX_QUOTA=team-moonfire-genaicore OAIPKG_OVERRIDE_WHEEL=unsafe_skip RUST_BACKTRACE=1 \
    twdev create-ray-devbox \
    cluster=prod-southcentralus-hpe-3 \
    num_pods=4 \
    setup_twapi=True \
    launch_cache_svc_daemon=True \
    num_gpu=8 \
    job_name=chkpt-conversion \
    priority_class=team-critical

python prepare_snapshot.py \
--model_name="falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal" \
--falcon_path="az://orngscuscresco/twapi/mini/e/damajercak-mix17-pdw2-ev3-itc-256-mixed-spi-gpt5-lr1e-5-run-20250828-185604/policy/step_000400/250901083240WNHLTA4C-0/" \
--export_dir="mix16-arm-4-gpt5-s400-cl-damajercak-0828-depth4-2" \
--override "force_fp8=False"


./run.sh \
  --model_name="falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal" \
  --falcon_path="az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-4-pdw2-ev3-mixed-itc-spi32-gpt5-100steps-tef03-5-tpm1-rm-lr1e-5-run-20250821-190842/policy/step_000110/" \
  --export_dir="mix16-arm-4-gpt5-s110-cl-damajercak-0822-depth4-2" \
  --override "skip_falcon_comparison=False"
```

**Parameters:**

- `--model_name`: The name of the model to export
- `--falcon_path`: Azure blob storage path to your model checkpoint (must start with `az://`)
- `--export_dir`: Name of the directory where the converted model will be stored
- `--override`: (Optional) Override configuration string with space-separated key=value pairs

**Example:**

```bash
./run.sh \
  --model_name="swe-agent-mini-2" \
  --falcon_path="az://orngcresco/path/to/tc/weights" \
  --export_dir="export-dir" \
  --override model="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=4 pipe_depth=2 n_replicas=1"
```

### Workflow Steps

The mopub workflow consists of the following automated steps:

#### 1. Model Conversion

- Creates a Ray devbox cluster for processing
- Converts model weights from TC to IXF format
- Generates manifest files for deployment

#### 2. Notification

- Sends a notification to the "Model Publish Notification Only" Teams channel
- Includes the snapshot path and next steps command
- Mentions relevant team members

#### 3. Pull Request Creation

After receiving the Teams notification, execute the provided command:

```bash
~/code/glass/personal/lixiaoli/mopub/publish.sh <snapshot_name> <openai_user> <snapshot_file>
```

This command will:

- Create a new branch in the `oai-engine-configs` repository
- Generate a model ledger JSON file
- Create a Pull Request for review

#### 4. Nimbus Deployment

Once the PR is merged, it automatically triggers a pipeline that onboards the model to Nimbus.

### Create AOAI Deployment

After the model is successfully onboarded to Nimbus through the automated pipeline, you need to manually create an Azure OpenAI (AOAI) deployment to make the model accessible for use.

**Process Overview:**

1. **Obtain Asset ID**: Navigate to the Nimbus user interface and retrieve the Asset ID for your newly onboarded model
2. **Create AOAI Deployment**: Use the Asset ID to create an AOAI deployment following the official deployment guide
3. **Follow Documentation**: Reference the specific deployment guide at: [Orange Model Deployment Guide](https://dev.azure.com/project-argos/Mimco/_wiki/wikis/Mackenzie.wiki/8383/Orange-Model-Deployment-Guide?anchor=%F0%9F%9B%AB-model-deployment-using-ptu-c) or [SWE PTUC deployment](https://microsoft.sharepoint.com/:fl:/s/c7d610de-7039-4ddc-9274-577c06a9724d/EZwxDl_zDeNFjooqIWRRZUwB76dcd-PJFx6wa-xG1bnUyA?e=MsGpti&nav=cz0lMkZzaXRlcyUyRmM3ZDYxMGRlLTcwMzktNGRkYy05Mjc0LTU3N2MwNmE5NzI0ZCZkPWIlMjE5Z2lLNzBQUFdVeWRMMXVpa2JESkh2RDV4UEVNckI1Smctelc3S3ViV2dvMm1GS29IZlVNUTdMN1N3SURNSU55JmY9MDE1M1FFRkhVNEdFSEY3NFlONE5DWTVDUktFRlNGQ1pLTSZjPSUyRiZhPUxvb3BBcHAmeD0lN0IlMjJ3JTIyJTNBJTIyVDBSVFVIeHRhV055YjNOdlpuUXVjMmhoY21Wd2IybHVkQzVqYjIxOFlpRTVaMmxMTnpCUVVGZFZlV1JNTVhWcGEySkVTa2gyUkRWNFVFVk5ja0kxU21jdGVsYzNTM1ZpVjJkdk1tMUdTMjlJWmxWTlVUZE1OMU4zU1VSTlNVNTVmREF4TlROUlJVWklWRTlJVWs0MVVFd3lNazVXUVV0SE5FYzFUVTAxV1V4Rk5FTSUzRCUyMiUyQyUyMmklMjIlM0ElMjI4NGI4ZmIwMC1jYWJkLTRhOGQtYmE1Ni1hMDI5YjI3ZDFkNjMlMjIlN0Q%3D)

**Why This Step Is Manual:**

This step is not included in the automation for two important reasons:

- **Environment Requirements**: It requires logging into a green devbox environment
- **Resource Coordination**: Due to limited GPU resources and potential ongoing tests, coordination with Poornima is necessary before proceeding

**⚠️ Important Note:** Always contact Poornima before creating deployments to avoid resource conflicts and ensure proper coordination with ongoing activities.

### Cluster Configuration

The tool uses the following default cluster settings:

- **Cluster**: `prod-southcentralus-hpe-3`
- **Priority Class**: `team-critical`
- **GPUs**: 8 per job
- **Pods**: 1

## File Structure

```
mopub/
├── run.sh                    # Main entry point
├── convert.sh               # Conversion orchestration script
├── prepare_snapshot.py      # Core conversion logic
├── export_inference.py      # Model export functionality
├── notify.py               # Teams notification handler
├── webhook.py              # Teams webhook implementation
├── publish.sh              # PR creation script
└── tests/                  # Test files
    └── test_caas.py
```

## Troubleshooting

### Conversion Failures

- Check the `prepare_snapshot.log` file for detailed error messages
- Verify your checkpoint path is correct and accessible

### Log Files

- `mopub.log`: Main execution log
- `prepare_snapshot.log`: Detailed conversion process log

## Contributing

When contributing to mopub:

1. Test your changes thoroughly with non-production models
2. Update documentation for any new features
3. Follow existing code style and patterns
4. Add appropriate error handling and logging

## License

This tool is for internal use within the organization. Follow the company's policies regarding code usage and distribution.
