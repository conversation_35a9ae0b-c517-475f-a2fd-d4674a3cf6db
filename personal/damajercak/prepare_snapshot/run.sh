#!/bin/bash

# Parse command line arguments
MODEL_NAME=""
FALCON_PATH=""
EXPORT_DIR=""
OVERRIDE=""

while [[ $# -gt 0 ]]; do
  case $1 in
    --model_name=*)
      MODEL_NAME="${1#*=}"
      shift
      ;;
    --falcon_path=*)
      FALCON_PATH="${1#*=}"
      shift
      ;;
    --export_dir=*)
      EXPORT_DIR="${1#*=}"
      shift
      ;;
    --override)
      shift
      OVERRIDE="$1"
      shift
      ;;
    *)
      echo "Unknown parameter: $1"
      exit 1
      ;;
  esac
done

# Validate required parameters
if [[ -z "$MODEL_NAME" || -z "$FALCON_PATH" || -z "$EXPORT_DIR" ]]; then
  echo "Usage: $0 --model_name=<model_name> --falcon_path=<falcon_path> --export_dir=<export_dir> [--override \"<override_string>\"]"
  echo "Example: $0 --model_name=\"falcon.multimodal.runs.scallion-d36-s64-lpe\" --falcon_path=\"az://orngcresco/path/to/tc/weights\" --export_dir=\"export-dir\" --override \"model=falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False\""
  exit 1
fi

# backup the original rcall config
cp ~/.rcall_config.py ~/.rcall_config.py.bak

# add extra setup to rcall config
# Build the convert.sh command with all parameters
CONVERT_CMD="/root/code/glass/personal/lixiaoli/mopub/convert.sh --model_name=\"${MODEL_NAME}\" --falcon_path=\"${FALCON_PATH}\" --export_dir=\"${EXPORT_DIR}\""
if [[ -n "$OVERRIDE" ]]; then
  CONVERT_CMD="$CONVERT_CMD --override \"${OVERRIDE}\""
fi

# Decide sed -i syntax (BSD vs GNU)
if sed --version >/dev/null 2>&1; then
  SED_INPLACE=(-i)
else
  SED_INPLACE=(-i '')
fi

sed "${SED_INPLACE[@]}" '/""" + EXTRA_SETUP/{
r /dev/stdin
d
}' ~/.rcall_config.py <<EOF
""" + EXTRA_SETUP + """
case "\$HOSTNAME" in
*-0)
  echo "yes" > ~/is_head_pod.txt
  nohup ${CONVERT_CMD} >> mopub.log 2>&1 & ;;
*)
  echo "no" > ~/is_head_pod.txt ;;
esac
"""
EOF

# create devbox
job_name="mopub-ptuc$(date +%m%d%H%M)-hpe3"
cluster="prod-southcentralus-hpe-3"
priority_class="team-critical"

BRIX_QUOTA=team-moonfire-genaicore OAIPKG_OVERRIDE_WHEEL=unsafe_skip RUST_BACKTRACE=1 \
    twdev create-ray-devbox \
    cluster=$cluster \
    num_pods=4 \
    setup_twapi=True \
    launch_cache_svc_daemon=True \
    num_gpu=8 \
    job_name=$job_name \
    priority_class=$priority_class

echo "Job $job_name submitted"

# recover the original rcall config
mv ~/.rcall_config.py.bak ~/.rcall_config.py