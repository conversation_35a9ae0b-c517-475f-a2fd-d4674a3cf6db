# for i in 7 8; do
#     python cloud-bus-shooter.py \
#         --quota-team "team-moonfire-genaicore" \
#         --torchflow-lineage "JUNE-FI" \
#         --engine "start-bus-gpt5-mini.sh" \
#         --user "swe-main-run" \
#         --snapshot_path "az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
#         --start_num 1 --end_num 10 \
#         --num_pods 1 \
#         --nowait \
#         --cluster "prod-uksouth-${i}" \
#         --priority_class "team-high"
# done

# for i in 2 5; do
#     python cloud-bus-shooter.py \
#         --quota-team "team-moonfire-genaicore" \
#         --torchflow-lineage "JUNE-FI" \
#         --engine "start-bus-gpt5-mini.sh" \
#         --user "swe-main-run" \
#         --snapshot_path "az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
#         --start_num 1 --end_num 10 \
#         --num_pods 1 \
#         --nowait \
#         --cluster "prod-southcentralus-hpe-${i}" \
#         --priority_class "team-high"
# done

# for i in 3; do
#     python cloud-bus-shooter.py \
#         --quota-team "team-moonfire-genaicore" \
#         --torchflow-lineage "JUNE-FI" \
#         --engine "start-bus-gpt5-mini.sh" \
#         --user "swe-main-run" \
#         --snapshot_path "az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
#         --start_num 1 --end_num 1 \
#         --num_pods 1 \
#         --nowait \
#         --cluster "prod-southcentralus-hpe-${i}" \
#         --priority_class "team-critical"
# done

for step in 160; do
    for ci in 2 3 5; do
        python cloud-bus-shooter.py \
            --quota-team "team-moonfire-genaicore" \
            --torchflow-lineage "JUNE-FI" \
            --engine "start-bus-gpt5.sh" \
            --name_infix "step-${step}" \
            --user "evaluation" \
            --snapshot_path "az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-4-pdw2-ev3-mixed-itc-spi32-gpt5-100steps-tef03-5-tpm1-rm-lr1e-5-run-20250821-190842/policy/step_000${step}/" \
            --start_num 1 --end_num 1 \
            --num_pods 2 \
            --nowait \
            --cluster "prod-southcentralus-hpe-${ci}" \
            --priority_class "team-critical"
    done
done

# python cloud-bus-shooter.py \
#     --quota-team "team-bus" \
#     --torchflow-lineage "JUNE-FI" \
#     --engine "start-bus-gpt5-mini.sh" \
#     --user "swe-main-run" \
#     --snapshot_path "az://orngscuscresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
#     --start_num 1 --end_num 10 \
#     --num_pods 1 \
#     --nowait \
#     --cluster stage-southcentralus-hpe-1
    
#  python cloud-bus-shooter.py \
#     --quota-team "team-bus" \
#     --torchflow-lineage "JUNE-FI" \
#     --engine "start-bus-gpt5-mini.sh" \
#     --user "swe-main-run" \
#     --snapshot_path "az://orngeus2cresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
#     --start_num 1 --end_num 20 \
#     --num_pods 1 \
#     --nowait \
#     --cluster prod-eastus2-30

# python cloud-bus-shooter.py \
#     --quota-team "team-bus" \
#     --torchflow-lineage "JUNE-FI" \
#     --engine "start-bus-gpt5-mini.sh" \
#     --user "swe-main-run" \
#     --snapshot_path "az://orngwus2cresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted" \
#     --start_num 1 --end_num 150 \
#     --num_pods 1 \
#     --nowait \
#     --cluster prod-westus2-19   

# python cloud-bus-shooter.py \
#     --engine "start-bus-gpt5-mini.sh" \
#     --user "swe-main-run" \
#     --start_num 1 --end_num 10 \
