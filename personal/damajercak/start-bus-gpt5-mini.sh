SNAPSHOT_PATH=$1
USER_OR_TOPIC=$2

RUST_BACKTRACE=1 nohup python -m harmony_scripts.engines.start_engine \
--name gpt5-mini-v2-engine \
--mode=optimal \
--snapshot_path=$SNAPSHOT_PATH \
--bus_rate_limiter=KV_UTIL \
--is_multimodal=False \
--gpu_kind=H100 \
--renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc" \
--restartable \
--extra_config_string="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=4 pipe_depth=2 n_replicas=1" \
--cluster=local \
--bus_enable_qos=True \
--enable_healthcheck=True \
--bus_topic_mode_or_user=$USER_OR_TOPIC \
--n_replicas=1
