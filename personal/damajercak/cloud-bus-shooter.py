#!/bin/env python3
# -*- coding: utf-8 -*-
import argparse
import subprocess
import tempfile
import time
import os
from string import Template

JOB_TEMPLATE = Template("""apiVersion: batch/v1
kind: Job
metadata:
  name: bus-launcher
spec:
  ttlSecondsAfterFinished: 3600
  template:
    metadata:
      labels:
        app: bus-launcher
        orange-devbox: "true"
    spec:
      restartPolicy: OnFailure
      containers:
        - name: main
          image: $IMAGE
          imagePullPolicy: Always
          command:
            - bash
            - -cx
            - |
              cd /bus-shooter
              bash bus-shooter.sh --engine engine.sh $ARGS
          env:
          - name: OPENAI_USER
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace  
          - name: BRIX_USE_KUBE_CONFIG_AUTH
            value: "true"
          - name: BRIX_QUOTA
            value: "$QUOTA_TEAM"
          - name: OAIPKG_OVERRIDE_WHEEL
            value: "unsafe_skip"
          - name: SKIP_TORCHFLOW_SETUP_CHECK
            value: "1"
          - name: BRIX_CLI_DISABLE_TAILSCALE
            value: "1"
          - name: BRIX_SELF_UPGRADE
            value: "0"
          - name: AZURE_USE_IDENTITY
            value: "1"
          - name: USE_STORAGE_MAP
            value: "false"          
          - name: BRIX_CLUSTER
            value: "$CLUSTER"
          - name: BRIX_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: OPENAI_STORAGE_SET
            value: oaiorange$(OPENAI_USER)
          - name: APPLIED_STORAGE_MAP
            value: az://orngoaiartifacts/storage-map/storage-map.json
          - name: OPENAI_ENCODINGS_BASE_DATA_GYM
            value: az://orngoaiartifacts/data-gym/encodings
          - name: DATA_GYM_BACKENDS
            value: azure
          - name: BRIX_TELEMETRY
            value: "off"
          envFrom:
          - secretRef:
              name: azure-service-principal
          - configMapRef:
              name: azure-storage-account    
          volumeMounts:
          - mountPath: /etc/brix/ssh
            name: brix-ssh
            readOnly: true
          - mountPath: /bus-shooter
            name: bus-shooter-engine-config
            readOnly: true
      volumes:
      - name: brix-ssh
        secret:
          defaultMode: 420
          items:
          - key: ssh-privatekey
            mode: 256
            path: id_rsa
          - key: ssh-publickey
            mode: 292
            path: id_rsa.pub
          secretName: brix-ssh
      - name: bus-shooter-engine-config
        configMap:
          name: bus-shooter-engine-config
"""
)



def main():
  # Get current kubectl context name
  try:
    current_context = subprocess.check_output(['kubectl', 'config', 'current-context'], text=True).strip()
    print(f"Current kubectl context: {current_context}")
  except subprocess.CalledProcessError as e:
    print("Failed to get current kubectl context.")
    exit(1)

  parser = argparse.ArgumentParser()
  parser.add_argument('--engine', type=str, required=False, default="start-bus-gpt5-mini.sh")
  parser.add_argument('--name_infix', type=str, required=False, default="")
  parser.add_argument('--user', type=str, required=False, default="msft")
  parser.add_argument('--snapshot_path', type=str, required=False, default="az://orngcresco/models/snapshots/zen-os4-gpt5mini-s400-4shard-400-4shard-2025-08-04-06-31-decrypted")
  parser.add_argument('--base_job_name', type=str, required=False, default="")
  parser.add_argument('--start_num', type=int, required=False, default=1)
  parser.add_argument('--end_num', type=int, required=False, default=1)
  parser.add_argument('--cluster', type=str, required=False, default=current_context)
  parser.add_argument('--priority_class', type=str, required=False, default="low-priority")
  parser.add_argument('--coasting_dog_delay', type=int, required=False, default=3600)
  parser.add_argument('--num_pods', type=int, required=False, default=1)
  parser.add_argument('--num_gpu', type=int, required=False, default=8)
  parser.add_argument('--nowait', default=False, action='store_true')
  parser.add_argument('--quota-team', type=str, required=False, default="")
  parser.add_argument('--torchflow-lineage', type=str, required=False, default="AUTO", choices=["AUTO", "MAY-FI", "JUNE-FI", "JULY-FI"])

  args = parser.parse_args()
  
  images = {
    "MAY-FI": "iridiumsdc.azurecr.io/kube-orange-box:1263171-cache",
    "JUNE-FI": "iridiumsdc.azurecr.io/kube-orange-box:1278936",
    "JULY-FI": "iridiumsdc.azurecr.io/kube-orange-box:1326938",
  }

  image = images["JUNE-FI"]
  
  if args.torchflow_lineage == "AUTO":
    # Try to detect the image from a running pod with label app=orange-devbox
    try:
      pod_info = subprocess.check_output(
        ['kubectl', 'get', 'po', '-l', 'app=orange-devbox', '-o', 'jsonpath={.items[0].spec.containers[0].image}'],
        text=True
      ).strip()

      if pod_info:
        image = pod_info
        print("Detected image from devbox:", image)

    except subprocess.CalledProcessError:
      print("Failed to detect image from running pod, using default JUNE-FI image.")
      image = images["JUNE-FI"]
  else:
    image = images[args.torchflow_lineage]

    


  if not args.base_job_name:
    engine_base = os.path.basename(args.engine)
    if engine_base.endswith('.sh'):
      engine_base = engine_base[:-3]
    if engine_base.startswith('start-'):
      engine_base = engine_base[len('start-'):]
    if args.name_infix:
      args.base_job_name = f"{engine_base}-{args.name_infix}-{args.cluster}-i"
    else:
      args.base_job_name = f"{engine_base}-{args.cluster}-i"


  subprocess.run(['kubectl', '--context', args.cluster, 'delete', 'cm', 'bus-shooter-engine-config', '--ignore-not-found'], check=True)
  py_dir = os.path.dirname(os.path.abspath(__file__))
  subprocess.run([
      'kubectl', '--context', args.cluster, 'create', 'cm', 'bus-shooter-engine-config',
      f'--from-file=engine.sh={args.engine}',
      f'--from-file=bus-shooter.sh={os.path.join(py_dir, "bus-shooter.sh")}',
      f'--from-file=coasting-dog.sh={os.path.join(py_dir, "coasting-dog.sh")}'
  ], check=True)
  subprocess.run(['kubectl', '--context', args.cluster, 'delete', 'job', 'bus-launcher', '--ignore-not-found'], check=True)

  with tempfile.NamedTemporaryFile('w', delete=False, suffix='.yaml') as jobfile:
    # Fill in the JOB_TEMPLATE with the actual argument values
    final_args = f" --user {args.user} --snapshot_path {args.snapshot_path} --base_job_name {args.base_job_name} --start_num {args.start_num} --end_num {args.end_num} --cluster {args.cluster} --priority_class {args.priority_class} --coasting_dog_delay {args.coasting_dog_delay} --num_pods {args.num_pods} --num_gpu {args.num_gpu}"

    print(f"Final arguments for job: {final_args}")

    job_yaml = JOB_TEMPLATE.safe_substitute(
      IMAGE=image,
      ARGS=final_args,
      CLUSTER=args.cluster,
      QUOTA_TEAM=args.quota_team
    )
    jobfile.write(job_yaml)
    jobfile.flush()
    subprocess.run(['kubectl', '--context', args.cluster, 'apply', '-f', jobfile.name], check=True)

  # Wait for the bus-launcher job pod to be ready
  if args.nowait:
    print("Skipping wait for bus-launcher pod.")
    return

  def is_pod_ready():
    try:
      output = subprocess.check_output(
        ['kubectl', '--context', args.cluster, 'get', 'pods', '-l', 'job-name=bus-launcher', '-o', 'jsonpath={.items[0].status.phase}'],
        text=True
      ).strip()
      return output == "Running"
    except subprocess.CalledProcessError:
      return False

  print("Waiting for bus-launcher pod to be ready...")
  for _ in range(60):
    if is_pod_ready():
      print("bus-launcher pod is running.")
      break
    time.sleep(5)
  else:
    print("Timeout waiting for bus-launcher pod to be ready.")

  # Fetch and print logs from the bus-launcher pod
  try:
    pod_name = subprocess.check_output(
      ['kubectl', '--context', args.cluster, 'get', 'pods', '-l', 'job-name=bus-launcher', '-o', 'jsonpath={.items[0].metadata.name}'],
      text=True
    ).strip()
    print(f"Fetching logs from pod: {pod_name}")
    # Stream logs in real-time
    with subprocess.Popen(
      ['kubectl', '--context', args.cluster, 'logs', '-f', pod_name, '-c', 'main'],
      text=True,
      stdout=subprocess.PIPE,
      bufsize=1
    ) as proc:
      for line in proc.stdout:
        print(line, end='')
  except subprocess.CalledProcessError as e:
    print("Failed to fetch logs from bus-launcher pod.")


if __name__ == "__main__":
  
  main()