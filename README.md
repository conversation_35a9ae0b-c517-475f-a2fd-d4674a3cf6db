# `glass`
Repo for Microsoft research/experimental code on top OpenAI's monorepo (i.e., `torchflow-mirror`). The repo will include custom graders, recipes, presets, dataset processors, etc. Intended to be used in <span style='color: orange;'>Project Orange</span>.

## Code of conduct
 * DO NOT CHECK IN LARGE FILES unless really necessary - in most cases, dataset files, binaries, logs, large .diff files etc. should go to a separate storage account. Current limit for push is 1 MiB - please don't abuse by chunking your file, etc.

## Repo structure

 * `project/<name>_msft` - MSFT-specific packages that are used directly (as entry-points or qstar presets).
     - let's put `_msft` suffix to avoid name collision with OpenAI's monorepo projects.
     - if the project contains overrides to OpenAI's projects, keep the relative path the same (without the suffix), e.g., `project/qstar_msft` contains overrides for `project_qstar`.
     - it's also OK to create deeper dir structure, e.g.: `project/<area>/<projectname>_msft`
 * `lib/<name>_msft` - MSFT-specific libraries.
 * `personal/*` - no code review is required; good for sharing experimental/half-baked stuff. Avoid checking in any large files or too many notebooks
 * `microsoft/*`, `microsoft-standalone/*` - paths originally moved from the `microsoft/main-research` branch in the `torchflow-mirror` repo. Need clean-up.

The idea/assumption is that the active projects will slowly move to `project/*`/`lib/*` and `personal/*`. We can fine-tune this dir structure plan - feedback/ideas are welcome!

## <span style='color: orange;'>Project Orange</span> usage
### Setup
1. Clone under `~/code`:

   ```
   cd ~/code
   git clone https://<EMAIL>/project-argos/Mimco/_git/glass
   ```

2. Do polyrepo setup
   ```bash
   cd glass
   source glass_setup.sh; polyrepo_setup
   # restart shell to pickup the changes
   ```

3. (Optional, but recommended): Install pre-commit hook

   ```
   source glass_setup.sh; precommit_setup
   ```

   Note: this will run a bunch of pre-commit checks when you push your commits. Usually they are helpful
   but sometimes they can be annoying esp. if you merge a others' cherry-picked commits (and don't want
   to change them). In such a case you can always skip pre-checks when pushing by doing:

   ```
   git push --no-verify
   ```

4. Add the repo to `CODE_INCLUDE_PATHS` in your `~/.rcall_config.py`, so `rcall-brix` knows to sync it to your jobs:
   ```python
   CODE_INCLUDE_PATHS = [
     # [...]
     "glass",
   ]
   ```

   ```python
   ENVIRONMENT.update(
      {
         "OAIPKG_POLYREPO_SIBLINGS" : "glass",
      }
   )
   ```

   After this step, if you create a devbox or peashooter cluster, or if you sync your code to it (using `b sync <devbox>`), it will contain `glass` repo and `oaipkg` can install projects from it.

### Workflows
1. Checkout corresponding version of `torchflow-mirror`:
   ```bash
   oaipkg polycheckout
   ```
   It simply checks out the commit pinned in `openai_commit.txt`, which should be a commit from the `orange/main` branch of `torchflow-mirror`.

   Sometimes that commit may be behind `orange/main`'s HEAD -- as of 2025/09/10, we need to manually
   test most important projects (like SWE Agent projects) in `glass` against latest `orange/main` and
   update the pin once we fix
   the issues. In the future, we need to automate tests and automate pin-moving pipeline.

2. Create a project -- please use the `_msft` suffix to minimize chances of name conflict with OpenAI:
   ```bash
   oaipkg create <your_project>_msft --location ~/code/glass/project/<your_project>_msft
   ```
   The tool asks a few questions. You can answer 'N' for compliance questions.

   The tool generates a project template - you can also create it manually, if you prefer. Existence of `pyproject.toml` is important though, if you want `oaipkg` to be able to discover your project.

3. Now you can install your project, e.g.:   
   ```bash
   oaipkg installoai qstar_msft
   ```
   (If you run it on your laptop and it fails due to missing wheels, then try: `orange_pull_storagemap && OAIPKG_OVERRIDE_WHEEL=unsafe_skip oaipkg installoai qstar_msft`)

   Run as entry-point:
   ```bash
   python -m qstar_msft
   ```

   Run as a plug-in to OAI's code, e.g., see [qstar_msft](project/qstar_msft/README.md) as an example.

### Important notes/troubleshooting

1. If there are any issues with `oaipkg` discovering your project on the devbox, try to run this manually in the SSH session:

   ```bash
   export OAIPKG_POLYREPO_SIBLINGS="glass"
   ```


## FAQ
### Why "glass"?
It's a short, easy to remember and type code name. (Also, because we like glass gardens; and windows are made of glass ;))

## Contact
Good place to ask questions:
 * [General Discussions in Project Orange Community](https://teams.microsoft.com/l/channel/19%3Aaeeeab91f87844088cd0d9bfc08d1c09%40thread.tacv2/General%20Discussions?groupId=d116f381-35ae-474f-a067-e912496a1c0c&tenantId=72f988bf-86f1-41af-91ab-2d7cd011db47) Teams channel
