import berry.preset_utils
from berry_models.neutrino import _d28
from berry_tw.chicken_common import (
    CHICKEN_COMMON_ML_CONFIG,
    CHICKEN_FP8KV_FP4MOE_BF16RES,
    CHICKEN_SCALLION_SAMPLING_ML_CONFIG,
)
from qstar.presets import numerics

# preset missing from the OAI's original library, needed to run ev3 sampling w/ d28.
d28_80g_chicken = berry.preset_utils.compose_presets(
    _d28,
    berry.preset_utils.args_preset(
        [
            "policy.layout=peashooter-80g-os4-128k-fp4",
            f"policy.ml_config={CHICKEN_COMMON_ML_CONFIG}",
            f"policy.sampling_ml_config={CHICKEN_SCALLION_SAMPLING_ML_CONFIG} {CHICKEN_FP8KV_FP4MOE_BF16RES}",
        ]
    ),
    numerics.fp4_scallion_flexpoint_bwd_bf16_logits,
)
